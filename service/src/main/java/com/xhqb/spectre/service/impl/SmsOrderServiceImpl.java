package com.xhqb.spectre.service.impl;

import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.mapper.SmsOrderMapper;
import com.xhqb.spectre.service.SmsOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SmsOrderServiceImpl implements SmsOrderService {

    @Autowired
    private SmsOrderMapper smsOrderMapper;

    @Override
    public SmsOrderDO selectByAppCodeAndOutOrderId(String appCode, String outOrderId) {
        return smsOrderMapper.selectByAppCodeAndOutOrderId(appCode, outOrderId);
    }
}
