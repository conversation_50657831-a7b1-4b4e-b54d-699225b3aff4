package com.xhqb.spectre.service.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

public class CommonRedisUtil {

    private static Logger logger = LoggerFactory.getLogger(CommonRedisUtil.class);
    private static String SYSTEMNAME = "sms:";
    private static final String KEY_FAIL_COUNT = "failCount:";
    private static final long HISTORY_TIMEOUT = 1;
    public static final String PARTNER_FAIL_KEY = "sms:failCount:";
    /**
     * 向redis送入map
     *
     * @param valueOperations
     * @param cid
     * @param functionName
     * @param map
     */
    public static void setRedisMap(ValueOperations<String, Object> valueOperations, String cid,
                                   String functionName, Map<String, Object> map) {
        String redisName = SYSTEMNAME + functionName + ":" + cid;
        valueOperations.set(redisName, map, 1, TimeUnit.DAYS);
    }

    /**
     * 向redis取出map
     *
     * @param valueOperations
     * @param cid
     * @param functionName
     * @return
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getRedisMap(ValueOperations<String, Object> valueOperations, String cid,
                                                  String functionName) {
        String redisName = SYSTEMNAME + functionName + ":" + cid;
        try {
            Map<String, Object> redisMap = (Map<String, Object>) valueOperations.get(redisName);
            logger.info("getRedisMap:{}", redisMap);
            if (redisMap == null) {
                return null;
            }
            return redisMap;
        } catch (Exception e) {
            logger.warn("redis value conver worng", e.getMessage(), e);
            valueOperations.set(redisName, null);
            return null;
        }

    }


    /**
     * 向redis的map送入键值，只送入一对
     *
     * @param valueOperations`
     * @param cid
     * @param functionName
     * @param mapKey
     * @param object
     */
    @SuppressWarnings("unchecked")
    public static void setRedisValue(ValueOperations<String, Object> valueOperations, String cid,
                                     String functionName, String mapKey, Object object) {
        String redisName = SYSTEMNAME + functionName + ":" + cid;
        Map<String, Object> redisMap = (Map<String, Object>) valueOperations.get(redisName);
        if (redisMap == null) {
            redisMap = new HashMap<>();
        }
        redisMap.put(mapKey, object);
        valueOperations.set(redisName, redisMap, 1, TimeUnit.DAYS);
    }


    /**
     * 在redis的map移除键值，
     *
     * @param valueOperations
     * @param cid
     * @param functionName
     * @param mapKey
     */
    @SuppressWarnings("unchecked")
    public static void removeRedisValue(ValueOperations<String, Object> valueOperations, String cid,
                                        String functionName, String mapKey) {
        String redisName = SYSTEMNAME + functionName + ":" + cid;
        Map<String, Object> redisMap = (Map<String, Object>) valueOperations.get(redisName);
        if (redisMap != null) {
            redisMap.remove(mapKey);
        }
        valueOperations.set(redisName, redisMap, 1, TimeUnit.DAYS);
    }

    /**
     * 向redis的map送入键值，送入多对，键值为数组类型
     *
     * @param valueOperations
     * @param cid
     * @param functionName
     * @param mapKeys
     * @param objects
     */
    @SuppressWarnings("unchecked")
    public static void setRedisValue(ValueOperations<String, Object> valueOperations, String cid,
                                     String functionName, String[] mapKeys, Object[] objects) {
        String redisName = SYSTEMNAME + functionName + ":" + cid;
        Map<String, Object> redisMap = (Map<String, Object>) valueOperations.get(redisName);
        if (redisMap == null) {
            redisMap = new HashMap<>();
        }
        for (int i = mapKeys.length; i > 0; i--) {
            //key非空，则把该KV送入到map
            if (!StringUtils.isEmpty(mapKeys[i - 1])) {
                redisMap.put(mapKeys[i - 1], objects[i - 1]);
            }
        }
        valueOperations.set(redisName, redisMap, 1, TimeUnit.DAYS);
    }

    /**
     * 向redis的map取出值
     *
     * @param valueOperations
     * @param cid
     * @param functionName
     * @param mapKey
     * @return
     */
    @SuppressWarnings("unchecked")
    public static Object getRedisMapValue(ValueOperations<String, Object> valueOperations, String cid,
                                          String functionName, String mapKey) {
        String redisName = SYSTEMNAME + functionName + ":" + cid;
        try {
            Map<String, Object> redisMap = (Map<String, Object>) valueOperations.get(redisName);
            logger.info("getRedisMap:{}", redisMap);
            if (redisMap == null) {
                return null;
            }
            return redisMap.get(mapKey);
        } catch (Exception e) {
            logger.warn("redis value conver worng", e.getMessage(), e);
            valueOperations.set(redisName, null);
            return null;
        }
    }

    /**
     * 根据functionname存储 objec
     *
     * @param valueOperations
     * @param functionName
     * @param object
     */
    public static void setRedisSingleValue(ValueOperations<String, Object> valueOperations,
                                           String functionName, Object object) {
        String redisName = SYSTEMNAME + ":" + functionName;
        valueOperations.set(redisName, object, 1, TimeUnit.DAYS);
    }

    /**
     * 根据functionname取redis缓存
     *
     * @param valueOperations
     * @param functionName
     * @return
     */
    public static Object getRedisSingleValue(ValueOperations<String, Object> valueOperations,
                                             String functionName) {
        String redisName = SYSTEMNAME + ":" + functionName;
        try {
            Object redisObj = valueOperations.get(redisName);
            logger.info("getRedisSingleValue:{}", redisObj);
            if (redisObj == null) {
                return null;
            }
            return redisObj;
        } catch (Exception e) {
            logger.warn("redis value conver worng", e.getMessage(), e);
            valueOperations.set(redisName, null);
            return null;
        }
    }

    /**
     * 将redis进行动态锁住
     *
     * @param valueOperations
     * @param key
     * @param value
     * @return
     */
    public static boolean setIfAbsent(ValueOperations<String, Object> valueOperations, String key,
                                      String functionName, Object value) {
        String redisName = SYSTEMNAME + functionName + ":" + key;
        boolean flag = false;
        try {
            flag = valueOperations.setIfAbsent(redisName, value);
            valueOperations.getOperations().expire(redisName, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.warn("redis locked is wrong, key:{}", e.getMessage(), e);
        }
        return flag;
    }

    /**
     * 将所有数据放入同一个Map中
     *
     * @param valueOperations
     * @param variable
     * @param functionName
     * @param objectMap
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> setRedisValueSameMap(ValueOperations<String, Object> valueOperations, String variable,
                                                           String functionName, Map<String, Object> objectMap) {
        if (objectMap == null || objectMap.isEmpty()) {
            return null;
        }
        String redisName = SYSTEMNAME + ":" + functionName + ":" + variable;
        Map<String, Object> redisMap = (Map<String, Object>) valueOperations.get(redisName);
        if (redisMap == null) {
            redisMap = new HashMap<>();
        }
        redisMap.putAll(objectMap);
        valueOperations.set(redisName, redisMap, 1, TimeUnit.DAYS);
        return redisMap;
    }

    public static boolean redisSingleValueIsExist(ValueOperations<String, Object> valueOperations, String key, Object value) {
        Object repeatFlagObj = getRedisSingleValue(valueOperations, key);
        if (repeatFlagObj != null) {//非首次进Q
            logger.info("key:{} 已存在", key);
            return true;
        } else {
            CommonRedisUtil.setRedisSingleValue(valueOperations, key, value);
        }
        return false;
    }

    /**
     * 根据functionnae 对值进行自增
     *
     * @param valueOperations
     * @param functionName
     * @param incrementNum
     * @return
     */
    public static Long increment(ValueOperations<String, Object> valueOperations,
                                 String functionName, Long incrementNum) {
        String redisName = SYSTEMNAME + ":" + functionName;
        Long redisObj = valueOperations.increment(redisName, incrementNum);
        logger.info("RedisIncrementValue:{}", redisObj);
        if (redisObj == null) {
            return 0L;
        }
        return redisObj;
    }


    /**
     * 判断请求是否在时间段内有多次
     *
     * @param valueOperations redis操作
     * @param key             缓存key
     * @param time            超时时间
     * @return 标志
     */
    public static boolean isSameRequest(ValueOperations<String, Object> valueOperations,
                                        String key, long time) {
//        boolean flag;
        String redisKey = SYSTEMNAME + key;
        // redis加锁
        // 建议升级spring-data-redis 使用带过期时间的setIfAbsent
        Boolean lockSucceed = valueOperations.setIfAbsent(redisKey, System.currentTimeMillis());
        if (lockSucceed) {
            valueOperations.getOperations().expire(redisKey, time, TimeUnit.MILLISECONDS);
        }
//        if (lockSucceed) {
//            // 设置解锁的超时时间
//            lockSucceed = valueOperations.getOperations().expire(redisKey, time, TimeUnit.MILLISECONDS);
//        }
//        if (lockSucceed == null || !lockSucceed) {
//            Long timeMillis =  valueOperations.get(redisKey) == null ? null : Long.parseLong(valueOperations.get(redisKey).toString());
//            if (timeMillis != null && (System.currentTimeMillis() - timeMillis) > time) {
//                logger.info("加锁进程非正常停止,导致{}无失效时间,故此需手动删除", redisKey);
//                valueOperations.getOperations().delete(redisKey);
//            }
//            logger.warn("在{}毫秒内有相同的请求，请求报文为：{}", time, redisKey);
//            flag = true;
//        } else {
//            flag = false;
//        }
        return !lockSucceed;
    }


    public static void deleteCache(ValueOperations<String, Object> valueOperations, String value) {
        Set<String> set = valueOperations.getOperations().keys("*" + value + "*");
        if (set != null && set.size() > 0) {
            for (String key : set) {
                valueOperations.getOperations().delete(key);
                logger.info("删除 cid:[" + value + "]" + "相关的缓存，key:" + key);
            }
        }
    }

    public static int increaseFailCount(RedisTemplate redisTemplate, String platform, int increaseValue) {

        ValueOperations<String, Object> valueOperations = redisTemplate.opsForValue();
        String redisKey = SYSTEMNAME + KEY_FAIL_COUNT + platform;
        redisTemplate.expire(redisKey, HISTORY_TIMEOUT, TimeUnit.HOURS);
        return (int) valueOperations.increment(redisKey, increaseValue).longValue();
    }

}
