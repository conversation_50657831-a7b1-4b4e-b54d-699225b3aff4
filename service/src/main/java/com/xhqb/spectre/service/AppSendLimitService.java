package com.xhqb.spectre.service;


import com.xhqb.spectre.common.dal.dto.result.AppSendLimitAndWhiteResp;
import com.xhqb.spectre.common.dal.entity.AppSendLimitDO;

import java.util.List;
import java.util.Map;

public interface AppSendLimitService {
    Map<String, String> getConf();

    AppSendLimitDO updateStatus(Integer id);

    List<AppSendLimitDO> getAppSendLimit(String aapId);

    AppSendLimitDO getAppSendLimitByAppCodeAndKey(String appCode, String key);

    AppSendLimitAndWhiteResp getAppSendLimitAndWhiteResp(String appId);
}
