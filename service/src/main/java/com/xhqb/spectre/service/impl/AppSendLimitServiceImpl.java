package com.xhqb.spectre.service.impl;

import com.xhqb.spectre.common.dal.dto.result.AppSendLimitAndWhiteResp;
import com.xhqb.spectre.common.dal.entity.AppSendLimitDO;
import com.xhqb.spectre.common.dal.entity.MobileWhiteDO;
import com.xhqb.spectre.common.dal.mapper.AppSendLimitMapper;
import com.xhqb.spectre.common.dal.mapper.MobileWhiteMapper;
import com.xhqb.spectre.common.enums.AppSendLimitEnum;
import com.xhqb.spectre.common.exception.ManagementException;
import com.xhqb.spectre.service.AppSendLimitService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AppSendLimitServiceImpl implements AppSendLimitService {

    @Autowired
    private MobileWhiteMapper mobileWhiteMapper;

    @Autowired
    private AppSendLimitMapper appSendLimitMapper;

    @Override
    public Map<String, String> getConf() {
        Map<String, String> map = new HashMap<>();
        for (AppSendLimitEnum value : AppSendLimitEnum.values()) {
            map.put(value.getCode(), value.getDesc());
        }
        return map;
    }

    @Override
    public AppSendLimitDO updateStatus(Integer id) {
        if (StringUtils.isBlank(String.valueOf(id))) {
            throw new ManagementException("未接收到更新id");
        }
        AppSendLimitDO oldAppSendLimit = appSendLimitMapper.selectByPrimaryKey(id);
        if (null != oldAppSendLimit) {
            oldAppSendLimit.setStatus(1);
            appSendLimitMapper.updateByPrimaryKeySelective(oldAppSendLimit);
        }
        return oldAppSendLimit;
    }

    @Override
    public List<AppSendLimitDO> getAppSendLimit(String appId) {
        if (StringUtils.isBlank(appId)) {
            throw new ManagementException("未接收到更新id");
        }
        return appSendLimitMapper.selectList(appId);
    }

    @Override
    public AppSendLimitDO getAppSendLimitByAppCodeAndKey(String appCode, String key) {
        AppSendLimitDO appSendLimit = new AppSendLimitDO();
        appSendLimit.setAppCode(appCode);
        appSendLimit.setSendLimitKey(key);
        appSendLimit.setStatus(0);
        return appSendLimitMapper.selectOne(appSendLimit);
    }

    @Override
    public AppSendLimitAndWhiteResp getAppSendLimitAndWhiteResp(String appId) {
        AppSendLimitAndWhiteResp appSendLimitAndWhiteResp = new AppSendLimitAndWhiteResp();
        if (StringUtils.isBlank(appId)) {
            throw new ManagementException("未接收到更新id");
        }
        List<AppSendLimitDO> appSendLimits = appSendLimitMapper.selectList(appId);
        List<MobileWhiteDO> mobileWhites = mobileWhiteMapper.selectList(appId);
        appSendLimitAndWhiteResp.setAppSendLimits(appSendLimits);
        appSendLimitAndWhiteResp.setMobileWhites(mobileWhites);
        return appSendLimitAndWhiteResp;
    }
}
