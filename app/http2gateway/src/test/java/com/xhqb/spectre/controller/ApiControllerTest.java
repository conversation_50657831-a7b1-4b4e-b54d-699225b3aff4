package com.xhqb.spectre.controller;

import com.chinamobile.cmos.SmsClient;
import com.zx.sms.BaseMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@ExtendWith(SpringExtension.class)
@WebMvcTest(ApiController.class)
@Slf4j
class ApiControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SmsClient mockSmsClient;

    private static final String TELEPHONE = "12345678900";

    @Test
    @Ignore
    void testSend() throws Exception {
//        // Setup
//        when(mockSmsClient.send(any(BaseMessage.class))).thenReturn(null);
//
//        // Run the test
//        final MockHttpServletResponse response = mockMvc.perform(post("/api/send")
//                        .param("telephone", TELEPHONE)
//                        .param("message", "【小花钱包】test")
//                        .accept(MediaType.APPLICATION_JSON))
//                .andReturn().getResponse();
//
//        log.info("reponse:{}", response.getContentAsString());
//        // Verify the response
//        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }
}
