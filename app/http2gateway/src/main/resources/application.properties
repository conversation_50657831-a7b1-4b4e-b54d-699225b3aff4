server.port=10086
cmpp.server=cmpp://10.32.8.42:7890?username=tmp101&password=tmp123&version=32&spcode=300277&msgsrc=tmp101&serviceid=X110291029

# cmpp account
kawaii.cmpp.client.accounts[0].id=1
kawaii.cmpp.client.accounts[0].username=132173
kawaii.cmpp.client.accounts[0].password=WgbeDn
kawaii.cmpp.client.accounts[0].serviceId=X587073190

kawaii.cmpp.client.accounts[1].id=2
kawaii.cmpp.client.accounts[1].username=159026
kawaii.cmpp.client.accounts[1].password=FkdaWn
kawaii.cmpp.client.accounts[1].serviceId=X923702387


spring.datasource.url=******************************************************************************************************************
spring.datasource.username=cmpp_kawaiidb_prod
spring.datasource.password=12345@Xhqb
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

hibernate.dialect.storage_engine=innodb
spring.jpa.properties.hibernate.hbm2ddl.auto=create
spring.jpa.properties.show-sql=false
spring.jpa.hibernate.naming.implicit-strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl
