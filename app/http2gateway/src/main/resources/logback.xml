<configuration debug="true" scan="true">
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <property scope="context" name="baseLogPath" value="."/>


    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}
            </pattern>
        </encoder>
    </appender>
    <logger name="entity">
        <level value="info"/>
    </logger>
    <logger name="om.xhqb.spectre">
        <level value="debug"/>
    </logger>
    <root level="info">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
