## http2gateway 介绍
### 说明
测试环境短信发送并不会真正发送给渠道商，因而在与渠道商交互的模块，测试老师无法进行完整用例测试。

### 目标
http2gateway模块主要是为了测试环境可以手动模拟短信提交回执、发送回执，可以进行全链路测试。

### 总结
当前模块只是用于本地测试，不会真正发布到生产环境。

## 建表
### 说明
模拟短信提交回执、发送回执时，需要获取到序列号以及企业代码，原有代码逻辑中并不会将这些核心信息打印在日志中，
测试老师不太方便获取到相关信息进行模拟，若改动代码进行日志打印，则破坏了原有的设计定义，不符合当前分表改动
初衷，所以在模拟模块将相关信息进行存储，方便测试时进行查询。
回执信息映射都在内存中，重启就失效了，所以短信回执模拟的表记录信息在应用重启之后将会被清空。

### 短信回执

```sql
CREATE TABLE `t_sms_receipt` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `mobile` varchar(128) NOT NULL COMMENT '手机号码',
  `sequence_id` varchar(128)  NOT NULL DEFAULT '' COMMENT '序列号',
  `msg_src` varchar(128) NOT NULL DEFAULT '' COMMENT '企业代码',
  `submit_status` int NOT NULL DEFAULT '0' COMMENT '提交状态 0:初始化 1:已回执',
  `send_status` int NOT NULL DEFAULT '0' COMMENT '回执状态 0:初始化 1:已回执',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志，0：未删除；1：已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='短信回执';

```