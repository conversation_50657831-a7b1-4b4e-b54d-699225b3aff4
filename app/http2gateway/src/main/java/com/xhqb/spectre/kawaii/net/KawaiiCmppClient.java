package com.xhqb.spectre.kawaii.net;


import com.xhqb.spectre.kawaii.net.handler.KawaiiCmppSubmitRequestHandler;
import com.xhqb.spectre.kawaii.store.KawaiiStore;
import com.xhqb.spectre.kawaii.store.KawaiiStoreMemory;
import com.zx.sms.connect.manager.cmpp.CMPPEndpointEntity;
import com.zx.sms.connect.manager.cmpp.CMPPServerChildEndpointConnector;
import com.zx.sms.connect.manager.cmpp.CMPPServerChildEndpointEntity;
import io.netty.channel.Channel;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/7/26
 */
public class KawaiiCmppClient extends CMPPServerChildEndpointEntity {

    private KawaiiCmppServer kawaiiCmppServer;

    public KawaiiCmppClient(KawaiiCmppServer kawaiiCmppServer) {
        this.kawaiiCmppServer = kawaiiCmppServer;
    }

    @Override
    protected CMPPServerChildEndpointConnector buildConnector() {
        return new KawaiiCmppClientConnector(this, kawaiiCmppServer.getKawaiiStore());
    }


    public static class KawaiiCmppClientConnector extends CMPPServerChildEndpointConnector {

        private KawaiiStore kawaiiStore;

        public KawaiiCmppClientConnector(CMPPEndpointEntity endpoint, KawaiiStore kawaiiStore) {
            super(endpoint);
            this.kawaiiStore = Objects.nonNull(kawaiiStore) ? kawaiiStore : new KawaiiStoreMemory();
        }

        @Override
        public synchronized boolean addChannel(Channel ch) {
            boolean result = super.addChannel(ch);
            ch.pipeline().addAfter("CMPPSubmitLongMessageHandler", "KawaiiCmppSubmitRequestHandler", new KawaiiCmppSubmitRequestHandler(kawaiiStore));
            return result;
        }
    }
}
