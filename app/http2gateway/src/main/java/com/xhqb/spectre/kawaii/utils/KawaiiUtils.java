package com.xhqb.spectre.kawaii.utils;

import com.zx.sms.codec.smgp.util.ByteUtil;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2024/7/25
 */
public class KawaiiUtils {

    public static String ensureLength(String str, int maxLen) {
        byte[] newBytes = new byte[maxLen];
        return ensureLength(str.getBytes(StandardCharsets.UTF_8), maxLen);
    }

    public static String ensureLength(byte[] src, int maxLen) {
        byte[] newBytes = new byte[maxLen];
        ByteUtil.appendString(src, maxLen, newBytes, 0);
        return new String(newBytes);
    }
}
