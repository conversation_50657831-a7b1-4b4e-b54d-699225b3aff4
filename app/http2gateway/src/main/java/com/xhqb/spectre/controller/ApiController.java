package com.xhqb.spectre.controller;

import com.chinamobile.cmos.SmsClient;
import com.xhqb.spectre.model.CommonResult;
import com.zx.sms.BaseMessage;
import com.zx.sms.codec.cmpp.msg.CmppSubmitRequestMessage;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Slf4j
public class ApiController {
    @Autowired
    private SmsClient smsClient;

    @ResponseBody
    @PostMapping("/send")
    public CommonResult<BaseMessage> send(@RequestBody DirectSmsRequest directSmsRequest) {
        final String telephone = directSmsRequest.getTelephone();
        final String message = directSmsRequest.getMessage();
        CmppSubmitRequestMessage msg = CmppSubmitRequestMessage.create(telephone, "", message);
        try {
            msg.setRegisteredDelivery((short) 1);
            BaseMessage response = smsClient.send(msg);
            return CommonResult.success(response);
        } catch (Exception e) {
            log.error("failed to send;", e);
            return CommonResult.make(-1, e.getMessage());
        }
    }

    @Data
    public static class DirectSmsRequest {
        private String telephone;
        private String message;
    }
}
