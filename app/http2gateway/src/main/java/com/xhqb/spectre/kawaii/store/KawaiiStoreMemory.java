package com.xhqb.spectre.kawaii.store;

import com.xhqb.spectre.dal.bean.SmsReceipt;
import com.xhqb.spectre.dal.mapper.SmsReceiptMapper;
import com.zx.sms.codec.cmpp.msg.CmppSubmitRequestMessage;
import com.zx.sms.codec.cmpp.msg.CmppSubmitResponseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/7/26
 */
@Service
@Slf4j
public class KawaiiStoreMemory implements KawaiiStore {

    private static final ConcurrentHashMap<StoreKey, List<CmppSubmitResponseMessage>> SUBMIT_REQUEST_MAP = new ConcurrentHashMap<>(64);

    @Resource
    private SmsReceiptMapper smsReceiptMapper;

    @Override
    public void saveCmppSubmitRequestMessage(CmppSubmitRequestMessage message) {
        List<CmppSubmitResponseMessage> respList = new ArrayList<>();
        CmppSubmitResponseMessage resp = new CmppSubmitResponseMessage(message.getHeader().getSequenceId());
        respList.add(resp);
        if (message.getFragments() != null) {
            for (CmppSubmitRequestMessage frag : message.getFragments()) {
                CmppSubmitResponseMessage submitResponseMessage = new CmppSubmitResponseMessage(frag.getHeader().getSequenceId());
                respList.add(submitResponseMessage);
            }
        }


        final String mobile = message.getDestterminalId()[0];
        final int sequenceId = message.getHeader().getSequenceId();
        final String msgSrc = message.getMsgsrc();
        StoreKey storeKey = new StoreKey(mobile, sequenceId, msgSrc);
        SUBMIT_REQUEST_MAP.put(storeKey, respList);

        SmsReceipt smsReceipt = new SmsReceipt();
        smsReceipt.setMobile(storeKey.getMobile());
        smsReceipt.setSequenceId(String.valueOf(storeKey.getSequenceId()));
        smsReceipt.setMsgSrc(storeKey.getMsgSrc());
        smsReceipt.setContent(message.getMsgContent());
        smsReceiptMapper.save(smsReceipt);
        log.info("短信提交信息存储成功,storeKey ={}", storeKey);
    }

    @Override
    public List<CmppSubmitResponseMessage> selectCmppSubmitRequestMessage(StoreKey storeKey) {
        return SUBMIT_REQUEST_MAP.get(storeKey);
    }

    @Override
    public void remCmppSubmitRequestMessage(StoreKey storeKey) {
        SUBMIT_REQUEST_MAP.remove(storeKey);
    }

}
