package com.xhqb.spectre.kawaii.store;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/7/26
 */
@Data
public class StoreKey {

    private String mobile;
    private Integer sequenceId;
    /**
     * 企业代码
     */
    private String msgSrc;


    public StoreKey() {
    }

    public StoreKey(String mobile, Integer sequenceId, String msgSrc) {
        this.mobile = mobile;
        this.sequenceId = sequenceId;
        this.msgSrc = msgSrc;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StoreKey storeKey = (StoreKey) o;
        return Objects.equals(mobile, storeKey.mobile) && Objects.equals(sequenceId, storeKey.sequenceId) && Objects.equals(msgSrc, storeKey.msgSrc);
    }

    @Override
    public int hashCode() {
        return Objects.hash(mobile, sequenceId, msgSrc);
    }
}
