package com.xhqb.spectre.kawaii.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/26
 */
@Data
public class CmppClientAccount {

    /**
     * 自定义渠道账号ID，保存全局唯一
     */
    private String id;
    /**
     * 渠道账号，可能和企业代码相同
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 服务代码
     */
    private String serviceId;
    /**
     * 企业代码，可能跟userName相同
     */
    private String msgSrc;
    /**
     * 协议版本号，48是3.0协议(0x30) ,32是2.0协议(0x20)
     */
    private Short version = 0x20;
}
