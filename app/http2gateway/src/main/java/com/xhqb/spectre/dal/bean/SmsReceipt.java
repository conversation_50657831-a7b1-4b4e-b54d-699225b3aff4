package com.xhqb.spectre.dal.bean;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/26
 */
@Data
@Entity
@Table(name = "t_sms_receipt")
@DynamicInsert
@DynamicUpdate
public class SmsReceipt {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "BIGINT NOT NULL COMMENT '主键'")
    private Long id;
    /**
     * 手机号码
     */
    @Column(name = "mobile", columnDefinition = "varchar(128) NOT NULL DEFAULT '' COMMENT '手机号码'")
    private String mobile;
    /**
     * 序列号
     */
    @Column(name = "sequence_id", columnDefinition = "varchar(128)  NOT NULL DEFAULT '' COMMENT '序列号'")
    private String sequenceId;
    /**
     * 企业代码
     */
    @Column(name = "msg_src", columnDefinition = "varchar(128) NOT NULL DEFAULT '' COMMENT '企业代码'")
    private String msgSrc;
    /**
     * 提交状态 0:初始化 1:已回执
     */
    @Column(name = "submit_status", columnDefinition = "int NOT NULL DEFAULT '0' COMMENT '提交状态 0:初始化 1:已回执'")
    private Integer submitStatus;
    /**
     * 回执状态 0:初始化 1:已回执
     */
    @Column(name = "send_status", columnDefinition = "int NOT NULL DEFAULT '0' COMMENT '回执状态 0:初始化 1:已回执'")
    private Integer sendStatus;

    /**
     * 短信内容
     */
    @Column(name = "content", columnDefinition = "text COMMENT '短信内容'")
    private String content;

    /**
     * 描述信息
     */
    @Column(name = "remark", columnDefinition = "varchar(128) NOT NULL DEFAULT '' COMMENT '描述信息'")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time", columnDefinition = "datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'")
    private Date createTime;
    /**
     * 更新时间
     */
    @Column(name = "update_time", columnDefinition = "datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'")
    private Date updateTime;
    /**
     * 删除标志，0：未删除；1：已删除
     */
    @Column(name = "is_delete", columnDefinition = "tinyint NOT NULL DEFAULT '0' COMMENT '删除标志，0：未删除；1：已删除'")
    private Integer isDelete;
}
