package com.xhqb.spectre.kawaii.controller;

import com.xhqb.spectre.dal.bean.SmsReceipt;
import com.xhqb.spectre.dal.mapper.SmsReceiptMapper;
import com.xhqb.spectre.kawaii.net.KawaiiCmppClient;
import com.xhqb.spectre.kawaii.net.KawaiiCmppServer;
import com.xhqb.spectre.kawaii.store.KawaiiStore;
import com.xhqb.spectre.kawaii.store.StoreKey;
import com.xhqb.spectre.kawaii.utils.KawaiiUtils;
import com.zx.sms.codec.cmpp.msg.CmppDeliverRequestMessage;
import com.zx.sms.codec.cmpp.msg.CmppReportRequestMessage;
import com.zx.sms.codec.cmpp.msg.CmppSubmitResponseMessage;
import com.zx.sms.common.util.ChannelUtil;
import com.zx.sms.common.util.MsgId;
import io.netty.channel.ChannelFuture;
import lombok.extern.slf4j.Slf4j;
import org.marre.sms.SmsDcs;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/7/26
 */
@RestController
@RequestMapping("/kawaii/sms/receipt")
@Slf4j
public class KawaiiSmsReceiptController {

    @Resource
    private KawaiiCmppServer kawaiiCmppServer;
    @Resource
    private KawaiiStore kawaiiStore;
    @Resource
    private SmsReceiptMapper smsReceiptMapper;

    /**
     * 模拟提交回执
     *
     * @param storeKey
     * @param op       操作 1成功 0失败
     * @return
     */
    @PostMapping("/submitResponse")
    public String submitResponse(@RequestBody StoreKey storeKey, @RequestParam(required = false, defaultValue = "1") boolean op) {
        KawaiiCmppClient kawaiiCmppClient = kawaiiCmppServer.getKawaiiCmppClientByMsgSrc(storeKey.getMsgSrc());
        if (Objects.isNull(kawaiiCmppClient)) {
            log.warn("未查询到cmpp客户端, storeKey = {}", storeKey);
            return "未查询到cmpp客户端";
        }

        SmsReceipt smsReceipt = smsReceiptMapper.selectSubmitSmsReceipt(storeKey.getMobile(), storeKey.getSequenceId() + "", storeKey.getMsgSrc());
        if (Objects.isNull(smsReceipt)) {
            return "cmpp提交信息数据库不存在";
        }

        List<CmppSubmitResponseMessage> cmppSubmitResponseMessages = kawaiiStore.selectCmppSubmitRequestMessage(storeKey);
        if (CollectionUtils.isEmpty(cmppSubmitResponseMessages)) {
            return "cmpp提交信息缓存不存在";
        }

        CmppSubmitResponseMessage cmppSubmitResponseMessage = cmppSubmitResponseMessages.get(0);
        if (!op) {
            cmppSubmitResponseMessage.setResult(8L);
            smsReceipt.setIsDelete(1);
            smsReceipt.setRemark("发送失败");
        } else {
            smsReceipt.setRemark("发送成功");
        }

        final String msgId = cmppSubmitResponseMessage.getMsgId().toString();
        final int sequenceId = cmppSubmitResponseMessage.getHeader().getSequenceId();
        ChannelFuture channelFuture = ChannelUtil.asyncWriteToEntity(kawaiiCmppClient, cmppSubmitResponseMessage, future -> {
            if (!future.isSuccess()) {
                log.warn("短信提交响应发送失败, msgId={}, sequenceId={}", msgId, sequenceId);
            } else {
                log.info("短信提交响应发送成功, msgId = {}, sequenceId = {}", msgId, sequenceId);
                smsReceipt.setSubmitStatus(1);
                smsReceiptMapper.saveAndFlush(smsReceipt);
            }
        });

        if (Objects.isNull(channelFuture)) {
            log.warn("短信提交响应发送失败(channelFuture=null), msgId={}, sequenceId={},mobile ={}", msgId, sequenceId, storeKey.getMobile());
        }

        return storeKey + ",短信提交回执响应完成";
    }

    /**
     * 模拟发送回执
     *
     * @param storeKey
     * @param op       操作 1成功 0失败
     * @return
     */
    @PostMapping("/sendRequest")
    public String sendRequest(@RequestBody StoreKey storeKey, @RequestParam(required = false, defaultValue = "1") boolean op) {
        KawaiiCmppClient kawaiiCmppClient = kawaiiCmppServer.getKawaiiCmppClientByMsgSrc(storeKey.getMsgSrc());
        if (Objects.isNull(kawaiiCmppClient)) {
            log.warn("未查询到cmpp客户端, storeKey = {}", storeKey);
            return "未查询到cmpp客户端";
        }

        SmsReceipt smsReceipt = smsReceiptMapper.selectSendSmsReceipt(storeKey.getMobile(), storeKey.getSequenceId() + "", storeKey.getMsgSrc());
        if (Objects.isNull(smsReceipt)) {
            return "cmpp提交信息数据库不存在(如未发起提交回执，需要先发起提交回执喔)";
        }

        List<CmppSubmitResponseMessage> cmppSubmitResponseMessages = kawaiiStore.selectCmppSubmitRequestMessage(storeKey);
        if (CollectionUtils.isEmpty(cmppSubmitResponseMessages)) {
            return "cmpp提交信息缓存不存在";
        }
        CmppSubmitResponseMessage cmppSubmitResponseMessage = cmppSubmitResponseMessages.get(0);
        MsgId msgId = cmppSubmitResponseMessage.getMsgId();


        CmppDeliverRequestMessage cmppDeliverRequestMessage = new CmppDeliverRequestMessage();
        cmppDeliverRequestMessage.setMsgId(cmppSubmitResponseMessage.getMsgId());
        cmppDeliverRequestMessage.setDestId(KawaiiUtils.ensureLength(storeKey.getMobile(), 21));
        cmppDeliverRequestMessage.setServiceid(KawaiiUtils.ensureLength("serviceId", 10));
        // GSM协议类型。详细解释请参考GSM03.40中的*******
        cmppDeliverRequestMessage.setTppid((short) 1);
        // GSM协议类型。详细解释请参考GSM03.40中的9.2.3.23，仅使用1位，右对齐
        cmppDeliverRequestMessage.setTpudhi((short) 1);
        //信息格式
        //  0：ASCII串
        //  3：短信写卡操作
        //  4：二进制信息
        //  8：UCS2编码
        //15：含GB汉字
        cmppDeliverRequestMessage.setMsgfmt(new SmsDcs((byte) 4));
        // Src_terminal_Id 源终端MSISDN号码（状态报告时填为CMPP_SUBMIT消息的目的终端号码）
        cmppDeliverRequestMessage.setSrcterminalId(KawaiiUtils.ensureLength("Src_terminal_Id", 21));
        // 是否为状态报告
        // 0：非状态报告
        // 1：状态报告
        cmppDeliverRequestMessage.setReportRequestMessage(null);
        CmppReportRequestMessage cmppReportRequestMessage = new CmppReportRequestMessage();
        cmppReportRequestMessage.setMsgId(cmppDeliverRequestMessage.getMsgId());

        if (op) {
            cmppReportRequestMessage.setStat("DELIVRD");
        } else {
            cmppReportRequestMessage.setStat("UNDELIV");
        }


        cmppReportRequestMessage.setDestterminalId(KawaiiUtils.ensureLength(storeKey.getMobile(), 21));

        // 消息长度
        cmppDeliverRequestMessage.setMsgLength((short) 60);
        cmppDeliverRequestMessage.setMsgContent(cmppReportRequestMessage.toString());

        cmppDeliverRequestMessage.setReportRequestMessage(cmppReportRequestMessage);

        ChannelFuture channelFuture = ChannelUtil.asyncWriteToEntity(kawaiiCmppClient, cmppDeliverRequestMessage, future -> {
            int sequenceId = cmppDeliverRequestMessage.getHeader().getSequenceId();
            String mobile = storeKey.getMobile();
            if (!future.isSuccess()) {
                log.warn("短信发送回执响应发送失败, msgId={}, sequenceId={}, mobile ={}", msgId, sequenceId, mobile);
            } else {
                log.info("短信发送回执响应发送成功, msgId = {}, sequenceId = {}, mobile={}", msgId, sequenceId, mobile);
                smsReceipt.setSendStatus(1);
                smsReceiptMapper.saveAndFlush(smsReceipt);
                kawaiiStore.remCmppSubmitRequestMessage(storeKey);
            }
        });

        if (Objects.isNull(channelFuture)) {
            log.warn("短信发送回执响应发送失败(channelFuture=null), msgId={}, mobile ={}", msgId, storeKey.getMobile());
        }

        return storeKey + ",短信发送回执响应完成";
    }
}