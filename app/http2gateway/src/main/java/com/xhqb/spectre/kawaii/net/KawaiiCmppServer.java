package com.xhqb.spectre.kawaii.net;

import com.xhqb.spectre.kawaii.config.CmppClientAccount;
import com.xhqb.spectre.kawaii.config.CmppClientAccountProperties;
import com.xhqb.spectre.kawaii.store.KawaiiStore;
import com.zx.sms.connect.manager.EndpointManager;
import com.zx.sms.connect.manager.cmpp.CMPPServerEndpointEntity;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * cmpp服务器
 *
 * <AUTHOR>
 * @date 2024/7/26
 */
@Configuration
@Slf4j
@EnableConfigurationProperties(CmppClientAccountProperties.class)
public class KawaiiCmppServer {

    private static final String SERVER_ID = "kawaiiCmppServer";

    private final AtomicBoolean started = new AtomicBoolean();
    private final EndpointManager endpointManager = EndpointManager.INS;
    private Map<String, KawaiiCmppClient> clientMapping = new HashMap<>(16);
    @Getter
    private CMPPServerEndpointEntity serverEndpoint;
    @Resource
    @Getter
    private KawaiiStore kawaiiStore;
    @Resource
    private CmppClientAccountProperties cmppClientAccountProperties;

    @PostConstruct
    public void start() {
        if (!started.compareAndSet(false, true)) {
            log.info("cmpp server started.");
        }
        serverEndpoint = new CMPPServerEndpointEntity();
        serverEndpoint.setId(SERVER_ID);
        serverEndpoint.setHost("0.0.0.0");
        serverEndpoint.setPort(7890);
        serverEndpoint.setValid(true);
        serverEndpoint.setUseSSL(false);
        endpointManager.openEndpoint(serverEndpoint);
        log.info("cmpp server start successfully.");
        initCmppClient();
    }

    private void initCmppClient() {
        List<CmppClientAccount> accounts = cmppClientAccountProperties.getAccounts();
        accounts.forEach(account -> {
            KawaiiCmppClient kawaiiCmppClient = new KawaiiCmppClient(this);
            // 自定义通道账号ID，保持全局唯一
            kawaiiCmppClient.setId(account.getId());
            kawaiiCmppClient.setChartset(StandardCharsets.UTF_8);
            // 自定义通道账号分组ID，用于对通道标识不同组，方便路由实现
            kawaiiCmppClient.setGroupName("kawaiiCmppTest");
            // 通道账号，可能和企业代码相同
            kawaiiCmppClient.setUserName(account.getUsername());
            // 密码
            kawaiiCmppClient.setPassword(account.getPassword());
            // 服务代码
            kawaiiCmppClient.setServiceId(account.getServiceId());
            // 企业代码，可能跟userName相同
            String msgSrc = StringUtils.isNotBlank(account.getMsgSrc()) ? account.getMsgSrc() : account.getUsername();
            kawaiiCmppClient.setMsgSrc(msgSrc);
            kawaiiCmppClient.setLiftTime(30 * 1000);
            kawaiiCmppClient.setValid(true);
            // 协议版本号，48是3.0 协议  0x30，32是2.0协议 0x20
            kawaiiCmppClient.setVersion(account.getVersion());
            join(kawaiiCmppClient);
        });
        log.info("cmpp client account init successfully. account size = {}", accounts.size());
    }

    @PreDestroy
    public void close() {
        endpointManager.close();
        log.info("cmpp server shutdown now.");
    }

    public void join(KawaiiCmppClient kawaiiCmppClient) {
        serverEndpoint.addchild(kawaiiCmppClient);
        clientMapping.put(kawaiiCmppClient.getMsgSrc(), kawaiiCmppClient);
    }


    public KawaiiCmppClient getKawaiiCmppClientByUsername(String username) {
        return (KawaiiCmppClient) serverEndpoint.getChild(username);
    }


    public KawaiiCmppClient getKawaiiCmppClientByMsgSrc(String msgSrc) {
        return clientMapping.get(msgSrc);
    }

}
