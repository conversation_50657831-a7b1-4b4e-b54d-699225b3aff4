package com.xhqb.spectre.kawaii.net.handler;

import com.xhqb.spectre.kawaii.store.KawaiiStore;
import com.zx.sms.codec.cmpp.msg.CmppSubmitRequestMessage;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/7/26
 */
@Slf4j
public class KawaiiCmppSubmitRequestHandler extends SimpleChannelInboundHandler<CmppSubmitRequestMessage> {

    private KawaiiStore kawaiiStore;

    public KawaiiCmppSubmitRequestHandler(KawaiiStore kawaiiStore) {
        this.kawaiiStore = kawaiiStore;
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, CmppSubmitRequestMessage msg) throws Exception {
        log.info("收到提交短信 = {}", msg);
        kawaiiStore.saveCmppSubmitRequestMessage(msg);
    }
}
