package com.xhqb.spectre.kawaii.store;

import com.zx.sms.codec.cmpp.msg.CmppSubmitRequestMessage;
import com.zx.sms.codec.cmpp.msg.CmppSubmitResponseMessage;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/26
 */
public interface KawaiiStore {

    /**
     * 保存cmpp提交的消息
     *
     * @param message
     */
    void saveCmppSubmitRequestMessage(CmppSubmitRequestMessage message);

    /**
     * 根据手机号+sequenceId 查询cmpp提交的信息
     *
     * @param storeKey
     * @return
     */
    List<CmppSubmitResponseMessage> selectCmppSubmitRequestMessage(StoreKey storeKey);

    /**
     * 移除cmpp提交信息
     *
     * @param storeKey
     */
    void remCmppSubmitRequestMessage(StoreKey storeKey);
}
