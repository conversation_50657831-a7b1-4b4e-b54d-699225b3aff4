package com.xhqb.spectre.dal.mapper;

import com.xhqb.spectre.dal.bean.SmsReceipt;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2024/7/26
 */
@Repository
public interface SmsReceiptMapper extends JpaRepository<SmsReceipt, Long> {

    /**
     * 查询提交短信回执信息
     *
     * @param mobile
     * @param sequenceId
     * @param msgSrc
     * @return
     */
    @Query(value = "SELECT * FROM t_sms_receipt WHERE mobile=:mobile and sequence_id=:sequenceId and msg_src=:msgSrc and submit_status=0 LIMIT 1", nativeQuery = true)
    SmsReceipt selectSubmitSmsReceipt(String mobile, String sequenceId, String msgSrc);


    /**
     * 查询提交短信回执信息
     *
     * @param mobile
     * @param sequenceId
     * @param msgSrc
     * @return
     */
    @Query(value = "SELECT * FROM t_sms_receipt WHERE mobile=:mobile and sequence_id=:sequenceId and msg_src=:msgSrc and submit_status=1 and send_status=0 and is_delete=0 LIMIT 1", nativeQuery = true)
    SmsReceipt selectSendSmsReceipt(String mobile, String sequenceId, String msgSrc);
}
