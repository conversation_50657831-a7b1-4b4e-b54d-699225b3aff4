package com.xhqb.spectre.route.model.context;

import com.xhqb.spectre.route.model.vo.ChannelStat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/30 18:29
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelStatContext implements Serializable {

    private static final long serialVersionUID = 1989579746896395376L;

    /**
     * 渠道异常数据
     */
    private List<ChannelStat> exceptionList = new ArrayList<>();

    /**
     * 渠道严重异常数据（需要暂停较长时间）
     */
    private List<ChannelStat> severeList = new ArrayList<>();

    /**
     * 渠道恢复数据（之前已暂停的渠道，此次数据恢复到正常）
     */
    private List<String> recoverList = new ArrayList<>();
}
