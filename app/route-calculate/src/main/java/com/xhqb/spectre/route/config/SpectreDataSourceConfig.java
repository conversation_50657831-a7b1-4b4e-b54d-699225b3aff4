package com.xhqb.spectre.route.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.xhqb.kael.boot.autoconfigure.druid.DatasourceConfigUtils;
import com.xhqb.kael.boot.autoconfigure.druid.DefaultConnectionProperties;
import com.xhqb.kael.boot.autoconfigure.druid.DruidConnectionProperties;
import com.xhqb.spectre.common.utils.CommonUtil;
import com.xhqb.spectre.route.algorithm.YearMonthShardingAlgorithm;
import com.xhqb.spectre.route.config.properties.SpectreDruidProperties;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.shardingsphere.api.config.sharding.ShardingRuleConfiguration;
import org.apache.shardingsphere.api.config.sharding.TableRuleConfiguration;
import org.apache.shardingsphere.api.config.sharding.strategy.StandardShardingStrategyConfiguration;
import org.apache.shardingsphere.shardingjdbc.api.ShardingDataSourceFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/*
 * @Author: yjq
 * @Date: 2024/07/30 17:51
 * @Description:
 */
@Configuration
@MapperScan(basePackages = "com.xhqb.spectre.common.dal.mapper", sqlSessionTemplateRef = "spectreSqlSessionTemplate")
@EnableConfigurationProperties(SpectreDruidProperties.class)
public class SpectreDataSourceConfig {


    private static final String DB_NAME = "spectredb";

    @Resource
    private VenusConfig venusConfig;


    @Bean(name = "spectreShardingDataSource")
    public DataSource spectreShardingReadDataSource(@Qualifier("spectreDataSource") DataSource dataSource) throws SQLException {
        return ShardingDataSourceFactory.createDataSource(createDataSourceMap(dataSource),
                createShardingRuleConfiguration(),
                createProperties());
    }

    private Map<String, DataSource> createDataSourceMap(DataSource dataSource) {
        Map<String, DataSource> dataSourceMap = new HashMap<>(1);
        dataSourceMap.put(DB_NAME, dataSource);
        return dataSourceMap;
    }

    private ShardingRuleConfiguration createShardingRuleConfiguration() {
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();

        YearMonthShardingAlgorithm orderShardingAlgorithm = new YearMonthShardingAlgorithm("t_sms_order", venusConfig);
        final String orderDataNodes = DB_NAME + "." + "t_sms_order" + "_" + "${2024..2026}${['01','02','03','04','05','06','07','08','09','10','11','12']}";
        TableRuleConfiguration orderRule = new TableRuleConfiguration("t_sms_order", orderDataNodes);
        orderRule.setTableShardingStrategyConfig(new StandardShardingStrategyConfiguration("send_time",
                orderShardingAlgorithm, orderShardingAlgorithm));

        shardingRuleConfig.getTableRuleConfigs().add(orderRule);
        return shardingRuleConfig;
    }

    private Properties createProperties() {
        Properties prop = new Properties();
        prop.setProperty("sql.show", venusConfig.getSqlPrintEnable());
        return prop;
    }


    /**
     * spectre数据源
     *
     * @param spectreDruidProperties
     * @return
     */
    @Bean(name = "spectreDataSource")
    public DataSource spectreDataSource(SpectreDruidProperties spectreDruidProperties) {
        DruidConnectionProperties defaultProperties = DruidConnectionProperties.withDefault(new DefaultConnectionProperties());
        BeanUtils.copyProperties(spectreDruidProperties, defaultProperties, CommonUtil.getNullPropertyNames(spectreDruidProperties));
        DruidDataSource dataSource = DatasourceConfigUtils.createDataSource(defaultProperties);
        dataSource.setName("spectre");
        return dataSource;
    }

    /**
     * session factory
     *
     * @param dataSource
     * @return
     * @throws Exception
     */
    @Bean(name = "spectreSqlSessionFactory")
    @Primary
    public SqlSessionFactory spectreSqlSessionFactory(@Qualifier("spectreShardingDataSource") DataSource dataSource, ObjectProvider<List<Interceptor>> interceptorObjectProvider) throws Exception {
        final SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        //设置加解密插件
        List<Interceptor> interceptorList = interceptorObjectProvider.getIfAvailable();
        if (CollectionUtils.isNotEmpty(interceptorList)) {
            bean.setPlugins(interceptorList.toArray(new Interceptor[0]));
        }
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*Mapper.xml"));
        return bean.getObject();
    }

    /**
     * transaction manager
     *
     * @param dataSource
     * @return
     */
    @Bean(name = "spectreTransactionManager")
    @Primary
    public DataSourceTransactionManager spectreTransactionManager(@Qualifier("spectreShardingDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * build sql session template
     *
     * @param sqlSessionFactory
     * @return
     */
    @Bean(name = "spectreSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate spectreSqlSessionTemplate(@Qualifier("spectreSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
