package com.xhqb.spectre.route.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/*
 * @Author: huangyanxi<PERSON>
 * @Date: 2022/8/29 16:21
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum RespCodeEnum {

    /**
     * 请求成功：0
     */
    SUCCESS(0, "请求处理成功"),

    /**
     * 前端异常：1000-9999
     */
    PARAM_ERROR(1000, "请求参数异常"),

    /**
     * 业务场景异常：10000-19999
     */
    BUSINESS_ERROR(11001, "业务异常"),

    /**
     * 后端服务异常：20000-29999
     */
    SYSTEM_ERROR(20001, "系统异常");
    ;

    private Integer code;

    private String msg;
}
