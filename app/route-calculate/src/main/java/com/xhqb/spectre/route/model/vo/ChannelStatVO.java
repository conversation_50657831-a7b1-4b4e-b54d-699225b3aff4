package com.xhqb.spectre.route.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/31 11:21
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelStatVO implements Serializable {

    private static final long serialVersionUID = -6876243102182114089L;

    /**
     * 渠道维度异常数据
     */
    private List<ChannelStat> channelList;

    /**
     * 渠道+运营商维度异常数据
     */
    private List<ChannelStat> channelIspList;
}
