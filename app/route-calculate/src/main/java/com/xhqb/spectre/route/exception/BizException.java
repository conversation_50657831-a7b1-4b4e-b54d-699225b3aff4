package com.xhqb.spectre.route.exception;

import com.xhqb.spectre.route.enums.RespCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/29 16:23
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizException extends RuntimeException {

    private static final long serialVersionUID = 2864769675659251522L;

    private static final int PARAM_ERROR_CODE = 1000;

    private Integer code;
    private String msg;

    public BizException(Integer code, String msg) {
        super(code + ":" + msg);
        this.code = code;
        this.msg = msg;
    }

    public BizException(RespCodeEnum codeEnum) {
        super(codeEnum.getCode() + ":" + codeEnum.getMsg());
        this.code = codeEnum.getCode();
        this.msg = codeEnum.getMsg();
    }

    public BizException(RespCodeEnum codeEnum, String msg) {
        super(codeEnum.getCode() + ":" + msg);
        this.code = codeEnum.getCode();
        this.msg = msg;
    }

    public BizException(String msg) {
        super(PARAM_ERROR_CODE + ":" + msg);
        this.code = PARAM_ERROR_CODE;
        this.msg = msg;
    }
}
