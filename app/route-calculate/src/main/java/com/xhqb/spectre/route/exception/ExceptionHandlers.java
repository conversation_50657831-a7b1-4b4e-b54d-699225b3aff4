package com.xhqb.spectre.route.exception;

import com.xhqb.spectre.route.enums.RespCodeEnum;
import com.xhqb.spectre.route.model.result.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/29 16:27
 * @Description:
 */
@Slf4j
@ResponseBody
@ControllerAdvice
public class ExceptionHandlers {

    @ExceptionHandler(BizException.class)
    protected BaseResult<Void> bizExceptionHandler(final BizException e) {
        log.warn(e.getMessage());
        return BaseResult.error(e);
    }

    @ExceptionHandler(Exception.class)
    protected BaseResult<Void> serverExceptionHandler(final Exception e) {
        log.error(e.getMessage(), e);
        return BaseResult.error(RespCodeEnum.SYSTEM_ERROR);
    }

    @ExceptionHandler(Throwable.class)
    protected BaseResult<Void> throwableHandler(final Throwable e) {
        log.error(e.getMessage(), e);
        return BaseResult.error(RespCodeEnum.SYSTEM_ERROR);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    protected BaseResult<Void> handleHttpRequestMethodNotSupportedException(final HttpRequestMethodNotSupportedException e) {
        log.warn(e.getMessage(), e);
        return BaseResult.error("不支持" + e.getMethod() + "请求方法");
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    protected BaseResult<Void> handleMissingServletRequestParameterException(final MissingServletRequestParameterException e) {
        log.warn(e.getMessage(), e);
        return BaseResult.error(RespCodeEnum.PARAM_ERROR);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    protected BaseResult<Void> handleMethodArgumentTypeMismatchException(final MethodArgumentTypeMismatchException e) {
        log.warn(e.getMessage(), e);
        return BaseResult.error(RespCodeEnum.PARAM_ERROR);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    protected BaseResult<Void> handleHttpMessageNotReadableException(final HttpMessageNotReadableException e) {
        log.warn(e.getMessage(), e);
        return BaseResult.error(RespCodeEnum.PARAM_ERROR);
    }

    @ExceptionHandler(BindException.class)
    protected BaseResult<Void> handleBindException(final BindException e) {
        log.warn(e.getMessage());
        return BaseResult.error(RespCodeEnum.PARAM_ERROR.getCode(), "请求参数格式有误");
    }
}
