package com.xhqb.spectre.route.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/29 16:29
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelStat implements Serializable {

    private static final long serialVersionUID = 6500091198876863979L;

    /**
     * 渠道账号ID
     */
    private Integer channelAccountId;

    /**
     * 运营商
     */
    private String isp;

    /**
     * 比例系数（单位：百分位。例如原比例系数为0.2，乘以100，得到最终的比例系数20）
     */
    private Integer scaleFactor;

    public static ChannelStat build(Integer channelAccountId, String isp, Integer scaleFactor) {
        return ChannelStat.builder()
                .channelAccountId(channelAccountId)
                .isp(isp)
                .scaleFactor(scaleFactor)
                .build();
    }
}
