package com.xhqb.spectre.route.controller;

import com.xhqb.spectre.route.model.result.BaseResult;
import com.xhqb.spectre.route.model.vo.ChannelStatVO;
import com.xhqb.spectre.route.service.ChannelStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/31 10:49
 * @Description:
 */
@RestController
@RequestMapping("/")
@Slf4j
public class ChannelStatController {

    @Resource
    private ChannelStatService channelStatService;

    /**
     * 查询实时异常渠道
     *
     * @return
     */
    @GetMapping("/queryRecentBadChannel")
    public BaseResult<ChannelStatVO> queryRecentBadChannel() {
        ChannelStatVO channelStatVO = channelStatService.queryRealTimeStat();
        return BaseResult.success(channelStatVO);
    }

}
