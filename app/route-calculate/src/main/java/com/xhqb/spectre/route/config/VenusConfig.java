package com.xhqb.spectre.route.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/29 14:53
 * @Description:
 */
@Component
@Data
public class VenusConfig {

    /**
     * 查询渠道数据的开始时间条件，表示距今多少分钟
     */
    @Value("${query.begin.since.minute:6}")
    private Integer queryBeginSince;

    /**
     * 查询渠道数据的结束时间条件，表示距今多少分钟
     */
    @Value("${query.end.since.minute:1}")
    private Integer queryEndSince;

    /**
     * 营销短信，查询渠道数据的开始时间条件，表示距今多少分钟
     */
    @Value("${market-query.begin.since.minute:11}")
    private Integer queryMarketBeginSince;

    /**
     * 营销短信，查询渠道数据的结束时间条件，表示距今多少分钟
     */
    @Value("${market-query.end.since.minute:1}")
    private Integer queryMarketEndSince;

    /**
     * 营销短信，渠道维度的最低样本数
     */
    @Value("${market.channel.sample:40}")
    private Integer marketChannelSample;

    /**
     * 营销短信，渠道+运营商维度的最低样本数
     */
    @Value("${market.isp.sample:15}")
    private Integer marketIspSample;

    /**
     * 营销短信，渠道维度异常恢复的最低样本数
     */
    @Value("${market.recover.channel.sample:10}")
    private Integer marketRecoverChannelSample;

    /**
     * 营销短信，渠道+运营商维度异常恢复的最低样本数
     */
    @Value("${market.recover.isp.sample:4}")
    private Integer marketRecoverIspSample;

    @Value("${sharding.sql.print.enable:false}")
    private String sqlPrintEnable;

    /**
     * 2024-01-31 23:59:59
     */
    @Value("${sharding.start.time.millis:1706716799000}")
    private volatile long shardingStartTimeMillis;
}
