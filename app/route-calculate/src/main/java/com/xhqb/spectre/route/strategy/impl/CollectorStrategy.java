package com.xhqb.spectre.route.strategy.impl;

import com.xhqb.spectre.common.enums.MessageTypeEnum;
import com.xhqb.spectre.route.constant.ChannelStatConstant;
import com.xhqb.spectre.route.strategy.AbstractSmsTypeStrategy;
import org.springframework.stereotype.Service;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/29 19:06
 * @Description:
 */
@Service
public class CollectorStrategy extends AbstractSmsTypeStrategy {

    /**
     * 短信类型，用于策略加载
     *
     * @return
     */
    @Override
    public String type() {
        return MessageTypeEnum.COLLECTOR.getMessageType();
    }

    /**
     * 计算渠道比例系数
     *
     * @param successRate
     * @return
     */
    @Override
    protected int doCalcScaleFactor(int successRate) {
        if (successRate >= 90) {
            return ChannelStatConstant.SCALE_FACTOR_100;
        } else if (successRate >= 80) {
            return ChannelStatConstant.SCALE_FACTOR_80;
        } else if (successRate >= 70) {
            return ChannelStatConstant.SCALE_FACTOR_60;
        } else if (successRate >= 60) {
            return ChannelStatConstant.SCALE_FACTOR_40;
        } else if (successRate >= 40) {
            return ChannelStatConstant.SCALE_FACTOR_20;
        } else if (successRate > 0) {
            return ChannelStatConstant.SCALE_FACTOR_10;
        } else {
            return ChannelStatConstant.SCALE_FACTOR_0;
        }
    }

    /**
     * 判断严重异常渠道是否已恢复正常
     *
     * @param successRate
     * @return
     */
    @Override
    protected boolean isRecover(int successRate) {
        return successRate >= ChannelStatConstant.RECOVER_SR_COLLECTOR;
    }
}
