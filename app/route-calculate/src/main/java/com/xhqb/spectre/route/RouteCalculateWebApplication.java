package com.xhqb.spectre.route;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/26 16:50
 * @Description:
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableScheduling
public class RouteCalculateWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(RouteCalculateWebApplication.class, args);
    }

}
