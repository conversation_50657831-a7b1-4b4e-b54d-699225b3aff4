package com.xhqb.spectre.route.strategy;

import com.xhqb.spectre.route.constant.ChannelStatConstant;
import com.xhqb.spectre.route.util.ChannelStatUtil;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/30 14:35
 * @Description:
 */
public abstract class AbstractSmsTypeStrategy implements SmsTypeStrategy {

    /**
     * 计算渠道比例系数
     *
     * @param successCount
     * @param totalCount
     * @param type
     * @return
     */
    @Override
    public int calcScaleFactor(int successCount, int totalCount, int type) {
        //判断样本数
        int sampleSize = getMinSampleSize(type);
        if (totalCount < sampleSize) {
            return ChannelStatConstant.SCALE_FACTOR_100;
        }
        int successRate = ChannelStatUtil.calcSuccessRate(successCount, totalCount);

        return doCalcScaleFactor(successRate);
    }

    /**
     * 判断严重异常渠道是否已恢复正常
     *
     * @param successCount
     * @param totalCount
     * @param type
     * @return
     */
    @Override
    public boolean isRecover(int successCount, int totalCount, int type) {
        //判断样本数
        int sampleSize = getRecoverMinSampleSize(type);
        if (totalCount < sampleSize) {
            return false;
        }
        int successRate = ChannelStatUtil.calcSuccessRate(successCount, totalCount);

        return isRecover(successRate);
    }

    /**
     * 计算渠道比例系数
     *
     * @param successRate
     * @return
     */
    protected abstract int doCalcScaleFactor(int successRate);

    /**
     * 判断严重异常渠道是否已恢复正常
     *
     * @param successRate
     * @return
     */
    protected abstract boolean isRecover(int successRate);
}
