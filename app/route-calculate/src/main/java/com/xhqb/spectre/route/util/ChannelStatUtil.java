package com.xhqb.spectre.route.util;

/*
 * @Author: h<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/8/29 17:56
 * @Description:
 */
public final class ChannelStatUtil {

    /**
     * 计算成功率，返回百分位数据
     *
     * @param successCount
     * @param totalCount
     * @return
     */
    public static int calcSuccessRate(int successCount, int totalCount) {
        if (successCount == 0 || totalCount == 0) {
            return 0;
        }
        return Math.round((float) (successCount * 100) / totalCount);
    }

}
