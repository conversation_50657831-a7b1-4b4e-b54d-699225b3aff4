package com.xhqb.spectre.route.model.result;

import com.xhqb.spectre.route.enums.RespCodeEnum;
import com.xhqb.spectre.route.exception.BizException;
import lombok.Data;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/29 16:25
 * @Description:
 */
@Data
public class BaseResult<T> implements Serializable {

    private static final long serialVersionUID = -3611271290010374139L;

    private Integer code;

    private String msg;

    private T data;

    public BaseResult(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> BaseResult<T> success(T data) {
        return new BaseResult<>(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMsg(), data);
    }

    public static <T> BaseResult<T> success() {
        return success(null);
    }

    public static <T> BaseResult<T> error(int code, String msg) {
        return new BaseResult<>(code, msg, null);
    }

    public static <T> BaseResult<T> error(RespCodeEnum codeEnum) {
        return new BaseResult<>(codeEnum.getCode(), codeEnum.getMsg(), null);
    }

    public static <T> BaseResult<T> error(BizException e) {
        return new BaseResult<>(e.getCode(), e.getMsg(), null);
    }

    public static <T> BaseResult<T> error(String msg) {
        return new BaseResult<>(RespCodeEnum.SYSTEM_ERROR.getCode(), msg, null);
    }

    public static <T> BaseResult<T> systemError() {
        return error(RespCodeEnum.SYSTEM_ERROR);
    }
}
