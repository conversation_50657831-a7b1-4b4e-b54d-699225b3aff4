package com.xhqb.spectre.route.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xhqb.spectre.common.dal.entity.SmsOrderStatDO;
import com.xhqb.spectre.common.dal.mapper.SmsOrderMapper;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import com.xhqb.spectre.common.utils.DateUtil;
import com.xhqb.spectre.route.config.VenusConfig;
import com.xhqb.spectre.route.constant.ChannelStatConstant;
import com.xhqb.spectre.route.model.context.ChannelStatContext;
import com.xhqb.spectre.route.model.vo.ChannelStat;
import com.xhqb.spectre.route.model.vo.ChannelStatVO;
import com.xhqb.spectre.route.service.ChannelStatService;
import com.xhqb.spectre.route.strategy.SmsTypeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/29 14:59
 * @Description:
 */
@Service
@Slf4j
public class ChannelStatServiceImpl implements ChannelStatService {

    @Resource
    private VenusConfig venusConfig;

    @Resource
    private SmsOrderMapper smsOrderMapper;

    /**
     * 渠道维度，异常渠道数据缓存，key为渠道账号ID
     */
    private final Cache<String, ChannelStat> channelStatCache = CacheBuilder.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(2, TimeUnit.MINUTES)
            .build();

    /**
     * 渠道维度，异常渠道数据缓存，key为渠道账号ID。用于成功率很低的场景，需要暂停渠道一段时间
     */
    private final Cache<String, ChannelStat> severeChannelStatCache = CacheBuilder.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    /**
     * 渠道+运营商维度，异常渠道数据缓存，key为渠道账号ID+运营商
     */
    private final Cache<String, ChannelStat> channelIspStatCache = CacheBuilder.newBuilder()
            .maximumSize(2000)
            .expireAfterWrite(2, TimeUnit.MINUTES)
            .build();

    /**
     * 渠道+运营商维度，异常渠道数据缓存，key为渠道账号ID+运营商。用于成功率很低的场景，需要暂停渠道一段时间
     */
    private final Cache<String, ChannelStat> severeChannelIspStatCache = CacheBuilder.newBuilder()
            .maximumSize(2000)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    /**
     * 短信类型策略列表
     */
    private static final Map<String, SmsTypeStrategy> SMS_TYPE_STRATEGY_MAP = new HashMap<>();

    public ChannelStatServiceImpl(ObjectProvider<List<SmsTypeStrategy>> listObjectProvider) {
        List<SmsTypeStrategy> strategyList = listObjectProvider.getIfAvailable();
        if (CollectionUtils.isEmpty(strategyList)) {
            return;
        }
        for (SmsTypeStrategy strategy : strategyList) {
            SMS_TYPE_STRATEGY_MAP.put(strategy.type(), strategy);
        }
    }

    /**
     * 计算渠道实时异常数据
     */
    @Override
    public void calcRealTimeStat() {
        //获取查询时间参数
        Integer now = DateUtil.getNow();
        Integer beginTime = now - venusConfig.getQueryBeginSince() * 60;
        Integer endTime = now - venusConfig.getQueryEndSince() * 60;
        Integer marketBeginTime = now - venusConfig.getQueryMarketBeginSince() * 60;
        Integer marketEndTime = now - venusConfig.getQueryMarketEndSince() * 60;

        //查询渠道维度数据
        List<SmsOrderStatDO> channelStatList = smsOrderMapper.selectStatByChannel(beginTime, endTime);
        List<SmsOrderStatDO> marketChannelStatList = smsOrderMapper.selectMarketStatByChannel(marketBeginTime, marketEndTime);
        channelStatList = mergeList(channelStatList, marketChannelStatList);

        //查询渠道+运营商维度数据
        List<SmsOrderStatDO> ispStatList = smsOrderMapper.selectStatByIsp(beginTime, endTime);
        List<SmsOrderStatDO> marketIspStatList = smsOrderMapper.selectMarketStatByIsp(marketBeginTime, marketEndTime);
        ispStatList = mergeList(ispStatList, marketIspStatList);

        //处理渠道维度数据
        handleChannelStat(channelStatList);

        //处理渠道+运营商维度数据
        handleChannelIspStat(ispStatList);
    }

    /**
     * 查询渠道实时异常数据
     * @return
     */
    @Override
    public ChannelStatVO queryRealTimeStat() {
        //查询渠道维度
        Map<String, ChannelStat> channelMap = cache2Map(channelStatCache);
        Map<String, ChannelStat> severeChannelMap = cache2Map(severeChannelStatCache);
        //合并map，以严重异常的数据优先
        mergeMap(severeChannelMap, channelMap);
        List<ChannelStat> channelList = new ArrayList<>(severeChannelMap.values());

        //查询渠道+运营商维度
        Map<String, ChannelStat> channelIspMap = cache2Map(channelIspStatCache);
        Map<String, ChannelStat> severeChannelIspMap = cache2Map(severeChannelIspStatCache);
        //合并map，以严重异常的数据优先
        mergeMap(severeChannelIspMap, channelIspMap);
        List<ChannelStat> channelIspList = new ArrayList<>(severeChannelIspMap.values());

        return ChannelStatVO.builder()
                .channelList(channelList)
                .channelIspList(channelIspList)
                .build();
    }

    /**
     * 处理渠道维度数据
     * @param list
     */
    private void handleChannelStat(List<SmsOrderStatDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            //清空缓存
            channelStatCache.invalidateAll();
            return;
        }
        //计算异常数据
        ChannelStatContext context = doHandleStat(list, ChannelStatConstant.STAT_TYPE_CHANNEL);
        log.info("渠道维度异常数据：{}", JSON.toJSONString(context));

        //更新异常数据
        channelStatCache.invalidateAll();
        context.getExceptionList().forEach(item -> channelStatCache.put(buildChannelKey(item.getChannelAccountId()), item));

        //更新严重异常数据
        context.getRecoverList().forEach(severeChannelStatCache::invalidate);
        context.getSevereList().forEach(item -> severeChannelStatCache.put(buildChannelKey(item.getChannelAccountId()), item));
    }

    /**
     * 处理渠道+运营商维度数据
     * @param list
     */
    private void handleChannelIspStat(List<SmsOrderStatDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            //清空缓存
            channelIspStatCache.invalidateAll();
            return;
        }
        //计算异常数据
        ChannelStatContext context = doHandleStat(list, ChannelStatConstant.STAT_TYPE_CHANNEL_ISP);
        log.info("渠道+运营商维度异常数据：{}", JSON.toJSONString(context));

        //更新异常数据
        channelIspStatCache.invalidateAll();
        context.getExceptionList().forEach(item -> channelIspStatCache.put(buildChannelIspKey(item.getChannelAccountId(), item.getIsp()), item));

        //更新严重异常数据
        context.getRecoverList().forEach(severeChannelIspStatCache::invalidate);
        context.getSevereList().forEach(item -> severeChannelIspStatCache.put(buildChannelIspKey(item.getChannelAccountId(), item.getIsp()), item));
    }

    /**
     * 计算渠道异常数据
     * @param statList
     * @param type
     * @return
     */
    private ChannelStatContext doHandleStat(List<SmsOrderStatDO> statList, int type) {
        List<ChannelStat> exceptionList = new ArrayList<>();
        List<ChannelStat> severeList = new ArrayList<>();
        List<String> recoverList = new ArrayList<>();

        //获取当前的严重异常数据
        Map<String, ChannelStat> lastSevereMap = getLastSevereMap(type);
        for (SmsOrderStatDO item : statList) {
            String smsTypeCode = item.getSmsTypeCode();
            SmsTypeStrategy strategy = SMS_TYPE_STRATEGY_MAP.get(smsTypeCode);
            if (strategy == null) {
                log.warn("不支持的短信类型：{}", smsTypeCode);
                continue;
            }
            int totalCount = item.getTotalCount();
            int successCount = item.getSuccessCount();

            //判断之前的严重异常数据是否已恢复正常
            String cacheKey = buildCacheKey(item.getChannelAccountId(), item.getIsp(), type);
            if (lastSevereMap.containsKey(cacheKey) && strategy.isRecover(successCount, totalCount, type)) {
                recoverList.add(cacheKey);
            }

            //计算渠道比例系数
            int scaleFactor = strategy.calcScaleFactor(successCount, totalCount, type);
            if (scaleFactor >= ChannelStatConstant.SCALE_FACTOR_100) {
                //正常渠道，跳过
                continue;
            }
            //添加异常数据
            String isp = type == ChannelStatConstant.STAT_TYPE_CHANNEL_ISP ? item.getIsp() : null;
            ChannelStat channelStatVO = ChannelStat.build(item.getChannelAccountId(), isp, scaleFactor);
            exceptionList.add(channelStatVO);

            //添加严重异常数据
            if (scaleFactor <= ChannelStatConstant.SCALE_FACTOR_10) {
                ChannelStat severeChannelStatVO = ChannelStat.build(item.getChannelAccountId(), isp, scaleFactor);
                severeList.add(severeChannelStatVO);
            }
        }

        return ChannelStatContext.builder()
                .exceptionList(exceptionList)
                .severeList(severeList)
                .recoverList(recoverList)
                .build();
    }

    /**
     * 获取当前的严重异常数据
     * @param type
     * @return
     */
    private Map<String, ChannelStat> getLastSevereMap(int type) {
        if (type == ChannelStatConstant.STAT_TYPE_CHANNEL) {
            return cache2Map(severeChannelStatCache);
        }
        return cache2Map(severeChannelIspStatCache);
    }

    private String buildCacheKey(Integer channelAccountId, String isp, int type) {
        if (type == ChannelStatConstant.STAT_TYPE_CHANNEL) {
            return buildChannelKey(channelAccountId);
        }
        return buildChannelIspKey(channelAccountId, isp);
    }

    private String buildChannelKey(Integer channelAccountId) {
        return String.valueOf(channelAccountId);
    }

    private String buildChannelIspKey(Integer channelAccountId, String isp) {
        return channelAccountId + "_" + isp;
    }

    private Map<String, ChannelStat> cache2Map(Cache<String, ChannelStat> cache) {
        Map<String, ChannelStat> map = new HashMap<>();
        ConcurrentMap<String, ChannelStat> cacheMap = cache.asMap();
        if (MapUtils.isNotEmpty(cacheMap)) {
            map.putAll(cacheMap);
        }
        return map;
    }

    /**
     * map合并，如果key已存在，则不覆盖
     * @param baseMap
     * @param map
     */
    private void mergeMap(Map<String, ChannelStat> baseMap, Map<String, ChannelStat> map) {
        for (String key : map.keySet()) {
            if (!baseMap.containsKey(key)) {
                baseMap.put(key, map.get(key));
            }
        }
    }

    /**
     * 合并营销短信渠道列表
     * @param statList
     * @param marketStatList
     * @return
     */
    private List<SmsOrderStatDO> mergeList(List<SmsOrderStatDO> statList, List<SmsOrderStatDO> marketStatList) {
        List<SmsOrderStatDO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(statList)) {
            for (SmsOrderStatDO item : statList) {
                if (MessageTypeEnum.MARKET.getMessageType().equals(item.getSmsTypeCode())) {
                    //营销类型，跳过，以marketStatList为准
                    continue;
                }
                list.add(item);
            }
        }
        if (CollectionUtils.isNotEmpty(marketStatList)) {
            list.addAll(marketStatList);
        }
        return list;
    }
}
