package com.xhqb.spectre.route.algorithm;

import com.xhqb.spectre.common.utils.ShardingUtils;
import com.xhqb.spectre.route.config.VenusConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingValue;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


/**
 * 根据发送时间进行按月分片
 *
 * @author: yjq
 * @date: 2023/12/26
 */
@Slf4j
public class YearMonthShardingAlgorithm implements PreciseShardingAlgorithm<Integer>, RangeShardingAlgorithm<Integer> {

    /**
     * 表名
     */
    private final String tableName;

    private volatile VenusConfig venusConfig;

    public YearMonthShardingAlgorithm(String tableName, VenusConfig venusConfig) {
        this.tableName = tableName;
        this.venusConfig = venusConfig;
    }

    @Override
    public String doSharding(Collection<String> collection, PreciseShardingValue<Integer> preciseShardingValue) {
        Long value = preciseShardingValue.getValue() * 1000L;
        if (value > venusConfig.getShardingStartTimeMillis()) {
            return ShardingUtils.getTableNameByTime(tableName, value);
        } else {
            return tableName;
        }
    }

    @Override
    public Collection<String> doSharding(Collection<String> collection, RangeShardingValue<Integer> rangeShardingValue) {
        long lowerEndpoint = rangeShardingValue.getValueRange().lowerEndpoint() * 1000L;
        long upperEndpoint = rangeShardingValue.getValueRange().upperEndpoint() * 1000L;

        List<String> result = new ArrayList<>();
        if (upperEndpoint <= venusConfig.getShardingStartTimeMillis()) {
            result.add(tableName);
            return result;
        }

        result.addAll(ShardingUtils.getTableNameByTimeRange(tableName, lowerEndpoint, upperEndpoint));
        return result;
    }
}
