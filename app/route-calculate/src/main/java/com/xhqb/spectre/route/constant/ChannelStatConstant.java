package com.xhqb.spectre.route.constant;

/*
 * @Author: huangyan<PERSON><PERSON>
 * @Date: 2022/8/29 19:10
 * @Description:
 */
public final class ChannelStatConstant {

    /**
     * 统计维度的最低样本数
     */
    public static final int SAMPLE_SIZE_CHANNEL = 20;
    public static final int SAMPLE_SIZE_CHANNEL_ISP = 7;

    /**
     * 营销统计维度的最低样本数
     */
    public static final int MARKET_SAMPLE_SIZE_CHANNEL = 40;
    public static final int MARKET_SAMPLE_SIZE_CHANNEL_ISP = 15;

    /**
     * 渠道恢复的最低样本数
     */
    public static final int RECOVER_SAMPLE_SIZE_CHANNEL = 7;
    public static final int RECOVER_SAMPLE_SIZE_CHANNEL_ISP = 3;

    /**
     * 营销渠道恢复的最低样本数
     */
    public static final int MARKET_RECOVER_SAMPLE_SIZE_CHANNEL = 10;
    public static final int MARKET_RECOVER_SAMPLE_SIZE_CHANNEL_ISP = 4;


    /**
     * 渠道恢复的默认成功率阈值（大于等于该阈值恢复正常）
     */
    public static final int RECOVER_SR_VERIFY = 80;
    public static final int RECOVER_SR_NOTIFY = 80;
    public static final int RECOVER_SR_MARKET = 50;
    public static final int RECOVER_SR_COLLECTOR = 70;
    public static final int RECOVER_SR_LIGHT_COLLECTOR = 60;
    public static final int RECOVER_SR_SEVERE_COLLECTOR = 60;

    /**
     * 渠道比例系数
     */
    public static final int SCALE_FACTOR_100 = 100;
    public static final int SCALE_FACTOR_80 = 80;
    public static final int SCALE_FACTOR_60 = 60;
    public static final int SCALE_FACTOR_40 = 40;
    public static final int SCALE_FACTOR_20 = 20;
    public static final int SCALE_FACTOR_10 = 10;
    public static final int SCALE_FACTOR_0 = 0;

    /**
     * 统计数据类型（1：渠道；2：渠道+运营商）
     */
    public static final int STAT_TYPE_CHANNEL = 1;
    public static final int STAT_TYPE_CHANNEL_ISP = 2;
}
