package com.xhqb.spectre.route.strategy;

import com.xhqb.spectre.route.constant.ChannelStatConstant;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/29 18:48
 * @Description: 异常渠道统计策略，根据短信类型区分
 */
public interface SmsTypeStrategy {

    /**
     * 短信类型，用于策略加载
     *
     * @return
     */
    String type();

    /**
     * 计算渠道比例系数
     *
     * @param successCount
     * @param totalCount
     * @param type
     * @return
     */
    int calcScaleFactor(int successCount, int totalCount, int type);

    /**
     * 判断异常渠道是否已恢复正常
     *
     * @param successCount
     * @param totalCount
     * @param type
     * @return
     */
    boolean isRecover(int successCount, int totalCount, int type);

    /**
     * 获取最低样本数
     *
     * @param type
     * @return
     */
    default int getMinSampleSize(int type) {
        if (type == ChannelStatConstant.STAT_TYPE_CHANNEL_ISP) {
            return ChannelStatConstant.SAMPLE_SIZE_CHANNEL_ISP;
        }
        return ChannelStatConstant.SAMPLE_SIZE_CHANNEL;
    }

    /**
     * 获取渠道恢复的最低样本数
     *
     * @param type
     * @return
     */
    default int getRecoverMinSampleSize(int type) {
        if (type == ChannelStatConstant.STAT_TYPE_CHANNEL_ISP) {
            return ChannelStatConstant.RECOVER_SAMPLE_SIZE_CHANNEL_ISP;
        }
        return ChannelStatConstant.RECOVER_SAMPLE_SIZE_CHANNEL;
    }
}
