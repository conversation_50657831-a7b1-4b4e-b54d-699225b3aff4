package com.xhqb.spectre.route.job;

import com.xhqb.spectre.route.service.ChannelStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/29 14:50
 * @Description:
 */
@Component
@Slf4j
public class ChannelStatCalculateJob {

    @Resource
    private ChannelStatService channelStatService;

    /**
     * 统计实时异常渠道数据，默认1分钟执行一次
     */
    @Scheduled(cron = "${channel-stat.calc.cron:0 */1 * * * ?}")
    public void execute() {
        log.info("统计实时异常渠道数据");
        channelStatService.calcRealTimeStat();
    }

}
