package com.xhqb.spectre.deliver.udp.client;

import com.google.protobuf.InvalidProtocolBufferException;
import com.xhqb.spectre.common.msg.Message;
import com.xhqb.spectre.common.protobuf.DeliverProto;
import com.xhqb.spectre.common.utils.SequenceNumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;

@SpringBootApplication
@Slf4j
public class DeliverUdpClientApplication implements CommandLineRunner {

    @Value("${udp.server.ip}")
    private String udpIp;

    @Value("${udp.server.port}")
    private int udpPort;

    @Value("${cmpp.username}")
    private String username;

    @Value("${cmpp.msgid}")
    private String msgId;

    public static void main(String[] args) {
        SpringApplication.run(DeliverUdpClientApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        DatagramSocket socket = new DatagramSocket();
        InetAddress address = InetAddress.getByName(udpIp);

        Message req = new Message(Message.DELIVER_REQUEST, SequenceNumberUtil.getSequenceNo());
        DeliverProto.Deliver.Builder builder = DeliverProto.Deliver.newBuilder();
        if (StringUtils.isEmpty(msgId)) {
            builder.setUsername(username).build();
        }
        else {
            builder.setUsername(username).setMsgid(msgId).build();
        }
        DeliverProto.Deliver deliver = builder.build();
        req.setBodyBuffer(deliver.toByteArray());

        byte[] bytes = req.getBuffer();
        DatagramPacket packet = new DatagramPacket(bytes, bytes.length, address, udpPort);
        socket.send(packet);

        bytes = new byte[1024];
        packet = new DatagramPacket(bytes, bytes.length);
        socket.receive(packet);
        Message resp = new Message(packet.getData());
        if(resp.getCommandId() == Message.DELIVER_REQUEST) {
            byte[] bodyBuffer = req.getBodyBuffer();
            try {
                DeliverProto.DeliverResp deliverResp = DeliverProto.DeliverResp.parseFrom(bodyBuffer);
                log.info("code: {}", deliverResp.getCode());
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
            }
        }
        socket.close();
    }
}
