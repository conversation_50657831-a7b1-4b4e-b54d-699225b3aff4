// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Deliver.proto

package com.xhqb.spectre.common.protobuf;

public final class DeliverProto {
  private DeliverProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DeliverOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.xhqb.spectre.common.protobuf.Deliver)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string username = 1;</code>
     * @return The username.
     */
    java.lang.String getUsername();
    /**
     * <code>string username = 1;</code>
     * @return The bytes for username.
     */
    com.google.protobuf.ByteString
        getUsernameBytes();

    /**
     * <code>optional string msgid = 2;</code>
     * @return Whether the msgid field is set.
     */
    boolean hasMsgid();
    /**
     * <code>optional string msgid = 2;</code>
     * @return The msgid.
     */
    java.lang.String getMsgid();
    /**
     * <code>optional string msgid = 2;</code>
     * @return The bytes for msgid.
     */
    com.google.protobuf.ByteString
        getMsgidBytes();
  }
  /**
   * Protobuf type {@code com.xhqb.spectre.common.protobuf.Deliver}
   */
  public static final class Deliver extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.xhqb.spectre.common.protobuf.Deliver)
      DeliverOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Deliver.newBuilder() to construct.
    private Deliver(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Deliver() {
      username_ = "";
      msgid_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Deliver();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Deliver(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              username_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              msgid_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.xhqb.spectre.common.protobuf.DeliverProto.internal_static_com_xhqb_spectre_common_protobuf_Deliver_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.xhqb.spectre.common.protobuf.DeliverProto.internal_static_com_xhqb_spectre_common_protobuf_Deliver_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.xhqb.spectre.common.protobuf.DeliverProto.Deliver.class, com.xhqb.spectre.common.protobuf.DeliverProto.Deliver.Builder.class);
    }

    private int bitField0_;
    public static final int USERNAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object username_;
    /**
     * <code>string username = 1;</code>
     * @return The username.
     */
    @java.lang.Override
    public java.lang.String getUsername() {
      java.lang.Object ref = username_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        username_ = s;
        return s;
      }
    }
    /**
     * <code>string username = 1;</code>
     * @return The bytes for username.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUsernameBytes() {
      java.lang.Object ref = username_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        username_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MSGID_FIELD_NUMBER = 2;
    private volatile java.lang.Object msgid_;
    /**
     * <code>optional string msgid = 2;</code>
     * @return Whether the msgid field is set.
     */
    @java.lang.Override
    public boolean hasMsgid() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string msgid = 2;</code>
     * @return The msgid.
     */
    @java.lang.Override
    public java.lang.String getMsgid() {
      java.lang.Object ref = msgid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        msgid_ = s;
        return s;
      }
    }
    /**
     * <code>optional string msgid = 2;</code>
     * @return The bytes for msgid.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMsgidBytes() {
      java.lang.Object ref = msgid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        msgid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getUsernameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, username_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, msgid_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getUsernameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, username_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, msgid_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.xhqb.spectre.common.protobuf.DeliverProto.Deliver)) {
        return super.equals(obj);
      }
      com.xhqb.spectre.common.protobuf.DeliverProto.Deliver other = (com.xhqb.spectre.common.protobuf.DeliverProto.Deliver) obj;

      if (!getUsername()
          .equals(other.getUsername())) return false;
      if (hasMsgid() != other.hasMsgid()) return false;
      if (hasMsgid()) {
        if (!getMsgid()
            .equals(other.getMsgid())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + USERNAME_FIELD_NUMBER;
      hash = (53 * hash) + getUsername().hashCode();
      if (hasMsgid()) {
        hash = (37 * hash) + MSGID_FIELD_NUMBER;
        hash = (53 * hash) + getMsgid().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.xhqb.spectre.common.protobuf.DeliverProto.Deliver parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.Deliver parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.Deliver parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.Deliver parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.Deliver parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.Deliver parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.Deliver parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.Deliver parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.Deliver parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.Deliver parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.Deliver parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.Deliver parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.xhqb.spectre.common.protobuf.DeliverProto.Deliver prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.xhqb.spectre.common.protobuf.Deliver}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.xhqb.spectre.common.protobuf.Deliver)
        com.xhqb.spectre.common.protobuf.DeliverProto.DeliverOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.xhqb.spectre.common.protobuf.DeliverProto.internal_static_com_xhqb_spectre_common_protobuf_Deliver_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.xhqb.spectre.common.protobuf.DeliverProto.internal_static_com_xhqb_spectre_common_protobuf_Deliver_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.xhqb.spectre.common.protobuf.DeliverProto.Deliver.class, com.xhqb.spectre.common.protobuf.DeliverProto.Deliver.Builder.class);
      }

      // Construct using com.xhqb.spectre.common.protobuf.DeliverProto.Deliver.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        username_ = "";

        msgid_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.xhqb.spectre.common.protobuf.DeliverProto.internal_static_com_xhqb_spectre_common_protobuf_Deliver_descriptor;
      }

      @java.lang.Override
      public com.xhqb.spectre.common.protobuf.DeliverProto.Deliver getDefaultInstanceForType() {
        return com.xhqb.spectre.common.protobuf.DeliverProto.Deliver.getDefaultInstance();
      }

      @java.lang.Override
      public com.xhqb.spectre.common.protobuf.DeliverProto.Deliver build() {
        com.xhqb.spectre.common.protobuf.DeliverProto.Deliver result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.xhqb.spectre.common.protobuf.DeliverProto.Deliver buildPartial() {
        com.xhqb.spectre.common.protobuf.DeliverProto.Deliver result = new com.xhqb.spectre.common.protobuf.DeliverProto.Deliver(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.username_ = username_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.msgid_ = msgid_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.xhqb.spectre.common.protobuf.DeliverProto.Deliver) {
          return mergeFrom((com.xhqb.spectre.common.protobuf.DeliverProto.Deliver)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.xhqb.spectre.common.protobuf.DeliverProto.Deliver other) {
        if (other == com.xhqb.spectre.common.protobuf.DeliverProto.Deliver.getDefaultInstance()) return this;
        if (!other.getUsername().isEmpty()) {
          username_ = other.username_;
          onChanged();
        }
        if (other.hasMsgid()) {
          bitField0_ |= 0x00000001;
          msgid_ = other.msgid_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.xhqb.spectre.common.protobuf.DeliverProto.Deliver parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.xhqb.spectre.common.protobuf.DeliverProto.Deliver) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object username_ = "";
      /**
       * <code>string username = 1;</code>
       * @return The username.
       */
      public java.lang.String getUsername() {
        java.lang.Object ref = username_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          username_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string username = 1;</code>
       * @return The bytes for username.
       */
      public com.google.protobuf.ByteString
          getUsernameBytes() {
        java.lang.Object ref = username_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          username_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string username = 1;</code>
       * @param value The username to set.
       * @return This builder for chaining.
       */
      public Builder setUsername(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        username_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string username = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUsername() {
        
        username_ = getDefaultInstance().getUsername();
        onChanged();
        return this;
      }
      /**
       * <code>string username = 1;</code>
       * @param value The bytes for username to set.
       * @return This builder for chaining.
       */
      public Builder setUsernameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        username_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object msgid_ = "";
      /**
       * <code>optional string msgid = 2;</code>
       * @return Whether the msgid field is set.
       */
      public boolean hasMsgid() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string msgid = 2;</code>
       * @return The msgid.
       */
      public java.lang.String getMsgid() {
        java.lang.Object ref = msgid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          msgid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string msgid = 2;</code>
       * @return The bytes for msgid.
       */
      public com.google.protobuf.ByteString
          getMsgidBytes() {
        java.lang.Object ref = msgid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          msgid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string msgid = 2;</code>
       * @param value The msgid to set.
       * @return This builder for chaining.
       */
      public Builder setMsgid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        msgid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string msgid = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        msgid_ = getDefaultInstance().getMsgid();
        onChanged();
        return this;
      }
      /**
       * <code>optional string msgid = 2;</code>
       * @param value The bytes for msgid to set.
       * @return This builder for chaining.
       */
      public Builder setMsgidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        bitField0_ |= 0x00000001;
        msgid_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.xhqb.spectre.common.protobuf.Deliver)
    }

    // @@protoc_insertion_point(class_scope:com.xhqb.spectre.common.protobuf.Deliver)
    private static final com.xhqb.spectre.common.protobuf.DeliverProto.Deliver DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.xhqb.spectre.common.protobuf.DeliverProto.Deliver();
    }

    public static com.xhqb.spectre.common.protobuf.DeliverProto.Deliver getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Deliver>
        PARSER = new com.google.protobuf.AbstractParser<Deliver>() {
      @java.lang.Override
      public Deliver parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Deliver(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Deliver> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Deliver> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.xhqb.spectre.common.protobuf.DeliverProto.Deliver getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DeliverRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.xhqb.spectre.common.protobuf.DeliverResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();
  }
  /**
   * Protobuf type {@code com.xhqb.spectre.common.protobuf.DeliverResp}
   */
  public static final class DeliverResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.xhqb.spectre.common.protobuf.DeliverResp)
      DeliverRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DeliverResp.newBuilder() to construct.
    private DeliverResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DeliverResp() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DeliverResp();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DeliverResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.xhqb.spectre.common.protobuf.DeliverProto.internal_static_com_xhqb_spectre_common_protobuf_DeliverResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.xhqb.spectre.common.protobuf.DeliverProto.internal_static_com_xhqb_spectre_common_protobuf_DeliverResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp.class, com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp)) {
        return super.equals(obj);
      }
      com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp other = (com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp) obj;

      if (getCode()
          != other.getCode()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.xhqb.spectre.common.protobuf.DeliverResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.xhqb.spectre.common.protobuf.DeliverResp)
        com.xhqb.spectre.common.protobuf.DeliverProto.DeliverRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.xhqb.spectre.common.protobuf.DeliverProto.internal_static_com_xhqb_spectre_common_protobuf_DeliverResp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.xhqb.spectre.common.protobuf.DeliverProto.internal_static_com_xhqb_spectre_common_protobuf_DeliverResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp.class, com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp.Builder.class);
      }

      // Construct using com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.xhqb.spectre.common.protobuf.DeliverProto.internal_static_com_xhqb_spectre_common_protobuf_DeliverResp_descriptor;
      }

      @java.lang.Override
      public com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp getDefaultInstanceForType() {
        return com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp.getDefaultInstance();
      }

      @java.lang.Override
      public com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp build() {
        com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp buildPartial() {
        com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp result = new com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp(this);
        result.code_ = code_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp) {
          return mergeFrom((com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp other) {
        if (other == com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.xhqb.spectre.common.protobuf.DeliverResp)
    }

    // @@protoc_insertion_point(class_scope:com.xhqb.spectre.common.protobuf.DeliverResp)
    private static final com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp();
    }

    public static com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DeliverResp>
        PARSER = new com.google.protobuf.AbstractParser<DeliverResp>() {
      @java.lang.Override
      public DeliverResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DeliverResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DeliverResp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DeliverResp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.xhqb.spectre.common.protobuf.DeliverProto.DeliverResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_xhqb_spectre_common_protobuf_Deliver_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_xhqb_spectre_common_protobuf_Deliver_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_xhqb_spectre_common_protobuf_DeliverResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_xhqb_spectre_common_protobuf_DeliverResp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rDeliver.proto\022 com.xhqb.spectre.common" +
      ".protobuf\"9\n\007Deliver\022\020\n\010username\030\001 \001(\t\022\022" +
      "\n\005msgid\030\002 \001(\tH\000\210\001\001B\010\n\006_msgid\"\033\n\013DeliverR" +
      "esp\022\014\n\004code\030\001 \001(\005B0\n com.xhqb.spectre.co" +
      "mmon.protobufB\014DeliverProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_xhqb_spectre_common_protobuf_Deliver_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_xhqb_spectre_common_protobuf_Deliver_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_xhqb_spectre_common_protobuf_Deliver_descriptor,
        new java.lang.String[] { "Username", "Msgid", "Msgid", });
    internal_static_com_xhqb_spectre_common_protobuf_DeliverResp_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_xhqb_spectre_common_protobuf_DeliverResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_xhqb_spectre_common_protobuf_DeliverResp_descriptor,
        new java.lang.String[] { "Code", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
