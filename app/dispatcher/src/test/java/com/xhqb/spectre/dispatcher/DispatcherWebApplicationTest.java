package com.xhqb.spectre.dispatcher;

import com.github.wujun234.uid.UidGenerator;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * DispatcherWebApplication Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>Sep 16, 2021</pre>
 */

@Ignore
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DispatcherWebApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DispatcherWebApplicationTest {

    @LocalServerPort
    private int port;

    @Autowired
    private UidGenerator defaultUidGenerator;

    @Test
    public void testGetId() throws Exception {
        Long id = defaultUidGenerator.getUID();
        System.out.println("defaultUidGenerator.getUID:" + id);
        Assert.assertTrue(id != null);
    }
}
