package com.xhqb.spectre.dispatcher.service.impl;

import com.xhqb.spectre.common.mq.ChannelCode;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class ChannelSelectServiceImplTest {

    private ChannelSelectServiceImpl channelSelectServiceImplUnderTest;

    @Before
    public void setUp() {
        channelSelectServiceImplUnderTest = new ChannelSelectServiceImpl();
    }

    @Test
    public void testSelect() {
        // Setup
        final List<ChannelCode> channelList = new ArrayList<>();
        channelList.add(new ChannelCode("", "1", "", 500));
        channelList.add(new ChannelCode("", "2", "", 80));
        channelList.add(new ChannelCode("", "3", "", 60));
        channelList.add(new ChannelCode("", "4", "", 40));
        channelList.add(new ChannelCode("", "5", "", 20));

        // Run the test
        int count1 = 0, count2 = 0, count3 = 0, count4 = 0, count5 = 0;
        for (int i = 0; i < 10000; i++) {
            ChannelCode result = channelSelectServiceImplUnderTest.select(channelList);
            Integer channelAccountId = Integer.valueOf(result.getChannelAccountId());
            switch (channelAccountId) {
                case 1:
                    count1++;
                    break;
                case 2:
                    count2++;
                    break;
                case 3:
                    count3++;
                    break;
                case 4:
                    count4++;
                    break;
                case 5:
                    count5++;
                    break;
            }
        }
        System.out.println(count1);
        System.out.println(count2);
        System.out.println(count3);
        System.out.println(count4);
        System.out.println(count5);

        // Verify the results
        assertThat(count1).isGreaterThan(5000);
    }

    @Test
    public void testRandomSort() {
        // Setup
        final List<ChannelCode> channelList = new ArrayList<>();
        channelList.add(new ChannelCode("", "1", "", 120));
        channelList.add(new ChannelCode("", "2", "", 10));
        channelList.add(new ChannelCode("", "3", "", 20));
        channelList.add(new ChannelCode("", "4", "", 30));
        channelList.add(new ChannelCode("", "5", "", 40));

        // Run the test
        int count1 = 0, count2 = 0, count3 = 0, count4 = 0, count5 = 0;
        for (int i = 0; i < 10000; i++) {
            List<ChannelCode> result = channelSelectServiceImplUnderTest.randomSort(channelList);
            Integer channelAccountId = Integer.valueOf(result.get(0).getChannelAccountId());
            switch (channelAccountId) {
                case 1:
                    count1++;
                    break;
                case 2:
                    count2++;
                    break;
                case 3:
                    count3++;
                    break;
                case 4:
                    count4++;
                    break;
                case 5:
                    count5++;
                    break;
            }
        }
        System.out.println(count1);
        System.out.println(count2);
        System.out.println(count3);
        System.out.println(count4);
        System.out.println(count5);

        // Verify the results
        assertThat(count1).isGreaterThan(5000);
    }
}
