package com.xhqb.spectre.dispatcher;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xhqb.spectre.channel.entity.SendMessage;
import com.xhqb.spectre.common.mq.ChannelCode;
import com.xhqb.spectre.dispatcher.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

/**
 * DispatcherWebApplication Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>Sep 16, 2021</pre>
 */

@Ignore
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DispatcherWebApplication.class)
@Slf4j
public class SendMQTest {


    @Autowired
    MessageService messageService;


    @Test
    public void testSend() {
        ChannelCode channelCodeSet1 = new ChannelCode();
        channelCodeSet1.setChannelAccountId("7");
        channelCodeSet1.setWeight(30);
//        ChannelCodeSet channelCodeSet2 = new ChannelCodeSet();
//        channelCodeSet2.setChannelAccountId(2);
//        channelCodeSet2.setWeight(40);
//        ChannelCodeSet channelCodeSet3 = new ChannelCodeSet();
//        channelCodeSet3.setChannelAccountId(3);
//        channelCodeSet3.setWeight(50);
//        ChannelCodeSet channelCodeSet4 = new ChannelCodeSet();
//        channelCodeSet4.setChannelAccountId(4);
//        channelCodeSet4.setWeight(25);
//        ChannelCodeSet channelCodeSet5 = new ChannelCodeSet();
//        channelCodeSet5.setChannelAccountId(5);
//        channelCodeSet5.setWeight(10);

        Long id = 688307893706004L;
        System.out.println("***************");
//        Long stamp = System.currentTimeMillis();
        SendMessage orderMessage = new SendMessage();
        orderMessage.setOrderId(String.valueOf(id));
        orderMessage.setRequestId(String.valueOf(orderMessage.getOrderId()));
        orderMessage.setSmsCode("market");
        orderMessage.setTplCode("test_001");
        orderMessage.setSignName("小花钱包");
        orderMessage.setAppCode("ceshi");
        orderMessage.setCity("上海");
        orderMessage.setProvince("上海");
        orderMessage.setIsp("123");
        orderMessage.setPhone("***********");
        orderMessage.setContent("测试人，您的账户有0元可用，提现可得最高100元现金大奖！戳 xh1.cn/HYWd 回T退订");
        orderMessage.setParamMap(Arrays.asList("测试人,0"));
        orderMessage.setChannelCodeSet(Lists.newArrayList(channelCodeSet1));

        String json = JSONObject.toJSONString(orderMessage);
        System.out.println(json);
//        messageService.send(orderMessage);
    }
}