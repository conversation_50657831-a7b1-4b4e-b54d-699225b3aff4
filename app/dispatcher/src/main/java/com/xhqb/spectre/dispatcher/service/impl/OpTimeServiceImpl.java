package com.xhqb.spectre.dispatcher.service.impl;

import com.xhqb.spectre.common.dal.entity.OpTimeDO;
import com.xhqb.spectre.common.dal.mapper.OpTimeMapper;
import com.xhqb.spectre.dispatcher.service.OpTimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/9 14:11
 * @Description:
 */
@Service
public class OpTimeServiceImpl implements OpTimeService {

    @Autowired
    private OpTimeMapper opTimeMapper;

    @Override
    public OpTimeDO findByModule(Integer module) {
        return opTimeMapper.selectByModule(module);
    }
}
