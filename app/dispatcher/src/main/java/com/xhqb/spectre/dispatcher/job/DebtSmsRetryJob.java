package com.xhqb.spectre.dispatcher.job;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.channel.entity.SendMessage;
import com.xhqb.spectre.common.dal.entity.DebtSmsRetryDO;
import com.xhqb.spectre.common.dal.entity.SubmitResqDO;
import com.xhqb.spectre.common.dal.mapper.DebtSmsRetryMapper;
import com.xhqb.spectre.common.dal.mapper.SubmitResqMapper;
import com.xhqb.spectre.dispatcher.service.DebtSmsRetryService;
import com.xhqb.spectre.dispatcher.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 人行债转短信重试
 *
 * <AUTHOR>
 * @date 2024/10/29
 */
@Component
@Job("debtSmsRetryJob")
@Slf4j
public class DebtSmsRetryJob implements SimpleJob {

    private static final int PAGE_SIZE = 50;

    @Resource
    private DebtSmsRetryMapper debtSmsRetryMapper;
    @Autowired
    private MessageService messageService;
    @Resource
    private SubmitResqMapper submitResqMapper;
    @Resource
    private DebtSmsRetryService debtSmsRetryService;

    @Override
    public void execute(ShardingContext shardingContext) {
        long start = System.currentTimeMillis();
        Long lastId = 0L;
        // 凌晨的时间
        int beginStartTime = Math.toIntExact(DateUtil.beginOfDay(new Date()).getTime() / 1000);
        int endStartTime = Math.toIntExact((System.currentTimeMillis() / 1000));
        List<DebtSmsRetryDO> debtSmsRetryList = debtSmsRetryMapper.selectDebtSmsRetryJob(beginStartTime, endStartTime, PAGE_SIZE, lastId);
        while (!CollectionUtils.isEmpty(debtSmsRetryList)) {
            lastId = debtSmsRetryList.get(debtSmsRetryList.size() - 1).getId();
            try {
                debtSmsRetryProcessList(debtSmsRetryList);
            } catch (Exception e) {
                log.warn("债转短信重试数据处理失败， debtSmsRetryList = {}", JSON.toJSONString(debtSmsRetryList), e);
            }
            debtSmsRetryList = debtSmsRetryMapper.selectDebtSmsRetryJob(beginStartTime, endStartTime, PAGE_SIZE, lastId);
        }
        log.info("debtSmsRetryJob执行耗时={}, beginStartTime = {}, endStartTime = {}", (System.currentTimeMillis() - start), beginStartTime, endStartTime);
    }


    private void debtSmsRetryProcessList(List<DebtSmsRetryDO> debtSmsRetryList) {
        for (DebtSmsRetryDO debtSmsRetry : debtSmsRetryList) {
            try {
                debtSmsRetryProcess(debtSmsRetry);
            } catch (Exception e) {
                log.warn("人行债转短信重试处理失败, debtSmsRetry ={}", JSON.toJSONString(debtSmsRetry), e);
            }
        }
    }

    private void debtSmsRetryProcess(DebtSmsRetryDO debtSmsRetry) {
        SendMessage sendMessage = JSON.parseObject(debtSmsRetry.getMessageJson(), SendMessage.class);
        // 根据orderId 判断 t_submit_resq 是否存在 订单记录 ,若存在 就不再进行重试
        SubmitResqDO submitResqDO = submitResqMapper.selectByOrderId(debtSmsRetry.getOrderId());
        if (Objects.isNull(submitResqDO)) {
            messageService.send(sendMessage);
        } else {
            // t_submit_resq 数据存在,表示接口请求超时, 不需要进行重试
            log.info("t_submit_resq 数据存在, 不需要再进行债转短信重试了, orderId ={}", debtSmsRetry.getOrderId());
            debtSmsRetryService.debtSmsRetrySave(sendMessage, false);
        }
    }
}
