package com.xhqb.spectre.dispatcher;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan({"com.xhqb.spectre", "com.xhqb.kael.mq"})
@MapperScan("com.xhqb.spectre.common.dal.mapper")
@EnableScheduling
public class DispatcherWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(DispatcherWebApplication.class, args);
    }
}
