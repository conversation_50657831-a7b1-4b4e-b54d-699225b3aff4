package com.xhqb.spectre.dispatcher.config;

import com.xhqb.spectre.dispatcher.util.ChannelRateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
@Slf4j
public class ChannelRateLimiterConfig {

    @Value("${channel.rate.limiter.map}")
    private String mapString;

    @Value("${channel.rate.limiter.default:200}")
    private Double defaultPermits;

    @Bean
    public ChannelRateLimiter channelRateLimiter() {
        Map<Integer, Double> map = parseMap(mapString);
        return new ChannelRateLimiter(map, defaultPermits);
    }

    private Map<Integer, Double> parseMap(String mapString) {
        if (mapString == null || mapString.isEmpty()) {
            return new HashMap<>();
        }
        // 去掉首尾的大括号
        mapString = mapString.trim().replaceAll("[{}]", "");

        // 分割成键值对
        String[] entries = mapString.split(",\\s*");

        Map<Integer, Double> result = new HashMap<>();
        for (String entry : entries) {
            String[] keyValue = entry.split(":");
            if (keyValue.length == 2) {
                try {
                    int key = Integer.parseInt(keyValue[0].trim());
                    Double value = Double.parseDouble(keyValue[1].trim());
                    result.put(key, value);
                } catch (NumberFormatException e) {
                    // 处理解析错误
                    log.error("解析限流配置错误: {}", entry);
                }
            }
        }
        return result;
    }
}
