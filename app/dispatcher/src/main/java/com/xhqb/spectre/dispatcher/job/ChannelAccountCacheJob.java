package com.xhqb.spectre.dispatcher.job;

import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.entity.ChannelAccount;
import com.xhqb.spectre.common.dal.entity.OpTimeDO;
import com.xhqb.spectre.common.utils.DateUtil;
import com.xhqb.spectre.dispatcher.cache.ChannelAccountCache;
import com.xhqb.spectre.dispatcher.service.ChannelAccountService;
import com.xhqb.spectre.dispatcher.service.OpTimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/9 11:31
 * @Description:
 */
@Component
@Slf4j
public class ChannelAccountCacheJob {

    @Autowired
    private OpTimeService opTimeService;

    @Autowired
    private ChannelAccountService channelAccountService;

    /**
     * 比较数据库时间和系统记录的时间
     */
    @Scheduled(cron = "${cache.refresh.cron}")
    public void execute() {
        log.info("刷新渠道账号数据到内存");
        refreshChannelAccount();
    }

    private void refreshChannelAccount() {
        //根据opTime判断数据是否变更
        OpTimeDO opTimeDO = opTimeService.findByModule(OpLogConstant.MODULE_CHANNEL_ACCOUNT);
        if (Objects.isNull(opTimeDO)) {
            return;
        }
        Integer opTime = opTimeDO.getOpTime();
        Integer lastUpdateTime = ChannelAccountCache.getInstance().getDataUpdateTime();
        if (Objects.isNull(lastUpdateTime) || opTime > lastUpdateTime) {
            List<ChannelAccount> channelAccountList = channelAccountService.getAllEnabled();
            ChannelAccountCache.getInstance().refresh(channelAccountList);
            ChannelAccountCache.getInstance().setDataUpdateTime(opTime);
            log.info("渠道账号缓存更新，此次更新时间：{}，上一次更新时间：{}", DateUtil.intToString(opTime), DateUtil.intToString(lastUpdateTime));
        }
    }
}
