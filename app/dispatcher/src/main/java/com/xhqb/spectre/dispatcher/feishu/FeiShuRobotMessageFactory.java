package com.xhqb.spectre.dispatcher.feishu;

import com.alibaba.fastjson.JSON;
import com.xhqb.msgcenter.model.MsgSendRequest;
import com.xhqb.msgcenter.model.iteam.MsgSendEntry;
import com.xhqb.msgcenter.model.response.OminiSendResult;
import com.xhqb.msgcenter.sdk.OminiSendMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Slf4j
public class FeiShuRobotMessageFactory {

    @Autowired
    private OminiSendMessage ominiSendMessage;

    @Value("${spring.application.name:spectre}")
    private String system;

    public <T extends Serializable> OminiSendResult sendEntity(String strategyId, T vars) {
        return doSend(strategyId, JSON.parseObject(JSON.toJSONString(vars)));
    }

    public OminiSendResult sendMap(String strategyId, Map<String, Object> vars) {
        return doSend(strategyId, vars);
    }


    public OminiSendResult doSend(String strategyId, Map<String, Object> vars) {
        MsgSendRequest msgSendRequest = new MsgSendRequest();
        try {
            ArrayList<MsgSendEntry> msgSendEntries = new ArrayList<>();
            msgSendRequest.setSystem(system);
            msgSendRequest.setMsgClient("1");
            MsgSendEntry msgSendEntry = new MsgSendEntry();
            //消息中心后台分配的策略id
            msgSendEntry.setStrategyId(strategyId);
            // 如果需要指定@用户 可以在模板参数中设置 atMobile= mobileList ,如 mobileList.add("13262296990")
            // 如果希望@所有人则设置 isAtAll = 1
            Map<String, Object> msgVariableJson = new HashMap<>(16);
            msgVariableJson.put("msgType", "json");
            msgVariableJson.put("isDynamic", "1");
            msgVariableJson.putAll(vars);
            msgSendEntry.setMsgVariableJson(msgVariableJson);
            msgSendEntries.add(msgSendEntry);
            msgSendRequest.setMsgSendEntries(msgSendEntries);
            OminiSendResult ominiSendResult = ominiSendMessage.sendMessage(msgSendRequest);
            if (!ominiSendResult.isSuccess()) {
                log.info("飞书消息推送失败 = {}", JSON.toJSONString(ominiSendResult));
            }
            return ominiSendResult;
        } catch (Exception e) {
            log.warn("飞书消息推送异常 = {}", JSON.toJSONString(msgSendRequest), e);
        }
        return null;
    }


}
