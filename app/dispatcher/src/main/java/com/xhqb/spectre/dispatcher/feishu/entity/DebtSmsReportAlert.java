package com.xhqb.spectre.dispatcher.feishu.entity;

import cn.hutool.core.date.DateUtil;
import com.xhqb.spectre.common.dal.entity.DebtSmsReportDO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/30
 */
@Data
public class DebtSmsReportAlert implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 报表日期
     */
    private String reportDate;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 批次号
     */
    private String bizBatchId;


    /**
     * 告警次数
     */
    private Integer alertTimes;

    /**
     * 提交总数
     */
    private Integer submitAmount;

    /**
     * 有效数量
     */
    private Integer validAmount;

    /**
     * 订单数量
     */
    private Integer orderAmount;

    /**
     * 成功数量
     */
    private Integer successAmount;


    public static DebtSmsReportAlert build(DebtSmsReportDO debtSmsReport) {
        DebtSmsReportAlert alert = new DebtSmsReportAlert();
        alert.setId(debtSmsReport.getId());
        alert.setReportDate(DateUtil.formatDateTime(debtSmsReport.getReportDate()));
        alert.setTplCode(debtSmsReport.getTplCode());
        alert.setBizBatchId(debtSmsReport.getBizBatchId());
        alert.setAlertTimes(debtSmsReport.getAlertTimes());
        alert.setSubmitAmount(debtSmsReport.getSubmitAmount());
        alert.setValidAmount(debtSmsReport.getValidAmount());
        alert.setOrderAmount(debtSmsReport.getOrderAmount());
        alert.setSuccessAmount(debtSmsReport.getSuccessAmount());
        return alert;
    }

}
