package com.xhqb.spectre.dispatcher.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/25 17:33
 * @Description:
 */
@Component
@Data
public class VenusConfig {

    /**
     * 保存订单的队列名（spectre-send-submit）
     */
    @Value("#{'${kael.mq.producers:}'.split(',')[0]}")
    private String sendSubmitQueue;

    /**
     * 更新订单发送状态的队列名（spectre-submit-resq）
     */
    @Value("#{'${kael.mq.producers:}'.split(',')[1]}")
    private String submitResqQueue;

    /**
     * 营销短信MQ队列名称
     */
    @Value("#{'${kael.mq.consumers:}'.split(',')[2]}")
    private String marketQueue;

    /**
     * 催收短信MQ队列名称
     */
    @Value("#{'${kael.mq.consumers:}'.split(',')[3]}")
    private String collectQueue;

    /**
     * 通知短信MQ队列名称
     */
    @Value("#{'${kael.mq.consumers:}'.split(',')[0]}")
    private String notifyQueue;

    /**
     * 验证码短信MQ队列名称
     */
    @Value("#{'${kael.mq.consumers:}'.split(',')[1]}")
    private String verifyQueue;

    /**
     * 验证码短信的有效期（单位：分钟）
     */
    @Value("${sms.verify.duration.minute:1}")
    private Integer verifyDuration;

    /**
     * 分发选中渠道的缓存时间
     */
    @Value("${selected-channel.cache.timeout.minute:30}")
    private Integer channelTimeout;

    /**
     * 验证码短信订单缓存时间
     */
    @Value("${order.verify.cache.timeout.minute:1}")
    private Integer verifyTimeout;

    /**
     * 通知短信订单缓存时间
     */
    @Value("${order.notify.cache.timeout.minute:10}")
    private Integer notifyTimeout;

    /**
     * 催收短信订单缓存时间
     */
    @Value("${order.collector.cache.timeout.minute:10}")
    private Integer collectorTimeout;

    /**
     * 营销短信订单缓存时间
     */
    @Value("${order.market.cache.timeout.minute:30}")
    private Integer marketTimeout;

    /**
     * 测试环境允许发送短信的手机号列表
     */
    @Value("${mobile.white.list:0}")
    private String mobileWhiteList;

    /**
     * 查询异常渠道的接口地址
     */
    @Value("${query.channel-stat.url:http://spectre-route-calculate/spectre-route-calculate/queryRecentBadChannel}")
    private String queryChannelStatUrl;

    /**
     * 订单分表名称后缀(主要用于订单分表跨月测试)
     */
    @Value("${order.sharding.name.suffix:}")
    private String tableNameSuffix;

    /**
     * 2024-01-31 23:59:59
     */
    @Value("${sharding.start.time.millis:1706716799000}")
    private long shardingStartTimeMillis;

    @Value("${sharding.sql.print.enable:false}")
    private String sqlPrintEnable;

    /**
     * 债转短信模板编码列表,多个模板编码使用逗号分割
     */
    @Value("${spectre.dispatcher.debtSmsTplCodeList:}")
    private List<String> debtSmsTplCodeList;

    /**
     * 债转短信重试间隔秒数
     */
    @Value("${spectre.dispatcher.debtSmsRetryIntervalSeconds:60}")
    private int debtSmsRetryIntervalSeconds;

    /**
     * 债转短信最大重试次数
     */
    @Value("${spectre.dispatcher.debtSmsRetryMaxTimes:3}")
    private int debtSmsRetryMaxTimes;

    /**
     * 债转短信通知最小触达率阈值
     */
    @Value("${spectre.dispatcher.debtSmsAlertMinReachRate:0.97}")
    private BigDecimal debtSmsAlertMinReachRate;

    /**
     * 债转短信最大告警次数
     */
    @Value("${spectre.dispatcher.debtSmsAlertMaxTimes:3}")
    private int debtSmsAlertMaxTimes;

    /**
     * 债转短信告警间隔分钟
     */
    @Value("${spectre.dispatcher.debtSmsReportIntervalMinutes:5}")
    private int debtSmsReportIntervalMinutes;

    /**
     * 债转短信报表告警策略ID
     */
    @Value("${spectre.dispatcher.debtSmsReportAlertStrategyId:}")
    private String debtSmsReportAlertStrategyId;

    /**
     * 债转短信报表告警@的用户信息
     */
    @Value("${spectre.dispatcher.debtSmsReportAlertForUserList:}")
    private List<String> debtSmsReportAlertForUserList;

    /**
     * 渠道限流开关
     */
    @Value("${spectre.dispatcher.syncSendSms:true}")
    private boolean syncSendSms;

    /**
     * 发送阻塞队列
     * spectre-dispatcher-blocked-sms
     */
    @Value("${tdmq.producers.blockedSms}")
    private String blockedSmsTopic;

    /**
     * 判断短信模板是否是指定的债转短信
     *
     * @param tplCode
     * @return
     */
    public boolean isDebtSmsTplCode(String tplCode) {
        if (CollectionUtils.isEmpty(debtSmsTplCodeList)) {
            return false;
        }

        return debtSmsTplCodeList.contains(tplCode);
    }
}
