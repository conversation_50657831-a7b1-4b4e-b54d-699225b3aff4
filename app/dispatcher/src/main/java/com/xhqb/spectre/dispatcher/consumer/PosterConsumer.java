package com.xhqb.spectre.dispatcher.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.channel.entity.SendMessage;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.dto.mq.SendSubmitDTO;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import com.xhqb.spectre.common.utils.DateUtil;
import com.xhqb.spectre.dispatcher.config.VenusConfig;
import com.xhqb.spectre.dispatcher.constant.LuciferConstant;
import com.xhqb.spectre.dispatcher.exception.BizException;
import com.xhqb.spectre.dispatcher.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;

@Component
@Slf4j
public class PosterConsumer {

    @Autowired
    private MessageService messageService;

    @Autowired
    private VenusConfig venusConfig;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 消息处理的线程池
     */
    @Resource(name = "sendSmsThreadPool")
    private ThreadPoolExecutor sendSmsThreadPool;

    /**
     * 营销短信MQ处理（spectre-sms-market）
     *
     * @param json
     */
    @MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[2]}", subscriptionType = SubscriptionType.Shared, clazz = String.class, receiverQueueSize = 100)
    public void marketMessage(String json) {
        LuciferConstant.MQ_CONSUMER_COUNTER.labels(venusConfig.getMarketQueue()).inc();
        handleMessage(json, MessageTypeEnum.MARKET);
    }

    /**
     * 催收短信MQ处理（spectre-sms-collector）
     *
     * @param json
     */
    @MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[3]}", subscriptionType = SubscriptionType.Shared, clazz = String.class, receiverQueueSize = 100)
    public void collectMessage(String json) {
        LuciferConstant.MQ_CONSUMER_COUNTER.labels(venusConfig.getCollectQueue()).inc();
        handleMessage(json, MessageTypeEnum.COLLECTOR);
    }

    /**
     * 通知短信MQ处理（spectre-sms-notify）
     *
     * @param json
     */
    @MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[0]}", subscriptionType = SubscriptionType.Shared, clazz = String.class, receiverQueueSize = 100)
    public void notifyMessage(String json) {
        LuciferConstant.MQ_CONSUMER_COUNTER.labels(venusConfig.getNotifyQueue()).inc();
        handleMessage(json, MessageTypeEnum.NOTIFY);
    }

    /**
     * 验证码短信MQ处理（spectre-sms-verify）
     *
     * @param json
     */
    @MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[1]}", subscriptionType = SubscriptionType.Shared, clazz = String.class, receiverQueueSize = 100)
    public void verifyMessage(String json) {
        LuciferConstant.MQ_CONSUMER_COUNTER.labels(venusConfig.getVerifyQueue()).inc();
        handleMessage(json, MessageTypeEnum.VERIFY);
    }

    /**
     * 消息处理
     *
     * @param json
     * @param messageTypeEnum
     */
    private void handleMessage(String json, MessageTypeEnum messageTypeEnum) {
        log.info("收到{}消息：{}", messageTypeEnum.getDescription(), json);
        try {
            SendMessage message = JSONObject.parseObject(json, SendMessage.class);
            //参数校验
            validateMessage(message);

            //设置重发次数（如果是logServer的重试短信，会传重发次数，没传设置为0）
            if (Objects.isNull(message.getResend())) {
                message.setResend(0);
            }

            String tableNameSuffix = DateUtil.getTableNameSuffix();
            if (StringUtils.isNotBlank(venusConfig.getTableNameSuffix())) {
                // 这里主要用于对订单分表跨月测试
                log.info("当前使用了apollo配置的订单分表后缀了 = {}", venusConfig.getTableNameSuffix());
                tableNameSuffix = venusConfig.getTableNameSuffix();
            }

            // 设置订单分表后缀日期
            message.setTableNameSuffix(tableNameSuffix);

            //验证码短信串行处理，其余类型提交到线程池处理
            if (MessageTypeEnum.VERIFY.getMessageType().equals(message.getSmsCode())) {
                messageService.send(message);
            } else {
                sendSmsThreadPool.submit(() -> messageService.send(message));
            }
        } catch (BizException e) {
            log.info("消息数据有误，原因：{}", e.getMsg());
        } catch (Exception e) {
            log.error("处理消息异常，消息：{}", json, e);
        }
    }

    /**
     * 消息校验
     *
     * @param message
     * @return
     */
    private void validateMessage(SendMessage message) {
        if (Objects.isNull(message)) {
            throw new BizException("消息解析错误");
        }
        if (CollectionUtils.isEmpty(message.getChannelCodeSet())) {
            throw new BizException("无可用渠道");
        }
        //验证码短信校验
        checkVerifyMessage(message);
    }

    private void checkVerifyMessage(SendMessage message) {
        if (!MessageTypeEnum.VERIFY.getMessageType().equals(message.getSmsCode())) {
            return;
        }
        //校验消息是否超时
        String orderIdStr = String.valueOf(message.getOrderId());
        long receiveTime = Long.parseLong(message.getReceiveTime());
        Integer verifyDuration = venusConfig.getVerifyDuration();
        long durationLong = verifyDuration * 60 * 1000;
        long currentTimeMillis = System.currentTimeMillis();
        if (currentTimeMillis - receiveTime > durationLong) {
            throw new BizException("验证码消息距今已超过" + verifyDuration + "分钟，不进行消费。订单号：" + orderIdStr);
        }
        //校验是否需要重发
        Integer resend = message.getResend();
        if (Objects.nonNull(resend) && resend > 0) {
            //取resend=0的短信订单，以其sendTime为基准，跟当前时间比较
            String orderCacheKey = MessageFormat.format(RedisKeys.DispatcherKeys.ORDER_KEY, orderIdStr, "0");
            String orderStr = (String) redisTemplate.opsForValue().get(orderCacheKey);
            if (StringUtils.isEmpty(orderStr)) {
                //未找到订单，表明订单已过期，无需重发
                throw new BizException("验证码消息距今已超过" + verifyDuration + "分钟，不进行重发。订单号：" + orderIdStr);
            }
            SendSubmitDTO sendSubmitDTO = JSON.parseObject(orderStr, SendSubmitDTO.class);
            if (Objects.isNull(sendSubmitDTO)) {
                throw new BizException("验证码消息距今已超过" + verifyDuration + "分钟，不进行重发。订单号：" + orderIdStr);
            }
            Integer sendTime = sendSubmitDTO.getSendTime();
            if (currentTimeMillis - ((long) sendTime * 1000) > durationLong) {
                throw new BizException("验证码消息距今已超过" + verifyDuration + "分钟，不进行重发。订单号：" + orderIdStr);
            }
        }
    }
}