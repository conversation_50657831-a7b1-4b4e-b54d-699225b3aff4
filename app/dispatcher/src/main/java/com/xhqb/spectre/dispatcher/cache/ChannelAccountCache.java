package com.xhqb.spectre.dispatcher.cache;

import com.xhqb.spectre.common.dal.entity.ChannelAccount;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/9 10:47
 * @Description: 渠道账号缓存
 */
public class ChannelAccountCache {

    private static final ChannelAccountCache INSTANCE = new ChannelAccountCache();

    private static Map<Integer, ChannelAccount> DATA_MAP = new ConcurrentHashMap<>();

    private static Integer DATA_UPDATE_TIME = null;

    private ChannelAccountCache() {
    }

    /**
     * Gets instance.
     *
     * @return the instance
     */
    public static ChannelAccountCache getInstance() {
        return INSTANCE;
    }

    /**
     * 获取渠道账号信息
     *
     * @param channelAccountId
     * @return
     */
    public ChannelAccount get(Integer channelAccountId) {
        return DATA_MAP.get(channelAccountId);
    }

    /**
     * 刷新缓存
     *
     * @param list
     */
    public void refresh(List<ChannelAccount> list) {
        DATA_MAP = Optional.ofNullable(list)
                .map(item -> item.stream().collect(Collectors.toMap(ChannelAccount::getId, Function.identity())))
                .orElse(new ConcurrentHashMap<>());
    }

    /**
     * 获取数据变更时间
     *
     * @return
     */
    public Integer getDataUpdateTime() {
        return DATA_UPDATE_TIME;
    }

    /**
     * 设置数据变更时间
     *
     * @param dataUpdateTime
     */
    public void setDataUpdateTime(Integer dataUpdateTime) {
        DATA_UPDATE_TIME = dataUpdateTime;
    }

    /**
     * 是否为空
     *
     * @return
     */
    public boolean isEmpty() {
        return Objects.isNull(DATA_MAP) || DATA_MAP.isEmpty();
    }
}
