package com.xhqb.spectre.dispatcher.model;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.channel.entity.SendMessage;
import com.xhqb.spectre.common.mq.ChannelCode;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class DoSendDTO implements Serializable {

    /**
     * 消息
     */
    private SendMessage message;

    private String originContent;

    /**
     * 通道列表
     */
    private List<ChannelCode> originChannelCodeList;

    private List<ChannelCode> remainChannelCodeList;

    /**
     * 开始发送时间
     */
    private long start;

    /**
     * 是否已经发送成功
     */
    private boolean hasSent;

    /**
     * 是否已经保存订单
     */
    private boolean hasSaveOrder;

    /**
     * 已请求send接口次数
     */
    private int requestedCount;

    /**
     * 人行征信短信是否需要重试处理
     */
    private boolean debtSmsNeedRetry;

    /**
     * 是否被阻塞发送
     */
    private boolean blocked;

    /**
     * 构造方法
     * @param message 发送消息对象
     * @param channelCodeList 通道列表
     * @return
     */
    public static DoSendDTO build(SendMessage message, List<ChannelCode> channelCodeList) {
        DoSendDTO doSendDTO = new DoSendDTO();
        doSendDTO.message = message;
        doSendDTO.originContent = message.getContent();
        doSendDTO.originChannelCodeList = new ArrayList<>(channelCodeList);
        doSendDTO.remainChannelCodeList = new ArrayList<>(channelCodeList);
        doSendDTO.start = System.nanoTime();
        doSendDTO.hasSent = false;
        doSendDTO.hasSaveOrder = false;
        doSendDTO.requestedCount = 0;
        doSendDTO.debtSmsNeedRetry = false;
        doSendDTO.blocked = false;
        return doSendDTO;
    }

    public String toJSON() {
        return JSON.toJSONString(this);
    }

    public void incrRequestedCount() {
        this.requestedCount++;
    }
}
