package com.xhqb.spectre.dispatcher.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xhqb.spectre.dispatcher.config.VenusConfig;
import com.xhqb.spectre.dispatcher.model.BaseResult;
import com.xhqb.spectre.dispatcher.model.ChannelStat;
import com.xhqb.spectre.dispatcher.model.ChannelStatVO;
import com.xhqb.spectre.dispatcher.service.ChannelStatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/31 14:35
 * @Description:
 */
@Service
@Slf4j
public class ChannelStatServiceImpl implements ChannelStatService {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private VenusConfig venusConfig;

    /**
     * 异常渠道缓存的key
     */
    private static final String CHANNEL_STAT_CACHE_KEY = "channelStat";

    /**
     * 数据缓存
     */
    private final Cache<String, Map<String, Integer>> DATA_CACHE = CacheBuilder.newBuilder()
            .maximumSize(10)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    /**
     * 刷新异常渠道缓存
     */
    @Override
    public void refreshCache() {
        //调用路由计算服务接口，查询异常渠道数据
        ResponseEntity<BaseResult<ChannelStatVO>> response = restTemplate.exchange(venusConfig.getQueryChannelStatUrl(),
                HttpMethod.GET, null, new ParameterizedTypeReference<BaseResult<ChannelStatVO>>() {
                });
        if (!Objects.equals(response.getStatusCode(), HttpStatus.OK)) {
            log.warn("调用路由计算服务接口Http响应失败，statusCode: {}", response.getStatusCode());
            return;
        }
        BaseResult<ChannelStatVO> result = response.getBody();
        if (result == null) {
            log.warn("路由计算服务接口响应值为null");
            return;
        }
        ChannelStatVO channelStatVO = result.getData();
        if (!result.isSuccess() || channelStatVO == null) {
            log.warn("路由计算服务接口返回异常，code：{}，msg：{}", result.getCode(), result.getMsg());
            return;
        }

        //封装缓存结构数据
        List<ChannelStat> channelList = channelStatVO.getChannelList();
        List<ChannelStat> channelIspList = channelStatVO.getChannelIspList();
        Map<String, Integer> statMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(channelList)) {
            log.info("获取到异常渠道数据，channelList：{}", JSON.toJSONString(channelList));
            channelList.forEach(item -> statMap.put(buildCacheKey(item.getChannelAccountId()), item.getScaleFactor()));
        }
        if (CollectionUtils.isNotEmpty(channelIspList)) {
            log.info("获取到异常渠道+运营商数据，channelIspList：{}", JSON.toJSONString(channelIspList));
            channelIspList.forEach(item -> statMap.put(buildCacheKey(item.getChannelAccountId(), item.getIsp()), item.getScaleFactor()));
        }

        //写入新缓存
        DATA_CACHE.put(CHANNEL_STAT_CACHE_KEY, statMap);
    }

    /**
     * 获取异常渠道数据
     *
     * @return
     */
    @Override
    public Map<String, Integer> getScaleFactorMap() {
        Map<String, Integer> map = DATA_CACHE.getIfPresent(CHANNEL_STAT_CACHE_KEY);
        return map != null ? map : new HashMap<>();
    }

    private String buildCacheKey(Integer channelAccountId) {
        return String.valueOf(channelAccountId);
    }

    private String buildCacheKey(Integer channelAccountId, String isp) {
        return channelAccountId + "_" + isp;
    }
}
