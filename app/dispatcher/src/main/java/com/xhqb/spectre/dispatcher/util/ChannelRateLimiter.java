package com.xhqb.spectre.dispatcher.util;

import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class ChannelRateLimiter {
    private Double defaultPermits;
    private static final Map<Integer, RateLimiter> rateLimiters = new ConcurrentHashMap<>();

    public ChannelRateLimiter(Map<Integer, Double> permits, Double defaultPermits) {
        this.defaultPermits = defaultPermits;

        permits.forEach((Integer k, Double v) -> {
            rateLimiters.put(k, RateLimiter.create(v));
        });
    }


    public boolean tryAcquire(int key, int permits) {
        RateLimiter rateLimiter = rateLimiters.computeIfAbsent(key, k -> RateLimiter.create(defaultPermits));
        return rateLimiter.tryAcquire(permits);
    }

    public double acquire(int key, int permits) {
        RateLimiter rateLimiter = rateLimiters.computeIfAbsent(key, k -> RateLimiter.create(defaultPermits));
        return rateLimiter.acquire(permits);
    }
}
