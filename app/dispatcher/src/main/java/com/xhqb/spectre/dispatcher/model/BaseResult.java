package com.xhqb.spectre.dispatcher.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/29 16:25
 * @Description:
 */
@Data
public class BaseResult<T> implements Serializable {

    private static final long serialVersionUID = -3611271290010374139L;

    private static final int SUCCESS = 0;

    private Integer code;

    private String msg;

    private T data;

    public BaseResult(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public boolean isSuccess() {
        return Objects.equals(code, SUCCESS);
    }
}
