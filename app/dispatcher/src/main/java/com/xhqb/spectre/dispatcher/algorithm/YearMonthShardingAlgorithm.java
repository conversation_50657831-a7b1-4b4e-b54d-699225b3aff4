package com.xhqb.spectre.dispatcher.algorithm;

import com.xhqb.spectre.common.utils.ShardingUtils;
import com.xhqb.spectre.dispatcher.config.VenusConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingValue;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


/**
 * 根据发送时间进行按月分片
 *
 * @author: yjq
 * @date: 2023/12/26
 */
@Slf4j
public class YearMonthShardingAlgorithm implements PreciseShardingAlgorithm<Long>, RangeShardingAlgorithm<Long> {

    /**
     * 表名
     */
    private final String tableName;

    private final VenusConfig venusConfig;

    public YearMonthShardingAlgorithm(String tableName, VenusConfig venusConfig) {
        this.tableName = tableName;
        this.venusConfig = venusConfig;
    }

    @Override
    public String doSharding(Collection<String> collection, PreciseShardingValue<Long> preciseShardingValue) {
        Long value = preciseShardingValue.getValue();
        if (value > venusConfig.getShardingStartTimeMillis()) {
            return ShardingUtils.getTableNameByTime(tableName, value);
        } else {
            return tableName;
        }
    }

    @Override
    public Collection<String> doSharding(Collection<String> collection, RangeShardingValue<Long> rangeShardingValue) {
        long lowerEndpoint = rangeShardingValue.getValueRange().lowerEndpoint() * 1000;
        long upperEndpoint = rangeShardingValue.getValueRange().upperEndpoint() * 1000;

        List<String> result = new ArrayList<>();
        if (upperEndpoint <= venusConfig.getShardingStartTimeMillis()) {
            result.add(tableName);
            return result;
        }

        result.addAll(ShardingUtils.getTableNameByTimeRange(tableName, lowerEndpoint, upperEndpoint));
        return result;
    }
}
