package com.xhqb.spectre.dispatcher.consumer;

import com.alibaba.fastjson.JSON;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.dispatcher.model.DoSendDTO;
import com.xhqb.spectre.dispatcher.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class SynchronizedSendConsumer {
    @Resource
    private MessageService messageService;

    /**
     * 阻塞处理短信发送队列
     * spectre-dispatcher-blocked-sms
     * @param json
     */
    @MQConsumer(topic = "${tdmq.producers.blockedSms}",
            subscriptionType = SubscriptionType.Shared,
            clazz = String.class,
            ackTimeout = 60L,
            receiverQueueSize = 1)
    public void consume(String json) {
        try {
            DoSendDTO doSendDTO = JSON.parseObject(json, DoSendDTO.class);
            messageService.doSend(doSendDTO, true);
        } catch (Exception e) {
            log.error("同步发送短信失败", e);
        }
    }
}
