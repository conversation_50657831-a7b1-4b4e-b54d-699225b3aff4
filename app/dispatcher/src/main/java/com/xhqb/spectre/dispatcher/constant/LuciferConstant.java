package com.xhqb.spectre.dispatcher.constant;

import com.xhqb.kael.lucifer.telemetry.PrometheusCounterMetrics;
import com.xhqb.kael.lucifer.telemetry.PrometheusHistogramMetrics;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/18 15:40
 * @Description:
 */
public final class LuciferConstant {

    /**
     * 分发短信状态（1：成功；2：失败）
     */
    public static final int DISPATCH_STATUS_SUCCESS = 1;
    public static final int DISPATCH_STATUS_FAILED = 2;

    /**
     * mq消费次数，分队列名
     */
    public static final Counter MQ_CONSUMER_COUNTER = new PrometheusCounterMetrics("dispatch_mq_consumer_total", "mq消费次数")
            .createWithLabels("queueName");

    /**
     * mq发送次数，分队列名
     */
    public static final Counter MQ_PRODUCE_COUNTER = new PrometheusCounterMetrics("dispatch_mq_produce_total", "mq发送次数")
            .createWithLabels("queueName");

    /**
     * 分发短信次数，分短信类型、状态
     */
    public static final Counter DISPATCH_COUNTER = new PrometheusCounterMetrics("dispatch_total", "分发短信次数")
            .createWithLabels("smsType", "status");

    /**
     * 分发短信总耗时
     */
    public static final Histogram DISPATCH_TOTAL_TIME = new PrometheusHistogramMetrics("dispatch_total_time", "分发短信总耗时")
            .createWithLabels("smsType");

    /**
     * 未分发的消息个数（发送失败）
     */
    public static final Counter DISPATCH_NOT_HANDLE_COUNTER = new PrometheusCounterMetrics("dispatch_not_handle_total", "未分发的消息个数")
            .createWithLabels("smsType");
}
