package com.xhqb.spectre.dispatcher.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.channel.entity.BaseResult;
import com.xhqb.spectre.channel.entity.SendMessage;
import com.xhqb.spectre.channel.service.CmppChannelService;
import com.xhqb.spectre.channel.util.MessageUtil;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.dto.mq.SendSubmitDTO;
import com.xhqb.spectre.common.dal.dto.mq.SubmitResqDTO;
import com.xhqb.spectre.common.dal.entity.ChannelAccount;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import com.xhqb.spectre.common.enums.VenusEnvEnum;
import com.xhqb.spectre.common.mq.ChannelCode;
import com.xhqb.spectre.common.utils.ClockUtil;
import com.xhqb.spectre.dispatcher.config.VenusConfig;
import com.xhqb.spectre.dispatcher.constant.LuciferConstant;
import com.xhqb.spectre.dispatcher.enums.ErrorCodeEnum;
import com.xhqb.spectre.dispatcher.enums.ProtocolEnum;
import com.xhqb.spectre.dispatcher.model.DoSendDTO;
import com.xhqb.spectre.dispatcher.service.*;
import com.xhqb.spectre.dispatcher.util.ChannelRateLimiter;
import io.prometheus.client.Collector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class MessageServiceImpl implements MessageService {

    @Autowired
    private MQTemplate<String> mqTemplate;

    @Autowired
    private CmppChannelService cmppChannelService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ChannelSelectService channelSelectService;

    @Autowired
    private VenusConfig venusConfig;

    @Autowired
    private ChannelAccountService channelAccountService;

    @Resource
    private ChannelStatService channelStatService;

    /**
     * logServer服务定时写入DB的时间间隔，单位毫秒
     */
    @Value("${order.schedule.time:30000}")
    private Long orderScheduleTime;

    @Resource
    private DebtSmsRetryService debtSmsRetryService;

    @Resource
    private ChannelRateLimiter channelRateLimiter;

    /**
     * 签名名称与其md5值前8位的映射（签名就几个，md5值前8位重复的可能性微乎其微，即使重复，也只影响路由选择，不影响短信发送）
     */
    private static final Map<String, String> SIGN_MD5_MAP = new ConcurrentHashMap<>();

    /**
     * 短信发送处理
     *
     * @param message
     */
    @Override
    public void send(SendMessage message) {
        //分发短信总耗时埋点
        long start = System.nanoTime();
        //是否已发送短信
        boolean hasSent = false;
        //是否已保存订单
        boolean hasSaveOrder = false;
        //已请求send接口次数
        int requestedCount = 0;
        // 人行征信短信是否需要重试处理
        boolean debtSmsNeedRetry = false;
        try {
            //暂存原模板内容，防止在渠道遍历时被覆盖而丢失
            String originContent = message.getContent();

            //获取可用的渠道列表，并加权随机排序
            List<ChannelCode> channelList = getAndSortChannel(message);

            if (venusConfig.isSyncSendSms()) {
                DoSendDTO doSendDTO = DoSendDTO.build(message, channelList);
                doSend(doSendDTO, false);
                return;
            }

            //遍历渠道列表，发送短信，发送成功后退出循环
            for (ChannelCode item : channelList) {
                //设置渠道账号信息
                ChannelAccount channelAccount = getChannelAccount(item);
                if (Objects.isNull(channelAccount)) {
                    continue;
                }
                message.setChannelAccount(channelAccount);
                //设置渠道自定义模板内容
                message.setContent(StringUtils.isBlank(item.getMsgContent()) ? originContent : item.getMsgContent());

                //保存订单
                SendSubmitDTO sendSubmitDTO = saveOrder(message, requestedCount);
                hasSaveOrder = true;

                //调用短信发送接口（如果禁止真实发送短信，则直接返回成功响应）
                BaseResult result = isAllowRealSend(message.getPhone()) ? cmppChannelService.send(message) : BaseResult.success();

                //短信分发埋点
                dispatchEventReport(!result.needRetrySend(), message.getSmsCode(), start);

                //渠道链接不存在，进行重试
                if (result.isChannelMiss()) {
                    requestedCount++;
                    debtSmsNeedRetry = true;
                    continue;
                }
                //已发送或其它异常，都认为是短信已发送
                hasSent = true;

                //缓存此次分发信息
                cacheDispatchInfo(sendSubmitDTO, channelAccount.getId());

                if (result.isSuccess()) {
                    // 短信发送成功 重置人行债转短信重试标记
                    debtSmsNeedRetry = false;
                } else if (result.isSystemError()) {
                    // 如果是系统异常, 如: http请求网络异常等 ,人行债转短信需要进行重试处理
                    debtSmsNeedRetry = true;
                }

                //已发送，退出循环
                break;
            }
        } catch (Exception e) {
            log.error("短信发送处理异常，消息内容：{}", JSON.toJSONString(message), e);
        }
        //短信未发送处理
        if (!hasSent) {
            unSentHandle(message, hasSaveOrder);
        }

        // 人行债转短信重试信息存储处理
        debtSmsRetrySaveIfNeeded(message, debtSmsNeedRetry);
    }


    /**
     * 发送
     */
    @Override
    public void doSend(DoSendDTO doSendDTO, boolean sync) {

        try {
            //循环短信通道
            Iterator<ChannelCode> iterator = doSendDTO.getRemainChannelCodeList().iterator();

            while (iterator.hasNext()){
                ChannelCode channelCode = iterator.next();
                //设置渠道账号信息
                ChannelAccount channelAccount = getChannelAccount(channelCode);
                if (Objects.isNull(channelAccount)) {
                    iterator.remove();
                    continue;
                }

                doSendDTO.getMessage().setChannelAccount(channelAccount);
                //设置渠道自定义模板内容
                doSendDTO.getMessage().setContent(StringUtils.isBlank(channelCode.getMsgContent()) ?
                        doSendDTO.getOriginContent() : channelCode.getMsgContent());

                int billCount = MessageUtil.count(doSendDTO.getMessage().getSignName() + doSendDTO.getMessage().getContent());

                if (sync) {
                    channelRateLimiter.acquire(channelAccount.getId(), billCount);
                } else {
                    if (!channelRateLimiter.tryAcquire(channelAccount.getId(), billCount)) {
                        mqTemplate.send(venusConfig.getBlockedSmsTopic(), doSendDTO.toJSON());
                        return;
                    }
                }

                // 移除渠道
                iterator.remove();

                //保存订单
                SendSubmitDTO sendSubmitDTO = saveOrder(doSendDTO.getMessage(), doSendDTO.getRequestedCount());
                doSendDTO.setHasSaveOrder(true);

                //调用短信发送接口（如果禁止真实发送短信，则直接返回成功响应）
                BaseResult result = isAllowRealSend(doSendDTO.getMessage().getPhone()) ?
                        cmppChannelService.send(doSendDTO.getMessage()) : BaseResult.success();

                //短信分发埋点
                dispatchEventReport(!result.needRetrySend(), doSendDTO.getMessage().getSmsCode(), doSendDTO.getStart());

                //渠道链接不存在，进行重试
                if (result.isChannelMiss()) {
                    doSendDTO.incrRequestedCount();
                    doSendDTO.setDebtSmsNeedRetry(true);
                    continue;
                }
                //已发送或其它异常，都认为是短信已发送
                doSendDTO.setHasSent(true);

                //缓存此次分发信息
                cacheDispatchInfo(sendSubmitDTO, channelAccount.getId());

                if (result.isSuccess()) {
                    // 短信发送成功 重置人行债转短信重试标记
                    doSendDTO.setDebtSmsNeedRetry(false);
                } else if (result.isSystemError()) {
                    // 如果是系统异常, 如: http请求网络异常等 ,人行债转短信需要进行重试处理
                    doSendDTO.setDebtSmsNeedRetry(true);
                }
                //已发送，退出循环
                break;
            }

        } catch (Exception e) {
            log.error("短信发送处理异常，消息内容：{}", JSON.toJSONString(doSendDTO.getMessage()), e);
        }

        //短信未发送处理
        if (!doSendDTO.isHasSent()) {
            unSentHandle(doSendDTO.getMessage(), doSendDTO.isHasSaveOrder());
        }

        // 人行债转短信重试信息存储处理
        debtSmsRetrySaveIfNeeded(doSendDTO.getMessage(), doSendDTO.isDebtSmsNeedRetry());
    }

    /**
     * 获取可用的渠道列表，并加权随机排序
     *
     * @param message
     * @return
     */
    private List<ChannelCode> getAndSortChannel(SendMessage message) {
        List<ChannelCode> channelList = message.getChannelCodeSet();
        try {
            //根据渠道比例系数更新权重
            updateChannelWeight(channelList, message.getIsp());

            //渠道列表加权随机排序
            channelList = channelSelectService.randomSort(channelList);

            //降低上一次选择渠道的优先级
            filterLastSelected(message, channelList);
        } catch (Exception e) {
            log.error("sortChannel exception", e);
        }
        //重置渠道列表
        message.setChannelCodeSet(channelList);

        return channelList;
    }

    /**
     * 获取渠道账号信息
     *
     * @param item
     * @return
     */
    private ChannelAccount getChannelAccount(ChannelCode item) {
        Integer channelAccountId = Integer.valueOf(item.getChannelAccountId());
        ChannelAccount channelAccount = channelAccountService.getById(channelAccountId);
        if (Objects.isNull(channelAccount)) {
            log.error("未找到渠道账号，渠道账号ID：{}", channelAccountId);
            return null;
        }
        if (!ProtocolEnum.CMPP.getCode().equals(channelAccount.getProtocol())) {
            log.warn("暂不支持非cmpp渠道账号发送短信，渠道账号ID：{}", channelAccountId);
            return null;
        }
        return channelAccount;
    }

    /**
     * 降低上一次选择渠道的优先级
     *
     * @param message
     * @param channelList
     */
    private void filterLastSelected(SendMessage message, List<ChannelCode> channelList) {
        boolean isResend = message.getResend() > 0;
        if (CollectionUtils.isEmpty(channelList) || (!isResend && channelList.size() == 1)) {
            return;
        }
        String lastSelectedId = getLastChannel(message);
        if (StringUtils.isEmpty(lastSelectedId)) {
            return;
        }
        //如果上一次选择的渠道在可选列表中，则移动到队尾；如果是重发短信，则排除掉该渠道
        int size = channelList.size();
        int endIndex = size - 1;
        for (int i = 0; i < size; i++) {
            ChannelCode item = channelList.get(i);
            if (item.getChannelAccountId().equals(lastSelectedId)) {
                if (i < endIndex) {
                    for (int j = i; j < endIndex; j++) {
                        ChannelCode nextItem = channelList.get(j + 1);
                        channelList.set(j, nextItem);
                    }
                    channelList.set(endIndex, item);
                }
                if (isResend) {
                    channelList.remove(endIndex);
                }
                return;
            }
        }
    }

    /**
     * 获取上一次选择的渠道
     *
     * @param message
     * @return
     */
    private String getLastChannel(SendMessage message) {
        if (message.getResend() > 0) {
            //重试场景，优先通过订单号获取
            String cacheKey = MessageFormat.format(RedisKeys.DispatcherKeys.ORDER_SELECTED_CHANNEL_KEY, message.getOrderId());
            String lastSelectedId = (String) redisTemplate.opsForValue().get(cacheKey);
            if (StringUtils.isNotEmpty(lastSelectedId)) {
                return lastSelectedId;
            }
        }
        //通过手机号、模板编码获取
        String signMd5 = getSignMd5(message.getSignName());
        String cacheKey = MessageFormat.format(RedisKeys.DispatcherKeys.SELECTED_CHANNEL_KEY, message.getTplCode(), signMd5, message.getPhone());
        return (String) redisTemplate.opsForValue().get(cacheKey);
    }

    /**
     * 发送订单至MQ
     *
     * @param message
     * @param requestedCount
     * @return
     */
    private SendSubmitDTO saveOrder(SendMessage message, int requestedCount) {
        SendSubmitDTO sendSubmitDTO = SendSubmitDTO.builder()
                .orderId(Long.valueOf(message.getOrderId()))
                .requestId(message.getRequestId())
                .appCode(message.getAppCode())
                .tplCode(message.getTplCode())
                .smsTypeCode(message.getSmsCode())
                .channelCode(message.getChannelAccount().getChannelCode())
                .channelAccountId(message.getChannelAccount().getId())
                .signName(message.getSignName())
                .mobile(message.getPhone())
                .content(message.getContent()) //不包含签名
                .sliceId(message.getSliceId())
                .provinceShortName(message.getProvince())
                .cityShortName(message.getCity())
                .ispCode(message.getIsp())
                .batchId(Objects.nonNull(message.getBatchId()) ? message.getBatchId() : 0)
                .billCount(MessageUtil.count(message.getSignName() + message.getContent()))
                .sendType(message.getSendType())
                .resend(message.getResend())
                .reqSrc(message.getReqSrc())
                .phoneStatus(Objects.nonNull(message.getPhoneStatus()) ? message.getPhoneStatus() : -1L)
                .channelCodeSet(message.getChannelCodeSet())
                .sendTime(ClockUtil.INS.tick())
                .bizBatchId(message.getBizBatchId())
                .callMetis(message.getCallMetis())
                .tableNameSuffix(message.getTableNameSuffix())
                .build();

        if (CollectionUtils.isEmpty(message.getParamMap())) {
            sendSubmitDTO.setParameter("");
        } else {
            sendSubmitDTO.setParameter(Joiner.on(",").join(message.getParamMap()));
        }
        //异步写MQ
        String sendSubmitQueue = venusConfig.getSendSubmitQueue();
        if (requestedCount == 0) {
            //立即发送
            mqTemplate.createMessage(sendSubmitQueue, JSON.toJSONString(sendSubmitDTO)).key(message.getOrderId()).sendAsync();
        } else {
            //延时发送（保证写入DB的顺序）
            mqTemplate.createMessage(sendSubmitQueue, JSON.toJSONString(sendSubmitDTO)).key(message.getOrderId())
                    .deliverAfter(orderScheduleTime * requestedCount, TimeUnit.MILLISECONDS)
                    .sendAsync();
        }

        LuciferConstant.MQ_PRODUCE_COUNTER.labels(sendSubmitQueue).inc();

        return sendSubmitDTO;
    }

    /**
     * 短信分发埋点上报
     *
     * @param isSuccess
     * @param smsTypeCode
     * @param start
     */
    private void dispatchEventReport(boolean isSuccess, String smsTypeCode, long start) {
        //分发次数埋点
        int dispatchStatus = isSuccess ? LuciferConstant.DISPATCH_STATUS_SUCCESS : LuciferConstant.DISPATCH_STATUS_FAILED;
        LuciferConstant.DISPATCH_COUNTER.labels(smsTypeCode, String.valueOf(dispatchStatus)).inc();
        //分发短信总耗时埋点
        LuciferConstant.DISPATCH_TOTAL_TIME.labels(smsTypeCode).observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
    }

    /**
     * 短信未发送处理
     *
     * @param message
     * @param hasSaveOrder
     */
    private void unSentHandle(SendMessage message, boolean hasSaveOrder) {
        log.warn("短信未进行发送，消息详情：{}，是否已保存订单：{}", JSON.toJSONString(message), hasSaveOrder);
        //未分发消息埋点
        LuciferConstant.DISPATCH_NOT_HANDLE_COUNTER.labels(message.getSmsCode()).inc();
        if (hasSaveOrder) {
            //更新订单为发送失败
            ChannelAccount channelAccount = message.getChannelAccount();
            long currentTimeMillis = System.currentTimeMillis();
            SubmitResqDTO submitResqDTO = SubmitResqDTO.builder()
                    .orderId(Long.valueOf(message.getOrderId()))
                    .channelMsgId("")
                    .channelCode(channelAccount.getChannelCode())
                    .resend(message.getResend())
                    .smsTypeCode(message.getSmsCode())
                    .result(ErrorCodeEnum.NO_AVAILABLE_CHANNEL.getCode())
                    .recvSubmitTime(currentTimeMillis)
                    .recvSendTime((int) (currentTimeMillis / 1000))
                    .reqSrc(message.getReqSrc())
                    .gatewayUserName(message.getGatewayUserName())
                    .requestId(message.getRequestId())
                    .build();
            String submitResqQueue = venusConfig.getSubmitResqQueue();
            mqTemplate.createMessage(submitResqQueue, JSON.toJSONString(submitResqDTO)).key(message.getOrderId()).sendAsync();
            LuciferConstant.MQ_PRODUCE_COUNTER.labels(submitResqQueue).inc();
        }
    }

    /**
     * 缓存短信分发信息
     *
     * @param sendSubmitDTO
     * @param channelAccountId
     */
    private void cacheDispatchInfo(SendSubmitDTO sendSubmitDTO, Integer channelAccountId) {
        String signMd5 = getSignMd5(sendSubmitDTO.getSignName());
        String orderIdStr = String.valueOf(sendSubmitDTO.getOrderId());
        String channelCacheKey = MessageFormat.format(RedisKeys.DispatcherKeys.SELECTED_CHANNEL_KEY, sendSubmitDTO.getTplCode(), signMd5, sendSubmitDTO.getMobile());
        String orderChannelCacheKey = MessageFormat.format(RedisKeys.DispatcherKeys.ORDER_SELECTED_CHANNEL_KEY, orderIdStr);
        String orderCacheKey = MessageFormat.format(RedisKeys.DispatcherKeys.ORDER_KEY, orderIdStr, String.valueOf(sendSubmitDTO.getResend()));
        Integer channelTimeout = venusConfig.getChannelTimeout();
        Integer orderTimeout = getOrderCacheTimeout(sendSubmitDTO.getSmsTypeCode());

        redisTemplate.executePipelined((RedisCallback<?>) connection -> {
            String accountId = String.valueOf(channelAccountId);
            //缓存此次选择的渠道，手机号、模板维度
            connection.stringCommands().setEx(channelCacheKey.getBytes(), channelTimeout * 60, accountId.getBytes());
            //缓存此次选择的渠道，订单号维度
            connection.stringCommands().setEx(orderChannelCacheKey.getBytes(), channelTimeout * 60, accountId.getBytes());
            //缓存订单信息
            connection.stringCommands().setEx(orderCacheKey.getBytes(), orderTimeout * 60, JSON.toJSONString(sendSubmitDTO).getBytes());

            return null;
        });
    }

    /**
     * 获取订单缓存时间
     *
     * @param smsTypeCode
     * @return
     */
    private Integer getOrderCacheTimeout(String smsTypeCode) {
        if (MessageTypeEnum.VERIFY.getMessageType().equals(smsTypeCode)) {
            return venusConfig.getVerifyTimeout();
        } else if (MessageTypeEnum.NOTIFY.getMessageType().equals(smsTypeCode)
                || MessageTypeEnum.DEBT_SWAP.getMessageType().equals(smsTypeCode)) {
            return venusConfig.getNotifyTimeout();
        } else if (MessageTypeEnum.COLLECTOR.getMessageType().equals(smsTypeCode)
                || MessageTypeEnum.LIGHT_COLLECTOR.getMessageType().equals(smsTypeCode)
                || MessageTypeEnum.SEVERE_COLLECTOR.getMessageType().equals(smsTypeCode)) {
            return venusConfig.getCollectorTimeout();
        } else if (MessageTypeEnum.MARKET.getMessageType().equals(smsTypeCode)) {
            return venusConfig.getMarketTimeout();
        } else {
            return venusConfig.getVerifyTimeout();
        }
    }

    /**
     * 获取签名名称的md5值的前8位
     *
     * @param signName
     * @return
     */
    private String getSignMd5(String signName) {
        if (StringUtils.isBlank(signName)) {
            return "";
        }
        if (SIGN_MD5_MAP.containsKey(signName)) {
            return SIGN_MD5_MAP.get(signName);
        }
        String md5 = DigestUtils.md5DigestAsHex(signName.getBytes(StandardCharsets.UTF_8));
        SIGN_MD5_MAP.put(signName, md5.substring(0, 8));

        return md5;
    }

    /**
     * 是否允许真实发送短信（防止测试环境大量发送真实短信，造成资损）
     *
     * @param mobile
     * @return
     */
    private boolean isAllowRealSend(String mobile) {
        //非开发、测试环境，允许发送
        if (!VenusEnvEnum.isDevAndTest()) {
            return true;
        }
        //手机号在白名单内，允许发送
        String mobileWhiteList = venusConfig.getMobileWhiteList();
        if (StringUtils.isEmpty(mobileWhiteList)) {
            return false;
        }
        return Arrays.asList(mobileWhiteList.split(",")).contains(mobile);
    }

    /**
     * 更新渠道权重
     *
     * @param channelList
     * @param isp
     */
    private void updateChannelWeight(List<ChannelCode> channelList, String isp) {
        if (CollectionUtils.isEmpty(channelList) || channelList.size() == 1) {
            return;
        }
        Map<String, Integer> scaleFactorMap = channelStatService.getScaleFactorMap();
        for (ChannelCode item : channelList) {
            int finalWeight = calcChannelWeight(item, isp, scaleFactorMap);
            item.setWeight(finalWeight);
        }
    }

    /**
     * 计算渠道权重（最终权重 = 固定权重 * 比例系数）
     *
     * @param item
     * @param isp
     * @param scaleFactorMap
     * @return
     */
    private int calcChannelWeight(ChannelCode item, String isp, Map<String, Integer> scaleFactorMap) {
        //获取渠道维度的比例系数
        String channelAccountId = item.getChannelAccountId();
        Integer scaleFactor = scaleFactorMap.get(channelAccountId);
        if (scaleFactor == null) {
            //未找到，表明渠道正常，比例系数设置为100
            scaleFactor = 100;
        }
        //获取渠道+运营商维度的比例系数
        String ispCacheKey = channelAccountId + "_" + isp;
        Integer ispScaleFactor = scaleFactorMap.get(ispCacheKey);
        if (ispScaleFactor == null) {
            ispScaleFactor = 100;
        }
        //取两者较低的那个
        int finalScaleFactor = Math.min(scaleFactor, ispScaleFactor);

        return Math.round((float) (item.getWeight() * finalScaleFactor) / 100);
    }


    /**
     * 人行债转短信重试信息存储
     *
     * @param message
     * @param debtSmsNeedRetry
     */
    private void debtSmsRetrySaveIfNeeded(SendMessage message, boolean debtSmsNeedRetry) {
        if (!venusConfig.isDebtSmsTplCode(message.getTplCode())) {
            return;
        }

        debtSmsRetryService.debtSmsRetrySave(message, debtSmsNeedRetry);
    }
}
