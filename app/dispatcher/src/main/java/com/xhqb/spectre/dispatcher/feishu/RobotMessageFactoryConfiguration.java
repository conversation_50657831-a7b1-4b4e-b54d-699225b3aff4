package com.xhqb.spectre.dispatcher.feishu;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Configuration
@ComponentScan("com.xhqb.msgcenter.sdk.*")
class RobotMessageFactoryConfiguration {

    @Bean
    FeiShuRobotMessageFactory feiShuRobotMessageFactory() {
        return new FeiShuRobotMessageFactory();
    }
}
