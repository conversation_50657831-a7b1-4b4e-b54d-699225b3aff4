package com.xhqb.spectre.dispatcher.service.impl;

import com.xhqb.spectre.common.dal.entity.ChannelAccount;
import com.xhqb.spectre.common.dal.mapper.ChannelAccountMapper;
import com.xhqb.spectre.dispatcher.cache.ChannelAccountCache;
import com.xhqb.spectre.dispatcher.service.ChannelAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/9 14:17
 * @Description:
 */
@Service
@Slf4j
public class ChannelAccountServiceImpl implements ChannelAccountService {

    @Autowired
    private ChannelAccountMapper channelAccountMapper;

    /**
     * 查询所有可用的渠道账号
     *
     * @return
     */
    public List<ChannelAccount> getAllEnabled() {
        return channelAccountMapper.selectAllEnabled().stream().map(ChannelAccount::buildChannelAccount).collect(Collectors.toList());
    }

    @Override
    public ChannelAccount getById(Integer id) {
        ChannelAccountCache dataCache = ChannelAccountCache.getInstance();
        ChannelAccount channelAccount = dataCache.get(id);
        if (Objects.isNull(channelAccount) && dataCache.isEmpty()) {
            dataCache.refresh(getAllEnabled());
            channelAccount = dataCache.get(id);
        }
        return channelAccount;
    }
}
