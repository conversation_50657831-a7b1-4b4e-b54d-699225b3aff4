package com.xhqb.spectre.dispatcher.job;

import com.xhqb.spectre.dispatcher.service.ChannelStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/31 16:32
 * @Description:
 */
@Component
@Slf4j
public class ChannelStatCacheJob {

    @Resource
    private ChannelStatService channelStatService;

    /**
     * 更新异常渠道数据缓存，默认每30秒执行一次
     */
    @Scheduled(cron = "${channel-stat.cache.refresh.cron:*/30 * * * * ?}")
    public void execute() {
        log.info("刷新异常渠道数据缓存");
        channelStatService.refreshCache();
    }

}
