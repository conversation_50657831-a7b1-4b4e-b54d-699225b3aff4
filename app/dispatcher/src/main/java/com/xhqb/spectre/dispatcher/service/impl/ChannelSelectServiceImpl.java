package com.xhqb.spectre.dispatcher.service.impl;

import com.xhqb.spectre.common.mq.ChannelCode;
import com.xhqb.spectre.dispatcher.service.ChannelSelectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/11 14:40
 * @Description:
 */
@Service
@Slf4j
public class ChannelSelectServiceImpl implements ChannelSelectService {

    /**
     * 加权随机选择一个渠道
     *
     * @param channelList
     * @return
     */
    @Override
    public ChannelCode select(List<ChannelCode> channelList) {
        if (CollectionUtils.isEmpty(channelList)) {
            return null;
        }
        return random(channelList);
    }

    /**
     * 加权随机排序（简单实现）
     *
     * @param channelList
     * @return
     */
    @Override
    public List<ChannelCode> randomSort(List<ChannelCode> channelList) {
        if (CollectionUtils.isEmpty(channelList) || channelList.size() == 1) {
            return channelList;
        }
        List<ChannelCode> sortedList = new ArrayList<>();
        Map<String, ChannelCode> channelMap = channelList.stream()
            .collect(Collectors.toMap(ChannelCode::getChannelAccountId, Function.identity()));
        while (channelMap.size() > 0) {
            List<ChannelCode> tmpList = new ArrayList<>(channelMap.values());
            ChannelCode selected = random(tmpList);
            sortedList.add(selected);
            channelMap.remove(selected.getChannelAccountId());
        }

        return sortedList;
    }

    /**
     * 加权随机选择算法
     *
     * @param list
     * @return
     */
    private ChannelCode random(List<ChannelCode> list) {
        if (list.size() == 1) {
            return list.get(0);
        }
        try {
            TreeMap<Double, ChannelCode> weightMap = new TreeMap<>();
            for (ChannelCode channelCodeSet : list) {
                Double lastWeight = weightMap.size() == 0 ? 0D : weightMap.lastKey();
                Double weight = channelCodeSet.getWeight() <= 0 ? 0.0001D : Double.valueOf(channelCodeSet.getWeight());
                weightMap.put(weight + lastWeight, channelCodeSet);
            }
            Double randomWeight = weightMap.lastKey() * Math.random();
            SortedMap<Double, ChannelCode> tailMap = weightMap.tailMap(randomWeight, false);

            return weightMap.get(tailMap.firstKey());
        } catch (Exception e) {
            log.warn("加权随机选择算法异常", e);
            return list.get(0);
        }
    }
}
