package com.xhqb.spectre.dispatcher.listener;

import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.entity.ChannelAccount;
import com.xhqb.spectre.common.dal.entity.OpTimeDO;
import com.xhqb.spectre.dispatcher.cache.ChannelAccountCache;
import com.xhqb.spectre.dispatcher.service.ChannelAccountService;
import com.xhqb.spectre.dispatcher.service.OpTimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/9 14:09
 * @Description:
 */
@Component
@Slf4j
public class ApplicationReadyEventListener implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    private OpTimeService opTimeService;

    @Autowired
    private ChannelAccountService channelAccountService;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("初始化渠道账号数据");
        List<ChannelAccount> channelAccountList = channelAccountService.getAllEnabled();
        ChannelAccountCache.getInstance().refresh(channelAccountList);

        log.info("初始化渠道账号更新时间");
        OpTimeDO opTimeDO = opTimeService.findByModule(OpLogConstant.MODULE_CHANNEL_ACCOUNT);
        if (Objects.nonNull(opTimeDO)) {
            ChannelAccountCache.getInstance().setDataUpdateTime(opTimeDO.getOpTime());
        }
    }
}
