package com.xhqb.spectre.dispatcher.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.msgcenter.model.response.OminiSendResult;
import com.xhqb.spectre.common.dal.dto.result.DebtSmsReportOrderResult;
import com.xhqb.spectre.common.dal.entity.DebtSmsReportDO;
import com.xhqb.spectre.common.dal.mapper.DebtSmsReportMapper;
import com.xhqb.spectre.common.dal.mapper.SmsOrderMapper;
import com.xhqb.spectre.common.enums.DebtSmsReportStatusEnum;
import com.xhqb.spectre.common.utils.CommonUtil;
import com.xhqb.spectre.dispatcher.config.VenusConfig;
import com.xhqb.spectre.dispatcher.feishu.FeiShuRobotMessageFactory;
import com.xhqb.spectre.dispatcher.feishu.entity.DebtSmsReportAlert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 人行债转短信报表任务
 *
 * <AUTHOR>
 * @date 2024/10/29
 */
@Component
@Job("debtSmsReportJob")
@Slf4j
public class DebtSmsReportJob implements SimpleJob {

    private static final int PAGE_SIZE = 10;
    private static final String KEY_PREFIX = "spectre:api:debt:sms";
    /**
     * map分组key, tplCode:bizBatchId
     */
    private static final String MAP_KEY = "%s:%s";

    @Resource
    private DebtSmsReportMapper debtSmsReportMapper;
    @Resource
    private SmsOrderMapper smsOrderMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private VenusConfig venusConfig;
    @Resource
    private SqlSessionFactory spectreSqlSessionFactory;
    @Resource
    private FeiShuRobotMessageFactory feiShuRobotMessageFactory;

    @Override
    public void execute(ShardingContext shardingContext) {
        long start = System.currentTimeMillis();
        Long lastId = 0L;
        // 凌晨的时间
        int beginStartTime = Math.toIntExact(DateUtil.beginOfDay(new Date()).getTime() / 1000);
        int endStartTime = Math.toIntExact((System.currentTimeMillis() / 1000));

        // 告警的数据信息
        List<DebtSmsReportDO> alertReportList = new ArrayList<>();

        List<DebtSmsReportDO> debtSmsReportList = debtSmsReportMapper.selectDebtSmsReportJob(beginStartTime, endStartTime, PAGE_SIZE, lastId);
        while (!CollectionUtils.isEmpty(debtSmsReportList)) {
            lastId = debtSmsReportList.get(debtSmsReportList.size() - 1).getId();
            try {
                debtSmsReportProcessList(debtSmsReportList, beginStartTime, endStartTime, alertReportList);
            } catch (Exception e) {
                log.warn("债转短信报表数据处理失败， debtSmsReportList = {}", JSON.toJSONString(debtSmsReportList), e);
            }
            debtSmsReportList = debtSmsReportMapper.selectDebtSmsReportJob(beginStartTime, endStartTime, PAGE_SIZE, lastId);
        }

        // 做告警通知
        debtSmsReportAlert(alertReportList);
        log.info("debtSmsReportJob执行耗时={}, beginStartTime = {}, endStartTime ={}", (System.currentTimeMillis() - start), beginStartTime, endStartTime);
    }

    /**
     * 债转短信告警
     *
     * @param alertReportList
     */
    private void debtSmsReportAlert(List<DebtSmsReportDO> alertReportList) {
        if (CollectionUtils.isEmpty(alertReportList)) {
            return;
        }

        List<DebtSmsReportAlert> alerts = alertReportList.stream().map(DebtSmsReportAlert::build).collect(Collectors.toList());
        try {
            Map<String, Object> vars = new HashMap<>(4);
            vars.put("reportList", alerts);
            vars.put("atUserList", venusConfig.getDebtSmsReportAlertForUserList());
            OminiSendResult ominiSendResult = feiShuRobotMessageFactory.sendMap(venusConfig.getDebtSmsReportAlertStrategyId(), vars);
            if (!ominiSendResult.isSuccess()) {
                log.warn("债转短信告警通知发送失败, ominiSendResult = {}", JSON.toJSONString(ominiSendResult));
            }
        } catch (Exception e) {
            log.warn("债转短信告警异常", e);
        }
    }

    /**
     * 债转短信报表列表处理
     *
     * @param debtSmsReportList
     * @param beginStartTime
     * @param endStartTime
     * @param alertReportList
     */
    private void debtSmsReportProcessList(List<DebtSmsReportDO> debtSmsReportList, int beginStartTime, int endStartTime, List<DebtSmsReportDO> alertReportList) {
        Map<String, DebtSmsReportDO> mappings = debtSmsReportGroup(debtSmsReportList);
        doDebtSmsReportOrderStats(debtSmsReportList, beginStartTime, endStartTime, mappings);
        // 做债转短信报表数据更新
        batchUpdate(debtSmsReportList);
        // 收集告警报表数据
        alertReportList.addAll(mappings.values());
    }

    /**
     * 债转短信报表数据分组
     * <p>
     * 分组的同时,填充下一次告警时间以及检测告警次数
     *
     * @param debtSmsReportList
     * @return
     */
    private Map<String, DebtSmsReportDO> debtSmsReportGroup(List<DebtSmsReportDO> debtSmsReportList) {
        Map<String, DebtSmsReportDO> mappings = new HashMap<>(debtSmsReportList.size());
        String tplCode, bizBatchId;
        int alertTimes;
        for (DebtSmsReportDO debtSmsReport : debtSmsReportList) {
            tplCode = debtSmsReport.getTplCode();
            bizBatchId = debtSmsReport.getBizBatchId();
            fetchAmountFromCache(debtSmsReport);
            mappings.put(getMapKey(tplCode, bizBatchId), debtSmsReport);
            // 设置下一次告警时间
            debtSmsReport.setStartTime(getNextStartTime());
            // 设置告警次数
            alertTimes = CommonUtil.nullToZero(debtSmsReport.getAlertTimes()) + 1;
            debtSmsReport.setAlertTimes(alertTimes);
            if (alertTimes >= venusConfig.getDebtSmsAlertMaxTimes()) {
                debtSmsReport.setStatus(DebtSmsReportStatusEnum.ALERT_REACHED.getStatus());
                debtSmsReport.setRemark(DebtSmsReportStatusEnum.ALERT_REACHED.getDesc());
            }
        }
        return mappings;
    }

    /**
     * 债转短信报表订单统计
     * <p>
     * 统计订单数据填充订单数量和成功数量, 在触达率达标时,设置已达标状态
     *
     * @param debtSmsReportList
     * @param beginStartTime
     * @param endStartTime
     * @param mappings
     */
    private void doDebtSmsReportOrderStats(List<DebtSmsReportDO> debtSmsReportList, int beginStartTime, int endStartTime, Map<String, DebtSmsReportDO> mappings) {
        long beginSendTime = beginStartTime;
        long endSendTime = endStartTime;
        List<DebtSmsReportOrderResult> debtSmsReportOrderResults = smsOrderMapper.selectDebtSmsReportOrderStats(beginSendTime, endSendTime, debtSmsReportList);
        for (DebtSmsReportOrderResult orderResult : debtSmsReportOrderResults) {
            DebtSmsReportDO debtSmsReport = mappings.get(getMapKey(orderResult.getTplCode(), orderResult.getBizBatchId()));
            if (Objects.nonNull(debtSmsReport)) {
                debtSmsReport.setOrderAmount(CommonUtil.nullToZero(orderResult.getOrderAmount()));
                debtSmsReport.setSuccessAmount(CommonUtil.nullToZero(orderResult.getSuccessAmount()));
                if (checkReachRateCompleted(debtSmsReport)) {
                    // 触达率已达标
                    debtSmsReport.setStatus(DebtSmsReportStatusEnum.RATE_REACHED.getStatus());
                    debtSmsReport.setRemark(DebtSmsReportStatusEnum.RATE_REACHED.getDesc());
                }
            }
        }
    }

    /**
     * 从缓存中拉取提交总数和有效总数
     *
     * @param debtSmsReport
     */
    private void fetchAmountFromCache(DebtSmsReportDO debtSmsReport) {
        try {
            doFetchAmountFromCache(debtSmsReport);
        } catch (Exception e) {
            log.warn("债转短信数量缓存拉取失败, debtSmsReport = {}", JSON.toJSONString(debtSmsReport), e);
        }
    }

    private void doFetchAmountFromCache(DebtSmsReportDO debtSmsReport) {
        final String tplCode = debtSmsReport.getTplCode();
        final String bizBatchId = debtSmsReport.getBizBatchId();
        final Date now = new Date();
        // 缓存在spectre-api模块写入 参考: DebtSmsServiceImpl
        String prefix = KEY_PREFIX + ":" + DateUtil.format(now, "yyyyMMdd");
        String submitAmountKKey = prefix + ":submit:" + tplCode + ":" + bizBatchId;
        String validAmountKey = prefix + ":valid:" + tplCode + ":" + bizBatchId;
        List<String> amountList = stringRedisTemplate.opsForValue().multiGet(Arrays.asList(submitAmountKKey, validAmountKey));
        if (CollectionUtils.isEmpty(amountList)) {
            log.info("根据模板编码和业务批次号未查询到债转短信数量, tplCode = {}, bizBatchId ={}, id ={}", tplCode, bizBatchId, debtSmsReport.getId());
            return;
        }

        int submitAmount = nullToZero(amountList.get(0));
        int validAmount = nullToZero(amountList.get(1));
        debtSmsReport.setSubmitAmount(submitAmount);
        debtSmsReport.setValidAmount(validAmount);
    }

    private int nullToZero(String str) {
        if (StringUtils.isBlank(str)) {
            return 0;
        }
        return Integer.parseInt(str);
    }

    private String getMapKey(String tplCode, String bizBatchId) {
        return String.format(MAP_KEY, tplCode, bizBatchId);
    }

    private int getNextStartTime() {
        DateTime dateTime = DateUtil.offsetMinute(new Date(), venusConfig.getDebtSmsReportIntervalMinutes());
        return Math.toIntExact(dateTime.getTime() / 1000);
    }

    /**
     * 检测触达率是否已达标
     *
     * @param debtSmsReport
     * @return
     */
    private boolean checkReachRateCompleted(DebtSmsReportDO debtSmsReport) {
        try {
            int successAmount = debtSmsReport.getSuccessAmount();
            int orderAmount = debtSmsReport.getOrderAmount();
            if (successAmount == 0 || orderAmount == 0) {
                // 未完成
                return false;
            }
            BigDecimal successDecimal = new BigDecimal(successAmount);
            BigDecimal orderDecimal = new BigDecimal(orderAmount);
            // 实际触达率
            BigDecimal actualReachRate = successDecimal.divide(orderDecimal, 2, RoundingMode.HALF_UP);
            return actualReachRate.compareTo(venusConfig.getDebtSmsAlertMinReachRate()) > -1;
        } catch (Exception e) {
            log.warn("检测触达率是否已达标失败, debtSmsReport ={}", JSON.toJSONString(debtSmsReport), e);
        }
        return false;
    }


    /**
     * 批量更新
     *
     * @param debtSmsReportList
     */
    private void batchUpdate(List<DebtSmsReportDO> debtSmsReportList) {
        if (CollectionUtils.isEmpty(debtSmsReportList)) {
            return;
        }
        try {
            try (SqlSession sqlSession = spectreSqlSessionFactory.openSession(ExecutorType.BATCH)) {
                DebtSmsReportMapper mapper = sqlSession.getMapper(DebtSmsReportMapper.class);
                debtSmsReportList.forEach(mapper::updateByPrimaryKeySelective);
                sqlSession.flushStatements();
                sqlSession.commit();
            }
        } catch (Exception e) {
            log.warn("债转短信报表数据批量更新异常, alertReportList = {}", JSON.toJSONString(debtSmsReportList), e);
        }
    }
}
