package com.xhqb.spectre.dispatcher.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.channel.entity.SendMessage;
import com.xhqb.spectre.common.dal.entity.DebtSmsRetryDO;
import com.xhqb.spectre.common.dal.mapper.DebtSmsRetryMapper;
import com.xhqb.spectre.common.enums.DebtSmsRetryStatusEnum;
import com.xhqb.spectre.common.utils.CommonUtil;
import com.xhqb.spectre.dispatcher.config.VenusConfig;
import com.xhqb.spectre.dispatcher.service.DebtSmsRetryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
@Service
@Slf4j
public class DebtSmsRetryServiceImpl implements DebtSmsRetryService {

    /**
     * 债转短信重试保存
     */
    private static final String KEY_PREFIX = "spectre-dispatcher:debt:sms:retry:save:";
    /**
     * 债转短信重试保存标记120s过期
     */
    private static final long EXPIRE_SECONDS = 120;

    @Resource
    private VenusConfig venusConfig;
    @Resource
    private DebtSmsRetryMapper debtSmsRetryMapper;
    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public void debtSmsRetrySave(SendMessage message, boolean debtSmsNeedRetry) {
        try {
            doDebtSmsRetrySave(message, debtSmsNeedRetry);
        } catch (Exception e) {
            log.warn("人行债转短信重试信息存储失败, orderId ={}, tplCode = {}", message.getOrderId(), message.getTplCode(), e);
        }
    }

    private void doDebtSmsRetrySave(SendMessage message, boolean debtSmsNeedRetry) {
        String tplCode = message.getTplCode();
        if (!venusConfig.isDebtSmsTplCode(tplCode)) {
            return;
        }

        // 债转短信重试记录存储
        Long orderId = Long.valueOf(message.getOrderId());
        DebtSmsRetryDO dbResult = debtSmsRetryMapper.selectByOrderIdWithoutMessage(orderId);
        if (Objects.nonNull(dbResult)) {
            log.info("债转短信重试记录数据库已存在,当前为重试债转短信,做重试次数更新处理, orderId = {}, tplCode ={}, id = {}", orderId, tplCode, dbResult.getId());
            updateDebtSmsRecordIfRetry(dbResult, debtSmsNeedRetry);
            return;
        }

        if (!debtSmsNeedRetry) {
            // 如果债转短信不需要重试 那么就不要往下执行了
            return;
        }

        Boolean exist = redisTemplate.opsForValue().setIfAbsent(KEY_PREFIX + orderId, "1", EXPIRE_SECONDS, TimeUnit.SECONDS);
        if (!Objects.equals(exist, true)) {
            log.info("债转短信重试记录redis已存在,不需要再次进行保存, orderId = {}, tplCode = {}", orderId, tplCode);
            return;
        }

        DebtSmsRetryDO debtSmsRetry = buildDebtSmsRetry(message, orderId);
        debtSmsRetryMapper.insertSelective(debtSmsRetry);
        log.info("债转短信重试记录保存成功, orderId ={}, tplCode = {}", orderId, tplCode);
    }

    private void updateDebtSmsRecordIfRetry(DebtSmsRetryDO dbResult, boolean debtSmsNeedRetry) {
        DebtSmsRetryDO debtSmsRetry = new DebtSmsRetryDO();
        debtSmsRetry.setId(dbResult.getId());
        debtSmsRetry.setStartTime(getNextStartTime());
        int retryTimes = CommonUtil.nullToZero(dbResult.getRetryTimes()) + 1;
        debtSmsRetry.setRetryTimes(retryTimes);
        if (retryTimes >= venusConfig.getDebtSmsRetryMaxTimes()) {
            // 重试已达标
            debtSmsRetry.setRetryTimes(venusConfig.getDebtSmsRetryMaxTimes());
            debtSmsRetry.setStatus(DebtSmsRetryStatusEnum.LIMIT_REACHED.getStatus());
            debtSmsRetry.setRemark(DebtSmsRetryStatusEnum.LIMIT_REACHED.getDesc());
        }

        if (!debtSmsNeedRetry) {
            // 债转短信重试已成功
            debtSmsRetry.setStatus(DebtSmsRetryStatusEnum.FINISHED.getStatus());
            debtSmsRetry.setRemark(DebtSmsRetryStatusEnum.FINISHED.getDesc());
        }

        // 日志打印提示
        if (DebtSmsRetryStatusEnum.isLimitReached(debtSmsRetry.getStatus())) {
            log.info("债转短信重试次数超过预设阈值,不再进行重试, orderId ={}, tplCode ={}, retryTimes = {}", dbResult.getOrderId(), dbResult.getTplCode(), retryTimes);
        } else if (DebtSmsRetryStatusEnum.isFinished(debtSmsRetry.getStatus())) {
            log.info("债转短信重试成功, orderId ={},tplCode ={}, retryTimes = {}", dbResult.getOrderId(), dbResult.getTplCode(), retryTimes);
        }

        debtSmsRetryMapper.updateByPrimaryKeySelective(debtSmsRetry);
    }

    private DebtSmsRetryDO buildDebtSmsRetry(SendMessage message, Long orderId) {
        DebtSmsRetryDO debtSmsRetry = new DebtSmsRetryDO();
        debtSmsRetry.setTplCode(message.getTplCode());
        debtSmsRetry.setOrderId(orderId);
        debtSmsRetry.setStartTime(getNextStartTime());
        debtSmsRetry.setMessageJson(JSON.toJSONString(message));
        return debtSmsRetry;
    }

    private int getNextStartTime() {
        DateTime dateTime = DateUtil.offsetSecond(new Date(), venusConfig.getDebtSmsRetryIntervalSeconds());
        return Math.toIntExact(dateTime.getTime() / 1000);
    }
}
