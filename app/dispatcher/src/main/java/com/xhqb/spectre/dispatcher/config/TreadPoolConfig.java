package com.xhqb.spectre.dispatcher.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.*;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/15 10:59
 * @Description:
 */
@Configuration
public class TreadPoolConfig {

    /**
     * 发送短信的线程池
     * @return
     */
    @Bean(name = "sendSmsThreadPool")
    public ThreadPoolExecutor sendSmsThreadPool() {
        return new ThreadPoolExecutor(
                2,
                4,
                3L,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(10000),
                new CustomizableThreadFactory("send-pool-"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
