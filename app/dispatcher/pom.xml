<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>app</artifactId>
        <groupId>com.xhqb.spectre</groupId>
        <version>${revision}</version>

    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>spectre-dispatcher</artifactId>

    <properties>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.xhqb.spectre</groupId>
            <artifactId>common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xhqb.spectre</groupId>
            <artifactId>channel</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xhqb</groupId>
            <artifactId>kael-mq-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <!--必选，infra里面集成了trace、metrics和logging，不必逐个引用-->
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-infra</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-venus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-lucifer</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>

        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.xhqb.kael.mybatis</groupId>
            <artifactId>kael-mybatis-plugin-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <!--sharding-jdbc -->
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-core</artifactId>
            <version>4.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-elasticjob</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb.msg-center</groupId>
            <artifactId>msg-center-service</artifactId>
            <version>5.9.0.RELEASE-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.xhqb.msg-center</groupId>
                    <artifactId>msg-center-common-dal</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!--            <plugin>-->
            <!--                <groupId>org.springframework.boot</groupId>-->
            <!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
            <!--            </plugin>-->

            <!--            <plugin>-->
            <!--                <groupId>org.apache.maven.plugins</groupId>-->
            <!--                <artifactId>maven-javadoc-plugin</artifactId>-->
            <!--            </plugin>-->

            <!--<plugin>-->
                <!--<groupId>org.apache.maven.plugins</groupId>-->
                <!--<artifactId>maven-compiler-plugin</artifactId>-->
                <!--<version>3.8.1</version>-->
                <!--<configuration>-->
                    <!--<source>8</source>-->
                    <!--<target>8</target>-->
                <!--</configuration>-->
            <!--</plugin>-->
        </plugins>
    </build>

</project>