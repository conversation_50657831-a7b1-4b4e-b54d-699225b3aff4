package com.xhqb.spectre.cmppserver.handler;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.cmppserver.dto.ClientMessageDTO;
import com.xhqb.spectre.cmppserver.dto.RedisCoupleMessage;
import com.zx.sms.codec.cmpp.msg.*;
import com.zx.sms.handler.api.AbstractBusinessHandler;
import io.netty.channel.ChannelHandler.Sharable;
import io.netty.channel.ChannelHandlerContext;
import io.netty.util.CharsetUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.UUID;

import static com.xhqb.spectre.cmppserver.constant.CmppConstant.CMPP_ORDER_ID;

@Sharable
@Slf4j
public class CMPPResponseSenderHandler extends AbstractBusinessHandler {

//	@Resource
//	private CachedUidGenerator cachedUidGenerator;

	@Autowired
	private RedisTemplate redisTemplate;

	@Autowired
	private MQTemplate<String> producer;

	@Value("#{'${kael.mq.producers:}'.split(',')[0]}")
	private String producerName;


	@Override
    public void channelRead(final ChannelHandlerContext ctx, Object msg) throws Exception {
    	
    	//此时未经过长短信合并
    	if (msg instanceof CmppDeliverRequestMessage) {
    		CmppDeliverRequestMessage e = (CmppDeliverRequestMessage) msg;
    		CmppDeliverResponseMessage responseMessage = new CmppDeliverResponseMessage(e.getHeader().getSequenceId());
			responseMessage.setResult(0);
			responseMessage.setMsgId(e.getMsgId());
			ctx.channel().writeAndFlush(responseMessage);
    	} else if (msg instanceof CmppSubmitRequestMessage) {
    		CmppSubmitRequestMessage e = (CmppSubmitRequestMessage) msg;
    		CmppSubmitResponseMessage resp = new CmppSubmitResponseMessage(e.getHeader().getSequenceId());
			resp.setResult(0);
			ctx.channel().writeAndFlush(resp);

			// 收到submit request 开始处理正常业务
			String phoneNum = e.getDestterminalId()[0];
			String content = new String(e.getMsgContentBytes(), CharsetUtil.UTF_16BE);
			String serviceId = e.getServiceId();
			String msgSrc = e.getMsgsrc();

			String uuid = UUID.randomUUID().toString();

			RedisCoupleMessage coupleMessage = RedisCoupleMessage.builder()
					.msgId(resp.getMsgId())
					.msgSrc(e.getMsgsrc())
					.srcId(e.getSrcId())
					.phoneNum(phoneNum).build();

			redisTemplate.opsForHash().put(CMPP_ORDER_ID, uuid, coupleMessage);

			String msgId = resp.getMsgId().toHexString(true);
			log.info("serviceId {} ,phone {}, content {}", serviceId, phoneNum, content);

			ClientMessageDTO messageDTO =  ClientMessageDTO.builder().content(content).phoneNum(phoneNum).build();
			producer.createMessage(producerName, JSONObject.toJSONString(messageDTO)).send();

			// 模拟隔1秒发送delivery
//			if (e.getRegisteredDelivery()==1) {
//				final CmppDeliverRequestMessage deliver = new CmppDeliverRequestMessage();
//				deliver.setDestId(e.getSrcId());
//				deliver.setSrcterminalId(e.getDestterminalId()[0]);
//				CmppReportRequestMessage report = new CmppReportRequestMessage();
//				report.setDestterminalId(deliver.getSrcterminalId());
//				report.setMsgId(resp.getMsgId());
//				String t = DateFormatUtils.format(CachedMillisecondClock.INS.now(), "yyMMddHHmm");
//				report.setSubmitTime(t);
//				report.setDoneTime(t);
//				report.setStat("DELIVRD");
//				report.setSmscSequence(0);
//				deliver.setReportRequestMessage(report);
//				ctx.executor().submit(new Runnable() {
//					public void run() {
//							ctx.channel().writeAndFlush(deliver);
//					}
//				});
//			}
			
			
    	}else if (msg instanceof CmppQueryRequestMessage) {
			CmppQueryRequestMessage e = (CmppQueryRequestMessage) msg;
			CmppQueryResponseMessage res = new CmppQueryResponseMessage(e.getHeader().getSequenceId());
			ctx.channel().writeAndFlush(res);
		}
    	
    	ctx.fireChannelRead(msg);
    }
    
	@Override
	public String name() {
		return "CMPPResponseSenderHandler";
	}

}
