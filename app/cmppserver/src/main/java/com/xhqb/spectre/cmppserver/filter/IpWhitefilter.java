package com.xhqb.spectre.cmppserver.filter;

import com.xhqb.kael.util.StringUtils;
import com.xhqb.kael.util.web.WebUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Slf4j
public class IpWhitefilter implements Filter {

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String ipAddr = WebUtils.getIpAddr(request);

        // 加一个ip 限制，目前打开
        if (!StringUtils.isEmpty(ipAddr)) {
            filterChain.doFilter(servletRequest, servletResponse);
        }
    }
}
