package com.xhqb.spectre.cmppserver.controller;

import com.xhqb.spectre.cmppserver.dto.ChildChannelAccount;
import com.xhqb.spectre.cmppserver.dto.ChildChannelStatus;
import com.xhqb.spectre.cmppserver.result.CmppHttpResult;
import com.zx.sms.connect.manager.EndpointEntity;
import com.zx.sms.connect.manager.EndpointManager;
import com.zx.sms.connect.manager.cmpp.CMPPServerChildEndpointEntity;
import com.zx.sms.connect.manager.cmpp.CMPPServerEndpointConnector;
import com.zx.sms.connect.manager.cmpp.CMPPServerEndpointEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.Charset;

@RestController
@RequestMapping("/channel")
public class ChannelAccountController {

    @Autowired
    private CMPPServerEndpointEntity serverEndpointEntity;

    @PostMapping("/online")
    public CmppHttpResult onlineChannel(@RequestBody ChildChannelAccount childChannelAccount) {
        CMPPServerEndpointConnector endpointConnector =
                (CMPPServerEndpointConnector)EndpointManager.INS.getEndpointConnector(serverEndpointEntity);
//        endpointConnector.getEndpointEntity()

        CMPPServerChildEndpointEntity childEndpointEntity = new CMPPServerChildEndpointEntity();
        childEndpointEntity.setValid(true);
        childEndpointEntity.setId(childChannelAccount.getServiceId());
        childEndpointEntity.setChartset(Charset.forName("utf-8"));
        childEndpointEntity.setGroupName(childChannelAccount.getGroupName());
        childEndpointEntity.setUserName(childChannelAccount.getUserName());
        childEndpointEntity.setPassword(childChannelAccount.getPassword());

        childEndpointEntity.setVersion((short) childChannelAccount.getVersion());

        childEndpointEntity.setMaxChannels((short) childChannelAccount.getMaxConnect());
        childEndpointEntity.setRetryWaitTimeSec((short) 30);
        childEndpointEntity.setMaxRetryCnt((short) 3);
        childEndpointEntity.setReSendFailMsg(false);

        serverEndpointEntity.addchild(childEndpointEntity);
        return CmppHttpResult.success();
    }

    @DeleteMapping("/offline")
    public CmppHttpResult offlineChannel(@RequestParam("username") String username) throws Exception {

        CMPPServerChildEndpointEntity childEndpointEntity = new CMPPServerChildEndpointEntity();
        childEndpointEntity.setValid(true);
        childEndpointEntity.setUserName(username);

        // 将连上channel踢下去


        // 直接干掉
        serverEndpointEntity.getChild(username).getSingletonConnector().close();
        serverEndpointEntity.removechild(childEndpointEntity);
        return CmppHttpResult.success();
    }

    @GetMapping("/peek")
    public CmppHttpResult peekChannel(@RequestParam("username") String channelUsername) {
        EndpointEntity endpointEntity = serverEndpointEntity.getChild(channelUsername);
        ChildChannelStatus childChannelAccount = new ChildChannelStatus();
        childChannelAccount.setValid(endpointEntity.isValid());
        childChannelAccount.setConnectNum(endpointEntity.getSingletonConnector().getConnectionNum());

        return CmppHttpResult.success(childChannelAccount);
//        return endpointEntity.;
    }

}
