package com.xhqb.spectre.cmppserver;

import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
public class CmppServerApplication implements CommandLineRunner {
    public static void main(String[] args) {
        SpringApplication.run(CmppServerApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
    }
}
