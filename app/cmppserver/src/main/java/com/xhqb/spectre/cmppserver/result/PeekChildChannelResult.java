package com.xhqb.spectre.cmppserver.result;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.cmppserver.dto.ChildChannelAccount;

public class PeekChildChannelResult {

//    public static void main(String[] args) {
//        ChildChannelAccount account = new ChildChannelAccount();
//        account.setIsValid(true);
//        account.setMaxConnect((short)5);
//        account.setPassword("ICP002");
//        account.setUserName("901722");
//        account.setServiceId("child2");
//        account.setVersion((short) 0x20);
//        account.setGroupName("test10");
//        System.out.println(JSONObject.toJSONString(account));
//    }
}
