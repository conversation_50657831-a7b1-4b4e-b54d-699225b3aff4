package com.xhqb.spectre.cmppserver.handler;

import com.zx.sms.BaseMessage;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelHandlerContext;

public class CMPPMessageReceiveHandler extends MessageReceiveHandler {

	@Override
	protected ChannelFuture reponse(ChannelHandlerContext ctx, Object msg) {
		
		if(msg instanceof BaseMessage) {
			BaseMessage basemsg = (BaseMessage)msg;
			if(basemsg.isRequest())
				return ctx.newSucceededFuture();
		}
		return null;
		
	}

}
