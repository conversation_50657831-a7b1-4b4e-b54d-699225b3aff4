package com.xhqb.spectre.cmppserver.dto;

import com.zx.sms.common.util.MsgId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RedisCoupleMessage implements Serializable {

    private static final long serialVersionUID = 4120740018151710040L;

    private MsgId msgId;

    private String phoneNum;

    private String msgSrc;

    private String srcId;
}
