package com.xhqb.spectre.cmppserver.config;

import com.zx.sms.connect.manager.EndpointManager;
import com.zx.sms.connect.manager.cmpp.CMPPServerEndpointEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class InitialChildAccount implements InitializingBean {
    @Autowired
    private CMPPServerEndpointEntity serverEndpointEntity;

    @Override
    public void afterPropertiesSet() throws Exception {
        final EndpointManager manager = EndpointManager.INS;
        manager.addEndpointEntity(serverEndpointEntity);
        manager.openEndpoint(serverEndpointEntity);

        log.info("InitialChildAccount start.....");
    }
}
