package com.xhqb.spectre.cmppserver.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChildChannelAccount {

    private String serviceId;

    private String userName;

    private String password;

    private Boolean isValid;

    private short version;

    private short maxConnect;

    private long writeLimit;

    private long readLimit;

    private Set<String> ipList;

    private String groupName;

    private Integer idleTimeSec;

}
