package com.xhqb.spectre.cmppserver.config;

import com.xhqb.spectre.cmppserver.filter.IpWhitefilter;
import com.xhqb.spectre.cmppserver.handler.CMPPMessageReceiveHandler;
import com.xhqb.spectre.cmppserver.handler.CMPPResponseSenderHandler;
import com.zx.sms.connect.manager.cmpp.CMPPServerChildEndpointEntity;
import com.zx.sms.connect.manager.cmpp.CMPPServerEndpointEntity;
import com.zx.sms.handler.api.AbstractBusinessHandler;
import com.zx.sms.handler.api.BusinessHandlerInterface;
import io.netty.channel.ChannelHandlerContext;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

@Configuration
public class CmppServerConfig {

    @Bean
    public FilterRegistrationBean ipwhiteFilter() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        IpWhitefilter ipWhitefilter = new IpWhitefilter();
        filterRegistrationBean.setFilter(ipWhitefilter);
        // 配置过滤规则
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.setName("requestReplaced");
        filterRegistrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return filterRegistrationBean;
    }

    @Bean
    public CMPPServerEndpointEntity CMPPServerEndpointEntity(List<CMPPServerChildEndpointEntity> childEndpointEntities){
        CMPPServerEndpointEntity server = new CMPPServerEndpointEntity();
        server.setId("server");
        server.setHost("0.0.0.0");
        server.setPort(7890);
        server.setValid(true);
        // 使用ssl加密数据流
        server.setUseSSL(false);

        childEndpointEntities.forEach($ -> server.addchild($));
        return server;
    }

    @Bean
    public List<BusinessHandlerInterface> businessHandler() {
        List<BusinessHandlerInterface> serverhandlers = new ArrayList<BusinessHandlerInterface>();

        CMPPMessageReceiveHandler receiver = new CMPPMessageReceiveHandler();
        serverhandlers.add(new AbstractBusinessHandler() {

            @Override
            public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
                CMPPResponseSenderHandler handler = new CMPPResponseSenderHandler();
                handler.setEndpointEntity(getEndpointEntity());
                ctx.pipeline().addAfter("sessionStateManager", handler.name(), handler);
                ctx.pipeline().remove(this);
            }

            @Override
            public String name() {
                return "AddCMPPResponseSenderHandler";
            }

        });
        serverhandlers.add(receiver);
        return serverhandlers;
    }

    @Bean
    public List<CMPPServerChildEndpointEntity> childEndpointEntities(List<BusinessHandlerInterface> businessHandlerInterfaces) {
        List<CMPPServerChildEndpointEntity> sourceChildEndpointEntities = new ArrayList<>();
        CMPPServerChildEndpointEntity child = new CMPPServerChildEndpointEntity();
        child.setId("child");
        child.setChartset(Charset.forName("utf-8"));
        child.setGroupName("test");
        child.setUserName("901783");
        child.setPassword("ICP001");

        child.setValid(true);
        child.setVersion((short) 0x20);

        child.setMaxChannels((short) 1);
        child.setRetryWaitTimeSec((short) 30);
        child.setMaxRetryCnt((short) 3);
        child.setReSendFailMsg(false);

        sourceChildEndpointEntities.add(child);

        List<CMPPServerChildEndpointEntity> childEndpointEntities = new ArrayList<>();
        sourceChildEndpointEntities.forEach( $ -> {
            CMPPServerChildEndpointEntity endpointEntity = new CMPPServerChildEndpointEntity();
            endpointEntity.setId($.getId());
            endpointEntity.setChartset(Charset.forName("utf-8"));
            endpointEntity.setGroupName($.getGroupName());
            endpointEntity.setUserName($.getUserName());
            endpointEntity.setPassword($.getPassword());

            endpointEntity.setValid(true);
            endpointEntity.setVersion((short) 0x20);

            endpointEntity.setMaxChannels((short) $.getMaxChannels());
            endpointEntity.setRetryWaitTimeSec((short) 30);
            endpointEntity.setMaxRetryCnt((short) 3);
            endpointEntity.setReSendFailMsg(false);
            businessHandlerInterfaces.forEach( busi -> endpointEntity.setBusinessHandlerSet(businessHandlerInterfaces));


            childEndpointEntities.add(endpointEntity);
        });
        return childEndpointEntities;
    }
}
