package com.xhqb.spectre.cmppserver.result;

import com.xhqb.spectre.cmppserver.enums.RespCodeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class CmppHttpResult implements Serializable {

    private Integer code;

    private String message;

    private Object data;

    public CmppHttpResult() {

    }

    public CmppHttpResult(final Integer code, final String msg, final Object data) {
        this.code = code;
        this.message = msg;
        this.data = data;
    }

    public static CmppHttpResult success(final Object data) {
        return get(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMsg(), data);
    }

    public static CmppHttpResult success() {
        return get(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMsg(), null);
    }

    private static CmppHttpResult get(final int code, final String msg, final Object data) {
        return new CmppHttpResult(code, msg, data);
    }


}
