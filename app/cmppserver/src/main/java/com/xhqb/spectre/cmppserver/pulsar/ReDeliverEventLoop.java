package com.xhqb.spectre.cmppserver.pulsar;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.cmppserver.dto.RedisCoupleMessage;
import com.xhqb.spectre.common.dal.dto.mq.CmppDeliverResqDTO;
import com.xhqb.spectre.common.dal.dto.mq.DeliverResqDTO;
import com.xhqb.spectre.common.enums.ProtocolTypeEnum;
import com.zx.sms.codec.cmpp.msg.CmppDeliverRequestMessage;
import com.zx.sms.codec.cmpp.msg.CmppReportRequestMessage;
import com.zx.sms.common.util.CachedMillisecondClock;
import com.zx.sms.connect.manager.cmpp.CMPPServerEndpointEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import static com.xhqb.spectre.cmppserver.constant.CmppConstant.CMPP_ORDER_ID;

@Slf4j
@Component
public class ReDeliverEventLoop {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private CMPPServerEndpointEntity serverEndpointEntity;

    @MQConsumer(topic="#{'${kael.mq.consumers:}'.split(',')[0]}", clazz= String.class, ackTimeout = 30l, count=4, receiverQueueSize = 100)
    public void consumeCmppDeliverReport(String message) {
        log.info("收到消息：consumeCmppDeliverReport " + message);
        DeliverResqDTO deliverResqDTO = JSONObject.parseObject(message, DeliverResqDTO.class);
        // 只处理cmpp 协议的Q
        if (deliverResqDTO.getType().equalsIgnoreCase(ProtocolTypeEnum.CMPP.getName())) {
            CmppDeliverResqDTO resqDTO = deliverResqDTO.getCmppDeliverResqDTO();
            RedisCoupleMessage coupleMessage =
                    (RedisCoupleMessage)redisTemplate.opsForHash()
                            .get(CMPP_ORDER_ID, resqDTO.getOrderId());

            final CmppDeliverRequestMessage deliver = new CmppDeliverRequestMessage();
            deliver.setDestId(coupleMessage.getSrcId());
            deliver.setSrcterminalId(coupleMessage.getPhoneNum());
            CmppReportRequestMessage report = new CmppReportRequestMessage();
            report.setDestterminalId(deliver.getSrcterminalId());
            report.setMsgId(coupleMessage.getMsgId());
            String t = DateFormatUtils.format(CachedMillisecondClock.INS.now(), "yyMMddHHmm");
            report.setSubmitTime(t);
            report.setDoneTime(t);
            report.setStat(resqDTO.getReportStatus());
            report.setSmscSequence(0);
            deliver.setReportRequestMessage(report);

            serverEndpointEntity.getChild(coupleMessage.getMsgSrc())
                    .getSingletonConnector()
                    .synwrite(deliver);
        }
//        received.set(true);
    }

}

