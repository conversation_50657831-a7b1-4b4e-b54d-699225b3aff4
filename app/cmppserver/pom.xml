<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>app</artifactId>
        <groupId>com.xhqb.spectre</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>spectre-cmppserver</artifactId>
    <packaging>jar</packaging>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb.spectre</groupId>
            <artifactId>common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.github.wujun234</groupId>
                    <artifactId>uid-generator-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--<dependency>-->
            <!--<groupId>com.github.wujun234</groupId>-->
            <!--<artifactId>uid-generator-spring-boot-starter</artifactId>-->
            <!--<version>1.0.3.RELEASE</version>-->
        <!--</dependency>-->

        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-util</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-venus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-infra</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb</groupId>
            <artifactId>kael-mq-starter</artifactId>
            <version>1.2.0.9-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.chinamobile.cmos</groupId>
            <artifactId>sms-core</artifactId>
            <version>xh-1.1.RELEASE</version>
        </dependency>
    </dependencies>

</project>