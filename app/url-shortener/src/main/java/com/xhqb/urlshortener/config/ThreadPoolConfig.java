package com.xhqb.urlshortener.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
public class ThreadPoolConfig {
    /**
     * @Async注解线程池核心线程数
     */
    @Value("${async.core.pool.size:1}")
    private volatile int asyncCorePoolSize;

    /**
     * @Async注解线程池最大线程数
     */
    @Value("${async.max.pool.size:4}")
    private volatile int asyncMaxPoolSize;

    /**
     * @Async注解线程池任务队列
     */
    @Value("${async.max.queue.len:8}")
    private volatile int asyncMaxQueueLen;



    /**
     * @return
     * @Async 线程池
     */
    @Bean(name = "burialPointExecutor")
    public ThreadPoolTaskExecutor burialPointExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(asyncCorePoolSize);
        executor.setMaxPoolSize(asyncMaxPoolSize);
        executor.setKeepAliveSeconds(3);
        executor.setQueueCapacity(asyncMaxQueueLen);
        executor.setThreadNamePrefix("async-burial-point-service-%d");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

}
