package com.xhqb.urlshortener.utils;

import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * 时间工具类
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/1/29.
 */
public class DateTimeUtil {

    public static final String DATE_FORMAT_DATETIME = "yyyy-MM-dd HH:mm:ss";

    private static final DateFormat DATE_FORMATTER = new SimpleDateFormat(DATE_FORMAT_DATETIME);

    /**
     * 获取不同的日期格式
     *
     * @param date
     * @param type
     * @return
     */
    public static String queryDateToStr(Date date, int type) {
        switch (type) {
            case 0:
                return queryDateToStr(date);
            case 1:
                return queryDateToStr(date, "yyyy/MM");
            case 2:
                return queryDateToStr(date, "yyyyMMdd");
            case 3:
                return queryDateToStr(date, "yyyyMM");
            case 4:
                return queryDateToStr(date, "yyyy/MM/dd HH:mm:ss");
            case 5:
                return queryDateToStr(date, "yyyyMMddHHmmss");
            case 6:
                return queryDateToStr(date, "yyyy/MM/dd HH:mm");
            case 7:
                return queryDateToStr(date, "HH:mm:ss");
            case 8:
                return queryDateToStr(date, "HH:mm");
            case 9:
                return queryDateToStr(date, "HHmmss");
            case 10:
                return queryDateToStr(date, "HHmm");
            case 11:
                return queryDateToStr(date, "yyyy-MM-dd");
            case 12:
                return queryDateToStr(date, "yyyy-MM-dd HH:mm:ss");
            case 13:
                return queryDateToStr(date, "yyyyMMddHHmmssSSS");
            case 14:
                return queryDateToStr(date, "yyyyMMddHHmm");
            case 15:
                return queryDateToStr(date, "MMddHHmmss");
            default:
                throw new IllegalArgumentException("Type undefined : " + type);
        }
    }

    /**
     * 获取字符串格式的时间
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String queryDateToStr(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat formatter = new SimpleDateFormat(pattern);
            return formatter.format(date);
        } else {
            return null;
        }
    }

    /**
     * 获取默认日期时间格式
     *
     * @param date
     * @return
     */
    public static String queryDateToStr(Date date) {
        return queryDateToStr(date, "yyyyMMdd");
    }

    /**
     * 获取未来若干秒的Date
     *
     * @param seconds
     * @return
     */
    public static Date mergeForwardSecs(int seconds) {
        return mergeDate(Calendar.SECOND, +seconds);
    }


    /**
     * 获取过去若干秒的date
     *
     * @param timeNow
     * @param seconds
     * @return
     */
    public static Date mergeBackSecs(Date timeNow, int seconds) {
        return mergeDate(timeNow, Calendar.SECOND, -seconds);
    }


    /**
     * 获取过去若干分钟的Date
     *
     * @param timeNow
     * @param mins
     * @return
     */
    public static Date mergeBackMins(Date timeNow, int mins) {
        return mergeDate(timeNow, Calendar.MINUTE, -mins);
    }

    /**
     * 获取过去若干分钟的Date
     *
     * @param mins
     * @return
     */
    public static Date mergeBackMins(int mins) {
        return mergeDate(Calendar.MINUTE, -mins);
    }


    /**
     * 获取未来若干分钟的Date
     *
     * @param timeNow
     * @param mins
     * @return
     */
    public static Date mergeForwardMins(Date timeNow, int mins) {
        return mergeDate(timeNow, Calendar.MINUTE, +mins);
    }

    /**
     * 获取未来若干分钟的Date
     *
     * @param mins
     * @return
     */
    public static Date mergeForwardMins(int mins) {
        return mergeDate(Calendar.MINUTE, +mins);
    }


    /**
     * 获取过去若干小时的Date
     *
     * @param timeNow
     * @param hours
     * @return
     */
    public static Date mergeBackHours(Date timeNow, int hours) {
        return mergeDate(timeNow, Calendar.HOUR_OF_DAY, -hours);
    }

    /**
     * 获取过去若干小时的Date
     *
     * @param hours
     * @return
     */
    public static Date mergeBackHours(int hours) {
        return mergeDate(Calendar.HOUR_OF_DAY, -hours);
    }

    /**
     * 获取未来若干小时的Date
     */
    public static Date mergeForwardHours(Date timeNow, int hours) {
        return mergeDate(timeNow, Calendar.HOUR_OF_DAY, +hours);
    }

    /**
     * 获取未来若干小时的Date
     *
     * @param hours
     * @return
     */
    public static Date mergeForwardHours(int hours) {
        return mergeDate(Calendar.HOUR_OF_DAY, +hours);
    }

    /**
     * 获取过去若干天的Date
     */
    public static Date mergeBackDays(Date timeNow, int days) {
        return mergeDate(timeNow, Calendar.DATE, -days);
    }

    /**
     * 获取过去若干天的Date
     */
    public static Date mergeBackDays(int days) {
        return mergeDate(Calendar.DATE, -days);
    }

    /**
     * 获取未来若干天的Date
     *
     * @param timeNow
     * @param days
     * @return
     */
    public static Date mergeForwardDays(Date timeNow, int days) {
        return mergeDate(timeNow, Calendar.DATE, +days);
    }

    /**
     * 获取未来若干天的Date
     *
     * @param days
     * @return
     */
    public static Date mergeForwardDays(int days) {
        return mergeDate(Calendar.DATE, +days);
    }

    /**
     * 获取过去若干月份的Date
     *
     * @param timeNow
     * @param months
     * @return
     */
    public static Date mergeBackMonths(Date timeNow, int months) {
        return mergeDate(timeNow, Calendar.MONTH, -months);
    }

    /**
     * 获取过去若干月份的Date
     *
     * @param months
     * @return
     */
    public static Date mergeBackMonths(int months) {
        return mergeDate(Calendar.MONTH, -months);
    }

    /**
     * 获取未来若干月份的Date
     *
     * @param timeNow
     * @param months
     * @return
     */
    public static Date mergeForwardMonths(Date timeNow, int months) {
        return mergeDate(timeNow, Calendar.MONTH, +months);
    }

    /**
     * 获取未来若干月份的Date
     *
     * @param months
     * @return
     */
    public static Date mergeForwardMonths(int months) {
        return mergeDate(Calendar.MONTH, +months);
    }

    /**
     * 对指定日期加减
     *
     * @param timeNow
     * @param dateItem
     * @param dateMerge
     * @return
     */
    private static Date mergeDate(Date timeNow, int dateItem, int dateMerge) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(timeNow);
        calendar.add(dateItem, dateMerge); //dateItem:Calendar单位标识, dateMerge:加减的度
        Date timeAgo = calendar.getTime();
        return timeAgo;
    }

    /**
     * 对当前日期进行加减
     *
     * @param dateItem
     * @param dateMerge
     * @return
     */
    private static Date mergeDate(int dateItem, int dateMerge) {
        Date timeNow = new Date();
        return mergeDate(timeNow, dateItem, dateMerge);
    }


    /************************** 计算时间段间隔 **************************/

    /**
     * 时间间隔 - 秒
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    public static long countTimeAsSecound(Date beginTime, Date endTime) {
        return (endTime.getTime() - beginTime.getTime()) / 1000;
    }

    /**
     * 根据正则规则将String日期转换成Date形式
     *
     * @param dateStr
     * @param regex
     * @return
     */
    public static Date transformStrToDate(String dateStr, String regex) throws ParseException {
        Date date = null;
        if (StringUtils.isAnyBlank(dateStr, regex)) {
            throw new ParseException("dateStr or regex can not be empty!", 0);
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat(regex);
        try {
            date = dateFormat.parse(dateStr);
        } catch (ParseException e) {
            throw new ParseException(new StringBuilder(e.getMessage()).append(", the dateStr = ")
                    .append(dateStr).append(" and the regex = ").append(regex).toString(), 1);
        }
        return date;
    }

    /**
     * 获取当天最大时间
     *
     * @return
     */
    public static Date getDateMaxTime(Date timeNow) {

        if (null == timeNow) {
            timeNow = new Date();
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(timeNow);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        return calendar.getTime();
    }

    /**
     * 获取当天最小时间
     *
     * @return
     */
    public static Date getDateMinTime(Date timeNow) {

        if (null == timeNow) {
            timeNow = new Date();
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(timeNow);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    /**
     * 时间戳转换为时间格式
     *
     * @param time
     * @return
     */
    public static String intToString(Integer time) {
        if (Objects.isNull(time) || time == 0) {
            return "";
        }
        return DATE_FORMATTER.format(new Date((long) time * 1000));
    }

    public static String longToString(Long time) {
        if (Objects.isNull(time) || time == 0) {
            return "";
        }
        return DATE_FORMATTER.format(new Date(time * 1000));
    }

    /**
     * int 时间搓转日期
     *
     * @param timestamp
     * @return
     */
    public static Date intToDate(Integer timestamp) {
        return Objects.isNull(timestamp) ? null : new Date((long) timestamp * 1000);
    }

    /**
     * 获取系统当前时间戳，精确到秒
     *
     * @return
     */
    public static Integer getCurrentTime() {
        long time = System.currentTimeMillis() / 1000L;
        return Integer.valueOf(Long.toString(time));
    }

}