package com.xhqb.urlshortener.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class UserShortUrlDTO implements Serializable {

    /**
     * 批次id
     */
    private String batchId;
    /**
     * 用户短链模版
     */
    @NotBlank(message = "用户短链模版不能为空")
    private String tplCode;
    /**
     * 用户信息
     */
    @NotNull(message = "用户信息不能为空")
    private List<UserShortUrlDataDTO> dataList;

}
