package com.xhqb.urlshortener.utils;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;

import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * ase工具
 *
 * @author: yjq
 * @date: 2024/05/28
 */
public final class AesUtil {

    private AesUtil(){

    }

    /**
     * 16字节
     */
    private static final String ENCODE_KEY = "1234567812345678";
    private static final String IV_KEY = "0000000000000000";

    public static String encryptFromString(String data, Mode mode, Padding padding) {
        AES aes;
        if (Mode.CBC == mode) {
            aes = new AES(mode, padding,
                    new SecretKeySpec(ENCODE_KEY.getBytes(), "AES"),
                    new IvParameterSpec(IV_KEY.getBytes()));
        } else {
            aes = new AES(mode, padding,
                    new SecretKeySpec(ENCODE_KEY.getBytes(), "AES"));
        }
        return aes.encryptBase64(data, StandardCharsets.UTF_8);
    }

    public static String decryptFromString(String data, Mode mode, Padding padding) {
        AES aes;
        if (Mode.CBC == mode) {
            aes = new AES(mode, padding,
                    new SecretKeySpec(ENCODE_KEY.getBytes(), "AES"),
                    new IvParameterSpec(IV_KEY.getBytes()));
        } else {
            aes = new AES(mode, padding,
                    new SecretKeySpec(ENCODE_KEY.getBytes(), "AES"));
        }
        byte[] decryptDataBase64 = aes.decrypt(data);
        return new String(decryptDataBase64, StandardCharsets.UTF_8);
    }

//    public static void main(String[] args) {
//        String mobile = AesUtil.encryptFromString("0376-19173492116", Mode.CBC, Padding.ZeroPadding);
//        System.out.println("加密数据"+mobile);
//        System.out.println("加密数据长度"+mobile.length());
//        String mobile2 = AesUtil.decryptFromString(mobile, Mode.CBC, Padding.ZeroPadding);
//        System.out.println("解密数据"+mobile2);
//        System.out.println("解密数据长度"+mobile2.length());
//    }
}
