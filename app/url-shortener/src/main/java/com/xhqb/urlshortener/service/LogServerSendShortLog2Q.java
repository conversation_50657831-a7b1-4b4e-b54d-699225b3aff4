package com.xhqb.urlshortener.service;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.common.mq.UrlShortenerLogMQ;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.function.Function;

@Component
@Slf4j
public class LogServerSendShortLog2Q {

    @Resource
    private MQTemplate<String> mqTemplate;


    @Value("#{'${kael.mq.producers:}'.split(',')[0]}")
    private String shortLogMessage;

    /**
     * @param urlShortenerLogMQ
     * @return
     * @Description: 发送日志消息
     */
    public Boolean logServerShortLogMessage2Q(UrlShortenerLogMQ urlShortenerLogMQ) {
        Boolean dispatchFlag = dispatchSMSMessage2QAsync(shortLogMessage, urlShortenerLogMQ);
        if (dispatchFlag != null) {
            return dispatchFlag;
        }
        return false;
    }

    /**
     * 异步发送
     *
     * @param messageMQTopic
     * @param urlShortenerLogMQ
     * @return
     */
    private Boolean dispatchSMSMessage2QAsync(String messageMQTopic, UrlShortenerLogMQ urlShortenerLogMQ) {
        mqTemplate.sendAsync(messageMQTopic, JSONObject.toJSONString(urlShortenerLogMQ)).thenAccept(msgId -> {
                    log.info("sms send object {}, MsgID: {}", urlShortenerLogMQ, msgId);
                })
                .exceptionally((Function<Throwable, Void>) e -> {
                    log.error(e.getMessage(), e);
                    return null;
                });
        return Boolean.TRUE;
    }

}
