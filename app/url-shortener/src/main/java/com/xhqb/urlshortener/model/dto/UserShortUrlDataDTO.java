package com.xhqb.urlshortener.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
public class UserShortUrlDataDTO {

    /**
     * 手机号
     */
    @NotNull(message = "手机号不能为空")
    private String mobile;
    /**
     * 短链模版编码
     */
    private String tplCode;

    /**
     * cid
     */
    private String cid;


    /**
     * 策略code
     */
    private String strategyCode;

    /**
     * 渠道code
     */
    private String channelCode;

    /**
     * 自定义参数 key: 参数名称 value: 参数值
     */
    private Map<String, String> customizeParamMap;
}
