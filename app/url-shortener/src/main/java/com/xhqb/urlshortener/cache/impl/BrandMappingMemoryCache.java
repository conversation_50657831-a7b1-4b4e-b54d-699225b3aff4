package com.xhqb.urlshortener.cache.impl;

import com.xhqb.spectre.common.dal.entity.BrandMappingDO;
import com.xhqb.spectre.common.dal.mapper.BrandMappingMapper;
import com.xhqb.urlshortener.cache.AbstractMemoryCache;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class BrandMappingMemoryCache extends AbstractMemoryCache<BrandMappingDO> {

    @Resource
    private BrandMappingMapper brandMappingMapper;

    @Override
    protected List<BrandMappingDO> loadCache() {
        return brandMappingMapper.selectAll();
    }

    @Override
    protected String tableName() {
        return "t_brand_mapping";
    }


    public Map<String,String> getBrandMapping() {
        List<BrandMappingDO> brandMappingDOList = cache();
        Map<String,String> brandMappingMap = new HashMap<>();
        for (BrandMappingDO brandMappingDO : brandMappingDOList) {
            brandMappingMap.put(brandMappingDO.getCode(),brandMappingDO.getBrand());
        }
        return brandMappingMap;
    }

}
