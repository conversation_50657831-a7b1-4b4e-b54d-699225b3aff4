package com.xhqb.urlshortener.job;

import com.xhqb.spectre.common.dal.entity.ShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.ShortUrlMapper;
import com.xhqb.urlshortener.cache.BaseDataCache;
import com.xhqb.urlshortener.service.MemoryHandleService;
import com.xhqb.urlshortener.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;


@Component
@Slf4j
public class ApiMemoryCacheRefreshJob {

    @Resource
    private MemoryHandleService memoryHandleService;

    @Resource
    private ShortUrlMapper shortUrlMapper;

    @Scheduled(cron = "${cache.refresh.cron}")
    public void execute() {
        // 开始刷新数据到内存
        refreshShortUrl();
    }

    /**
     * 更新应用
     */
    private void refreshShortUrl() {
        ShortUrlDO shortUrlDO = shortUrlMapper.listByUpdateAndType();
        if (Objects.isNull(shortUrlDO)) {
            return;
        }
        BaseDataCache instance = BaseDataCache.getInstance();
        Date updateTime = instance.getUrlShortenerUpdateTime();
        if (Objects.nonNull(updateTime) && updateTime.equals(shortUrlDO.getUpdateTime())) {
            return;
        }
        instance.setUrlShortenerUpdateTime(shortUrlDO.getUpdateTime());
        memoryHandleService.refreshUrlShortener();
        log.info("shortUrl更新,时间{}", DateTimeUtil.queryDateToStr(shortUrlDO.getUpdateTime(), 4));
    }

}
