package com.xhqb.urlshortener.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xhqb.kael.util.json.JsonLogUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * description: admin服务统一返回结果
 * date: 2022-11-21
 *
 * @author: yjq
 */
@Data
public class ShortUrlResult<T> implements Serializable {

    /**
     * 处理成功的编码
     */
    private static final Integer SUCCESS_CODE = 0;

    /**
     * 响应码
     */
    private Integer code;
    /**
     * 响应消息
     */
    private String message;
    /**
     * 响应数据
     */
    private T data;

    public ShortUrlResult() {
        this.code = SUCCESS_CODE;
        this.message = "success";
    }

    public ShortUrlResult(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public ShortUrlResult(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }


    @JSONField(serialize = false)
    @JsonIgnore
    public boolean isSuccess() {
        return Objects.equals(SUCCESS_CODE, code);
    }

    @Override
    public String toString() {
        return JsonLogUtil.toJSONString(this);
    }

    public static <T> ShortUrlResult<T> make(Integer code, String msg, T data) {
        return new ShortUrlResult<>(code, msg, data);
    }

    public static <T> ShortUrlResult<T> success(T data) {
        return make(SUCCESS_CODE, "success", data);
    }
}
