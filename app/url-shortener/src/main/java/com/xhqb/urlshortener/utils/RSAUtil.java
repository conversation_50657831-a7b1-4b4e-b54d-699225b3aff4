package com.xhqb.urlshortener.utils;

import com.alibaba.fastjson.JSON;
import com.xhqb.kael.util.crypto.Base64Util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import java.security.Key;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class RSAUtil {
    private static final Logger logger = LoggerFactory.getLogger(RSAUtil.class);


    public static byte[] decryptPrivateKey(byte[] binaryData, String privateKey) throws Exception {
        byte[] keyBytes = Base64Util.decode(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);

        // 获取RSA算法实例
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key priKey = keyFactory.generatePrivate(keySpec);

        // 初始化加密器
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        return cipher.doFinal(binaryData);
    }

    /**
     * 使用公钥对数据进行加密。
     *
     * @param data      待加密的字符串数据。
     * @param publicKey 用于加密的公钥字符串，该公钥应为Base64编码。
     * @return 加密后的数据，以Base64编码的字符串形式返回。如果加密失败或发生异常，则返回空字符串。
     */
    public static String encrypt(String data, String publicKey) {
        try {
            // 将输入字符串转换为字节数组
            byte[] dataBytes = data.getBytes();
            // 调用encryptWithPublicKey方法使用公钥对字节数组进行加密
            byte[] resultBytes = encryptWithPublicKey(dataBytes, publicKey);
            // 如果加密成功且结果不为空
            if (resultBytes != null) {
                // 将加密后的字节数组转换为Base64编码的字符串并返回
                return Base64.getEncoder().encodeToString(resultBytes);
            }
        } catch (Exception e) {
            // 捕获并处理所有异常，记录错误信息
            logger.error("加密失败", e);
        }
        // 如果加密失败或发生异常，则返回空字符串
        return "";
    }

    // 加密方法
    public static byte[] encryptWithPublicKey(byte[] data, String publicKey) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(publicKey);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey pubKey = keyFactory.generatePublic(keySpec);
            Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
            cipher.init(Cipher.ENCRYPT_MODE, pubKey);
            return cipher.doFinal(data);
        } catch (Exception e) {
            logger.error("加密失败", e);
        }
        return null;
    }


//    public static void main(String[] args) {
//        try {
//            // 示例公钥和私钥（Base64编码）
//            String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCPNLUAeZbHgbKbREwLP/i0fRmfqc9bBS8zNloaFPC76noeFh1X1WcDpHe7TuE44+WbMxNmrZPQ8UtkBKoBgKSZDtH6DQbcKMoTUCSIz+tbKAuia0awgd/ICnnFc7kZ5m2q/B4XMh3evciHSyVD3i0/iCnLsnN23GHVN6TVJsM9aDmz/Li7lTKItd/6R+35H4Cko7NMT6JCMEQEq/W0WkIuNgHZCRBUzhvVQofSE3wvq5ISvuqvbmYsfn4Z8J0Q2tngSDpP/87ff3A6pfGqNc3xK7RUNWyOZs+2EfKs6p+1W7m7drtFDv3Go1+CIu3bOBGaHb42vUgNuhuDicMql0xHAgMBAAECggEAQc6SrpkxNJThIoCMAU9wVmB2eexqoQUM35fGZ93fkjr5ywGWklo6cG4Ppz9kN+RiCqO58qEpIAcCAgAyr2YSPVOWIjYCRQFpet6FK2mAWe9ZCVYKsem8kAxgUOAQ21oMY1pf6YDaQRUtJR7fDQT3/g3wRu7GKIS1YGmlkV2ysnCTyEQBhUwwsZOtkBzSb3jVbHaNdgADB73ah4qHDpO3Sf2ZmyDfFPf73G/c0MABkKWkL95b3loyWQCqDvTVImFCl+Hc41P0PzvXRsFDp+48FbiBUhk8+kiltmLPc75iO7WLzBCEkKsn8k6QaF86gLMSduVUxoBYe2hhR7qauaMZwQKBgQDl2vpvddu7HoSu4Ro27oazHZefDtA46pIEPHnF5L1QXkQuLlb73oXTm+8iCjUlIXck7att13rerlM8W1Q7iySV0lL8palPdRfmnm9qFv9EZL39VENJVsyn1Y/C55pquBejc/bT/YQcjFGCSA2zvqUUvFX2tbnxHqCnksOQUJNNjwKBgQCffqI/8dyBZXZ1/OcbAR3YMW5ClH718Sm9ThYc9u39OO9QadVAyOoxIhIjKneIYvppfmlPg1ZMePDq6DReVTR486L73JXhe+FyZhyIG+JqpCgnulKBtmGcV3FOWciopVbK0MMwET5wOimVOFFix0i8HCzwCRzq6YQK+dHXJYSpyQKBgQDSoASYmnFNWDvRXGh/Kbe47kb/870OSrSeuSQFGQP5X0gu4TvH9TLAo8VPdfcZHT9mUXujXa8z2p5fUnkvTuOpfl+ZL9yj+PJ723bQyl4DuCT4vHwj+ivzJ5liyoQPgr32g88MluS2nEYckhYsPWhcRfCn8AKBVsV0iOI3nf49NQKBgC3i+f0ZsY4MyesiKr9LL/HuLsG7BVfu3UpYd+Y3aLsMsVVaTX9JDo+6NKunYSJ9D3xgYR3+NErmFLIQwlhNfT47Ii4CBaEMygzmsdi65QY3WlIruecVuLmJMtEO5bsXboQFUdK3c1ZIdYQ34rpfiV6NYKRiis5PBq09uJCEe8l5AoGARzBX5RQq/r5AngZ58WYGL1wYbqCK+3XnfZiHkWFyhr5WTJBdta2tZHkAuWeWmDgKKldVsrOU+H9bxBQIDfQh1tgNYgh/OxBkbQflS46Rd/Mncbl55rhKfnAGAm/WuONl3hK40UIbYdRzBi98TSOXqAV4KkF5z3bGxkW9VhbT9Gw=";
//            String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjzS1AHmWx4Gym0RMCz/4tH0Zn6nPWwUvMzZaGhTwu+p6HhYdV9VnA6R3u07hOOPlmzMTZq2T0PFLZASqAYCkmQ7R+g0G3CjKE1AkiM/rWygLomtGsIHfyAp5xXO5GeZtqvweFzId3r3Ih0slQ94tP4gpy7Jzdtxh1Tek1SbDPWg5s/y4u5UyiLXf+kft+R+ApKOzTE+iQjBEBKv1tFpCLjYB2QkQVM4b1UKH0hN8L6uSEr7qr25mLH5+GfCdENrZ4Eg6T//O339wOqXxqjXN8Su0VDVsjmbPthHyrOqftVu5u3a7RQ79xqNfgiLt2zgRmh2+Nr1IDbobg4nDKpdMRwIDAQAB";
//
//            // 要加密的数据
//            String originalText = "19173492115";
//            byte[] dataToEncrypt = originalText.getBytes();
//
//            // 加密数据
//            byte[] encryptedData = encryptWithPublicKey(dataToEncrypt, publicKey);
//            System.out.println("加密后的数据: " + Base64.getEncoder().encodeToString(encryptedData));
//            System.out.println("加密后的数据长度: " + Base64.getEncoder().encodeToString(encryptedData).length());
//
//            // 解密数据
//            byte[] decryptedData = decryptPrivateKey(encryptedData, privateKey);
//            System.out.println("解密后的数据: " + new String(decryptedData));
//
//            String longUrl = "https://c.xiaohuaqb.com/xhactivity/template?timestamp=1734605601686&mobile=";
//            System.out.println("长链:" + longUrl + Base64.getEncoder().encodeToString(encryptedData));
//            System.out.println("长链长度:" + (longUrl + Base64.getEncoder().encodeToString(encryptedData)).length());
//        } catch (Exception e) {
//            logger.error("发生异常", e);
//        }
//    }
}
