package com.xhqb.urlshortener.constant;

import com.xhqb.kael.lucifer.telemetry.PrometheusCounterMetrics;
import com.xhqb.kael.lucifer.telemetry.PrometheusGaugeMetrics;
import com.xhqb.kael.lucifer.telemetry.PrometheusHistogramMetrics;
import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;
import io.prometheus.client.Histogram;

/**
 * 埋点常量
 *
 * <AUTHOR>
 * @date 2021/11/15
 */
public class LuciferConstant {

    private static final double[] METRIC_BUCKETS =
            new double[]{.005, .01, .025, .05, .075, .1, .25, .5, .75, 1, 2.5, 5, 7.5, 10, 15, 20, 30};

    /**
     * 正在处理的请求
     */
    public static final Gauge GAUGE = new PrometheusGaugeMetrics("spectre_url_shortener_gauge", "gauge description")
            .createWithLabels();

    /**
     * 接口请求次数
     */
    public static final Counter COUNTER = new PrometheusCounterMetrics("spectre_url_shortener_http_request_total", "description")
            .createWithLabels("status");

    /**
     * 接口处理耗时
     */
    public static final Histogram HISTOGRAM = new PrometheusHistogramMetrics("spectre_url_shortener_histogram", "histogram description", METRIC_BUCKETS)
            .createWithLabels("status");


    /**
     * zbx接口请求次数
     */
    public static final Counter ZBX_COUNTER = new PrometheusCounterMetrics("spectre_url_shortener_zbx_http_request_total", "description")
            .createWithLabels("status");

    /**
     * zbx接口处理耗时
     */
    public static final Histogram ZBX_HISTOGRAM = new PrometheusHistogramMetrics("spectre_url_shortener_zbx_histogram", "histogram description", METRIC_BUCKETS)
            .createWithLabels("status");


    /**
     * 用户短链创建接口处理耗时
     */
    public static final Histogram INNER_USER_SHORT_URL_CREATE_HISTOGRAM = new PrometheusHistogramMetrics("spectre_url_inner_user_short_url_create_histogram", "histogram description", METRIC_BUCKETS)
            .createWithLabels("status");
}
