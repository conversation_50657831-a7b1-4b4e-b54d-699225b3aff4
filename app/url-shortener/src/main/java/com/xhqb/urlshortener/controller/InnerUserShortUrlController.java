package com.xhqb.urlshortener.controller;

import com.xhqb.spectre.common.dal.query.ShortUrlLogQuery;
import com.xhqb.urlshortener.model.dto.BrandMappingDTO;
import com.xhqb.urlshortener.model.dto.UserShortUrlDTO;
import com.xhqb.urlshortener.model.result.ShortUrlResult;
import com.xhqb.urlshortener.model.vo.UserShortUrlVO;
import com.xhqb.urlshortener.service.UserShortUrlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生成用户短链（内部系统调用）
 *
 * <AUTHOR>
 * @date 2024/04/24
 */
@Slf4j
@RestController
@RequestMapping("/userShortUrl")
public class InnerUserShortUrlController {

    @Resource
    private UserShortUrlService userShortUrlService;


    /**
     * 生成用户短链
     *
     * @param userShortUrlDTO 用户信息
     * @return 短链列表
     */
    @PostMapping("/hold")
    public ShortUrlResult<List<UserShortUrlVO>> hold(@RequestBody UserShortUrlDTO userShortUrlDTO) {
        return ShortUrlResult.success(userShortUrlService.hold(userShortUrlDTO));
    }

    /**
     * 更新品牌 最近三十天 修复 Brand 数据
     *
     * @return 修复结果
     */
    @PostMapping("/updateBrand")
    public ShortUrlResult<String> updateBrand(@RequestBody ShortUrlLogQuery shortUrlLogQuery) {
        userShortUrlService.updateBrand(shortUrlLogQuery);
        return ShortUrlResult.success("success");
    }


    @PostMapping("/addBrand")
    public ShortUrlResult<String> addBrand(@RequestBody BrandMappingDTO brandMappingDTO) {
        userShortUrlService.addBrand(brandMappingDTO);
        return ShortUrlResult.success("success");
    }


}
