package com.xhqb.urlshortener.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSONObject;
import com.sensorsdata.analytics.javasdk.SensorsAnalytics;
import com.sensorsdata.analytics.javasdk.bean.EventRecord;
import com.xhqb.kael.util.Md5Utils;
import com.xhqb.spectre.common.dal.entity.ShortUrlDO;
import com.xhqb.spectre.common.dal.entity.UserShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.ShortUrlMapper;
import com.xhqb.spectre.common.dal.mapper.UserShortUrlDOMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 神策埋点上报服务
 * report -- 短链点击数据上报
 * <AUTHOR>
 */
@Service
@Slf4j
public class BurialPointReportBizService {

    @Resource
    private SensorsAnalytics sensorsAnalyticsV2;

    @Resource
    @Lazy
    private BurialPointReportBizService proxyService;

    @Resource
    private ShortUrlMapper shortUrlMapper;

    @Resource
    private UserShortUrlDOMapper userShortUrlDOMapper;

    /**
     * 短链域名
     */
    @Value("${short-url.domain:xh1.cn}")
    private String shortUrlDomain;

    /**
     * 上报开关
     */
    @Value("${short-url.report.enable:false}")
    private boolean reportEnable;


    @Async("burialPointExecutor")
    public void report(String shortCode) {
        long start = System.nanoTime();
        try {
            if(!reportEnable){
                return;
            }
            proxyService.doReport(shortCode);
        } catch (Exception e) {
            log.warn("report error:", e);
        }
        log.info("report time:{}", (System.nanoTime() - start) / 1000);
    }

    public void doReport(String shortCode) {
        if (Objects.equals(6, shortCode.length())) {
            proxyService.userReport(shortCode);
            return;
        }
        proxyService.commonReport(shortCode);
    }

    @SneakyThrows
    public void commonReport(String shortCode) {

        log.info("commonShortUrlReport shortCode:{}", shortCode);
        ShortUrlDO shortUrlDO = shortUrlMapper.selectByCode(shortCode);
        if (Objects.isNull(shortUrlDO)) {
            return;
        }

        EventRecord.Builder eventRecordBuilder = proxyService.buildEventRecord(null);
        EventRecord eventRecord = eventRecordBuilder.setEventName("SmsClick")
                .addProperty("url", buildShortUrl(shortCode))
                .addProperty("respUrl", shortUrlDO.getSrcUrl())
                .build();
        log.info("commonShortUrlReport eventRecord:{}", eventRecord);
        sensorsAnalyticsV2.track(eventRecord);
    }

    @SneakyThrows
    public void userReport(String shortCode) {

        log.info("userReport shortCode:{}", shortCode);
        UserShortUrlDO userShortUrlDO = userShortUrlDOMapper.selectByCode(shortCode);
        if (Objects.isNull(userShortUrlDO)) {
            return;
        }

        String cid = userShortUrlDO.getCid();

        if (StringUtils.isBlank(cid)) {
            EventRecord.Builder eventRecordBuilder = proxyService.buildEventRecord(null);
            EventRecord eventRecord = eventRecordBuilder
                    .setEventName("SmsClick")
                    .addProperty("url", userShortUrlDO.getShortUrl())
                    .addProperty("respUrl", userShortUrlDO.getLongUrl())
                    .addProperty("phone", userShortUrlDO.getMobile())
                    .build();
            log.info("userShortUrlNotWithCidReport eventRecord:{}", eventRecord);
            sensorsAnalyticsV2.track(eventRecord);
            return;
        }

        String cidMd5Hex = DigestUtil.md5Hex(cid);
        EventRecord.Builder eventRecordBuilder = proxyService.buildEventRecord(cidMd5Hex);
        String longUrl = userShortUrlDO.getLongUrl();
        EventRecord eventRecord = eventRecordBuilder
                .setEventName("SmsClick")
                .addProperty("url", userShortUrlDO.getShortUrl())
                .addProperty("respUrl", longUrl)
                .addProperty("cid", cidMd5Hex)
                .addProperty("phone", userShortUrlDO.getMobile())
                .addProperty("appChannel", extractFromUrl(longUrl, "appChannel"))
                .addProperty("adv_id", extractFromUrl(longUrl, "strategyCode"))
                .addProperty("description", allParamFormUrl(longUrl))
                .addProperty("failLoadTime", userShortUrlDO.getExpiredDate())
                .build();
        log.info("userShortUrlWithCidReport eventRecord:{}", eventRecord);
        sensorsAnalyticsV2.track(eventRecord);
    }

    @SneakyThrows
    public EventRecord.Builder buildEventRecord(String cid) {
        String cookieId = "ABCDEF123456789";

        if (StringUtils.isBlank(cid)) {
            return EventRecord.builder().setDistinctId(cookieId).isLoginId(Boolean.FALSE);
        }
        return EventRecord.builder().setDistinctId(cid).isLoginId(Boolean.TRUE);
    }


    public String allParamFormUrl(String url) {
        JSONObject jsonObject = new JSONObject();
        try {

            if (StringUtils.isEmpty(url)) {
                return "";
            }

            URI uri = new URI(url);
            String query = uri.getQuery();
            if (query != null) {
                Map<String, String> queryParams = Arrays.stream(query.split("&"))
                        .map(param -> param.split("="))
                        .collect(Collectors.toMap(param -> param[0], param -> param.length > 1 ? param[1] : ""));

                if (CollUtil.isNotEmpty(queryParams)) {
                    for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                        if (Objects.equals("appChannel", entry.getKey()) || Objects.equals("strategyCode", entry.getKey())) {
                            continue;
                        }
                        jsonObject.put(entry.getKey(), entry.getValue());
                    }
                }

            }
        } catch (URISyntaxException e) {
            log.error("Invalid URL: {}", url, e);
        }
        return jsonObject.toJSONString();

    }

    public String extractFromUrl(String url, String paramCode) {
        try {

            if (StringUtils.isEmpty(url)) {
                return "";
            }

            URI uri = new URI(url);
            String query = uri.getQuery();
            if (query != null) {
                Map<String, String> queryParams = Arrays.stream(query.split("&"))
                        .map(param -> param.split("="))
                        .collect(Collectors.toMap(param -> param[0], param -> param.length > 1 ? param[1] : ""));
                return queryParams.get(paramCode);
            }
        } catch (URISyntaxException e) {
            log.error("Invalid URL: {}", url, e);
        }
        return "";
    }


    private String buildShortUrl(String shortCode) {
        return shortUrlDomain + "/" + shortCode;
    }
}
