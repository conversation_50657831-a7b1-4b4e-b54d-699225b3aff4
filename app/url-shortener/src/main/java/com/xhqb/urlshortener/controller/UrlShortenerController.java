package com.xhqb.urlshortener.controller;

import com.xhqb.spectre.common.utils.CommonUtil;
import com.xhqb.urlshortener.constant.LuciferConstant;
import com.xhqb.urlshortener.service.UrlShortenerService;
import com.xhqb.urlshortener.service.ZbxUrlShortenerService;
import io.prometheus.client.Collector;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Optional;

@Slf4j
@RestController
@RequiredArgsConstructor
public class UrlShortenerController {
    private final UrlShortenerService urlShortenerService;

    private final ZbxUrlShortenerService zbxUrlShortenerService;

    @Value("${redirect.defaultUrl}")
    private String defaultUrl;

    @Value("${zbx.redirect.defaultUrl:https://www.zboxin.com}")
    private String zbxDefaultUrl;


    /**
     * 普通短链跳转
     *
     * @param code
     * @param request
     * @param response
     */
    @GetMapping("/{code}")
    public void redirectByCode(@PathVariable String code, HttpServletRequest request, HttpServletResponse response) {
        urlShortenerService.redirectByCode(code, request, response);
    }

    /**
     * 普通短链跳转
     *
     * @param code
     * @param request
     * @param response
     */
    @GetMapping("/zbx/{code}")
    public void redirectToOriginalUrl(@PathVariable String code,
                                      HttpServletRequest request,
                                      HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        long start = System.nanoTime();
        try {
            String originalUrl = Optional.ofNullable(zbxUrlShortenerService.getOriginalUrl(code))
                    .orElse(zbxDefaultUrl);

            stopWatch.stop();
            log.info("redirectToOriginalUrl: ip={}, code={}, url={}, ttl={}ms",
                    CommonUtil.getInetAddress(), code, originalUrl, stopWatch.getTotalTimeMillis());
            response.sendRedirect(originalUrl);
        } catch (IOException e) {
            log.error("zbx短链编码={}, getUrl ERROR :", code, e);
        } finally {
            LuciferConstant.ZBX_COUNTER.labels(String.valueOf(response.getStatus())).inc();
            LuciferConstant.ZBX_HISTOGRAM.labels(String.valueOf(response.getStatus()))
                    .observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
        }
    }


    /**
     * 默认页面
     *
     * @param response
     */
    @RequestMapping("/")
    public void index(HttpServletResponse response) {
        try {
            response.sendRedirect(defaultUrl);
        } catch (IOException e) {
            log.error("index ERROR :", e);
        }
    }

    /**
     * 用户短链跳转
     *
     * @param userCode
     * @param request
     * @param response
     */
    @GetMapping("/user/{code}")
    public void redirectByUserCode(@PathVariable("code") String userCode, HttpServletRequest request, HttpServletResponse response) {
        urlShortenerService.redirectByUserCode(userCode, request, response);
    }
}
