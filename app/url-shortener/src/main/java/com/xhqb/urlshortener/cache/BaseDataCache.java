package com.xhqb.urlshortener.cache;

import com.xhqb.spectre.common.dal.entity.ShortUrlDO;
import com.xhqb.urlshortener.model.UrlShortener;
import lombok.Getter;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @author: pengxiaoxi
 * @description:
 * @date: 2024-04-08 18:30
 */

public final class BaseDataCache {
    /**
     * 短链缓存 只缓存4位code
     */
    private Map<String, UrlShortener> urlShortenerMap = new ConcurrentHashMap<>(16);
    @Getter
    private Date urlShortenerUpdateTime;
    @Getter
    private byte[] ipData;

    public static BaseDataCache getInstance() {
        return BaseDataCacheHolder.INSTANCE;
    }

    public void refresh(List<ShortUrlDO> shortUrlDOList) {
        urlShortenerMap = Optional.ofNullable(shortUrlDOList)
                .map(list -> list.stream().collect(Collectors.toMap(ShortUrlDO::getShortCode, UrlShortener::buildUrlShortener, (old, now) -> now)))
                .orElseGet(ConcurrentHashMap::new);
    }

    static class BaseDataCacheHolder {
        private static final BaseDataCache INSTANCE = new BaseDataCache();
    }

    public UrlShortener get(String shortCode) {
        return urlShortenerMap.get(shortCode);
    }

    public void setUrlShortenerUpdateTime(Date urlShortenerUpdateTime) {
        this.urlShortenerUpdateTime = urlShortenerUpdateTime;
    }

    public void setIpData(byte[] ipData) {
        this.ipData = ipData;
    }

    private BaseDataCache() {
    }
}
