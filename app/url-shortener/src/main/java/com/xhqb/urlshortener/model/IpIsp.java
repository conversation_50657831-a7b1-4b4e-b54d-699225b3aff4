package com.xhqb.urlshortener.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.Optional;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class IpIsp {

    private String ip;

    private String province;

    private String city;

    private String isp;

    public static IpIsp buildIpIsp(String[] isps, String ip) {
        if (isps.length < 10) {
            return null;
        }
        return Optional.ofNullable(isps).map(item -> {
            IpIsp apiDO = IpIsp.builder()
                    .province(isps[2])
                    .city(isps[3])
                    .isp(isps[5])
                    .ip(ip)
                    .build();
            return apiDO;
        }).orElse(null);
    }

}
