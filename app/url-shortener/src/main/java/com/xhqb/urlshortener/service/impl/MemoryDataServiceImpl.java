package com.xhqb.urlshortener.service.impl;

import com.xhqb.spectre.common.dal.entity.ShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.ShortUrlMapper;
import com.xhqb.urlshortener.cache.BaseDataCache;
import com.xhqb.urlshortener.model.UrlShortener;
import com.xhqb.urlshortener.service.MemoryDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class MemoryDataServiceImpl implements MemoryDataService {
    @Autowired
    private ShortUrlMapper shortUrlMapper;

    /**
     * 从内存中获取数据
     *
     * @param code
     * @return
     */
    @Override
    public UrlShortener getUrlShortenerByCode(String code) {
        UrlShortener urlShortener = BaseDataCache.getInstance().get(code);
        if (urlShortener == null) {
            ShortUrlDO shortUrlDO = shortUrlMapper.selectByCode(code);
            if (shortUrlDO != null) {
                urlShortener = UrlShortener.buildUrlShortener(shortUrlDO);
            }
        }
        if (urlShortener != null && urlShortener.getExpiredDate().after(new Date())) {
            return urlShortener;
        }
        return null;
    }
}
