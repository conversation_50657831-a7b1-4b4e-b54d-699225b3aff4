package com.xhqb.urlshortener.model;

import com.xhqb.spectre.common.dal.entity.UserShortUrlDO;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.Optional;

@SuperBuilder
@Data
public class UserUrlShort {
    /**
     * 短链code
     */
    private String shortCode;

    /**
     * 长链url
     */
    private String longUrl;

    /**
     * 短链url
     */
    private String shortUrl;

    public static UserUrlShort buildUserUrlShort(UserShortUrlDO userShortUrlDO) {
        return Optional.ofNullable(userShortUrlDO).map(item -> UserUrlShort.builder()
                .shortCode(userShortUrlDO.getShortCode())
                .longUrl(userShortUrlDO.getLongUrl())
                .shortUrl(userShortUrlDO.getShortUrl())
                .build()).orElse(null);
    }
}
