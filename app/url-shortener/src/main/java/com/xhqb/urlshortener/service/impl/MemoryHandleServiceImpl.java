package com.xhqb.urlshortener.service.impl;

import com.xhqb.spectre.common.dal.entity.ShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.ShortUrlMapper;
import com.xhqb.urlshortener.cache.BaseDataCache;
import com.xhqb.urlshortener.service.MemoryHandleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;

@Service
@Slf4j
public class MemoryHandleServiceImpl implements MemoryHandleService {

    private static String IP_DAT_FILE_HTTP_PATH = "http://pkg.xhdev.xyz/download/zdata/ip/qqzeng-ip-utf8.dat";

    @Resource
    private ShortUrlMapper shortUrlMapper;

    @Override
    public void refreshUrlShortener() {
        List<ShortUrlDO> list = shortUrlMapper.listByType();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        BaseDataCache.getInstance().refresh(list);
    }

    @Override
    public void setIpData() {
        getHttpFile();
    }

    @PostConstruct
    private void initData() {
        log.info("启动时刷新短链数据");
        refreshUrlShortener();
        log.info("启动时获取ip数据");
        setIpData();
    }

    private void getHttpFile() {
        try {
            URL url = new URL(IP_DAT_FILE_HTTP_PATH);
            byte[] data = download(url);
            BaseDataCache.getInstance().setIpData(data);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private byte[] download(URL url) throws IOException {
        URLConnection uc = url.openConnection();
        int len = uc.getContentLength();
        InputStream is = new BufferedInputStream(uc.getInputStream());
        try {
            byte[] data = new byte[len];
            int offset = 0;
            while (offset < len) {
                int read = is.read(data, offset, data.length - offset);
                if (read < 0) {
                    break;
                }
                offset += read;
            }
            if (offset < len) {
                throw new IOException(
                        String.format("Read %d bytes; expected %d", offset, len));
            }
            return data;
        } finally {
            is.close();
        }
    }
}
