package com.xhqb.urlshortener.cache.impl;

import com.xhqb.spectre.common.dal.entity.ShortUrlTplDO;
import com.xhqb.spectre.common.dal.mapper.ShortUrlTplDOMapper;
import com.xhqb.urlshortener.cache.AbstractMemoryCache;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
public class ShortUrlTplMemoryCache extends AbstractMemoryCache<ShortUrlTplDO> {

    @Resource
    private ShortUrlTplDOMapper shortUrlTplDOMapper;

    @Override
    protected List<ShortUrlTplDO> loadCache() {
        return shortUrlTplDOMapper.selectByCurDate();
    }

    @Override
    protected String tableName() {
        return "t_short_url_tpl";
    }

    public ShortUrlTplDO selectByTplCode(String tplCode) {
        return super.cache().stream()
                .filter(tpl -> Objects.equals(tpl.getTplCode(), tplCode)).findFirst().orElse(null);
    }

}
