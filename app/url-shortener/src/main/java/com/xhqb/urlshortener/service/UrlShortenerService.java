package com.xhqb.urlshortener.service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @author: pengxiaoxi
 * @description:
 * @date: 2024-04-09 13:37
 */
public interface UrlShortenerService {
    /**
     * 普通短链进行跳转
     *
     * @param code
     * @param request
     * @param response
     */
    void redirectByCode(String code, HttpServletRequest request, HttpServletResponse response);

    /**
     * 用户短链编码进行转发
     *
     * @param userCode
     * @param request
     * @param response
     */
    void redirectByUserCode(String userCode, HttpServletRequest request, HttpServletResponse response);
}
