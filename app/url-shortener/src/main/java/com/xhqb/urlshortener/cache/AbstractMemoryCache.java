package com.xhqb.urlshortener.cache;

import com.xhqb.spectre.common.dal.mapper.CacheMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public abstract class AbstractMemoryCache<T> implements MemoryCache<T> {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String UPDATE_TIME_FIELD = "update_time";

    @Resource
    private CacheMapper cacheMapper;
    /**
     * 是否是debug模式
     */
    @Value("${memory.cache.refresh.debug:true}")
    private boolean debug;

    /**
     * 缓存的最新更新的时间
     */
    private volatile long lastUpdate;

    /**
     * 缓存列表
     */
    private List<T> cacheList;

    /**
     * 对数据进行缓存处理
     *
     * @param refresh 是否刷新
     * @return
     */
    @Override
    public List<T> cache(boolean... refresh) {
        List<T> result = this.doCache(refresh);
        if (Objects.isNull(result)) {
            return Collections.emptyList();
        } else {
            return result;
        }
    }

    private List<T> doCache(boolean... refresh) {
        // 强制刷新
        boolean forceRefresh = false;
        if (Objects.nonNull(refresh) && refresh.length > 0 && refresh[0]) {
            forceRefresh = true;
        }

        if (!forceRefresh && !CollectionUtils.isEmpty(this.cacheList)) {
            return this.cacheList;
        }

        if (this.expire()) {
            // 强制刷新
            this.cacheList = this.loadCache();
            return this.cacheList;
        }

        return this.cacheList;
    }

    /**
     * 缓存是否过期
     *
     * @return
     */
    private boolean expire() {
        String tableName = this.tableName();
        Date updateTime = cacheMapper.lastUpdateTime(tableName, this.getUpdateTimeField());
        // 更新时间为空 或者 更新时间大于当前缓存的最新时间 则表示当前缓存已经过期
        // 需要重新再进行数据刷新
        boolean isExpire = Objects.isNull(updateTime) || updateTime.getTime() > lastUpdate;
        if (isExpire) {
            // 刷新的时间
            long refreshTime = 0L;
            if (Objects.nonNull(updateTime)) {
                refreshTime = updateTime.getTime();
            }
            if (debug) {
                logger.info("tableName = {} 内存数据更新，数据库时间 = {},上次时间 = {}", tableName, refreshTime, lastUpdate);
            }
            // 过期了重新刷新缓存的最新时间
            this.lastUpdate = refreshTime;
        }
        return isExpire || CollectionUtils.isEmpty(cacheList);
    }

    /**
     * 获取表更新时间字段
     *
     * @return
     */
    protected String getUpdateTimeField() {
        return UPDATE_TIME_FIELD;
    }

    /**
     * 获取缓存数据
     *
     * @return
     */
    protected abstract List<T> loadCache();

    /**
     * 获取到缓存对应的表名称
     *
     * @return
     */
    protected abstract String tableName();
}
