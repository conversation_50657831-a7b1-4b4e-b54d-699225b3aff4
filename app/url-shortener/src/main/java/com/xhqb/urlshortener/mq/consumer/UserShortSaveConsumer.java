package com.xhqb.urlshortener.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.common.dal.entity.UserShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.UserShortUrlDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户短链code保存消费者
 *
 * <AUTHOR>
 * @date 2024/04/24
 */
@Slf4j
@Component
public class UserShortSaveConsumer {


    @Resource
    private UserShortUrlDOMapper userShortUrlDOMapper;

    @MQConsumer(topic = "#{'${kael.mq.consumers}'.split(',')[0]}", subscriptionType = SubscriptionType.Shared,
            clazz = String.class, receiverQueueSize = 1, ackTimeout = 60L)
    public void userShortSave(String message) {
        long start = System.currentTimeMillis();
        try {
            log.info("UserShortSaveConsumer start:{}", message);
            List<UserShortUrlDO> userShortUrlDOList = JSON.parseArray(message, UserShortUrlDO.class);
            userShortUrlDOMapper.batchInsert(userShortUrlDOList);
            log.info("UserShortSaveConsumer end:{}", System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.warn("用户短链导入处理信息数据消费mq异常|message={}|e:", message, e);
        }

    }
}



