package com.xhqb.urlshortener.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xhqb.spectre.common.dal.entity.ZbxShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.ZbxShortUrlMapper;
import com.xhqb.urlshortener.service.ZbxUrlShortenerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 中博信短链
 */
@Service
@Slf4j
public class ZbxUrlShortenerServiceImpl implements ZbxUrlShortenerService {

    @Autowired
    private ZbxShortUrlMapper zbxShortUrlMapper;
    /**
     * 本地缓存
     */
    private static final Cache<String, String> cache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterAccess(7, TimeUnit.DAYS)
            .recordStats() // 开启统计信息记录
            .build();

    /**
     * 获取短链地址
     * @param code
     * @return
     */
    @Override
    @Cacheable(value = "zbx-get-url", key = "#code", unless = "#result == null")
    public String getOriginalUrl(String code) {
        return Optional.ofNullable(cache.getIfPresent(code))
                .orElseGet(() -> {
                    String originalUrl = fetchOriginalUrlFromDB(code);
                    if (!StringUtils.isEmpty(originalUrl)) {
                        cache.put(code, originalUrl);
                        return originalUrl;
                    }
                    return null;
                });
    }


    /**
     * 从数据库中获取原始链接, 如果数据库中不存在或者已过期则返回null
     * @param code
     * @return
     */
    private String fetchOriginalUrlFromDB(String code) {
        log.info("fetchOriginalUrlFromDB: {}", code);
        return Optional.ofNullable(zbxShortUrlMapper.selectByCode(code))
                .filter(item -> item.getExpiredDate().after(new Date()))
                .map(ZbxShortUrlDO::getSrcUrl)
                .orElse(null);
    }
}
