package com.xhqb.urlshortener.service.impl;

import com.xhqb.spectre.common.mq.UrlShortenerLogMQ;
import com.xhqb.urlshortener.service.LogServerSendShortLog2Q;
import com.xhqb.urlshortener.service.SenderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/11/12
 * 发送mq消息service
 */
@Service
@Slf4j
public class SenderServiceImpl implements SenderService {

    @Resource
    private LogServerSendShortLog2Q logServerSendShortLog2Q;

    @Override
    public Boolean sendUrlShortenerMessage(UrlShortenerLogMQ urlShortenerLogMQ) {
        return logServerSendShortLog2Q.logServerShortLogMessage2Q(urlShortenerLogMQ);
    }
}
