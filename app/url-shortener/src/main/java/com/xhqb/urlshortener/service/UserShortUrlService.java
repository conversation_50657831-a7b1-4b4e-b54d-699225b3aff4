package com.xhqb.urlshortener.service;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.ShortUrlLogDO;
import com.xhqb.spectre.common.dal.entity.ShortUrlTplDO;
import com.xhqb.spectre.common.dal.entity.UserShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.ShortUrlLogMapper;
import com.xhqb.spectre.common.dal.mapper.ShortUrlTplDOMapper;
import com.xhqb.spectre.common.dal.query.ShortUrlLogQuery;
import com.xhqb.spectre.common.utils.DateUtil;
import com.xhqb.urlshortener.cache.impl.ShortUrlTplMemoryCache;
import com.xhqb.urlshortener.component.UserShortUrlBloomFilter;
import com.xhqb.urlshortener.constant.LuciferConstant;
import com.xhqb.urlshortener.model.dto.BrandMappingDTO;
import com.xhqb.urlshortener.model.dto.UserShortUrlDTO;
import com.xhqb.urlshortener.model.dto.UserShortUrlDataDTO;
import com.xhqb.urlshortener.model.vo.UserShortUrlVO;
import com.xhqb.urlshortener.mq.producer.ShortCodeSynProducer;
import com.xhqb.urlshortener.mq.producer.UserShortSaveProducer;
import com.xhqb.urlshortener.utils.AesUtil;
import com.xhqb.urlshortener.utils.ShortUrlGeneratorUtils;
import io.prometheus.client.Collector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import javax.annotation.Resource;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.*;

/**
 * 用户短链生成服务
 *
 * <AUTHOR>
 * @date 2024/04/24
 */
@Service
@Slf4j
public class UserShortUrlService {

    @Resource
    private ShortUrlTplMemoryCache shortUrlTplMemoryCache;
    @Resource
    private UserShortUrlBloomFilter bloomFilter;
    @Resource
    private UserShortSaveProducer userShortSaveProducer;
    @Resource
    private ShortCodeSynProducer shortCodeSynProducer;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private ShortUrlTplDOMapper shortUrlTplDOMapper;
    @Resource
    private ShortUrlLogMapper shortUrlLogMapper;
    @Resource
    private BrandMappingBizService brandMappingBizService;

    /**
     * 短链默认域名
     */
    @Value("${url.shortener.short.url:xh1.cn/}")
    private volatile String shortUrl;

    /**
     * 用户short和长链在redis缓存中保存天数
     */
    @Value("${url.shortener.key.expire.day:3}")
    private volatile int keyExpireDay;

    /**
     * short code 获取失败重试次数
     */
    @Value("${url.shortener.retry.num:3}")
    private volatile int retryNum;

    /**
     * 是否开启生成用户短链
     */
    @Value("${gen.user.short.code.enable:false}")
    private volatile boolean genUserShortCodeEnable;


    public List<UserShortUrlVO> hold(UserShortUrlDTO userShortUrlDTO) {
        long startTime = System.nanoTime();
        List<UserShortUrlVO> resultList = new ArrayList<>();
        String status = "success";
        try {
            resultList = doHold(userShortUrlDTO);
        } catch (Exception e) {
            log.warn("生成用户短链异常", e);
            status = "fail";
        } finally {
            int size = CollectionUtils.isEmpty(resultList) ? 0 : resultList.size();
            log.info("生成用户短链完成|size:{}|cost:{}", size, System.nanoTime() - startTime);
            // 请求时间
            LuciferConstant.INNER_USER_SHORT_URL_CREATE_HISTOGRAM.labels(status)
                    .observe((System.nanoTime() - startTime) / Collector.NANOSECONDS_PER_SECOND);
        }
        return resultList;
    }


    /**
     * 根据短链模版批量生成用户短链
     *
     * @param userShortUrlDTO 请求参数
     * @return {@link List}<{@link UserShortUrlVO}>
     */
    public List<UserShortUrlVO> doHold(UserShortUrlDTO userShortUrlDTO) {

        String inputTplCode = userShortUrlDTO.getTplCode();
        List<UserShortUrlDataDTO> inputDataList = userShortUrlDTO.getDataList();
        if (CollectionUtils.isEmpty(inputDataList)) {
            log.info("用户数据信息为空|tplCode:{}|batchId:{}", inputTplCode, userShortUrlDTO.getBatchId());
            return Collections.emptyList();
        }

        ShortUrlTplDO shortUrlTplDO = this.getShortUrlTplIfPresent(userShortUrlDTO);
        if (Objects.isNull(shortUrlTplDO)) {
            return Collections.emptyList();
        }

        Date expiredDate = null;
        try {
            expiredDate = DateUtils.parseDate(shortUrlTplDO.getExpiredDate(), DateUtil.DATE_FORMAT_DATETIME);
        } catch (ParseException e) {
            log.warn("时间转换异常|expiredDate:{}|e:", shortUrlTplDO.getExpiredDate(), e);
        }

        if (Objects.nonNull(expiredDate) && new Date().after(expiredDate)) {
            log.info("短链模版已过期|tplCode:{}|expiredDate:{}", inputTplCode, shortUrlTplDO.getExpiredDate());
            return Collections.emptyList();
        }

        if (genUserShortCodeEnable) {
            return this.useGenBuildUserShortUrlList(userShortUrlDTO, inputDataList, shortUrlTplDO);
        } else {
            return this.useDefaultBuildUserShortUrlList(inputDataList, shortUrlTplDO);
        }

    }


    List<UserShortUrlVO> useGenBuildUserShortUrlList(UserShortUrlDTO userShortUrlDTO, List<UserShortUrlDataDTO> inputDataList, ShortUrlTplDO shortUrlTplConfig) {
        // 1. 生成短链
        List<UserShortUrlVO> userShortUrlVOList = new ArrayList<>();
        List<UserShortUrlDO> userShortUrlDOList = new ArrayList<>();
        for (UserShortUrlDataDTO shortUrlDataDTO : inputDataList) {
            UserShortUrlVO userShortUrlVO = createUserShortUrlVO(shortUrlTplConfig, shortUrlDataDTO.getMobile());
            UserShortUrlDO userShortUrlDO = new UserShortUrlDO();
            userShortUrlDO.setBatchId(userShortUrlDTO.getBatchId());
            // longUrl
            String longUrl = getLongUrlV2(shortUrlTplConfig, shortUrlDataDTO);
            userShortUrlDO.setLongUrl(longUrl);
            userShortUrlDO.setTplCode(shortUrlTplConfig.getTplCode());
            userShortUrlDO.setMobile(shortUrlDataDTO.getMobile());
            userShortUrlDO.setExpiredDate(shortUrlTplConfig.getExpiredDate());
            userShortUrlDO.setShortCode(userShortUrlVO.getShortCode());
            userShortUrlDO.setShortUrl(userShortUrlVO.getShortUrl());
            userShortUrlDO.setBatchId(userShortUrlDTO.getBatchId() != null ? userShortUrlDTO.getBatchId() : "");
            userShortUrlDO.setCid(ObjectUtils.isEmpty(shortUrlDataDTO.getCid()) ? "" : shortUrlDataDTO.getCid());
            userShortUrlVOList.add(userShortUrlVO);
            userShortUrlDOList.add(userShortUrlDO);
        }

        // 2. 同步批量保存数据到Redis
        Map<String, String> keysAndValues = new HashMap<>(userShortUrlDOList.size());
        for (UserShortUrlDO userShortUr : userShortUrlDOList) {
            keysAndValues.put(RedisKeys.USER_SHORT_URL_PRE_KEY + userShortUr.getShortCode(), userShortUr.getLongUrl());
        }
        // keyExpireDay -> keyExpireSeconds
        int keyExpireSeconds = keyExpireDay * 86400 + RandomUtil.randomInt(60, 600);
        this.batchWriteWithPipeline(keysAndValues, keyExpireSeconds);

        // 3. 异步保存到db
        userShortSaveProducer.doSendResp(userShortUrlDOList);

        // 4. 短链数据同步到其他实例
        shortCodeSynProducer.send(userShortUrlDOList);

        return userShortUrlVOList;
    }


    List<UserShortUrlVO> useDefaultBuildUserShortUrlList(List<UserShortUrlDataDTO> inputDataList, ShortUrlTplDO shortUrlTplConfig) {
        List<UserShortUrlVO> voList = new ArrayList<>();
        for (UserShortUrlDataDTO userShortUrlData : inputDataList) {
            UserShortUrlVO vo = new UserShortUrlVO();
            vo.setShortUrl(shortUrl + shortUrlTplConfig.getShortCode());
            vo.setMobile(userShortUrlData.getMobile());
            vo.setShortCode(shortUrlTplConfig.getShortCode());
            voList.add(vo);
        }
        return voList;
    }


    ShortUrlTplDO getShortUrlTplIfPresent(UserShortUrlDTO userShortUrlDTO) {
        String inputTplCode = userShortUrlDTO.getTplCode();
        ShortUrlTplDO shortUrlTplDO = shortUrlTplMemoryCache.selectByTplCode(inputTplCode);
        if (Objects.isNull(shortUrlTplDO)) {
            ShortUrlTplDO dbModel = shortUrlTplDOMapper.selectByTplCode(inputTplCode);
            if (Objects.isNull(dbModel)) {
                log.info("短链模版不存在|tplCode:{}", inputTplCode);
                return null;
            }
            shortUrlTplDO = dbModel;
        }

        if (StringUtils.isBlank(shortUrlTplDO.getLongUrl())) {
            log.info("长链信息配置为空|tplCode:{}", inputTplCode);
            return null;
        }

        if (StringUtils.isBlank(shortUrlTplDO.getShortCode())) {
            log.info("shortcode配置为空|tplCode:{}", inputTplCode);
            return null;
        }

        return shortUrlTplDO;
    }


    UserShortUrlVO createUserShortUrlVO(ShortUrlTplDO shortUrlTplDO, String mobile) {
        String shortCode = this.getUserShortCodeSupportRetry(shortUrlTplDO.getLongUrl());
        if (StringUtils.isBlank(shortCode)) {
            shortCode = shortUrlTplDO.getShortCode();
        }
        UserShortUrlVO userShortUrlVO = new UserShortUrlVO();
        userShortUrlVO.setShortUrl(shortUrl + shortCode);
        userShortUrlVO.setMobile(mobile);
        userShortUrlVO.setShortCode(shortCode);
        return userShortUrlVO;
    }

    /**
     * 根据长链获取对应shortCode
     *
     * @param longUrl 长链
     * @return {@link String}
     */
    String getUserShortCodeSupportRetry(String longUrl) {
        String shortCode = "";
        for (int i = 0; i < this.retryNum; i++) {
            String[] shortCodeArr = ShortUrlGeneratorUtils.getHashParam(longUrl);
            if (Objects.nonNull(shortCodeArr)) {
                for (String item : shortCodeArr) {
                    if (bloomFilter.contains(item)) {
                        log.info("生成的短链无效|shortCode:{}", item);
                        continue;
                    }
                    bloomFilter.put(item);
                    return item;
                }
            }
        }
        return shortCode;
    }

    /**
     * 使用Pipeline进行批量写入
     *
     * @param keysAndValues 待写入的数据
     * @param timeout       缓存时间
     */
    void batchWriteWithPipeline(Map<String, String> keysAndValues, long timeout) {
        redisTemplate.executePipelined((RedisCallback<String>) connection -> {
            for (Map.Entry<String, String> entry : keysAndValues.entrySet()) {
                byte[] key = redisTemplate.getStringSerializer().serialize(entry.getKey());
                byte[] value = redisTemplate.getStringSerializer().serialize(entry.getValue());
                connection.setEx(key, timeout, value);
            }
            return null;
        });
    }

    String getLongUrlV2(ShortUrlTplDO shortUrlTplConfig, UserShortUrlDataDTO userShortUrlDataDTO) {
        String longUrl = shortUrlTplConfig.getLongUrl();
        Map<String, String> mobileParams = new LinkedHashMap<>();
        if (Objects.equals(1, shortUrlTplConfig.getIsEncrypt())) {
            // 使用AES加密算法对手机号进行加密
            String encryptMobile = AesUtil.encryptFromString(userShortUrlDataDTO.getMobile(), Mode.CBC, Padding.ZeroPadding);
            mobileParams.put("phone", encryptMobile);
            mobileParams.put("decode", "true");
        }
        Map<String, String> params = getLongUrlParams(userShortUrlDataDTO);
        params.putAll(mobileParams);
        longUrl = appendQueryParams(longUrl, params);
        return longUrl;
    }

    Map<String, String> getLongUrlParams(UserShortUrlDataDTO userShortUrlDataDTO) {
        Map<String, String> params = new HashMap<>();
        if (Objects.nonNull(userShortUrlDataDTO.getCid())) {
            params.put("cid", userShortUrlDataDTO.getCid());
        }
        if (Objects.nonNull(userShortUrlDataDTO.getStrategyCode())) {
            params.put("strategyCode", userShortUrlDataDTO.getStrategyCode());
        }
        if (Objects.nonNull(userShortUrlDataDTO.getChannelCode())) {
            params.put("appChannel", userShortUrlDataDTO.getChannelCode());
        }
        if (Objects.nonNull(userShortUrlDataDTO.getCustomizeParamMap())) {
            params.putAll(userShortUrlDataDTO.getCustomizeParamMap());
        }
        return params;
    }


    /**
     * 向给定的URL追加查询参数。
     *
     * @param url    待追加参数的原始URL字符串。
     * @param params 要追加到URL中的参数映射，其中键为参数名，值为参数值。
     * @return 追加参数后的新URL字符串。如果发生错误，则返回原始URL。
     * @throws IllegalArgumentException 如果提供的URL格式不正确或无法解析，则抛出此异常。
     */
    public static String appendQueryParams(String url, Map<String, String> params) {
        try {
            URI uri = new URI(url);
            StringBuilder queryString = new StringBuilder();

            // 如果URL已经有查询参数，先解析它们
            Map<String, String> existingParams = new HashMap<>();
            if (uri.getQuery() != null) {
                for (String param : uri.getQuery().split("&")) {
                    String[] parts = param.split("=", 2);
                    if (parts.length == 2) {
                        existingParams.put(parts[0], parts[1]);
                    }
                }
            }

            // 合并新旧参数
            existingParams.putAll(params);

            // 构建新的查询字符串
            for (Map.Entry<String, String> entry : existingParams.entrySet()) {
                if (queryString.length() > 0) {
                    queryString.append("&");
                }
                queryString.append(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8.name()))
                        .append("=")
                        .append(entry.getValue());
            }

            // 构建新的URL
            return new URI(uri.getScheme(), uri.getUserInfo(), uri.getHost(), uri.getPort(), uri.getPath(), queryString.toString(), null).toString();
        } catch (Exception e) {
            log.error("拼接URL参数失败", e);
        }
        return url;
    }

    public void updateBrand(ShortUrlLogQuery baseQuery) {
        try {
            // 1. 解析时间范围
            Date startDate;
            Date endDate;
            try {
                startDate = DateUtils.parseDate(baseQuery.getStartClickTime(), "yyyy-MM-dd HH:mm:ss");
                endDate = DateUtils.parseDate(baseQuery.getEndClickTime(), "yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                log.warn("时间解析失败");
                return;
            }

            // 2. 按天循环处理
            Date currentDay = DateUtils.truncate(startDate, Calendar.DATE);
            while (currentDay.before(endDate)) {
                Date dayEnd = DateUtils.addSeconds(
                        DateUtils.addDays(DateUtils.truncate(currentDay, Calendar.DATE), 1),
                        -1
                );

                // 3. 构建每日查询条件
                ShortUrlLogQuery dailyQuery = new ShortUrlLogQuery();
                BeanUtils.copyProperties(baseQuery, dailyQuery);
                dailyQuery.setStartClickTime(DateFormatUtils.format(currentDay, "yyyy-MM-dd HH:mm:ss"));
                dailyQuery.setEndClickTime(DateFormatUtils.format(dayEnd, "yyyy-MM-dd HH:mm:ss"));

                // 4. 处理当天数据
                try {
                    doUpdateBrand(dailyQuery);
                    log.info("成功处理日期: {}", DateFormatUtils.format(currentDay, "yyyy-MM-dd"));
                } catch (Exception e) {
                    log.error("处理日期 {} 失败", DateFormatUtils.format(currentDay, "yyyy-MM-dd"), e);
                }

                // 5. 移动到下一天
                currentDay = DateUtils.addDays(currentDay, 1);
            }
        } catch (Exception e) {
            log.error("更新品牌失败", e);
        }
    }

    public void doUpdateBrand(ShortUrlLogQuery shortUrlLogQuery) {
        List<ShortUrlLogDO> shortUrlLogDOList = shortUrlLogMapper.selectByQuery(shortUrlLogQuery);
        if (CollectionUtils.isEmpty(shortUrlLogDOList)) {
            return;
        }
        // 批量处理用户代理解析
        shortUrlLogDOList.forEach(brandMappingBizService::userAgentParser);
        // 批量更新
        shortUrlLogDOList.forEach(shortUrlLogDO -> {
            shortUrlLogMapper.updateByPrimaryKeySelective(shortUrlLogDO);
        });
    }

    public void addBrand(BrandMappingDTO brandMappingDTO) {
        try {
            brandMappingBizService.addBrand(brandMappingDTO);
        } catch (Exception e) {
            log.warn("添加品牌失败", e);
        }

    }
}
