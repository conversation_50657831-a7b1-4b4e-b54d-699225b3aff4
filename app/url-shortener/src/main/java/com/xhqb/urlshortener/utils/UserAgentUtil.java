package com.xhqb.urlshortener.utils;

import com.blueconic.browscap.Capabilities;
import com.blueconic.browscap.ParseException;
import com.blueconic.browscap.UserAgentParser;
import com.blueconic.browscap.UserAgentService;
import com.xhqb.kael.util.web.WebUtils;
import com.xhqb.spectre.common.dal.entity.ShortUrlLogDO;
import com.xhqb.urlshortener.constant.ShortenerConstant;
import com.xhqb.urlshortener.model.IpIsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class UserAgentUtil {

    static UserAgentParser parser = null;

    // 初始化uasParser对象
    static {
        try {
            parser = new UserAgentService().loadParser(); // handle IOException and ParseException
        } catch (IOException | ParseException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取user-agent
     *
     * @param request
     * @return
     */
    public static ShortUrlLogDO getUserAgent(HttpServletRequest request, String shortCode) {
        try {
            ShortUrlLogDO shortUrlLogDO = new ShortUrlLogDO();
            shortUrlLogDO.setShortCode(shortCode);
            shortUrlLogDO.setClickTime(DateTimeUtil.getCurrentTime());

            // ua
            String userAgentString = request.getHeader("user-agent");
            if (StringUtils.isEmpty(userAgentString) || userAgentString.length() > 1024) {
                return null;
            }
            shortUrlLogDO.setUserAgent(userAgentString);

            Capabilities capabilities = parser.parse(userAgentString);
            if (capabilities == null) {
                return null;
            }
            // os
            String os = capabilities.getPlatform();
            String osVersion = capabilities.getPlatformVersion();
            if (os.length() > 32 || osVersion.length() > 32) {
                return null;
            }
            shortUrlLogDO.setOs(os);
            shortUrlLogDO.setOsVersion(osVersion);

            // 请求ip
            String ip = WebUtils.getIpAddr(request);
            IpIsp ipIsp = IpSearch.getIpIsp(ip);
            if (ipIsp != null) {
                shortUrlLogDO.setProvince(ipIsp.getProvince());
                shortUrlLogDO.setCity(ipIsp.getCity());
                shortUrlLogDO.setIsp(ipIsp.getIsp());
            }
            shortUrlLogDO.setIp(ip);

            // 设备平台
            String platform = capabilities.getDeviceType();
            if (platform.length() > 64) {
                return null;
            }
            shortUrlLogDO.setPlatform(platform);

            // 来源
            String referrer = getReferer(request);
            if (referrer.length() > 2048) {
                return null;
            }
            shortUrlLogDO.setReferrer(referrer);

            // 设备型号，设备品牌
            String model = getMobileInfo(userAgentString);
            if (model.length() > 512) {
                return null;
            }
            shortUrlLogDO.setModel(model);
            shortUrlLogDO.setBrand(!StringUtils.isBlank(model) ? model.split(" ")[0] : "");

            return shortUrlLogDO;
        } catch (Exception e) {
            log.warn("getUserAgent exception", e);
            return null;
        }
    }

    /**
     * 获取来源
     *
     * @param request
     * @return
     */
    private static String getReferer(HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        if (StringUtils.isBlank(referer)) {
            referer = request.getHeader("referer");
        }
        return StringUtils.isBlank(referer) ? "" : referer;
    }

    /**
     * 解析平台类型
     *
     * @param deviceType
     * @param agentString
     * @return
     */
    private static String getDeviceType(String deviceType, String agentString) {
        switch (deviceType) {
            case ShortenerConstant.DEVICE_TYPE_COMPUTER:
                return "PC";
            case ShortenerConstant.DEVICE_TYPE_TABLET: {
                if (agentString.contains("Android")) return "Android Pad";
                if (agentString.contains("iOS")) return "iPad";
                return "Unknown";
            }
            case ShortenerConstant.DEVICE_TYPE_MOBILE: {
                if (agentString.contains("Android")) return "Android";
                if (agentString.contains("iOS")) return "IOS";
                return "Unknown";
            }
            default:
                return "Unknown";
        }
    }

    public static String getMobileInfo(String userAgent) {
        if (StringUtils.isEmpty(userAgent)) {
            return "";
        }
        Pattern pattern = Pattern.compile(";\\s?(\\S*?\\s?\\S*?)\\s?(Build)?/");
        Matcher matcher = pattern.matcher(userAgent);
        String model = "";
        if (matcher.find()) {
            model = matcher.group(1).trim();
            model = model.replace("U;", "");
            model = model.replace("zh-cn;", "");
            log.info("通过userAgent解析出机型：" + model.trim());
        }
        model = model.trim();
        //model长度超过500时，进行截断，防止入库出错
        if (model.length() > 500) {
            model = model.substring(0, 500);
        }
        return model;
    }

    /**
     * 获取ip
     *
     * @param request
     * @return
     */
    private static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("http_client_ip");
                log.error("http_client_ip:" + ip);
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
                log.error("getRemoteAddr:" + ip);
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
                log.error("Proxy-Client-IP:" + ip);
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
                log.error("WL-Proxy-Client-IP:" + ip);
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
                log.error("HTTP_X_FORWARDED_FOR:" + ip);
            }
            // 如果是多级代理，那么取第一个ip为客户ip
            if (ip != null && ip.indexOf(",") != -1) {
                ip = ip.substring(ip.lastIndexOf(",") + 1, ip.length()).trim();
                log.error("ip:" + ip);
            }
            return ip;
        }
        return null;
    }
}
