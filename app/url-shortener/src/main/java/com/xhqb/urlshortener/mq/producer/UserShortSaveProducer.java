package com.xhqb.urlshortener.mq.producer;

import com.alibaba.fastjson.JSON;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.common.dal.entity.UserShortUrlDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.pulsar.client.api.TypedMessageBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 用户短链异步保存消费者
 *
 * <AUTHOR>
 * @date 2024/04/24
 */
@Component
@Slf4j
public class UserShortSaveProducer {


    /**
     * 用户短链保存
     * topic = url-shortener-save
     * 该topic由UserShortSaveConsumer消费
     */
    @Value("#{'${kael.mq.producers}'.split(',')[1]}")
    private volatile String urlShortenerSaveTopic;

    @Resource
    private MQTemplate<String> mqTemplate;

    public void doSendResp(List<UserShortUrlDO> userShortUrlDOList) {
        if (CollectionUtils.isEmpty(userShortUrlDOList)) {
            return;
        }
        long start = System.currentTimeMillis();
        String message = JSON.toJSONString(userShortUrlDOList);
        log.info("用户短链导入处理信息 = {}", message);
        TypedMessageBuilder<String> mqTemplateMessage = mqTemplate.createMessage(urlShortenerSaveTopic, message);

        if (Objects.nonNull(mqTemplateMessage)) {
            // 立即发送
            mqTemplateMessage.sendAsync();
            log.info("用户短链导入处理信息响应完成,cost = {}", (System.currentTimeMillis() - start));
        } else {
            log.warn("响应用户短链导入处理信息消息失败");
        }

    }
}
