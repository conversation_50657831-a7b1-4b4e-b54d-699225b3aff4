package com.xhqb.urlshortener.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.common.dal.entity.ShortUrlClickCountDO;
import com.xhqb.spectre.common.dal.entity.ShortUrlLogDO;
import com.xhqb.spectre.common.dal.entity.ShortUrlUpdateIndexDO;
import com.xhqb.spectre.common.dal.entity.UserShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: huangyanxiong
 * @Date: 2023/12/4 14:39
 * @Description: 增量更新短链点击次数
 */
@Component
@Job("updateClickCountJob")
@Slf4j
public class UpdateClickCountJob implements SimpleJob {

    @Resource
    private ShortUrlUpdateIndexMapper shortUrlUpdateIndexMapper;

    @Resource
    private ShortUrlMapper shortUrlMapper;

    @Resource
    private ShortUrlLogMapper shortUrlLogMapper;

    @Resource
    private ShortUrlTplDOMapper shortUrlTplDOMapper;

    @Resource
    private UserShortUrlDOMapper userShortUrlDOMapper;


    /**
     * 更新短链点击次数，暂定每5分钟执行一次
     *
     * @param shardingContext 分片上下文
     */
    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("更新短链点击次数任务开始");
        updateClickCount();
        log.info("更新短链点击次数任务结束");
    }

    private void updateClickCount() {
        //获取上一次更新时间
        ShortUrlUpdateIndexDO updateIndexDO = shortUrlUpdateIndexMapper.selectOne();
        if (updateIndexDO == null) {
            log.warn("未获取到短链点击次数的上一次更新时间");
            return;
        }
        Date lastUpdateTime = updateIndexDO.getLastUpdateTime();

        //查询短链点击次数
        ShortUrlLogDO lastShortUrlLogDO = shortUrlLogMapper.selectLastItem(lastUpdateTime);
        if (lastShortUrlLogDO == null) {
            log.info("新的短链点击记录为空，无需更新");
            return;
        }
        Date thisUpdateTime = lastShortUrlLogDO.getCreateTime();
        List<ShortUrlClickCountDO> list = shortUrlLogMapper.sumClickCount(lastUpdateTime, thisUpdateTime);
        if (CollectionUtils.isEmpty(list)) {
            log.info("新的短链点击记录为空，无需更新");
            return;
        }

        //更新短链点击次数
        for (ShortUrlClickCountDO item : list) {
            String shortCode = item.getShortCode();
            Integer clickCount = item.getClickCount();
            if (StringUtils.isBlank(shortCode) || clickCount == null || clickCount <= 0) {
                continue;
            }
            shortUrlMapper.updateClickCount(shortCode, clickCount);
        }

        // 更新短链模板点击次数
        updateShortUrlTplClickCount(list);

        //更新短链点击次数的更新时间
        ShortUrlUpdateIndexDO newUpdateIndexDO = ShortUrlUpdateIndexDO.builder()
                .id(updateIndexDO.getId())
                .lastUpdateTime(thisUpdateTime)
                .build();
        shortUrlUpdateIndexMapper.updateByPrimaryKeySelective(newUpdateIndexDO);
    }

    /**
     * 更新短链接模板的点击次数。
     *
     * @param clickCountDOList 包含点击次数的短链接点击计数数据对象列表。
     * <p>
     * 方法内部逻辑：
     * 1. 首先，通过流操作将点击计数数据转换为以为短链编码键、点击次数为值的映射（Map），
     *    若存在相同短链编码的多条记录，则点击次数累加。
     * 2. 提取上述映射中的所有短链编码，并查询对应的用户短链接数据。
     * 3. 若未查询到任何用户短链接数据，则直接返回。
     * 4. 遍历用户短链接数据，根据其模板代码和短链编码，更新模板代码到点击次数的映射。
     * 5. 遍历更新后的映射，通过模板代码更新对应短链接模板的点击次数。
     *
     */
    private void updateShortUrlTplClickCount(List<ShortUrlClickCountDO> clickCountDOList) {
        try {
            Map<String, Integer> shortCodeToClickCountMap = clickCountDOList.stream()
                    .collect(Collectors.toMap(ShortUrlClickCountDO::getShortCode, ShortUrlClickCountDO::getClickCount, Integer::sum));

            List<String> shortCodes = new ArrayList<>(shortCodeToClickCountMap.keySet());
            List<UserShortUrlDO> userShortUrlDOList = userShortUrlDOMapper.selectByShortCodes(shortCodes);
            if (CollectionUtils.isEmpty(userShortUrlDOList)) {
                return;
            }

            Map<String, Integer> tplCodeToClickCountMap = new HashMap<>();
            for (UserShortUrlDO userShortUrlDO : userShortUrlDOList) {
                String tplCode = userShortUrlDO.getTplCode();
                String shortCode = userShortUrlDO.getShortCode();
                Integer clickCount = shortCodeToClickCountMap.getOrDefault(shortCode, 0);
                tplCodeToClickCountMap.merge(tplCode, clickCount, Integer::sum);
            }

            for (Map.Entry<String, Integer> entry : tplCodeToClickCountMap.entrySet()) {
                shortUrlTplDOMapper.updateClickCountByTplCode(entry.getKey(), entry.getValue());
            }
        } catch (Exception e) {
            log.error("更新短链模板点击次数失败", e);
        }
    }

}
