package com.xhqb.urlshortener.config;

import com.sensorsdata.analytics.javasdk.ISensorsAnalytics;
import com.sensorsdata.analytics.javasdk.SensorsAnalytics;
import com.sensorsdata.analytics.javasdk.bean.FailedData;
import com.sensorsdata.analytics.javasdk.bean.SuperPropertiesRecord;
import com.sensorsdata.analytics.javasdk.consumer.Callback;
import com.sensorsdata.analytics.javasdk.consumer.FastBatchConsumer;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 神策埋点配置
 */
@Configuration
public class SensorsReportConfig {
    @Value("${sensors.report.url}")
    private String reportUrl;
    @Value("${sensors.report.bulkSize}")
    private int bulkSize;
    @Value("${sensors.report.maxCacheSize}")
    private int maxCacheSize;
    @Value("${sensors.report.timeoutSec}")
    private int timeoutSec;
    @Value("${sensors.report.flushSec}")
    private int flushSec;
    @Value("${sensors.report.timing}")
    private boolean timing;

    @SneakyThrows
    @Bean(name = "sensorsAnalyticsV2")
    public ISensorsAnalytics sensorsAnalytics() {
        ISensorsAnalytics sensorsAnalytics = new SensorsAnalytics(new FastBatchConsumer(reportUrl, timing, bulkSize, maxCacheSize, flushSec, timeoutSec, new Callback() {
            @Override
            public void onFailed(FailedData failedData) {
                //可收集发送失败的数据, 日志中把失败原因加上 failedData.getFailedMessage()
            }
        }));
        // 公共参数
        SuperPropertiesRecord propertiesRecord = SuperPropertiesRecord.builder()
                .addProperty("serverName", "spectre-url-shortener")
                .build();
        // 公共参数注册
        sensorsAnalytics.registerSuperProperties(propertiesRecord);
        return sensorsAnalytics;
    }
}
