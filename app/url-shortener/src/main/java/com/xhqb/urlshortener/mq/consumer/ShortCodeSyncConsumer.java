package com.xhqb.urlshortener.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.urlshortener.component.UserShortUrlBloomFilter;
import com.xhqb.urlshortener.model.dto.ShortCodeSynDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 短链code同步消费者
 *
 * <AUTHOR>
 * @date 2024/04/24
 */
@Slf4j
@Component
public class ShortCodeSyncConsumer {


    @Resource
    private UserShortUrlBloomFilter userShortUrlBloomFilter;

    @MQConsumer(topic = "#{'${kael.mq.consumers}'.split(',')[1]}", subscriptionType = SubscriptionType.Shared,
            clazz = String.class, receiverQueueSize = 1, ackTimeout = 60L, broadcast = true)
    public void consumer(String message) {
        long start = System.currentTimeMillis();
        log.info("ShortUrlBloomFilterConsumer start:{}", message);
        try {
            List<ShortCodeSynDTO> shortCodeSynList = JSON.parseArray(message, ShortCodeSynDTO.class);
            for (ShortCodeSynDTO shortCodeSyn : shortCodeSynList) {
                userShortUrlBloomFilter.put(shortCodeSyn.getShortCode());
            }
            log.info("ShortUrlBloomFilterConsumer end:{}", System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.warn("布滤器同步节点更新消息消费mq异常|exception:", e);
        }
    }

}
