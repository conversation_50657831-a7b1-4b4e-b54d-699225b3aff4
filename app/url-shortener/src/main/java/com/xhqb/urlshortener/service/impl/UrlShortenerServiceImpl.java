package com.xhqb.urlshortener.service.impl;

import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.ShortUrlLogDO;
import com.xhqb.spectre.common.dal.entity.UserShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.UserShortUrlDOMapper;
import com.xhqb.spectre.common.mq.UrlShortenerLogMQ;
import com.xhqb.spectre.common.utils.DateUtil;
import com.xhqb.urlshortener.component.UserShortUrlBloomFilter;
import com.xhqb.urlshortener.constant.LuciferConstant;
import com.xhqb.urlshortener.model.UrlShortener;
import com.xhqb.urlshortener.service.*;
import com.xhqb.urlshortener.utils.UserAgentUtil;
import io.prometheus.client.Collector;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.Objects;

/**
 * @author: pengxiaoxi
 * @description:
 * @date: 2024-04-09 13:37
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UrlShortenerServiceImpl implements UrlShortenerService {
    private final MemoryDataService memoryDataService;
    private final SenderService senderService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final UserShortUrlBloomFilter bloomFilter;
    private final UserShortUrlDOMapper userShortUrlDOMapper;
    private final BurialPointReportBizService burialPointReportBizService;
    private final BrandMappingBizService brandMappingBizService;

    @Value("${redirect.defaultUrl}")
    private String defaultUrl;

    /**
     * 普通短链跳转
     *
     * @param code
     * @param request
     * @param response
     */
    @Override
    public void redirectByCode(String code, HttpServletRequest request, HttpServletResponse response) {
        try {
            long start = System.nanoTime();
            UrlShortener urlShortener = memoryDataService.getUrlShortenerByCode(code);
            if (Objects.nonNull(urlShortener)) {
                log.info("URL Retrieved: {}", urlShortener.getSrcUrl());
                redirectIfPresent(urlShortener.getSrcUrl(), request, code, start, response);
            } else {
                response.sendRedirect(defaultUrl);
            }
        } catch (IOException e) {
            log.error("短链编码={},getUrl ERROR :", code, e);
        }
    }

    /**
     * 用户短链跳转
     *
     * @param userCode
     * @param request
     * @param response
     */
    @Override
    public void redirectByUserCode(String userCode, HttpServletRequest request, HttpServletResponse response) {
        if (StringUtils.isBlank(userCode) || !Objects.equals(6, userCode.length())) {
            log.info("用户短链不合法|userCode:{}", userCode);
            return;
        }

        // 过滤
        if (bloomFilter.hasReady() && !bloomFilter.contains(userCode)) {
            log.info("布隆过滤没有准备好获取不在布隆过滤器中|userCode:{}", userCode);
            return;
        }


        try {
            long start = System.nanoTime();
            // redis中获取
            String longUrl = getLongUrlForRedis(userCode);
            if (StringUtils.isBlank(longUrl)) {
                // 数据库获取
                longUrl = getLongUrlFromDb(userCode);
                if (StringUtils.isBlank(longUrl)) {
                    response.sendRedirect(defaultUrl);
                    return;
                }
            }
            // 跳转
            redirectIfPresent(longUrl, request, userCode, start, response);
        } catch (Exception e) {
            log.error("短链编码={},getUrl ERROR :", userCode, e);
        }

    }


    /**
     * 记录统计日志、埋点
     *
     * @param request
     * @param shortCode
     */
    private void sendMqAndRecord(HttpServletRequest request, String shortCode, long start, HttpServletResponse response) {
        // 构建mq信息
        ShortUrlLogDO shortUrlLogDO = UserAgentUtil.getUserAgent(request, shortCode);
        brandMappingBizService.userAgentParser(shortUrlLogDO);
        if (shortUrlLogDO != null) {
            asyncValidateAndSetUserTplCode(shortCode, shortUrlLogDO);
            UrlShortenerLogMQ urlShortenerLogMQ = UrlShortenerLogMQ.buildUrlShortenerLog(shortUrlLogDO);
            senderService.sendUrlShortenerMessage(urlShortenerLogMQ);
            burialPointReportBizService.report(shortCode);
        }
        // 统计次数
        LuciferConstant.COUNTER.labels(String.valueOf(response.getStatus())).inc();
        // 请求时间
        LuciferConstant.HISTOGRAM.labels(String.valueOf(response.getStatus()))
                .observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
    }

    /**
     * 从数据库获取长链接
     *
     * @param userCode
     * @return
     */
    private String getLongUrlFromDb(String userCode) {
        UserShortUrlDO userShortUrlDO = userShortUrlDOMapper.selectByCode(userCode);
        if (Objects.nonNull(userShortUrlDO)) {
            String expiredDate = userShortUrlDO.getExpiredDate();
            Date expireDate = DateUtil.stringToDate(expiredDate);
            if (Objects.nonNull(expireDate) && StringUtils.isNotBlank(userShortUrlDO.getLongUrl()) && expireDate.after(new Date())) {
                log.info("URL Retrieved: {}", userShortUrlDO.getLongUrl());
                return userShortUrlDO.getLongUrl();
            }
        }
        return null;
    }

    /**
     * 从redis获取数据
     *
     * @param userCode
     * @return
     */
    private String getLongUrlForRedis(String userCode) {
        Object longUrlValue = redisTemplate.opsForValue().get(RedisKeys.USER_SHORT_URL_PRE_KEY + userCode);
        if (longUrlValue == null) {
            return "";
        }
        return String.valueOf(longUrlValue);
    }

    /**
     * 链接存在跳转且记录
     *
     * @param longUrl
     * @param request
     * @param shortCode
     * @param start
     * @param response
     */
    private void redirectIfPresent(String longUrl, HttpServletRequest request, String shortCode, long start, HttpServletResponse response) {
        if (StringUtils.isNotBlank(longUrl)) {
            try {
                sendMqAndRecord(request, shortCode, start, response);
                response.sendRedirect(longUrl);
            } catch (IOException e) {
                log.error("短链编码={},getUrl ERROR :", shortCode, e);
            }
        }
    }

    private void asyncValidateAndSetUserTplCode(String shortCode, ShortUrlLogDO shortUrlLogDO) {
        shortUrlLogDO.setUserTplCode(shortCode);
        if (Objects.equals(6, shortCode.length())) {
            UserShortUrlDO userShortUrlDO = userShortUrlDOMapper.selectByCode(shortCode);
            if (Objects.nonNull(userShortUrlDO)) {
                shortUrlLogDO.setUserTplCode(userShortUrlDO.getTplCode());
            }
        }
    }
}
