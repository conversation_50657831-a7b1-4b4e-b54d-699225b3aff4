package com.xhqb.urlshortener.component;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xhqb.spectre.common.dal.entity.UserShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.UserShortUrlDOMapper;
import com.xhqb.urlshortener.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.shade.com.google.common.hash.BloomFilter;
import org.apache.pulsar.shade.com.google.common.hash.Funnels;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 用户短链布隆过滤器，对外提供新增、判断shortcode是否存在接口
 * <p>
 * 数据更新更新时机
 * 1、服务启动时，从db中异步加载指定天数
 * 2、每月定时，从db中异步加载指定天数
 * 3、接口生成调用生成
 *
 * <AUTHOR>
 * @date 2024/04/23
 */
@Component
@Slf4j
public class UserShortUrlBloomFilter {

    private static final AtomicBoolean HAS_READY = new AtomicBoolean(false);
    private static final AtomicBoolean LOAD_DATA_FLAG = new AtomicBoolean(false);
    private final AtomicReference<BloomFilter<String>> bloomFilterRef = new AtomicReference<>();

    /**
     * 用户短链有效天数
     */
    @Value("${user.short.url.valid.days:7}")
    private volatile int userShortUrlValidDays;
    /**
     * 用户短链一次查询加载条数
     */
    @Value("${user.short.url.load.size:1000}")
    private volatile int userShortUrlLoadSize;
    /**
     * 布隆过滤器大小，默认1000w个。假设每天20w，一个月约600W。
     */
    @Value("${shortLink.gen.bloomFilter.size:10000000}")
    private volatile Integer bloomFilterSize;
    /**
     * 布隆过滤器误判率容忍度
     */
    @Value("${shortLink.gen.bloomFilter.fpp:0.01}")
    private volatile double fpp;

    /**
     * 是否启用布隆过滤器
     */
    @Value("${bloom.filter.enable:true}")
    private volatile boolean bloomFilterEnable;

    /**
     * 初始化布隆过滤器的专用处理线程
     */
    private ThreadPoolExecutor initBloomFilterWorks;

    @Resource
    private UserShortUrlDOMapper userShortUrlDOMapper;

    @PostConstruct
    public void init() {

        initBloomFilterWorks = new ThreadPoolExecutor(0, 1, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("shortLink-initBloomFilter-work-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        log.info("服务启动加载隆过滤器");
        this.asyncLoadData();
    }

    /**
     * 定时刷新数据到bloomFilter 月初1号0点23分 ps:2024-05-01 00:23:00 星期三 2024-06-01 00:23:00 星期六
     */
    @Scheduled(cron = "${cache.refresh.user.short.url.cron:0 23 0 1 * ? }")
    public void execute() {
        log.info("开始每月定时更新布隆过滤器");
        this.asyncLoadData();
    }

    void asyncLoadData() {

        if (!this.bloomFilterEnable) {
            log.info("布隆过滤器没有启用");
            return;
        }

        if (LOAD_DATA_FLAG.get()) {
            log.info("数据正常加载中");
            return;
        }

        // TODO cl 这里有隐患，如果服务节点数比较多，重启时或定时任务会出现并发查询，db可能扛不住
        initBloomFilterWorks.execute(() -> {
            long startTime = System.currentTimeMillis();
            try {
                LOAD_DATA_FLAG.set(true);
                bloomLoadHandler();
            } catch (Throwable e) {
                log.error("ShortLink init BloomFilter failed|exception:", e);
            } finally {
                LOAD_DATA_FLAG.set(false);
            }
            log.info("初始化布隆过滤器总耗时|cost:{}", System.currentTimeMillis() - startTime);
        });
    }

    void bloomLoadHandler() {
        HAS_READY.set(false);
        BloomFilter<String> newFilter = BloomFilter.create(Funnels.stringFunnel(StandardCharsets.UTF_8), this.bloomFilterSize, this.fpp);
        long lastId = 0L;
        Date selectedDate = DateTimeUtil.mergeBackDays(new Date(), this.userShortUrlValidDays);
        List<UserShortUrlDO> userShortUrlList;
        do {
            userShortUrlList = userShortUrlDOMapper.loadUserShortUrlList(selectedDate, lastId, this.userShortUrlLoadSize);
            if (!CollectionUtils.isEmpty(userShortUrlList)) {
                refreshBloomFilter(userShortUrlList, newFilter);
                lastId = userShortUrlList.get(userShortUrlList.size() - 1).getId();
            }
        } while (!CollectionUtils.isEmpty(userShortUrlList) && userShortUrlList.size() == this.userShortUrlLoadSize);
        bloomFilterRef.set(newFilter);
        HAS_READY.set(true);

    }

    /**
     * 批量写入布隆过滤器
     *
     * @param modelList   刷新数据
     * @param bloomFilter 布隆过滤器
     */
    private void refreshBloomFilter(List<UserShortUrlDO> modelList, BloomFilter<String> bloomFilter) {
        if (CollectionUtils.isEmpty(modelList)) {
            return;
        }
        modelList.stream()
                .map(UserShortUrlDO::getShortCode)
                .filter(StringUtils::isNotBlank)
                .forEach(bloomFilter::put);
    }

    /**
     * 判断是否包含短链shortCode
     *
     * @param shortCode 用户短链code
     * @return 判断shortCode 是否存在
     */
    public boolean contains(String shortCode) {
        BloomFilter<String> bloomFilter = bloomFilterRef.get();
        if (Objects.nonNull(bloomFilter)) {
            return bloomFilterRef.get().mightContain(shortCode);
        }
        return true;
    }

    /**
     * 添加短链hashCode
     *
     * @param shortCode hashCode
     */
    public void put(String shortCode) {
        if (StringUtils.isBlank(shortCode)) {
            return;
        }

        BloomFilter<String> bloomFilter = bloomFilterRef.get();
        if (Objects.nonNull(bloomFilter)) {
            bloomFilter.put(shortCode);
        }
    }

    /**
     * 判断短链数据是否有加载完成
     *
     * @return boolean
     */
    public boolean hasReady() {
        return this.bloomFilterEnable && HAS_READY.get();
    }


}
