package com.xhqb.urlshortener.mq.producer;


import com.alibaba.fastjson.JSON;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.common.dal.entity.UserShortUrlDO;
import com.xhqb.urlshortener.model.dto.ShortCodeSynDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.pulsar.client.api.TypedMessageBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用户短链同步消费者
 *
 * <AUTHOR>
 * @date 2024/04/24
 */
@Component
@Slf4j
public class ShortCodeSynProducer {

    /**
     * 布滤器同步节点更新消息
     * topic = short-url-bloom-filter-update
     * 该topic由UserShortUrlBloomFilter消费
     */
    @Value("#{'${kael.mq.producers}'.split(',')[2]}")
    private volatile String shortCodeSynTopic;

    @Resource
    private MQTemplate<String> mqTemplate;

    public void send(List<UserShortUrlDO> userShortUrlDOList) {
        if (CollectionUtils.isEmpty(userShortUrlDOList)) {
            return;
        }

        long start = System.currentTimeMillis();
        List<ShortCodeSynDTO> shortCodeSynList = new ArrayList<>(userShortUrlDOList.size());
        for (UserShortUrlDO userShortUrl : userShortUrlDOList ) {
            ShortCodeSynDTO shortCodeSynDTO = new ShortCodeSynDTO();
            shortCodeSynDTO.setShortCode(userShortUrl.getShortCode());
            shortCodeSynList.add(shortCodeSynDTO);
        }

        String message = JSON.toJSONString(shortCodeSynList);
        log.info("布滤器同步节点更新消息 = {}", message);
        TypedMessageBuilder<String> mqTemplateMessage = mqTemplate.createMessage(shortCodeSynTopic, message);
        if (Objects.nonNull(mqTemplateMessage)) {
            mqTemplateMessage.sendAsync();
            log.info("布滤器同步节点更新消息响应完成,cost = {}", (System.currentTimeMillis() - start));
        }

    }
}
