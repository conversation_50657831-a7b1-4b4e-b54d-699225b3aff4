package com.xhqb.urlshortener.model;

import com.xhqb.spectre.common.dal.entity.ShortUrlDO;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.Optional;

@SuperBuilder
@Data
public class UrlShortener {
    private String shortCode;
    private String srcUrl;
    private Date expiredDate;
    private Integer id;

    public static UrlShortener buildUrlShortener(ShortUrlDO shortUrlDO) {
        return Optional.ofNullable(shortUrlDO).map(item -> UrlShortener.builder()
                .shortCode(shortUrlDO.getShortCode())
                .srcUrl(shortUrlDO.getSrcUrl())
                .expiredDate(shortUrlDO.getExpiredDate())
                .id(shortUrlDO.getId())
                .build()).orElse(null);
    }
}
