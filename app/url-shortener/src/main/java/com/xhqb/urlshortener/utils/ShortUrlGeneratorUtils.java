package com.xhqb.urlshortener.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.DatatypeConverter;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

/**
 * 短链生成服务
 *
 * <AUTHOR>
 * @date 2024/04/24
 */
@Slf4j
public final class ShortUrlGeneratorUtils {

    public static final int SHORT_URL_LEN = 6;
    public static final int SHORT_URL_COUNT = 4;

    private static final String[] CHARS_DICTIONARY = {
            "0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
            "A", "B", "C", "D", "E", "F", "G", "H", "I", "J",
            "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T",
            "U", "V", "W", "X", "Y", "Z", "a", "b", "c", "d",
            "e", "f", "g", "h", "i", "j", "k", "l", "m", "n",
            "o", "p", "q", "r", "s", "t", "u", "v", "w", "x",
            "y", "z"
    };

    /**
     * SHA算法生成短链，返回4个hashParam
     * 算法分析：
     * 一、在长度为40的SHA算法下生成的hash冲突概率分析如下：
     * 以下为大量测试验证得出的结果，在重试N次调用getHashParam()，所有N*4个hashParam均冲突时的数据量级如下所示：
     * 重试 3 次，冲突率达1%时的平均出现index：491597577
     *
     * @param longUrl 长链
     * @return 短链数组
     */
    public static String[] getHashParam(String longUrl) {
        if (StringUtils.isBlank(longUrl)) {
            return null;
        }

        // 加盐key
        String key = "spectre" + UUID.randomUUID().toString();

        // 对传入长链进行 MD5 加密
        String encryptResult;
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA");
            messageDigest.update((key + longUrl).getBytes());
            byte[] digest = messageDigest.digest();
            encryptResult = DatatypeConverter.printHexBinary(digest).toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            log.error("shortUrl run error|exception:", e);
            return null;
        }

        String[] resultUrl = new String[SHORT_URL_COUNT];
        //得到 4组短链接字符串
        for (int i = 0; i < SHORT_URL_COUNT; i++) {
            // 把加密字符按照 10 位一组 16 进制，切分成4段
            String sTempSubString = encryptResult.substring(i * 10, i * 10 + 10);

            /**
             * 每段与 0x3FFFFFFF 进行位与运算
             * 这里需要使用 long 型来转换，因为 Integer.parseInt() 只能处理 31 位 , 首位为符号位 , 如果不用 long ，则会越界
             */
            long lHexLong = 0xFFFFFFFFFFL & Long.parseLong(sTempSubString, 16);
            StringBuilder outChars = new StringBuilder();

            //循环获得每组6位的字符串
            for (int j = 0; j < SHORT_URL_LEN; j++) {
                // 把得到的值与 0x0000003D（即十进制的61）进行位与运算，取得字符数组 chars 索引(具体需要看chars数组的长度，以防下标溢出，注意起点为0)
                long index = 0x0000003D & lHexLong;
                // 把取得的单个字符相加
                outChars.append(CHARS_DICTIONARY[(int) index]);
                // 每次循环按位右移 6 位
                lHexLong = lHexLong >> 6;
            }
            resultUrl[i] = outChars.toString();
        }
        return resultUrl;
    }

}
