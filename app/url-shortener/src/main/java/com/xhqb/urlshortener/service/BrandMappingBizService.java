package com.xhqb.urlshortener.service;

import com.xhqb.spectre.common.dal.entity.BrandMappingDO;
import com.xhqb.spectre.common.dal.entity.ShortUrlLogDO;
import com.xhqb.spectre.common.dal.mapper.BrandMappingMapper;
import com.xhqb.urlshortener.cache.impl.BrandMappingMemoryCache;
import com.xhqb.urlshortener.model.dto.BrandMappingDTO;
import com.xhqb.urlshortener.model.dto.BrandMappingDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class BrandMappingBizService {

    @Resource
    private BrandMappingMemoryCache brandMappingMemoryCache;

    @Resource
    private BrandMappingMapper brandMappingMapper;

    public String userAgentParser(String userAgent) {
        Map<String, String> brandMapping = brandMappingMemoryCache.getBrandMapping();
        for (String code : brandMapping.keySet()) {
            if (userAgent.contains(code)) {
                return code + ":" + brandMapping.get(code);
            }
        }
        if (userAgent.trim().toLowerCase().startsWith("curl")) {
            return "Unknown:curl";
        }
        return "";
    }

    public void userAgentParser(ShortUrlLogDO shortUrlLogDO) {
        try {
            if (Objects.isNull(shortUrlLogDO)) {
                return;
            }
            String result = userAgentParser(shortUrlLogDO.getUserAgent());
            String[] brandCode = result.split(":");
            if (brandCode.length == 2) {
                shortUrlLogDO.setModel(brandCode[0]);
                shortUrlLogDO.setBrand(brandCode[1]);
            }
        }  catch (Exception e) {
           log.warn("userAgentParser exception", e);
        }
    }


    public void addBrand(BrandMappingDTO brandMappingDTO) {
        if(brandMappingDTO == null){
            return;
        }
        List<BrandMappingDataDTO> dataDTOList = brandMappingDTO.getDataDTOList();
        if(CollectionUtils.isEmpty(dataDTOList)){
            return;
        }

        Map<String, String> brandMapping = brandMappingMemoryCache.getBrandMapping();

        List<BrandMappingDO> brandMappingDOList = new ArrayList<>();
        Set<String> codeSet = brandMapping.keySet();
        for (BrandMappingDataDTO dataDTO : dataDTOList) {
            if(codeSet.contains(dataDTO.getCode())){
                continue;
            }
            BrandMappingDO brandMappingDO = new BrandMappingDO();
            brandMappingDO.setBrand(dataDTO.getBrand());
            brandMappingDO.setCode(dataDTO.getCode());
            brandMappingDO.setName(dataDTO.getName());
            brandMappingDO.setType(dataDTO.getType());
            brandMappingDO.setCreateTime( new Date());
            brandMappingDO.setUpdateTime( new Date());
            brandMappingDOList.add(brandMappingDO);
        }

        brandMappingDOList.forEach(brandMappingMapper::insertSelective);
    }
}
