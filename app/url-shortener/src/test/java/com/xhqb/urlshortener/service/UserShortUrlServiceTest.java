package com.xhqb.urlshortener.service;

import com.xhqb.spectre.common.dal.entity.ShortUrlTplDO;
import com.xhqb.urlshortener.cache.impl.ShortUrlTplMemoryCache;
import com.xhqb.urlshortener.component.UserShortUrlBloomFilter;
import com.xhqb.urlshortener.model.dto.UserShortUrlDTO;
import com.xhqb.urlshortener.model.dto.UserShortUrlDataDTO;
import com.xhqb.urlshortener.model.vo.UserShortUrlVO;
import com.xhqb.urlshortener.mq.producer.ShortCodeSynProducer;
import com.xhqb.urlshortener.mq.producer.UserShortSaveProducer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * spectre
 *
 * @author: cl
 * @date: 2024/04/25
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class UserShortUrlServiceTest {

    @Mock
    private ShortUrlTplMemoryCache shortUrlTplMemoryCache;
    @Mock
    private UserShortUrlBloomFilter bloomFilter;
    @Mock
    private UserShortSaveProducer userShortSaveProducer;
    @Mock
    private ShortCodeSynProducer shortCodeSynProducer;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @InjectMocks
    private UserShortUrlService userShortUrlService;

    @BeforeEach
    public void setup() {
        ReflectionTestUtils.setField(userShortUrlService, "shortUrl", "http://xh1.cn/");
        ReflectionTestUtils.setField(userShortUrlService, "keyExpireDay", 3);
        ReflectionTestUtils.setField(userShortUrlService, "retryNum", 3);
        ReflectionTestUtils.setField(userShortUrlService, "genUserShortCodeEnable", true);
    }

    @Test
    public void testHoldWithEmptyInputData() {
        UserShortUrlDTO userShortUrlDTO = new UserShortUrlDTO();
        userShortUrlDTO.setTplCode("TPL123");
        userShortUrlDTO.setDataList(Collections.emptyList());

        List<UserShortUrlVO> result = userShortUrlService.hold(userShortUrlDTO);
        assertTrue(result.isEmpty());
        verify(shortUrlTplMemoryCache, never()).selectByTplCode(anyString());
    }

    @Test
    public void testHoldWithExpiredTemplate() {
        UserShortUrlDTO userShortUrlDTO = new UserShortUrlDTO();
        userShortUrlDTO.setTplCode("TPL123");
        UserShortUrlDataDTO userShortUrlDataDTO = new UserShortUrlDataDTO();
        userShortUrlDataDTO.setMobile("xxxxxxxxxxxx");
        userShortUrlDataDTO.setTplCode("xxxxxxxxxxxx");
        userShortUrlDTO.setDataList(Collections.singletonList(userShortUrlDataDTO));

        ShortUrlTplDO mockTpl = new ShortUrlTplDO();
        mockTpl.setExpiredDate("2022-01-01 00:00:00");
        when(shortUrlTplMemoryCache.selectByTplCode("TPL123")).thenReturn(mockTpl);

        List<UserShortUrlVO> result = userShortUrlService.hold(userShortUrlDTO);
        assertTrue(result.isEmpty());
        verify(shortUrlTplMemoryCache, times(1)).selectByTplCode("TPL123");
    }

    @Test
    public void testUseGenBuildUserShortUrlList() {
        UserShortUrlDTO userShortUrlDTO = new UserShortUrlDTO();
        userShortUrlDTO.setTplCode("TPL123");
        userShortUrlDTO.setDataList(new ArrayList<>());
        UserShortUrlDataDTO userShortUrlDataDTO =  new UserShortUrlDataDTO();
        userShortUrlDataDTO.setMobile("xxxxxxxxxxxx");
        userShortUrlDataDTO.setTplCode("xxxxxxxxxxxx");
        userShortUrlDTO.getDataList().add(userShortUrlDataDTO);

        ShortUrlTplDO mockTpl = new ShortUrlTplDO();
        mockTpl.setShortCode("code123");
        mockTpl.setLongUrl("http://longurl.com");
        when(shortUrlTplMemoryCache.selectByTplCode("TPL123")).thenReturn(mockTpl);

        List<UserShortUrlVO> result = userShortUrlService.useGenBuildUserShortUrlList(userShortUrlDTO, userShortUrlDTO.getDataList(), mockTpl);
        assertFalse(result.isEmpty());
        // 验证Redis管道写入是否被调用
//        verify(redisTemplate, atLeastOnce()).executePipelined(any(SessionCallback.class));

        // 验证是否调用了异步保存到数据库的生产者
        verify(userShortSaveProducer, atLeastOnce()).doSendResp(any());

        // 验证是否调用了短链同步到其他实例的生产者
        verify(shortCodeSynProducer, atLeastOnce()).send(any());
    }
}
