package com.xhqb.urlshortener.utils;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

/**
 * 短链生成测试
 *
 * @author: cl
 * @date: 2024/04/24
 */
public class ShortUrlGeneratorUtilsTest {

    @Test
    public void testGenerateShortUrlConsistency() {
        String longUrl = "https://www.xhqb.com/aaaaa/bbbbb.html";
        String[] shortUrls = ShortUrlGeneratorUtils.getHashParam(longUrl);

        Assertions.assertNotNull(shortUrls );
        Assertions.assertEquals(4, shortUrls.length);

        // 测试每个短链接是否达到了预期的长度
        Arrays.stream(shortUrls).forEach(url ->
                Assertions.assertEquals(ShortUrlGeneratorUtils.SHORT_URL_LEN, url.length())
        );
    }

    @Test
    public void testEmptyOrNullUrl() {
        String[] shortUrls = ShortUrlGeneratorUtils.getHashParam("");
        Assertions.assertNull(shortUrls);

        shortUrls = ShortUrlGeneratorUtils.getHashParam(null);
        Assertions.assertNull(shortUrls);
    }


}