package com.xhqb.urlshortener.component;

import com.xhqb.spectre.common.dal.entity.UserShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.UserShortUrlDOMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * spectre
 *
 * @author: cl
 * @date: 2024/04/25
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class UserShortUrlBloomFilterTest {

    @Mock
    private UserShortUrlDOMapper userShortUrlDOMapper;

    @InjectMocks
    private UserShortUrlBloomFilter userShortUrlBloomFilter;

    @BeforeEach
    public void setup() {
        // Mock system environment or configurations if needed
        ReflectionTestUtils.setField(userShortUrlBloomFilter, "userShortUrlValidDays", 7);
        ReflectionTestUtils.setField(userShortUrlBloomFilter, "userShortUrlLoadSize", 1000);
        ReflectionTestUtils.setField(userShortUrlBloomFilter, "bloomFilterSize", 10000000);
        ReflectionTestUtils.setField(userShortUrlBloomFilter, "fpp", 0.01);
        ReflectionTestUtils.setField(userShortUrlBloomFilter, "bloomFilterEnable", true);
    }

    @Test
    public void testInit() {
        assertNotNull(userShortUrlBloomFilter);
    }

    @Test
    public void testAsyncLoadDataNotEnabled() {
        ReflectionTestUtils.setField(userShortUrlBloomFilter, "bloomFilterEnable", false);
        userShortUrlBloomFilter.asyncLoadData();
        verify(userShortUrlDOMapper, never()).loadUserShortUrlList(any(), anyLong(), anyInt());
    }

    @Test
    public void testContains() {
        String shortCode = "testCode";
        userShortUrlBloomFilter.put(shortCode);
        assertTrue(userShortUrlBloomFilter.contains(shortCode));
    }

    @Test
    public void testPut() {
        String shortCode = "anotherTestCode";
        userShortUrlBloomFilter.put(shortCode);
        assertTrue(userShortUrlBloomFilter.contains(shortCode));
    }

    @Test
    public void testBloomLoadHandler() {
        UserShortUrlDO userShortUrlDO = new UserShortUrlDO();
        userShortUrlDO.setShortCode("aaaaaa");
        userShortUrlDO.setId(1L);
        when(userShortUrlDOMapper.loadUserShortUrlList(any(), anyLong(), anyInt())).thenReturn(Collections.singletonList(userShortUrlDO));
        userShortUrlBloomFilter.bloomLoadHandler();
        assertTrue(userShortUrlBloomFilter.contains("aaaaaa"));
        assertTrue(userShortUrlBloomFilter.hasReady());
    }

}