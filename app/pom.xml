<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xhqb.spectre</groupId>
        <artifactId>spectre-parent</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>app</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <modules>
        <module>api</module>
        <module>dispatcher</module>
        <module>admin</module>
        <module>receipt</module>
        <module>receipt-cmpp</module>
        <module>channel</module>
        <module>cmpp-gateway</module>
        <module>clean-cmpp-redis-data</module>
        <module>sms-log-server</module>
        <module>cmppserver</module>
        <module>url-shortener</module>
        <module>route-calculate</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.xhqb.spectre</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xhqb.spectre</groupId>
                <artifactId>service</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>