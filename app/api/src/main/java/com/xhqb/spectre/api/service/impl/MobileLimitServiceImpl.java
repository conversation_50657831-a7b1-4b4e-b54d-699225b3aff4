package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.annotation.RateLimit;
import com.xhqb.spectre.api.service.MobileLimitService;
import org.springframework.stereotype.Service;

@Service
public class MobileLimitServiceImpl implements MobileLimitService {
    /**
     * 检查手机号码30s的发送量 （号码）
     *
     * @return
     */
    @RateLimit(prefix = "SMS:TIMEOUT:REDISKEY30S_", period = 30, count = 10)
    @Override
    public Boolean checkMobileMaxCountHalfMinute(String key, Integer limitValue) {
        return false;
    }


    /**
     * 检查一个小时内短信发送量 （号码）
     *
     * @return
     */
    @RateLimit(prefix = "SMS:TIMEOUT:REDISKEY1H_", period = 60 * 60, count = 20)
    @Override
    public Boolean checkMobileMaxCountHour(String key, Integer limitValue) {
        return false;
    }

    /**
     * 检查一天内短信发送量（号码）
     *
     * @return
     */
    @RateLimit(prefix = "SMS:TIMEOUT:REDISKEY1D_", period = 86400, count = 50)
    @Override
    public Boolean checkMobileMaxCountDay(String key, Integer limitValue) {
        return false;
    }

    /**
     * 检查相同短信内容
     *
     * @return
     */
    @RateLimit(prefix = "SMS:SAME:CONTENT:MAXCYCLE_", period = 86400, count = 20)
    @Override
    public Boolean checkMobileSameContentMaxCycle(String key, Integer limitValue) {
        return false;
    }

    /**
     * 一天可接收的短信验证码条数
     *
     * @param key
     * @param limitValue
     * @return
     */
    @RateLimit(prefix = "VERIFY:TIMEOUT:REDISKEY1D_", period = 86400, count = 20)
    @Override
    public Boolean checkVerifyCountDay(String key, Integer limitValue) {
        return false;
    }

    /**
     * 减去计数
     *
     * @param appKey
     */
    @RateLimit(period = 60, count = 10)
    @Override
    public void postSubtractAppFrequencyLimit(String appKey) {
    }
}
