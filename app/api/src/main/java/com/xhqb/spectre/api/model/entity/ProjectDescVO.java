package com.xhqb.spectre.api.model.entity;

import com.xhqb.spectre.common.dal.entity.ProjectDescDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectDescVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Integer id;

    private String description;

    public static ProjectDescVO buildVO(ProjectDescDO projectDescDO) {
        return ProjectDescVO.builder()
                .id(projectDescDO.getId())
                .description(projectDescDO.getDescription())
                .build();
    }
}
