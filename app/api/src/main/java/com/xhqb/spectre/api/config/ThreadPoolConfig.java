package com.xhqb.spectre.api.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xhqb.spectre.api.thread.CustomThreadPoolExecutor;
import com.xhqb.spectre.api.thread.WaitExecutionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * description: 线程池配置
 * date: 2024-06-30
 *
 * @author: yjq
 */
@Configuration
@EnableAsync
public class ThreadPoolConfig {


    @Bean
    public ThreadPoolExecutor encodeDataCollectorExecutor() {
        return new CustomThreadPoolExecutor(1, 2, 1L, TimeUnit.MINUTES,
                new LinkedBlockingQueue<>(1000),
                new ThreadFactoryBuilder().setNameFormat("encode-data-worker-%d").build(),
                new WaitExecutionHandler());
    }
}
