package com.xhqb.spectre.api.constant;

/*
 * @Author: h<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/11/11 14:34
 * @Description:
 */
public final class ShortUrlConstant {

    /**
     * 有效期类型。1：90天；2：180天；3：365天；4：永久有效
     */
    public static final int EXPIRED_TYPE_90 = 1;
    public static final int EXPIRED_TYPE_180 = 2;
    public static final int EXPIRED_TYPE_365 = 3;
    public static final int EXPIRED_TYPE_PERMANENT = 4;

    //通用状态值（1：有效；0：无效）
    public static final int STATUS_VALID = 1;
    public static final int STATUS_INVALID = 0;

    /**
     * 短链创建类型  接口创建 2 后台 1
     */
    public static final int API_TYPE = 2;
}
