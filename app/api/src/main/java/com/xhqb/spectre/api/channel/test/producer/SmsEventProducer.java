package com.xhqb.spectre.api.channel.test.producer;

import com.alibaba.fastjson.JSON;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.common.message.SmsEventMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class SmsEventProducer {

    @Resource
    private MQTemplate<String> mqTemplate;

    /**
     * topic:spectre-sms-event
     */
    @Value("${tdmq.producers.smsEvent}")
    private String topic;

    public void productMsg(SmsEventMessage smsEventMessage) {
        mqTemplate.send(topic, JSON.toJSONString(smsEventMessage));
    }
}
