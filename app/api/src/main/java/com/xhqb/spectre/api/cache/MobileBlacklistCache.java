package com.xhqb.spectre.api.cache;

/**
 * 手机黑名单缓存
 */
public interface MobileBlacklistCache {
    /**
     * 判断号码是否在黑名单
     */
    boolean isBlacklisted(String smsTypeCode, String mobile, String appCode);

    /**
     * 号码添加到黑名单
     */
    void put(String smsTypeCode, String mobile, String appCode, long expireAt);

    /**
     * 号码从黑名单移除
     */
    void remove(String smsTypeCode, String mobile, String appCode);

    /**
     * 获取上次刷新缓存的时间
     */
    Integer getLastRefreshTime();

    /**
     * 设置刷新缓存的时间
     */
    void updateLastRefreshTime(Integer time);

    /**
     * 判断operating time 时间是否晚于最后刷新时间
     *
     * @param timestamp 时间戳
     * @return 需要刷新则为true
     */
    boolean isAfterLastRefresh(Integer timestamp);
}
