package com.xhqb.spectre.api.service.impl;

import com.github.wujun234.uid.impl.CachedUidGenerator;
import com.xhqb.kael.util.StringUtils;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.controller.common.ResultCode;
import com.xhqb.spectre.api.model.entity.PhoneIsp;
import com.xhqb.spectre.api.model.entity.SMSMessageMQAssembler;
import com.xhqb.spectre.api.model.smsreq.BaseSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.VerifyCodeSMSReqDTO;
import com.xhqb.spectre.api.model.smsresp.VerifyCodeResultVO;
import com.xhqb.spectre.api.service.AssemblerVerifyService;
import com.xhqb.spectre.api.service.MemoryDataService;
import com.xhqb.spectre.api.service.SMSSenderService;
import com.xhqb.spectre.api.utils.AssemberMessageObjectUtils;
import com.xhqb.spectre.api.utils.ChannelUtils;
import com.xhqb.spectre.api.utils.PhoneSearchFastUtil;
import com.xhqb.spectre.common.dal.dto.TplData;
import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.common.mq.ChannelCode;
import com.xhqb.spectre.common.mq.MessageMQ;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class AssemblerVerifyServiceImpl implements AssemblerVerifyService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssemblerVerifyServiceImpl.class);

    @Resource
    private SMSSenderService smsSenderService;

    @Resource
    private RedisTemplate<String, String> redisVerifyCodeTemplate;

    @Resource
    private MemoryDataService memoryDataService;

    /**
     * 雪花ID生成器
     */
    @Resource
    private CachedUidGenerator cachedUidGenerator;

    /**
     * @param baseSMSReqDTO
     * @param verifyCodeResultVO
     * @return
     * @Decription： 验证码发送任务主逻辑
     */
    @Override
    public ResultCode sendVerifyTask(BaseSMSReqDTO baseSMSReqDTO, VerifyCodeResultVO verifyCodeResultVO) {
        VerifyCodeSMSReqDTO verifyCodeSMSReqDTO = (VerifyCodeSMSReqDTO) baseSMSReqDTO;
        // 埋点，后续可以自动设置RequestId
        String requestID = verifyCodeResultVO.getRequestId();
        if (StringUtils.isEmpty(requestID)) {
            LOGGER.warn("RequestID 生成失败");
            return ResultCode.MISS_REQUEST_PARAM;
        }
        String phone = verifyCodeSMSReqDTO.getPhoneNumbers();

        Long resultCode = SmsApiApplicationConstant.PHONE_STATUS_CODE;

        PhoneIsp phoneIsp = PhoneSearchFastUtil.getInstance().getIsp(phone);
        if (Objects.isNull(phoneIsp)) {
            phoneIsp = PhoneIsp.buildPhoneIspDefault(phone);
            LOGGER.warn("解析电话号码归属地失败，将使用默认归属地 号码：{}", phone);
        }
        // 渠道商
        TplData tplData = memoryDataService.getTplDataByCode(verifyCodeSMSReqDTO.getTplCode(), verifyCodeSMSReqDTO.getSignCode());
        if (null == tplData) {
            LOGGER.warn("模版信息不存在");
            return ResultCode.WRONG_SMS_TEMPLATE;
        }
        boolean isTplDisabled = ChannelUtils.isTplDisabled(tplData, phoneIsp);
        if (isTplDisabled) {
            LOGGER.warn("发送验证码MQ失败 命中模版禁用规则 phone{}", phone);
            return ResultCode.DISABLED_VALID;
        }
        // 判断是否命中渠道禁用规则
        List<ChannelCode> channelCodeList = new ArrayList<>();
        if (verifyCodeSMSReqDTO.getChannelAccountId() != null && verifyCodeSMSReqDTO.getChannelAccountId() != 0) {
            ChannelCode channelCode = new ChannelCode(tplData.getCode(), String.valueOf(verifyCodeSMSReqDTO.getChannelAccountId()), null, 10);
            channelCodeList.add(channelCode);
        } else {
            channelCodeList = ChannelUtils.getChannelCode(tplData, memoryDataService, phoneIsp, null, null);
            if (channelCodeList == null || channelCodeList.size() == 0) {
                LOGGER.warn("发送验证码MQ失败 未获取到渠道信息 phone{}", phone);
                return ResultCode.CHANNEL_EMPTY;
            }
        }
        // 组装验证码发送消息体
        SMSMessageMQAssembler<BaseBodyMessage> smsMessageMQAssembler = SMSMessageMQAssembler.buildByVerifyCodeReqDTO(verifyCodeSMSReqDTO, phoneIsp);
        smsMessageMQAssembler.setRequestId(requestID);
        smsMessageMQAssembler.setChannelCodeSet(channelCodeList);
        smsMessageMQAssembler.setSignName(tplData.getSignName());
        smsMessageMQAssembler.setOrderId(String.valueOf(cachedUidGenerator.getUID()));
        smsMessageMQAssembler.setPhoneStatus(resultCode);

        // 用验证码替换模版内容中的参数
        String processedStr = new StringBuilder(tplData.getContent()).toString().replaceAll(SmsApiApplicationConstant.TPL_CONTENT_PARAM, "%s");
        String finalStr = String.format(processedStr, verifyCodeSMSReqDTO.getSmsVerifyCode());
        smsMessageMQAssembler.setContent(finalStr);

        //装配验证码消息体
        MessageMQ<BaseBodyMessage> verifyMessageMQ = AssemberMessageObjectUtils.buildSMSBodyMessageMQ(smsMessageMQAssembler);
        if (verifyMessageMQ == null) {
            LOGGER.error("RequestID {}, PhoneNumber {}消息装配失败", verifyCodeResultVO.getRequestId(), phone);
            return ResultCode.SYS_FAILURES;
        }

        if (saveVerifyCode2RedisByIdentificationCode(verifyCodeSMSReqDTO.getAppCode(), phone, verifyCodeSMSReqDTO.getSmsVerifyCode())) {
            if (smsSenderService.sendVerifySMSMessage(verifyMessageMQ)) {
//                LOGGER.info("Verify SMS {} dispatch to SMS-DISPATCHER success", verifyMessageMQ);
                return ResultCode.SUCCESS;
            } else {
                LOGGER.warn("VeriyCode SMS:{} dispatch to TDMQ failed", verifyMessageMQ);
            }
        } else {
            LOGGER.warn("VerifyCode stored to redis failed. Phone: {}, appCode: {} ", phone, verifyCodeSMSReqDTO.getAppCode());
        }
        return ResultCode.FAILED;
    }


    /**
     * @param appKey
     * @param phoneNumber
     * @param verifyCode
     * @return
     * @Decription: 验证码存储缓存
     */
    private Boolean saveVerifyCode2RedisByIdentificationCode(String appKey, String phoneNumber, String verifyCode) {
        String verifyCodeKey = SmsApiApplicationConstant.VERIFY_CODE_PREFIX + appKey + phoneNumber;

        try {
            redisVerifyCodeTemplate.opsForValue().set(verifyCodeKey, verifyCode, SmsApiApplicationConstant.VERIFIY_CODE_TIMEOUT, SmsApiApplicationConstant.VERIFY_CODE_TIMEUNIT_SECONDS);
            return Boolean.TRUE;
        } catch (Exception e) {
            LOGGER.error("VerifyCode stored into cache failed {}", e.getMessage());
        }

        return Boolean.FALSE;
    }


}
