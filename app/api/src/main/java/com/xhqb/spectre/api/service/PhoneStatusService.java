package com.xhqb.spectre.api.service;

import com.xhqb.spectre.api.model.entity.MobileStatus;
import com.xhqb.spectre.api.model.smsreq.CheckPhoneStatusReqDTO;
import com.xhqb.spectre.api.model.smsresp.CheckPhoneStatusResultVO;

import java.util.List;
import java.util.Set;

public interface PhoneStatusService {

    CheckPhoneStatusResultVO<MobileStatus> check(CheckPhoneStatusReqDTO checkPhoneStatusReqDTO);

    CheckPhoneStatusResultVO<MobileStatus> getPhoneNumberStatuses(CheckPhoneStatusReqDTO checkPhoneStatusReqDTO);

    int refreshPhoneNumberStatuses(CheckPhoneStatusReqDTO checkPhoneStatusReqDTO);

    List<MobileStatus> checkAndSave(Set<String> phoneNumbers);
}
