package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.controller.common.ResultCode;
import com.xhqb.spectre.api.exception.SMSSenderException;
import com.xhqb.spectre.api.model.entity.ErrorPhone;
import com.xhqb.spectre.api.model.entity.SMSMessageMQAssembler;
import com.xhqb.spectre.api.model.entity.SMSSendFailedRecord;
import com.xhqb.spectre.api.model.entity.SMSSendSuccessRecord;
import com.xhqb.spectre.api.model.smsreq.BaseSMSReqDTO;
import com.xhqb.spectre.api.model.smsresp.BaseSMSResultVO;
import com.xhqb.spectre.api.model.smsresp.BatchSMSResultVO;
import com.xhqb.spectre.api.service.SMSSenderService;
import com.xhqb.spectre.api.service.SendMessageService;
import com.xhqb.spectre.api.utils.AssemberMessageObjectUtils;
import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.common.mq.MessageMQ;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class SendMessageServiceImpl implements SendMessageService {

    @Resource
    public SMSSenderService smsSenderService;

    /**
     * 发送消息
     *
     * @param singleMessageMQ
     * @param baseSMSReqDTO
     * @param baseSMSResultVO
     * @return
     */
    @Override
    public boolean sendMessage(MessageMQ<BaseBodyMessage> singleMessageMQ, BaseSMSReqDTO baseSMSReqDTO, BaseSMSResultVO baseSMSResultVO) {
        boolean sendStatus = Boolean.FALSE;
        // 防御性实现、预防控制异常
        if (singleMessageMQ == null) {
            log.error("RequestID {}, PhoneNumber {}消息装配失败", baseSMSResultVO.getRequestId(), baseSMSReqDTO.getPhoneNumbers());
            return sendStatus;
        }
        switch (baseSMSReqDTO.getMessageTypeEnum()) {
            //通知类消息, 债转通知
            case NOTIFY:
            case DEBT_SWAP:
                sendStatus = smsSenderService.sendNotifySMSMessage(singleMessageMQ, Boolean.FALSE);
                break;
            //市场类消息
            case MARKET:
                sendStatus = smsSenderService.sendMarketSMSMessage(singleMessageMQ, Boolean.FALSE);
                break;
            //催收类消息
            case COLLECTOR:
            case LIGHT_COLLECTOR:
            case SEVERE_COLLECTOR:
                sendStatus = smsSenderService.sendCollectSMSMessage(singleMessageMQ, Boolean.FALSE);
                break;
            //模版不支持的类型
            case UNKNOWN:
            default:
                log.error("Not Support SMS MessageType");
                throw new SMSSenderException(ResultCode.UNKONW_MSG_TYPE);
        }
        return sendStatus;
    }

    @Override
    public MessageMQ<BaseBodyMessage> buildSingleMessageMQ(SMSMessageMQAssembler smsMessageMQAssember) {
        MessageMQ<BaseBodyMessage> singleMessageMQ = AssemberMessageObjectUtils.buildSMSBodyMessageMQ(smsMessageMQAssember);
        if (null == singleMessageMQ) {
            log.warn("对象{} 构建消息为空", smsMessageMQAssember);
            return null;
        }
        return singleMessageMQ;
    }

    @Override
    public BatchSMSResultVO buildBatchSMSResultVO(BaseSMSReqDTO baseSMSReqDTO, BaseSMSResultVO baseSMSResultVO, List<String> successPhoneList, List<ErrorPhone> failedPhoneList) {
        BatchSMSResultVO batchSMSResultVO = new BatchSMSResultVO();
        //设置返回错误记录集合
        List<SMSSendSuccessRecord> smsSendSuccessRecordList = new ArrayList<SMSSendSuccessRecord>();
        //拼装
        List<SMSSendFailedRecord> smsSendFailedRecordList = new ArrayList<SMSSendFailedRecord>();
        if ((baseSMSResultVO != null) && (baseSMSResultVO.getSmsSendResult() != null)) {
            smsSendFailedRecordList.addAll(((BatchSMSResultVO) baseSMSResultVO.getSmsSendResult()).getFailureSMSRecord());
        }
        for (String successPhone : successPhoneList) {
            SMSSendSuccessRecord smsSendSuccessRecord = new SMSSendSuccessRecord();
            smsSendSuccessRecord.setPhoneNumber(successPhone);
            smsSendSuccessRecord.setSendTime(new Date());
            smsSendSuccessRecordList.add(smsSendSuccessRecord);
        }

        for (ErrorPhone errorPhone : failedPhoneList) {
            SMSSendFailedRecord smsSendFailedRecord = new SMSSendFailedRecord();
            smsSendFailedRecord.setPhoneNumber(errorPhone.getPhone());
            smsSendFailedRecord.setFileCode(errorPhone.getErrorCode());
            smsSendFailedRecord.setFileMsg(errorPhone.getErrorMsg());
            smsSendFailedRecordList.add(smsSendFailedRecord);
        }

        batchSMSResultVO.setTotal(smsSendFailedRecordList.size() + smsSendSuccessRecordList.size());
        batchSMSResultVO.setSuccess(smsSendSuccessRecordList.size());
        batchSMSResultVO.setFailure(smsSendFailedRecordList.size());
        batchSMSResultVO.setSuccessSMSRecord(smsSendSuccessRecordList);
        batchSMSResultVO.setFailureSMSRecord(smsSendFailedRecordList);
        return batchSMSResultVO;
    }
}
