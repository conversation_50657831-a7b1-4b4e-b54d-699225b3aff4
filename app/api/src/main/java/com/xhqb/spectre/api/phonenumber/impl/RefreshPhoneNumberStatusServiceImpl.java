package com.xhqb.spectre.api.phonenumber.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.api.config.TopicConfig;
import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.phonenumber.RefreshPhoneNumberStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RefreshPhoneNumberStatusServiceImpl implements RefreshPhoneNumberStatusService {

    private final MQTemplate<String> mqTemplate;

    /**
     * 每次队列总数
     */
    private final int perNum;

    /**
     * 刷新手机状态队列名称
     */
    private final String refreshPhoneNumberStatusTopic;

    @Autowired
    public RefreshPhoneNumberStatusServiceImpl(MQTemplate<String> mqTemplate,
                                               TopicConfig topicConfig,
                                               VenusConfig venusConfig) {
        this.mqTemplate = mqTemplate;
        this.refreshPhoneNumberStatusTopic = topicConfig.getRefreshPhoneNumberStatusTopic();

        this.perNum = venusConfig.getPerNum();
    }

    @Override
    public void refresh(List<String> phones) {
        if (phones == null || phones.isEmpty()) {
            return;
        }

        List<List<String>> partitionedLists = Lists.partition(phones, perNum);
        partitionedLists.forEach(list -> {
            mqTemplate.send(refreshPhoneNumberStatusTopic, Joiner.on(",").join(list));
        });
    }
}
