package com.xhqb.spectre.api.exception;


import com.xhqb.spectre.api.controller.common.IErrorCode;

/**
 * @Description 自定义异常
 */

public class GlobalException extends RuntimeException {
    private IErrorCode errorCode;

    public IErrorCode getErrorCode() {
        return errorCode;
    }

    public GlobalException(IErrorCode errorCode) {
        super(errorCode.getResultMsg());
        this.errorCode = errorCode;
    }

    public GlobalException(String message) {
        super(message);
    }

    public GlobalException(Throwable cause) {
        super(cause);
    }

    public GlobalException(String message, Throwable cause) {
        super(message, cause);
    }

}
