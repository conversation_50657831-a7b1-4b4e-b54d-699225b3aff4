package com.xhqb.spectre.api.service.impl;

import cn.hutool.core.date.DateTime;
import com.xhqb.spectre.api.bi.mapper.DebtSmsMapper;
import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.exception.GlobalException;
import com.xhqb.spectre.api.model.entity.*;
import com.xhqb.spectre.api.service.SmsQueryService;
import com.xhqb.spectre.api.utils.DateUtil;
import com.xhqb.spectre.common.dal.dto.result.QueryTestStatResult;
import com.xhqb.spectre.common.dal.entity.*;
import com.xhqb.spectre.common.dal.entity.support.TplChannelDOSupport;
import com.xhqb.spectre.common.dal.mapper.*;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-03-04
 */
@Service
@Slf4j
public class SmsQueryServiceImpl implements SmsQueryService {

    @Resource
    private SmsOrderMapper smsOrderMapper;

    @Resource
    private GatewayUserMapper gatewayUserMapper;

    @Resource
    private AppMapper appMapper;

    @Resource
    private TplMapper tplMapper;

    @Resource
    private SignMapper signMapper;

    @Resource
    private TplChannelMapper tplChannelMapper;

    @Resource
    private TplDisableMapper tplDisableMapper;

    @Resource
    private DebtSmsMapper debtSmsMapper;

    @Resource
    private VenusConfig venusConfig;

    @Resource
    private ChannelAccountMapper channelAccountMapper;

    @Resource
    private PhoneNumberStatusMapper phoneNumberStatusMapper;
    @Resource
    private ChannelMapper channelMapper;

    /**
     * update by yequnzhang
     * 一个requestId会存在多条数据
     * 另外重发时会存在重复数据，根据requestId和orderId过滤数据
     *
     * @param requestIdList
     * @param appCode
     * @return
     */
    @Override
    public List<QueryOrderVO> queryOrder(List<String> requestIdList, String appCode) {
        List<SmsOrderDO> smsOrderDOList = smsOrderMapper.selectByRequestIdsAndAppCode(requestIdList, appCode,
                DateUtil.getCurrentDayLatestTimestamp(), DateUtil.getNDaysAgoEarliestTimestamp(venusConfig.getNAgoDay()));
        List<SmsOrderDO> filter = new ArrayList<>();
        if (smsOrderDOList != null && smsOrderDOList.size() > 0) {
            HashMap<String, SmsOrderDO> map = new HashMap<>();
            for (SmsOrderDO smsOrderDO : smsOrderDOList) {
                SmsOrderDO inner = map.get(smsOrderDO.getRequestId() + smsOrderDO.getOrderId());
                if (inner == null || inner.getResend() < smsOrderDO.getResend()) {
                    map.put(smsOrderDO.getRequestId() + smsOrderDO.getOrderId(), smsOrderDO);
                }
            }
            filter = new ArrayList<>(map.values());
        }
        return DO2VO(filter);
    }

    private List<QueryOrderVO> DO2VO(List<SmsOrderDO> dos) {
        List<QueryOrderVO> queryOrderVOList = new ArrayList<>();
        for (SmsOrderDO smsOrderDO : dos) {
            if (smsOrderDO != null) {
                QueryOrderVO queryOrderVO = QueryOrderVO.builder()
                        .requestId(smsOrderDO.getRequestId())
                        .orderId(smsOrderDO.getOrderId())
                        .mobile(smsOrderDO.getMobile())
                        .sendStatus(smsOrderDO.getSendStatus())
                        .reportStatus(smsOrderDO.getReportStatus())
                        .content(smsOrderDO.getContent())
                        .billCount(smsOrderDO.getBillCount())
                        .reportCode(smsOrderDO.getReportCode())
                        .reportDesc(smsOrderDO.getReportDesc())
                        .reportTime(smsOrderDO.getReportTime())
                        .sendTime(smsOrderDO.getSendTime())
                        .sendDesc(smsOrderDO.getSendDesc())
                        .sendCode(smsOrderDO.getSendCode())
                        .bizBatchId(smsOrderDO.getBizBatchId())
                        .build();
                queryOrderVOList.add(queryOrderVO);
            }
        }
        return queryOrderVOList;
    }

    @Override
    public CommonPager<QueryTplVO> findTplListByPage(TplQuery tplQuery) {
        Set<String> gatewayCodeSet = gatewayUserMapper.selectAll().stream().map(GatewayUserDO::getTplCode).collect(Collectors.toSet());
        Map<String, AppDO> appMap = appMapper.selectByQuery(new AppQuery()).stream().collect(Collectors.toMap(AppDO::getCode, Function.identity()));
        Map<Integer, SignDO> signMap = signMapper.selectByQuery(new SignQuery()).stream().collect(Collectors.toMap(SignDO::getId, Function.identity()));
        return PageResultUtils.result(
                () -> tplMapper.countByQuery(tplQuery),
                () -> tplMapper.selectByQuery(tplQuery).stream()
                        .map(item -> QueryTplVO.buildTplVO(item, isSupportMultiContent(item, gatewayCodeSet, appMap)))
                        .map(item -> enrichTplVOWithSignInfo(signMap, item))
                        .collect(Collectors.toList())
        );
    }

    private QueryTplVO enrichTplVOWithSignInfo(Map<Integer, SignDO> signMap, QueryTplVO queryTplVO) {
        SignDO signDO = signMap.get(queryTplVO.getSignId());
        if (signDO != null) {
            queryTplVO.setSignCode(StringUtils.isEmpty(signDO.getCode()) ? "" : signDO.getCode());
            queryTplVO.setSignName(StringUtils.isEmpty(signDO.getName()) ? "" : signDO.getName());
        } else {
            queryTplVO.setSignCode("");
            queryTplVO.setSignName("");
        }
        return queryTplVO;
    }


    /**
     * 是否支持多个模板内容
     *
     * @param tplDO
     * @param gatewayCodeSet
     * @param appMap
     * @return
     */
    private boolean isSupportMultiContent(TplDO tplDO, Set<String> gatewayCodeSet, Map<String, AppDO> appMap) {
        String tplCode = tplDO.getCode();
        if (gatewayCodeSet.contains(tplCode)) {
            return true;
        }
        AppDO appDO = appMap.get(tplDO.getAppCode());
        return Objects.nonNull(appDO) && appDO.isSupportContentApi() && tplCode.equals(buildHttpContentTplCode(tplDO));
    }

    private String buildHttpContentTplCode(TplDO tplDO) {
        return "http_" + tplDO.getAppCode() + "_" + tplDO.getSmsTypeCode() + "_tpl";
    }

    @Override
    public List<TplInfoVO> queryByAppCode(String appCode) {
        List<TplDO> tplDOList = tplMapper.selectByAppCode(appCode);
        List<SignDO> signDOList = signMapper.selectEnum(1);
        Map<Integer, SignDO> signMap = signDOList.stream().collect(Collectors.toMap(SignDO::getId, SignDO -> SignDO));
        List<TplInfoVO> tplInfoVOList = new ArrayList<>();
        if (tplDOList != null) {
            for (TplDO tplDO : tplDOList) {
                SignDO signDO = signMap.get(tplDO.getSignId());
                tplInfoVOList.add(TplInfoVO.buildTplInfoVO(tplDO, signDO.getName(), signDO.getCode()));
            }
        }
        return tplInfoVOList;
    }

    @Override
    public QueryTplVO queryTpl(String code, String signName) {
        SignDO signDO = signMapper.selectByName(signName);
        if (signDO == null) return null;
        TplDO tplDO = tplMapper.selectByCodeAndSign(code, signDO.getId());
        if (tplDO == null) return null;
        return QueryTplVO.buildTplVO(tplDO, false);
    }

    @Override
    public QueryTplVO getTplById(Integer id) {
        TplDO tplDO = validateAndSelectById(id);
        List<TplChannelDOSupport> channelDOList = tplChannelMapper.selectSupportByTplId(id);
        List<TplDisableDO> disableDOList = tplDisableMapper.selectByTplId(id);
        return QueryTplVO.buildTplVOSupport(tplDO, channelDOList, disableDOList, isSupportMultiContent(tplDO));
    }

    @Override
    public CommonPager<QueryReissueOrderVO> queryReissueOrderByMobile(ReissueOrderQuery query) {
        List<QueryReissueOrderVO> reissueOrderVOList = smsOrderMapper.selectByReissueOrderQuery(query,
                        DateUtil.getCurrentDayLatestTimestamp(), DateUtil.getNDaysAgoEarliestTimestamp(venusConfig.getNAgoDay())).stream()
                .map(this::buildQueryReissueOrderVO).collect(Collectors.toList());

        // reissueOrderVOList 获取 appCode List
        List<String> appCodeList = reissueOrderVOList.stream().map(QueryReissueOrderVO::getAppCode).distinct().collect(Collectors.toList());

        Map<String, String> appCodeMap = new HashMap<>();
        // appCodeList 遍历 查询 appName
        if (!CollectionUtils.isEmpty(appCodeList)) {
            appCodeList.forEach(appCode -> {
                AppDO appDO = appMapper.selectByCode(appCode);
                if (Objects.nonNull(appDO)) {
                    appCodeMap.put(appCode, appDO.getName());
                }
            });
        }

        for (QueryReissueOrderVO vo : reissueOrderVOList) {
            if (appCodeMap.containsKey(vo.getAppCode())) {
                vo.setName(appCodeMap.get(vo.getAppCode()));
            }
        }

        return PageResultUtils.result(
                () -> smsOrderMapper.countByReissueOrderQuery(query,
                        DateUtil.getCurrentDayLatestTimestamp(), DateUtil.getNDaysAgoEarliestTimestamp(venusConfig.getNAgoDay())),
                () -> reissueOrderVOList
        );
    }

    @Override
    public List<QueryChannelAccountVO> queryChannelAccount(ChannelAccountQuery query) {
        return channelAccountMapper.selectByQuery(query).stream().map(this::buildChannelAccount).collect(Collectors.toList());
    }

    @Override
    public TestStatVO queryTestStats(TestStatQuery query) {
        String smsTypeCode = query.getSmsTypeCode();
        Integer channelAccountId = query.getChannelAccountId();
        String statDate = query.getStatDate();

        // 当前统计时间
        DateTime currentStartTime = cn.hutool.core.date.DateUtil.beginOfDay(cn.hutool.core.date.DateUtil.parseDate(statDate));
        DateTime currentEndTime = cn.hutool.core.date.DateUtil.endOfDay(currentStartTime);
        QueryTestStatResult currentResult = smsOrderMapper.selectQueryTestStats(
                DateUtil.dateToSendTimeLong(currentStartTime), DateUtil.dateToSendTimeLong(currentEndTime), smsTypeCode, channelAccountId);
        Long currentSendSum = currentResult.getTotalCount();
        Long currentSuccessSum = currentResult.getSuccessCount();

        // 前一周统计时间
        DateTime oldStartTime = cn.hutool.core.date.DateUtil.beginOfDay(cn.hutool.core.date.DateUtil.offsetDay(currentStartTime, -7));
        DateTime oldEndTime = cn.hutool.core.date.DateUtil.endOfDay(oldStartTime);
        QueryTestStatResult oldResult = smsOrderMapper.selectQueryTestStats(
                DateUtil.dateToSendTimeLong(oldStartTime), DateUtil.dateToSendTimeLong(oldEndTime), smsTypeCode, channelAccountId);
        Long oldSendSum = oldResult.getTotalCount();
        Long oldSuccessSum = oldResult.getSuccessCount();

        TestStatVO testStatVO = new TestStatVO();
        testStatVO.setStatDate(statDate);
        testStatVO.setCurrentSendSum(currentSendSum);
        testStatVO.setCurrentSuccessSum(currentSuccessSum);
        testStatVO.setOldSendSum(oldSendSum);
        testStatVO.setOldSuccessSum(oldSuccessSum);
        return testStatVO;
    }

    @Override
    public List<QueryDebtSmsVO> queryDebtSms(DebtSmsQuery query) {
        return debtSmsMapper.selectByContractNo(query.getContractNos())
                .stream()
                .map(QueryDebtSmsVO::buildVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ChannelBindingVO> checkBindingThenGetAccount(List<TplChannelDTO> tplChannelDTOList) {
        // 1. 空列表直接返回
        if (CollectionUtils.isEmpty(tplChannelDTOList)) {
            return Collections.emptyList();
        }

        // 2. 批量获取模板数据
        Set<String> distinctTplCodes = tplChannelDTOList.stream()
                .map(TplChannelDTO::getTplCode)
                .collect(Collectors.toSet());
        List<TplDO> tplDOS = tplMapper.selectByCodeList(new ArrayList<>(distinctTplCodes));

        // 3.构建 tplCodeMap key:code value:id
        Map<String, Integer> tplCodeMap = tplDOS.stream()
                .filter(tplDO -> tplDO != null && tplDO.getId() != null)
                .collect(Collectors.toMap(TplDO::getCode, TplDO::getId, (oldId, newId) -> oldId));

        Map<Integer, Integer> tplIdAndSignIdMap = tplDOS.stream()
                .filter(tplDO -> tplDO != null && tplDO.getId() != null)
                .collect(Collectors.toMap(TplDO::getId, TplDO::getSignId, (oldId, newId) -> oldId));

        // 4. 提取有效模板ID和签名 id
        List<Integer> validTplIds = tplDOS.stream().map(TplDO::getId).collect(Collectors.toList());
        List<Integer> signIds = tplDOS.stream().map(TplDO::getSignId).collect(Collectors.toList());

        // 5. 批量获取签名和绑定信息
        Map<Integer, String> signCodeMap = loadSignMappings(signIds, tplIdAndSignIdMap);
        Map<String, Integer> bindingMap = loadChannelBindings(validTplIds);

        // 6. 流式处理结果
        return tplChannelDTOList.stream()
                .map(dto -> createBindingVO(dto, tplCodeMap, bindingMap, signCodeMap))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> retrieveRecentEmptyNumbers(int emptyCount) {
        if (emptyCount <= 0) {
            return Collections.emptyList();
        }
        return phoneNumberStatusMapper.selectEmptyNumbers(emptyCount).stream()
                .map(PhoneNumberStatusDO::getMobile)
                .collect(Collectors.toList());
    }

    @Override
    public List<ChannelVO> getAllSmsChannels() {
        return channelMapper.selectAll().stream().map(this::buildVO).collect(Collectors.toList());
    }

    @Override
    public List<String> smsContentByTplCodeAndCount(SmsContentByTplCodeAndCount smsContentByTplCodeAndCount) {
        String tplCode = smsContentByTplCodeAndCount.getTplCode();
        Integer count = smsContentByTplCodeAndCount.getCount();
        if (StringUtils.isEmpty(tplCode) || count == null || count < 1) {
            log.warn("tplCode or count is null");
            return Collections.emptyList();
        }

        List<TplDO> tplDOS = tplMapper.selectByCodeList(Collections.singletonList(tplCode));
        if (CollectionUtils.isEmpty(tplDOS)) {
            log.info("tplCode {} is not exist", tplCode);
            return Collections.emptyList();
        }

        Date curDete = new Date();
        Date endDate = cn.hutool.core.date.DateUtil.endOfDay(DateUtils.addDays(curDete, -1));
        Date startDate = cn.hutool.core.date.DateUtil.beginOfDay(DateUtils.addDays(curDete, -venusConfig.getQueryOrderDays()));
        List<SmsOrderDO> smsOrderDOList = smsOrderMapper.selectByTplCodeAndDate(tplCode, startDate.getTime() / 1000, endDate.getTime() / 1000, count);
        if (CollectionUtils.isEmpty(smsOrderDOList)) {
            log.info("当前tplCode:{} |smsOrderDOList is empty", tplCode);
            return Collections.emptyList();
        }
        return smsOrderDOList.stream().map(SmsOrderDO::getContent).collect(Collectors.toList());
    }

    private ChannelVO buildVO(ChannelDO channelDO) {
        ChannelVO vo = new ChannelVO();
        vo.setChannelCode(channelDO.getCode());
        vo.setChannelName(channelDO.getName());
        return vo;
    }

    private QueryChannelAccountVO buildChannelAccount(ChannelAccountDO channelAccountDO) {
        QueryChannelAccountVO vo = new QueryChannelAccountVO();
        BeanUtils.copyProperties(channelAccountDO, vo);
        return vo;
    }

    private QueryReissueOrderVO buildQueryReissueOrderVO(SmsOrderDO smsOrderDO) {
        return QueryReissueOrderVO.builder()
                .sendTime(DateUtil.intToString(smsOrderDO.getSendTime()))
                .tplCode(smsOrderDO.getTplCode())
                .content(smsOrderDO.getContent())
                .sendStatus(smsOrderDO.getReportStatus())
                .appCode(smsOrderDO.getAppCode())
                .sendDesc(smsOrderDO.getReportDesc())
                .build();
    }


    /**
     * 是否支持多个模板内容
     *
     * @param tplDO
     * @return
     */
    private boolean isSupportMultiContent(TplDO tplDO) {
        return isGatewayTpl(tplDO.getCode()) || isHttpContentTpl(tplDO);
    }


    /**
     * 是否网关账号的模板
     *
     * @param tplCode
     * @return
     */
    private boolean isGatewayTpl(String tplCode) {
        GatewayUserQuery query = new GatewayUserQuery();
        query.setTplCode(tplCode);
        Integer count = gatewayUserMapper.countByQuery(query);
        return Objects.nonNull(count) && count > 0;
    }

    private TplDO validateAndSelectById(Integer id) {
        TplDO tplDO = tplMapper.selectByPrimaryKey(id);
        if (Objects.isNull(tplDO)) {
            throw new GlobalException("未找到该模板");
        }
        return tplDO;
    }

    /**
     * 是否http内容模板
     *
     * @param tplDO
     * @return
     */
    private boolean isHttpContentTpl(TplDO tplDO) {
        AppDO appDO = appMapper.selectByCode(tplDO.getAppCode());
        return Objects.nonNull(appDO) && appDO.isSupportContentApi() && tplDO.getCode().equals(buildHttpContentTplCode(tplDO));
    }

    private ChannelBindingVO createBindingVO(
            TplChannelDTO dto,
            Map<String, Integer> tplCodeMap,
            Map<String, Integer> bindingMap,
            Map<Integer, String> signCodeMap) {


        ChannelBindingVO vo = new ChannelBindingVO();
        vo.setTplCode(dto.getTplCode());
        vo.setChannelCode(dto.getChannelCode());
        int tplId = tplCodeMap.get(dto.getTplCode());
        // 获取签名代码
        vo.setSignCode(signCodeMap.getOrDefault(tplId, null));
        // 获取账户绑定
        String bindingKey = dto.getChannelCode() + tplId;
        Integer channelAccountId = bindingMap.get(bindingKey);
        vo.setChannelAccountId(channelAccountId);
        vo.setBinding(Objects.nonNull(channelAccountId));
        return vo;
    }

    /**
     * 查询模板数据并建立索引
     */
    private Map<String, Optional<Integer>> getTemplatesIndex(List<TplChannelDTO> dtos) {
        Set<String> distinctTplCodes = dtos.stream()
                .map(TplChannelDTO::getTplCode)
                .collect(Collectors.toSet());

        // 批量查询模板并建立缓存
        Map<String, Integer> templateCache = tplMapper.selectByCodeList(new ArrayList<>(distinctTplCodes))
                .stream()
                .collect(Collectors.toMap(
                        TplDO::getCode,
                        TplDO::getId,
                        (oldId, newId) -> oldId));

        // 为不存在的模板返回Optional.empty()
        return distinctTplCodes.stream()
                .collect(Collectors.toMap(
                        Function.identity(),
                        code -> Optional.ofNullable(templateCache.get(code))
                ));
    }

    /**
     * 加载签名映射关系
     */
    private Map<Integer, String> loadSignMappings(List<Integer> signIds, Map<Integer, Integer> tplIdAndSignIdMap) {
        if (CollectionUtils.isEmpty(signIds)) {
            return Collections.emptyMap();
        }

        // 精确查询所需签名
        Map<Integer, String> signMap = signMapper.selectByIds(new ArrayList<>(signIds))
                .stream()
                .collect(Collectors.toMap(
                        SignDO::getId,
                        SignDO::getCode
                ));

        Map<Integer, String> retMap = new HashMap<>();
        for (Map.Entry<Integer, Integer> tplCodeAndSignIdMap : tplIdAndSignIdMap.entrySet()) {
            Integer tplId = tplCodeAndSignIdMap.getKey();
            Integer signId = tplCodeAndSignIdMap.getValue();
            retMap.put(tplId, signMap.getOrDefault(signId, null));
        }
        return retMap;
    }

    /**
     * 加载渠道绑定关系
     */
    private Map<String, Integer> loadChannelBindings(List<Integer> tplIds) {
        if (CollectionUtils.isEmpty(tplIds)) {
            return Collections.emptyMap();
        }

        return tplChannelMapper.selectByTplIdList(tplIds)
                .stream()
                .collect(Collectors.toMap(
                        bind -> bind.getChannelCode() + bind.getTplId(),
                        TopTplChannelDO::getChannelAccountId,
                        (oldId, newId) -> oldId
                ));
    }

}
