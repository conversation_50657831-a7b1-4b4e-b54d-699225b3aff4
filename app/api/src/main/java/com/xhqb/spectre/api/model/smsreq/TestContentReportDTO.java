package com.xhqb.spectre.api.model.smsreq;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * 测试上报
 *
 */
@Data
@ToString
public class TestContentReportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;

    /**
     * 短信内容
     */
    @Size(max = 256, message = "内容错误")
    @Pattern(
            regexp = "^【[^】]{1,15}】.+$",
            message = "内容错误"
    )
    private String content;

    /**
     * 渠道
     * 0：未知
     * 1：正常
     * 2 : 拦截
     */
    @NotNull(message = "状态无效")
    @Range(min = 0, max = 2, message = "状态无效")
    private Integer status;

    /**
     * 手机品牌
     */
    @Size(max = 64, message = "参数错误")
    private String brand;

    /**
     * 设备型号
     */
    @Size(max = 64, message = "参数错误")
    private String deviceModel;

    /**
     * 系统
     */
    @Size(max = 64, message = "参数错误")
    private String clientVersion;

    public String toJson() {
        return JSON.toJSONString(this);
    }
}
