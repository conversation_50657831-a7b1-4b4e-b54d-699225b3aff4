package com.xhqb.spectre.api.controller;

import com.xhqb.spectre.api.controller.common.CommonResult;
import com.xhqb.spectre.api.controller.common.ResultCode;
import com.xhqb.spectre.api.model.entity.MarketSceneVO;
import com.xhqb.spectre.api.model.entity.OrderStatusVO;
import com.xhqb.spectre.api.model.entity.ProjectDescVO;
import com.xhqb.spectre.api.model.smsreq.OrderStatusDTO;
import com.xhqb.spectre.api.service.MarketSceneService;
import com.xhqb.spectre.api.service.ProjectDescService;
import com.xhqb.spectre.api.service.SmsOrderStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 公共配置接口
 */
@RestController
@RequestMapping("/api/spectre/v3")
@Slf4j
public class SpectreCommonApiController {

    @Resource
    private ProjectDescService projectDescService;

    @Resource
    private MarketSceneService marketSceneService;

    @Resource
    private SmsOrderStatusService smsOrderStatusService;

    /**
     * 查询项目用途接口
     *
     * @return
     */
    @GetMapping("/queryProjectDesc")
    public CommonResult<List<ProjectDescVO>> queryProjectDesc() {
        log.info("获取项目用途列表");
        List<ProjectDescVO> list = projectDescService.listAll(1);
        return CommonResult.success(list);
    }

    /**
     * 查询项目用途接口
     *
     * @return
     */
    @GetMapping("/queryMarketScene")
    public CommonResult<List<MarketSceneVO>> queryMarketScene() {
        log.info("获取营销场景列表");
        List<MarketSceneVO> list = marketSceneService.listAll(1);
        return CommonResult.success(list);
    }

    @PostMapping("/queryOrderStatus")
    public CommonResult<List<OrderStatusVO>> queryOrderStatus(@RequestBody OrderStatusDTO dto) {
        log.info("获取营销短信发送状态列表; requestId={}", dto.getRequestId());
        if (dto.getMobiles() == null || dto.getMobiles().size() > 1000) {
            return CommonResult.failed(ResultCode.VALIDATE_FAILED);
        }
        List<OrderStatusVO> list = smsOrderStatusService.queryOrderStatus(dto);
        return CommonResult.success(list);
    }
}
