package com.xhqb.spectre.api.service;

import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.xhqb.spectre.api.model.entity.MobileStatus;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/25
 * 获取手机号码状态
 */
public interface TencentApiService {
    Long getPhoneStatus(String phone);

    String getPlaintext() throws TencentCloudSDKException;

    List<MobileStatus> checkMobileStatus(List<String> phones);
}
