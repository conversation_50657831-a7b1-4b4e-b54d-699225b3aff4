package com.xhqb.spectre.api.service.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.api.cache.MobileBlacklistCache;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.service.MemoryHandleService;
import com.xhqb.spectre.api.service.MobileBlackService;
import com.xhqb.spectre.api.service.OpTimeService;
import com.xhqb.spectre.api.utils.CommonUtil;
import com.xhqb.spectre.api.utils.DateTimeUtil;
import com.xhqb.spectre.api.utils.DateUtil;
import com.xhqb.spectre.api.utils.MemoryContainerUtil;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.dto.*;
import com.xhqb.spectre.common.dal.entity.*;
import com.xhqb.spectre.common.dal.entity.test.TestTplDO;
import com.xhqb.spectre.common.dal.mapper.*;
import com.xhqb.spectre.common.dal.query.AppQuery;
import com.xhqb.spectre.common.enums.DeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 需要缓存的数据处理service
 */
@Slf4j
@Service
public class MemoryHandleServiceImpl implements MemoryHandleService {

    Logger logger = LoggerFactory.getLogger(MemoryHandleServiceImpl.class);

    private static String PHONE_DAT_FILE_HTTP_PATH = "http://pkg.xhdev.xyz/download/zdata/phone/qqzeng-phone.dat";

    @Resource
    private AppMapper appMapper;

    @Resource
    private TplMapper tplMapper;

    @Resource
    private SignMapper signMapper;

    @Resource
    private TplChannelMapper tplChannelMapper;

    @Resource
    private TplDisableMapper tplDisableMapper;

    @Resource
    private ChannelAccountDisableMapper accountDisableMapper;

    @Resource
    private MobileWhiteMapper mobileWhiteMapper;

    @Resource
    private AppSendLimitMapper appSendLimitMapper;

    @Resource
    private MobileBlackMapper mobileBlackMapper;

    private static final int PAGE_SIZE = 1000;

    @Resource
    private OpTimeService opTimeService;

    @Resource
    private SmsTypeDisableMapper smsTypeDisableMapper;

    @Resource
    private TestTplMapper testTplMapper;

    @Autowired
    private MobileBlackService mobileBlackService;

    @Autowired
    private MobileBlacklistCache mobileBlacklistCache;

    @Override
    public void refreshTime() {
        Long currentTime = System.currentTimeMillis() / 1000L;
//        logger.info("系统启动时间：{}", DateTimeUtil.longToString(currentTime));
        List<OpTimeDO> opTimeDOList = opTimeService.findAllOptime();
        if (opTimeDOList != null && opTimeDOList.size() > 0) {
            for (OpTimeDO opTimeDO : opTimeDOList
            ) {
                if (opTimeDO.getOpTime() != null && opTimeDO.getModule() != null) {
                    switch (opTimeDO.getModule()) {
                        case OpLogConstant.MODULE_APP:
                            MemoryContainerUtil.setAppUpdateTime(opTimeDO.getOpTime());
//                            logger.info("module {} 更新时间 {}", OpLogConstant.MODULE_APP, MemoryContainerUtil.getAppUpdateTime());
                            break;
                        case OpLogConstant.MODULE_TPL:
                            MemoryContainerUtil.setTplUpdateTime(opTimeDO.getOpTime());
//                            logger.info("module {} 更新时间 {}", OpLogConstant.MODULE_TPL, MemoryContainerUtil.getTplUpdateTime());
                            break;
                        case OpLogConstant.MODULE_MOBILE_BLACK:
                            MemoryContainerUtil.setBlackUpdateTime(opTimeDO.getOpTime());
//                            logger.info("module {} 更新时间 {}", OpLogConstant.MODULE_MOBILE_BLACK, MemoryContainerUtil.getBlackUpdateTime());
                            break;
                        case OpLogConstant.MODULE_MOBILE_WHITE:
                            MemoryContainerUtil.setWhiteUpdateTime(opTimeDO.getOpTime());
//                            logger.info("module {} 更新时间 {}", OpLogConstant.MODULE_MOBILE_WHITE, MemoryContainerUtil.getWhiteUpdateTime());
                            break;
                        case OpLogConstant.MODULE_CHANNEL_ACCOUNT_DISABLE:
                            MemoryContainerUtil.setChannelUpdateTime(opTimeDO.getOpTime());
//                            logger.info("module {} 更新时间 {}", OpLogConstant.MODULE_CHANNEL_ACCOUNT_DISABLE, MemoryContainerUtil.getChannelUpdateTime());
                            break;
                        case OpLogConstant.MODULE_SEND_LIMIT:
                            MemoryContainerUtil.setSendLimitUpdateTime(opTimeDO.getOpTime());
//                            logger.info("module {} 更新时间 {}", OpLogConstant.MODULE_SEND_LIMIT, MemoryContainerUtil.getSendLimitUpdateTime());
                            break;
                        case OpLogConstant.MODULE_SMS_TYPE_DISABLE:
                            MemoryContainerUtil.setSmsTypeDisabledUpdateTime(opTimeDO.getOpTime());
                        default:
                            break;
                    }
                }
            }
        }
    }

    @Override
    public void refreshAppInfo() {
        List<AppDO> appDOList = appMapper.selectByQuery(new AppQuery());
        if (CollectionUtils.isEmpty(appDOList)) {
            MemoryContainerUtil.remove(MemoryContainerUtil.APP_KEY);
            return;
        }
        Map<String, AppData> map = new HashMap<>();
        for (AppDO appDo : appDOList) {
            map.put(appDo.getCode(), buildAppData(appDo));
        }
        MemoryContainerUtil.put(MemoryContainerUtil.APP_KEY, map);
    }

    @Override
    public void refreshTplInfo() {
        List<TplDO> tplDOList = tplMapper.selectAllEnabled();
        if (CollectionUtils.isEmpty(tplDOList)) {
            //没有可用模板，删除所有缓存模板
            MemoryContainerUtil.remove(MemoryContainerUtil.TPL_KEY);
            return;
        }
        Map<String, TplData> map = new HashMap<>();
        for (TplDO tplDO : tplDOList) {
            TplData tplData = buildTplCacheData(tplDO);
            map.put(tplData.getCode() + SmsApiApplicationConstant.TPL_DATA_SPLIT_STR + tplData.getSignCode(), tplData);
        }
        MemoryContainerUtil.put(MemoryContainerUtil.TPL_KEY, map);

    }

    @Override
    public void refreshChannelAccountDisable() {
        List<ChannelAccountDisableDO> accountDisableDOList = accountDisableMapper.selectAll();
        if (CollectionUtils.isEmpty(accountDisableDOList)) {
            MemoryContainerUtil.remove(MemoryContainerUtil.CHANNEL_ACCOUNT_KEY);
            return;
        }
        Map<String, List<ChannelAccountDisableData>> dataMap = new HashMap<>();
        for (ChannelAccountDisableDO item : accountDisableDOList) {
            if (new Date().after(DateTime.of(item.getMaskEndTime()))) {
                //过滤掉结束时间小于当前时间的数据
                continue;
            }
            String channelAccountId = String.valueOf(item.getChannelAccountId());
            ChannelAccountDisableData data = buildDisableData(item);
            if (dataMap.containsKey(channelAccountId)) {
                dataMap.get(channelAccountId).add(data);
            } else {
                List<ChannelAccountDisableData> tmpList = new ArrayList<>();
                tmpList.add(data);
                dataMap.put(channelAccountId, tmpList);
            }
        }
        MemoryContainerUtil.put(MemoryContainerUtil.CHANNEL_ACCOUNT_KEY, dataMap);

    }

    private ChannelAccountDisableData buildDisableData(ChannelAccountDisableDO item) {
        return ChannelAccountDisableData.builder()
                .channelAccountId(item.getChannelAccountId())
                .ispList(CommonUtil.strToList(item.getIsps()))
                .areaList(StringUtils.isEmpty(item.getAreas()) ? Collections.emptyList() : JSON.parseArray(item.getAreas(), AreaData.class))
                .startTime(DateTime.of(item.getMaskStartTime()).toString())
                .endTime(DateTime.of(item.getMaskEndTime()).toString())
                .disableType(item.getDisableType())
                .periodStartTime(item.getPeriodStartTime())
                .periodEndTime(item.getPeriodEndTime())
                .build();
    }

    @Override
    public void refreshMobileWhite() {
        List<MobileWhiteDO> whiteDOList = mobileWhiteMapper.loadAllWhiteInfo();
        if (CollectionUtils.isEmpty(whiteDOList)) {
            MemoryContainerUtil.remove(MemoryContainerUtil.WHITE_KEY);
            return;
        }
        Set<String> whiteSet = whiteDOList.stream().map(item -> buildMobileWhiteValue(item)).collect(Collectors.toSet());
        MemoryContainerUtil.put(MemoryContainerUtil.WHITE_KEY, whiteSet);
    }

    private String buildMobileWhiteValue(MobileWhiteDO mobileWhiteDO) {
        return mobileWhiteDO.getAppCode() + "_" + mobileWhiteDO.getCfgType() + "_" + mobileWhiteDO.getMobile();
    }

    @Override
    public synchronized void refreshMobileBlack() {
        long startTime = System.currentTimeMillis();
        Date now = new Date();
        Integer lastRefreshTime = mobileBlacklistCache.getLastRefreshTime();
        Stream<MobileBlackDO> mobileBlackDOStream = mobileBlackService
                .selectAllByLastRefresh(lastRefreshTime, 1_000);

        AtomicInteger totalCount = new AtomicInteger(0);
        mobileBlackDOStream.forEach(mobileBlackDO -> {
            if (DeleteEnum.isDeleted(mobileBlackDO.getIsDelete())) {
                mobileBlacklistCache.remove(mobileBlackDO.getSmsTypeCode(),
                        mobileBlackDO.getMobile(),
                        mobileBlackDO.getAppCode());
            } else if (Objects.nonNull(mobileBlackDO.getExpiredTime()) && !now.after(mobileBlackDO.getExpiredTime())) {
                mobileBlacklistCache.put(mobileBlackDO.getSmsTypeCode(),
                        mobileBlackDO.getMobile(),
                        mobileBlackDO.getAppCode(),
                        mobileBlackDO.getExpiredTime().getTime());
            }
            totalCount.incrementAndGet();
        });

        log.info("黑名单缓存刷新完成, lastRefreshTime:{},cost:{},size:{}", lastRefreshTime,
                (System.currentTimeMillis() - startTime), totalCount);
    }

    private String buildCacheValue(String smsTypeCode, String mobile, String appCode) {
        String key = smsTypeCode + "_" + mobile;
        if (StringUtils.isBlank(appCode)) {
            return key;
        }
        return key + "_" + appCode;
    }

    @Override
    public void refreshAppSendLimit() {
        List<AppSendLimitDO> sendLimitDOList = appSendLimitMapper.selectAllEnabled();
        if (CollectionUtils.isEmpty(sendLimitDOList)) {
            MemoryContainerUtil.remove(MemoryContainerUtil.SEND_LIMIT_KEY);
            return;
        }
        Map<String, List<AppSendLimitData>> dataMap = new HashMap<>();
        for (AppSendLimitDO item : sendLimitDOList) {
            String appCode = item.getAppCode();
            AppSendLimitData data = AppSendLimitData.builder()
                    .appCode(appCode)
                    .limitKey(item.getSendLimitKey())
                    .limitValue(item.getSendLimitValue())
                    .build();
            if (dataMap.containsKey(appCode)) {
                dataMap.get(appCode).add(data);
            } else {
                List<AppSendLimitData> tmpList = new ArrayList<>();
                tmpList.add(data);
                dataMap.put(appCode, tmpList);
            }
        }
        MemoryContainerUtil.put(MemoryContainerUtil.SEND_LIMIT_KEY, dataMap);
    }

    private AppData buildAppData(AppDO appDO) {
        return AppData.builder()
                .code(appDO.getCode())
                .name(appDO.getName())
                .skey(appDO.getSkey())
                .cbUrl(appDO.getCbUrl())
                .contentApiType(appDO.getContentApiType())
                .build();
    }

    private TplData buildTplCacheData(TplDO tplDO) {
        //签名
        SignDO signDO = signMapper.selectByPrimaryKey(tplDO.getSignId());
        //渠道信息
        List<TplChannelData> channelList = tplChannelMapper.selectByTplId(tplDO.getId()).stream().map(item -> TplChannelData.builder()
                        .channelAccountId(item.getChannelAccountId())
                        .channelTplId(item.getChannelTplId())
                        .ispList(CommonUtil.strToList(item.getIsps()))
                        .areaFilterType(item.getAreaFilterType())
                        .areaList(StringUtils.isEmpty(item.getAreas()) ? Collections.emptyList() : JSON.parseArray(item.getAreas(), AreaData.class))
                        .weight(item.getWeight())
                        .remark(item.getRemark())
                        .tplContent(item.getTplContent())
                        .build())
                .collect(Collectors.toList());
        //模板屏蔽信息
        List<TplDisableData> disableList = tplDisableMapper.selectByTplId(tplDO.getId()).stream().map(item -> TplDisableData.builder()
                        .ispList(CommonUtil.strToList(item.getIsps()))
                        .areaList(StringUtils.isEmpty(item.getAreas()) ? Collections.emptyList() : JSON.parseArray(item.getAreas(), AreaData.class))
                        .startTime(DateTimeUtil.intToString(item.getStartTime()))
                        .endTime(DateTimeUtil.intToString(item.getEndTime()))
                        .build())
                .collect(Collectors.toList());

        return TplData.builder()
                .code(tplDO.getCode())
                .smsTypeCode(tplDO.getSmsTypeCode())
                .appCode(tplDO.getAppCode())
                .signId(tplDO.getSignId())
                .signCode(signDO.getCode())
                .signName(signDO.getName())
                .content(tplDO.getContent())
                .paramCount(CommonUtil.getTplParamCount(tplDO.getContent()))
                .channelInfoList(channelList)
                .disableInfoList(disableList)
                .tag(tplDO.getTag())
                .build();
    }


    @Override
    public void refreshPhoneSearch() {
        getHttpFile();
    }

    private void getHttpFile() {
        try {
            URL url = new URL(PHONE_DAT_FILE_HTTP_PATH);
            byte[] data = download(url);
            MemoryContainerUtil.put(MemoryContainerUtil.PHONE_DATA, data);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private byte[] download(URL url) throws IOException {
        URLConnection uc = url.openConnection();
        int len = uc.getContentLength();
        InputStream is = new BufferedInputStream(uc.getInputStream());
        try {
            byte[] data = new byte[len];
            int offset = 0;
            while (offset < len) {
                int read = is.read(data, offset, data.length - offset);
                if (read < 0) {
                    break;
                }
                offset += read;
            }
            if (offset < len) {
                throw new IOException(
                        String.format("Read %d bytes; expected %d", offset, len));
            }
            return data;
        } finally {
            is.close();
        }
    }

    /**
     * 将短信类型屏蔽信息数据放到内存
     */
    @Override
    public void refreshSmsTypeDisabled() {
        List<SmsTypeDisableDO> smsTypeDisableDOList = smsTypeDisableMapper.selectAll();
        if (CollectionUtils.isEmpty(smsTypeDisableDOList)) {
            MemoryContainerUtil.remove(MemoryContainerUtil.SMS_TYPE_DISABLED_KEY);
            return;
        }
        Map<String, List<SmsTypeDisableData>> dataMap = new HashMap<>();
        for (SmsTypeDisableDO item : smsTypeDisableDOList) {
            if (new Date().after(DateTimeUtil.intToDate(item.getEndTime()))) {
                //过滤掉结束时间小于当前时间的数据
                continue;
            }
            String smsTypeCode = String.valueOf(item.getSmsTypeCode());
            SmsTypeDisableData data = buildSmsTypeDisabledData(item);
            if (dataMap.containsKey(smsTypeCode)) {
                dataMap.get(smsTypeCode).add(data);
            } else {
                List<SmsTypeDisableData> tmpList = new ArrayList<>();
                tmpList.add(data);
                dataMap.put(smsTypeCode, tmpList);
            }
        }
        MemoryContainerUtil.put(MemoryContainerUtil.SMS_TYPE_DISABLED_KEY, dataMap);
    }

    @Override
    public void refreshChannelTestTpl() {
        List<TestTplDO> modelTestTplList = testTplMapper.selectAll();
        if (CollectionUtils.isEmpty(modelTestTplList)) {
            MemoryContainerUtil.remove(MemoryContainerUtil.CHANNEL_TEST_TPL);
            return;
        }
        Map<String, ChannelTestData> map = new HashMap<>();
        for (TestTplDO testTplDO : modelTestTplList) {
            ChannelTestData channelTestData = buildChannelTestData(testTplDO);
            map.put(channelTestData.getSmsTplCode() + SmsApiApplicationConstant.TPL_DATA_SPLIT_STR + channelTestData.getSignCode(), channelTestData);
        }
        MemoryContainerUtil.put(MemoryContainerUtil.CHANNEL_TEST_TPL, map);
    }

    private ChannelTestData buildChannelTestData(TestTplDO testTplDO) {
        // TplDO
        TplDO tplDO = tplMapper.selectByPrimaryKey(testTplDO.getSmsTplId());
        // SignDO
        SignDO signDO = signMapper.selectByPrimaryKey(tplDO.getSignId());
        // TplChannelData
        return ChannelTestData.builder()
                .id(testTplDO.getId())
                .smsTplId(testTplDO.getSmsTplId())
                .smsTplCode(tplDO.getCode())
                .signId(tplDO.getSignId())
                .signCode(signDO.getCode())
                .signName(signDO.getName())
                .maxTimes(testTplDO.getMaxTimes())
                .timePeriod(testTplDO.getTimePeriod())
                .senderLevel(testTplDO.getSenderLevel())
                .build();
    }

    private SmsTypeDisableData buildSmsTypeDisabledData(SmsTypeDisableDO smsTypeDisableDO) {
        return SmsTypeDisableData.builder()
                .smsTypeCode(smsTypeDisableDO.getSmsTypeCode())
                .ispList(CommonUtil.strToList(smsTypeDisableDO.getIsps()))
                .areaList(StringUtils.isEmpty(smsTypeDisableDO.getAreas()) ? Collections.emptyList() : JSON.parseArray(smsTypeDisableDO.getAreas(), AreaData.class))
                .startTime(DateTimeUtil.intToString(smsTypeDisableDO.getStartTime()))
                .endTime(DateTimeUtil.intToString(smsTypeDisableDO.getEndTime()))
                .build();
    }
}
