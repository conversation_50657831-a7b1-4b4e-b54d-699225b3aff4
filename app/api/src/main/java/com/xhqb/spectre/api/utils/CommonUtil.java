package com.xhqb.spectre.api.utils;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.model.entity.AreaVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.beans.PropertyDescriptor;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtil {

    private static final Pattern TPL_PARAM_PATTERN = Pattern.compile("\\[\\*]");
    private static final Pattern TPL_PARAM_PATTERN2 = Pattern.compile(SmsApiApplicationConstant.TPL_CONTENT_PARAM2);

    private static final char[] ALL_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890".toCharArray();

    private static final String UNKNOWN = "unknown";

    /**
     * string转为List结构
     *
     * @param isps 逗号分隔的字符串
     * @return
     */
    public static List<String> strToList(String isps) {
        return StringUtils.isEmpty(isps) ? Collections.emptyList() : Arrays.asList(isps.split(","));
    }

    /**
     * 地域信息转为List结构
     *
     * @param areas
     * @return
     */
    public static List<AreaVO> areaToList(String areas) {
        return StringUtils.isEmpty(areas) ? Collections.emptyList() : JSON.parseArray(areas, AreaVO.class);
    }

    /**
     * 获取短信模板参数个数
     *
     * @param tplContent
     * @return
     */
    public static Integer getTplParamCount(String tplContent) {
        Integer count = 0;
        Matcher matcher = TPL_PARAM_PATTERN.matcher(tplContent);
        while (matcher.find()) {
            count++;
        }
        if (count == 0) {
            matcher = TPL_PARAM_PATTERN2.matcher(tplContent);
            while (matcher.find()) {
                count++;
            }
        }
        return count;
    }

    /**
     * 获取IP地址
     *
     * @return
     */
    public static String getIpAddress() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }


    /**
     * 生成62进制的随机字符串
     *
     * @param length
     * @return
     */
    public static String getRandStr(int length) {
        StringBuffer sb = new StringBuffer();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            sb.append(ALL_CHARS[random.nextInt(62)]);
        }
        return sb.toString();
    }

    public static String maskMobile(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return "";
        }
        return mobile.replaceAll("(\\d{3})\\d+(\\d{4})", "$1****$2");
    }

    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }
}
