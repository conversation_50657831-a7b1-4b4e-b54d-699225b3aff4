package com.xhqb.spectre.api.model.entity;

import com.xhqb.spectre.api.constant.SMSSendFailTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Optional;

/**
 * <AUTHOR>
 * @data 2021/11/24
 * 发送失败手机号码model
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class ErrorPhone implements Serializable {

    private static final long serialVersionUID = -1853037868595661967L;
    private String phone;
    private String errorCode;
    private String errorMsg;

    public static ErrorPhone buildErrorPhone(MobileStatus mobileStatus) {
        return Optional.ofNullable(mobileStatus).map(item -> {
            ErrorPhone apiDO = ErrorPhone.builder()
                    .phone(mobileStatus.getMobile())
                    .errorCode(String.valueOf(SMSSendFailTypeEnum.SMS_SEND_TYPE_PHONE_NOT_VALID.getStatusCode()))
                    .errorMsg(SMSSendFailTypeEnum.SMS_SEND_TYPE_PHONE_NOT_VALID.getStatusMsg())
                    .build();
            return apiDO;
        }).orElse(null);
    }

}
