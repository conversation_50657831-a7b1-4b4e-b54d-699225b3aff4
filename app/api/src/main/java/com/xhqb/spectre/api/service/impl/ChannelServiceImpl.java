package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.model.smsresp.ChannelEnumVO;
import com.xhqb.spectre.api.service.ChannelService;
import com.xhqb.spectre.common.dal.mapper.ChannelMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChannelServiceImpl implements ChannelService {

    @Autowired
    private ChannelMapper channelMapper;

    /**
     * 查询渠道枚举
     *
     * @return
     */
    @Override
    public List<ChannelEnumVO> queryEnum() {
        return channelMapper.selectEnum().stream()
                .map(ChannelEnumVO::buildVO)
                .collect(Collectors.toList());
    }
}
