package com.xhqb.spectre.api.service.delayjob.message;

import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.common.mq.MessageMQ;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class NotifyDelayingMessage extends DelayingMessage<MessageMQ<BaseBodyMessage>> {
    private static final long serialVersionUID = 1L;

    // MessageMQ<NotifyBodyMessage> messageMQ;
}
