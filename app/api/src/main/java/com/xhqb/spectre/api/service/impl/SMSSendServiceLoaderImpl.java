package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.model.smsreq.BaseSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.ContentSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.SingleSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.SingleTestSMSReqDTO;
import com.xhqb.spectre.api.service.AssemblerSMSService;
import com.xhqb.spectre.api.service.SMSSendServiceLoader;
import com.xhqb.spectre.api.service.impl.assembler.ContentSMSAssemblerServiceImpl;
import com.xhqb.spectre.api.service.impl.assembler.SingleSMSAssemblerServiceImpl;
import com.xhqb.spectre.api.service.impl.assembler.SingleTestSMSAssemblerServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 根据request加载具体的短信发送实现
 */
@Component
public class SMSSendServiceLoaderImpl implements SMSSendServiceLoader {

    private static final Logger LOGGER = LoggerFactory.getLogger(SMSSendServiceLoaderImpl.class);

    @Resource
    SingleSMSAssemblerServiceImpl singleSMSSenderService;

    @Resource
    ContentSMSAssemblerServiceImpl contentSMSAssemblerService;

    @Resource
    SingleTestSMSAssemblerServiceImpl singleTestSMSAssemblerService;

    @Override
    public AssemblerSMSService loadEndpoint(BaseSMSReqDTO request) {

        if (request instanceof SingleSMSReqDTO) {
            return singleSMSSenderService;
        }else if (request instanceof ContentSMSReqDTO){
            return contentSMSAssemblerService;
        }else if (request instanceof SingleTestSMSReqDTO){
            return singleTestSMSAssemblerService;
        }
        LOGGER.error("未知请求request={}", request);
        return null;
    }
}
