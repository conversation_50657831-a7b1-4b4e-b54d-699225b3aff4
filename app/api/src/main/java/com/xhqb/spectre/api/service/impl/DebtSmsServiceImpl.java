package com.xhqb.spectre.api.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.xhqb.spectre.api.aspect.RedisLuaScript;
import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.service.DebtSmsService;
import com.xhqb.spectre.common.dal.entity.DebtSmsReportDO;
import com.xhqb.spectre.common.dal.mapper.DebtSmsReportMapper;
import com.xhqb.spectre.common.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

/**
 * 债转短信特殊处理
 *
 * <AUTHOR>
 * @date 2024/10/25
 */
@Service
@Slf4j
public class DebtSmsServiceImpl implements DebtSmsService {

    private static final String KEY_PREFIX = "spectre:api:debt:sms";

    @Resource
    private VenusConfig venusConfig;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private DebtSmsReportMapper debtSmsReportMapper;

    @Override
    public void debtSmsProcess(Integer submitAmount, Integer validAmount, String tplCode, String bizBatchId) {
        if (!venusConfig.isDebtSmsTplCode(tplCode)) {
            // 不是债转短信 不做任何处理
            return;
        }
        try {
            doDebtSmsProcess(submitAmount, validAmount, tplCode, bizBatchId);
        } catch (Exception e) {
            log.warn("债转短信记录相关信息处理失败,tplCode = {}, bizBatchId ={}, submitAmount ={}, validAmount ={}",
                    tplCode, bizBatchId, submitAmount, validAmount, e);
        }
    }

    private void doDebtSmsProcess(Integer submitAmount, Integer validAmount, String tplCode, String bizBatchId) {
        final Date now = new Date();
        long expireSeconds = DateUtil.between(now, DateUtil.endOfDay(now), DateUnit.SECOND);
        String prefix = KEY_PREFIX + ":" + DateUtil.format(now, "yyyyMMdd");
        String submitAmountKKey = prefix + ":submit:" + tplCode + ":" + bizBatchId;
        String validAmountKey = prefix + ":valid:" + tplCode + ":" + bizBatchId;
        String saveKey = prefix + ":save:" + tplCode + ":" + bizBatchId;
        Object[] values = {CommonUtil.nullToZero(submitAmount) + "", CommonUtil.nullToZero(validAmount) + "", expireSeconds + ""};
        // 债转短信相关处理
        Long result = stringRedisTemplate.execute(RedisLuaScript.debtSmsIncr, Arrays.asList(submitAmountKKey, validAmountKey, saveKey), values);
        if (Objects.nonNull(result) && Objects.equals(result, 1L)) {
            // 保存债转批次信息 (暂时直接保存... 数据量比较少,若未来发现数据量比较多 可以采用异步的方式做数据存储)
            log.info("保存债转短信批次信息,tplCode = {}, bizBatchId ={}, submitAmount ={}, validAmount ={}",
                    tplCode, bizBatchId, submitAmount, validAmount);
            saveDebtSmsReport(tplCode, bizBatchId, saveKey);
        }
    }

    private void saveDebtSmsReport(String tplCode, String bizBatchId, String saveKey) {
        try {
            DebtSmsReportDO debtSmsReport = new DebtSmsReportDO();
            debtSmsReport.setTplCode(tplCode);
            debtSmsReport.setBizBatchId(bizBatchId);
            debtSmsReport.setReportDate(new Date());
            DateTime dateTime = DateUtil.offsetMinute(new Date(), venusConfig.getDebtSmsRetryIntervalMinutes());
            int startTime = Math.toIntExact(dateTime.getTime() / 1000);
            debtSmsReport.setStartTime(startTime);
            // 债转短信报表数据存储
            debtSmsReportMapper.insertSelective(debtSmsReport);
        } catch (Exception e) {
            log.warn("保存债转短信报表信息失败,tplCode = {}, bizBatchId ={}", tplCode, bizBatchId, e);
            stringRedisTemplate.delete(saveKey);
        }
    }
}
