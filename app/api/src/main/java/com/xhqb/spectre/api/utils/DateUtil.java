/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xhqb.spectre.api.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * DateUtils.
 */
@Slf4j
public class DateUtil {

    private static final String DATE_FORMAT_DATETIME = "yyyy-MM-dd HH:mm:ss";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DATE_FORMAT_DATETIME);

    /**
     * yyyy-MM-dd 格式化
     */
    private static final ThreadLocal<SimpleDateFormat> SMALL_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));
    /**
     * yyyy-MM-dd HH:mm:ss 格式化
     */
    private static final ThreadLocal<SimpleDateFormat> FULL_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    /**
     * yyyy-MM-dd HH:00:00 格式化
     */
    private static final ThreadLocal<SimpleDateFormat> DATE_HOUR_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:00:00"));

    /**
     * yyyy-MM 格式化
     */
    private static final ThreadLocal<SimpleDateFormat> MONTH_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM"));

    /**
     * parseLocalDateTime.
     * out put format:yyyy-MM-dd HH:mm:ss
     *
     * @param dataTime date String
     * @return yyyy -MM-dd HH:mm:ss
     * @see LocalDateTime
     */
    public static LocalDateTime parseLocalDateTime(final String dataTime) {
        return LocalDateTime.parse(dataTime, DateTimeFormatter.ofPattern(DATE_FORMAT_DATETIME));
    }

    /**
     * Parse local date time local date time.
     *
     * @param dataTime          the data time
     * @param dateTimeFormatter the date time formatter
     * @return the local date time
     */
    public static LocalDateTime parseLocalDateTime(final String dataTime, final String dateTimeFormatter) {
        return LocalDateTime.parse(dataTime, DateTimeFormatter.ofPattern(dateTimeFormatter));
    }

    /**
     * acquireMinutesBetween.
     *
     * @param start this is start date.
     * @param end   this is start date.
     * @return The number of days between start and end, if end is after start, returns a positive number, otherwise returns a negative number
     */
    public static long acquireMinutesBetween(final LocalDateTime start, final LocalDateTime end) {
        return start.until(end, ChronoUnit.MINUTES);
    }

    /**
     * Acquire millis between long.
     *
     * @param start the start
     * @param end   the end
     * @return the long
     */
    public static long acquireMillisBetween(final LocalDateTime start, final LocalDateTime end) {
        return start.until(end, ChronoUnit.MILLIS);
    }

    /**
     * Format local date time from timestamp local date time.
     *
     * @param timestamp the timestamp
     * @return the local date time
     */
    public static LocalDateTime formatLocalDateTimeFromTimestamp(final Long timestamp) {
        return LocalDateTime.ofEpochSecond(timestamp / 1000, 0, ZoneOffset.ofHours(8));
    }

    /**
     * Format local date time from timestamp by system time zone.
     *
     * @param timestamp the timestamp
     * @return the local date time
     */
    public static LocalDateTime formatLocalDateTimeFromTimestampBySystemTimezone(final Long timestamp) {
        return LocalDateTime.ofEpochSecond(timestamp / 1000, 0, OffsetDateTime.now().getOffset());
    }

    /**
     * Format local date time to string.
     * use default pattern yyyy-MM-dd HH:mm:ss
     *
     * @param localDateTime the localDateTime
     * @return the format string
     */
    public static String localDateTimeToString(final LocalDateTime localDateTime) {
        return DATE_TIME_FORMATTER.format(localDateTime);
    }

    /**
     * Format local date time to string.
     *
     * @param localDateTime the localDateTime
     * @param pattern       formatter pattern
     * @return the format string
     */
    public static String localDateTimeToString(final LocalDateTime localDateTime, final String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(formatter);
    }

    public static String dateToString(Date date) {
        return FULL_FORMAT.get().format(date);
    }

    public static Date stringToDate(String dateTimeStr) {
        try {
            return FULL_FORMAT.get().parse(dateTimeStr);
        } catch (ParseException e) {
            log.warn("stringToDate ParseException, dateTimeStr: {}", dateTimeStr, e);
            return null;
        }
    }

    public static Date hourToDate(String hour) {
        try {
            return DATE_HOUR_FORMAT.get().parse(hour);
        } catch (ParseException e) {
            log.warn("hourToDate ParseException, hour: {}", hour, e);
            return null;
        }
    }

    /**
     * int 时间搓转日期
     *
     * @param timestamp
     * @return
     */
    public static Date intToDate(Integer timestamp) {
        return Objects.isNull(timestamp) || timestamp <= 0 ? null : new Date((long) timestamp * 1000);
    }

    /**
     * 日期转date
     *
     * @param date
     * @return
     */
    public static Integer dateToInt(Date date) {
        return Objects.isNull(date) ? 0 : (int) (date.getTime() / 1000);
    }

    /**
     * 日期转date
     *
     * @param date
     * @return
     */
    public static Long dateToSendTimeLong(Date date) {
        return Objects.isNull(date) ? 0L : (date.getTime() / 1000);
    }

    /**
     * 时间戳转换为时间格式
     *
     * @param time
     * @return
     */
    public static String intToString(Integer time) {
        if (Objects.isNull(time) || time == 0) {
            return "";
        }
        return FULL_FORMAT.get().format(new Date((long) time * 1000));
    }

    /**
     * 时间格式转换为时间戳
     *
     * @param dateTime
     * @return
     */
    public static Integer stringToInt(String dateTime) {
        Date date = stringToDate(dateTime);
        if (Objects.isNull(date)) {
            return 0;
        }
        return (int) (date.getTime() / 1000);
    }

    /**
     * 获取到当前时间
     *
     * @return 返回秒
     */
    public static Integer getNow() {
        return (int) (System.currentTimeMillis() / 1000);
    }

    /**
     * 将日期转换成 yyyy-MM-dd
     *
     * @param date
     * @return
     */
    public static String smallToStr(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return SMALL_FORMAT.get().format(date);
    }

    /**
     * 将 yyyy-MM-dd 转换成日期
     *
     * @param small
     * @return
     */
    public static Date smallToDate(String small) {
        if (StringUtils.isBlank(small)) {
            return null;
        }
        try {
            return SMALL_FORMAT.get().parse(small);
        } catch (ParseException e) {
            log.warn("smallToDate ParseException, small: {}", small, e);
            return null;
        }
    }

    /**
     * 将日期转换成 yyyy-MM-dd HH:mm:ss
     *
     * @param date
     * @return
     */
    public static String fullToStr(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return FULL_FORMAT.get().format(date);
    }

    /**
     * 判断小时区间
     *
     * @param startHour
     * @param endHour
     * @return
     */
    public static boolean betweenHour(int startHour, int endHour) {
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        return hour >= startHour && hour < endHour;
    }

    /**
     * 补全日期（日期格式：yyyy-MM-dd）
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<String> completeDateList(String startDate, String endDate) {
        SimpleDateFormat sdf = SMALL_FORMAT.get();
        List<String> dateList = new ArrayList<>();
        try {
            dateList.add(startDate);
            Calendar cStart = Calendar.getInstance();
            cStart.setTime(sdf.parse(startDate));
            Date end = sdf.parse(endDate);
            while (end.after(cStart.getTime())) {
                //根据日历的规则，为给定的日历字段添加或减去指定的时间量
                cStart.add(Calendar.DAY_OF_MONTH, 1);
                dateList.add(sdf.format(cStart.getTime()));
            }
        } catch (Exception e) {
            log.warn("completeDateList exception", e);
        }
        return dateList;
    }

    /**
     * 补全小时数据（日期格式：yyyy-MM-dd HH:00:00）
     *
     * @param startHour
     * @param endHour
     * @return
     */
    public static List<String> completeHourList(String startHour, String endHour) {
        List<String> hourList = new ArrayList<>();
        try {
            DateFormat dateFormat = DATE_HOUR_FORMAT.get();
            hourList.add(startHour);
            Calendar cStart = Calendar.getInstance();
            cStart.setTime(dateFormat.parse(startHour));
            Date end = dateFormat.parse(endHour);
            while (end.after(cStart.getTime())) {
                //根据日历的规则，为给定的日历字段添加或减去指定的时间量
                cStart.add(Calendar.HOUR_OF_DAY, 1);
                hourList.add(dateFormat.format(cStart.getTime()));
            }
        } catch (Exception e) {
            log.warn("completeDateList exception", e);
        }
        return hourList;
    }

    /**
     * 补全月份（月份格式：yyyy-MM）
     *
     * @param startMonth
     * @param endMonth
     * @return
     */
    public static List<String> completeMonthList(String startMonth, String endMonth) {
        List<String> monthList = new ArrayList<>();
        try {
            DateFormat dateFormat = MONTH_FORMAT.get();
            monthList.add(startMonth);
            Calendar cStart = Calendar.getInstance();
            cStart.setTime(dateFormat.parse(startMonth));
            Date end = dateFormat.parse(endMonth);
            while (end.after(cStart.getTime())) {
                //根据日历的规则，为给定的日历字段添加或减去指定的时间量
                cStart.add(Calendar.MONTH, 1);
                monthList.add(dateFormat.format(cStart.getTime()));
            }
        } catch (Exception e) {
            log.warn("completeMonthList exception", e);
        }
        return monthList;
    }

    /**
     * 获取月份的最后一天
     *
     * @param month yyyy-MM
     * @return yyyy-MM-dd
     */
    public static String getLastDayOfMonth(String month) {
        try {
            Calendar c = Calendar.getInstance();
            c.setTime(MONTH_FORMAT.get().parse(month));
            int lastDay = c.getActualMaximum(Calendar.DAY_OF_MONTH);
            c.set(Calendar.DAY_OF_MONTH, lastDay);

            return SMALL_FORMAT.get().format(c.getTime());
        } catch (Exception e) {
            log.warn("getLastDayOfMonth exception, month: {}", month, e);
            return null;
        }
    }

    public static long getCurrentDayLatestTimestamp() {
        long currentDayLatestTimeTime = 0;
        try {
            Date currentDayLatestTime = DateUtils.ceiling(new Date(), Calendar.DAY_OF_MONTH);
            currentDayLatestTimeTime = (currentDayLatestTime.getTime() / 1000) - 1;
        } catch (Exception e) {
            log.warn("ceiling时间转化存在异常 exception:{}", e.getMessage());
        }
        return currentDayLatestTimeTime;
    }

    public static long getNDaysAgoEarliestTimestamp(int n) {
        // 获取前n天的日期的最早时间
        long nDaysAgoEarliestTimestamp = 0;
        try {
            Date nDaysAgoDatetime = DateUtils.truncate(DateUtils.addDays(new Date(), -n), Calendar.DAY_OF_MONTH);
            nDaysAgoEarliestTimestamp = nDaysAgoDatetime.getTime() / 1000;
        } catch (Exception e) {
            log.warn("truncate时间转化存在异常 exception:{}", e.getMessage());
        }
        return nDaysAgoEarliestTimestamp;
    }
}
