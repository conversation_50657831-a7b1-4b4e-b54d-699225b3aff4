package com.xhqb.spectre.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhqb.kael.sequencegenerator.DistributedSequence;
import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.constant.ShortUrlConstant;
import com.xhqb.spectre.api.controller.common.CommonResult;
import com.xhqb.spectre.api.controller.common.ResultCode;
import com.xhqb.spectre.api.exception.GlobalException;
import com.xhqb.spectre.api.model.entity.*;
import com.xhqb.spectre.api.service.ShortUrlService;
import com.xhqb.spectre.api.service.limit.UserShortUrlLimit;
import com.xhqb.spectre.api.utils.CommonUtil;
import com.xhqb.spectre.api.utils.DateUtil;
import com.xhqb.spectre.api.utils.HttpUtils;
import com.xhqb.spectre.common.dal.entity.*;
import com.xhqb.spectre.common.dal.mapper.ShortUrlLogMapper;
import com.xhqb.spectre.common.dal.mapper.ShortUrlMapper;
import com.xhqb.spectre.common.dal.mapper.ShortUrlTplDOMapper;
import com.xhqb.spectre.common.dal.mapper.UserShortUrlDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/25 15:19
 **/
@Service
@Slf4j
public class ShortUrlServiceImpl implements ShortUrlService {

    private static final Date PERMANENT_DATE = DateUtil.smallToDate("2099-12-31");
    private static final String PERMANENT_DATE_STR = "2099-12-31 23:59:59";

    private static final String OPERATOR = "system";
    private static final String NAME_TEMPLATE = "业务Id%s活动";
    private static final String SHORT_URL_TPL_CODE_KEY = "spectre:admin:shortUrl:tpl:code";


    @Autowired
    private ShortUrlMapper shortUrlMapper;
    @Autowired
    private VenusConfig venusConfig;
    @Resource
    private ShortUrlTplDOMapper shortUrlTplDOMapper;
    @Resource
    private DistributedSequence distributedSequence;
    @Resource
    private UserShortUrlLimit userShortUrlLimit;
    @Resource
    private UserShortUrlDOMapper userShortUrlDOMapper;
    @Resource
    private ShortUrlLogMapper shortUrlLogMapper;

    @Override
    public String create(ShortUrlDTO shortUrlDTO) {

        //计算过期日期
        Date expiredDate = calExpiredDate(shortUrlDTO.getValidPeriod());

        //生成短链编码
        int shortCodeLength = venusConfig.getShortCodeLength();
        String shortCode = CommonUtil.getRandStr(shortCodeLength);
        if (Objects.nonNull(shortUrlMapper.selectByCode(shortCode))) {
            shortCode = CommonUtil.getRandStr(shortCodeLength);
        }
        //保存
        String currentUser = OPERATOR;
        ShortUrlDO shortUrlDO = ShortUrlDO.builder()
                .shortCode(shortCode)
                .srcUrl(shortUrlDTO.getSrcUrl())
                .description(shortUrlDTO.getDescription())
                .expiredDate(expiredDate)
                .status(ShortUrlConstant.STATUS_VALID)
                .type(ShortUrlConstant.API_TYPE)
                .creator(currentUser)
                .updater(currentUser)
                .build();
        try {

            shortUrlMapper.insertSelective(shortUrlDO);
        } catch (DuplicateKeyException e) {
            log.warn("短链编码重复，编码值：{}", shortCode);
            //重试一次
            shortUrlDO.setShortCode(CommonUtil.getRandStr(shortCodeLength));
            shortUrlMapper.insertSelective(shortUrlDO);
        }

        return buildShortUrl(shortUrlDO.getShortCode());

    }

    @Override
    public JSONObject createShortUrlTpl(ShortUrlTplDTO shortUrlTplDTO) {
        JSONObject jsonObject = new JSONObject();
        String tplCode = distributedSequence.nextStr(SHORT_URL_TPL_CODE_KEY);
        String bizId = shortUrlTplDTO.getBizId();
        if (StringUtils.isBlank(bizId)) {
            bizId = tplCode;
        }
        try {
            ShortUrlTplDO modelDO = shortUrlTplDOMapper.selectByBizId(bizId);
            if (Objects.nonNull(modelDO)) {
                populateResponse(jsonObject, modelDO);
                return jsonObject;
            }

            ShortUrlDTO shortUrlDTO = new ShortUrlDTO();
            shortUrlDTO.setValidPeriod(4);
            shortUrlDTO.setSrcUrl(shortUrlTplDTO.getLongUrl());
            shortUrlDTO.setDescription(String.format(NAME_TEMPLATE, bizId));
            String shortCode = innerCreate(shortUrlDTO);

            ShortUrlTplDO insertDO = ShortUrlTplDO.builder()
                    .longUrl(shortUrlTplDTO.getLongUrl())
                    .shortCode(shortCode)
                    .expiredDate(calExpiredDateByAnyDay(shortUrlTplDTO.getValidPeriod()))
                    .bizId(bizId)
                    .isEncrypt(0)
                    .tplCode(tplCode)
                    .name(String.format(NAME_TEMPLATE, bizId))
                    .creator(OPERATOR)
                    .updater(OPERATOR)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .scenario(shortUrlTplDTO.getScenario())
                    .build();
            shortUrlTplDOMapper.insertSelective(insertDO);
            populateResponse(jsonObject, insertDO);

        } catch (Exception e) {
            log.warn("创建短链模板失败|bizId:{}", shortUrlTplDTO.getBizId(), e);
        }

        return jsonObject;
    }


    @Override
    public List<UserShortUrlVO> createUserShortUrl(UserShortUrlDTO userShortUrlDTO) {
        try {
            // 根据 bizId 获取对应短链模板
            ShortUrlTplDO shortUrlTplDO = shortUrlTplDOMapper.selectByBizId(userShortUrlDTO.getBizId());
            if (Objects.isNull(shortUrlTplDO)) {
                log.warn("短链模板不存在|bizId:{}", userShortUrlDTO.getBizId());
                return CollUtil.newArrayList();
            }

            if (!userShortUrlLimit.tryAcquire(userShortUrlDTO.getUserShortUrlInfoList().size())) {
                log.warn("用户短链创建失败-新增越限|bizId:{}", userShortUrlDTO.getBizId());
                return CollUtil.newArrayList();
            }
            String tplCode = shortUrlTplDO.getTplCode();

            Map<String, Object> params = new HashMap<>(255);
            params.put("tplCode", tplCode);
            params.put("dataList", userShortUrlDTO.getUserShortUrlInfoList());

            String result;
            try {
                result = HttpUtils.doPost(getShortenerUrl(), JSON.toJSONString(params));
            } catch (Exception e) {
                log.error("创建用户短链HTTP请求失败|bizId:{}", userShortUrlDTO.getBizId(), e);
                return CollUtil.newArrayList();
            }

            log.info("创建用户短链HTTP请求结果|bizId:{}|result:{}", userShortUrlDTO.getBizId(), result);

            CommonResult<List<UserShortUrlVO>> resultCommonResult = null;
            if (StringUtils.isNotBlank(result)) {
                resultCommonResult = JSON.parseObject(result, new TypeReference<CommonResult<List<UserShortUrlVO>>>() {
                });
            }

            if (Objects.isNull(resultCommonResult) || !Objects.equals(resultCommonResult.getCode(), 0)) {
                log.warn("创建用户短链API返回结果异常|bizId:{}", userShortUrlDTO.getBizId());
                return CollUtil.newArrayList();
            }

            List<UserShortUrlVO> data = resultCommonResult.getData();
            return CollUtil.isEmpty(data) ? CollUtil.newArrayList() : data;

        } catch (Exception e) {
            log.error("创建用户短链失败|bizId:{}", userShortUrlDTO.getBizId(), e);
            return CollUtil.newArrayList();
        }
    }

    @Override
    public List<UserChannelInfoVO> queryChannelInfoByMobiles(List<String> mobileList) {
        try {
            return doQueryChannelInfoByMobiles(mobileList);
        } catch (Exception e) {
            log.warn("查询渠道信息失败", e);
        }
        return CollUtil.newArrayList();
    }

    @Override
    public UserThirtyChannelInfoVO queryPastThirtyChannels(String mobile) {
        try {
            return doQueryPastThirtyChannels(mobile);
        } catch (GlobalException e) {
            log.warn("查询渠道信息失败异常 e:{}", e.getMessage());
        } catch (Exception e) {
            log.warn("查询渠道信息失败", e);
        }
        return bulidDefaultVO(mobile);
    }

    private UserThirtyChannelInfoVO doQueryPastThirtyChannels(String mobile) {
        UserThirtyChannelInfoVO userThirtyChannelInfoVO = bulidDefaultVO(mobile);
        if (StringUtils.isBlank(mobile)) {
            throw new GlobalException(ResultCode.MISS_PARAM);
        }

        Date endTime = new Date();
        Date startTime = DateUtils.addDays(endTime, -venusConfig.getUserShortUrlQueryDay());
        List<UserShortUrlDO> userShortUrlList = userShortUrlDOMapper.selectByMobile(mobile, startTime, endTime);
        if (CollUtil.isEmpty(userShortUrlList)) {
            throw new GlobalException(String.format("根据mobile:%s|查询用户短链为空", DigestUtil.md5Hex(mobile)));
        }

        // 批量获取所有对应的日志记录
        Map<String, String> shortCodeChannelMap = new HashMap<>();
        for (UserShortUrlDO userShortUrl : userShortUrlList) {
            String shortCode = userShortUrl.getShortCode();
            if (StringUtils.isBlank(shortCode)) {
                continue;
            }
            String appChannel = extractChannelCodeFromUrl(userShortUrl.getLongUrl());
            shortCodeChannelMap.put(shortCode, appChannel);
        }

        Set<String> allShortCodes = shortCodeChannelMap.keySet();
        if (CollUtil.isEmpty(allShortCodes)) {
            throw new GlobalException(String.format("根据mobile:%s|查询用户短链对应的日志记录为空", DigestUtil.md5Hex(mobile)));
        }
        List<ShortUrlLogDO> allShortUrlLogs = shortUrlLogMapper.selectByShortCodes(new ArrayList<>(allShortCodes), startTime, endTime);

        List<ShortUrlChannelDataVO> channelDataList = new ArrayList<>();
        allShortUrlLogs.forEach(logDO -> {
            String appChannel = shortCodeChannelMap.getOrDefault(logDO.getShortCode(), "");
            if (StringUtils.isNotBlank(appChannel)) {
                Integer clickTime = logDO.getClickTime();
                ShortUrlChannelDataVO channelDataVO = ShortUrlChannelDataVO.builder()
                        .appChannel(appChannel)
                        .clickTime(clickTime)
                        .build();
                channelDataList.add(channelDataVO);
            }
        });

        // 对相同channel的记录，只保留clickTime最大的
        List<ShortUrlChannelDataVO> distinctChannelDataList = channelDataList.stream()
                .collect(Collectors.groupingBy(
                        ShortUrlChannelDataVO::getAppChannel,
                        Collectors.maxBy(Comparator.comparing(ShortUrlChannelDataVO::getClickTime))
                ))
                .values()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());

        userThirtyChannelInfoVO.setChannelDataList(CollUtil.isEmpty(distinctChannelDataList) ? CollUtil.newArrayList() : distinctChannelDataList);
        return userThirtyChannelInfoVO;
    }

    private UserThirtyChannelInfoVO bulidDefaultVO(String mobile) {
        UserThirtyChannelInfoVO userThirtyChannelInfoVO = new UserThirtyChannelInfoVO();
        userThirtyChannelInfoVO.setMobile(mobile);
        userThirtyChannelInfoVO.setChannelDataList(CollUtil.newArrayList());
        return userThirtyChannelInfoVO;
    }

    private List<UserChannelInfoVO> doQueryChannelInfoByMobiles(List<String> mobileList) {
        if (CollUtil.isEmpty(mobileList)) {
            log.info("请求参数为空 mobileList is empty");
            return CollUtil.newArrayList();
        }
        String mobile = mobileList.get(0);
        Date endTime = new Date();
        Date startTime = DateUtils.addDays(endTime, -venusConfig.getUserShortUrlQueryDay());

        List<UserShortUrlDO> userShortUrlList = userShortUrlDOMapper.selectByMobile(mobile, startTime, endTime);
        if (CollUtil.isEmpty(userShortUrlList)) {
            log.info("根据mobile:{}|查询用户短链为空", DigestUtil.md5Hex(mobile));
            return CollUtil.newArrayList();
        }

        Map<String, List<UserShortUrlDO>> mobileUrlMap = userShortUrlList.stream()
                .collect(Collectors.groupingBy(UserShortUrlDO::getMobile));

        // 批量获取所有对应的日志记录
        Set<String> allShortCodes = userShortUrlList.stream()
                .map(UserShortUrlDO::getShortCode)
                .collect(Collectors.toSet());
        List<ShortUrlLogDO> allShortUrlLogs = shortUrlLogMapper.selectByShortCodes(new ArrayList<>(allShortCodes), startTime, endTime);

        Map<String, List<ShortUrlLogDO>> shortCodeLogMap = allShortUrlLogs.stream()
                .collect(Collectors.groupingBy(ShortUrlLogDO::getShortCode));

        List<UserChannelInfoVO> resultList = new ArrayList<>();
        for (Map.Entry<String, List<UserShortUrlDO>> mobileUrlEntry : mobileUrlMap.entrySet()) {
            String mobileKey = mobileUrlEntry.getKey();
            List<UserShortUrlDO> mobileUrlList = mobileUrlEntry.getValue();

            if (CollUtil.isEmpty(mobileUrlList)) {
                log.info("mobile:{}对应的短链为空", DigestUtil.md5Hex(mobileKey));
                continue;
            }

            Map<String, String> shortCodeLongUrlMap = mobileUrlList.stream()
                    .collect(Collectors.toMap(UserShortUrlDO::getShortCode, UserShortUrlDO::getLongUrl));

            // 求最近点击 shortCode
            String lastShortCode = mobileUrlList.stream()
                    .map(userShortUrlDO -> shortCodeLogMap.get(userShortUrlDO.getShortCode()))
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .max(Comparator.comparing(ShortUrlLogDO::getCreateTime))
                    .map(ShortUrlLogDO::getShortCode)
                    .orElse(null);

            UserChannelInfoVO userChannelInfoVO = UserChannelInfoVO.builder()
                    .mobile(mobileKey)
                    .channelId(extractChannelCodeFromUrl(shortCodeLongUrlMap.get(lastShortCode)))
                    .build();
            resultList.add(userChannelInfoVO);

        }
        return resultList;
    }

    public String innerCreate(ShortUrlDTO shortUrlDTO) {
        String shortCode;
        //计算过期日期
        Date expiredDate = calExpiredDate(shortUrlDTO.getValidPeriod());

        //生成短链编码
        int shortCodeLength = venusConfig.getShortCodeLength();
        shortCode = CommonUtil.getRandStr(shortCodeLength);

        //保存
        String currentUser = OPERATOR;
        ShortUrlDO shortUrlDO = ShortUrlDO.builder()
                .shortCode(shortCode)
                .srcUrl(shortUrlDTO.getSrcUrl())
                .description(shortUrlDTO.getDescription())
                .expiredDate(expiredDate)
                .status(1)
                .creator(currentUser)
                .updater(currentUser)
                .build();
        try {
            shortUrlMapper.insertSelective(shortUrlDO);
        } catch (DuplicateKeyException e) {
            log.warn("短链编码重复，编码值：{}", shortCode);
            //重试一次
            shortUrlDO.setShortCode(CommonUtil.getRandStr(shortCodeLength));
            shortUrlMapper.insertSelective(shortUrlDO);
        }

        return shortCode;
    }


    public String extractChannelCodeFromUrl(String url) {
        try {

            if (StringUtils.isEmpty(url)) {
                return "";
            }

            URI uri = new URI(url);
            String query = uri.getQuery();
            if (query != null) {
                Map<String, String> queryParams = Arrays.stream(query.split("&"))
                        .map(param -> param.split("="))
                        .collect(Collectors.toMap(param -> param[0], param -> param.length > 1 ? param[1] : ""));
                return queryParams.get("appChannel");
            }
        } catch (URISyntaxException e) {
            log.error("Invalid URL: {}", url, e);
        }
        return "";
    }


    private String calExpiredDateByAnyDay(Integer days) {
        if (Objects.isNull(days)) {
            return PERMANENT_DATE_STR;
        }
        if (days > 0) {
            LocalDateTime currentDate = LocalDateTime.now();
            LocalDateTime expiredDate = currentDate.plusDays(days);
            return expiredDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        return PERMANENT_DATE_STR;
    }


    private Date calExpiredDate(Integer validPeriod) {
        if (Objects.isNull(validPeriod)) {
            return PERMANENT_DATE;
        }
        Calendar c = Calendar.getInstance();
        switch (validPeriod) {
            case ShortUrlConstant.EXPIRED_TYPE_90:
                c.add(Calendar.DAY_OF_MONTH, 90);
                break;
            case ShortUrlConstant.EXPIRED_TYPE_180:
                c.add(Calendar.DAY_OF_MONTH, 180);
                break;
            case ShortUrlConstant.EXPIRED_TYPE_365:
                c.add(Calendar.DAY_OF_MONTH, 365);
                break;
            default:
                return PERMANENT_DATE;
        }
        return c.getTime();
    }

    private String buildShortUrl(String shortCode) {
        return venusConfig.getShortUrlDomain() + "/" + shortCode;
    }

    private String getShortenerUrl() {
        return venusConfig.getShortenerUrl() + "/userShortUrl/hold";
    }

    private void populateResponse(JSONObject jsonObject, ShortUrlTplDO modelDO) {
        jsonObject.put("bizId", modelDO.getBizId());
        jsonObject.put("shortUrl", buildShortUrl(modelDO.getShortCode()));
    }
}
