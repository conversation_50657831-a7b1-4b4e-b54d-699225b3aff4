package com.xhqb.spectre.api.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.api.controller.common.CommonResult;
import com.xhqb.spectre.api.model.entity.*;
import com.xhqb.spectre.api.service.ShortUrlService;
import com.xhqb.spectre.api.service.ZbxShortUrlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/25 15:09
 **/
@RestController
@Slf4j
@RequestMapping("/shortUrl")
public class ShortUrlController {

    @Autowired
    private ShortUrlService shortUrlService;

    @Autowired
    private ZbxShortUrlService zbxShortUrlService;

    /**
     * 远程生成短链接接口
     *
     * @param shortUrlDTO 所需要的请求参数
     * @return 短链地址
     */
    @PostMapping("/createShortUrl")
    public CommonResult<String> create(@Valid @RequestBody ShortUrlDTO shortUrlDTO) {
        log.info("create shortUrl, shortUrlDTO: {}", shortUrlDTO);
        return CommonResult.success(shortUrlService.create(shortUrlDTO));
    }

    /**
     * 中博信远程生成短链接接口
     *
     * @param shortenDTO 所需要的请求参数
     * @return 短链地址
     */
    @PostMapping("/zbx/shorten")
    public CommonResult<String> shorten(@Valid @RequestBody ShortenDTO shortenDTO) {
        log.info("create zbx short url, shortenDTO: {}", shortenDTO);
        return CommonResult.success(zbxShortUrlService.shorten(shortenDTO));
    }

    /**
     * 创建短链接模板的接口方法（营销服务调用）
     *
     * @param shortUrlTplDTO 短链接模板的DTO对象，包含创建短链接模板所需的信息
     * @return 返回一个CommonResult对象，包含操作结果信息
     */
    @PostMapping("/createShortUrlTpl")
    public CommonResult<JSONObject> createTplShortUrl(@Valid @RequestBody ShortUrlTplDTO shortUrlTplDTO) {
        log.info("create shortUrlTpl, shortUrlTplDTO: {}", shortUrlTplDTO);
        return CommonResult.success(shortUrlService.createShortUrlTpl(shortUrlTplDTO));
    }

    /**
     * 创建用户短链的接口（营销服务调用）
     *
     * @param userShortUrlDTO 用户短链的DTO对象，包含创建短链所需的信息
     * @return 返回一个CommonResult对象，包含创建的用户短链列表
     */
    @PostMapping("/createUserShortUrl")
    public CommonResult<List<UserShortUrlVO>> createUserShortUrl(@Valid @RequestBody UserShortUrlDTO userShortUrlDTO) {
        long startTime = System.currentTimeMillis();
        int size = CollUtil.isEmpty(userShortUrlDTO.getUserShortUrlInfoList()) ? 0 : userShortUrlDTO.getUserShortUrlInfoList().size();
        List<UserShortUrlVO> resultList = shortUrlService.createUserShortUrl(userShortUrlDTO);
        log.info("短信api收到创建用户短链请求 = 业务 id:{} | size:{} |cost:{}", userShortUrlDTO.getBizId(), size, System.currentTimeMillis() - startTime);
        return CommonResult.success(resultList);
    }

    /**
     * 查询注册渠道归因（oidc调用）
     *
     * @param shortUrlChannelInfoDTO 用户信息
     * @return 注册归因
     */
    @PostMapping("/queryChannelInfoByMobiles")
    public CommonResult<List<UserChannelInfoVO>> queryChannelInfoByMobiles(@RequestBody ShortUrlChannelInfoDTO shortUrlChannelInfoDTO) {
        long startTime = System.currentTimeMillis();
        int size = Objects.isNull(shortUrlChannelInfoDTO) || CollUtil.isEmpty(shortUrlChannelInfoDTO.getMobileList()) ? 0 : shortUrlChannelInfoDTO.getMobileList().size();
        List<UserChannelInfoVO> resultList = shortUrlService.queryChannelInfoByMobiles(shortUrlChannelInfoDTO.getMobileList());
        log.info("短信api收到查询渠道信息请求 手机号 List｜size:{} |cost:{}", size, System.currentTimeMillis() - startTime);
        return CommonResult.success(resultList);
    }

    /**
     * 查询-注册归因-过去30天渠道信息(是否付费) - OIDC调用
     * @param mobile 手机号
     * @return 注册归因
     */
    @GetMapping("/queryPastThirtyChannels")
    public CommonResult<UserThirtyChannelInfoVO> queryPastThirtyChannels(String mobile) {
        long startTime = System.currentTimeMillis();
        UserThirtyChannelInfoVO userThirtyChannelInfoVO =  shortUrlService.queryPastThirtyChannels(mobile);
        log.info("短信api收到查询渠道信息请求 手机号:{} |cost:{}", DigestUtil.md5Hex(mobile), System.currentTimeMillis() - startTime);
        return CommonResult.success(userThirtyChannelInfoVO);
    }

}
