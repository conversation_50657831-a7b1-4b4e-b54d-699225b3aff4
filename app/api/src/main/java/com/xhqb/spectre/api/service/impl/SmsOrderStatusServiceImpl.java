package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.model.entity.OrderStatusVO;
import com.xhqb.spectre.api.model.smsreq.OrderStatusDTO;
import com.xhqb.spectre.api.service.SmsOrderStatusService;
import com.xhqb.spectre.api.utils.DateUtil;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.mapper.SmsOrderMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SmsOrderStatusServiceImpl implements SmsOrderStatusService {

    @Resource
    private SmsOrderMapper smsOrderMapper;
    @Resource
    private VenusConfig venusConfig;

    @Override
    public List<OrderStatusVO> queryOrderStatus(OrderStatusDTO dto) {
        List<SmsOrderDO> smsOrderDOList = smsOrderMapper.selectOrderStatus(dto.getAppCode(),
                dto.getRequestId(), dto.getTplCode(), dto.getMobiles(), DateUtil.getCurrentDayLatestTimestamp(),DateUtil.getNDaysAgoEarliestTimestamp(venusConfig.getMarketQueryDays()));
        List<SmsOrderDO> result = smsOrderDOList.stream()
                .sorted(Comparator.comparingInt(SmsOrderDO::getResend).reversed()) // 按 resend 递减排序
                .collect(Collectors.groupingBy(SmsOrderDO::getMobile)) // 按 mobile_enc 进行分组
                .values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        return smsOrderDOList.stream().map(OrderStatusVO::buildOrderStatusVO).collect(Collectors.toList());
    }
}
