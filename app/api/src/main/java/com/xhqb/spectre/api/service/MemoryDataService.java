package com.xhqb.spectre.api.service;

import com.xhqb.spectre.common.dal.dto.*;

import java.util.List;

public interface MemoryDataService {
    AppData getAppInfoByKey(String appKey);

    TplData getTplDataByCode(String code, String signCode);

    List<ChannelAccountDisableData> getChannelDisabledData(Integer accountId);

    boolean isBlack(String phone, String smsCode, String appCode);

    boolean isWhite(String appCode, String cfgType, String phone);

    List<AppSendLimitData> getSendLimit(String appCode);

    AppSendLimitData getSendLimitByAppKeyAndLimitKey(String appCode, String limitKey);

    List<SmsTypeDisableData> getSmsTypeDisabledData(String smsType);
    ChannelTestData getChannelTestData(String code, String signCode);

}
