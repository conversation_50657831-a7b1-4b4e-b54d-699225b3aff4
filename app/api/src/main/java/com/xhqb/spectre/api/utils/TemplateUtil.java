package com.xhqb.spectre.api.utils;

import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.model.smsreq.SingleSMSReqDTO;
import com.xhqb.spectre.common.dal.dto.TplData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 模板解析工具类
 */
public class TemplateUtil {
    private static Logger logger = LoggerFactory.getLogger(TemplateUtil.class);

    /**
     * 解析并替换 [*] 形式的占位符。
     * @param content 模板内容
     */
    public static String parseSequentialPlaceholders(String content, String... args) {

        // 原模板中包含 %%，则替换为 %, 兼容老的模板, 老模板内容更新后删除
        content = content.replaceAll("(?<!%)%%(?!%)", "%");

        // ${key} 形式的占位符替换为 [*]
        if (countKeyedPlaceholders(content) > 0) {
            content = content.replaceAll(SmsApiApplicationConstant.TPL_CONTENT_PARAM2,
                    SmsApiApplicationConstant.TPL_PARAM_SYMBOL);
        }

        // 计算占位符个数
        int placeholderCount = countSequentialPlaceholders(content);

        // 如果不包含占位符则返回原值
        if (placeholderCount == 0) {
            return content;
        }

        // 替换占位符
        for (String param : args) {
            content = content.replaceFirst(SmsApiApplicationConstant.TPL_PARAM_SYMBOL_REGEX,
                Matcher.quoteReplacement(param));
        }
        return content;
    }

    /**
     * 解析并替换 ${key} 形式的占位符。
     */
    public static String parseKeyedPlaceholders(String content, Map<String, String> keyValuePairs) {
        StringBuilder sb = new StringBuilder(content);
        int placeholderStart;
        while ((placeholderStart = sb.indexOf("${")) != -1) {
            int placeholderEnd = sb.indexOf("}", placeholderStart);
            if (placeholderEnd == -1) break;

            String key = sb.substring(placeholderStart + 2, placeholderEnd);
            String replacement = keyValuePairs.getOrDefault(key, "");
            sb.replace(placeholderStart, placeholderEnd + 1, replacement);
        }
        return sb.toString();
    }

    /**
     * 判断是否可以覆盖占位符
     * @param content
     * @param keyValuePairs
     * @return
     */
    public static boolean hasAllPlaceholdersWithPairs(String content, Map<String, String> keyValuePairs) {
        if (content == null || !content.contains("${")) {
            return true;
        }
        if (keyValuePairs == null || keyValuePairs.isEmpty()) {
            return false;
        }

        int start = 0;
        while ((start = content.indexOf("${", start)) != -1) {
            int end = content.indexOf("}", start);
            if (end == -1) break;

            String key = content.substring(start + 2, end);
            String value = keyValuePairs.get(key);

            if (value == null || value.isEmpty()) {
                return false;
            }

            start = end + 1;
        }

        return true;
    }

    /**
     * 计算字符串中 [*] 形式的占位符个数
     * @param content 模板内容
     * @return
     */
    public static int countSequentialPlaceholders(String content) {
        if (content == null) {
            return 0;
        }

        int count = 0;
        int idx = 0;
        while ((idx = content.indexOf(SmsApiApplicationConstant.TPL_PARAM_SYMBOL, idx)) != -1) {
            count++;
            idx += SmsApiApplicationConstant.TPL_PARAM_SYMBOL.length();
        }
        return count;
    }

    /**
     * 计算字符串中 ${key} 形式的占位符个数
     * @param content 模板内容
     * @return
     */
    public static int countKeyedPlaceholders(String content) {
        if (content == null || content.isEmpty()) {
            return 0;
        }

        // 定义正则表达式
        Pattern pattern = Pattern.compile(SmsApiApplicationConstant.TPL_CONTENT_PARAM2);
        Matcher matcher = pattern.matcher(content);

        int count = 0;
        while (matcher.find()) {
            count++;
        }

        return count;
    }

    /**
     * 验证模版参数
     *
     * @param tplData
     * @param phone
     * @param singleSMSReqDTO
     * @return true 为参数正常
     */
    public static boolean validateTplParams(TplData tplData, String phone, SingleSMSReqDTO singleSMSReqDTO) {
        final String content = tplData.getContent();
        //顺序占位符个数
        int countedSequentialPlaceholders = TemplateUtil.countSequentialPlaceholders(content);
        //变量占位符个数
        int countedKeyedPlaceholders = TemplateUtil.countKeyedPlaceholders(content);

        //模板没有参数立即返回
        if (countedSequentialPlaceholders == 0 && countedKeyedPlaceholders == 0) {
            return true;
        }

        //顺序占位符和变量占位符不能同时存在
        if (countedSequentialPlaceholders > 0 && countedKeyedPlaceholders > 0) {
            logger.warn("短信模版内容参数配置有误, tplId={}, tplCode={}", tplData.getId(), tplData.getCode());
            return false;
        }

        int paramCountFromMap = Optional.ofNullable(singleSMSReqDTO.getParamMap())
                .map(m -> m.get(phone))
                .map(s -> s.split(",").length)
                .orElse(0);

        int paramListSize = Optional.ofNullable(singleSMSReqDTO.getParamList())
                .map(List::size)
                .orElse(0);

        //判断顺序占位符个数
        if (countedSequentialPlaceholders > 0) {
            //参数中大于模板中的参数个数
            boolean result = paramCountFromMap >= countedSequentialPlaceholders;
            if (!result) {
                logger.warn("顺序参数个数错误; phone={}, paramCount={}, tplCode={}, tplParamCount={}", phone, paramCountFromMap, tplData.getCode(), countedSequentialPlaceholders);
            }
            return result;
        }

        //判断变量占位符个数
        if (countedKeyedPlaceholders > 0) {

            // 使用顺序传参方式给变量参数的模板
            if (paramListSize == 0 && paramCountFromMap >= countedKeyedPlaceholders) {
                return true;
            }

            Map<String, String> matchedParam = Optional.ofNullable(singleSMSReqDTO.getParamList())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(item -> phone.equalsIgnoreCase(item.get(SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY)))
                    .findFirst()
                    .orElse(null);

            boolean result = TemplateUtil.hasAllPlaceholdersWithPairs(content, matchedParam);
            if (!result) {
                logger.warn("变量参数个数错误; phone={}, matchedParam={}, tplCode={}, tplParamCount={}", phone, matchedParam, tplData.getCode(), countedKeyedPlaceholders);
            }
            return result;
        }

        return false;
    }
}
