package com.xhqb.spectre.api.exception;

import com.xhqb.spectre.api.constant.LuciferConstant;
import com.xhqb.spectre.api.controller.common.ResultCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * @Description 短信发送异常
 */
public class SMSSenderException extends RuntimeException {
    private ResultCode code = ResultCode.SYS_FAILURES;

    private String message = "";

    /**
     * 以结果码为参数构造异常
     *
     * @param code 结果码
     */
    public SMSSenderException(ResultCode code) {
        super();
        this.code = code;
        this.message = code.getResultMsg();
        LuciferConstant.EXCEPTION_TOTAL.labels(String.valueOf(code.getResultCode())).inc();
    }

    /**
     * constructor
     *
     * @param message
     */
    public SMSSenderException(String message) {        //用来创建指定参数对象
        this.message = message;
    }

    /**
     * 以结果码和msg为构造异常
     *
     * @param code 结果码
     * @param msg  消息
     */
    public SMSSenderException(ResultCode code, String msg) {
        this.message = msg;
        this.code = code;
    }

    /**
     * 以结果码和Throwable为参数构造函数
     *
     * @param code 结果码
     * @param e    Throwable
     */
    public SMSSenderException(ResultCode code, Throwable e) {
        super(e);
        this.code = code;
    }

    public ResultCode getCode() {
        return code;
    }

    public void setCode(ResultCode code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }


}
