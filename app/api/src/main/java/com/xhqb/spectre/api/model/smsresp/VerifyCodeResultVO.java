package com.xhqb.spectre.api.model.smsresp;

import com.xhqb.spectre.api.constant.SendStatusEnum;
import com.xhqb.spectre.api.model.entity.SMSSendFailedRecord;
import com.xhqb.spectre.api.model.entity.SMSSendSuccessRecord;
import lombok.Data;

import java.io.Serializable;

@Data
public class VerifyCodeResultVO implements Serializable {
    private static final long serialVersionUID = 1L;

//    private String identificationCode;

    /**
     * 返回短信发送请求的请求Id
     */
    private String requestId;

    private String tplCode;

    private String sendCode;

    private String sendMsg;

}
