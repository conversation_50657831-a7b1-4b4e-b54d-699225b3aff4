package com.xhqb.spectre.api.mq;

import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.service.PhoneStatusService;
import com.xhqb.spectre.common.dal.entity.PhoneNumberStatusDO;
import com.xhqb.spectre.common.dal.mapper.PhoneNumberStatusMapper;
import com.xhqb.spectre.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PhoneNumberStatusConsumer {

    @Resource
    private PhoneNumberStatusMapper phoneNumberStatusMapper;

    @Resource
    private PhoneStatusService phoneStatusService;

    @Resource
    private VenusConfig venusConfig;

    @MQConsumer(topic = "${tdmq.producers.refreshPhoneNumberStatusTopic}",
            receiverQueueSize = 1,
            ackTimeout = 60L,
            clazz = String.class
    )
    public void consumePhoneNumberStatus(String message)
    {
        try {

            Iterable<String> split = Splitter.on(",").splitToList(message);
            Set<String> phoneNumbers = Sets.newHashSet(split);
            Set<String> validPhoneNumbers = this.filterValidPhoneNumbers(phoneNumbers);
            Set<String> invalidPhoneNumbers = Sets.difference(phoneNumbers, validPhoneNumbers);

            if (invalidPhoneNumbers.isEmpty()) {
                log.info("无需更新号码; 总数={}, 有效={}", phoneNumbers.size(), validPhoneNumbers.size());
                return;
            }

            phoneStatusService.checkAndSave(invalidPhoneNumbers);
            log.info("PhoneNumberStatusConsumer; 总数={}, 有效={}, 查询={}", phoneNumbers.size(),
                    validPhoneNumbers.size(),
                    invalidPhoneNumbers.size());
        } catch (Exception e) {
            log.error("PhoneNumberStatusConsumer error: ", e);
        }
    }

    /**
     * 筛选有效电话号码
     *
     * @param phoneNumbers  待验证的电话号码列表
     * @return 有效的电话号码集合
     */
    public Set<String> filterValidPhoneNumbers(Set<String> phoneNumbers) {
        Integer timestamp = DateUtil.getNow() - venusConfig.getPhoneNumberStatusExpired();
        List<PhoneNumberStatusDO> phoneNumberStatusDOS = phoneNumberStatusMapper.selectByMobiles(phoneNumbers, timestamp);
        return phoneNumberStatusDOS.stream()
                .map(PhoneNumberStatusDO::getMobile)
                .collect(Collectors.toSet());
    }
}
