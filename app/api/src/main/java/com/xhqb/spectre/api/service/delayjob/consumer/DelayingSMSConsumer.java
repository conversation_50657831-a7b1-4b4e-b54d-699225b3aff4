package com.xhqb.spectre.api.service.delayjob.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhqb.spectre.api.service.SMSSenderService;
import com.xhqb.spectre.api.service.delayjob.SMSDelayingQueueService;
import com.xhqb.spectre.api.service.delayjob.message.CollectDelayingMessage;
import com.xhqb.spectre.api.service.delayjob.message.MarketDelayingMessage;
import com.xhqb.spectre.api.service.delayjob.message.NotifyDelayingMessage;
import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.common.mq.MessageMQ;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 后面增加ShedLock分布式锁支持： 这样可以多节点扩展
 */
@Component
public class DelayingSMSConsumer {
    private static final Logger LOGGER = LoggerFactory.getLogger(DelayingSMSConsumer.class);

    private static ObjectMapper mapper = Jackson2ObjectMapperBuilder.json().build();

    @Resource
    SMSDelayingQueueService delayingQueueService;

    @Resource
    SMSSenderService smsSender2QService;

    /**
     * 定时消费队列中的数据
     * zset会对score进行排序 让最早消费的数据位于最前
     * 拿最前的数据跟当前时间比较 时间到了则消费
     */
    @Scheduled(cron = "*/1 * * * * *")
    public void consumeMarketSMS() throws JsonProcessingException {
        List<MarketDelayingMessage> msgList = delayingQueueService.pullMarketDelayingMessage();
        if (null != msgList) {
            long current = System.currentTimeMillis();
            // LOGGER.info("comsume market message timestamp:{}", current);
            msgList.stream().forEach(msg -> {
                // 已超时的消息拿出来消费
                if (current >= msg.getDelayTime()) {
                    try {
                        // 发送市场类短信至dispatcher队列
                        smsSender2QService.sendMarketSMSMessage((MessageMQ<BaseBodyMessage>) msg.getCustomMessage(), Boolean.TRUE);
                        LOGGER.info("消费消息：{}:消息创建时间：{},消费时间：{}", mapper.writeValueAsString(msg), msg.getCreateTime(), LocalDateTime.now());
                    } catch (JsonProcessingException e) {
                        LOGGER.error("消息 {}，解析异常： {}", msg, e.getMessage());
                    }
                    //移除消息
                    delayingQueueService.removeMarketDelayingMessage(msg);
                }
            });
        }
    }

    /**
     * 定时消费队列中的数据
     * zset会对score进行排序 让最早消费的数据位于最前
     * 拿最前的数据跟当前时间比较 时间到了则消费
     */
    @Scheduled(cron = "*/1 * * * * *")
    public void consumeCollectSMS() throws JsonProcessingException {
        List<CollectDelayingMessage> msgList = delayingQueueService.pullCollectDelayingMessage();
        if (null != msgList) {
            long current = System.currentTimeMillis();
            // LOGGER.info("comsume collect message timestamp:{}", current);
            msgList.stream().forEach(msg -> {
                // 已超时的消息拿出来消费
                if (current >= msg.getDelayTime()) {
                    try {
                        // 发送催收类短信至dispatcher队列
                        smsSender2QService.sendCollectSMSMessage((MessageMQ<BaseBodyMessage>) msg.getCustomMessage(), Boolean.TRUE);
                        LOGGER.info("消费消息：{}:消息创建时间：{},消费时间：{}", mapper.writeValueAsString(msg), msg.getCreateTime(), LocalDateTime.now());
                    } catch (JsonProcessingException e) {
                        LOGGER.error("消息 {}，解析异常： {}", msg, e.getMessage());
                    }
                    //移除消息
                    delayingQueueService.removeCollectDelayingMessage(msg);
                }
            });
        }
    }

    /**
     * 定时消费队列中的数据
     * zset会对score进行排序 让最早消费的数据位于最前
     * 拿最前的数据跟当前时间比较 时间到了则消费
     */
    @Scheduled(cron = "*/1 * * * * *")
    public void consumeNotifySMS() throws JsonProcessingException {
        List<NotifyDelayingMessage> msgList = delayingQueueService.pullNotifyDelayingMessage();
        if (null != msgList) {
            long current = System.currentTimeMillis();
            // LOGGER.info("comsume notify message timestamp:{}", current);
            msgList.stream().forEach(msg -> {
                // 已超时的消息拿出来消费
                if (current >= msg.getDelayTime()) {
                    try {
                        // 发送催收类短信至dispatcher队列
                        smsSender2QService.sendNotifySMSMessage((MessageMQ<BaseBodyMessage>) msg.getCustomMessage(), Boolean.TRUE);
                        LOGGER.info("消费消息：{}:消息创建时间：{},消费时间：{}", mapper.writeValueAsString(msg), msg.getCreateTime(), LocalDateTime.now());
                    } catch (JsonProcessingException e) {
                        LOGGER.error("消息 {}，解析异常： {}", msg, e.getMessage());
                    }
                    //移除消息
                    delayingQueueService.removeNotifyDelayingMessage(msg);
                }
            });
        }
    }
}
