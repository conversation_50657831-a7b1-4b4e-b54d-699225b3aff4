package com.xhqb.spectre.api.model.smsreq;


import lombok.Data;

import java.util.Map;

@Data
public class VerifyCodeSMSReqDTO extends BaseSMSReqDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 模版参数
     * 20210924 废弃
     */
    private Map<String, String> dynamicParamMap;

    /**
     * 验证码识别码
     */
//    private String identificationCode;

    /**
     * 自定义验证码，如果设置了该参数，则codeLen参数无效
     */
    private String smsVerifyCode;

    /**
     * 验证码长度
     */
    private Integer codeLen;

}
