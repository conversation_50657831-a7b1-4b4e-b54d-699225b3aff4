package com.xhqb.spectre.api.config;

import com.xhqb.spectre.api.interceptor.SignCompareAndVerifyInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Description: 加入鉴权验签、接口幂等
 * Author：
 * Date：
 */
@Configuration
public class AppSignConfig implements WebMvcConfigurer {

    @Bean
    public SignCompareAndVerifyInterceptor digitalSignInterceptor() {
        return new SignCompareAndVerifyInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(digitalSignInterceptor()).addPathPatterns("/api/spectre/v3/**", "/shortUrl/**");
    }
}
