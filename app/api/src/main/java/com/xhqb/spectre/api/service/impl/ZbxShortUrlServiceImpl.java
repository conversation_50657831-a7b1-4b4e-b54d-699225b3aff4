package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.constant.ShortUrlConstant;
import com.xhqb.spectre.api.exception.GlobalException;
import com.xhqb.spectre.api.model.entity.ShortenDTO;
import com.xhqb.spectre.api.service.ZbxShortUrlService;
import com.xhqb.spectre.api.utils.DateUtil;
import com.xhqb.spectre.common.dal.entity.ZbxShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.ZbxShortUrlMapper;
import com.xhqb.spectre.common.utils.UniqueKeyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.Objects;


@Service
@Slf4j
public class ZbxShortUrlServiceImpl implements ZbxShortUrlService {

    private static final Date PERMANENT_DATE = DateUtil.smallToDate("2099-12-31");

    private static final String OPERATOR = "system";

    @Autowired
    private ZbxShortUrlMapper zbxShortUrlMapper;
    @Autowired
    private VenusConfig venusConfig;

    @Override
    public String shorten(ShortenDTO shortenDTO) {
        int retries = 0;
        int maxRetries = 4;
        boolean inserted = false;

        while (retries < maxRetries) {
            ZbxShortUrlDO zbxShortUrlDO = buildZbxShortUrlDO(shortenDTO);
            try {
                zbxShortUrlMapper.insertSelective(zbxShortUrlDO);
                inserted = true;
                return buildShortUrl(zbxShortUrlDO.getShortCode());

            } catch (DuplicateKeyException e) {
                log.warn("短链编码重复，编码值：{}, 重试次数：{}/{}", zbxShortUrlDO.getShortCode(), retries + 1, maxRetries);
                retries++;
            } catch (Exception e) {
                log.warn("插入短链时发生未知异常，编码值：{}, 重试次数：{}/{}", zbxShortUrlDO.getShortCode(), retries + 1, maxRetries, e);
                retries++;
            }
        }

        if (!inserted) {
            log.error("短链插入失败，达到最大重试次数，ShortenDTO：{}, 最大重试次数：{}", shortenDTO, maxRetries);
        }
        throw new GlobalException("短链插入失败，达到最大重试次数");
    }

    private ZbxShortUrlDO buildZbxShortUrlDO(ShortenDTO shortenDTO) {
        String currentUser = OPERATOR;
        int length = venusConfig.getZbxShortCodeLength();
        String shortCode = UniqueKeyUtil.generate(length);
        Date expiredDate = calExpiredDate(shortenDTO.getValidPeriod());

        return ZbxShortUrlDO.builder()
                .shortCode(shortCode)
                .srcUrl(shortenDTO.getSrcUrl().trim())
                .description("")
                .expiredDate(expiredDate)
                .status(ShortUrlConstant.STATUS_VALID)
                .type(ShortUrlConstant.API_TYPE)
                .creator(currentUser)
                .updater(currentUser)
                .build();
    }

    private Date calExpiredDate(Integer validPeriod) {
        if (Objects.isNull(validPeriod)) {
            return PERMANENT_DATE;
        }
        Calendar c = Calendar.getInstance();
        switch (validPeriod) {
            case ShortUrlConstant.EXPIRED_TYPE_90:
                c.add(Calendar.DAY_OF_MONTH, 90);
                break;
            case ShortUrlConstant.EXPIRED_TYPE_180:
                c.add(Calendar.DAY_OF_MONTH, 180);
                break;
            case ShortUrlConstant.EXPIRED_TYPE_365:
                c.add(Calendar.DAY_OF_MONTH, 365);
                break;
            default:
                return PERMANENT_DATE;
        }
        return c.getTime();
    }

    private String buildShortUrl(String shortCode) {
        return venusConfig.getZbxShortUrlDomain() + "/" + shortCode;
    }
}
