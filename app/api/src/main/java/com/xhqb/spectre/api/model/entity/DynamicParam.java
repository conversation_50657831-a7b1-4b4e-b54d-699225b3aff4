package com.xhqb.spectre.api.model.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 发送短信模版参数
 *
 */
@Data
public class DynamicParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 短信参数
     */
    private String sendParams;

   /*  @Override
    public boolean equals(Object o) {
        return true;
    }

    @Override
    public int hashCode() {
        String str = phone + sendParams;
        return str.hashCode();
    } */
}
