package com.xhqb.spectre.api.service;

import com.xhqb.spectre.api.model.entity.BlackAddDTO;
import com.xhqb.spectre.api.model.smsreq.MobileBlackDTO;
import com.xhqb.spectre.api.model.smsresp.MobileBlackVO;
import com.xhqb.spectre.common.dal.entity.MobileBlackDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.MobileBlackQuery;

import javax.annotation.Nullable;
import java.util.List;
import java.util.stream.Stream;

public interface MobileBlackService {
    CommonPager<MobileBlackVO> listByPage(MobileBlackQuery query);
    MobileBlackVO getById(Integer id);
    void create(MobileBlackDTO dto);
    void delete(Integer id);
    void batchDelete(List<Integer> idList);

    Object createBlackListEntry(BlackAddDTO blackAddDTO);

    Stream<MobileBlackDO> selectAllByLastRefresh(@Nullable Integer lastRefreshTime, int pageSize);
}
