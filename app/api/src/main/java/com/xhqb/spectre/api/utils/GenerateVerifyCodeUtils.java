package com.xhqb.spectre.api.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Random;

/**
 * Description: 验证码生成工具包
 */
public class GenerateVerifyCodeUtils {
    private static Logger logger = LoggerFactory.getLogger(GenerateVerifyCodeUtils.class.getName());

    /**
     * 生成指定长度的验证码
     *
     * @param len
     * @return
     */
    public static String getDigitalVerifyCode(Integer len) {
        String[] verifyString = new String[]{"0", "1", "2", "3", "4", "5",
                "6", "7", "8", "9"};
        Random random = new Random(System.currentTimeMillis());
        StringBuilder verifyBuilder = new StringBuilder();
        for (int i = 0; i < len; i++) {
            int rd = random.nextInt(10);
            verifyBuilder.append(verifyString[rd]);
        }
        String verifyCode = verifyBuilder.toString();
        return verifyCode;
    }
}
