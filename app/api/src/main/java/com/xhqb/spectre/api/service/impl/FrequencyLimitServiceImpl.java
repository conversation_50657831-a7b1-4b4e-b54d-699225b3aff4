package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.constant.CfgTypeLimitCheckEnum;
import com.xhqb.spectre.api.constant.LimitCheckResultEnum;
import com.xhqb.spectre.api.service.FrequencyLimitService;
import com.xhqb.spectre.api.service.MemoryDataService;
import com.xhqb.spectre.api.service.MobileLimitService;
import com.xhqb.spectre.common.dal.dto.AppSendLimitData;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

import static com.xhqb.spectre.api.constant.LimitConstants.*;

/**
 * 频率检查
 * <p>
 * 后续可能针对实际需求进行优化
 * 目前频率是以30s、1H、1D、24小时内相同内容、24小时内验证码 作为处理键值的
 */
@Service
public class FrequencyLimitServiceImpl implements FrequencyLimitService {
    private static final Logger LOGGER = LoggerFactory.getLogger(FrequencyLimitServiceImpl.class);

    @Resource
    private MemoryDataService memoryDataService;

    @Resource
    private MobileLimitService mobileLimitService;

    /**
     * 限流+白名单
     * 1、 实质应该白名单通过后、不做任何频率限制
     * 2、 白名单未通过的、进行五种条件的频率检查
     * 3、 频率检查通过后，可以发送短信
     * <p>
     * App 数量限制 - windows： 1 days，  key ： appMaxCountDay + appid， value：smsCount
     * Mobile30s的量：    - windows： 30s，     key :  mobileMaxCountHalfMinute + appid + phone, value: List.size < send_limit_value
     * Mobile1小时量      - windows： 1h，       key :  mobileMaxCountHour + appid + phone, value: List.size < send_limit_value
     * Mobile1天          - windows： 1d，       key :  mobileMaxCountDay + appid + phone, value: List.size < send_limit_value
     * Mobile相同内容（后面拼接发送参数）:  - windows:  1d,     key :  mobileSameContentMaxCycle + appid + templateid + phone, value: List.size < send_limit_value
     */
    @Override
    public LimitCheckResultEnum checkSendLimitFrequencyFrom(String tplCode, String appCode, String cfgType, String phone, String smsCode) {
        AppSendLimitData appSendLimitData = memoryDataService.getSendLimitByAppKeyAndLimitKey(appCode, cfgType);
        CfgTypeLimitCheckEnum cfgTypeLimitCheckEnum = CfgTypeLimitCheckEnum.getByCfgType(cfgType);
        if (Objects.isNull(appSendLimitData)) {
            // 如果内存中没有数据，就使用默认的限流规则
            return doCheckSendLimitFrequencyDefault(tplCode, appCode, phone, cfgTypeLimitCheckEnum);
        }
        LimitCheckResultEnum limitCheckResultEnum = doCheckSendLimitFrequency(tplCode, appCode, phone, cfgTypeLimitCheckEnum, appSendLimitData, smsCode);
        return limitCheckResultEnum;
    }

    /**
     * 减去redis中的计数
     *
     * @param tlpCode
     * @param appCode
     * @param cfgType
     * @param phone
     * @param smsCode
     */
    @Override
    public void subtractSMSFrequencyLimitCount(String tlpCode, String appCode, String cfgType, String phone, String smsCode) {
        StringBuilder limitCheckKey = new StringBuilder();
        CfgTypeLimitCheckEnum cfgTypeLimitCheckEnum = CfgTypeLimitCheckEnum.getByCfgType(cfgType);
        switch (cfgTypeLimitCheckEnum) {
            case APP_SEND_LIMIT_MOBILE_MAZX_COUNT_HALF_MINUTE:
                String mobileLimitKey30S = limitCheckKey.append(SMS_TIMEOUT_REDIS_KEY30S).append(":").append(appCode).append(":").append(phone).toString();
                mobileLimitService.postSubtractAppFrequencyLimit(mobileLimitKey30S);
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_HOUR:
                String mobileLimitKey1H = limitCheckKey.append(SMS_TIMEOUT_REDIS_KEY1H).append(":").append(appCode).append(":").append(phone).toString();
                mobileLimitService.postSubtractAppFrequencyLimit(mobileLimitKey1H);
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_DAY:
                String mobileLimitKey1D = limitCheckKey.append(SMS_TIMEOUT_REDIS_KEY1D).append(":").append(appCode).append(":").append(phone).toString();
                mobileLimitService.postSubtractAppFrequencyLimit(mobileLimitKey1D);
                break;
            case APP_SEND_LIMIT_MOBILE_SAME_CONTENT_MAX_CYCLE:
                String mobileSameSMSKey = limitCheckKey.append(SMS_SAME_CONTENT_REDIS_KEY24H).append(":").append(appCode).append(":").append(tlpCode).append(":").append(phone).toString();
                mobileLimitService.postSubtractAppFrequencyLimit(mobileSameSMSKey);
                break;
            default:
                LOGGER.info("default. no frequency need to increase");
        }
        if (smsCode.equalsIgnoreCase(MessageTypeEnum.VERIFY.getMessageType())) {
            String mobileVerify = limitCheckKey.append(VERIFY_TIMEOUT_REDIS_KEY1D).append(":").append(appCode).append(":").append(phone).toString();
            mobileLimitService.postSubtractAppFrequencyLimit(mobileVerify);
        }
    }

    private LimitCheckResultEnum doCheckSendLimitFrequency(String tplCode, String appCode, String phone, CfgTypeLimitCheckEnum cfgType, AppSendLimitData appSendLimit, String smsCode) {
        //初始化默认频率检查变量
        LimitCheckResultEnum limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_PASS;
        StringBuilder limitCheckKey = new StringBuilder();
        switch (cfgType) {
            case APP_SEND_LIMIT_MOBILE_MAZX_COUNT_HALF_MINUTE:
                String mobileLimitKey30S = limitCheckKey.append(appCode).append(":").append(phone).toString();
                if (!mobileLimitService.checkMobileMaxCountHalfMinute(mobileLimitKey30S, Integer.parseInt(appSendLimit.getLimitValue()))) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_MAZX_COUNT_HALF_MINUTE;
                }
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_HOUR:
                String mobileLimitKey1H = limitCheckKey.append(appCode).append(":").append(phone).toString();
                if (!mobileLimitService.checkMobileMaxCountHour(mobileLimitKey1H, Integer.parseInt(appSendLimit.getLimitValue()))) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_HOUR;
                }
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_DAY:
                String mobileLimitKey1D = limitCheckKey.append(appCode).append(":").append(phone).toString();
                if (!mobileLimitService.checkMobileMaxCountDay(mobileLimitKey1D, Integer.parseInt(appSendLimit.getLimitValue()))) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_DAY;
                }
                break;
            case APP_SEND_LIMIT_MOBILE_SAME_CONTENT_MAX_CYCLE:
                String mobileSameSMSKey = limitCheckKey.append(appCode).append(":").append(tplCode).append(":").append(phone).toString();
                if (!mobileLimitService.checkMobileSameContentMaxCycle(mobileSameSMSKey, Integer.parseInt(appSendLimit.getLimitValue()))) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_SAME_CONTENT_MAX_CYCLE;
                }
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_VERIFY:
                if (smsCode.equalsIgnoreCase(MessageTypeEnum.VERIFY.getMessageType())) {
                    String mobileVerify = limitCheckKey.append(appCode).append(":").append(phone).toString();
                    if (!mobileLimitService.checkVerifyCountDay(mobileVerify, Integer.parseInt(appSendLimit.getLimitValue()))) {
                        limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_VERIFY;
                    }
                }
                break;
            default:
                LOGGER.info("doCheckSendLimitFrequency default");
        }
        return limitCheckResultEnum;
    }

    private LimitCheckResultEnum doCheckSendLimitFrequencyDefault(String tlpCode, String appCode, String phone, CfgTypeLimitCheckEnum cfgType) {
        // 初始化默认频率检查变量
        LimitCheckResultEnum limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_PASS;
        StringBuilder limitCheckKey = new StringBuilder();
        switch (cfgType) {
            case APP_SEND_LIMIT_MOBILE_MAZX_COUNT_HALF_MINUTE:
                String mobileLimitKey30S = limitCheckKey.append(appCode).append(":").append(phone).toString();
                if (!mobileLimitService.checkMobileMaxCountHalfMinute(mobileLimitKey30S, DEFAULT_MOBILE_MAX_HALF_MINUTE)) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_MAZX_COUNT_HALF_MINUTE;
                }
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_HOUR:
                String mobileLimitKey1H = limitCheckKey.append(appCode).append(":").append(phone).toString();
                if (!mobileLimitService.checkMobileMaxCountHour(mobileLimitKey1H, DEFAULT_MOBILE_MAX_HOUR)) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_HOUR;
                }
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_DAY:
                String mobileLimitKey1D = limitCheckKey.append(appCode).append(":").append(phone).toString();
                if (!mobileLimitService.checkMobileMaxCountDay(mobileLimitKey1D, DEFAULT_MOBILE_MAX_DAY)) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_DAY;
                }
                break;
            case APP_SEND_LIMIT_MOBILE_SAME_CONTENT_MAX_CYCLE:
                String mobileSameSMSKey = limitCheckKey.append(appCode).append(":").append(tlpCode).append(":").append(phone).toString();
                if (!mobileLimitService.checkMobileSameContentMaxCycle(mobileSameSMSKey, DEFAULT_MOBILE_SAME_MAX_CYCLE)) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_SAME_CONTENT_MAX_CYCLE;
                }
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_VERIFY:
                String mobileVerify = limitCheckKey.append(appCode).append(":").append(phone).toString();
                if (!mobileLimitService.checkVerifyCountDay(mobileVerify, DEFAULT_MOBILE_MAX_VERIFY)) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_VERIFY;
                }
                break;
            default:
                LOGGER.info("doCheckSendLimitFrequencyDefault default");
        }
        return limitCheckResultEnum;
    }

}

