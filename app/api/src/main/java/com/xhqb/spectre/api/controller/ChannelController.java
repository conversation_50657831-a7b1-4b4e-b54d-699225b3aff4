package com.xhqb.spectre.api.controller;

import com.xhqb.spectre.api.controller.common.CommonResult;
import com.xhqb.spectre.api.model.smsresp.ChannelEnumVO;
import com.xhqb.spectre.api.service.ChannelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/spectre/v3/channel")
@Slf4j
public class ChannelController {

    @Autowired
    private ChannelService channelService;

    /**
     * 查询渠道枚举
     *
     * @return
     */
    @GetMapping("/enum")
    public CommonResult<List<ChannelEnumVO>> queryEnum() {
        return CommonResult.success(channelService.queryEnum());
    }
}
