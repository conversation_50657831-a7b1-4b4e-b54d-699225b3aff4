package com.xhqb.spectre.api.constant;

/**
 * 短信发送失败类型
 */
public enum SMSSendFailTypeEnum {
    SMS_SEND_TYPE_NOT_SUPPORT(5000, "不支持的短信类型发送"),

    SMS_SEND_TYPE_DISPATCH_FAILED(5001, "短信派发失败"),

    SMS_SEND_TYPE_PHONE_NOT_VALID(5002, "无效手机号码"),

    SMS_SEND_TYPE_SEND_LIMIT_BLOCKED(5003, "触发了短信发送频率限制"),

    SMS_SEND_TYPE_SEND_PARAM_NOT_VALID(5004, "缺失短信参数或传递的参数个数有误"),

    SMS_SEND_TYPE_BLACK_PHONE(5005, "手机号码命中黑名单"),

    SMS_SEND_TYPE_SMS_TYPE_DISABLED(5006, "短信类型被屏蔽"),

    SMS_SEND_FAILED_NO_AVAIL_PARTNER(3105, "无可用的短信渠道"),

    SMS_SEND_FAILED_NO_CONTENT(3106, "无效短信内容");

    private Integer statusCode;

    private String statusMsg;

    private SMSSendFailTypeEnum(Integer statusCode, String statusMsg) {
        this.statusCode = statusCode;
        this.statusMsg = statusMsg;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    private void setStatusCode(Integer code) {
        this.statusCode = code;
    }


    public String getStatusMsg() {
        return statusMsg;
    }


    private void setStatusMsg(String message) {
        this.statusMsg = message;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code
     * @return
     */
    public static SMSSendFailTypeEnum getByCode(Integer code) {
        SMSSendFailTypeEnum[] arr = values();
        int length = arr.length;

        for (int i = 0; i < length; ++i) {
            SMSSendFailTypeEnum statusEnum = arr[i];
            if (statusEnum.getStatusCode().equals(code)) {
                return statusEnum;
            }
        }

        return null;
    }

}
