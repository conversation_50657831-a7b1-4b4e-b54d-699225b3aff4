package com.xhqb.spectre.api.constant;

import com.xhqb.kael.lucifer.telemetry.PrometheusCounterMetrics;
import com.xhqb.kael.lucifer.telemetry.PrometheusGaugeMetrics;
import com.xhqb.kael.lucifer.telemetry.PrometheusHistogramMetrics;
import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;
import io.prometheus.client.Histogram;
import io.prometheus.client.Summary;

/**
 * 埋点常量
 *
 * <AUTHOR>
 * @date 2021/10/13
 */
public class LuciferConstant {

    private static final double[] METRIC_BUCKETS =
            new double[]{.005, .01, .025, .05, .075, .1, .25, .5, .75, 1, 2.5, 5, 7.5, 10, 15, 20, 30};

    public static final Summary RECEIVED_BYTES = Summary.build()
            .namespace("http")
            .name("requests_size_bytes")
            .labelNames("uri", "status", "smsCode", "resultCode")
            .help("Request size in bytes.").register();
    /**
     * 正在处理的请求
     */
    public static final Gauge GAUGE = new PrometheusGaugeMetrics("spectre_api_v3_gauge", "gauge description")
            .createWithLabels("uri");

    /**
     * 接口请求次数
     */
    public static final Counter COUNTER = new PrometheusCounterMetrics("spectre_api_http_request_total", "description")
            .createWithLabels("uri", "status", "smsCode", "resultCode");

    /**
     * 接口处理耗时
     */
    public static final Histogram HISTOGRAM = new PrometheusHistogramMetrics("spectre_api_v3_histogram", "histogram description", METRIC_BUCKETS)
            .createWithLabels("uri", "status", "smsCode", "resultCode");
    /**
     * mq 发送次数 分短信类型
     */
    public static final Counter MQ_SEND_COUNTER = new PrometheusCounterMetrics("mq_send_total", "mq send total")
            .createWithLabels("smsCode");
    /**
     * mq 发送耗时
     */
    public static final Histogram MQ_SEND_TIME = new PrometheusHistogramMetrics("mq_send_time", "mq_send_time")
            .createWithLabels("smsCode");

    /**
     * 加载刷新配置耗时
     */
    public static final Histogram LOAD_MEMORY_TIME = new PrometheusHistogramMetrics("load_memory_time", "load_memory_time")
            .createWithLabels("moduleType");

    /**
     * 限流耗时
     */
    public static final Histogram SEND_LIMIT_TIME = new PrometheusHistogramMetrics("send_limit_time", "send_limit_time")
            .createWithLabels("smsCode");

    /**
     * 限流次数
     */
    public static final Counter SEND_LIMIT_TOTAL = new PrometheusCounterMetrics("send_limit_total", "send_limit_total")
            .createWithLabels("cfgType");

    /**
     * 黑名单命中次数
     */
    public static final Counter BLACK_TOTAL = new PrometheusCounterMetrics("black_total", "black_total")
            .createWithLabels("appKey", "smsCode");

    /**
     * 发生异常次数
     */
    public static final Counter EXCEPTION_TOTAL = new PrometheusCounterMetrics("exception_total", "exception_total")
            .createWithLabels("exceptionCode");
}
