package com.xhqb.spectre.api.channel.test.producer;


import com.alibaba.fastjson.JSON;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.common.dal.dto.ChannelTestData;
import com.xhqb.spectre.common.dal.dto.mq.ApiChannelMessage;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class ChannelApiProducer {

    @Resource
    private MQTemplate<String> mqTemplate;
    @Resource
    private VenusConfig venusConfig;
    /**
     * topic:spectre-channel-api-test
     */
    @Value("#{'${kael.mq.producers}'.split(',')[4]}")
    private String topic;

    @SneakyThrows
    public void productMsg(List<ApiChannelMessage> messages) {
        if (Objects.isNull(messages)) {
            return;
        }
        mqTemplate.send(topic, JSON.toJSONString(messages));
        log.info("api渠道测试生产者消息发送成功，消息内容：{}", JSON.toJSONString(messages));
    }
}
