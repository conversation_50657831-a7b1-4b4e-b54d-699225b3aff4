package com.xhqb.spectre.api.model.smsresp;

import com.xhqb.spectre.api.model.entity.SMSSendFailedRecord;
import com.xhqb.spectre.api.model.entity.SMSSendSuccessRecord;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchSMSResultVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 批量发送的短信总数
     */
    private Integer total;

    /**
     * 成功发送的短信总数
     */
    private Integer success;

    /**
     * 因各种非法造成的失败短信记录数
     */
    private Integer failure;

    /**
     * 成功下发的记录
     */
    private List<SMSSendSuccessRecord> successSMSRecord;

    /**
     * 未成功下发的记录
     */
    private List<SMSSendFailedRecord> failureSMSRecord;

}
