package com.xhqb.spectre.api.model.smsresp;

import com.xhqb.kael.util.tostring.XhJsonToStringStyle;
import com.xhqb.spectre.api.constant.SendStatusEnum;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class BaseSMSResultVO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回短信发送请求的请求Id
     */
    private String requestId;

    /**
     * 发送状态（0：接口调用失败；1：接口调用成功； 2：接口下发部分成功； 3：接口下发成功）
     */
    private SendStatusEnum sendStatus;

    private String tplCode;

    /**
     * 发送结果描述
     */
    private T smsSendResult;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, XhJsonToStringStyle.XH_JSON_STYLE);
    }


}
