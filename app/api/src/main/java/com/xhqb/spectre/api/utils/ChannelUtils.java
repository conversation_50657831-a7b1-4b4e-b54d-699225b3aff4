package com.xhqb.spectre.api.utils;

import com.xhqb.kael.util.StringUtils;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.model.entity.PhoneIsp;
import com.xhqb.spectre.api.service.MemoryDataService;
import com.xhqb.spectre.common.constant.DisableTypeConstant;
import com.xhqb.spectre.common.dal.dto.*;
import com.xhqb.spectre.common.enums.TplChannelAreaFilterEnum;
import com.xhqb.spectre.common.mq.ChannelCode;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 渠道处理逻辑
 */
public class ChannelUtils {

    private static Logger logger = LoggerFactory.getLogger(ChannelUtils.class);

    /**
     * 判断是否命中模版地域禁用规则
     *
     * @param tplData
     * @param phoneIsp
     */
    public static boolean isTplDisabled(TplData tplData, PhoneIsp phoneIsp) {
        List<TplDisableData> disableData = tplData.getDisableInfoList();
        boolean flag = Boolean.FALSE;
        for (TplDisableData tplDisableData : disableData
        ) {
            if (tplDisableData.getStartTime() != null && tplDisableData.getEndTime() != null) {
                flag = isDisabled(tplDisableData.getStartTime(), tplDisableData.getEndTime(), tplDisableData.getAreaList(), tplDisableData.getIspList(), phoneIsp);
                if (flag) {
                    break;
                }
            }
        }
        return flag;
    }

    /**
     * 判断是否短信类型禁用
     *
     * @param tplData
     * @param memoryDataService
     * @param phoneIsp
     * @return
     */
    public static boolean isSmsTypeDisabled(TplData tplData, MemoryDataService memoryDataService, PhoneIsp phoneIsp) {
        if (tplData == null || memoryDataService == null) {
            return false;
        }
        boolean flag = false;
        List<SmsTypeDisableData> smsTypeDisableDataList = memoryDataService.getSmsTypeDisabledData(tplData.getSmsTypeCode());
        if (!CollectionUtils.isEmpty(smsTypeDisableDataList)) {
            for (SmsTypeDisableData smsTypeDisableData : smsTypeDisableDataList) {
                if (smsTypeDisableData.getStartTime() != null && smsTypeDisableData.getEndTime() != null) {
                    flag = isDisabled(smsTypeDisableData.getStartTime(), smsTypeDisableData.getEndTime(), smsTypeDisableData.getAreaList(), smsTypeDisableData.getIspList(), phoneIsp);
                    if (flag) {
                        break;
                    }
                }
            }
        }
        return flag;
    }

    /**
     * 判断是否在禁用时间范围、
     *
     * @param startDate
     * @param endDate
     * @param areaDataList
     * @param ispList
     * @param phoneIsp
     * @return
     */
    private static boolean isDisabled(String startDate, String endDate, List<AreaData> areaDataList, List<String> ispList, PhoneIsp phoneIsp) {
        Date currDate = new Date();
        boolean flag = Boolean.FALSE;
        try {
            Date starTime = DateTimeUtil.transformStrToDate(startDate, DateTimeUtil.DATE_FORMAT_DATETIME);
            Date endTime = DateTimeUtil.transformStrToDate(endDate, DateTimeUtil.DATE_FORMAT_DATETIME);
            if (!starTime.after(currDate) && !endTime.before(currDate) && isDisabledISP(ispList, phoneIsp.getIsp())) {
                // 在禁用时间范围内
                flag = isAreaDisabled(areaDataList, phoneIsp);
            }
        } catch (Exception e) {
            logger.error("ChannelUtils is Disabled error: {}", e.getMessage());
        }
        return flag;
    }

    /**
     * 判断是否在禁用时间范围（支持新的屏蔽类型）
     *
     * @param disableData
     * @param phoneIsp
     * @return
     */
    private static boolean isDisabledWithType(ChannelAccountDisableData disableData, PhoneIsp phoneIsp) {
        Date currDate = new Date();
        boolean flag = Boolean.FALSE;
        try {
            Date starTime = DateTimeUtil.transformStrToDate(disableData.getStartTime(), DateTimeUtil.DATE_FORMAT_DATETIME);
            Date endTime = DateTimeUtil.transformStrToDate(disableData.getEndTime(), DateTimeUtil.DATE_FORMAT_DATETIME);

            if (!starTime.after(currDate) && !endTime.before(currDate) && isDisabledISP(disableData.getIspList(), phoneIsp.getIsp())) {
                // 在禁用时间范围内，判断屏蔽类型
                Integer disableType = disableData.getDisableType();
                if (disableType == null || disableType.equals(DisableTypeConstant.TYPE_FULL_PERIOD)) {
                    // 类型1：整个时间段屏蔽（原功能）
                    flag = isAreaDisabled(disableData.getAreaList(), phoneIsp);
                    if(flag) {
                        logger.debug("整个时间段屏蔽, disableData={}", disableData);
                    }
                } else if (disableType.equals(DisableTypeConstant.TYPE_PERIOD_TIME)) {
                    // 类型2：固定时间段屏蔽（新功能）
                    if (isInPeriodTime(disableData.getPeriodStartTime(), disableData.getPeriodEndTime())) {
                        flag = isAreaDisabled(disableData.getAreaList(), phoneIsp);
                        if(flag) {
                            logger.debug("固定时间段屏蔽, disableData={}", disableData);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("ChannelUtils isDisabledWithType error: {}", e.getMessage());
        }
        return flag;
    }

    /**
     * 判断当前时间是否在指定的时间段内
     *
     * @param periodStartTime 时间段开始时间（HH:mm格式）
     * @param periodEndTime   时间段结束时间（HH:mm格式）
     * @return
     */
    private static boolean isInPeriodTime(String periodStartTime, String periodEndTime) {
        if (periodStartTime == null || periodEndTime == null) {
            return false;
        }
        try {
            Calendar now = Calendar.getInstance();
            int currentHour = now.get(Calendar.HOUR_OF_DAY);
            int currentMinute = now.get(Calendar.MINUTE);
            int currentMinutes = currentHour * 60 + currentMinute;

            String[] startParts = periodStartTime.split(":");
            String[] endParts = periodEndTime.split(":");
            int startMinutes = Integer.parseInt(startParts[0]) * 60 + Integer.parseInt(startParts[1]);
            int endMinutes = Integer.parseInt(endParts[0]) * 60 + Integer.parseInt(endParts[1]);

            return currentMinutes >= startMinutes && currentMinutes < endMinutes;
        } catch (Exception e) {
            logger.error("ChannelUtils isInPeriodTime error: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否命中运营商 如果为空则通过
     *
     * @return
     */
    private static boolean isDisabledISP(List<String> ispList, String phoneIsp) {
        boolean flag = Boolean.FALSE;
        if (ispList == null || ispList.size() == 0) {
            return false;
        }
        for (String isp : ispList
        ) {
            if (SmsApiApplicationConstant.AREA_DISABLE_PROVINCE.equalsIgnoreCase(isp)) {
                flag = true;
                break;
            } else if (phoneIsp.indexOf(isp) > -1) {
                flag = true;
                break;
            }
        }
        return flag;
    }

    /**
     * 获取可用渠道信息
     *
     * @param tplData           模版信息
     * @param memoryDataService
     * @param phoneIsp          手机号码归属地
     * @param paramMap          短信模版顺序参数
     * @param paramList         短信模版$变量参数
     * @return
     * @throws Exception
     */
    public static List<ChannelCode> getChannelCode(TplData tplData,
                                                   MemoryDataService memoryDataService,
                                                   PhoneIsp phoneIsp,
                                                   Map<String, String> paramMap,
                                                   List<Map<String, String>> paramList) {
        List<ChannelCode> channelCodeList = new ArrayList<>();
        List<TplChannelData> tplChannelDatalist = tplData.getChannelInfoList();
        if (tplChannelDatalist == null || tplChannelDatalist.size() == 0) {
            return null;
        }
        for (TplChannelData tplChannelData : tplChannelDatalist
        ) {
            List<ChannelAccountDisableData> disableDataList = memoryDataService.getChannelDisabledData(tplChannelData.getChannelAccountId());
            boolean channelDisabled = isChannelDisabled(disableDataList, phoneIsp);
            if (!channelDisabled) {
                // 获取可用渠道
                if (TplChannelAreaFilterEnum.checkFilterType(tplChannelData.getAreaFilterType())) {
                    if (TplChannelAreaFilterEnum.CONTAINS.getType().equals(tplChannelData.getAreaFilterType())) {
                        // 包含
                        if (isDisabledISP(tplChannelData.getIspList(), phoneIsp.getIsp())) {
                            List<AreaData> areaDataList = tplChannelData.getAreaList();
                            boolean flag = isAreaDisabled(areaDataList, phoneIsp);
                            if (flag) {
                                ChannelCode channelCode = ChannelCode.buildChannelCode(tplChannelData);
                                if (channelCode != null) {
                                    channelCode.setMsgContent(buildMsgContent(paramMap, paramList, tplChannelData.getTplContent(), phoneIsp.getPhone()));
                                    channelCodeList.add(channelCode);
                                }
                            }
                        }
                    } else if (TplChannelAreaFilterEnum.NOT_CONTAINS.getType().equals(tplChannelData.getAreaFilterType())) {
                        // 不包含即禁用
                        if (isDisabledISP(tplChannelData.getIspList(), phoneIsp.getIsp())) {
                            List<AreaData> areaDataList = tplChannelData.getAreaList();
                            boolean flag = isAreaDisabled(areaDataList, phoneIsp);
                            if (!flag) {
                                ChannelCode channelCode = ChannelCode.buildChannelCode(tplChannelData);
                                if (channelCode != null) {
                                    channelCode.setMsgContent(buildMsgContent(paramMap, paramList, tplChannelData.getTplContent(), phoneIsp.getPhone()));
                                    channelCodeList.add(channelCode);
                                }
                            }
                        }
                    }
                } else {
                    logger.warn("模版渠道地域过滤类型配置错误{}", tplChannelData.toString());
                }
            }
        }
        return channelCodeList;
    }

    /**
     * 构建短信内容
     *
     * @param paramMap 短信参数
     * @param content  模版内容
     * @param phone    手机号码
     * @return
     */
    public static String buildMsgContent(Map<String, String> paramMap, String content, String phone) {
        if (StringUtils.isEmpty(content) || StringUtils.isEmpty(phone)) {
            return "";
        }
        if (paramMap != null && paramMap.containsKey(phone)) {
            String[] param = paramMap.get(phone).split(",");
            if (param != null && param.length > 0) {
                // 替换参数
                //String finalStr = String.format(replaceParam(content), param);
                return TemplateUtil.parseSequentialPlaceholders(content, param);

            } else {
                return content;
            }
        }
        return content;
    }

    /**
     * 替换短信模版[*]参数, 弃用
     * @param content
     * @return
     */
    private static String replaceParam(String content) {
        if (StringUtils.isEmpty(content)) {
            return null;
        }
        // 先将模版中的%转义
        content = content.replaceAll("(?<!%)%(?!%)", "%%");
        String result = content;
        Pattern p = Pattern.compile(SmsApiApplicationConstant.TPL_CONTENT_PARAM);
        Matcher m = p.matcher(content);
        Pattern p2 = Pattern.compile(SmsApiApplicationConstant.TPL_CONTENT_PARAM2);
        Matcher m2 = p2.matcher(content);
        if (m.find()) {
            result = new StringBuilder(content).toString().replaceAll(SmsApiApplicationConstant.TPL_CONTENT_PARAM, "%s");
        } else if (m2.find()) {
            result = new StringBuilder(content).toString().replaceAll(SmsApiApplicationConstant.TPL_CONTENT_PARAM2, "%s");
        }
        return result;
    }

    public static String buildMsgContent(Map<String, String> paramMap, List<Map<String, String>> paramList, String content, String phone) {
        if (paramMap != null && !paramMap.isEmpty()) {
            return buildMsgContent(paramMap, content, phone);
        } else if (paramList != null && !paramList.isEmpty()) {
            return buildMsgContent(paramList, content, phone);
        }
        return "";
    }

    /**
     * 构建短信内容
     *
     * @param params
     * @param content
     * @param phone
     * @return
     */
    public static String buildMsgContent(List<Map<String, String>> params, String content, String phone) {
        if (StringUtils.isEmpty(content) || StringUtils.isEmpty(phone) || params == null) {
            return "";
        }
        List<Map<String, String>> list = params.stream()
                .filter(item -> item.get(SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY) != null && item.get(SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY).equalsIgnoreCase(phone)).collect(Collectors.toList());
        if (list.size() == 0) {
            return content;
        }
        Map<String, String> map = list.get(0);
        // 解析短信内容
        Pattern p = Pattern.compile(SmsApiApplicationConstant.TPL_CONTENT_PARAM2);
        Matcher m = p.matcher(content);
        while (m.find()) {
            // 取出目标值
            String matchKey = m.group(2); // 不包含${}
            String matchStr = m.group(); // 包含${}
//            logger.info("content: {} 匹配内容：{}", content, matchStr);
            if (!SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY.equalsIgnoreCase(matchKey) && !StringUtils.isEmpty(matchKey)) {
                String value = map.get(matchKey);
                content = content.replaceAll("(\\$\\{)(" + matchKey + ")}", value);
            }
        }
        return content;
    }

    /**
     * 提取kv变量中模板的参数值
     *
     * @param params
     * @param content
     * @param phone
     * @return
     */
    public static ArrayList<String> extractPlaceholderValues(List<Map<String, String>> params,
                                                  String content,
                                                  String phone) {
        if (StringUtils.isEmpty(content) || StringUtils.isEmpty(phone) || params == null || params.isEmpty()) {
            return new ArrayList<>();
        }

        List<Map<String, String>> matchedMaps = params.stream()
                .filter(item -> item != null
                        && item.get(SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY) != null
                        && item.get(SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY).equalsIgnoreCase(phone))
                .collect(Collectors.toList());

        if (matchedMaps.isEmpty()) {
            return new ArrayList<>();
        }

        Map<String, String> paramMap = matchedMaps.get(0);

        Pattern pattern = Pattern.compile(SmsApiApplicationConstant.TPL_CONTENT_PARAM2);
        Matcher matcher = pattern.matcher(content);

        ArrayList<String> values = new java.util.ArrayList<>();

        while (matcher.find()) {
            String key = matcher.group(2);

            if (SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY.equalsIgnoreCase(key)) {
                continue;
            }

            if (!StringUtils.isEmpty(key)) {
                String value = paramMap.get(key);
                if (value != null) {
                    values.add(value);
                }
            }
        }

        return values;
    }

    /**
     * 判断是否命中渠道禁用配置
     *
     * @param disableDataList
     * @param phoneIsp
     * @return
     * @throws Exception
     */
    private static boolean isChannelDisabled(List<ChannelAccountDisableData> disableDataList, PhoneIsp phoneIsp) {
        boolean channelDisabled = Boolean.FALSE;
        if (disableDataList == null || disableDataList.size() == 0) {
            return Boolean.FALSE;
        }
        for (ChannelAccountDisableData disableData : disableDataList) {
            if (disableData.getStartTime() != null && disableData.getEndTime() != null) {
                // 支持屏蔽日期范围内的固定时间段屏蔽
                channelDisabled = isDisabledWithType(disableData, phoneIsp);
                if (channelDisabled) {
                    break;
                }
            }
        }
        return channelDisabled;
    }

    /**
     * 判断是否命中区域禁用配置
     * 如果为空则返回false
     *
     * @param areaDataList
     * @param phoneIsp
     * @return
     */
    private static boolean isAreaDisabled(List<AreaData> areaDataList, PhoneIsp phoneIsp) {
        boolean flag = Boolean.FALSE;
        if (areaDataList == null || areaDataList.size() == 0) {
            return false;
        }
        for (AreaData areaData : areaDataList
        ) {
            if (StringUtils.isEmpty(areaData.getProvinceShortName()) && StringUtils.isEmpty(areaData.getCityShortName())) {
                flag = true;
                logger.warn("{}区域信息为空", phoneIsp.getPhone());
                break;
            }
            if (areaData.getProvinceShortName() != null && SmsApiApplicationConstant.AREA_DISABLE_PROVINCE.equalsIgnoreCase(areaData.getProvinceShortName())) {
                flag = true;
//                logger.info("{}命中全国渠道规则", phoneIsp.getPhone());
                break;
            }
            if (StringUtils.isEmpty(areaData.getCityShortName())) {
                if (areaData.getProvinceShortName() != null && areaData.getProvinceShortName().equalsIgnoreCase(phoneIsp.getProvince())) {
                    // 全省禁用
                    flag = true;
//                    logger.info("{}命中全省渠道规则", phoneIsp.getPhone());
                    break;
                }
            } else if (areaData.getCityShortName() != null && areaData.getCityShortName().equalsIgnoreCase(phoneIsp.getCity())) {
                // 全市禁用
                flag = true;
//                logger.info("{}命中全市渠道规则", phoneIsp.getPhone());
                break;
            }
        }
        return flag;
    }

}
