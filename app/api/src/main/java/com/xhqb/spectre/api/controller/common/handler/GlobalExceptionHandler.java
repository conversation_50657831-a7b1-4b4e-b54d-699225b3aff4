package com.xhqb.spectre.api.controller.common.handler;

import com.xhqb.spectre.api.controller.common.CommonResult;
import com.xhqb.spectre.api.exception.GlobalException;
import com.xhqb.spectre.api.exception.SMSSenderException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;

@RestControllerAdvice
public class GlobalExceptionHandler {

    private final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 全局异常
     */
    @ExceptionHandler(value = GlobalException.class)
    public CommonResult handle(GlobalException e) {
        if (e.getErrorCode() != null) {
            return CommonResult.failed(e.getErrorCode());
        }
        return CommonResult.failed(e.getMessage());
    }

    /**
     * 方法参数校验
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommonResult handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        logger.warn("handleMethodArgumentNotValidException: {}", e.getBindingResult().getFieldError().getDefaultMessage());
        return CommonResult.failed(e.getBindingResult().getFieldError().getDefaultMessage());
    }

    /**
     * ValidationException
     */
    @ExceptionHandler(ValidationException.class)
    public CommonResult<String> handleValidationException(ValidationException e) {
        logger.warn("handleValidationException: {}", e.getCause().getMessage());
        return CommonResult.failed(e.getCause().getMessage());
    }

    /**
     * ConstraintViolationException
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public CommonResult handleConstraintViolationException(ConstraintViolationException e) {
        logger.warn("handleConstraintViolationException: {}", e.getMessage());
        return CommonResult.failed(e.getMessage());
    }

    /**
     * RuntimeException
     */
    @ExceptionHandler(RuntimeException.class)
    public CommonResult handleRuntimeException(RuntimeException e) {
        logger.warn("handleRuntimeException: {}", e.getMessage());
        return CommonResult.failed(e.getMessage());
    }

    /**
     * DataProcessException

     @ExceptionHandler(SMSDataProcessException.class) public CommonResult handleDataProcessException(SMSDataProcessException e) {
     logger.error("handleDataProcessException: {}", e.getMessage());
     return CommonResult.failed(e.getMessage());
     }     */

    /**
     * SMSSenderException
     */
    @ExceptionHandler(SMSSenderException.class)
    public CommonResult handleSMSSenderException(SMSSenderException e) {
        logger.warn("handleSMSSenderException: {}", e.getMessage());
        return CommonResult.failed(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public CommonResult handException(Exception e) {
        logger.warn("发现异常：{}", e.getMessage());
        return CommonResult.failed("系统异常：" + e.getMessage());

    }
}
