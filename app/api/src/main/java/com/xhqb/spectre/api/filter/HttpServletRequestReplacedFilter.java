package com.xhqb.spectre.api.filter;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.util.web.WebUtils;
import com.xhqb.spectre.api.constant.LuciferConstant;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.controller.common.CommonResult;
import com.xhqb.spectre.api.service.MemoryDataService;
import com.xhqb.spectre.api.servlet.MyHttpServletRequestWrapper;
import com.xhqb.spectre.api.servlet.MyHttpServletResponseWrapper;
import com.xhqb.spectre.api.utils.GetRequestJsonUtil;
import com.xhqb.spectre.common.dal.dto.TplData;
import io.prometheus.client.Collector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;

/**
 * 增加埋点逻辑
 *
 * <AUTHOR>
 * @date 2021/10/14
 */
//@WebFilter(urlPatterns = "/*", filterName = "requestReplaced")
@Slf4j
public class HttpServletRequestReplacedFilter implements Filter {

    private MemoryDataService memoryDataService;

    @Override
    public void destroy() {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain chain) throws IOException, ServletException {
        long start = System.nanoTime();
        ApplicationContext ac = WebApplicationContextUtils.getWebApplicationContext(request.getServletContext());
        memoryDataService = (MemoryDataService) ac.getBean("memoryDataServiceImpl");
        HttpServletRequest httpServletRequest = ((HttpServletRequest) request);
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        MyHttpServletResponseWrapper responseWrapper = new MyHttpServletResponseWrapper(httpServletResponse);

        ServletRequest requestWrapper = null;
        // the best path url (for restful)
        String mappingUri = httpServletRequest.getRequestURI();
        String tplCode = "";
        String signCode = "";
        LuciferConstant.GAUGE.labels(mappingUri).inc();
        String ip = WebUtils.getIpAddr(httpServletRequest); // 请求ip
//        log.info("请求IP：{}", ip);
        try {
            if (request instanceof HttpServletRequest) {
                requestWrapper = new MyHttpServletRequestWrapper(httpServletRequest);
                // 获取短信类型 没有tplCode参数标识是校验验证码接口
                tplCode = getTplCode((MyHttpServletRequestWrapper) requestWrapper);
                signCode = getSignCode((MyHttpServletRequestWrapper) requestWrapper);
            }
            if (requestWrapper == null) {
                chain.doFilter(request, responseWrapper);
            } else {
                chain.doFilter(requestWrapper, responseWrapper);
            }
        } finally {
            byte[] bytes = responseWrapper.getBytes();
            // response status
            String status = getStatue(httpServletResponse);
            String resultCode = getResultCode(bytes);
            // request length
            long requestContentLength = getContentLength(httpServletRequest);
            String smsCode = "checkVerify";
            if (!StringUtils.isBlank(tplCode)) {
                TplData tplData = memoryDataService.getTplDataByCode(tplCode, signCode);
                if (!ObjectUtils.isEmpty(tplData)) {
                    smsCode = tplData.getSmsTypeCode();
                }
            }
            // 请求总数
            LuciferConstant.COUNTER.labels(mappingUri, status, smsCode, resultCode).inc();
            // 请求时间
            LuciferConstant.HISTOGRAM.labels(mappingUri, status, smsCode, resultCode)
                    .observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
            LuciferConstant.RECEIVED_BYTES.labels(mappingUri, status, smsCode, resultCode).observe(requestContentLength);
            LuciferConstant.GAUGE.labels(mappingUri).dec();

            //将数据 再写到 response 中
            response.getOutputStream().write(bytes);
            response.getOutputStream().flush();
            response.getOutputStream().close();
        }
    }

    private long getContentLength(HttpServletRequest httpServletRequest) {
        long contentLength = httpServletRequest.getContentLength();
        if (contentLength < 0) {
            contentLength = 0;
        }
        return contentLength;
    }

    private String getStatue(HttpServletResponse response) {
        return String.valueOf(response.getStatus());
    }

    /**
     * 获取模版code
     *
     * @param request
     * @return
     */
    private String getTplCode(HttpServletRequest request) {
        String code = "";
        if (request == null) {
            return code;
        }
        JSONObject json = getRequestJson(request);
        if (json != null) {
            code = json.getString(SmsApiApplicationConstant.PARAM_TPLCODE);
        }
        return code;
    }

    private JSONObject getRequestJson(HttpServletRequest request) {
        JSONObject json = null;
        if (request == null || request.getMethod().equalsIgnoreCase("get")) {
            return null;
        }
        try {
            json = GetRequestJsonUtil.getRequestJsonObject(request);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return json;
    }

    /**
     * 获取signcode
     *
     * @param request
     * @return
     */
    private String getSignCode(HttpServletRequest request) {
        String code = "";
        if (request == null) {
            return code;
        }
        JSONObject json = getRequestJson(request);
        if (json != null) {
            code = json.getString(SmsApiApplicationConstant.PARAM_SIGNCODE);
            if (StringUtils.isBlank(code)) {
                code = SmsApiApplicationConstant.SIGN_CODE_DEFAULT;
            }
        }
        return code;
    }

    /**
     * 获取接口返回code
     *
     * @param bytes
     * @return
     */
    private String getResultCode(byte[] bytes) {
        String code = "";
        try {
            String val = new String(bytes, "UTF-8");
            CommonResult commonResult = JSONObject.parseObject(val, CommonResult.class);
            if (!ObjectUtils.isEmpty(commonResult)) {
                code = String.valueOf(commonResult.getCode());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return code;
    }

    @Override
    public void init(FilterConfig arg0) throws ServletException {
    }
}
