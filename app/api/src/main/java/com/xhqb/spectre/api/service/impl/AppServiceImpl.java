package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.model.smsresp.AppVO;
import com.xhqb.spectre.api.service.AppService;
import com.xhqb.spectre.common.dal.mapper.AppMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AppServiceImpl implements AppService {

    @Autowired
    private AppMapper appMapper;

    @Override
    public List<AppVO> getAll() {
        return appMapper.selectAll().stream()
                .map(AppVO::buildVO)
                .collect(Collectors.toList());
    }
}
