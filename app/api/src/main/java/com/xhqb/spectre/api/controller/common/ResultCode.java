package com.xhqb.spectre.api.controller.common;

/**
 * 响应码枚举，参考HTTP状态码的语义
 */
public enum ResultCode implements IErrorCode {

    /**
     * 成功
     */
    SUCCESS(200, "success"),

    /**
     * 失败
     */
    FAILED(400, "fail"),

    /**
     * 没有相关权限
     */
    FORBIDDEN(403, "没有相关权限"),

    /**
     * 参数缺失
     */
    MISS_PARAM(1000, "参数缺失"),
    /**
     * logID
     */
    WRONG_SMS_TEMPLATE(1001, "无效的模版CODE"),
    /**
     * AppID不能为空
     */
    APPID_NOTNULL(1002, "appCode不能为空"),
    /**
     * 缺失request参数
     */
    MISS_REQUEST_PARAM(1003, "缺失request参数"),
    /**
     * 缺失outBizId参数
     */
    MISS_ID_PARAM(1004, "缺失outBizId参数"),
    /**
     * 缺失渠道参数
     */
    MISS_CHANNEL_PARAM(1005, "缺失渠道参数"),

    //自定义批量短信发送
    /**
     * 缺失短信内容参数
     */
    MISS_MESSAGE_CONTENT(1006, "缺失短信内容参数"),
    /**
     * 缺失短信类型参数
     */
    MISS_MESSAGE_TYPE(1007, "缺失短信类型参数"),
    /**
     * 缺失短信签名参数
     */
    MISS_MESSAGE_SIGN(1008, "缺失短信签名参数"),
    /**
     * 缺失短信参数号码集
     */
    MISS_MSG_PARAM_PHONES(1009, "缺失短信参数号码集"),
    /**
     * 缺失短信参数号码集
     */
    MISS_MSG_PHONES(1010, "缺失短信号码集"),
    /**
     * 无效手机号码
     */
    WRONG_PHONE_NUMBER(1011, "手机号码无效"),

    /**
     * 缺少短信参数或参数个数有误
     */
    WRONG_SMS_SEND_PARAMS(1012, "缺少短信参数或参数个数有误"),
    /**
     * 请求参数非法
     */
    ILLEGAL_PARAM(1013, "请求参数非法"),

    /**
     * 参数不能为空
     */
    DYNAMIC_PARAM_NOT_VALID(1014, "所选模版参数不能为空"),

    BLACK_PHONE_VALID(1015, "手机号码命中黑名单"),

    APPCODE_OUTORDERID_EXIST(1016, "appCode和outOrderId已经存在"),

    VERIFY_CODE_LENGTH_ERROR(1017, "验证码长度必须在4-6位之间"),

    WRONG_VERIFY_CODE(1018, "验证码不能为空"),

    WRONG_IDENTIFICATION_CODE(1019, "验证码识别码不能为空"),

    CURTIME_ERROR(1020, "缺少timestamp参数或参数错误"),

    MISS_NONCE_ERROR(1021, "缺少随机码参数或随机码已失效"),

    MISS_NONCE_LENGTH_ERROR(1022, "随机码长度必须在32位内"),

    CHANNEL_EMPTY(1023, "没有可用的渠道信息"),

    DISABLED_VALID(1024, "命中禁用规则"),

    SEND_TIME_VALID(1025, "延时发送时间设置错误"),

    SMS_TYPE_DISABLED(1026, "短信类型被禁用"),

    /**
     * 参数检验失败
     */
    VALIDATE_FAILED(2000, "参数检验失败"),

    /**
     * 时间戳无效
     */
    ILLEDGE_TIMESTAMP(2001, "时间戳无效"),

    /**
     * AppId/AppKey无效
     */
    ILLEDGE_KEY(2002, "AppKey无效"),

    /**
     * 签名超时
     */
    SIGN_TIMEOUT(2003, "签名超时"),

    /**
     * 签名认证失败
     */
    UNAUTHORIZED(2004, "签名认证失败"),


    /**
     * 短信发送过于频繁
     */
    SMS_SEND_FREQUENTLY(2005, "短信发送过于频繁，请稍后再试"),

    /**
     * 发送号码数超过最大号码数
     */
    EXCEED_MAX_PHONE_NUMBERS(2006, "手机号码超出最大允许提交值"),

    /**
     * 缺失短信发送权重
     */
    MISS_MSG_SEND_WEIGHT(2007, "缺失短信发送权重"),

    /**
     * 缺失短信模板
     */
    MISS_MSG_TEMPLATE(2008, "缺失短信模板"),

    /**
     * 短信模板不存在
     */
    MSG_TEMPLATE_NOT_EXIST(2009, "短信模板不存在"),


    /**
     * 不支持该号码
     */
    ILLEGAL_NUM(2010, "不支持该号码"),

    /**
     * 未知短信发送类型
     */
    UNKONW_MSG_TYPE(2011, "未知的短信发送类型"),

    //发送频率、重发问题

    /**
     * 重复发送同一条短信
     */
    DUPLICATE_MSG(2012, "重复发送同一条短信"),

    /**
     * 同号码短时间内发送了相同短信内容
     */
    FREQUENT_IN_MIN(2013, "同号码短时间内发送了相同短信内容"),

    /**
     * 同号码发送该短信模板次数达到上限
     */
    FREQUENT_IN_DAY(2014, "同号码发送该短信模板次数达到上限"),


    //未知结果码

    /**
     * 无http结果返回
     */
    NULL_RETURN(2015, "无http结果返回"),

    /**
     * unknownRsCode
     */
    UNKNOWN_RS_CODE(2016, "未定义的MsgSendResultEnum结果码"),

    /**
     * 成功
     */
    RATELIMIT(2017, "系统繁忙"),

    /**
     * 成功
     */
    REPEATSUBMITSMSREQUET(2018, "重复提交短信发送请求"),

    // 发送频率检查限制
    SEND_LIMIT_BLOCKED(2019, "短信发送频率越限"),

    SMS_TYPE_CODE_ERROR(2020, "短信模版类型错误"),

    SMS_TYPE_CODE_ERROR_MARKET(2021, "短信模版不是市场营销类型"),

    SMS_TYPE_CODE_ERROR_VERIFY(2022, "短信模版不是验证码类型"),

    SUPPORT_CONTENT_API_FLAG_FALSE(2023, "应用不支持短信内容API"),

    TPL_CONTENT_ERROR(2024, "短信内容未进行备案"),
    USER_SHORT_URL_LIMIT_ERROR(2025, "用户短链新增越限"),

    /**
     * 系统异常
     */
    SYS_FAILURES(3001, "系统异常");


    private Integer resultCode;
    private String resultMsg;

    private ResultCode(Integer code, String message) {
        this.resultCode = code;
        this.resultMsg = message;
    }

    public Integer getResultCode() {
        return resultCode;
    }

    private void setResultCode(Integer code) {
        this.resultCode = code;
    }


    public String getResultMsg() {
        return resultMsg;
    }


    private void setResultMsg(String message) {
        this.resultMsg = message;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code
     * @return
     */
    public static ResultCode getByCode(Integer code) {
        ResultCode[] arr = values();
        int length = arr.length;

        for (int i = 0; i < length; ++i) {
            ResultCode statusEnum = arr[i];
            if (statusEnum.getResultCode().equals(code)) {
                return statusEnum;
            }
        }

        return null;
    }

}
