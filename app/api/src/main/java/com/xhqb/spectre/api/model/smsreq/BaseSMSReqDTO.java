package com.xhqb.spectre.api.model.smsreq;

import com.xhqb.kael.util.tostring.XhJsonToStringStyle;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class BaseSMSReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务应用ID
     */
    private String appCode;

    /**
     * 签名： 主要是用于验权验签
     */
    private String sign;

    /**
     * 外部流水线号 requestId
     */
    private String requestId;

    /**
     * tplCode 模版编号
     */
    private String tplCode;

    /**
     * 定时/实时发送： 空即实时发送，非空且时间是未来时间即代表是定时发送
     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="Asia/Shanghai")
    private String sendTime = null;

    /**
     * 短信发送号码组，以逗号分开
     */
    private String phoneNumbers;

    /**
     * 发送短信类型 （可选，如未指定，后续可根据appId、templateId自动设置）
     */
    private MessageTypeEnum messageTypeEnum;

    private Integer batchId;

    /**
     * 渠道id，如果设置了该参数就不读取模版关联的渠道信息
     */
    private Integer channelAccountId;

    /**
     * 签名code，如果没有传递就使用默认的小花签名
     */
    private String signCode;

    /**
     * 业务批次号
     */
    private String bizBatchId;

    /**
     * 是否回调消息中心（0：不回调 1：回调）
     */
    private Integer callMetis;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, XhJsonToStringStyle.XH_JSON_STYLE);
    }

}
