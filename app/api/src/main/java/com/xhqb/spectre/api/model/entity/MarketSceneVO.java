package com.xhqb.spectre.api.model.entity;

import com.xhqb.spectre.common.dal.entity.MarketSceneDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class MarketSceneVO implements Serializable {
    private static final long serialVersionUID = -1L;
    /**
     * 主键
     */
    private Integer id;

    /**
     * 营销场景名称
     */
    private String name;

    public static MarketSceneVO buildMarketSceneVO(MarketSceneDO marketSceneDO) {
        if (Objects.isNull(marketSceneDO)) {
            return null;
        }
        return MarketSceneVO.builder()
                .id(marketSceneDO.getId())
                .name(marketSceneDO.getName())
                .build();
    }
}
