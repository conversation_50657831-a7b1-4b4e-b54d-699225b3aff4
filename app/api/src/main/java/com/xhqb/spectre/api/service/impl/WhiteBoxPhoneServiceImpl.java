package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.constant.RedisConstants;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.common.dal.entity.MobileWhiteDO;
import com.xhqb.spectre.common.dal.mapper.MobileWhiteMapper;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 参考黑名单实现逻辑
 * 1、cfgType: 频率检查类型
 * 2、appId： 应用Id
 * 3、phoneNum：手机号码
 */
@Service
public class WhiteBoxPhoneServiceImpl implements com.xhqb.spectre.api.service.WhiteBoxPhoneService, InitializingBean {

    @Resource
    RedisTemplate redisTemplate;

    @Resource
    MobileWhiteMapper mobileWhiteMapper;


    private final Map<String, Map<String, String>> whiteBoxPhoneMap = new HashMap<>();

    /**
     * 判断是白名单
     *
     * @param appCode
     * @param cfgType
     * @param phoneNum
     * @return
     */
    @Override
    public Boolean isWhiteBoxAllowedFromDB(String appCode, String cfgType, String phoneNum) {
        MobileWhiteDO mobileWhite = new MobileWhiteDO();
        mobileWhite.setCfgType(cfgType);
        mobileWhite.setAppCode(appCode);
        mobileWhite.setMobile(phoneNum);

        MobileWhiteDO whiteMobile = mobileWhiteMapper.selectOne(mobileWhite);
        if (whiteMobile == null) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean isWhiteBoxAllowed(String appId, String cfgType, String phoneNum) {
        return redisTemplate.opsForHash().get(SmsApiApplicationConstant.MOBILE_WHITE_PREFIX + cfgType, appId + ":" + phoneNum) != null;
    }

    @Override
    public void loadWhiteBoxPhoneNumberList() {
        List<MobileWhiteDO> whiteList = mobileWhiteMapper.loadAllWhiteInfo();
        whiteBoxPhoneMap.clear();
        for (MobileWhiteDO mobileWhite : whiteList) {
            Map<String, String> innerMap = new HashMap<String, String>();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(mobileWhite.getAppCode())
                    .append(":")
                    .append(mobileWhite.getMobile());
            innerMap.put(stringBuilder.toString(), "allowed");
            whiteBoxPhoneMap.put(mobileWhite.getCfgType(), innerMap);
        }

        whiteBoxPhoneMap.forEach((k, v) -> {
            v.forEach((k1, v1) -> {
                redisTemplate.opsForHash().put(RedisConstants.MOBILE_WHITE_PREFIX + k, k1, "allowed");
            });
        });
    }

    /**
     * 刷新白名单更新列表
     * 1、 后续补充实现
     */
    @Override
    public void refreshWhiteBoxPhoneNumberList() {
        clearWhiteBoxPhoneNumberList();
        loadWhiteBoxPhoneNumberList();
    }

    /**
     * 本块逻辑待实现
     */
    private void clearWhiteBoxPhoneNumberList() {
        // 实现白名单的缓存刷新逻辑
        redisTemplate.delete(RedisConstants.MOBILE_WHITE_PREFIX + "*");
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        loadWhiteBoxPhoneNumberList();
    }
}
