package com.xhqb.spectre.api.aspect;

import com.google.common.collect.ImmutableList;
import com.xhqb.spectre.api.annotation.RateLimit;
import com.xhqb.spectre.api.constant.RateLimitType;
import com.xhqb.spectre.api.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.Serializable;
import java.lang.reflect.Method;

@Slf4j
@Aspect
@Configuration
public class RedisSubLimitAspect {

    private final RedisTemplate<String, Serializable> limitRedisTemplate;

    @Autowired
    public RedisSubLimitAspect(RedisTemplate<String, Serializable> limitRedisTemplate) {
        this.limitRedisTemplate = limitRedisTemplate;
    }

    @Around("execution(* com.xhqb.spectre.api.service.impl.MobileLimitServiceImpl.postSubtractAppFrequencyLimit(..))")
    public Object interceptor(ProceedingJoinPoint pjp) {
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        Method method = signature.getMethod();
        RateLimit limitAnnotation = method.getAnnotation(RateLimit.class);
        RateLimitType limitType = limitAnnotation.limitType();
        Object[] args = pjp.getArgs();
        if (args.length != 1) {
            return false;
        }
        String key = null;
        switch (limitType) {
            case IP:
                key = CommonUtil.getIpAddress();
                break;
            case CUSTOMER:
                key = args[0].toString();
                break;
            default:
                key = StringUtils.upperCase(method.getName());
        }
        ImmutableList<String> keys = ImmutableList.of(key);
        try {
            Number count = limitRedisTemplate.execute(RedisLuaScript.arDecr, keys);
//            log.info("check substr limit count is {} for key = {}", count, key);
            return pjp.proceed();
        } catch (Throwable e) {
            if (e instanceof RuntimeException) {
                throw new RuntimeException(e.getLocalizedMessage());
            }
            throw new RuntimeException("server exception");
        }
    }
}
