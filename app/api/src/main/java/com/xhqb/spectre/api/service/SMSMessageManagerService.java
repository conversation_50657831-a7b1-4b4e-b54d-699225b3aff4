package com.xhqb.spectre.api.service;

import com.xhqb.spectre.api.model.smsreq.ContentSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.SingleSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.SingleTestSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.VerifyCodeSMSReqDTO;
import com.xhqb.spectre.api.model.smsresp.BaseSMSResultVO;
import com.xhqb.spectre.api.model.smsresp.VerifyCodeResultVO;

public interface SMSMessageManagerService {


    /**
     * 发送短信
     *
     * @param singleSMSReqDTO
     * @return
     */
    BaseSMSResultVO dealSingleSms(SingleSMSReqDTO singleSMSReqDTO);

    /**
     * 发送验证码
     *
     * @param verifyCodeSMSReqDTO
     * @return
     */
    VerifyCodeResultVO dealVerifyCodeSms(VerifyCodeSMSReqDTO verifyCodeSMSReqDTO);

    /**
     * 按内容发送短信
     * @param contentSMSReqDTO
     * @return
     */
    BaseSMSResultVO dealContentSms(ContentSMSReqDTO contentSMSReqDTO);


    /**
     * 发送测试短信
     *
     * @param singleTestSMSReqDTO
     * @return
     */
    BaseSMSResultVO dealTestSingleSms(SingleTestSMSReqDTO singleTestSMSReqDTO);

}
