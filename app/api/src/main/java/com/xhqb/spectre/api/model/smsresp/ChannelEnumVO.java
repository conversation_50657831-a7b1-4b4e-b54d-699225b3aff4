package com.xhqb.spectre.api.model.smsresp;

import com.xhqb.spectre.common.dal.entity.ChannelDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelEnumVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String code;

    private String name;

    public static ChannelEnumVO buildVO(ChannelDO channelDO) {
        return ChannelEnumVO.builder()
                .code(channelDO.getCode())
                .name(channelDO.getName())
                .build();
    }
}
