package com.xhqb.spectre.api.bi.mapper;

import com.xhqb.spectre.api.bi.entity.DebtSmsDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【t_debt_sms(债转通知短信)】的数据库操作Mapper
* @createDate 2024-11-07 17:25:44
* @Entity generator.entity.DebtSms
*/
public interface DebtSmsMapper {

    int deleteByPrimaryKey(Long id);

    int insert(DebtSmsDO record);

    int insertSelective(DebtSmsDO record);

    DebtSmsDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DebtSmsDO record);

    int updateByPrimaryKey(DebtSmsDO record);

    List<DebtSmsDO> selectByContractNo(@Param("contractNos") Set<String> contractNos);

}
