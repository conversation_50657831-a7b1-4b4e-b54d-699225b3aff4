package com.xhqb.spectre.api.config;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.core.http.HttpClient;
import com.aliyun.httpcomponent.httpclient.ApacheAsyncHttpClientBuilder;
import com.aliyun.sdk.service.dytnsapi20200217.AsyncClient;
import darabonba.core.client.ClientOverrideConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.net.ssl.KeyManager;
import javax.net.ssl.X509TrustManager;
import java.time.Duration;

@Configuration
public class AliyunClientConfig {

    @Resource
    private VenusConfig venusConfig;

    private HttpClient createHttpClient() {
        return new ApacheAsyncHttpClientBuilder()
                .connectionTimeout(Duration.ofSeconds(venusConfig.getConnectionTimeout()))
                .responseTimeout(Duration.ofSeconds(venusConfig.getResponseTimeout()))
                .maxConnections(venusConfig.getMaxConnections())
                .maxIdleTimeOut(Duration.ofSeconds(venusConfig.getMaxIdleTimeOut()))
                .x509TrustManagers(new X509TrustManager[]{})
                .keyManagers(new KeyManager[]{})
                .ignoreSSL(false)
                .build();
    }

    private StaticCredentialProvider createCredentialProvider() {
        return StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(venusConfig.getAccessKeyId())
                .accessKeySecret(venusConfig.getAccessKeySecret())
                .build());
    }

    @Bean
    public AsyncClient asyncClient() {
        return AsyncClient.builder()
                .httpClient(createHttpClient())
                .credentialsProvider(createCredentialProvider())
                //.serviceConfiguration(Configuration.create()) // Service-level configuration
                // Client-level configuration rewrite, can set Endpoint, Http request parameters, etc.
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                // Endpoint 请参考 https://api.aliyun.com/product/Dytnsapi
                                .setEndpointOverride("dytnsapi.aliyuncs.com")
                )
                .build();
    }
}
