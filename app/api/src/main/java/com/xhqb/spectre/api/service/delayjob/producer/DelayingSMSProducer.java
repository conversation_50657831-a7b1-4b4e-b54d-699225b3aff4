package com.xhqb.spectre.api.service.delayjob.producer;

import com.xhqb.kael.sequencegenerator.DistributedSequence;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.service.delayjob.SMSDelayingQueueService;
import com.xhqb.spectre.api.service.delayjob.message.CollectDelayingMessage;
import com.xhqb.spectre.api.service.delayjob.message.MarketDelayingMessage;
import com.xhqb.spectre.api.service.delayjob.message.NotifyDelayingMessage;
import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.common.mq.MessageMQ;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

@Component
public class DelayingSMSProducer {
    private static final Logger LOGGER = LoggerFactory.getLogger(DelayingSMSProducer.class);

    @Resource
    SMSDelayingQueueService delayingQueueService;

    @Resource
    DistributedSequence smsDelayingJobIDSeqService;


    /**
     * 发送市场类定时发送消息
     *
     * @param messageContent
     */
    public Boolean sendMarketMessage(MessageMQ<BaseBodyMessage> messageContent, long delay) {
        Boolean status = Boolean.FALSE;
        try {
            if (messageContent != null) {
                String seqId = smsDelayingJobIDSeqService.nextStr(SmsApiApplicationConstant.SMSAPI_DELAYING_SEND_JOB_ID_SEQ);
                MarketDelayingMessage message = new MarketDelayingMessage();
                //时间戳默认为毫秒 延迟5s即为 5*1000
                long time = System.currentTimeMillis();
                LocalDateTime dateTime = Instant.ofEpochMilli(time).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
                message.setDelayTime(time + delay);
                message.setCreateTime(dateTime);
                message.setId(seqId);
                message.setCustomMessage(messageContent);
                status = delayingQueueService.pushMarketDelayingMessage(message);
            }
        } catch (Exception e) {
            LOGGER.error("send sms delayjob exception: {}", e.getMessage());
        }
        return status;
    }

    /**
     * 发送催收类定时发送消息
     *
     * @param messageContent
     */
    public Boolean sendCollectMessage(MessageMQ<BaseBodyMessage> messageContent, long delay) {
        Boolean status = Boolean.FALSE;
        try {
            if (messageContent != null) {
                String seqId = smsDelayingJobIDSeqService.nextStr(SmsApiApplicationConstant.SMSAPI_DELAYING_SEND_JOB_ID_SEQ);
                CollectDelayingMessage message = new CollectDelayingMessage();
                //时间戳默认为毫秒 延迟5s即为 5*1000
                long time = System.currentTimeMillis();
                LocalDateTime dateTime = Instant.ofEpochMilli(time).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
                message.setDelayTime(time + delay);
                message.setCreateTime(dateTime);
                message.setId(seqId);
                message.setCustomMessage(messageContent);
                status = delayingQueueService.pushCollectDelayingMessage(message);
            }
        } catch (Exception e) {
            LOGGER.error("send sms delayjob exception: {}", e.getMessage());
        }
        return status;
    }

    /**
     * 发送通知类定时发送消息
     *
     * @param messageContent
     */
    public Boolean sendNotifyMessage(MessageMQ<BaseBodyMessage> messageContent, long delay) {
        Boolean status = Boolean.FALSE;
        try {
            if (messageContent != null) {
                String seqId = smsDelayingJobIDSeqService.nextStr(SmsApiApplicationConstant.SMSAPI_DELAYING_SEND_JOB_ID_SEQ);
                NotifyDelayingMessage message = new NotifyDelayingMessage();
                //时间戳默认为毫秒 延迟5s即为 5*1000
                long time = System.currentTimeMillis();
                LocalDateTime dateTime = Instant.ofEpochMilli(time).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
                message.setDelayTime(time + 1000);
                message.setCreateTime(dateTime);
                message.setId(seqId);
                message.setCustomMessage(messageContent);
                status = delayingQueueService.pushNotifyDelayingMessage(message);
            }
        } catch (Exception e) {
            LOGGER.error("send sms delayjob exception: {}", e.getMessage());
        }
        return status;
    }
}
