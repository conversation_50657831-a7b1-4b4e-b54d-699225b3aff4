package com.xhqb.spectre.api.interceptor;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.controller.common.CommonResult;
import com.xhqb.spectre.api.controller.common.ResultCode;
import com.xhqb.spectre.api.model.sign.BizAccount;
import com.xhqb.spectre.api.model.sign.BizParam;
import com.xhqb.spectre.api.service.MemoryDataService;
import com.xhqb.spectre.api.servlet.MyHttpServletRequestWrapper;
import com.xhqb.spectre.api.utils.SignatureUtil;
import com.xhqb.spectre.common.dal.dto.AppData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/***
 * Description： 鉴权、验签拦截实现
 *
 */
@SuppressWarnings("SuspiciousMethodCalls")
@Slf4j
public class SignCompareAndVerifyInterceptor extends HandlerInterceptorAdapter {

    //随机数在redis中的存放时间，默认时间为1分钟
    private static final long NONCE_STR_SECONDS = 60L;
    private static final long TIMEOUT_SECONDS = 60L;

    private static final int NONCE_LENGTH = 32;

    @Resource
    private MemoryDataService memoryDataService;

    @Resource
    private RedisTemplate redisTemplate;

    // 是否禁用签名 true 是，false否
    @Value("${sign_disabled}")
    private String signDisabled;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        long begin = System.currentTimeMillis();
        //封装request
        MyHttpServletRequestWrapper myWrapper = new MyHttpServletRequestWrapper(request);
        Map<String, String> headerMap = myWrapper.getHeadersInfo();

        if ("true".equalsIgnoreCase(signDisabled)) {
            return true;
        }
        CommonResult result = new CommonResult();
        String appCode = headerMap.get(SmsApiApplicationConstant.PARAM_APP_KEY.toLowerCase());
        if (StringUtils.isEmpty(appCode)) {
            responseResult(response, result.failed(ResultCode.APPID_NOTNULL));
            log.info("interceptor app不能为空 headerMap {}", headerMap);
            return false;
        }
        // 判断时间是否大于xx秒(防止重放攻击)
        long now = System.currentTimeMillis() / 1000l;
        String timestamp = headerMap.get(SmsApiApplicationConstant.PARAM_TIMESTAMP);
        if (StringUtils.isEmpty(timestamp) || now - Long.parseLong(timestamp) > TIMEOUT_SECONDS) {
            responseResult(response, result.failed(ResultCode.CURTIME_ERROR));
            log.info("interceptor 缺少timestamp参数或参数错误 headerMap {}", headerMap);
            return false;
        }
        // 获取随机字符串
        String nonceStr = headerMap.get(SmsApiApplicationConstant.PARAM_NONCE);
        // 判断该用户的nonceStr参数是否已经在redis中（防止短时间内的重放攻击）
        String nonceRedisKey = SmsApiApplicationConstant.PARAM_APP_KEY + ":" + appCode + ":" + nonceStr;
        Boolean haveNonceStr = redisTemplate.hasKey(nonceRedisKey);
        if (StringUtils.isEmpty(nonceStr) || haveNonceStr) {
            responseResult(response, result.failed(ResultCode.MISS_NONCE_ERROR));
            log.info("interceptor 缺少随机码参数或随机码已失效 headerMap {}", headerMap);
            return false;
        } else if (nonceStr.length() > NONCE_LENGTH) {
            responseResult(response, result.failed(ResultCode.MISS_NONCE_LENGTH_ERROR));
            log.info("interceptor 随机码长度必须在32位内 headerMap {}", headerMap);
            return false;
        }
        AppData app = memoryDataService.getAppInfoByKey(appCode);
        // 判断APP是否存在并验证appKey是否有效
        if ((app == null) || StringUtils.isEmpty(app.getSkey())) {
            responseResult(response, result.failed(ResultCode.ILLEDGE_KEY));
            log.info("interceptor appcode无效 headerMap {}", headerMap);
            return false;
        }

        String appSecret = app.getSkey();
        BizAccount bizAccount = new BizAccount(appSecret, appCode);

        List<BizParam> listParams = new ArrayList<BizParam>();
        listParams.add(new BizParam(SmsApiApplicationConstant.PARAM_APP_KEY, appCode));
        listParams.add(new BizParam(SmsApiApplicationConstant.PARAM_TIMESTAMP, timestamp));
        listParams.add(new BizParam(SmsApiApplicationConstant.PARAM_NONCE, nonceStr));

        String requestSign = headerMap.get(SmsApiApplicationConstant.PARAM_SIGN_KEY);
        //API验证数字签名
        Boolean pass = validateBizParamSign(listParams, requestSign, bizAccount);
//        log.info("interceptor time {}", System.currentTimeMillis() - begin);
        if (pass) {
            // 将本次用户请求的nonceStr参数存到redis中设置60秒后自动删除
            redisTemplate.opsForValue().set(nonceRedisKey, nonceStr, NONCE_STR_SECONDS, TimeUnit.SECONDS);
            return true;
        } else {
            log.info("interceptor 签名认证失败，应用未被授权 headerMap {}", headerMap);
            responseResult(response, result.failed(ResultCode.UNAUTHORIZED));
            return false;
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

    }

    /**
     * API 数字签名认证，
     */
    private boolean validateBizParamSign(List<BizParam> bizParamList, String requestSign, BizAccount bizAccount) {
        String smsSign = SignatureUtil.getSignature(bizParamList, bizAccount);
        return StringUtils.equals(smsSign, requestSign);
    }

    private void responseResult(HttpServletResponse response, CommonResult result) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-type", "application/json;charset=UTF-8");
        response.setStatus(200);
        try (PrintWriter writer = response.getWriter()) {
            writer.write(JSON.toJSONString(result));
            writer.flush();
        } catch (IOException ex) {
            log.error("App Sign Failed Exception {}", ex.getMessage());
        }
    }

}
