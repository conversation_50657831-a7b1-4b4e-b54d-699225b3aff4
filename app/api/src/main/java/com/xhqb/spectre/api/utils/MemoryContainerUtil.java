package com.xhqb.spectre.api.utils;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 缓存容器
 */
public class MemoryContainerUtil {

    private static final ConcurrentHashMap<String, Object> concurrentHashMap = new ConcurrentHashMap<>();

    /**
     * 启动时时间，之后记录更新时间
     */
    private static volatile Integer appUpdateTime = null;
    private static volatile Integer tplUpdateTime = null;
    private static volatile Integer channelUpdateTime = null;
    private static volatile Integer blackUpdateTime = null;
    private static volatile Integer whiteUpdateTime = null;
    private static volatile Integer sendLimitUpdateTime = null;
    private static volatile Integer smsTypeDisabledUpdateTime = null;
    private static volatile Integer channelTestTplUpdateTime = null;
    private static volatile Integer channelTestTaskUpdateTime = null;

    public static final String API_MAP_KEY_PREFIX = "spectre-api";

    public static final String APP_KEY = API_MAP_KEY_PREFIX + ":app";
    public static final String TPL_KEY = API_MAP_KEY_PREFIX + ":tpl";
    public static final String CHANNEL_ACCOUNT_KEY = API_MAP_KEY_PREFIX + ":channel";
    public static final String BLACK_KEY = API_MAP_KEY_PREFIX + ":black";
    public static final String WHITE_KEY = API_MAP_KEY_PREFIX + ":white";
    public static final String SEND_LIMIT_KEY = API_MAP_KEY_PREFIX + ":sendLimit";
    public static final String SMS_TYPE_DISABLED_KEY = API_MAP_KEY_PREFIX + ":smsTypeDisabled";

    public static final String PHONE_DATA = API_MAP_KEY_PREFIX + ":phoneData";
    public static final String CHANNEL_TEST_TPL = API_MAP_KEY_PREFIX + ":channelTestTpl";
    public static final String CHANNEL_TEST_TASK = API_MAP_KEY_PREFIX + ":channelTestTask";

    /**
     * 获取缓存
     *
     * @param key
     * @return
     */
    public static Object get(String key) {
        return concurrentHashMap.get(key);
    }

    /**
     * 写入容器
     *
     * @param key
     * @param value
     * @return
     */
    public static boolean put(String key, Object value) {
        concurrentHashMap.put(key, value);
        return true;
    }

    /**
     * 删除key
     *
     * @param key
     */
    public static void remove(String key) {
        concurrentHashMap.remove(key);
    }


    public static Integer getAppUpdateTime() {
        return appUpdateTime;
    }

    public static void setAppUpdateTime(Integer appUpdateTime) {
        MemoryContainerUtil.appUpdateTime = appUpdateTime;
    }


    public static Integer getChannelTestTplUpdateTime() {
        return channelTestTplUpdateTime;
    }

    public static void setChannelTestTplUpdateTime(Integer channelTestTplUpdateTime) {
        MemoryContainerUtil.channelTestTplUpdateTime = channelTestTplUpdateTime;
    }

    public static Integer getChannelTestTaskUpdateTime() {
        return channelTestTaskUpdateTime;
    }

    public static void setChannelTestTaskUpdateTime(Integer channelTestTaskUpdateTime) {
        MemoryContainerUtil.channelTestTaskUpdateTime = channelTestTaskUpdateTime;
    }

    public static Integer getTplUpdateTime() {
        return tplUpdateTime;
    }

    public static void setTplUpdateTime(Integer tplUpdateTime) {
        MemoryContainerUtil.tplUpdateTime = tplUpdateTime;
    }

    public static Integer getChannelUpdateTime() {
        return channelUpdateTime;
    }

    public static void setChannelUpdateTime(Integer channelUpdateTime) {
        MemoryContainerUtil.channelUpdateTime = channelUpdateTime;
    }

    public static Integer getBlackUpdateTime() {
        return blackUpdateTime;
    }

    public static void setBlackUpdateTime(Integer blackUpdateTime) {
        MemoryContainerUtil.blackUpdateTime = blackUpdateTime;
    }

    public static Integer getWhiteUpdateTime() {
        return whiteUpdateTime;
    }

    public static void setWhiteUpdateTime(Integer whiteUpdateTime) {
        MemoryContainerUtil.whiteUpdateTime = whiteUpdateTime;
    }

    public static Integer getSendLimitUpdateTime() {
        return sendLimitUpdateTime;
    }

    public static void setSendLimitUpdateTime(Integer sendLimitUpdateTime) {
        MemoryContainerUtil.sendLimitUpdateTime = sendLimitUpdateTime;
    }

    public static Integer getSmsTypeDisabledUpdateTime() {
        return smsTypeDisabledUpdateTime;
    }

    public static void setSmsTypeDisabledUpdateTime(Integer smsTypeDisabledUpdateTime) {
        MemoryContainerUtil.smsTypeDisabledUpdateTime = smsTypeDisabledUpdateTime;
    }
}
