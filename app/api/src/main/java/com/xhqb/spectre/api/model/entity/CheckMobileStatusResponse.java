package com.xhqb.spectre.api.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CheckMobileStatusResponse implements Serializable {
    private static final long serialVersionUID = 1394455937709633374L;
    private Integer chargeCount;
    private String description;
    private String requestId;
    private String Result;
    private String mobileStatusList;
}
