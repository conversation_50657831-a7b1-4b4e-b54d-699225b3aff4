package com.xhqb.spectre.api.config;

import com.xhqb.spectre.api.cache.MobileBlacklistCache;
import com.xhqb.spectre.api.cache.impl.LocalMobileBlacklistCache;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class BlacklistCacheConfig {

    @Bean(name = "localMobileBlacklistCache")
    @Primary
    public MobileBlacklistCache localMobileBlacklistCache() {
        return new LocalMobileBlacklistCache();
    }
}
