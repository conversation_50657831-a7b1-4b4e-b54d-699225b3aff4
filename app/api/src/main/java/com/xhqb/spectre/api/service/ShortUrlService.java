package com.xhqb.spectre.api.service;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.api.model.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/25 15:17
 **/
public interface ShortUrlService {

    String create(ShortUrlDTO shortUrlDTO);

    JSONObject createShortUrlTpl(ShortUrlTplDTO shortUrlTplDTO);

    List<UserShortUrlVO> createUserShortUrl(UserShortUrlDTO userShortUrlDTO);

    List<UserChannelInfoVO> queryChannelInfoByMobiles(List<String> mobileList);

    UserThirtyChannelInfoVO queryPastThirtyChannels(String mobile);
}
