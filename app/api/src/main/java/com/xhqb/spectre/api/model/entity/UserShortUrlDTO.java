package com.xhqb.spectre.api.model.entity;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/6
 */
@Data
public class UserShortUrlDTO implements Serializable {
    private static final long serialVersionUID = 995632787647875479L;

    /**
     * 业务 id
     */
    @NotBlank(message = "业务id不能为空")
    private String bizId;

    /**
     * 用户信息
     */
    @NotEmpty(message = "userShortUrlInfoList cannot be empty")
    @Size(max = 100, message = "userShortUrlInfoList size cannot exceed 100")
    private List<UserInfoDTO> userShortUrlInfoList;

}
