package com.xhqb.spectre.api.controller;

import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.controller.common.CommonResult;
import com.xhqb.spectre.api.model.entity.*;
import com.xhqb.spectre.api.model.smsreq.QueryOrderRequestDTO;
import com.xhqb.spectre.api.model.smsreq.QueryTplRequest;
import com.xhqb.spectre.api.service.SmsQueryService;
import com.xhqb.spectre.api.utils.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.ChannelDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 查询订单 查询模版接口
 */
@RestController
@RequestMapping("/api/spectre/v3")
@Slf4j
public class SpectreQueryApiController {

    @Resource
    SmsQueryService smsQueryService;

    /**
     * 查询订单接口
     *
     * @param queryOrderRequestDTO
     * @return
     * @date 2022-03-04
     */
    @PostMapping("/queryOrder")
    public CommonResult<List<QueryOrderVO>> queryOrder(@RequestHeader Map<String, String> headerMap, @RequestBody QueryOrderRequestDTO queryOrderRequestDTO) {
        queryOrderRequestDTO.setAppCode(headerMap.get(SmsApiApplicationConstant.PARAM_APP_KEY.toLowerCase()));
        if (queryOrderRequestDTO.getRequestIds() == null) {
            return CommonResult.failed("外部流水号不能为空");
        }
        List<QueryOrderVO> list = smsQueryService.queryOrder(queryOrderRequestDTO.getRequestIds(), queryOrderRequestDTO.getAppCode());
        return CommonResult.success(list);
    }

    /**
     * 模板列表查询
     *
     * @param tplQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/queryTpl")
    public CommonResult<CommonPager<QueryTplVO>> queryTplList(@RequestHeader Map<String, String> headerMap, @ModelAttribute TplQuery tplQuery, Integer pageNum, Integer pageSize) {
        if (pageSize > 100) {
            pageSize = 10;
        }
        tplQuery.setAppCode(headerMap.get(SmsApiApplicationConstant.PARAM_APP_KEY.toLowerCase()));
        tplQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<QueryTplVO> commonPager = smsQueryService.findTplListByPage(tplQuery);
        return CommonResult.success(commonPager);
    }

    /**
     * 查询模板详情
     *
     * @param id
     * @return
     */
    @GetMapping("/queryTpl/{id}")
    public CommonResult<QueryTplVO> queryTplInfo(@RequestHeader Map<String, String> headerMap, @PathVariable("id") Integer id) {
        String appCode = headerMap.get(SmsApiApplicationConstant.PARAM_APP_KEY.toLowerCase());
        QueryTplVO queryTplVO = smsQueryService.getTplById(id);
        if (queryTplVO != null && queryTplVO.getAppCode().equalsIgnoreCase(appCode)) {
            return CommonResult.success(queryTplVO);
        }
        return CommonResult.failed("查询的数据不存在");
    }

    /**
     * 查询模版详情
     */
    @PostMapping("/queryTpl/code")
    public CommonResult<QueryTplVO> queryTpl(@RequestHeader Map<String, String> headerMap, @RequestBody QueryTplRequest request) {
        request.setAppCode(headerMap.get(SmsApiApplicationConstant.PARAM_APP_KEY.toLowerCase()));
        if (request.getTplCode() == null) {
            return CommonResult.failed("参数tplCode不能为空");
        }
        if (request.getSignName() == null) {
            return CommonResult.failed("参数signName不能为空");
        }
        QueryTplVO vo = smsQueryService.queryTpl(request.getTplCode(), request.getSignName());
        return CommonResult.success(vo);
    }

    /**
     * 返回指定应用下所有模版
     *
     * @param appCode 应用ID
     * @return 模版详情list
     */
    @GetMapping("/queryTpl/all/{appCode}")
    public CommonResult<List<TplInfoVO>> queryByAppCode(@RequestHeader Map<String, String> headerMap, @PathVariable("appCode") String appCode) {
        if (StringUtils.isEmpty(appCode)) {
            return CommonResult.failed("参数appCode不能为空");
        }
        String appKey = headerMap.get(SmsApiApplicationConstant.PARAM_APP_KEY.toLowerCase());
        if (appKey.equalsIgnoreCase(appCode)) {
            List<TplInfoVO> tplVOList = smsQueryService.queryByAppCode(appCode);
            return CommonResult.success(tplVOList);
        }
        return CommonResult.failed("查询的appCode和鉴权的appCode不一致");
    }

    @PostMapping("/queryReissueOrder")
    public CommonResult<CommonPager<QueryReissueOrderVO>> queryReissueOrder(@RequestHeader Map<String, String> headerMap, @RequestBody ReissueOrderQuery query) {
        log.info("查询短信重发记录请求参数｜query:{}", JsonLogUtil.toJSONString(query));
        query.setPageParameter(new PageParameter(query.getPageNum(), query.getPageSize()));
        ValidatorUtil.validate(query);
        CommonPager<QueryReissueOrderVO> commonPager = smsQueryService.queryReissueOrderByMobile(query);
        return CommonResult.success(commonPager);
    }

    @PostMapping("/queryChannelAccount")
    public CommonResult<List<QueryChannelAccountVO>> queryChannelAccount(@RequestHeader Map<String, String> headerMap, @RequestBody ChannelAccountQuery query) {
        log.info("查询短信渠道账号请求参数｜query:{}", JsonLogUtil.toJSONString(query));
        query.setPageParameter(new PageParameter(query.getPageNum(), query.getPageSize()));
        return CommonResult.success(smsQueryService.queryChannelAccount(query));
    }

    @PostMapping("/queryTestStats")
    public CommonResult<TestStatVO> queryTestStats(@RequestBody TestStatQuery query) {
        log.info("查询短信测试请求参数｜query:{}", JsonLogUtil.toJSONString(query));
        return CommonResult.success(smsQueryService.queryTestStats(query));
    }


    /**
     * 根据ck合同号查询债转短信
     *
     * @param headerMap
     * @param query
     * @return
     */
    @PostMapping("/queryDebtSms")
    public CommonResult<List<QueryDebtSmsVO>> queryDebtSms(@RequestHeader Map<String, String> headerMap,
                                                           @Valid @RequestBody DebtSmsQuery query) {
        log.info("查询债转短信请求参数｜query:{}", JsonLogUtil.toJSONString(query));
        return CommonResult.success(smsQueryService.queryDebtSms(query));
    }


    /**
     * 渠道和模板编码的绑定关系进行校验是否合法并返回渠道账号ID
     *
     * @param bindingAccountDTO 渠道和模板编码请求参数
     * @return 渠道账号IDList
     */
    @PostMapping("/checkBindingThenGetAccount")
    public CommonResult<List<ChannelBindingVO>> checkBindingThenGetAccount(@RequestBody BindingAccountDTO bindingAccountDTO) {
        log.info("渠道和模板编码的绑定关系进行校验是否合法并返回渠道账号ID请求参数｜bindingAccountDTO:{}", JsonLogUtil.toJSONString(bindingAccountDTO));
        return CommonResult.success(smsQueryService.checkBindingThenGetAccount(bindingAccountDTO.getTplChannelDTOList()));
    }

    /**
     * 获取最近空号的号码
     *
     * @param emptyCount 获取数量
     * @return 最近空号的号码
     */
    @PostMapping("/retrieveRecentEmptyNumbers/{emptyCount}")
    public CommonResult<List<String>> retrieveRecentEmptyNumbers(@PathVariable("emptyCount") int emptyCount) {
        log.info("获取最近空号的号码请求参数｜emptyCount:{}", emptyCount);
        return CommonResult.success(smsQueryService.retrieveRecentEmptyNumbers(emptyCount));
    }

    /**
     * 获取所有短信渠道
     *
     * @return 所有短信渠道
     */
    @PostMapping("/getAllSmsChannels")
    public CommonResult<List<ChannelVO>> getAllSmsChannels() {
        log.info("获取所有短信渠道");
        return CommonResult.success(smsQueryService.getAllSmsChannels());
    }

    /**
     * 通过模板编码获取短信内容
     * @param smsContentByTplCodeAndCount smsContentByTplCodeAndCount
     * @return 短信内容
     */
    @PostMapping("/smsContentByTplCodeAndCount")
    public CommonResult<List<String>> smsContentByTplCodeAndCount(@RequestBody SmsContentByTplCodeAndCount smsContentByTplCodeAndCount) {
        log.info("通过模板编码获取短信内容请求参数｜smsContentByTplCodeAndCount:{}", JsonLogUtil.toJSONString(smsContentByTplCodeAndCount));
        return CommonResult.success(smsQueryService.smsContentByTplCodeAndCount(smsContentByTplCodeAndCount));
    }
}
