package com.xhqb.spectre.api.aspect;

// import com.ctrip.framework.apollo.core.utils.StringUtils;

import com.xhqb.spectre.api.annotation.AutoIdempotent;
import com.xhqb.spectre.api.controller.common.ResultCode;
import com.xhqb.spectre.api.exception.GlobalException;
import com.xhqb.spectre.api.exception.SMSSenderException;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.lang.reflect.Method;
import java.util.Collections;

// import org.springframework.data.redis.core.RedisTemplate;

/**
 * 分布式锁+自定义注解实现接口幂等切面
 */
@Aspect
@Component
public class IndempotentAspect {
    /**
     * lua
     */
    private static final String RELEASE_LOCK_LUA_SCRIPT = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    // 增强带有AutoIdempotent注解的方法
    @Pointcut("@annotation(com.xhqb.spectre.api.annotation.AutoIdempotent)")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object around(@NotNull ProceedingJoinPoint joinPoint) throws Throwable {

        // 可以根据业务获取用户唯一的个人信息，例如手机号码
        String appId = "";

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        AutoIdempotent autoIdempotent = method.getAnnotation(AutoIdempotent.class);
        String prefix = autoIdempotent.prefix();
        if (StringUtils.isBlank(prefix)) {
            throw new GlobalException("AutoIdempotent prefix can't be null");
        }
        // 拼接 key
        String delimiter = autoIdempotent.delimiter();
        StringBuilder sb = new StringBuilder();
        sb.append(prefix).append(delimiter).append(appId);
        final String lockKey = sb.toString();
        final String UUID = "";
        try {
            // 获取锁
            boolean success = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, UUID, autoIdempotent.expire(), autoIdempotent.timeUnit());
            if (!success) {
                throw new SMSSenderException(ResultCode.UNKNOWN_RS_CODE);
            }
            Object result = joinPoint.proceed();
            return result;
        } finally {
            // 最后记得释放锁
            DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(RELEASE_LOCK_LUA_SCRIPT, Long.class);
            Long result = stringRedisTemplate.execute(redisScript, Collections.singletonList(lockKey), UUID);
        }

    }


}
