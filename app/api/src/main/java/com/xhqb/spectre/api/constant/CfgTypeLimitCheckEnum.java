package com.xhqb.spectre.api.constant;

/**
 * 频率限制检查
 */
public enum CfgTypeLimitCheckEnum {

    APP_SEND_LIMIT_APP_MAX_COUNT_DAY(10001, "appMaxCountDay"),

    APP_SEND_LIMIT_MOBILE_MAZX_COUNT_HALF_MINUTE(10002, "mobileMaxCountHalfMinute"),

    APP_SEND_LIMIT_MOBILE_MAX_COUNT_HOUR(10003, "mobileMaxCountHour"),

    APP_SEND_LIMIT_MOBILE_MAX_COUNT_DAY(10004, "mobileMaxCountDay"),

    APP_SEND_LIMIT_MOBILE_SAME_CONTENT_MAX_CYCLE(10005, "mobileSameContentMaxCycle"),

    APP_SEND_LIMIT_MOBILE_MAX_COUNT_VERIFY(10006, "mobileMaxCountVerify");

    private Integer cfgCode;

    private String cfgType;

    private CfgTypeLimitCheckEnum(Integer cfgCode, String cfgType) {
        this.cfgCode = cfgCode;
        this.cfgType = cfgType;
    }

    public Integer getCfgCode() {
        return cfgCode;
    }

    private void setCfgCode(Integer code) {
        this.cfgCode = code;
    }


    public String getCfgType() {
        return cfgType;
    }


    private void setCfgType(String message) {
        this.cfgType = message;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code
     * @return
     */
    public static CfgTypeLimitCheckEnum getByCode(Integer code) {
        CfgTypeLimitCheckEnum[] arr = values();
        int length = arr.length;

        for (int i = 0; i < length; ++i) {
            CfgTypeLimitCheckEnum statusEnum = arr[i];
            if (statusEnum.getCfgCode().equals(code)) {
                return statusEnum;
            }
        }

        return null;
    }

    public static CfgTypeLimitCheckEnum getByCfgType(String cfgType) {
        CfgTypeLimitCheckEnum[] arr = values();
        int length = arr.length;

        for (int i = 0; i < length; ++i) {
            CfgTypeLimitCheckEnum statusEnum = arr[i];
            if (statusEnum.getCfgType().equals(cfgType)) {
                return statusEnum;
            }
        }

        return null;
    }

}
