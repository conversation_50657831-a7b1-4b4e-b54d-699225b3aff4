package com.xhqb.spectre.api.utils;

import com.xhqb.spectre.api.controller.common.ResultCode;
import com.xhqb.spectre.api.exception.GlobalException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Objects;
import java.util.Set;

@Slf4j
public class ValidatorUtil {

    private static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    private ValidatorUtil() {
    }

    /**
     * 扩张 Hibernate Validator 的功能，当object的属性验证失败时，优先从错误信息提取错误码，提取不到则获取自定义的 RespCodeEnum
     * 验证失败，抛出 BizException 异常。
     *
     * @param object
     * @throws
     */
    public static void validate(Object object) {
        if (Objects.isNull(object)) {
            throw new GlobalException(ResultCode.VALIDATE_FAILED);
        }
        Set<ConstraintViolation<Object>> constraintViolations = VALIDATOR.validate(object);
        if (CollectionUtils.isEmpty(constraintViolations)) {
            return;
        }
        for (ConstraintViolation<Object> constraintViolation : constraintViolations) {
            if (!StringUtils.isEmpty(constraintViolation.getMessage())) {
                throw new GlobalException(constraintViolation.getMessage());
            }
        }
    }
}
