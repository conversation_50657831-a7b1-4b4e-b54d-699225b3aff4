package com.xhqb.spectre.api.model.smsresp;

import lombok.Data;

import java.io.Serializable;

@Data
public class VerifyCodeCheckResultVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 接口请求序列号
     */
    private String requestId;

    /**
     * 验证码验证结果
     */
    private Boolean resultFlag;

    /**
     * 验证码错误CODE
     */
    private String resultCode;

    /**
     * 验证码错误信息， 假如result为FALSE、会返回错误信息
     */
    private String resultMsg;

}
