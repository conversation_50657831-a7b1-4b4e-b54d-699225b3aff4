package com.xhqb.spectre.api.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.faceid.v20180301.FaceidClient;
import com.tencentcloudapi.faceid.v20180301.models.MobileStatusRequest;
import com.tencentcloudapi.faceid.v20180301.models.MobileStatusResponse;
import com.tencentcloudapi.kms.v20190118.KmsClient;
import com.tencentcloudapi.kms.v20190118.models.GenerateDataKeyRequest;
import com.tencentcloudapi.kms.v20190118.models.GenerateDataKeyResponse;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.model.entity.CheckMobileStatusResponse;
import com.xhqb.spectre.api.model.entity.MobileStatus;
import com.xhqb.spectre.api.service.RedisService;
import com.xhqb.spectre.api.service.TencentApiService;
import com.xhqb.spectre.api.utils.HttpUtils;
import com.xhqb.spectre.api.utils.TencentSignatureUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/25
 * 获取手机号码状态
 */
@Slf4j
@Service
public class TencentApiServiceImpl implements TencentApiService {

    @Value("${tencent.phoneStatus.secretId}")
    private String secretId;

    @Value("${tencent.phoneStatus.secretKey}")
    private String secretKey;

//    @Value("${tencent.kms.keyId}")
//    private String kmsKeyId;

    @Value("${tencent.phoneStatus.switch:false}")
    private volatile boolean phoneStatusSwitch;


    @Resource
    private RedisService redisService;

    private static final String defaultRegion = "ap-guangzhou";
    private static final String defaultSpec = "AES_256";

    /**
     * 查询状态接口地址
     */
    @Value("${tencent.phoneStatus.url}")
    private String urlStr ;
    private static final String host = "faceid.tencentcloudapi.com";
    private static final String service = "faceid";
    private static final String version = "2018-03-01";
    private static final String region = "ap-guangzhou";
    private static final String action = "CheckMobileStatus";
    private static final String tempToken = null;

    private static Credential credential = null;

    public synchronized Credential getInstance() {
        if (credential == null)
            credential = new Credential(secretId, secretKey);
        return credential;
    }

    /**
     * 先判断redis中是否包含，包含则返回redis中的值，否则查询接口
     *
     * @param phone
     * @return
     */
    @Override
    public Long getPhoneStatus(String phone) {
        Long resultCode = null;
        try {
            Credential credential = getInstance();
            FaceidClient client = new FaceidClient(credential, "");
            // 实例化一个请求对象,每个接口都会对应一个request对象
            MobileStatusRequest req = new MobileStatusRequest();
            req.setMobile(phone);
            // 返回的resp是一个MobileStatusResponse的实例，与请求对象对应
            MobileStatusResponse resp = client.MobileStatus(req);
            resultCode = resp.getStatusCode();
        } catch (TencentCloudSDKException e) {
            log.warn("TencentApiServiceImpl getPhoneStatus error {}", e.getMessage());
        }
        return resultCode;
    }

    /**
     * 此方法暂时没用到
     *
     * @return
     */
    @Override
    public String getPlaintext() {
        Credential credential = getInstance();
        KmsClient kmsClient = new KmsClient(credential, defaultRegion);
        GenerateDataKeyRequest req = new GenerateDataKeyRequest();
//        req.setKeyId(kmsKeyId);
        req.setKeySpec(defaultSpec);
        try {
            GenerateDataKeyResponse response = kmsClient.GenerateDataKey(req);
        } catch (TencentCloudSDKException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 批量查询手机号码状态
     *
     * @param phones
     * @return
     */
    @Override
    public List<MobileStatus> checkMobileStatus(List<String> phones) {
        if (phones == null || phones.size() == 0) {
            return null;
        }
        List<MobileStatus> result = new ArrayList<>();
//        List<MobileStatus> findList = redisService.batchGetRedis(phones);
        List<MobileStatus> findList = redisService.batchGetRedisPipeLined(phones);
        if (findList != null && findList.size() > 0) {
            if (findList.size() == phones.size()) {
                return findList;
            } else {
                // redis中查询到部分，剩余部分从接口中查询
                List<String> list = new ArrayList<>();
                for (String phone : phones) {
                    Boolean flag = Boolean.FALSE;
                    for (MobileStatus mobileStatus : findList) {
                        if (mobileStatus.getMobile().equalsIgnoreCase(phone)) {
                            flag = Boolean.TRUE;
                            break;
                        }
                    }
                    if (flag == Boolean.FALSE) {
                        list.add(phone);
                    }
                }
                List<MobileStatus> otherList = getApiMobileStatus(list);
                result.addAll(otherList);
                result.addAll(findList);
            }
        } else {
            result = getApiMobileStatus(phones);
        }
        return result;
    }

    private List<MobileStatus> getApiMobileStatus(List<String> phones) {
        List<MobileStatus> result = new ArrayList<>();
        //false 则不再调用接口获取结果
        if (!this.phoneStatusSwitch) {
            log.info("关闭三方状态查询");
            return result;
        }
        // 缓存中不存在 调接口查询
        if (phones.size() > 50) {
            List<MobileStatus> phoneStatusList1 = apiMobileStatus(phones.subList(0, 50));
            List<MobileStatus> phoneStatusList2 = apiMobileStatus(phones.subList(50, phones.size()));
            result.addAll(phoneStatusList1);
            result.addAll(phoneStatusList2);
        } else {
            result = apiMobileStatus(phones);
        }
        return result;
    }

    /**
     * 从接口中获取数据
     *
     * @param phones
     * @return
     */
    private List<MobileStatus> apiMobileStatus(List<String> phones) {
        if (phones == null || phones.size() == 0) {
            return null;
        }
        List<MobileStatus> result = new ArrayList<>();
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("Mobiles", phones.toArray());
            String bodyParam = JSONObject.toJSON(map).toString();
            HashMap<String, String> headerHashMap = TencentSignatureUtil.getAuthorizationHearder(host, bodyParam, service, secretId, secretKey, tempToken, action, version, region);
            String respString = HttpUtils.getBizTokenKey(headerHashMap, urlStr, bodyParam);
            if (StringUtils.isEmpty(respString)) {
                return result;
            }
            JSONObject jsonObject = JSONObject.parseObject(respString);
            String response = jsonObject.getString("Response");
            if (StringUtils.isEmpty(response)) {
                return null;
            }
            CheckMobileStatusResponse mobileStatusResponse = JSONObject.parseObject(response, CheckMobileStatusResponse.class);
            if (mobileStatusResponse == null || !"0".equalsIgnoreCase(mobileStatusResponse.getResult())) {
                return null;
            }

            String mobileStatusList = mobileStatusResponse.getMobileStatusList();
            if (StringUtils.isEmpty(mobileStatusList)) {
                return null;
            }
            result = JSONArray.parseArray(mobileStatusList, MobileStatus.class);
            // 保存到redis
            redisService.batchSetRedisPipeLined(result);
//            redisService.batchSetRedis(result.stream().collect(Collectors.toMap(item -> SmsApiApplicationConstant.REDISPHONESTATUSKEY + item.getMobile(), item -> JSONObject.toJSON(item).toString())));
            if (phones.size() != result.size()) {
                List<MobileStatus> otherList = new ArrayList<>();
                for (String phone : phones) {
                    boolean flag = false;
                    for (MobileStatus mobileStatus : result) {
                        if (mobileStatus.getMobile().equalsIgnoreCase(phone)) {
                            flag = true;
                            break;
                        }
                    }
                    if (!flag) {
                        otherList.add(new MobileStatus(phone, "", "", "", SmsApiApplicationConstant.PHONE_STATUS_CODE99));
                    }
                }
                result.addAll(otherList);
            }
        } catch (Exception e) {
            log.warn("TencentApiServiceImpl checkMobileStatus error {}", e.getMessage());
        }
        return result;
    }
}
