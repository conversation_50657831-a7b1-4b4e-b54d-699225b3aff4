package com.xhqb.spectre.api.model.smsresp;

import com.xhqb.spectre.api.utils.DateUtil;
import com.xhqb.spectre.common.dal.entity.AppDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AppVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String code;

    private String name;

    private String description;

    private String skey;

    /**
     * 短信内容API类型，0：不支持；1：支持；2：支持并且免模板检测
     */
    private Integer contentApiType;

    private String createTime;

    private String creator;

    private String updateTime;

    private String updater;

    public static AppVO buildVO(AppDO appDO) {
        return AppVO.builder()
                .id(appDO.getId())
                .code(appDO.getCode())
                .name(appDO.getName())
                .description(appDO.getDescription())
                .skey(appDO.getSkey())
                .contentApiType(appDO.getContentApiType())
                .createTime(DateUtil.dateToString(appDO.getCreateTime()))
                .creator(appDO.getCreator())
                .updateTime(DateUtil.dateToString(appDO.getUpdateTime()))
                .updater(appDO.getUpdater())
                .build();
    }
}
