package com.xhqb.spectre.api.service.impl.assembler;

import com.github.wujun234.uid.impl.CachedUidGenerator;
import com.xhqb.spectre.api.constant.SMSSendFailTypeEnum;
import com.xhqb.spectre.api.constant.SendStatusEnum;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.controller.common.ResultCode;
import com.xhqb.spectre.api.exception.SMSSenderException;
import com.xhqb.spectre.api.model.entity.ErrorPhone;
import com.xhqb.spectre.api.model.entity.PhoneIsp;
import com.xhqb.spectre.api.model.entity.SMSMessageMQAssembler;
import com.xhqb.spectre.api.model.smsreq.BaseSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.ContentSMSReqDTO;
import com.xhqb.spectre.api.model.smsresp.BaseSMSResultVO;
import com.xhqb.spectre.api.model.smsresp.BatchSMSResultVO;
import com.xhqb.spectre.api.service.MemoryDataService;
import com.xhqb.spectre.api.service.SendMessageService;
import com.xhqb.spectre.api.utils.ChannelUtils;
import com.xhqb.spectre.api.utils.PhoneSearchFastUtil;
import com.xhqb.spectre.common.dal.dto.TplData;
import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.common.mq.ChannelCode;
import com.xhqb.spectre.common.mq.MessageMQ;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 按短信内容发送 处理逻辑
 */
@Slf4j
@Service
public class ContentSMSAssemblerServiceImpl extends AbstractAssemblerSMSService {

    /**
     * 雪花ID生成器
     */
    @Resource
    private CachedUidGenerator cachedUidGenerator;

    @Resource
    private MemoryDataService memoryDataService;

    @Resource
    private SendMessageService sendMessageService;

    /**
     * @param baseSMSReqDTO
     * @param baseSMSResultVO
     * @return
     * @Decription:
     */
    @Override
    public Boolean sendSMSTask(BaseSMSReqDTO baseSMSReqDTO, BaseSMSResultVO baseSMSResultVO) {
        ContentSMSReqDTO contentSMSReqDTO = (ContentSMSReqDTO) baseSMSReqDTO;
        List<String> phoneList = contentSMSReqDTO.getPhoneList();

        //失败的号码
        List<ErrorPhone> failPhoneList = new ArrayList<ErrorPhone>();
        //成功下发的号码
        List<String> successPhoneList = new ArrayList<String>();

        if (CollectionUtils.isEmpty(phoneList)) {
            baseSMSResultVO.setSendStatus(SendStatusEnum.INTERFACE_SUCCESS);
            baseSMSResultVO.setSmsSendResult(sendMessageService.buildBatchSMSResultVO(baseSMSReqDTO, baseSMSResultVO, successPhoneList, failPhoneList));
            log.warn("可用号码数为0 requestDto {},phoneNumbers : {}", contentSMSReqDTO, contentSMSReqDTO.getPhoneNumbers());
//            new SMSSenderException(ResultCode.WRONG_PHONE_NUMBER);
            return Boolean.FALSE;
        }

        // 获取手机号码归属地
        for (String phone : phoneList) {
            try {
                Long phoneStatus = SmsApiApplicationConstant.PHONE_STATUS_CODE;
                PhoneIsp phoneIsp = PhoneSearchFastUtil.getInstance().getIsp(phone);
                if (Objects.isNull(phoneIsp)) {
                    phoneIsp = PhoneIsp.buildPhoneIspDefault(phone);
                }
                TplData tplData = memoryDataService.getTplDataByCode(contentSMSReqDTO.getTplCode(), contentSMSReqDTO.getSignCode());
                //渠道商
                if (ChannelUtils.isSmsTypeDisabled(tplData, memoryDataService, phoneIsp)) {
                    log.warn("发送短信MQ失败 命中短信类型禁用规则{}", phone);
                    failPhoneList.add(new ErrorPhone(phone, String.valueOf(SMSSendFailTypeEnum.SMS_SEND_TYPE_SMS_TYPE_DISABLED.getStatusCode()), SMSSendFailTypeEnum.SMS_SEND_TYPE_SMS_TYPE_DISABLED.getStatusMsg()));
//                    new SMSSenderException(ResultCode.SMS_TYPE_DISABLED);
                    continue;
                }
                boolean isTplDisabled = ChannelUtils.isTplDisabled(tplData, phoneIsp);
                if (!isTplDisabled) {
                    List<ChannelCode> channelCodeList = new ArrayList<>();
                    // 判断是否命中渠道禁用规则
                    if (contentSMSReqDTO.getChannelAccountId() != null && contentSMSReqDTO.getChannelAccountId() != 0) {
                        ChannelCode channelCode = new ChannelCode(tplData.getCode(), String.valueOf(contentSMSReqDTO.getChannelAccountId()), null, 10);
                        channelCodeList.add(channelCode);
                    } else {
                        channelCodeList = ChannelUtils.getChannelCode(tplData, memoryDataService, phoneIsp, null, null);
                        if (channelCodeList == null || channelCodeList.size() == 0) {
                            log.warn("发送短信MQ失败 未获取到渠道信息{}", phone);
                            failPhoneList.add(new ErrorPhone(phone, String.valueOf(SMSSendFailTypeEnum.SMS_SEND_FAILED_NO_AVAIL_PARTNER.getStatusCode()), SMSSendFailTypeEnum.SMS_SEND_FAILED_NO_AVAIL_PARTNER.getStatusMsg()));
//                            new SMSSenderException(ResultCode.CHANNEL_EMPTY);
                            continue;
                        }
                    }
                    // 组装单条短信发送体
                    SMSMessageMQAssembler<BaseBodyMessage> smsMessageMQAssembler = SMSMessageMQAssembler.buildByContentSMSReqDTO(contentSMSReqDTO, phoneIsp);
                    smsMessageMQAssembler.setOrderId(String.valueOf(cachedUidGenerator.getUID()));
                    smsMessageMQAssembler.setRequestId(contentSMSReqDTO.getRequestId());
                    smsMessageMQAssembler.setChannelCodeSet(channelCodeList);
                    smsMessageMQAssembler.setSignName(tplData.getSignName());
                    if (contentSMSReqDTO.getParamList() != null) {
                        String paramsString = contentSMSReqDTO.getParamList().stream().filter(item -> item.get(SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY) != null && item.get(SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY).equalsIgnoreCase(phone)).collect(Collectors.toList()).toString();
                        List<String> params = new ArrayList<>();
                        params.add(paramsString);
                        smsMessageMQAssembler.setParamMap(params);
                    } else {
                        smsMessageMQAssembler.setParamMap(null);
                    }
                    smsMessageMQAssembler.setContent(ChannelUtils.buildMsgContent(contentSMSReqDTO.getParamList(), contentSMSReqDTO.getContent(), phone));
                    smsMessageMQAssembler.setPhoneStatus(phoneStatus);
                    // 构建
                    MessageMQ<BaseBodyMessage> singleMessageMQ = sendMessageService.buildSingleMessageMQ(smsMessageMQAssembler);
                    Boolean sendStatus = sendMessageService.sendMessage(singleMessageMQ, baseSMSReqDTO, baseSMSResultVO);
                    if (sendStatus) {
                        successPhoneList.add(phone);
                    } else {
                        failPhoneList.add(new ErrorPhone(phone, String.valueOf(SMSSendFailTypeEnum.SMS_SEND_TYPE_DISPATCH_FAILED.getStatusCode()), SMSSendFailTypeEnum.SMS_SEND_TYPE_DISPATCH_FAILED.getStatusMsg()));
                    }
                } else {
                    log.warn("发送短信MQ失败 命中模版禁用规则{}", phone);
                    failPhoneList.add(new ErrorPhone(phone, String.valueOf(SMSSendFailTypeEnum.SMS_SEND_FAILED_NO_AVAIL_PARTNER.getStatusCode()), SMSSendFailTypeEnum.SMS_SEND_FAILED_NO_AVAIL_PARTNER.getStatusMsg()));
//                    new SMSSenderException(ResultCode.CHANNEL_EMPTY);
                }
            } catch (Exception e) {
                log.error("ContentSMSAssemblerServiceImpl sendSMSTask error: {}", e.getMessage());
//                e.printStackTrace();
//                new SMSSenderException(ResultCode.SYS_FAILURES);
                return Boolean.FALSE;
            }
        }

        BatchSMSResultVO batchSMSResultVO = sendMessageService.buildBatchSMSResultVO(baseSMSReqDTO, baseSMSResultVO, successPhoneList, failPhoneList);
        if (batchSMSResultVO.getFailure() == 0) {
            baseSMSResultVO.setSendStatus(SendStatusEnum.INTERFACE_DISPATCH_SUCCESS);
        } else if (batchSMSResultVO.getSuccess() != 0) {
            baseSMSResultVO.setSendStatus(SendStatusEnum.INTERFACE_DISPATCH_PARTLY_SUCCESS);
        } else {
            baseSMSResultVO.setSendStatus(SendStatusEnum.INTERFACE_SUCCESS);
        }
        baseSMSResultVO.setSmsSendResult(batchSMSResultVO);

        return Boolean.TRUE;
    }

}
