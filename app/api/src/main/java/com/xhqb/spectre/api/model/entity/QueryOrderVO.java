package com.xhqb.spectre.api.model.entity;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class QueryOrderVO {
    String requestId;

    /**
     * 手机号
     */
    String mobile;

    /**
     * 订单号
     */
    Long orderId;

    int sendStatus;

    /**
     * 回执状态
     */
    int reportStatus;

    /**
     * 短信内容
     */
    String content;

    /**
     * 计费条数
     */
    Integer billCount;
    /**
     * 短信回执状态码
     */
    String reportCode;
    /**
     * 回执状态描述
     */
    String reportDesc;

    /**
     * 发送时间
     */
    private Integer sendTime;
    /**
     * 回执时间
     */
    private Integer reportTime;

    /**
     * 发送编码
     */
    private String sendCode;

    /**
     * 发送描述
     */
    private String sendDesc;

    /**
     * 业务批次号
     */
    private String bizBatchId;

}
