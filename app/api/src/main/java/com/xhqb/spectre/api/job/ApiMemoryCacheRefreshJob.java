package com.xhqb.spectre.api.job;

import com.xhqb.spectre.api.cache.MobileBlacklistCache;
import com.xhqb.spectre.api.constant.LuciferConstant;
import com.xhqb.spectre.api.constant.TimeConstants;
import com.xhqb.spectre.api.service.MemoryHandleService;
import com.xhqb.spectre.api.service.OpTimeService;
import com.xhqb.spectre.api.utils.DateTimeUtil;
import com.xhqb.spectre.api.utils.MemoryContainerUtil;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.entity.OpTimeDO;
import io.prometheus.client.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;


@Component
@EnableScheduling
@Configuration
public class ApiMemoryCacheRefreshJob {

    private Logger logger = LoggerFactory.getLogger(ApiMemoryCacheRefreshJob.class);

    @Resource
    private OpTimeService opTimeService;

    @Resource
    private MemoryHandleService memoryHandleService;

    @Autowired
    private MobileBlacklistCache mobileBlacklistCache;


    /**
     * 比较数据库时间和系统记录的时间
     */
    @Scheduled(cron = "${cache.refresh.cron}")
    public void execute() {
        logger.info("刷新内存数据 begin {}", DateTimeUtil.getCurrentStr());
        // 开始刷新应用数据到内存
        refreshApp();

        // 开始刷新模版数据到内存
        refreshTpl();

        // 开始刷新渠道禁用数据到内存
        refreshChannelDisable();

        // 开始刷新黑名单数据到内存
        refreshBlack();

        // 开始刷新白名单数据到内存
        refreshWhite();

        // 开始刷新限流配置数据到内存
        refreshSendLimit();

        // 刷新短信类型屏蔽表
        refreshSmsTypeDisabled();

        // 刷新测试模板
        refreshChannelTestTpl();


        logger.info("刷新内存数据 end {}", DateTimeUtil.getCurrentStr());
    }

    private void refreshChannelTestTpl() {
        long start = System.nanoTime();

        Date opTime = new Date();
        Integer opTimeInt = Math.toIntExact(opTime.getTime() / 1000);
        Date updateTime = DateTimeUtil.intToDate(MemoryContainerUtil.getChannelTestTplUpdateTime());
        if (updateTime == null) {
            memoryHandleService.refreshChannelTestTpl();
            MemoryContainerUtil.setChannelTestTplUpdateTime(opTimeInt);
            logger.info("渠道测试模板更新，数据库时间{},设置上次时间{}", DateTimeUtil.intToString(opTimeInt), DateTimeUtil.intToString(MemoryContainerUtil.getAppUpdateTime()));
        } else if (opTime.getTime() > updateTime.getTime()) {
            memoryHandleService.refreshChannelTestTpl();
            MemoryContainerUtil.setChannelTestTplUpdateTime(opTimeInt);
            logger.info("渠道测试模板更新，数据库时间{},上次时间{}", DateTimeUtil.intToString(opTimeInt), DateTimeUtil.intToString(MemoryContainerUtil.getAppUpdateTime()));
        }
        // 请求时间
        LuciferConstant.LOAD_MEMORY_TIME.labels("channelTestTpl").observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
    }

    /**
     * 更新应用
     */
    private void refreshApp() {
        long start = System.nanoTime();
        OpTimeDO opTimeDO = opTimeService.findByModule(OpLogConstant.MODULE_APP);
        if (opTimeDO == null) {
            return;
        }
        Date opTime = tranOpTime(opTimeDO.getOpTime());
        Date updateTime = DateTimeUtil.intToDate(MemoryContainerUtil.getAppUpdateTime());
        if (updateTime == null) {
            memoryHandleService.refreshAppInfo();
            MemoryContainerUtil.setAppUpdateTime(opTimeDO.getOpTime());
            logger.info("App更新，数据库时间{},设置上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getAppUpdateTime()));
        } else if (opTime.getTime() > updateTime.getTime()) {
            memoryHandleService.refreshAppInfo();
            MemoryContainerUtil.setAppUpdateTime(opTimeDO.getOpTime());
            logger.info("App更新，数据库时间{},上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getAppUpdateTime()));
        } else {
//            logger.info("App不更新，数据库时间{},上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getAppUpdateTime()));
        }
        // 请求时间
        LuciferConstant.LOAD_MEMORY_TIME.labels("app").observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
    }

    private Date tranOpTime(Integer opTime) {
        Date date = DateTimeUtil.intToDate(opTime);
        if (date == null) {
            return null;
        }
        return date;
    }

    /**
     * 更新模版
     */
    private void refreshTpl() {
        long start = System.nanoTime();
        OpTimeDO opTimeDO = opTimeService.findByModule(OpLogConstant.MODULE_TPL);
        if (opTimeDO == null) {
            return;
        }
        Date opTime = tranOpTime(opTimeDO.getOpTime());
        Date updateTime = DateTimeUtil.intToDate(MemoryContainerUtil.getTplUpdateTime());
        if (updateTime == null) {
            memoryHandleService.refreshTplInfo();
            logger.info("Tpl更新，数据库时间{},设置上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getTplUpdateTime()));
            MemoryContainerUtil.setTplUpdateTime(opTimeDO.getOpTime());
        } else if (opTime.getTime() > updateTime.getTime()) {
            memoryHandleService.refreshTplInfo();
            MemoryContainerUtil.setTplUpdateTime(opTimeDO.getOpTime());
            logger.info("Tpl更新，数据库时间{},上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getTplUpdateTime()));
        } else {
//            logger.info("Tpl不需要更新，数据库时间{},上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getTplUpdateTime()));
        }
        // 请求时间
        LuciferConstant.LOAD_MEMORY_TIME.labels("tpl").observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
    }

    /**
     * 更新渠道禁用
     */
    private void refreshChannelDisable() {
        long start = System.nanoTime();
        OpTimeDO opTimeDO = opTimeService.findByModule(OpLogConstant.MODULE_CHANNEL_ACCOUNT_DISABLE);
        if (opTimeDO == null) {
            return;
        }
        Date opTime = tranOpTime(opTimeDO.getOpTime());
        Date updateTime = DateTimeUtil.intToDate(MemoryContainerUtil.getChannelUpdateTime());
        if (updateTime == null) {
            memoryHandleService.refreshChannelAccountDisable();
            MemoryContainerUtil.setChannelUpdateTime(opTimeDO.getOpTime());
            logger.info("channelAccountDisabled更新，数据库时间{},设置上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getChannelUpdateTime()));
        } else if (opTime.getTime() > updateTime.getTime()) {
            memoryHandleService.refreshChannelAccountDisable();
            MemoryContainerUtil.setChannelUpdateTime(opTimeDO.getOpTime());
            logger.info("channelAccountDisabled更新，数据库时间{},上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getChannelUpdateTime()));
        } else {
//            logger.info("channelAccountDisabled不需要，数据库时间{},上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getChannelUpdateTime()));
        }
        // 请求时间
        LuciferConstant.LOAD_MEMORY_TIME.labels("channelDisable").observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
    }

    /**
     * 刷新黑名单
     */
    private void refreshBlack() {
        try {
            long start = System.nanoTime();
            OpTimeDO opTimeDO = opTimeService.findByModule(OpLogConstant.MODULE_MOBILE_BLACK);
            if (opTimeDO == null || opTimeDO.getOpTime() == null) {
                return;
            }

            if (mobileBlacklistCache.isAfterLastRefresh(opTimeDO.getOpTime())) {
                logger.info("黑名单缓存更新, 数据库时间:{}, 上次更新时间:{}",
                        DateTimeUtil.intToString(opTimeDO.getOpTime()),
                        DateTimeUtil.intToString(mobileBlacklistCache.getLastRefreshTime()));
                memoryHandleService.refreshMobileBlack();
                mobileBlacklistCache.updateLastRefreshTime(opTimeDO.getOpTime());
            }

            LuciferConstant.LOAD_MEMORY_TIME.labels("black")
                    .observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
        } catch (Exception e) {
            logger.error("刷新黑名单缓存异常", e);
        }
    }

    /**
     * 刷新白名单
     */
    private void refreshWhite() {
        long start = System.nanoTime();
        OpTimeDO opTimeDO = opTimeService.findByModule(OpLogConstant.MODULE_MOBILE_WHITE);
        if (opTimeDO == null) {
            return;
        }
        Date opTime = tranOpTime(opTimeDO.getOpTime());
        Date updateTime = DateTimeUtil.intToDate(MemoryContainerUtil.getWhiteUpdateTime());
        if (updateTime == null) {
            memoryHandleService.refreshMobileWhite();
            MemoryContainerUtil.setWhiteUpdateTime(opTimeDO.getOpTime());
            logger.info("white更新，数据库时间{},设置上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getWhiteUpdateTime()));
        } else if (opTime.getTime() > updateTime.getTime()) {
            memoryHandleService.refreshMobileWhite();
            MemoryContainerUtil.setWhiteUpdateTime(opTimeDO.getOpTime());
            logger.info("white更新，数据库时间{},上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getWhiteUpdateTime()));
        } else {
//            logger.info("white不需要，数据库时间{},上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getWhiteUpdateTime()));
        }
        LuciferConstant.LOAD_MEMORY_TIME.labels("white").observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
    }

    /**
     * 刷新限流数据
     */
    private void refreshSendLimit() {
        long start = System.nanoTime();
        OpTimeDO opTimeDO = opTimeService.findByModule(OpLogConstant.MODULE_SEND_LIMIT);
        if (opTimeDO == null) {
            return;
        }
        Date opTime = tranOpTime(opTimeDO.getOpTime());
        Date updateTime = DateTimeUtil.intToDate(MemoryContainerUtil.getSendLimitUpdateTime());
        if (updateTime == null) {
            memoryHandleService.refreshAppSendLimit();
            MemoryContainerUtil.setSendLimitUpdateTime(opTimeDO.getOpTime());
            logger.info("appSendLimit更新，数据库时间{},设置上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getSendLimitUpdateTime()));
        } else if (opTime.getTime() > updateTime.getTime()) {
            memoryHandleService.refreshAppSendLimit();
            MemoryContainerUtil.setSendLimitUpdateTime(opTimeDO.getOpTime());
            logger.info("appSendLimit更新，数据库时间{},上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getSendLimitUpdateTime()));
        } else {
//            logger.info("appSendLimit不需要，数据库时间{},上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getSendLimitUpdateTime()));
        }
        LuciferConstant.LOAD_MEMORY_TIME.labels("sendLimit").observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
    }

    /**
     * 刷新短信类型屏蔽表数据到内存
     */
    private void refreshSmsTypeDisabled() {
        long start = System.nanoTime();
        OpTimeDO opTimeDO = opTimeService.findByModule(OpLogConstant.MODULE_SMS_TYPE_DISABLE);
        if (opTimeDO == null) {
            return;
        }
        Date opTime = tranOpTime(opTimeDO.getOpTime());
        Date updateTime = DateTimeUtil.intToDate(MemoryContainerUtil.getSmsTypeDisabledUpdateTime());
        if (updateTime == null) {
            memoryHandleService.refreshSmsTypeDisabled();
            MemoryContainerUtil.setSmsTypeDisabledUpdateTime(opTimeDO.getOpTime());
            logger.info("smsTypeDisabled更新，数据库时间{},设置上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getSmsTypeDisabledUpdateTime()));
        } else if (opTime.getTime() > updateTime.getTime()) {
            memoryHandleService.refreshSmsTypeDisabled();
            MemoryContainerUtil.setSmsTypeDisabledUpdateTime(opTimeDO.getOpTime());
            logger.info("smsTypeDisabled更新，数据库时间{},上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getSmsTypeDisabledUpdateTime()));
        } else {
//            logger.info("smsTypeDisabled不需要，数据库时间{},上次时间{}", DateTimeUtil.intToString(opTimeDO.getOpTime()), DateTimeUtil.intToString(MemoryContainerUtil.getSmsTypeDisabledUpdateTime()));
        }
        LuciferConstant.LOAD_MEMORY_TIME.labels("smsTypeDisabled").observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
    }


}
