package com.xhqb.spectre.api.model.entity;

import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.common.dal.entity.PhoneNumberStatusDO;
import com.xhqb.spectre.common.enums.CarrierEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Optional;

@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Data
@ToString
public class MobileStatus implements Serializable {
    private static final long serialVersionUID = 1L;
    private String mobile;
    private String lastTime;
    private String area;
    private String numberType;
    private Long status;

    public static MobileStatus buildMobileStatus(String phone) {
        return Optional.ofNullable(phone).map(item -> {
            MobileStatus apiDO = MobileStatus.builder()
                    .mobile(phone)
                    .lastTime("")
                    .area("")
                    .numberType("")
                    .status(SmsApiApplicationConstant.PHONE_STATUS_CODE1)
                    .build();
            return apiDO;
        }).orElse(null);
    }

    public static MobileStatus buildMobileStatusError(String phone) {
        return Optional.ofNullable(phone).map(item -> {
            MobileStatus apiDO = MobileStatus.builder()
                    .mobile(phone)
                    .lastTime("")
                    .area("")
                    .numberType(CarrierEnum.UNKNOWN.getCode())
                    .status(SmsApiApplicationConstant.PHONE_STATUS_CODE99)
                    .build();
            return apiDO;
        }).orElse(null);
    }

    public static MobileStatus buildMobileStatus(PhoneNumberStatusDO entity) {
        return Optional.ofNullable(entity).map(item -> MobileStatus.builder()
                .mobile(entity.getMobile())
                .lastTime("")
                .area("")
                .numberType(CarrierEnum.getCode(entity.getCarrier()))
                .status(Long.valueOf(entity.getStatus()))
                .build()).orElse(null);
    }
}
