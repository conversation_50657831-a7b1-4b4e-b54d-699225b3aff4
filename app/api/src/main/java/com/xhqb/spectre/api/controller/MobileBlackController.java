package com.xhqb.spectre.api.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.api.controller.common.CommonResult;
import com.xhqb.spectre.api.model.smsreq.MobileBlackDTO;
import com.xhqb.spectre.api.model.smsresp.MobileBlackVO;
import com.xhqb.spectre.api.service.MobileBlackService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.MobileBlackQuery;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/spectre/v3/mobileBlack")
@AllArgsConstructor
@Slf4j
public class MobileBlackController {

    private MobileBlackService mobileBlackService;

    /**
     * 查询黑名单列表
     *
     * @param query
     * @param pageNum
     * @param pageSize
     * @return 分页列表数据
     */
    @GetMapping("")
    public CommonResult<CommonPager<MobileBlackVO>> queryList(@ModelAttribute MobileBlackQuery query,
                                                              Integer pageNum,
                                                              Integer pageSize) {
        log.info("获取黑名单列表; query={}", query);
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<MobileBlackVO> commonPager = mobileBlackService.listByPage(query);
        return CommonResult.success(commonPager);
    }

    /**
     * 查询黑名单详情
     *
     * @param id 主键
     * @return MobileBlackVO
     */
    @GetMapping("/{id}")
    public CommonResult<MobileBlackVO> queryInfo(@PathVariable("id") Integer id) {
        log.info("查询黑名单详情; id={}", id);
        return CommonResult.success(mobileBlackService.getById(id));
    }

    /**
     * 创建黑名单
     *
     * @param dto MobileBlackDTO
     * @return
     */
    @PostMapping("")
    public CommonResult<String> create(@RequestBody MobileBlackDTO dto) {
        log.info("创建黑名单; dto={}", JSON.toJSONString(dto));
        mobileBlackService.create(dto);
        return CommonResult.success("");
    }

    /**
     * 删除黑名单
     *
     * @param id id
     * @return
     */
    @DeleteMapping("/{id}/")
    public CommonResult<String> delete(@PathVariable("id") Integer id) {
        log.info("删除黑名单; id={}", id);
        mobileBlackService.delete(id);
        return CommonResult.success("");
    }
}
