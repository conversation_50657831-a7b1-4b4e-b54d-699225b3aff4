package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.cache.MobileBlacklistCache;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.service.MemoryDataService;
import com.xhqb.spectre.api.utils.MemoryContainerUtil;
import com.xhqb.spectre.common.dal.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MemoryDataServiceImpl implements MemoryDataService {

    @Autowired
    private MobileBlacklistCache mobileBlacklistCache;

    /**
     * 根据appKey获取应用信息
     *
     * @param appKey
     * @return
     */
    @Override
    public AppData getAppInfoByKey(String appKey) {
        if (StringUtils.isEmpty(appKey)) {
            return null;
        }
        Object obj = MemoryContainerUtil.get(MemoryContainerUtil.APP_KEY);
        if (Objects.isNull(obj)) {
            return null;
        }
        Map<String, AppData> map = (Map<String, AppData>) obj;
        AppData appData = map.get(appKey);
        if (appData == null) {
            return null;
        }
        return appData;
    }

    @Override
    public TplData getTplDataByCode(String code, String signCode) {
        if (StringUtils.isEmpty(code) || StringUtils.isEmpty(signCode)) {
            return null;
        }
        Object obj = MemoryContainerUtil.get(MemoryContainerUtil.TPL_KEY);
        if (Objects.isNull(obj)) {
            return null;
        }
        Map<String, TplData> map = (Map<String, TplData>) obj;
        TplData tplData = map.get(code + SmsApiApplicationConstant.TPL_DATA_SPLIT_STR + signCode);
        if (tplData == null) {
            return null;
        }
        return tplData;
    }

    /**
     * 获取渠道禁用信息
     *
     * @param accountId
     * @return
     */
    @Override
    public List<ChannelAccountDisableData> getChannelDisabledData(Integer accountId) {
        if (Objects.isNull(accountId)) {
            return null;
        }
        Object obj = MemoryContainerUtil.get(MemoryContainerUtil.CHANNEL_ACCOUNT_KEY);
        if (Objects.isNull(obj)) {
            return null;
        }
        Map<String, List<ChannelAccountDisableData>> map = (Map<String, List<ChannelAccountDisableData>>) obj;
        List<ChannelAccountDisableData> result = map.get(String.valueOf(accountId));
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result;
    }

    /**
     * 判断是否黑名单
     *
     * @param phone
     * @param smsCode
     * @param appCode
     * @return
     */
    @Override
    public boolean isBlack(String phone, String smsCode, String appCode) {
        return mobileBlacklistCache.isBlacklisted(smsCode, phone, appCode);
    }

    /**
     * 判断是否白名单
     *
     * @param appCode
     * @param cfgType
     * @param phone
     * @return
     */
    @Override
    public boolean isWhite(String appCode, String cfgType, String phone) {
        if (Objects.isNull(appCode) || Objects.isNull(cfgType) || Objects.isNull(phone)) {
            return Boolean.FALSE;
        }
        String key = appCode + "_" + cfgType + "_" + phone;
        Object obj = MemoryContainerUtil.get(MemoryContainerUtil.WHITE_KEY);
        if (Objects.isNull(obj)) {
            return Boolean.FALSE;
        }
        Set<String> set = (Set<String>) obj;
        if (CollectionUtils.isEmpty(set)) {
            return Boolean.FALSE;
        }
        return set.contains(key);
    }

    /**
     * 获取限流规则
     *
     * @param appCode
     * @return
     */
    @Override
    public List<AppSendLimitData> getSendLimit(String appCode) {
        if (Objects.isNull(appCode)) {
            return null;
        }
        Object obj = MemoryContainerUtil.get(MemoryContainerUtil.SEND_LIMIT_KEY);
        if (Objects.isNull(obj)) {
            return null;
        }
        Map<String, List<AppSendLimitData>> map = (Map<String, List<AppSendLimitData>>) obj;
        List<AppSendLimitData> list = map.get(appCode);
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }
        return null;
    }

    @Override
    public AppSendLimitData getSendLimitByAppKeyAndLimitKey(String appCode, String limitKey) {
        if (Objects.isNull(appCode) || Objects.isNull(limitKey)) {
            return null;
        }
        Object obj = MemoryContainerUtil.get(MemoryContainerUtil.SEND_LIMIT_KEY);
        if (Objects.isNull(obj)) {
            return null;
        }
        Map<String, List<AppSendLimitData>> map = (Map<String, List<AppSendLimitData>>) obj;
        List<AppSendLimitData> list = map.get(appCode);
        if (!CollectionUtils.isEmpty(list)) {
            List<AppSendLimitData> result = list.stream().filter(item -> item.getLimitKey().equalsIgnoreCase(limitKey)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(result)) {
                return result.get(0);
            }
        }
        return null;
    }

    /**
     * 根据短信类型获取短信类型禁用规则数据
     *
     * @param smsType 短信类型
     * @return
     */
    @Override
    public List<SmsTypeDisableData> getSmsTypeDisabledData(String smsType) {
        if (Objects.isNull(smsType)) {
            return null;
        }
        Object obj = MemoryContainerUtil.get(MemoryContainerUtil.SMS_TYPE_DISABLED_KEY);
        if (Objects.isNull(obj)) {
            return null;
        }
        Map<String, List<SmsTypeDisableData>> map = (Map<String, List<SmsTypeDisableData>>) obj;
        List<SmsTypeDisableData> list = map.get(smsType);
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }
        return null;
    }

    @Override
    public ChannelTestData getChannelTestData(String code, String signCode) {
        if (Objects.isNull(code) || Objects.isNull(signCode)) {
            return null;
        }
        Object obj = MemoryContainerUtil.get(MemoryContainerUtil.CHANNEL_TEST_TPL);
        if (Objects.isNull(obj)) {
            return null;
        }
        Map<String, ChannelTestData> map = (Map<String, ChannelTestData>) obj;
        ChannelTestData channelTestData = map.get(code + ":" + signCode);
        if (Objects.nonNull(channelTestData)) {
            return channelTestData;
        }
        return null;
    }

}
