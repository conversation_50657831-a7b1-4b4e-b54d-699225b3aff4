package com.xhqb.spectre.api.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class AreaVO implements Serializable {

    private static final long serialVersionUID = -2901570380896509644L;

    /**
     * 地址ID
     */
    private String id;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 省份简名
     */
    private String provinceShortName;

    /**
     * 城市简名
     */
    private String cityShortName;
}
