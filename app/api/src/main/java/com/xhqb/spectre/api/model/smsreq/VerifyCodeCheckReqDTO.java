package com.xhqb.spectre.api.model.smsreq;


import lombok.Data;

import java.io.Serializable;

@Data
public class VerifyCodeCheckReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // 基础参数
    /**
     * 业务应用ID
     */
    private String appCode;

    /**
     * 签名： 主要是用于验权验签
     */
    private String sign;

    /**
     * 时间戳， 可选字段， 应用于判断时间戳接口验证控制
     */
    private String timestamp;


    //业务参数

    /**
     * SMS请求ID、证明唯一的短信发送请求标识
     */
    private String requestId;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 验证码
     */
    private String smsVerifyCode;


    /**
     * 短信验证码识别码
     */
    private String identificationCode;
    

}
