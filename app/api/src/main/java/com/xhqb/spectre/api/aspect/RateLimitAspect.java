package com.xhqb.spectre.api.aspect;

import com.google.common.collect.ImmutableList;
import com.xhqb.spectre.api.annotation.RateLimit;
import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.constant.LuciferConstant;
import com.xhqb.spectre.api.constant.RateLimitType;
import com.xhqb.spectre.api.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.io.Serializable;
import java.lang.reflect.Method;

@Aspect
@Configuration
@Slf4j
public class RateLimitAspect {

    private final RedisTemplate<String, Serializable> limitRedisTemplate;

    @Resource
    private VenusConfig venusConfig;

    @Autowired
    public RateLimitAspect(RedisTemplate<String, Serializable> limitRedisTemplate) {
        this.limitRedisTemplate = limitRedisTemplate;
    }

    @Around("execution(* com.xhqb.spectre.api.service.impl.MobileLimitServiceImpl.check*(..))")
    public Object interceptor(ProceedingJoinPoint pjp) {
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        Method method = signature.getMethod();
        RateLimit limitAnnotation = method.getAnnotation(RateLimit.class);
        RateLimitType limitType = limitAnnotation.limitType();
        Object[] args = pjp.getArgs();
        if (args.length != 2) {
            return false;
        }
        String key;
        int limitPeriod = limitAnnotation.period();
        int limitCount = Integer.parseInt(args[1].toString());
        switch (limitType) {
            case IP:
                key = CommonUtil.getIpAddress();
                break;
            case CUSTOMER:
                key = args[0].toString();
                break;
            default:
                key = StringUtils.upperCase(method.getName());
        }
        ImmutableList<String> keys = ImmutableList.of(StringUtils.joinWith(":", limitAnnotation.prefix(), key));
        try {
            Number count = limitRedisTemplate.execute(RedisLuaScript.arIncr, keys, limitCount, limitPeriod);
//            log.info("check limit count is {} limitCount ={} for prefix={} and key = {}", count, limitCount, limitAnnotation.prefix(), key);
            if (count != null && count.intValue() <= limitCount) {
                return true;
            } else {
                // 加上一个开关控制, 防止有问题时,可以快速调整配置信息,而不必要进行服务发布 2025-01-06
                // 自定义埋点禁止把无穷枚举放进label里, 所以这里新版本里面只取限制注解的前缀, 抛弃key参数拼接
                String labelValue = limitAnnotation.prefix() + (venusConfig.isEnableSendLimitTotalMetricOptimize() ? "" : key);
                LuciferConstant.SEND_LIMIT_TOTAL.labels(labelValue).inc();
                return false;
            }
        } catch (Throwable e) {
            if (e instanceof RuntimeException) {
                throw new RuntimeException(e.getLocalizedMessage());
            }
            throw new RuntimeException("server exception");
        }
    }

    /**
     * 限流 脚本
     *
     * @return lua脚本
     */
    public String buildLuaScript() {
        StringBuilder lua = new StringBuilder();
        lua.append("local c");
        lua.append("\nc = redis.call('get',KEYS[1])");
        // 调用不超过最大值，则直接返回
        lua.append("\nif c and tonumber(c) > tonumber(ARGV[1]) then");
        lua.append("\nreturn c;");
        lua.append("\nend");
        // 执行计算器自加
        lua.append("\nc = redis.call('incr',KEYS[1])");
        lua.append("\nif tonumber(c) == 1 then");
        // 从第一次调用开始限流，设置对应键值的过期
        lua.append("\nredis.call('expire',KEYS[1],ARGV[2])");
        lua.append("\nend");
        lua.append("\nreturn c;");
        return lua.toString();
    }

}
