package com.xhqb.spectre.api.service.impl.assembler;


import com.xhqb.spectre.api.model.smsreq.BaseSMSReqDTO;
import com.xhqb.spectre.api.model.smsresp.BaseSMSResultVO;
import com.xhqb.spectre.api.service.AssemblerSMSService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;


/**
 * 短信发送流程抽象
 */
@Component
public abstract class AbstractAssemblerSMSService implements AssemblerSMSService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractAssemblerSMSService.class);

    /**
     * 发送短信
     *
     * @param
     * @param
     * @return
     */

    public abstract Boolean sendSMSTask(BaseSMSReqDTO baseSMSReqDTO, BaseSMSResultVO baseSMSResultVO);

}
