package com.xhqb.spectre.api.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.api.channel.test.annotation.EncodeDataCollector;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.controller.common.CommonResult;
import com.xhqb.spectre.api.model.entity.BlackAddDTO;
import com.xhqb.spectre.api.model.entity.MobileStatus;
import com.xhqb.spectre.api.model.smsreq.*;
import com.xhqb.spectre.api.model.smsresp.*;
import com.xhqb.spectre.api.service.MobileBlackService;
import com.xhqb.spectre.api.service.PhoneStatusService;
import com.xhqb.spectre.api.service.CheckVerifyCodeService;
import com.xhqb.spectre.api.service.SMSMessageManagerService;
import com.xhqb.spectre.common.dal.mapper.MobileBlackMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping("/api/spectre/v3")
@Slf4j
public class SpectreApiController {

    @Resource
    @Qualifier("SMSMessageManagerServiceImpl")
    SMSMessageManagerService smsMessageManagerService;

    /**
     * SMS V2验证码检查服务
     * 1、 requestID+identificationCode + phoneNumer, verifycCode
     * 2、 字符码批量，目前采用0-9的6位数字码
     */
    @Resource
    @Qualifier("checkVerifyCodeServiceImpl")
    CheckVerifyCodeService checkVerifyCodeService;

    @Resource
    PhoneStatusService phoneStatusService;

    @Resource
    private MobileBlackService mobileBlackService;

    /**
     * 定时/实时短信 通知、市场、催收
     */
    @PostMapping("/sendSMS")
    @EncodeDataCollector
    public CommonResult<BaseSMSResultVO<SingleSMSResultVO>> sendSingleSMS(@RequestHeader Map<String, String> headerMap, @RequestBody SingleSMSReqDTO singleSMSRequest) {
        singleSMSRequest.setAppCode(headerMap.get(SmsApiApplicationConstant.PARAM_APP_KEY.toLowerCase()));
        singleSMSRequest.setSign(headerMap.get(SmsApiApplicationConstant.PARAM_SIGN_KEY.toLowerCase()));
        singleSMSRequest.setRequestId(headerMap.get(SmsApiApplicationConstant.PARAM_REQUESTID.toLowerCase()));
        log.info("sendSingleSMS request: {}", singleSMSRequest);

        BaseSMSResultVO<SingleSMSResultVO> singleSMSResultVO = smsMessageManagerService.dealSingleSms(singleSMSRequest);
        return CommonResult.success(singleSMSResultVO);
    }

    /**
     * 发送验证码
     *
     * @param verifyCodeSMSReqDTO
     * @return
     */
    @PostMapping("/sendVerify")
    public CommonResult<VerifyCodeResultVO> sendVerifyCode(@RequestHeader Map<String, String> headerMap, @RequestBody VerifyCodeSMSReqDTO verifyCodeSMSReqDTO) {
        verifyCodeSMSReqDTO.setAppCode(headerMap.get(SmsApiApplicationConstant.PARAM_APP_KEY.toLowerCase()));
        verifyCodeSMSReqDTO.setSign(headerMap.get(SmsApiApplicationConstant.PARAM_SIGN_KEY.toLowerCase()));
        verifyCodeSMSReqDTO.setRequestId(headerMap.get(SmsApiApplicationConstant.PARAM_REQUESTID.toLowerCase()));
        log.info("sendVerifyCode request: {}", verifyCodeSMSReqDTO);

        VerifyCodeResultVO verifyCodeResultVO = smsMessageManagerService.dealVerifyCodeSms(verifyCodeSMSReqDTO);
        return CommonResult.success(verifyCodeResultVO);
    }

    /**
     * 检验验证码短信
     * 检验验证码： 直接读取Redis队列（带Expire time），比较验证码、识别码，并返回验证码比较结果
     */
    @PostMapping("/checkVerify")
    public CommonResult<VerifyCodeCheckResultVO> checkVerifyCode(@RequestHeader Map<String, String> headerMap, @RequestBody VerifyCodeCheckReqDTO verifyCodeCheckRequest) {
        verifyCodeCheckRequest.setAppCode(headerMap.get(SmsApiApplicationConstant.PARAM_APP_KEY.toLowerCase()));
        verifyCodeCheckRequest.setSign(headerMap.get(SmsApiApplicationConstant.PARAM_SIGN_KEY.toLowerCase()));
        verifyCodeCheckRequest.setRequestId(headerMap.get(SmsApiApplicationConstant.PARAM_REQUESTID.toLowerCase()));
        log.info("checkVerifyCode request: {}", JSON.toJSONString(verifyCodeCheckRequest));

        VerifyCodeCheckResultVO verifyCodeCheckResultVO = checkVerifyCodeService.checkSMSVerifyCode(verifyCodeCheckRequest);
        return CommonResult.success(verifyCodeCheckResultVO);
    }

    /**
     * 定时/实时发送市场营销短信
     */
    @PostMapping("/sendMarket")
    @EncodeDataCollector
    public CommonResult<BaseSMSResultVO<SingleSMSResultVO>> sendSMSMarket(@RequestHeader Map<String, String> headerMap, @RequestBody SingleSMSReqDTO singleSMSRequest) {
        singleSMSRequest.setAppCode(headerMap.get(SmsApiApplicationConstant.PARAM_APP_KEY.toLowerCase()));
        singleSMSRequest.setSign(headerMap.get(SmsApiApplicationConstant.PARAM_SIGN_KEY.toLowerCase()));
        singleSMSRequest.setRequestId(headerMap.get(SmsApiApplicationConstant.PARAM_REQUESTID.toLowerCase()));
        log.info("sendSMSMarket request: {}", singleSMSRequest);

        BaseSMSResultVO<SingleSMSResultVO> singleSMSResultVO = smsMessageManagerService.dealSingleSms(singleSMSRequest);
        return CommonResult.success(singleSMSResultVO);
    }

    /**
     * 检查手机号码状态
     *
     * @param headerMap
     * @param checkPhoneStatusReqDTO
     * @return
     */
    @PostMapping("/checkPhone")
    public CommonResult<CheckPhoneStatusResultVO<MobileStatus>> checkPhoneStatus(@RequestHeader Map<String, String> headerMap,
                                                                                 @RequestBody CheckPhoneStatusReqDTO checkPhoneStatusReqDTO) {
        checkPhoneStatusReqDTO.setRequestId(headerMap.get(SmsApiApplicationConstant.PARAM_REQUESTID.toLowerCase()));
        log.info("checkPhoneStatus requestId:{}, json={}",
                checkPhoneStatusReqDTO.getRequestId(),
                JSON.toJSONString(checkPhoneStatusReqDTO));

        CheckPhoneStatusResultVO<MobileStatus> checkPhoneStatusResultVO = phoneStatusService.getPhoneNumberStatuses(checkPhoneStatusReqDTO);
        return CommonResult.success(checkPhoneStatusResultVO);
    }

    @PostMapping("/refreshPhoneNumberStatuses")
    public CommonResult<Integer> refreshPhoneNumberStatuses(@RequestHeader Map<String, String> headerMap,
                                                          @RequestBody CheckPhoneStatusReqDTO checkPhoneStatusReqDTO) {
        checkPhoneStatusReqDTO.setRequestId(headerMap.get(SmsApiApplicationConstant.PARAM_REQUESTID.toLowerCase()));
        log.info("refreshPhoneNumberStatuses requestId: {}", checkPhoneStatusReqDTO.getRequestId());

        Integer count = phoneStatusService.refreshPhoneNumberStatuses(checkPhoneStatusReqDTO);
        return CommonResult.success(count);
    }

    /**
     * 按内容发送短信
     *
     * @param headerMap
     * @param contentSMSReqDTO
     * @return
     */
    @PostMapping("/sendContentAuto")
    @EncodeDataCollector
    public CommonResult<BaseSMSResultVO<SingleSMSResultVO>> sendSMSContent(@RequestHeader Map<String, String> headerMap, @RequestBody ContentSMSReqDTO contentSMSReqDTO) {
        contentSMSReqDTO.setAppCode(headerMap.get(SmsApiApplicationConstant.PARAM_APP_KEY.toLowerCase()));
        contentSMSReqDTO.setSign(headerMap.get(SmsApiApplicationConstant.PARAM_SIGN_KEY.toLowerCase()));
        contentSMSReqDTO.setRequestId(headerMap.get(SmsApiApplicationConstant.PARAM_REQUESTID.toLowerCase()));
        log.info("sendContent contentSMSReqDTO: {}", contentSMSReqDTO);

        BaseSMSResultVO<SingleSMSResultVO> result = smsMessageManagerService.dealContentSms(contentSMSReqDTO);
        return CommonResult.success(result);
    }

    /**
     * admin 渠道测试api
     * @param headerMap
     * @param singleTestSMSReqDTO
     * @return
     */
    @PostMapping("/testTask")
    public CommonResult<BaseSMSResultVO<SingleSMSResultVO>> sendTestSingleSMS(@RequestHeader Map<String, String> headerMap, @RequestBody SingleTestSMSReqDTO singleTestSMSReqDTO) {
        singleTestSMSReqDTO.setRequestId(headerMap.get(SmsApiApplicationConstant.PARAM_REQUESTID.toLowerCase()));
        log.info("sendTestSingleSMS request: {}", JsonLogUtil.toJSONString(singleTestSMSReqDTO));

        BaseSMSResultVO<SingleSMSResultVO> singleSMSResultVO = smsMessageManagerService.dealTestSingleSms(singleTestSMSReqDTO);
        return CommonResult.success(singleSMSResultVO);
    }

    @PostMapping("/blackCreate")
    public CommonResult<Object> blackCreate(@RequestHeader Map<String, String> headerMap, @RequestBody BlackAddDTO blackAddDTO) {
        log.info("blackCreate request: {}", JsonLogUtil.toJSONString(blackAddDTO));
        return CommonResult.success(mobileBlackService.createBlackListEntry(blackAddDTO));
    }
}
