package com.xhqb.spectre.api.model.entity;

import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.model.smsreq.ContentSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.SingleSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.SingleTestSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.VerifyCodeSMSReqDTO;
import com.xhqb.spectre.common.mq.ChannelCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Optional;

/**
 * 消息实体装配对象
 *
 * @param <T>
 */
@Data
@ToString
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SMSMessageMQAssembler<T> {

    //应用ID
    private String appCode;

    // 手机号码
    private String phone;

    //省份
    private String province;

    // 城市
    private String city;

    // 运营商
    private String isp;

    //消息类型
    private String smsCode;

    // 渠道信息
    private List<ChannelCode> channelCodeSet;

    // 订单ID
    private String orderId;

    // 签名
    private String signName;

    // 分片ID
    private Integer sliceId;

    // 发送时间
    private String sendTime;

    // 发送MQ时间
    private String receiveTime;

    //模版ID
    private String tplCode;

    // 短信内容
    private String content;

    // 模版参数
    private List<String> paramMap;

    // 请求来源
    private Integer reqSrc;

    // 外部流水号
    private String requestId;

    private Integer batchId;

    // 手机号码状态
    private Long phoneStatus;

    /**
     * 业务批次号
     */
    private String bizBatchId;

    /**
     * 是否回调消息中心（0：不回调 1：回调）
     */
    private Integer callMetis;

    public static SMSMessageMQAssembler buildBySingleSMSReqDTO(SingleSMSReqDTO apiDTO, PhoneIsp phoneIsp) {
        return Optional.ofNullable(apiDTO).map(item -> {
            SMSMessageMQAssembler apiDO = SMSMessageMQAssembler.builder()
                    .appCode(item.getAppCode())
                    .smsCode(item.getMessageTypeEnum().getMessageType())
                    .tplCode(item.getTplCode())
                    .sendTime(item.getSendTime())
                    .sliceId(SmsApiApplicationConstant.DEFAULT_SLICE_NUMBER)
                    .province(phoneIsp.getProvince())
                    .city(phoneIsp.getCity())
                    .isp(phoneIsp.getIsp())
                    .phone(phoneIsp.getPhone())
                    .reqSrc(SmsApiApplicationConstant.DEFAULT_REQSRC)
                    .receiveTime(String.valueOf(System.currentTimeMillis()))
                    .batchId(item.getBatchId())
                    .bizBatchId(apiDTO.getBizBatchId())
                    .callMetis(apiDTO.getCallMetis())
                    .build();
            return apiDO;
        }).orElse(null);
    }

    public static SMSMessageMQAssembler buildBySingleTestSMSReqDTO(SingleTestSMSReqDTO apiDTO, PhoneIsp phoneIsp) {
        return Optional.ofNullable(apiDTO).map(item -> {
            SMSMessageMQAssembler apiDO = SMSMessageMQAssembler.builder()
                    .appCode(item.getAppCode())
                    .smsCode(item.getMessageTypeEnum().getMessageType())
                    .tplCode(item.getTplCode())
                    .sendTime(item.getSendTime())
                    .sliceId(SmsApiApplicationConstant.DEFAULT_SLICE_NUMBER)
                    .province(phoneIsp.getProvince())
                    .city(phoneIsp.getCity())
                    .isp(phoneIsp.getIsp())
                    .phone(phoneIsp.getPhone())
                    .reqSrc(SmsApiApplicationConstant.DEFAULT_REQSRC)
                    .receiveTime(String.valueOf(System.currentTimeMillis()))
                    .batchId(item.getBatchId())
                    .bizBatchId(apiDTO.getBizBatchId())
                    .callMetis(apiDTO.getCallMetis())
                    .build();
            return apiDO;
        }).orElse(null);
    }
    public static SMSMessageMQAssembler buildByVerifyCodeReqDTO(VerifyCodeSMSReqDTO verifyCodeSMSReqDTO, PhoneIsp phoneIsp) {
        return Optional.ofNullable(verifyCodeSMSReqDTO).map(item -> {
            SMSMessageMQAssembler apiDO = SMSMessageMQAssembler.builder()
                    .appCode(item.getAppCode())
                    .smsCode(item.getMessageTypeEnum().getMessageType())
                    .tplCode(item.getTplCode())
                    .sendTime(item.getSendTime())
                    .sliceId(SmsApiApplicationConstant.DEFAULT_SLICE_NUMBER)
                    .province(phoneIsp.getProvince())
                    .city(phoneIsp.getCity())
                    .isp(phoneIsp.getIsp())
                    .phone(phoneIsp.getPhone())
                    .reqSrc(SmsApiApplicationConstant.DEFAULT_REQSRC)
                    .receiveTime(String.valueOf(System.currentTimeMillis()))
                    .bizBatchId(verifyCodeSMSReqDTO.getBizBatchId())
                    .callMetis(verifyCodeSMSReqDTO.getCallMetis())
                    .build();
            return apiDO;
        }).orElse(null);
    }

    public static SMSMessageMQAssembler buildByContentSMSReqDTO(ContentSMSReqDTO apiDTO, PhoneIsp phoneIsp) {
        return Optional.ofNullable(apiDTO).map(item -> {
            SMSMessageMQAssembler apiDO = SMSMessageMQAssembler.builder()
                    .appCode(item.getAppCode())
                    .smsCode(item.getSmsType())
                    .tplCode(item.getTplCode())
                    .sendTime(item.getSendTime())
                    .sliceId(SmsApiApplicationConstant.DEFAULT_SLICE_NUMBER)
                    .province(phoneIsp.getProvince())
                    .city(phoneIsp.getCity())
                    .isp(phoneIsp.getIsp())
                    .phone(phoneIsp.getPhone())
                    .reqSrc(SmsApiApplicationConstant.DEFAULT_REQSRC)
                    .receiveTime(String.valueOf(System.currentTimeMillis()))
                    .batchId(item.getBatchId())
                    .bizBatchId(apiDTO.getBizBatchId())
                    .callMetis(apiDTO.getCallMetis())
                    .build();
            return apiDO;
        }).orElse(null);
    }

}
