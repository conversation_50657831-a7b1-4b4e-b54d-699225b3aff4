package com.xhqb.spectre.api.model.entity;

import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.Optional;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class PhoneIsp {

    private String phone;

    private String province;

    private String city;

    private String postCode;

    private String phoneCode;

    private String cityCode;

    private String isp;

    public static PhoneIsp buildPhoneIsp(String[] isps, String phone) {
        if (isps.length != 6) {
            return null;
        }
        return Optional.ofNullable(isps).map(item -> {
            PhoneIsp apiDO = PhoneIsp.builder()
                    .province(isps[0])
                    .city(isps[1])
                    .postCode(isps[2])
                    .phoneCode(isps[3])
                    .cityCode(isps[4])
                    .isp(isps[5])
                    .phone(phone)
                    .build();
            return apiDO;
        }).orElse(null);
    }

    /**
     * 当没有获取到归属地址，设置默认归属地
     *
     * @param phone
     * @return
     */
    public static PhoneIsp buildPhoneIspDefault(String phone) {
        return Optional.ofNullable(phone).map(item -> {
            PhoneIsp phoneIsp = PhoneIsp.builder()
                    .province("")
                    .city("")
                    .postCode("")
                    .phoneCode("")
                    .cityCode("")
                    .isp("")
                    .phone(phone)
                    .build();
            return phoneIsp;
        }).orElse(null);
    }
}
