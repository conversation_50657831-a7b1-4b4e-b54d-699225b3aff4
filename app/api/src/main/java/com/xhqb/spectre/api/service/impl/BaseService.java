package com.xhqb.spectre.api.service.impl;


import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.controller.common.ResultCode;
import com.xhqb.spectre.api.exception.SMSSenderException;
import com.xhqb.spectre.api.model.entity.DynamicParam;
import com.xhqb.spectre.api.model.smsreq.BaseSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.VerifyCodeSMSReqDTO;
import com.xhqb.spectre.api.utils.DateTimeUtil;
import com.xhqb.spectre.api.utils.PhoneNumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;


/**
 * 基础service类
 * 主要完成基础参数的检查
 */
public abstract class BaseService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseService.class);

    /**
     * 默认BG
     */
    protected static final String DEFAULT_BUSINESS_GROUP = "BGALL";

    /**
     * mq最大延迟时间
     */
    @Value("${mq.max.delay.time.hours:72}")
    private volatile int mqMaxDelayTimeHours;

    /**
     * 校验入参
     *
     * @param req
     * @return
     */
    public void checkBaseRequest(BaseSMSReqDTO req) throws SMSSenderException {
        if (req == null) {
            LOGGER.warn("request不能为空");
            throw new SMSSenderException(ResultCode.MISS_REQUEST_PARAM);
        }
        if (StringUtils.isBlank(req.getTplCode())) {
            LOGGER.warn("TplCode不能为空");
            throw new SMSSenderException(ResultCode.MISS_MSG_TEMPLATE);
        }
        if (StringUtils.isBlank(req.getPhoneNumbers()) || StringUtils.isBlank(req.getPhoneNumbers().trim())) {
            LOGGER.warn("发送号码不能为空");
            throw new SMSSenderException(ResultCode.MISS_MSG_PHONES);
        }
        if (req.getPhoneNumbers().split(",").length > SmsApiApplicationConstant.ALLOW_SUBMIT_MAX) {
            LOGGER.warn("发送号码超出最大允许提交值");
            throw new SMSSenderException(ResultCode.EXCEED_MAX_PHONE_NUMBERS);
        }
        if (req.getSendTime() != null) {
            Long sendTime = Long.valueOf(req.getSendTime());
            LOGGER.info("延时发送 {}", req.getPhoneNumbers());
            // 最大可设置的延时时间
            Date forward = DateTimeUtil.mergeForwardHours(mqMaxDelayTimeHours);
            if (sendTime > forward.getTime()) {
                String msg = String.format("延时发送设置的时间大于当前时间%s小时|phoneNumbers:%s", mqMaxDelayTimeHours, req.getPhoneNumbers());
                LOGGER.warn(msg);
                throw new SMSSenderException(ResultCode.SEND_TIME_VALID);
            }
        }
        if (StringUtils.isBlank(req.getSignCode())) {
            req.setSignCode(SmsApiApplicationConstant.SIGN_CODE_DEFAULT);
        }
    }

    /**
     * 校验发送验证码特有参数
     *
     * @param req
     * @throws SMSSenderException
     */
    public void checkVerifyCodeRequest(VerifyCodeSMSReqDTO req) throws SMSSenderException {
        // 检查短信个性化参数
        if (!PhoneNumberUtils.isXhMobileNum(req.getPhoneNumbers())) {
            LOGGER.warn("无效的手机号码");
            throw new SMSSenderException(ResultCode.WRONG_PHONE_NUMBER);
        }
        if (null != req.getCodeLen() && (req.getCodeLen() < 4 || req.getCodeLen() > 6)) {
            LOGGER.warn("验证码长度必须在4-6位");
            throw new SMSSenderException(ResultCode.VERIFY_CODE_LENGTH_ERROR);
        }
        if (req.getPhoneNumbers().split(",").length > 1) {
            LOGGER.warn("发送号码超出最大允许提交值");
            throw new SMSSenderException(ResultCode.EXCEED_MAX_PHONE_NUMBERS);
        }
    }

    /**
     * 批量短信下发时移除重复的发送号码和参数
     *
     * @param tplParamInfos
     * @return
     */
    public List<DynamicParam> removeDuplicateSendInfo(List<DynamicParam> tplParamInfos) {
        Set<DynamicParam> setTplParam = new HashSet<>();
        setTplParam.addAll(tplParamInfos);
        return new ArrayList<>(setTplParam);
    }

}
