package com.xhqb.spectre.api.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tencentcloudapi.cloudhsm.v20191112.models.InquiryPriceBuyVsmRequest;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.model.entity.MobileStatus;
import com.xhqb.spectre.api.service.RedisService;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.dto.AppData;
import com.xhqb.spectre.common.dal.dto.AppSendLimitData;
import com.xhqb.spectre.common.dal.dto.ChannelAccountDisableData;
import com.xhqb.spectre.common.dal.dto.TplData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 从redis中查询数据处理类
 */
@Service
public class RedisServiceImpl implements RedisService {

    @Resource
    private StringRedisTemplate RedisTemplate;

    @Resource
    private RedisTemplate redisTemplate;

    @Value("${phoneStatus.times}")
    private Long redisPhoneStatusTimes;

    @Override
    public AppData getAppInfoByKey(String appKey) {
        boolean flag = RedisTemplate.hasKey(RedisKeys.AppKeys.APP_HASH_KEY);
        if (!flag) {
            return null;
        }
        String jsonStr = (String) RedisTemplate.opsForHash().get(RedisKeys.AppKeys.APP_HASH_KEY, appKey);
        AppData appData = JSON.parseObject(jsonStr, AppData.class);
        return appData;
    }

    @Override
    public TplData getTplDatagetTplByCode(String code) {
        boolean flag = RedisTemplate.hasKey(RedisKeys.TplKeys.TPL_HASH_KEY);
        if (!flag) {
            return null;
        }
        String tplStr = (String) RedisTemplate.opsForHash().get(RedisKeys.TplKeys.TPL_HASH_KEY, code);
        TplData tplData = JSON.parseObject(tplStr, TplData.class);
        return tplData;
    }

    @Override
    public List<ChannelAccountDisableData> getChannelDisabledData(Integer accountId) {
        boolean flag = RedisTemplate.hasKey(RedisKeys.ChannelAccountDisableKeys.DISABLE_HASH_KEY);
        if (!flag) {
            return null;
        }
        String jsonStr = (String) RedisTemplate.opsForHash().get(RedisKeys.ChannelAccountDisableKeys.DISABLE_HASH_KEY, String.valueOf(accountId));
        if (StringUtils.isEmpty(jsonStr)) {
            return null;
        }
        List<ChannelAccountDisableData> list = JSON.parseArray(jsonStr, ChannelAccountDisableData.class);
        if (list == null || list.size() == 0) {
            return null;
        }
        return list;
    }

    /**
     * 判断是否黑名单
     *
     * @param phone
     * @param smsCode
     * @return
     */
    @Override
    @Deprecated
    public boolean isBlack(String phone, String smsCode) {
        String key = smsCode + "_" + phone;
        boolean flag = RedisTemplate.hasKey(RedisKeys.MobileBlackKeys.MOBILE_BLACK_SET_KEY);
        if (!flag) {
            return false;
        }
        Set<String> set = RedisTemplate.opsForSet().members(RedisKeys.MobileBlackKeys.MOBILE_BLACK_SET_KEY);
        if (set == null || set.size() == 0) {
            return false;
        }
        Set<String> value = set.stream().filter(item -> item.equalsIgnoreCase(key)).collect(Collectors.toSet());
        if (value != null && value.size() > 0) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否白名单
     *
     * @param appCode
     * @param phone
     * @return
     */
    @Override
    public boolean isWhite(String appCode, String cfgType, String phone) {
        String key = appCode + "_" + cfgType + "_" + phone;
        boolean flag = RedisTemplate.hasKey(RedisKeys.MobileWhiteKeys.MOBILE_WHITE_SET_KEY);
        if (!flag) {
            return Boolean.FALSE;
        }
        Set<String> set = RedisTemplate.opsForSet().members(RedisKeys.MobileWhiteKeys.MOBILE_WHITE_SET_KEY);
        if (set == null || set.size() == 0) {
            return Boolean.FALSE;
        }
        Set<String> value = set.stream().filter(item -> item.equalsIgnoreCase(key)).collect(Collectors.toSet());
        if (value != null && value.size() > 0) {
            return Boolean.TRUE;
        }
        return false;
    }

    /**
     * 获取应用限流配置
     *
     * @param appCode
     * @return
     */
    @Override
    public List<AppSendLimitData> getSendLimit(String appCode) {
        boolean flag = RedisTemplate.hasKey(RedisKeys.AppSendLimitKeys.APP_SEND_LIMIT_HASH_KEY);
        if (!flag) {
            return null;
        }
        String object = (String) RedisTemplate.opsForHash().get(RedisKeys.AppSendLimitKeys.APP_SEND_LIMIT_HASH_KEY, appCode);
        List<AppSendLimitData> list = JSON.parseArray(object, AppSendLimitData.class);
        if (list == null || list.size() == 0) {
            return null;
        }
        return list;
    }

    @Override
    public void batchSetRedisPipeLined(List<MobileStatus> list) {
        if (list == null || list.size() == 0) {
            return;
        }
        //批量set数据
        RedisTemplate.executePipelined((RedisCallback<MobileStatus>) connection -> {
            for (MobileStatus mobileStatus : list) {
                connection.setEx((SmsApiApplicationConstant.REDISPHONESTATUSKEY + mobileStatus.getMobile()).getBytes(), redisPhoneStatusTimes, JSONObject.toJSONString(mobileStatus).getBytes());
            }
            return null;
        });
    }

    @Override
    public List<MobileStatus> batchGetRedisPipeLined(List<String> keys) {
        if (keys == null || keys.size() == 0) {
            return null;
        }
        List<Object> content = RedisTemplate.executePipelined((RedisCallback<MobileStatus>) connection -> {
            for (String phone : keys) {
                connection.get((SmsApiApplicationConstant.REDISPHONESTATUSKEY + phone).getBytes());
            }
            return null;
        });
        List<MobileStatus> result = new ArrayList<>();
        for (Object obj: content) {
            MobileStatus mobileStatus =  parseMobile(obj);
            if (mobileStatus != null){
                result.add(mobileStatus);
            }
        }
        return result;
    }

    @Override
    public void batchSetRedis(Map<String, String> map) {
        if (map == null || map.size() == 0) {
            return;
        }
        RedisTemplate.opsForValue().multiSet(map);
    }

    @Override
    public List<MobileStatus> batchGetRedis(List<String> keys) {
        if (keys == null || keys.size() == 0) {
            return null;
        }
        List<MobileStatus> result = new ArrayList<>();
        List<String> newKeys = keys.stream().map(item -> setKey(item)).collect(Collectors.toList());
        List<String> content = RedisTemplate.opsForValue().multiGet(newKeys);
        if (!CollectionUtils.isEmpty(content) && content.size() > 0 && content.get(0) != null) {
            result = content.stream().map(item -> parseMobile(item)).collect(Collectors.toList());
        }
        return result;
    }

    private String setKey(String phone) {
        return SmsApiApplicationConstant.REDISPHONESTATUSKEY + phone;
    }

    private MobileStatus parseMobile(Object json) {
        if (json != null) {
            return JSONObject.parseObject(json.toString(), MobileStatus.class);
        }
        return null;
    }

}
