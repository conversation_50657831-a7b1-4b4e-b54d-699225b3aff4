package com.xhqb.spectre.api.utils;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;

/**
 * aes工具类
 *
 * @author: cl
 * @date: 2024/05/22
 */
public final class AesUtil {

    public static String decrypt(String content, String password) {
        final SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, password.getBytes());
        return aes.decryptStr(content, CharsetUtil.CHARSET_UTF_8);
    }

    public static String encrypt(String content, String password) {
        final SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, password.getBytes());
        return aes.encryptBase64(content);
    }

}
