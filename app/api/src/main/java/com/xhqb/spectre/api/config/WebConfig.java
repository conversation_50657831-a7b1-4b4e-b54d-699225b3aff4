package com.xhqb.spectre.api.config;

import com.xhqb.spectre.api.filter.HttpServletRequestReplacedFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * 控制过滤器执行顺序 @order注解无效
 *
 * <AUTHOR>
 * @date 2021/10/13
 */
@Configuration
public class WebConfig {

    @Bean
    public FilterRegistrationBean luciferFilter() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        HttpServletRequestReplacedFilter httpServletRequestReplacedFilter = new HttpServletRequestReplacedFilter();
        filterRegistrationBean.setFilter(httpServletRequestReplacedFilter);
        // 配置过滤规则
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.setName("requestReplaced");
        filterRegistrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return filterRegistrationBean;
    }
}
