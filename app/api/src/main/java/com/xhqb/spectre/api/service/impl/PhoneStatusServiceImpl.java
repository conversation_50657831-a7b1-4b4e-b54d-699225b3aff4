package com.xhqb.spectre.api.service.impl;

import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.controller.common.ResultCode;
import com.xhqb.spectre.api.exception.SMSSenderException;
import com.xhqb.spectre.api.model.entity.MobileStatus;
import com.xhqb.spectre.api.model.smsreq.CheckPhoneStatusReqDTO;
import com.xhqb.spectre.api.model.smsresp.CheckPhoneStatusResultVO;
import com.xhqb.spectre.api.phonenumber.CheckPhoneNumberStatusService;
import com.xhqb.spectre.api.phonenumber.PhoneNumberStatusFactory;
import com.xhqb.spectre.api.phonenumber.RefreshPhoneNumberStatusService;
import com.xhqb.spectre.api.service.PhoneStatusService;
import com.xhqb.spectre.api.service.TencentApiService;
import com.xhqb.spectre.api.utils.CmppMsgIdUtils;
import com.xhqb.spectre.api.utils.PhoneNumberUtils;
import com.xhqb.spectre.common.dal.entity.PhoneNumberStatusDO;
import com.xhqb.spectre.common.dal.mapper.PhoneNumberStatusMapper;
import com.xhqb.spectre.common.enums.CarrierEnum;
import com.xhqb.spectre.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 获取手机号码状态
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@Service
@Slf4j
public class PhoneStatusServiceImpl extends BaseService implements PhoneStatusService {

    @Resource
    private PhoneNumberStatusMapper phoneNumberStatusMapper;

    @Resource
    private TencentApiService tencentApiService;

    @Resource
    private RefreshPhoneNumberStatusService refreshPhoneNumberStatusService;

    @Resource
    private PhoneNumberStatusFactory phoneNumberStatusFactory;

    @Resource
    private VenusConfig venusConfig;

    @Override
    public CheckPhoneStatusResultVO<MobileStatus> check(CheckPhoneStatusReqDTO checkPhoneStatusReqDTO) {
        CheckPhoneStatusResultVO result = new CheckPhoneStatusResultVO();
        checkParam(checkPhoneStatusReqDTO);
        long t1 = System.currentTimeMillis();
        List<String> phones = Arrays.stream(checkPhoneStatusReqDTO.getPhoneNumbers().split(",")).map(item -> item).collect(Collectors.toList());
        List<String> normalPhones = phones.stream().filter(item -> PhoneNumberUtils.isXhMobileNum(item)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(normalPhones)) {
            log.warn("无可用的手机号码");
            throw new SMSSenderException(ResultCode.WRONG_PHONE_NUMBER);
        }
        List<MobileStatus> list = tencentApiService.checkMobileStatus(normalPhones);
        if (CollectionUtils.isEmpty(list)) {
            // 如果接口不正常
            log.info("外部接口异常 checkPhone error time:{}", System.currentTimeMillis() - t1);
            throw new SMSSenderException(ResultCode.NULL_RETURN);
        }
        List<String> errorPhones = phones.stream().filter(item -> !PhoneNumberUtils.isXhMobileNum(item)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(errorPhones)) {
            List<MobileStatus> errorList = errorPhones.stream().map(item -> MobileStatus.buildMobileStatusError(item)).collect(Collectors.toList());
            list.addAll(errorList);
        }
        result.setPhoneResult(list);
        if (StringUtils.isEmpty(checkPhoneStatusReqDTO.getRequestId())) {
            long t4 = System.currentTimeMillis();
            String requestId = CmppMsgIdUtils.getMsgId();
            result.setRequestId(requestId);
        } else {
            result.setRequestId(checkPhoneStatusReqDTO.getRequestId());
        }
        return result;
    }

    @Override
    public CheckPhoneStatusResultVO<MobileStatus> getPhoneNumberStatuses(CheckPhoneStatusReqDTO checkPhoneStatusReqDTO) {

        checkParam(checkPhoneStatusReqDTO);

        Iterable<String> split = Splitter.on(",")
                .trimResults()
                .omitEmptyStrings()
                .split(checkPhoneStatusReqDTO.getPhoneNumbers());
        Set<String> originalPhoneNumbers = Sets.newHashSet(split);

        Set<String> normalPhoneNumbers = originalPhoneNumbers
                .stream()
                .filter(PhoneNumberUtils::isXhMobileNum)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(normalPhoneNumbers)) {
            log.warn("无可用的手机号码");
            throw new SMSSenderException(ResultCode.WRONG_PHONE_NUMBER);
        }

        Integer timestamp = DateUtil.getNow() - venusConfig.getPhoneNumberStatusExpired();
        List<PhoneNumberStatusDO> phoneNumberStatusDOList = phoneNumberStatusMapper.selectByMobiles(normalPhoneNumbers, timestamp);

        Set<String> existPhoneNumbers = phoneNumberStatusDOList
                .stream()
                .map(PhoneNumberStatusDO::getMobile)
                .collect(Collectors.toSet());

        List<MobileStatus> mobileStatusList = phoneNumberStatusDOList.stream()
                .map(MobileStatus::buildMobileStatus)
                .collect(Collectors.toList());

        List<MobileStatus> invalidMobileStatusList;
        Set<String> invalidPhoneNumbers = Sets.difference(originalPhoneNumbers, existPhoneNumbers);
        if (originalPhoneNumbers.size() <= venusConfig.getRealTimeNum() && !invalidPhoneNumbers.isEmpty()) {
            invalidMobileStatusList = checkAndSave(invalidPhoneNumbers);
            mobileStatusList.addAll(invalidMobileStatusList);
        } else {
            invalidMobileStatusList = invalidPhoneNumbers.stream()
                    .map(MobileStatus::buildMobileStatusError)
                    .collect(Collectors.toList());
            mobileStatusList.addAll(invalidMobileStatusList);
        }
        log.info("checkPhoneStatus result; requestId:{}, 总数={}, 缓存数={}, 调用数={}",
                checkPhoneStatusReqDTO.getRequestId(),
                originalPhoneNumbers.size(),
                existPhoneNumbers.size(),
                invalidMobileStatusList.size());
        return buildCheckPhoneStatusResultVO(checkPhoneStatusReqDTO.getRequestId(), mobileStatusList);
    }

    private CheckPhoneStatusResultVO<MobileStatus> buildCheckPhoneStatusResultVO(String requestId, List<MobileStatus> mobileStatusList) {
        CheckPhoneStatusResultVO result = new CheckPhoneStatusResultVO();
        if (StringUtils.isEmpty(requestId)) {
            requestId = CmppMsgIdUtils.getMsgId();
        }
        result.setRequestId(requestId);
        result.setPhoneResult(mobileStatusList);
        return result;
    }

    @Override
    public int refreshPhoneNumberStatuses(CheckPhoneStatusReqDTO checkPhoneStatusReqDTO) {

        //分割号码
        List<String> phoneNumbers = Splitter.on(",")
                .trimResults()
                .omitEmptyStrings()
                .splitToList(checkPhoneStatusReqDTO.getPhoneNumbers());


        //过滤非法号码
        List<String> normalPhoneNumbers = phoneNumbers
                .stream()
                .filter(PhoneNumberUtils::isXhMobileNum)
                .collect(Collectors.toList());

        log.info("刷新手机号状态; requestId={}, 总数={}, 过滤后={}",
                checkPhoneStatusReqDTO.getRequestId(),
                phoneNumbers.size(), normalPhoneNumbers.size());

        //发送号码至队列
        refreshPhoneNumberStatusService.refresh(normalPhoneNumbers);
        return normalPhoneNumbers.size();
    }

    @Override
    public List<MobileStatus> checkAndSave(Set<String> phoneNumbers) {
        CheckPhoneNumberStatusService checkPhoneNumberStatusService = phoneNumberStatusFactory
                .getCheckPhoneNumberStatusService("aliyun");
        try {
            List<MobileStatus> mobileStatuses = checkPhoneNumberStatusService.check(phoneNumbers);
            List<PhoneNumberStatusDO> phoneNumberStatusDOList = mobileStatuses.stream()
                    .filter(mobileStatus -> !SmsApiApplicationConstant.PHONE_STATUS_CODE99.equals(mobileStatus.getStatus()))
                    .map(this::buildPhoneNumberStatusDO)
                    .collect(Collectors.toList());
            if (!phoneNumberStatusDOList.isEmpty()) {
                phoneNumberStatusMapper.saveOrUpdateBatch(phoneNumberStatusDOList);
            }
            return mobileStatuses;
        } catch (Exception e) {
            log.error("查询状态异常", e);
            return phoneNumbers.stream()
                    .map(MobileStatus::buildMobileStatusError)
                    .collect(Collectors.toList());
        }
    }

    private Set<String> getPhoneNumbers(String textBasedMessage) {
        List<String> phoneNumbers = Splitter.on(",")
                .trimResults()
                .omitEmptyStrings()
                .splitToList(textBasedMessage);

        return phoneNumbers
                .stream()
                .filter(PhoneNumberUtils::isXhMobileNum)
                .collect(Collectors.toSet());
    }

    private void checkParam(CheckPhoneStatusReqDTO req) {
        if (StringUtils.isBlank(req.getPhoneNumbers().trim())) {
            log.warn("手机号码不能为空");
            throw new SMSSenderException(ResultCode.MISS_MSG_PHONES);
        }
        if (req.getPhoneNumbers().split(",").length > SmsApiApplicationConstant.ALLOW_SUBMIT_MAX) {
            log.warn("手机号码超出最大允许提交值");
            throw new SMSSenderException(ResultCode.EXCEED_MAX_PHONE_NUMBERS);
        }
    }

    public PhoneNumberStatusDO buildPhoneNumberStatusDO(MobileStatus mobileStatus) {
        CarrierEnum carrierEnum = CarrierEnum.getCarrier(mobileStatus.getNumberType());
        Date now = new Date();
        Integer lastTime = (int) now.toInstant().getEpochSecond();
        return PhoneNumberStatusDO.builder()
                .mobile(mobileStatus.getMobile())
                .status((int) mobileStatus.getStatus().longValue())
                .lastTime(lastTime)
                .carrier(carrierEnum.getId())
                .createTime(now)
                .updateTime(now)
                .build();
    }
}
