package com.xhqb.spectre.api.model.entity;

import lombok.Data;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/2/6
 */
@Data
public class ShortUrlTplDTO implements Serializable {
    private static final long serialVersionUID = 995632787647875479L;
    /**
     * 长链
     */
    @NotBlank(message = "长链不能为空")
    @Size(max = 512, message = "长链最大为{max}个字符")
    @URL(message = "长链格式有误")
    private String longUrl;

    /**
     * 有效期。
     */
    @NotNull(message = "有效期不能为空")
    private Integer validPeriod;

    /**
     * 业务 id
     */
    private String bizId;


    /**
     * 标志 formal, test
     */
    @NotNull(message = "模板使用场景不能为空")
    private String scenario;

}
