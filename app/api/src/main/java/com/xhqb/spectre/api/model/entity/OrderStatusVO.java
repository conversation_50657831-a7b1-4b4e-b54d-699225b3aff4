package com.xhqb.spectre.api.model.entity;

import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderStatusVO implements Serializable {
    /**
     * requestId
     */
    private String requestId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 发送状态
     */
    private int sendStatus;

    /**
     * 回执状态
     */
    private int reportStatus;

    /**
     * 回执状态码
     */
    private String reportCode;

    /**
     * 回执描述
     */
    private String reportDesc;

    /**
     * 计费条数
     */
    private Integer billCount;

    public static OrderStatusVO buildOrderStatusVO(SmsOrderDO smsOrderDO) {
        return OrderStatusVO.builder()
                .requestId(smsOrderDO.getRequestId())
                .mobile(smsOrderDO.getMobile())
                .orderId(smsOrderDO.getOrderId())
                .sendStatus(smsOrderDO.getSendStatus())
                .reportStatus(smsOrderDO.getReportStatus())
                .billCount(smsOrderDO.getBillCount())
                .reportCode(smsOrderDO.getReportCode())
                .reportDesc(smsOrderDO.getReportDesc())
                .build();
    }
}
