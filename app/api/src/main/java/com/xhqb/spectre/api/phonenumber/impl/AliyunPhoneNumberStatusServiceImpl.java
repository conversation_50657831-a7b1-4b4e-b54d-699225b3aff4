package com.xhqb.spectre.api.phonenumber.impl;

import com.aliyun.sdk.service.dytnsapi20200217.AsyncClient;
import com.aliyun.sdk.service.dytnsapi20200217.models.PhoneNumberStatusForPublicRequest;
import com.aliyun.sdk.service.dytnsapi20200217.models.PhoneNumberStatusForPublicResponse;
import com.aliyun.sdk.service.dytnsapi20200217.models.PhoneNumberStatusForPublicResponseBody;
import com.google.common.util.concurrent.RateLimiter;
import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.constant.PhoneNumberStatusEnum;
import com.xhqb.spectre.api.model.entity.MobileStatus;
import com.xhqb.spectre.api.phonenumber.CheckPhoneNumberStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AliyunPhoneNumberStatusServiceImpl implements CheckPhoneNumberStatusService {

    private final AsyncClient asyncClient;

    private VenusConfig venusConfig;

    private static RateLimiter rateLimiter;

    @PostConstruct
    public void init() {
        rateLimiter = RateLimiter.create(this.venusConfig.getPhoneNumberStatusLimit());
    }

    @Autowired
    public AliyunPhoneNumberStatusServiceImpl(AsyncClient asyncClient, VenusConfig venusConfig) {
        this.asyncClient = asyncClient;
        this.venusConfig = venusConfig;
    }

    @Override
    public List<MobileStatus> check(Set<String> phones) {
        List<CompletableFuture<MobileStatus>> futures = new ArrayList<>();
        rateLimiter.acquire(phones.size());
        for (String phoneNumber : phones) {
            PhoneNumberStatusForPublicRequest request = PhoneNumberStatusForPublicRequest.builder()
                    .inputNumber(phoneNumber)
                    .authCode(venusConfig.getAuthCode())
                    .mask(venusConfig.getMask())
                    .build();
            CompletableFuture<PhoneNumberStatusForPublicResponse> responseCompletableFuture = asyncClient
                    .phoneNumberStatusForPublic(request);

            CompletableFuture<MobileStatus> mobileStatusCompletableFuture = responseCompletableFuture.thenApply(res -> {
                if (res != null && res.getStatusCode() == HttpStatus.OK.value()) {
                    PhoneNumberStatusForPublicResponseBody body = res.getBody();
                    if (body != null && "OK".equals(body.getCode())) {
                        PhoneNumberStatusForPublicResponseBody.Data data = body.getData();
                        if (data != null && data.getStatus() != null) {
                            PhoneNumberStatusEnum phoneNumberStatusEnum = PhoneNumberStatusEnum.getByStatus(data.getStatus());
                            MobileStatus mobileStatus = new MobileStatus();
                            mobileStatus.setMobile(phoneNumber);
                            mobileStatus.setStatus(phoneNumberStatusEnum.getCode());
                            mobileStatus.setNumberType(data.getCarrier());
                            return mobileStatus;
                        }
                    }
                }
                log.warn("查询手机号状态失败, mobile:{}, response={}", phoneNumber, res);
                return MobileStatus.buildMobileStatusError(phoneNumber);
            }).exceptionally(ex -> {
                log.warn("查询手机号状态失败, mobile:{}", phoneNumber, ex);
                return MobileStatus.buildMobileStatusError(phoneNumber);
            });

            futures.add(mobileStatusCompletableFuture);
        }
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList()))
                .join();
    }
}
