package com.xhqb.spectre.api.utils;

import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;

import java.util.Calendar;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 生成msgId
 */
public class CmppMsgIdUtils {

    private static Calendar cal = Calendar.getInstance();

    private static final long gateway = Long.valueOf(SmsApiApplicationConstant.DEFAULT_GATEWAY_CODE + (int) (Math.random() * 900 + 100)).longValue();

    private final static long getMsgid(long GwSeq) {
        long res = 0;
        cal.setTimeInMillis(System.currentTimeMillis());
        long month = cal.get(Calendar.MONTH) + 1;
        long day = cal.get(Calendar.DAY_OF_MONTH);
        long hour = cal.get(Calendar.HOUR_OF_DAY);
        long minute = cal.get(Calendar.MINUTE);
        long seconds = cal.get(Calendar.SECOND);
        res = month << 60 | day << 55 | hour << 50 | minute << 44 | seconds << 38 | GwSeq << 16 | getMsgIdSeq();
        return res;
    }

    private static AtomicLong msgidseq = new AtomicLong(100l);

    /**
     *  * 自增循环使用(msgid)	 
     */
    private final static long getMsgIdSeq() {
        long next = msgidseq.incrementAndGet();
        if (next > 65535) {
            synchronized (msgidseq) {
                if (msgidseq.get() > 65535) {
                    msgidseq.set(100);
                    next = msgidseq.incrementAndGet();
                } else {
                    next = msgidseq.incrementAndGet();
                }
            }
        }
        return next;
    }

    private static String getMsgID(long l) {
        long mm = l, dd = l, HH = l, MM = l, SS = l, gw = l, sq = l;
        mm >>>= 60;
        dd = dd & 0xF80000000000000L;
        dd >>>= 55;
        HH = HH & 0x7C000000000000L;
        HH >>>= 50;
        MM = MM & 0x3F00000000000L;
        MM >>>= 44;
        SS = SS & 0xFC000000000L;
        SS >>>= 38;
        gw = gw & 0x3FFFFF0000L;
        gw >>>= 16;
        sq = sq & 0xFFFFL;
        return String.format("%1$02d%2$02d%3$02d%4$02d%5$02d%6$07d%7$05d", mm, dd, HH, MM, SS, gw, sq);
    }

    public static String getMsgId() {
        long msgId = getMsgid(gateway);
        return getMsgID(msgId);
    }

}
