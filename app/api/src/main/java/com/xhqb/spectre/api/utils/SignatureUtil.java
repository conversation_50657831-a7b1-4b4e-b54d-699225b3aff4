package com.xhqb.spectre.api.utils;

import com.xhqb.spectre.api.model.sign.BizAccount;
import com.xhqb.spectre.api.model.sign.BizParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;


public class SignatureUtil {

    private static Logger logger = LoggerFactory.getLogger(SignatureUtil.class);

    /**
     * 获取签名字符串
     *
     * @param params 参数数组
     * @return 签名字符串
     */
    public static String getSignature(BizParam[] params, BizAccount account) {
        String signature = "";
        if (params != null) {
            // 排序
            sortParams(params);
            // 拼接参数
            String stitchParams = stitchParams(params);
            // 拼接privateKey
            stitchParams += account.getPrivateKey();
            // 签名
            signature = sha1(stitchParams);
        }
        return signature;
    }

    /**
     * 获取签名字符串
     *
     * @param params 参数列表
     * @return 签名字符串
     */
    public static String getSignature(List<BizParam> params, BizAccount account) {
        String signature = "";
        if (params != null) {
            // 排序
            sortParams(params);
            // 拼接参数
            String stitchParams = stitchParams(params);
            // 拼接privateKey
            stitchParams += account.getPrivateKey();
            // 签名
            signature = sha1(stitchParams);
        }
        return signature;
    }

    /**
     * @param params 参数数组
     * @return 签名后的参数数组
     */
    public static BizParam[] getParamAfterSignature(BizParam[] params, BizAccount account) {
        if (params == null) {
            params = new BizParam[0];
        }
        Object[] objects = insertElement2Array(params, new BizParam("signature", getSignature(params, account)), params.length);
        if (objects == null) {
            objects = new BizParam[0];
        }
        int len = objects.length;
        BizParam[] newParams = new BizParam[len];
        for (int i = 0; i < len; i++) {
            if (objects[i] instanceof BizParam) {
                newParams[i] = (BizParam) objects[i];
            }
        }
        return newParams;
    }

    /**
     * @param params 参数列表
     * @return 签名后的参数列表
     */
    public static List<BizParam> getParamAfterSignature(List<BizParam> params, BizAccount account) {
        if (params != null) {
            params.add(new BizParam("signature", getSignature(params, account)));
        }
        return params;
    }


    /**
     * 对参数按照key的升序进行排序
     *
     * @param params 参数数组
     */
    public static void sortParams(BizParam[] params) {
        int num = params.length;
        for (int i = 0; i < num; i++) {
            for (int j = i + 1; j < num; j++) {
                if (params[i].getParamKey().compareTo(params[j].getParamKey()) > 0) {
                    BizParam param = params[i];
                    params[i] = params[j];
                    params[j] = param;
                }
            }
        }
    }


    /**
     * 参数排序
     *
     * @param params 参数列表
     */
    public static void sortParams(List<BizParam> params) {
        if (params != null) {
            // 排序
            Collections.sort(params, new Comparator<BizParam>() {
                @Override
                public int compare(BizParam p1, BizParam p2) {
                    return p1.getParamKey().compareTo(p2.getParamKey());
                }
            });
        }
    }

    /**
     * 对参数进行url编码
     *
     * @param params 参数数组
     */
    public static void urlEncodeParams(BizParam[] params) {
        if (params != null) {
            int num = params.length;
            for (int i = 0; i < num; i++) {
                try {
                    params[i].setParamValue(URLEncoder.encode(params[i].getParamValue().toString(), "utf-8"));
                } catch (UnsupportedEncodingException e) {
                    logger.error("Unsupported Encoding Exception : {}", e.getMessage());
                }
            }
        }
    }


    /**
     * 对参数进行Url编码
     *
     * @param params 参数列表
     */
    public static void urlEncodeParams(List<BizParam> params) {
        if (params != null) {
            for (BizParam param : params) {
                try {
                    param.setParamValue(URLEncoder.encode(param.getParamValue().toString(), "utf-8"));
                } catch (UnsupportedEncodingException e) {
                    logger.error("Unsupported Encoding Exception : {}", e.getMessage());
                }
            }
        }
    }


    /**
     * 获取签名串
     *
     * @param params 签名参数
     * @return 待签名的签名串
     */
    public static String stitchParams(BizParam[] params) {
        StringBuilder builder = new StringBuilder();
        if (params != null) {
            int num = params.length;
            for (int i = 0; i < num; i++) {
                builder.append(params[i].getParamKey());
                builder.append(params[i].getParamValue());
            }
        }
        return builder.toString();
    }


    /**
     * 获取签名串
     *
     * @param params 参数列表
     * @return 待签名的签名串
     */
    public static String stitchParams(List<BizParam> params) {
        StringBuilder builder = new StringBuilder();
        if (params != null) {
            for (BizParam param : params) {
                if (param.getParamValue() == null) {
                    continue;
                }
                builder.append(param.getParamKey());
                builder.append(param.getParamValue());
            }
        }
        return builder.toString();
    }


    /**
     * sha1加密
     *
     * @param decrypt 待加密的字符串
     * @return 加密后的字符串
     */
    public static String sha1(String decrypt) {
        try {
            MessageDigest digest = java.security.MessageDigest
                    .getInstance("SHA-1");
            digest.update(decrypt.getBytes());
            byte[] messageDigest = digest.digest();
            // Create Hex String
            return FormatUtil.formatBytes2HexString(messageDigest, false);

        } catch (NoSuchAlgorithmException e) {
            logger.error("sha1 encrypt exception: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 数组插入元素
     *
     * @param objects 原数组
     * @param element 元素
     * @param index   插入下标
     * @return 新数组
     */
    private static Object[] insertElement2Array(Object[] objects, Object element, int index) {
        Object[] newObjs = null;
        if (objects != null) {
            if (objects[0].getClass().equals(element.getClass())) {
                int len = objects.length;
                newObjs = new Object[len + 1];
                System.arraycopy(objects, 0, newObjs, 0, index);
                newObjs[index] = element;
                System.arraycopy(objects, index, newObjs, index, len - index);
            }
        } else {
            newObjs = new Object[1];
            newObjs[0] = element;
        }
        return newObjs;
    }
}
