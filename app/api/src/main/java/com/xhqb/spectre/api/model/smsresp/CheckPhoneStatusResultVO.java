package com.xhqb.spectre.api.model.smsresp;

import com.xhqb.kael.util.tostring.XhJsonToStringStyle;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
public class CheckPhoneStatusResultVO<T> implements Serializable {

    private static final long serialVersionUID = 5189923991017739372L;
    /**
     * 返回短信发送请求的请求Id
     */
    private String requestId;

    /**
     * 发送结果描述
     */
    private T phoneResult;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, XhJsonToStringStyle.XH_JSON_STYLE);
    }
}
