package com.xhqb.spectre.api.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.xhqb.kael.boot.autoconfigure.druid.DatasourceConfigUtils;
import com.xhqb.kael.boot.autoconfigure.druid.DefaultConnectionProperties;
import com.xhqb.kael.boot.autoconfigure.druid.DruidConnectionProperties;
import com.xhqb.spectre.api.config.properties.BiDruidProperties;
import com.xhqb.spectre.api.utils.CommonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.List;


@Configuration
@MapperScan(basePackages = "com.xhqb.spectre.api.bi.mapper", sqlSessionTemplateRef = "biSqlSessionTemplate")
@EnableConfigurationProperties(BiDruidProperties.class)
public class BiDataSourceConfig {

    /**
     * bi数据源
     *
     * @param biDruidProperties
     * @return
     */
    @Bean(name = "biDataSource")
    public DataSource biDataSource(BiDruidProperties biDruidProperties) {
        DruidConnectionProperties defaultProperties = DruidConnectionProperties.withDefault(new DefaultConnectionProperties());
        BeanUtils.copyProperties(biDruidProperties, defaultProperties, CommonUtil.getNullPropertyNames(biDruidProperties));
        DruidDataSource dataSource = DatasourceConfigUtils.createDataSource(defaultProperties);
        dataSource.setName("bi");
        return dataSource;
    }

    /**
     * session factory
     *
     * @param dataSource
     * @param interceptorObjectProvider
     * @return
     * @throws Exception
     */
    @Bean(name = "biSqlSessionFactory")
    public SqlSessionFactory biSqlSessionFactory(@Qualifier("biDataSource") DataSource dataSource,
                                                  ObjectProvider<List<Interceptor>> interceptorObjectProvider) throws Exception {
        final SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        //设置加解密插件
        List<Interceptor> interceptorList = interceptorObjectProvider.getIfAvailable();
        if (CollectionUtils.isNotEmpty(interceptorList)) {
            bean.setPlugins(interceptorList.toArray(new Interceptor[0]));
        }
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/bi/*Mapper.xml"));
        return bean.getObject();
    }

    /**
     * transaction manager
     *
     * @param dataSource
     * @return
     */
    @Bean(name = "biTransactionManager")
    public DataSourceTransactionManager biTransactionManager(@Qualifier("biDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * build sql session template
     *
     * @param sqlSessionFactory
     * @return
     */
    @Bean(name = "biSqlSessionTemplate")
    public SqlSessionTemplate biSqlSessionTemplate(@Qualifier("biSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
