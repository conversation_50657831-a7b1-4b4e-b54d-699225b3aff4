package com.xhqb.spectre.api.config.properties;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/*
 * @Author: yjq
 * @Date: 2024/07/30 17:51
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ConfigurationProperties(prefix = "kael.datasource.druid.spectre-api")
public class SpectreDruidProperties extends DruidProperties {
}
