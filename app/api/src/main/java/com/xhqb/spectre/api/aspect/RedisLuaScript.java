package com.xhqb.spectre.api.aspect;

import org.springframework.data.redis.core.script.DefaultRedisScript;

/**
 * 限流脚本
 *
 * <AUTHOR>
 * @date 2021/10/12
 */
public class RedisLuaScript {
    /**
     * 限流计数+1脚本
     * 如果值大于限制值 直接返回
     * 否则+1 返回
     */
    private static final String arIncrStr = "local c"
            + "\nc = redis.call('get',KEYS[1])"
            + "\nif not c or tonumber(c) < tonumber(ARGV[1]) then"
            + "\nc = redis.call('incr',KEYS[1])"
            + "\nif tonumber(c) == 1 then"
            + "\nredis.call('expire',KEYS[1],ARGV[2])"
            + "\nend"
            + "\nreturn c;"
            + "\nend"
            + "\nif c and tonumber(c) > tonumber(ARGV[1]) then"
            + "\nreturn c;"
            + "\nend";

    /**
     * 增加计数
     */
    public static final DefaultRedisScript<Long> arIncr = new DefaultRedisScript<>(arIncrStr, Long.class);

    /**
     * 限流计数-1脚本
     */
    private static final String arDecrStr = "local count = redis.call('get',KEYS[1]);"
            + "\nif count and tonumber(count)>0 then "
            + "\ncount=redis.call('incrby',KEYS[1],-1);"
            + "\nend;"
            + "\nreturn tonumber(count)";

    /**
     * 减去计数
     */
    public static final DefaultRedisScript<Long> arDecr = new DefaultRedisScript<>(arDecrStr, Long.class);


    /**
     * 债转短信脚本
     * KEYS[1]:债转短信提交的总数key
     * KEYS[2]:债转短信有效的总数key
     * KEYS[3]:债转短信批次是否已存储key
     * ARGV[1]:债转短信提交的总数
     * ARGV[2]:债转短信有效的总数
     * ARGV[3]:当前key过期时间
     */
    private static final String debtSmsIncrStr = "redis.call('incrby',KEYS[1],tonumber(ARGV[1]));"
            + "\nredis.call('incrby',KEYS[2],tonumber(ARGV[2]));"
            + "\nredis.call('expire',KEYS[1],tonumber(ARGV[3]));"
            + "\nredis.call('expire',KEYS[2],tonumber(ARGV[3]));"
            + "\nif redis.call('setnx',KEYS[3],'1') == 1 then "
            + "\nredis.call('expire',KEYS[3],tonumber(ARGV[3]));"
            + "\nreturn 1 else return 0 end;";

    /**
     * 债转短信计数
     */
    public static final DefaultRedisScript<Long> debtSmsIncr = new DefaultRedisScript<>(debtSmsIncrStr, Long.class);
}
