package com.xhqb.spectre.api.model.smsresp;

import com.xhqb.spectre.api.model.entity.SMSSendFailedRecord;
import com.xhqb.spectre.api.model.entity.SMSSendSuccessRecord;
import lombok.Data;

import java.io.Serializable;

@Data
public class SingleSMSResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  短信发送状态成功记录
     */
    private SMSSendSuccessRecord sendSuccessRecord;

    /**
     *  短信发送状态失败记录
     */
    private SMSSendFailedRecord smsSendFailedRecord;
}
