package com.xhqb.spectre.api.service;

import com.xhqb.spectre.api.model.entity.ErrorPhone;
import com.xhqb.spectre.api.model.entity.SMSMessageMQAssembler;
import com.xhqb.spectre.api.model.smsreq.BaseSMSReqDTO;
import com.xhqb.spectre.api.model.smsresp.BaseSMSResultVO;
import com.xhqb.spectre.api.model.smsresp.BatchSMSResultVO;
import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.common.mq.MessageMQ;

import java.util.List;

public interface SendMessageService {
    boolean sendMessage(MessageMQ<BaseBodyMessage> singleMessageMQ, BaseSMSReqDTO baseSMSReqDTO, BaseSMSResultVO baseSMSResultVO);

    MessageMQ<BaseBodyMessage> buildSingleMessageMQ(SMSMessageMQAssembler smsMessageMQAssember) ;

    BatchSMSResultVO buildBatchSMSResultVO(BaseSMSReqDTO baseSMSReqDTO, BaseSMSResultVO baseSMSResultVO, List<String> successPhoneList, List<ErrorPhone> failedPhoneList);
}
