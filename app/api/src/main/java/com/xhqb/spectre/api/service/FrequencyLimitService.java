package com.xhqb.spectre.api.service;

import com.xhqb.spectre.api.constant.LimitCheckResultEnum;


public interface FrequencyLimitService {


    LimitCheckResultEnum checkSendLimitFrequencyFrom(String tplCode, String appCode, String cfgType, String phone, String smsCode);

    
    /**
     * 减去redis中频率计数
     *
     * @param tlpCode
     * @param appCode
     * @param cfgType
     * @param phone
     */
    void subtractSMSFrequencyLimitCount(String tlpCode, String appCode, String cfgType, String phone, String smsCode);
}
