package com.xhqb.spectre.api.model.smsreq;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 按内容发送短信请求对象
 *
 * <AUTHOR>
 * @date 2022-01-10
 */
@Data
@ToString(callSuper = true)
public class ContentSMSReqDTO extends BaseSMSReqDTO implements Serializable {

    private static final long serialVersionUID = 5938206782957923978L;

    /**
     * 发送内容 key 手机号码 value 短信内容 最大支持50个
     */
    private String content;

    /**
     * 短信类型
     */
    private String smsType;

    /**
     * 内容参数
     */
    private List<Map<String, String>> paramList;

    /**
     * 有效的发送号码
     */
    private List<String> phoneList;
}
