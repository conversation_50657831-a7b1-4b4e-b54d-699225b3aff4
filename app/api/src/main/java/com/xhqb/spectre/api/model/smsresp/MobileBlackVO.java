package com.xhqb.spectre.api.model.smsresp;

import com.xhqb.spectre.api.constant.MobileBlackConstant;
import com.xhqb.spectre.api.utils.CommonUtil;
import com.xhqb.spectre.common.dal.entity.MobileBlackDO;
import com.xhqb.spectre.common.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MobileBlackVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 用户CID
     */
    private String cid;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 黑名单来源
     */
    private String source;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 描述，拉黑原因
     */
    private String description;

    /**
     * 是否人工添加
     */
    private Boolean manual;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新人
     */
    private String updater;


    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 过期时间
     */
    private String expiredTime;

    public static MobileBlackVO buildMobileBlackVO(MobileBlackDO item, boolean isMobileMask) {
        String mobile = isMobileMask ? CommonUtil.maskMobile(item.getMobile()) : item.getMobile();
        return MobileBlackVO.builder()
                .id(item.getId())
                .cid(item.getCid())
                .mobile(mobile)
                .source(item.getSource())
                .smsTypeCode(item.getSmsTypeCode())
                .description(item.getDescription())
                .manual(item.getAddType().equals(MobileBlackConstant.ADD_TYPE_MANUAL))
                .createTime(DateUtil.dateToString(item.getCreateTime()))
                .creator(item.getCreator())
                .updateTime(DateUtil.dateToString(item.getUpdateTime()))
                .updater(item.getUpdater())
                .appCode(item.getAppCode())
                .expiredTime(DateUtil.dateToString(item.getExpiredTime()))
                .build();
    }
}
