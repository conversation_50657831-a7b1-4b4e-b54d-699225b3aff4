package com.xhqb.spectre.api.channel.test.aop;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.api.channel.test.producer.ChannelApiProducer;
import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.model.smsreq.BaseSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.ContentSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.SingleSMSReqDTO;
import com.xhqb.spectre.api.service.MemoryDataService;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.dto.ChannelTestData;
import com.xhqb.spectre.common.dal.dto.mq.ApiChannelMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Aspect
@Slf4j
@Component
public class EncodeDataCollectorAspect {

    private static final Map<String, AtomicInteger> SEND_RESULT_MAP = new ConcurrentHashMap<>();
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Lazy
    @Resource
    private EncodeDataCollectorAspect thisProxy;
    @Resource
    private MemoryDataService memoryDataService;

    @Resource
    private ChannelApiProducer channelApiProducer;

    @Resource
    private VenusConfig venusConfig;


    @Around("@annotation(com.xhqb.spectre.api.channel.test.annotation.EncodeDataCollector)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result;
        Object[] args = joinPoint.getArgs();
        result = joinPoint.proceed();
        thisProxy.asyncRequestHandler(args);
        return result;
    }

    @Async("encodeDataCollectorExecutor")
    public void asyncRequestHandler(Object[] args) {
        try {
            if (args.length <= 1) {
                return;
            }
            if (args[1] instanceof BaseSMSReqDTO) {
                BaseSMSReqDTO baseRequest = (BaseSMSReqDTO) args[1];
                if (!(venusConfig.isChannelApiTestEnable() || venusConfig.getChannelApiTestTplCodes().contains(baseRequest.getTplCode()))) {
                    return;
                }
                if (!check(baseRequest)) {
                    return;
                }
                // 本地计数
                preprocessSMSRequest(baseRequest);
            }

        } catch (Exception e) {
            log.warn("渠道测试数据异常", e);
        }

    }

    private boolean check(BaseSMSReqDTO baseRequest) {
        ChannelTestData channelTestData = memoryDataService.getChannelTestData(baseRequest.getTplCode(), baseRequest.getSignCode());
        if (Objects.isNull(channelTestData)) {
            return false;
        }
        if (log.isDebugEnabled()) {
            log.info("channelTestData:{}", JsonLogUtil.toJSONString(channelTestData));
        }
        // 校验数据
        String[] timeSplit = channelTestData.getTimePeriod().split("-");
        DateTime todayStartTime = DateUtil.parse(DateUtil.today() + " " + timeSplit[0]);
        DateTime todayEndTime = DateUtil.parse(DateUtil.today() + " " + timeSplit[1]);
        // cur时间在不在这个中间
        long curTime = System.currentTimeMillis();
        if (curTime < todayStartTime.getTime() || curTime > todayEndTime.getTime()) {
            log.info("当前时间不在发送时间段内");
            return false;
        }
        return true;
    }

    /**
     * 短信发送计数
     *
     * @param baseSMSReq 短信发送内容
     */
    private void preprocessSMSRequest(BaseSMSReqDTO baseSMSReq) {
        int count = 0;
        String key = baseSMSReq.getTplCode() + ":" + baseSMSReq.getSignCode();

        if (baseSMSReq instanceof SingleSMSReqDTO) {
            SingleSMSReqDTO singleSMSReqDTO = (SingleSMSReqDTO) baseSMSReq;
            String[] phoneNumbers = Objects.nonNull(singleSMSReqDTO.getPhoneNumbers()) ?
                    singleSMSReqDTO.getPhoneNumbers().split(",") :
                    new String[0];
            count = phoneNumbers.length;
        } else if (baseSMSReq instanceof ContentSMSReqDTO) {
            ContentSMSReqDTO contentSMSReqDTO = (ContentSMSReqDTO) baseSMSReq;
            count = contentSMSReqDTO.getParamList().size();
        }

        if (count != 0) {
            AtomicInteger atomicInteger = SEND_RESULT_MAP.computeIfAbsent(key, a -> new AtomicInteger(0));
            atomicInteger.addAndGet(count);
        }

        if (log.isDebugEnabled()) {
            log.info("preprocessSMSRequest|resultMap:{}", JsonLogUtil.toJSONString(SEND_RESULT_MAP));
        }

    }

    @Scheduled(cron = "${memory.cache.refresh.cron:0 0/2 * * * ?}")
    public void refreshRedisCache() {
        if (SEND_RESULT_MAP.isEmpty()) {
            return;
        }
        Date endOfDay = DateUtil.endOfDay(new DateTime());
        List<ApiChannelMessage> apiChannelMessageList = new ArrayList<>();
        SEND_RESULT_MAP.forEach((key, value) -> {
            if (value.get() > 0) {
                String redisKey = RedisKeys.ENCODE_DATA_COUNT_KEY + key;
                long count =  stringRedisTemplate.opsForValue().increment(redisKey, value.get());
                ApiChannelMessage apiChannelMessage = buildApiChannelMessage(key, count);
                apiChannelMessageList.add(apiChannelMessage);
                stringRedisTemplate.expireAt(key, endOfDay);
            }
        });

        if(CollectionUtils.isNotEmpty(apiChannelMessageList)){
            channelApiProducer.productMsg(apiChannelMessageList);
        }
        SEND_RESULT_MAP.clear();
    }

    private static ApiChannelMessage buildApiChannelMessage(String key, long count) {
        ApiChannelMessage apiChannelMessage = new ApiChannelMessage();
        String[] split = key.split(":");
        apiChannelMessage.setTplCode(split[0]);
        apiChannelMessage.setSignCode(split[1]);
        apiChannelMessage.setCount(count);
        return apiChannelMessage;
    }
}
