package com.xhqb.spectre.api.service.impl.assembler;


import com.github.wujun234.uid.impl.CachedUidGenerator;
import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.constant.SMSSendFailTypeEnum;
import com.xhqb.spectre.api.constant.SendStatusEnum;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.model.entity.ErrorPhone;
import com.xhqb.spectre.api.model.entity.PhoneIsp;
import com.xhqb.spectre.api.model.entity.SMSMessageMQAssembler;
import com.xhqb.spectre.api.model.smsreq.BaseSMSReqDTO;
import com.xhqb.spectre.api.model.smsreq.SingleSMSReqDTO;
import com.xhqb.spectre.api.model.smsresp.BaseSMSResultVO;
import com.xhqb.spectre.api.model.smsresp.BatchSMSResultVO;
import com.xhqb.spectre.api.service.MemoryDataService;
import com.xhqb.spectre.api.service.SendMessageService;
import com.xhqb.spectre.api.utils.ChannelUtils;
import com.xhqb.spectre.api.utils.CommonUtil;
import com.xhqb.spectre.api.utils.PhoneSearchFastUtil;
import com.xhqb.spectre.api.utils.TemplateUtil;
import com.xhqb.spectre.common.dal.dto.TplData;
import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.common.mq.ChannelCode;
import com.xhqb.spectre.common.mq.MessageMQ;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
public class SingleSMSAssemblerServiceImpl extends AbstractAssemblerSMSService {

    private final Logger LOGGER = LoggerFactory.getLogger(SingleSMSAssemblerServiceImpl.class);

    /**
     * 雪花ID生成器
     */
    @Resource
    private CachedUidGenerator cachedUidGenerator;

    @Resource
    private MemoryDataService memoryDataService;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private VenusConfig venusConfig;

    /**
     * @param baseSMSReqDTO
     * @param baseSMSResultVO
     * @return
     * @Decription: 单条短信发送主逻辑
     */
    @Override
    public Boolean sendSMSTask(BaseSMSReqDTO baseSMSReqDTO, BaseSMSResultVO baseSMSResultVO) {
        SingleSMSReqDTO singleSMSReqDTO = (SingleSMSReqDTO) baseSMSReqDTO;
        List<String> phoneList = singleSMSReqDTO.getPhoneList();

        //失败的号码
        List<ErrorPhone> failPhoneList = new ArrayList<ErrorPhone>();
        //成功下发的号码
        List<String> successPhoneList = new ArrayList<String>();

        if (phoneList.isEmpty() || phoneList.size() == 0) {
            baseSMSResultVO.setSendStatus(SendStatusEnum.INTERFACE_SUCCESS);
            baseSMSResultVO.setSmsSendResult(sendMessageService.buildBatchSMSResultVO(baseSMSReqDTO, baseSMSResultVO, successPhoneList, failPhoneList));
            LOGGER.warn("可用号码数为0 requestDto {},phoneNumbers : {}", singleSMSReqDTO, singleSMSReqDTO.getPhoneNumbers());
//            new SMSSenderException(ResultCode.WRONG_PHONE_NUMBER);
            return Boolean.FALSE;
        }

        // 获取手机号码归属地
        for (String phone : phoneList) {
            try {
                Long phoneStatus = SmsApiApplicationConstant.PHONE_STATUS_CODE;
                PhoneIsp phoneIsp = PhoneSearchFastUtil.getInstance().getIsp(phone);
                if (Objects.isNull(phoneIsp)) {
                    phoneIsp = PhoneIsp.buildPhoneIspDefault(phone);
                }
                TplData tplData = memoryDataService.getTplDataByCode(singleSMSReqDTO.getTplCode(), singleSMSReqDTO.getSignCode());
                if (validParam(tplData, phone, singleSMSReqDTO)) {
                    failPhoneList.add(new ErrorPhone(phone, String.valueOf(SMSSendFailTypeEnum.SMS_SEND_TYPE_SEND_PARAM_NOT_VALID.getStatusCode()), SMSSendFailTypeEnum.SMS_SEND_TYPE_SEND_PARAM_NOT_VALID.getStatusMsg()));
                    LOGGER.warn("短信参数个数有误,phone:{},code:{},paramCount:{},requestDto:{}", phone, tplData.getCode(), tplData.getParamCount(), singleSMSReqDTO);
                    // 缺失短信内容参数
//                    new SMSSenderException(ResultCode.WRONG_SMS_SEND_PARAMS);
                    continue;
                }
                //渠道商
                if (ChannelUtils.isSmsTypeDisabled(tplData, memoryDataService, phoneIsp)) {
                    LOGGER.warn("发送短信MQ失败 命中短信类型禁用规则{}", phone);
                    failPhoneList.add(new ErrorPhone(phone, String.valueOf(SMSSendFailTypeEnum.SMS_SEND_TYPE_SMS_TYPE_DISABLED.getStatusCode()), SMSSendFailTypeEnum.SMS_SEND_TYPE_SMS_TYPE_DISABLED.getStatusMsg()));
//                    new SMSSenderException(ResultCode.SMS_TYPE_DISABLED);
                    continue;
                }
                boolean isTplDisabled = ChannelUtils.isTplDisabled(tplData, phoneIsp);
                if (!isTplDisabled) {
                    List<ChannelCode> channelCodeList = new ArrayList<>();
                    // 判断是否命中渠道禁用规则
                    if (singleSMSReqDTO.getChannelAccountId() != null && singleSMSReqDTO.getChannelAccountId() != 0) {
                        ChannelCode channelCode = new ChannelCode(tplData.getCode(), String.valueOf(singleSMSReqDTO.getChannelAccountId()), null, 10);
                        channelCodeList.add(channelCode);
                    } else {
                        channelCodeList = ChannelUtils.getChannelCode(tplData, memoryDataService, phoneIsp, singleSMSReqDTO.getParamMap(), singleSMSReqDTO.getParamList());
                        if (channelCodeList == null || channelCodeList.size() == 0) {
                            LOGGER.warn("发送短信MQ失败 未获取到渠道信息{}", phone);
                            failPhoneList.add(new ErrorPhone(phone, String.valueOf(SMSSendFailTypeEnum.SMS_SEND_FAILED_NO_AVAIL_PARTNER.getStatusCode()), SMSSendFailTypeEnum.SMS_SEND_FAILED_NO_AVAIL_PARTNER.getStatusMsg()));
//                            new SMSSenderException(ResultCode.CHANNEL_EMPTY);
                            continue;
                        }
                    }
                    // 组装单条短信发送体
                    SMSMessageMQAssembler<BaseBodyMessage> smsMessageMQAssembler = SMSMessageMQAssembler.buildBySingleSMSReqDTO(singleSMSReqDTO, phoneIsp);
                    smsMessageMQAssembler.setOrderId(String.valueOf(cachedUidGenerator.getUID()));
                    smsMessageMQAssembler.setRequestId(singleSMSReqDTO.getRequestId());
                    smsMessageMQAssembler.setChannelCodeSet(channelCodeList);
                    smsMessageMQAssembler.setSignName(tplData.getSignName());
                    smsMessageMQAssembler.setParamMap(null);
                    String content = tplData.getContent();
                    if (singleSMSReqDTO.getParamMap() != null) {
                        content = ChannelUtils.buildMsgContent(singleSMSReqDTO.getParamMap(), tplData.getContent(), phone);
                        // 拼装[*]参数
                        smsMessageMQAssembler.setParamMap(CommonUtil.strToList(singleSMSReqDTO.getParamMap().get(phone)));
                    } else if (singleSMSReqDTO.getParamList() != null) {
                        content = ChannelUtils.buildMsgContent(singleSMSReqDTO.getParamList(), tplData.getContent(), phone);

                        // 拼装${}参数
                        if (venusConfig.isParamMapEnable()) {
                            List<String> param = ChannelUtils.extractPlaceholderValues(singleSMSReqDTO.getParamList(), tplData.getContent(), phone);
                            smsMessageMQAssembler.setParamMap(param);
                        }
                    }
                    if (StringUtils.isEmpty(content)) {
                        LOGGER.warn("发送短信MQ失败 组装短信内容失败{}", phone);
                        failPhoneList.add(new ErrorPhone(phone,
                                String.valueOf(SMSSendFailTypeEnum.SMS_SEND_FAILED_NO_CONTENT.getStatusCode()),
                                SMSSendFailTypeEnum.SMS_SEND_FAILED_NO_AVAIL_PARTNER.getStatusMsg()));
//                        new SMSSenderException(ResultCode.CHANNEL_EMPTY);
                        continue;
                    }
                    smsMessageMQAssembler.setContent(content);
                    smsMessageMQAssembler.setPhoneStatus(phoneStatus);
                    // 构建
                    MessageMQ<BaseBodyMessage> singleMessageMQ = sendMessageService.buildSingleMessageMQ(smsMessageMQAssembler);
                    Boolean sendStatus = sendMessageService.sendMessage(singleMessageMQ, baseSMSReqDTO, baseSMSResultVO);
                    if (sendStatus) {
                        successPhoneList.add(phone);
                    } else {
                        failPhoneList.add(new ErrorPhone(phone, String.valueOf(SMSSendFailTypeEnum.SMS_SEND_TYPE_DISPATCH_FAILED.getStatusCode()), SMSSendFailTypeEnum.SMS_SEND_TYPE_DISPATCH_FAILED.getStatusMsg()));
                    }
                } else {
                    LOGGER.warn("发送短信MQ失败 命中模版地域禁用规则{}", phone);
                    failPhoneList.add(new ErrorPhone(phone, String.valueOf(SMSSendFailTypeEnum.SMS_SEND_FAILED_NO_AVAIL_PARTNER.getStatusCode()), SMSSendFailTypeEnum.SMS_SEND_FAILED_NO_AVAIL_PARTNER.getStatusMsg()));
//                    new SMSSenderException(ResultCode.CHANNEL_EMPTY);
                }
            } catch (Exception e) {
                LOGGER.error("SingleSMSAssemblerServiceImpl sendSMSTask error: {}", e.getMessage());
                e.printStackTrace();
//                new SMSSenderException(ResultCode.SYS_FAILURES);
                return Boolean.FALSE;
            }
        }

        BatchSMSResultVO batchSMSResultVO = sendMessageService.buildBatchSMSResultVO(baseSMSReqDTO, baseSMSResultVO, successPhoneList, failPhoneList);
        if (batchSMSResultVO.getFailure() == 0) {
            baseSMSResultVO.setSendStatus(SendStatusEnum.INTERFACE_DISPATCH_SUCCESS);
        } else if (batchSMSResultVO.getSuccess() != 0) {
            baseSMSResultVO.setSendStatus(SendStatusEnum.INTERFACE_DISPATCH_PARTLY_SUCCESS);
        } else {
            baseSMSResultVO.setSendStatus(SendStatusEnum.INTERFACE_SUCCESS);
        }
        baseSMSResultVO.setSmsSendResult(batchSMSResultVO);

        return Boolean.TRUE;
    }

    /**
     * 判断单个电话号码参数是否异常
     *
     * @param tplData
     * @param phone
     * @param singleSMSReqDTO
     * @return boolean true异常 false正常
     */
    private boolean validParam(TplData tplData, String phone, SingleSMSReqDTO singleSMSReqDTO) {

        // 新校验规则
        if (venusConfig.isNewValidateParamEnable()) {
            return !TemplateUtil.validateTplParams(tplData, phone, singleSMSReqDTO);
        }

        Integer paramCount = tplData.getParamCount();
        if (paramCount != null && paramCount != 0) {
            if (singleSMSReqDTO.getParamMap() == null && singleSMSReqDTO.getParamList() == null) {
                return true;
            } else if (singleSMSReqDTO.getParamMap() != null) {
                if (singleSMSReqDTO.getParamMap().containsKey(phone)) {
                    String[] param = singleSMSReqDTO.getParamMap().get(phone).split(",");
                    if (paramCount > param.length) {
                        // 参数个数不一样
                        return true;
                    }
                    return false;
                } else {
                    return true;
                }
            } else if (singleSMSReqDTO.getParamList() != null) {
                List<Map<String, String>> listParam = singleSMSReqDTO.getParamList().stream().filter(item -> item.get(SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY) != null && item.get(SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY).equalsIgnoreCase(phone)).collect(Collectors.toList());
                if (listParam == null || listParam.size() == 0) {
                    return true;
                }
                Map<String, String> map = listParam.get(0);
                if (map.size() <= 1) {
                    return true;
                }
                // 判断参数相对模板是否完整
                return !TemplateUtil.hasAllPlaceholdersWithPairs(tplData.getContent(), map);
            }
        }
        return false;
    }

}
