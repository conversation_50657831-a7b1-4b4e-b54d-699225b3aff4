package com.xhqb.spectre.api.service.limit;

import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.google.common.util.concurrent.RateLimiter;
import com.xhqb.spectre.api.config.VenusConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * 用户短链创建限流服务
 */
@SuppressWarnings("UnstableApiUsage")
@Component
@Slf4j
public class UserShortUrlLimit {
    private volatile RateLimiter userShortUrlLimiter;
    @Resource
    private VenusConfig venusConfig;

    @PostConstruct
    public void init() {
        ConfigService.getAppConfig().addChangeListener(changeEvent -> {
            ConfigChange change;
            String propertyName;
            for (String key : changeEvent.changedKeys()) {
                change = changeEvent.getChange(key);
                propertyName = change.getPropertyName();
                if (Objects.equals(propertyName, "user.shortUrl.add.limit.value")) {
                    this.initHitLibraryLimit(venusConfig.getNodeNum(), Integer.parseInt(change.getNewValue()));
                }
            }
        });

        this.initHitLibraryLimit(venusConfig.getNodeNum(), venusConfig.getLimitValue());
    }

    public boolean tryAcquire(int msgCount) {
        return !venusConfig.isLimitEnable() || userShortUrlLimiter.tryAcquire(msgCount);
    }


    private void initHitLibraryLimit(Integer nodeNum, Integer limitValue) {
        int num = getNodeLimitValue(nodeNum, limitValue);
        userShortUrlLimiter = RateLimiter.create(num);
        log.info("init user shortUrl add limit|nodeNum:{}|limitValue:{}|qps:{}", nodeNum, limitValue, num);
    }

    public int getNodeLimitValue(Integer nodeNum, Integer limitValue) {
        if (nodeNum == null || nodeNum <= 0) {
            return limitValue;
        }
        int num = limitValue / nodeNum;
        return Math.max(num, 1);
    }
}
