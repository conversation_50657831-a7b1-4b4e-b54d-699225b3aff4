package com.xhqb.spectre.api.model.entity;

import com.xhqb.spectre.api.utils.DateTimeUtil;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.entity.TplDisableDO;
import com.xhqb.spectre.common.dal.entity.support.TplChannelDOSupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryTplVO implements Serializable {
    private Integer id;

    private String code;

    private String smsTypeCode;

    private String title;

    private Integer signId;

    private String content;

    private String appCode;

    private String remark;

    private Integer status;

    private List<TplChannelVO> channelInfoList;

    private List<TplDisableVO> disableInfoList;

    private String createTime;

    private String creator;

    private String updateTime;

    private String updater;

    /**
     * 是否网关账号的模板
     */
    private boolean gatewayTplFlag;

    private String signCode;

    private String signName;

    public static QueryTplVO buildTplVO(TplDO tplDO, boolean isGatewayTpl) {
        return QueryTplVO.builder()
                .id(tplDO.getId())
                .code(tplDO.getCode())
                .smsTypeCode(tplDO.getSmsTypeCode())
                .title(tplDO.getTitle())
                .signId(tplDO.getSignId())
                .content(tplDO.getContent())
                .appCode(tplDO.getAppCode())
                .remark(tplDO.getRemark())
                .status(tplDO.getStatus())
                .createTime(DateTimeUtil.queryDateToStr(tplDO.getCreateTime(), 12))
                .creator(tplDO.getCreator())
                .updateTime(DateTimeUtil.queryDateToStr(tplDO.getUpdateTime(), 12))
                .updater(tplDO.getUpdater())
                .gatewayTplFlag(isGatewayTpl)
                .build();
    }

    public static QueryTplVO buildTplVOSupport(TplDO tplDO, List<TplChannelDOSupport> tplChannelDOList, List<TplDisableDO> tplDisableDOList, boolean isGatewayTpl) {
        QueryTplVO tplVO = buildTplVO(tplDO, isGatewayTpl);
        tplVO.setChannelInfoList(tplChannelDOList.stream().map(TplChannelVO::buildTplChannelVOSupport).collect(Collectors.toList()));
        tplVO.setDisableInfoList(tplDisableDOList.stream().map(TplDisableVO::buildTplDisableVO).collect(Collectors.toList()));
        return tplVO;
    }
}
