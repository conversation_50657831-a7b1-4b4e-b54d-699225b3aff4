package com.xhqb.spectre.api.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 幂等注解： 采用"自定义注解+分布式锁"保证接口幂等
 */
@Inherited
@Documented
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface AutoIdempotent {

    /**
     * key前缀
     */
    String prefix() default "";

    /**
     * 过期秒数,默认为5秒
     */
    int expire() default 5;

    /**
     * 超时时间单位，默认为秒
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * Key的分隔符（默认 :）
     */
    String delimiter() default ":";
}
