package com.xhqb.spectre.api.listener;

import com.xhqb.spectre.api.service.MemoryHandleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 应用启动事件
 * ApplicationReadyEvent
 * ApplicationReadyEvent
 * ApplicationContextInitializedEvent
 * ApplicationEnvironmentPreparedEvent
 * ApplicationFailedEvent
 * ApplicationPreparedEvent
 * ApplicationReadyEvent
 * ApplicationStartedEvent
 * ApplicationStartingEvent
 * SpringApplicationEvent
 */
@Component
public class ApplicationReadyEventListener implements ApplicationListener<ApplicationReadyEvent> {

    private Logger logger = LoggerFactory.getLogger(ApplicationReadyEventListener.class);

    @Resource
    private MemoryHandleService memoryHandleService;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        // 拉取phone地址解析数据
        memoryHandleService.refreshPhoneSearch();

        // 设置更新时间
        memoryHandleService.refreshTime();

        // 开始拉取应用数据到内存
        memoryHandleService.refreshAppInfo();

        // 开始拉取模版数据到内存
        memoryHandleService.refreshTplInfo();

        // 开始拉取渠道禁用数据到内存
        memoryHandleService.refreshChannelAccountDisable();

        // 开始拉取黑名单数据到内存
        memoryHandleService.refreshMobileBlack();

        // 开始拉取白名单数据到内存
        memoryHandleService.refreshMobileWhite();

        // 开始拉取限流配置数据到内存
        memoryHandleService.refreshAppSendLimit();

        // 拉取短信类型屏蔽数据
        memoryHandleService.refreshSmsTypeDisabled();
    }

}
