package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.model.smsreq.TestContentReportDTO;
import com.xhqb.spectre.api.service.TestContentService;
import com.xhqb.spectre.common.dal.entity.test.tool.TestContentTaskRecordDO;
import com.xhqb.spectre.common.dal.mapper.TestContentTaskRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Objects;

@Service
@Slf4j
public class TestContentServiceImpl implements TestContentService {

    @Autowired
    private TestContentTaskRecordMapper testContentTaskRecordMapper;
    @Override
    public void report(TestContentReportDTO testContentReportDTO) {
        TestContentTaskRecordDO taskRecordDO = testContentTaskRecordMapper.selectByContent(testContentReportDTO.getMobile(),
                testContentReportDTO.getContent().trim());
        if (Objects.nonNull(taskRecordDO)) {
            taskRecordDO.setAppReportStatus(testContentReportDTO.getStatus());
            taskRecordDO.setAppReportTime((int)Instant.now().getEpochSecond());
            taskRecordDO.setAppReportExtend(testContentReportDTO.toJson());
            log.info("更新测试内容报告, taskRecordDO={}", taskRecordDO);
            testContentTaskRecordMapper.updateByPrimaryKeySelective(taskRecordDO);
        }
    }
}
