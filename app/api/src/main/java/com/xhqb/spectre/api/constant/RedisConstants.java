package com.xhqb.spectre.api.constant;

public interface RedisConstants {

    String NOTIFY_TEMPLATE_ID = "smsv2:notify:templateId";

    String VERIFY_TEMPLATE_ID = "smsv2:verify:templateId";

    String TEMPLATE_ALL_ENTRY = "smsv2:template:allEntry";

    String SIGNATURE_ALL_ENTRY = "smsv2:signature:allEntry";

    String MOBILE_BLACK_PREFIX = "smsv2:mobileblack:";

    String BATCH_MESSAGE_LIST = "smsv2:batchMessage:list";

    String MOBILE_BELONG_GROUP = "smsv2:beglonggroup:";

    String MOBILE_WHITE_PREFIX = "smsv2:mobilewhite:";
}
