package com.xhqb.spectre.api.service.impl;

import com.alibaba.fastjson.JSON;
import com.xhqb.kael.sequencegenerator.DistributedSequence;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.constant.*;
import com.xhqb.spectre.api.controller.common.ResultCode;
import com.xhqb.spectre.api.exception.SMSSenderException;
import com.xhqb.spectre.api.model.entity.SMSSendFailedRecord;
import com.xhqb.spectre.api.model.smsreq.*;
import com.xhqb.spectre.api.model.smsresp.BaseSMSResultVO;
import com.xhqb.spectre.api.model.smsresp.BatchSMSResultVO;
import com.xhqb.spectre.api.model.smsresp.VerifyCodeResultVO;
import com.xhqb.spectre.api.service.*;
import com.xhqb.spectre.api.utils.*;
import com.xhqb.spectre.common.dal.dto.AppData;
import com.xhqb.spectre.common.dal.dto.AppSendLimitData;
import com.xhqb.spectre.common.dal.dto.TplData;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import io.prometheus.client.Collector;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 短信发送主处理逻辑
 */
@Service
public class SMSMessageManagerServiceImpl extends BaseService implements SMSMessageManagerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SMSMessageManagerServiceImpl.class);

    @Resource
    private FrequencyLimitService frequencyLimitService;

    /**
     * ID唯一号生成器
     */
    @Resource
    private DistributedSequence smsRequestIDSeqService;

    @Resource
    private SMSSendServiceLoader smsSendServiceLoader;

    @Resource
    private AssemblerVerifyService assemblerVerifyService;

    @Resource
    private MemoryDataService memoryDataService;

    @Value("${disabledCheckSendLimit}")
    private String disabledCheckSendLimit;

    @Resource
    private VenusConfig venusConfig;
    @Resource
    private DebtSmsService debtSmsService;


    @Override
    public BaseSMSResultVO dealSingleSms(SingleSMSReqDTO singleSMSReqDTO) {
        //新建短信接口对象
        BaseSMSResultVO baseSMSResultVO = new BaseSMSResultVO();
        BatchSMSResultVO batchSMSResultVO = new BatchSMSResultVO();

        //发送失败的记录数
        List<SMSSendFailedRecord> smsSendFailedRecordList = new ArrayList<SMSSendFailedRecord>();
        //过滤后的待发送号码组
        List<String> phoneList = new ArrayList<>();

        // 检查短信基础参数
        checkBaseRequest(singleSMSReqDTO);
        String[] phones = singleSMSReqDTO.getPhoneNumbers().split(",");

        TplData tplData = checkTplData(singleSMSReqDTO.getTplCode(), singleSMSReqDTO.getParamMap(), false, singleSMSReqDTO.getSignCode(), singleSMSReqDTO.getParamList());

        // 针对短信后台群发非营销类短信跳过黑名单检测的功能(2024-09-11)
        boolean skipBlackCheck = isSkipBlackCheck(singleSMSReqDTO, tplData);
        // 黑名单
        List<String> blackList = new ArrayList<>();
        Map<String, List<String>> blackResult = checkBlackBatch(phones,
                tplData.getSmsTypeCode(), singleSMSReqDTO.getAppCode(), skipBlackCheck, tplData.getCode(), tplData.getTag());
        if (blackResult != null && blackResult.get("black") != null && blackResult.get("black").size() > 0) {
            // 部分命中黑名单
            blackList = blackResult.get("black");
        }

        for (String phoneNumber : phones) {
            if (!PhoneNumberUtils.isXhMobileNum(phoneNumber)) {
                //检查是否为有效号码
                smsSendFailedRecordList.add(AssemberMessageObjectUtils.buildSMSSendFailedRecord(singleSMSReqDTO.getTplCode(), singleSMSReqDTO.getAppCode(), phoneNumber, SMSSendFailTypeEnum.SMS_SEND_TYPE_PHONE_NOT_VALID));
            } else if (blackList.contains(phoneNumber)) {
                smsSendFailedRecordList.add(AssemberMessageObjectUtils.buildSMSSendFailedRecord(singleSMSReqDTO.getTplCode(), singleSMSReqDTO.getAppCode(), phoneNumber, SMSSendFailTypeEnum.SMS_SEND_TYPE_BLACK_PHONE));
            } else if (LimitCheckResultEnum.APP_SEND_LIMIT_PASS != checkWhitePhoneAndSendLimit(singleSMSReqDTO.getAppCode(), singleSMSReqDTO.getTplCode(), phoneNumber, tplData.getSmsTypeCode())) {
                //检查白名单或触发频率限制的号码
                smsSendFailedRecordList.add(AssemberMessageObjectUtils.buildSMSSendFailedRecord(singleSMSReqDTO.getTplCode(), singleSMSReqDTO.getAppCode(), phoneNumber, SMSSendFailTypeEnum.SMS_SEND_TYPE_SEND_LIMIT_BLOCKED));
            } else {
                //有效发送号码列表
                phoneList.add(phoneNumber);
            }
        }

        //根据模版消息类型，设置消息体类型
        MessageTypeEnum messageTypeEnum = MessageTypeEnum.getByMessageType(tplData.getSmsTypeCode());
        if (messageTypeEnum == null) {
            singleSMSReqDTO.setMessageTypeEnum(MessageTypeEnum.UNKNOWN);
        } else {
            singleSMSReqDTO.setMessageTypeEnum(messageTypeEnum);
        }

        // 设置有效号码组
        singleSMSReqDTO.setPhoneList(phoneList);

        //构建短信发送记录
        if (StringUtils.isEmpty(singleSMSReqDTO.getRequestId())) {
            String requestId = CmppMsgIdUtils.getMsgId();
            singleSMSReqDTO.setRequestId(requestId);
            baseSMSResultVO.setRequestId(requestId);
        } else {
            baseSMSResultVO.setRequestId(singleSMSReqDTO.getRequestId());
        }
        batchSMSResultVO.setFailureSMSRecord(smsSendFailedRecordList);
        baseSMSResultVO.setSmsSendResult(batchSMSResultVO);

        // 债转短信数据收集(2024-10-25)
        debtSmsService.debtSmsProcess(phones.length, phoneList.size(), singleSMSReqDTO.getTplCode(), singleSMSReqDTO.getBizBatchId());

        AssemblerSMSService iSendSMSService = smsSendServiceLoader.loadEndpoint(singleSMSReqDTO);
        if (null == iSendSMSService) {
            baseSMSResultVO.setSendStatus(SendStatusEnum.INTERFACE_FAILED);
            SMSSendFailedRecord smsSendFailedRecord = AssemberMessageObjectUtils.buildSMSSendFailedRecord(singleSMSReqDTO.getTplCode(), singleSMSReqDTO.getAppCode(), singleSMSReqDTO.getPhoneNumbers(), SMSSendFailTypeEnum.SMS_SEND_TYPE_NOT_SUPPORT);
            smsSendFailedRecordList.clear();
            smsSendFailedRecordList.add(smsSendFailedRecord);
            batchSMSResultVO.setFailureSMSRecord(smsSendFailedRecordList);
            baseSMSResultVO.setSmsSendResult(batchSMSResultVO);
            //减去频率计数
            for (String phoneNumber : singleSMSReqDTO.getPhoneNumbers().split(",")) {
                subSMSFrequencyLimitCount(singleSMSReqDTO.getAppCode(), phoneNumber, singleSMSReqDTO.getTplCode(), tplData.getSmsTypeCode());
            }
            return baseSMSResultVO;
        }
        baseSMSResultVO.setTplCode(singleSMSReqDTO.getTplCode());
        Boolean sendResult = iSendSMSService.sendSMSTask(singleSMSReqDTO, baseSMSResultVO);
        // 发送记录
        if (!sendResult) {
            // 日志记录
            LOGGER.warn("baseSMSResultVO {} sms send failed.", baseSMSResultVO);
        }
        return baseSMSResultVO;
    }

    /**
     * 验证短信模版
     *
     * @param tplCode  模版code
     * @param params   参数
     * @param isVerify 是否验证码类型
     * @param signCode 签名code
     * @return
     */
    private TplData checkTplData(String tplCode, Map<String, String> params, boolean isVerify, String signCode, List<Map<String, String>> paramList) {
        // 检查短信模版是否有效
        TplData tplData = memoryDataService.getTplDataByCode(tplCode, signCode);
        if (null == tplData) {
            LOGGER.warn("无效的模版CODE tplCode:{} signCode:{}", tplCode, signCode);
            throw new SMSSenderException(ResultCode.WRONG_SMS_TEMPLATE);
        }
        if (isVerify) {
            // 判断模版是否为验证码
            if (!tplData.getSmsTypeCode().equalsIgnoreCase(MessageTypeEnum.VERIFY.getMessageType())) {
                LOGGER.warn("短信模版不是验证码类型 tplCode: {}", tplCode);
                throw new SMSSenderException(ResultCode.SMS_TYPE_CODE_ERROR_VERIFY);
            }
            if (tplData.getParamCount() != null && tplData.getParamCount() != 1) {
                LOGGER.warn("验证码模版短信参数个数有误");
                throw new SMSSenderException(ResultCode.WRONG_SMS_SEND_PARAMS);
            }
        } else {
            if (tplData.getParamCount() != null && tplData.getParamCount() != 0) {
                if ((params == null || params.isEmpty()) && (paramList == null)) {
                    LOGGER.warn("模版参数不能为空");
                    throw new SMSSenderException(ResultCode.DYNAMIC_PARAM_NOT_VALID);
                }
            }
        }
        return tplData;
    }

    /**
     * 跳过债转短信发送限制
     *
     * @param tplCode
     * @return
     */
    private boolean skipSendLimitForDebtSms(String tplCode) {
        try {
            return venusConfig.isDebtSmsTplCode(tplCode);
        } catch (Exception e) {
            LOGGER.warn("跳过债转短信验证处理失败,tplCode ={}", tplCode, e);
        }
        return false;
    }

    // 白名单 + 频率检查
    private LimitCheckResultEnum checkWhitePhoneAndSendLimit(String appCode, String tplCode, String phone, String smsCode) {
        // 取消限流
        if ("true".equalsIgnoreCase(disabledCheckSendLimit)) {
            return LimitCheckResultEnum.APP_SEND_LIMIT_PASS;
        }

        if (skipSendLimitForDebtSms(tplCode)) {
            if (venusConfig.isDebug()) {
                LOGGER.info("当前模板为债转短信,不进行短信发送频率限制, tplCode = {}", tplCode);
            }
            return LimitCheckResultEnum.APP_SEND_LIMIT_PASS;
        }

        long start = System.nanoTime();
        // 判断是否白名单 如果是白名单就不检查频率限制
        if (memoryDataService == null || memoryDataService.isWhite(appCode, SmsApiApplicationConstant.WHITEBOX_APP_SEND_LIMIT, phone)) {
            LOGGER.info("sms send for whitelist: {}", phone);
            return LimitCheckResultEnum.APP_SEND_LIMIT_PASS;
        }
        // 发送频率检查 没有配置就使用系统默认配置
        List<AppSendLimitData> appSendLimitList = memoryDataService.getSendLimit(appCode);
        if (appSendLimitList == null || appSendLimitList.isEmpty()) {
            appSendLimitList = buildDefaultSendLimit(appCode, smsCode);
        }
        for (AppSendLimitData appSendLimit : appSendLimitList) {
            // 频率检查（各指标频率检查）
            try {
                LimitCheckResultEnum resultCode = frequencyLimitService.checkSendLimitFrequencyFrom(tplCode, appCode, appSendLimit.getLimitKey(), phone, smsCode);
                if (resultCode != LimitCheckResultEnum.APP_SEND_LIMIT_PASS) {
                    return resultCode;
                }
            } catch (Exception e) {
                LOGGER.error("Frequency Check Exception: {}", e.getMessage());
            }
        }
        LuciferConstant.SEND_LIMIT_TIME.labels(smsCode)
                .observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
        return LimitCheckResultEnum.APP_SEND_LIMIT_PASS;
    }

    /**
     * 构建默认限流数据
     *
     * @param appCode
     * @return
     */
    private List<AppSendLimitData> buildDefaultSendLimit(String appCode, String smsCode) {
        List<AppSendLimitData> appSendLimitList = new ArrayList<>();
        if (smsCode.equalsIgnoreCase(MessageTypeEnum.VERIFY.getMessageType())) {
            AppSendLimitData verifyLimit = new AppSendLimitData(appCode, CfgTypeLimitCheckEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_VERIFY.getCfgType(), String.valueOf(LimitConstants.DEFAULT_MOBILE_MAX_VERIFY));
            appSendLimitList.add(verifyLimit);
        }
        AppSendLimitData halfM = new AppSendLimitData(appCode, CfgTypeLimitCheckEnum.APP_SEND_LIMIT_MOBILE_MAZX_COUNT_HALF_MINUTE.getCfgType(), String.valueOf(LimitConstants.DEFAULT_MOBILE_MAX_HALF_MINUTE));
        appSendLimitList.add(halfM);

        AppSendLimitData hour = new AppSendLimitData(appCode, CfgTypeLimitCheckEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_HOUR.getCfgType(), String.valueOf(LimitConstants.DEFAULT_MOBILE_MAX_HOUR));
        appSendLimitList.add(hour);

        AppSendLimitData day = new AppSendLimitData(appCode, CfgTypeLimitCheckEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_DAY.getCfgType(), String.valueOf(LimitConstants.DEFAULT_MOBILE_MAX_DAY));
        appSendLimitList.add(day);

        AppSendLimitData sameDay = new AppSendLimitData(appCode, CfgTypeLimitCheckEnum.APP_SEND_LIMIT_MOBILE_SAME_CONTENT_MAX_CYCLE.getCfgType(), String.valueOf(LimitConstants.DEFAULT_MOBILE_SAME_MAX_CYCLE));
        appSendLimitList.add(sameDay);

        return appSendLimitList;
    }

    /**
     * 是否跳过黑名单检测
     * <p>
     * 主要针对短信后台群非营销类短信提供跳过黑名单检测的功能,主要是解决重庆大诚短信被黑名单拦截的情况
     * <p>
     * 当短信后台群发跳过检测黑名单出现问题时,可以通过apollo设置spectre.api.skipBlackCheckEnable=false,关闭跳过黑名单检测的功能
     * <p>
     * 新增债转短信跳过黑名单逻辑(2024-10-25)
     *
     * @param smsReqDTO
     * @param tplData
     * @return
     */
    private boolean isSkipBlackCheck(BaseSMSReqDTO smsReqDTO, TplData tplData) {
        String tplCode = smsReqDTO.getTplCode();
        if (venusConfig.isDebtSmsTplCode(tplCode)) {
            if (venusConfig.isDebug()) {
                LOGGER.info("当前为债转短信,不进行黑名单校验, tplCode = {}", tplCode);
            }
            return true;
        }

        if (!venusConfig.isSkipBlackCheckEnable()) {
            // 检测开关关闭 表示所有类型的短信都不会跳过黑名单检测
            // 打开的情况下,才对短信后台群发非营销类短信跳过黑名单检测的功能生效
            return false;
        }

        Integer batchId = smsReqDTO.getBatchId();
        if (Objects.isNull(batchId) || Objects.equals(batchId, 0)) {
            // 当batchId为空,或者为0的情况下, 不是短信后台群发 不跳过黑名单检测
            return false;
        }

        String smsTypeCode = tplData.getSmsTypeCode();
        if (MessageTypeEnum.isMarket(smsTypeCode)) {
            // 当前为群发短信, 并且为营销类短信, 不跳过黑名单检测
            return false;
        }

        // 非营销类的群发短信 跳过黑名单检测
        if (venusConfig.isDebug()) {
            LOGGER.info("非营销类的群发短信,跳过黑名单检测已生效,tplData ={}", JSON.toJSONString(tplData));
        }
        return true;
    }

    /**
     * 判断多个手机号码是否命中黑名单
     *
     * @param phoneList
     * @param smsCode 短信类型
     * @param appKey         应用code
     * @param skipBlackCheck 是否跳过黑名单检查 (当前为后台群发过来的请求,非营销类短信不进行黑名单过滤,解决群发重庆大诚黑名单拦截过滤的情况)
     */
    private Map<String, List<String>> checkBlackBatch(String[] phoneList,
                                                      String smsCode,
                                                      String appKey,
                                                      boolean skipBlackCheck,
                                                      String smsTplCode,
                                                      Integer tag) {
        if (phoneList == null || phoneList.length == 0) {
            return null;
        }
        Map<String, List<String>> result = new HashMap<>();
        List<String> black = new ArrayList<>();
        List<String> white = new ArrayList<>();

        for (String phone : phoneList) {
            boolean flag = memoryDataService.isBlack(phone, smsCode, appKey);

            // flag未命中黑名单 & 原始smsCode不是营销类短信 & (是否营销类通知模板 or 是营销标签)
            if (!flag
                    && !Objects.equals(smsCode, MessageTypeEnum.MARKET.getMessageType())
                    && (isTplInMarketBlacklist(smsTplCode) || Objects.equals(tag, 1))) {
                    flag = memoryDataService.isBlack(phone, MessageTypeEnum.MARKET.getMessageType(), appKey);
                    LOGGER.info("尝试匹配营销黑名单|flag:{}|smsCode:{}|smsTplCode:{}|phone:{}", flag, smsCode, smsTplCode, phone);
            }

            if (flag && !skipBlackCheck) {
                // 只有短信后台群发非营销类短信才会跳过黑名单检测,解决重庆大诚群发短信被黑名单拦截的情况(2024-09-11)
                black.add(phone);
                LOGGER.info("命中黑名单|smsCode:{}|smsTplCode:{}|phone:{}", smsCode, smsTplCode, phone);
                LuciferConstant.BLACK_TOTAL.labels(appKey, smsCode).inc();
            } else {
                white.add(phone);
            }
        }
        if (white.size() == 0) {
            LOGGER.warn("手机号码全部命中黑名单");
            throw new SMSSenderException(ResultCode.BLACK_PHONE_VALID);
        }
        result.put("black", black);
        result.put("white", white);
        return result;
    }

    /**
     * 模板编码是否在营销类名单中
     * @param smsTplCode 模板编码
     * @return true 表示在名单中, false表示不在名单中
     */
    private boolean isTplInMarketBlacklist(String smsTplCode) {
        return Optional.ofNullable(smsTplCode)
                .filter(StringUtils::isNotBlank)
                .flatMap(code -> Optional.ofNullable(venusConfig.getNoticeTplFilterMarketBlackList())
                        .map(list -> list.contains(code)))
                .orElse(false);
    }

    /**
     * 是否命中黑名单
     *
     * @param phone   电话号码
     * @param smsCode 发送类型
     */
    private void checkBlack(String phone, String smsCode, String appKey) {
        if (StringUtils.isEmpty(phone) || StringUtils.isEmpty(smsCode)) {
            return;
        }
        boolean flag = memoryDataService.isBlack(phone, smsCode, appKey);
        if (flag) {
            LuciferConstant.BLACK_TOTAL.labels(appKey, smsCode).inc();
            LOGGER.warn("手机号码命中黑名单");
            throw new SMSSenderException(ResultCode.BLACK_PHONE_VALID);
        }
    }

    /**
     * 处理失败时减去限流计数
     *
     * @param appCode
     * @param phone
     * @param tlpCode
     * @return
     */
    private boolean subSMSFrequencyLimitCount(String appCode, String phone, String tlpCode, String smsCode) {
        // 判断是否白名单
        if (memoryDataService == null || memoryDataService.isWhite(appCode, SmsApiApplicationConstant.WHITEBOX_APP_SEND_LIMIT, phone)) {
            LOGGER.info("sms send for whitelist: {}", phone);
            return true;
        }
        // 发送频率检查 没有配置就直接过
        List<AppSendLimitData> appSendLimitList = memoryDataService.getSendLimit(appCode);
        if (appSendLimitList == null || appSendLimitList.isEmpty()) {
            appSendLimitList = buildDefaultSendLimit(appCode, smsCode);
        }
        //减去限制逻辑计数
        for (AppSendLimitData appSendLimit : appSendLimitList) {
            try {
                frequencyLimitService.subtractSMSFrequencyLimitCount(tlpCode, appCode, appSendLimit.getLimitKey(), phone, smsCode);
            } catch (Exception e) {
                LOGGER.error("Frequency Limit Count Increase Exception: {}", e.getMessage());
            }
        }
        return true;
    }

    /**
     * 发送验证码
     *
     * @param verifyCodeSMSReqDTO
     * @return
     */
    @Override
    public VerifyCodeResultVO dealVerifyCodeSms(VerifyCodeSMSReqDTO verifyCodeSMSReqDTO) {
        VerifyCodeResultVO verifyCodeResultVO = new VerifyCodeResultVO();

        // 检查短信基础参数
        checkBaseRequest(verifyCodeSMSReqDTO);
        checkVerifyCodeRequest(verifyCodeSMSReqDTO);

        // 检查验证码模版是否有效
        TplData tplData = checkTplData(verifyCodeSMSReqDTO.getTplCode(), null, true, verifyCodeSMSReqDTO.getSignCode(), null);

        // 黑名单
        checkBlack(verifyCodeSMSReqDTO.getPhoneNumbers(), tplData.getSmsTypeCode(), verifyCodeSMSReqDTO.getAppCode());

        //白名单过滤、频率限制处理
        if (LimitCheckResultEnum.APP_SEND_LIMIT_PASS != checkWhitePhoneAndSendLimit(verifyCodeSMSReqDTO.getAppCode(), verifyCodeSMSReqDTO.getTplCode(), verifyCodeSMSReqDTO.getPhoneNumbers(), tplData.getSmsTypeCode()
        )) {
            LOGGER.warn("短信请求触发发送频率限制");
            throw new SMSSenderException(ResultCode.SEND_LIMIT_BLOCKED);
        }

        if (StringUtils.isEmpty(verifyCodeSMSReqDTO.getSmsVerifyCode())) {
            if (null == verifyCodeSMSReqDTO.getCodeLen()) {
                verifyCodeSMSReqDTO.setSmsVerifyCode(GenerateVerifyCodeUtils.getDigitalVerifyCode(SmsApiApplicationConstant.SMS_VERIFY_CODE_LEN));
            } else {
                verifyCodeSMSReqDTO.setSmsVerifyCode(GenerateVerifyCodeUtils.getDigitalVerifyCode(verifyCodeSMSReqDTO.getCodeLen()));
            }
        }

        //根据模版消息类型，设置消息体类型
        MessageTypeEnum messageTypeEnum = MessageTypeEnum.getByMessageType(tplData.getSmsTypeCode());
        if (messageTypeEnum == null) {
            verifyCodeSMSReqDTO.setMessageTypeEnum(MessageTypeEnum.UNKNOWN);
        } else {
            verifyCodeSMSReqDTO.setMessageTypeEnum(messageTypeEnum);
        }

        // 配置短信身份码
//        String identificationCode = smsRequestIDSeqService.nextStr(SmsApiApplicationConstant.SMSAPI_IDENTIFICATION_CODE_SEQ);
//        verifyCodeSMSReqDTO.setIdentificationCode(identificationCode);

        // 构建短信发送记录
        if (StringUtils.isEmpty(verifyCodeSMSReqDTO.getRequestId())) {
            String requestId = CmppMsgIdUtils.getMsgId();
            verifyCodeResultVO.setRequestId(requestId);
            verifyCodeSMSReqDTO.setRequestId(requestId);
        } else {
            verifyCodeResultVO.setRequestId(verifyCodeSMSReqDTO.getRequestId());
        }
        verifyCodeResultVO.setTplCode(verifyCodeSMSReqDTO.getTplCode());
//        verifyCodeResultVO.setIdentificationCode(identificationCode);

        ResultCode sendResult = assemblerVerifyService.sendVerifyTask(verifyCodeSMSReqDTO, verifyCodeResultVO);
        verifyCodeResultVO.setSendCode(String.valueOf(sendResult.getResultCode()));
        verifyCodeResultVO.setSendMsg(sendResult.getResultMsg());
        //发送返回组装、入库及维护频率指标
        if (sendResult.getResultCode() != 200) {
            //减去频率计数
            subSMSFrequencyLimitCount(verifyCodeSMSReqDTO.getAppCode(), verifyCodeSMSReqDTO.getPhoneNumbers(), verifyCodeSMSReqDTO.getTplCode(), tplData.getSmsTypeCode());
        }
        return verifyCodeResultVO;
    }

    /**
     * 按内容发送短信
     *
     * @param contentSMSReqDTO
     * @return
     */
    @Override
    public BaseSMSResultVO dealContentSms(ContentSMSReqDTO contentSMSReqDTO) {
        //新建短信接口对象
        BaseSMSResultVO baseSMSResultVO = new BaseSMSResultVO();
        BatchSMSResultVO batchSMSResultVO = new BatchSMSResultVO();

        //发送失败的记录数
        List<SMSSendFailedRecord> smsSendFailedRecordList = new ArrayList<SMSSendFailedRecord>();
        //过滤后的待发送号码组
        List<String> phoneListEnable = new ArrayList<>();

        // 检查基础参数
        checkContentRequest(contentSMSReqDTO);
        AppData appData = getAppData(contentSMSReqDTO.getAppCode());
        String tplCode = SmsApiApplicationConstant.PREFIX_SMS_CONTENT_TPL_CODE + contentSMSReqDTO.getAppCode() + "_" + contentSMSReqDTO.getSmsType() + SmsApiApplicationConstant.SUFFIX_SMS_CONTENT_TPL_CODE;
        TplData tplData = getTplData(tplCode, contentSMSReqDTO.getSignCode(), contentSMSReqDTO.getSmsType(), appData.getContentApiType(), contentSMSReqDTO.getContent());
        List<String> phoneLists = getPhoneLists(contentSMSReqDTO.getParamList());

        // 针对短信后台群发非营销类短信跳过黑名单检测的功能(2024-09-11)
        boolean skipBlackCheck = isSkipBlackCheck(contentSMSReqDTO, tplData);
        // 黑名单
        List<String> blackList = new ArrayList<>();
        Map<String, List<String>> blackResult = checkBlackBatch(phoneLists.toArray(new String[0]),
                tplData.getSmsTypeCode(), contentSMSReqDTO.getAppCode(), skipBlackCheck, tplData.getCode(), tplData.getTag());
        if (blackResult != null && blackResult.get("black") != null && blackResult.get("black").size() > 0) {
            // 部分命中黑名单
            blackList = blackResult.get("black");
        }

        for (String phoneNumber : phoneLists) {
            if (!PhoneNumberUtils.isXhMobileNum(phoneNumber)) {
                //检查是否为有效号码
                smsSendFailedRecordList.add(AssemberMessageObjectUtils.buildSMSSendFailedRecord(tplData.getCode(), contentSMSReqDTO.getAppCode(), phoneNumber, SMSSendFailTypeEnum.SMS_SEND_TYPE_PHONE_NOT_VALID));
            } else if (blackList.contains(phoneNumber)) {
                smsSendFailedRecordList.add(AssemberMessageObjectUtils.buildSMSSendFailedRecord(tplData.getCode(), contentSMSReqDTO.getAppCode(), phoneNumber, SMSSendFailTypeEnum.SMS_SEND_TYPE_BLACK_PHONE));
            } else if (LimitCheckResultEnum.APP_SEND_LIMIT_PASS != checkWhitePhoneAndSendLimit(contentSMSReqDTO.getAppCode(), tplData.getCode(), phoneNumber, tplData.getSmsTypeCode())) {
                //检查白名单或触发频率限制的号码
                smsSendFailedRecordList.add(AssemberMessageObjectUtils.buildSMSSendFailedRecord(tplData.getCode(), contentSMSReqDTO.getAppCode(), phoneNumber, SMSSendFailTypeEnum.SMS_SEND_TYPE_SEND_LIMIT_BLOCKED));
            } else {
                //有效发送号码列表
                phoneListEnable.add(phoneNumber);
            }
        }

        //根据模版消息类型，设置消息体类型
        MessageTypeEnum messageTypeEnum = MessageTypeEnum.getByMessageType(tplData.getSmsTypeCode());
        if (messageTypeEnum == null) {
            contentSMSReqDTO.setMessageTypeEnum(MessageTypeEnum.UNKNOWN);
        } else {
            contentSMSReqDTO.setMessageTypeEnum(messageTypeEnum);
        }

        //构建短信发送记录
        if (StringUtils.isEmpty(contentSMSReqDTO.getRequestId())) {
            String requestId = CmppMsgIdUtils.getMsgId();
            contentSMSReqDTO.setRequestId(requestId);
            baseSMSResultVO.setRequestId(requestId);
        } else {
            baseSMSResultVO.setRequestId(contentSMSReqDTO.getRequestId());
        }
        batchSMSResultVO.setFailureSMSRecord(smsSendFailedRecordList);
        baseSMSResultVO.setSmsSendResult(batchSMSResultVO);

        AssemblerSMSService iSendSMSService = smsSendServiceLoader.loadEndpoint(contentSMSReqDTO);
        if (null == iSendSMSService) {
            baseSMSResultVO.setSendStatus(SendStatusEnum.INTERFACE_FAILED);
            SMSSendFailedRecord smsSendFailedRecord = AssemberMessageObjectUtils.buildSMSSendFailedRecord(tplData.getCode(), contentSMSReqDTO.getAppCode(), contentSMSReqDTO.getPhoneNumbers(), SMSSendFailTypeEnum.SMS_SEND_TYPE_NOT_SUPPORT);
            smsSendFailedRecordList.clear();
            smsSendFailedRecordList.add(smsSendFailedRecord);
            batchSMSResultVO.setFailureSMSRecord(smsSendFailedRecordList);
            baseSMSResultVO.setSmsSendResult(batchSMSResultVO);
            //减去频率计数
            for (String phoneNumber : phoneLists) {
                if (!blackList.contains(phoneNumber)) {
                    subSMSFrequencyLimitCount(contentSMSReqDTO.getAppCode(), phoneNumber, tplData.getCode(), tplData.getSmsTypeCode());
                }
            }
            return baseSMSResultVO;
        }
        // 设置有效号码组
        contentSMSReqDTO.setPhoneList(phoneListEnable);
        contentSMSReqDTO.setTplCode(tplData.getCode());
        baseSMSResultVO.setTplCode(tplData.getCode());
        Boolean sendResult = iSendSMSService.sendSMSTask(contentSMSReqDTO, baseSMSResultVO);
        // 发送记录
        if (!sendResult) {
            // 日志记录
            LOGGER.warn("baseSMSResultVO {} sms send failed.", baseSMSResultVO.toString());
        }
        return baseSMSResultVO;
    }

    /**
     * 发送测试短信
     *
     * @param singleTestSMSReqDTO
     * @return
     */

    @Override
    public BaseSMSResultVO dealTestSingleSms(SingleTestSMSReqDTO singleTestSMSReqDTO) {
        //新建短信接口对象
        BaseSMSResultVO baseSMSResultVO = new BaseSMSResultVO();
        BatchSMSResultVO batchSMSResultVO = new BatchSMSResultVO();
        // 检查短信基础参数
        checkBaseRequest(singleTestSMSReqDTO);
        TplData tplData = checkTplData(singleTestSMSReqDTO.getTplCode(), singleTestSMSReqDTO.getParamMap(), false, singleTestSMSReqDTO.getSignCode(), singleTestSMSReqDTO.getParamList());
        LOGGER.info("短信模板信息:{}", JsonLogUtil.toJSONString(tplData));
        //根据模版消息类型，设置消息体类型
        MessageTypeEnum messageTypeEnum = MessageTypeEnum.getByMessageType(tplData.getSmsTypeCode());
        if (messageTypeEnum == null) {
            singleTestSMSReqDTO.setMessageTypeEnum(MessageTypeEnum.UNKNOWN);
        } else {
            singleTestSMSReqDTO.setMessageTypeEnum(messageTypeEnum);
        }
        // 构建短信发送记录
        if (StringUtils.isEmpty(singleTestSMSReqDTO.getRequestId())) {
            String requestId = CmppMsgIdUtils.getMsgId();
            singleTestSMSReqDTO.setRequestId(requestId);
            baseSMSResultVO.setRequestId(requestId);
        } else {
            baseSMSResultVO.setRequestId(singleTestSMSReqDTO.getRequestId());
        }
        LOGGER.info("短信请求信息:{}", JsonLogUtil.toJSONString(singleTestSMSReqDTO));
        List<SMSSendFailedRecord> smsSendFailedRecordList = new ArrayList<>();
        AssemblerSMSService iSendSMSService = smsSendServiceLoader.loadEndpoint(singleTestSMSReqDTO);
        if (null == iSendSMSService) {
            baseSMSResultVO.setSendStatus(SendStatusEnum.INTERFACE_FAILED);
            SMSSendFailedRecord smsSendFailedRecord = AssemberMessageObjectUtils.buildSMSSendFailedRecord(singleTestSMSReqDTO.getTplCode(), singleTestSMSReqDTO.getAppCode(), singleTestSMSReqDTO.getPhoneNumbers(), SMSSendFailTypeEnum.SMS_SEND_TYPE_NOT_SUPPORT);
            smsSendFailedRecordList.add(smsSendFailedRecord);
            batchSMSResultVO.setFailureSMSRecord(smsSendFailedRecordList);
            baseSMSResultVO.setSmsSendResult(batchSMSResultVO);
            return baseSMSResultVO;
        }
        baseSMSResultVO.setTplCode(singleTestSMSReqDTO.getTplCode());
        Boolean sendResult = iSendSMSService.sendSMSTask(singleTestSMSReqDTO, baseSMSResultVO);
        // 发送记录
        if (!sendResult) {
            // 日志记录
            LOGGER.warn("baseSMSResultVO {} sms send failed.", baseSMSResultVO);
        }
        return baseSMSResultVO;
    }

    /**
     * 获取参数中的手机号码
     *
     * @param params
     * @return
     */
    private List<String> getPhoneLists(List<Map<String, String>> params) {
        List<String> phoneLists = params.stream().map(item -> item.get(SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY)).collect(Collectors.toList());
        if (phoneLists.size() != params.size()) {
            LOGGER.warn("参数中缺少手机号码");
            throw new SMSSenderException(ResultCode.MISS_MSG_PARAM_PHONES);
        }
        if (phoneLists.size() == 0) {
            LOGGER.warn("参数中缺少手机号码");
            throw new SMSSenderException(ResultCode.MISS_MSG_PHONES);
        }
        return phoneLists;
    }

    /**
     * 验证并获取模版数据
     *
     * @param tplCode
     * @param signCode
     * @param smsType
     * @return
     */
    private TplData getTplData(String tplCode, String signCode, String smsType, Integer checkTpl, String content) {
        TplData tplData = memoryDataService.getTplDataByCode(tplCode, signCode);
        if (tplData == null) {
            LOGGER.warn("无效的模版CODE tplCode:{} signCode:{} smsType: {}", tplCode, signCode, smsType);
            throw new SMSSenderException(ResultCode.WRONG_SMS_TEMPLATE);
        }
        if (!tplData.getSmsTypeCode().equalsIgnoreCase(smsType)) {
            LOGGER.warn("模版中的短信类型和传递参数的短信类型不一致");
            throw new SMSSenderException(ResultCode.SMS_TYPE_CODE_ERROR);
        }
        if (SmsApiApplicationConstant.SUPPORT_CONTENT_API_FLAG1.equals(checkTpl)) {
            // 验证短信内容
            if (!validContent(content, tplData.getContent())) {
                LOGGER.warn("短信内容未进行备案: {}", content);
                throw new SMSSenderException(ResultCode.TPL_CONTENT_ERROR);
            }
        }
        return tplData;
    }

    /**
     * 验证短信内容
     *
     * @param requestContent 请求参数中的内容
     * @param tplContent     模版中的内容
     * @return true验证通过 false验证失败
     */
    private boolean validContent(String requestContent, String tplContent) {
        if (!requestContent.contains(SmsApiApplicationConstant.TPL_PARAM_SYMBOL) && !SmsApiApplicationConstant.TPL_PARAM_SYMBOL_PATTERN2.matcher(requestContent).find()) {
            // 内容不包含参数
            Pattern newTplContent = replaceParam(tplContent);
            return newTplContent.matcher(requestContent).matches();
        }
        Pattern newRContent = replaceParam(requestContent);
        Pattern newTplContent = replaceParam(tplContent);
        if (newRContent.toString().equalsIgnoreCase(newTplContent.toString())) {
            return true;
        }
        return false;
    }

    /**
     * 替换参数
     *
     * @param content
     * @return
     */
    private Pattern replaceParam(String content) {
        content = content.replaceAll(SmsApiApplicationConstant.TPL_PARAM_SYMBOL_REGEX, SmsApiApplicationConstant.TPL_PARAM_SYMBOL_REPLACE);
        content = content.replaceAll(SmsApiApplicationConstant.TPL_CONTENT_PARAM2, SmsApiApplicationConstant.TPL_PARAM_SYMBOL_REPLACE);
        String patternStr = "^" + content + "$";
        return Pattern.compile(patternStr);
    }

    /**
     * 获取应用信息
     *
     * @param appCode
     * @return
     */
    private AppData getAppData(String appCode) {
        AppData appData = memoryDataService.getAppInfoByKey(appCode);
        if (appData == null) {
            LOGGER.warn("appKey数据不存在");
            throw new SMSSenderException(ResultCode.ILLEDGE_KEY);
        }
        if (appData.getContentApiType() == SmsApiApplicationConstant.SUPPORT_CONTENT_API_FLAG0) {
            LOGGER.warn("应用不支持短信内容api");
            throw new SMSSenderException(ResultCode.SUPPORT_CONTENT_API_FLAG_FALSE);
        }
        return appData;
    }

    /**
     * 校验按短信内容发送参数
     *
     * @param req
     */
    private void checkContentRequest(ContentSMSReqDTO req) {
        if (req == null) {
            LOGGER.warn("请求参数不能为空");
            throw new SMSSenderException(ResultCode.MISS_REQUEST_PARAM);
        }
        if (StringUtils.isBlank(req.getSmsType())) {
            LOGGER.warn("smsType不能为空");
            throw new SMSSenderException(ResultCode.MISS_MESSAGE_TYPE);
        }
        if (StringUtils.isBlank(req.getContent())) {
            LOGGER.warn("短信内容不能为空");
            throw new SMSSenderException(ResultCode.MISS_MESSAGE_CONTENT);
        }
        List<Map<String, String>> params = req.getParamList();
        if (CollectionUtils.isNotEmpty(params) && params.size() > SmsApiApplicationConstant.ALLOW_SUBMIT_MAX_CONTENT_PHONE) {
            LOGGER.warn("发送号码超出最大允许提交值");
            throw new SMSSenderException(ResultCode.EXCEED_MAX_PHONE_NUMBERS);
        }
        if (req.getSendTime() != null) {
            Long sendTime = Long.valueOf(req.getSendTime());
            LOGGER.info("延时发送 {}", req);
            // 最大可设置的延时时间
            Date forward = DateTimeUtil.mergeForwardHours(venusConfig.getMqMaxDelayTimeHours());
            if (sendTime > forward.getTime()) {
                String msg = String.format("延时发送设置的时间大于当前时间%s小时|phoneNumbers:%s", venusConfig.getMqMaxDelayTimeHours(), req.getPhoneNumbers());
                LOGGER.warn(msg);
                throw new SMSSenderException(ResultCode.SEND_TIME_VALID);
            }
        }
        if (StringUtils.isBlank(req.getSignCode())) {
            LOGGER.warn("短信签名不能为空");
            throw new SMSSenderException(ResultCode.MISS_MESSAGE_SIGN);
        }

    }
}
