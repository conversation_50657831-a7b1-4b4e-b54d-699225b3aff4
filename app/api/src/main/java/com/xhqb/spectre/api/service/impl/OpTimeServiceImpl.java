package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.service.OpTimeService;
import com.xhqb.spectre.common.dal.entity.OpTimeDO;
import com.xhqb.spectre.common.dal.mapper.OpTimeMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class OpTimeServiceImpl implements OpTimeService {

    @Resource
    private OpTimeMapper opTimeMapper;


    @Override
    public List<OpTimeDO> findAllOptime() {
        return opTimeMapper.selectAll();
    }

    @Override
    public OpTimeDO findByModule(Integer module) {
        return opTimeMapper.selectByModule(module);
    }
}
