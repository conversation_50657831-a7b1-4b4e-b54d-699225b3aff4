package com.xhqb.spectre.api.service.impl;

import com.xhqb.kael.sequencegenerator.DistributedSequence;
import com.xhqb.kael.util.StringUtils;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.controller.common.ResultCode;
import com.xhqb.spectre.api.exception.SMSSenderException;
import com.xhqb.spectre.api.model.smsreq.VerifyCodeCheckReqDTO;
import com.xhqb.spectre.api.model.smsresp.VerifyCodeCheckResultVO;
import com.xhqb.spectre.api.service.CheckVerifyCodeService;
import com.xhqb.spectre.common.enums.VerifyCodeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CheckVerifyCodeServiceImpl implements CheckVerifyCodeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CheckVerifyCodeServiceImpl.class);

    @Resource
    private RedisTemplate<String, String> redisVerifyCodeTemplate;

    @Resource
    private DistributedSequence smsRequestIDSeqService;

    /***
     * 验证比对服务接口
     *      RequestID + 号码 + 身份码
     *      code：验证码
     * @param verifyCodeCheckReq
     * @return
     */
    @Override
    public VerifyCodeCheckResultVO checkSMSVerifyCode(VerifyCodeCheckReqDTO verifyCodeCheckReq) {
        VerifyCodeCheckResultVO verifyCodeCheckResultVO = new VerifyCodeCheckResultVO();
        checkBaseRequest(verifyCodeCheckReq);

        String verifyCodeKey = SmsApiApplicationConstant.VERIFY_CODE_PREFIX + verifyCodeCheckReq.getAppCode() + verifyCodeCheckReq.getPhoneNumber();
        String realCode = redisVerifyCodeTemplate.opsForValue().get(verifyCodeKey);

        //预防空指针异常
        if (smsRequestIDSeqService != null) {
            String requestID = smsRequestIDSeqService.nextStr((SmsApiApplicationConstant.SMSAPI_REQUEST_ID_SEQ));
            if (requestID != null) {
                verifyCodeCheckResultVO.setRequestId(requestID);
            }
        }

        // 验证码过期处理逻辑
        if ((realCode == null) || (StringUtils.isEmpty(realCode))) {
            verifyCodeCheckResultVO.setResultFlag(Boolean.FALSE);
            verifyCodeCheckResultVO.setResultCode(VerifyCodeEnum.VERIFY_CODE_NOT_VALID.getCode());
            verifyCodeCheckResultVO.setResultMsg(VerifyCodeEnum.VERIFY_CODE_NOT_VALID.getDescription());
            return verifyCodeCheckResultVO;
        }

        LOGGER.info("RedisVerifyCode: {}, smsVerifyCode: {}", realCode, verifyCodeCheckReq.getSmsVerifyCode());
        //验证码比对逻辑
        if ((realCode.equals(verifyCodeCheckReq.getSmsVerifyCode()))) {
            verifyCodeCheckResultVO.setResultFlag(Boolean.TRUE);
            verifyCodeCheckResultVO.setResultCode(VerifyCodeEnum.VERIFY_CODE_SUCCESS.getCode());
            verifyCodeCheckResultVO.setResultMsg(VerifyCodeEnum.VERIFY_CODE_SUCCESS.getDescription());
        } else {
            verifyCodeCheckResultVO.setResultFlag(Boolean.FALSE);
            verifyCodeCheckResultVO.setResultCode(VerifyCodeEnum.VERIFY_CODE_FAILED.getCode());
            verifyCodeCheckResultVO.setResultMsg(VerifyCodeEnum.VERIFY_CODE_FAILED.getDescription());
        }

        return verifyCodeCheckResultVO;
    }

    /**
     * 校验入参
     *
     * @param req
     * @return
     */
    private void checkBaseRequest(VerifyCodeCheckReqDTO req) throws SMSSenderException {
        if (req == null) {
            LOGGER.warn("request不能为空");
            throw new SMSSenderException(ResultCode.MISS_REQUEST_PARAM);
        }
        if (StringUtils.isEmpty(req.getRequestId())) {
            LOGGER.warn("requestId不能为空");
            throw new SMSSenderException(ResultCode.MISS_REQUEST_PARAM);
        }
        if (StringUtils.isEmpty(req.getPhoneNumber())) {
            LOGGER.warn("发送号码不能为空");
            throw new SMSSenderException(ResultCode.MISS_MSG_PHONES);
        }
        if (StringUtils.isEmpty(req.getSmsVerifyCode())) {
            LOGGER.warn("校验验证码不能为空");
            throw new SMSSenderException(ResultCode.WRONG_VERIFY_CODE);
        }
        if (req.getPhoneNumber().split(",").length > 1) {
            LOGGER.warn("发送号码超出最大允许提交值");
            throw new SMSSenderException(ResultCode.EXCEED_MAX_PHONE_NUMBERS);
        }
    }
}
