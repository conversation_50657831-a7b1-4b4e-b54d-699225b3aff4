package com.xhqb.spectre.api.constant;

/**
 * 发送结果状态类型
 */
public enum SendStatusEnum {

    INTERFACE_FAILED(3100, "接口调用失败"),

    INTERFACE_SUCCESS(3102, "接口调用成功、短信派发失败"),

    INTERFACE_DISPATCH_SUCCESS(3103, "短信派发成功"),

    INTERFACE_DISPATCH_PARTLY_SUCCESS(3104, "短信派发部分成功");

    private Integer statusCode;

    private String statusMsg;

    private SendStatusEnum(Integer statusCode, String statusMsg) {
        this.statusCode = statusCode;
        this.statusMsg = statusMsg;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    private void setStatusCode(Integer code) {
        this.statusCode = code;
    }


    public String getStatusMsg() {
        return statusMsg;
    }


    private void setStatusMsg(String message) {
        this.statusMsg = message;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code
     * @return
     */
    public static SendStatusEnum getByCode(Integer code) {
        SendStatusEnum[] arr = values();
        int length = arr.length;

        for (int i = 0; i < length; ++i) {
            SendStatusEnum statusEnum = arr[i];
            if (statusEnum.getStatusCode().equals(code)) {
                return statusEnum;
            }
        }

        return null;
    }

}
