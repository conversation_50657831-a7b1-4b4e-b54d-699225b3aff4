package com.xhqb.spectre.api.controller;

import com.xhqb.spectre.api.controller.common.CommonResult;
import com.xhqb.spectre.api.model.smsreq.TestContentReportDTO;
import com.xhqb.spectre.api.service.TestContentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/openapi/spectre/v3/test-content")
@Slf4j
public class TestContentController {

    @ExceptionHandler({HttpMessageNotReadableException.class, MissingServletRequestParameterException.class})
    public CommonResult<String> handleMissingRequestBody() {
        return CommonResult.failed("参数错误");
    }

    @Autowired
    private TestContentService testContentService;
    @PostMapping("/report")
    public CommonResult<String> report(@Valid @RequestBody TestContentReportDTO testContentReportDTO) {
        log.info("收到测试上报数据, testReportDTO={}", testContentReportDTO);
        testContentService.report(testContentReportDTO);
        return CommonResult.success("");
    }
}
