package com.xhqb.spectre.api.cache.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Expiry;
import com.xhqb.spectre.api.cache.MobileBlacklistCache;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 本地黑名单缓存
 */
@Slf4j
public class LocalMobileBlacklistCache implements MobileBlacklistCache {

    private final Cache<String, Long> cache;

    private Integer lastRefreshTime = 0;

    public LocalMobileBlacklistCache() {
        this.cache = Caffeine.newBuilder()
                .expireAfter(new Expiry<String, Long>() {
                    @Override
                    public long expireAfterCreate(@NotNull String key,
                                                  @NotNull Long expirationTimestamp,
                                                  long currentTime) {
                        return computeTTL(expirationTimestamp);
                    }
                    @Override
                    public long expireAfterUpdate(@NotNull String key,
                                                  @NotNull Long expirationTimestamp,
                                                  long currentTime,
                                                  long currentDuration) {
                        return computeTTL(expirationTimestamp);
                    }
                    @Override
                    public long expireAfterRead(@NotNull String key,
                                                @NotNull Long expirationTimestamp,
                                                long currentTime,
                                                long currentDuration) {
                        return currentDuration;
                    }

                    private long computeTTL(Long expirationTimestamp) {
                        if (Objects.isNull(expirationTimestamp)) {
                            return 0;
                        }

                        long ttl = expirationTimestamp - System.currentTimeMillis();
                        return ttl > 0 ? TimeUnit.MILLISECONDS.toNanos(ttl) : 0;
                    }
                })
                .maximumSize(10_000_000)
                .build();
    }

    private String buildKey(String smsTypeCode, String mobile, String appCode) {
        if(StringUtils.isEmpty(appCode)) {
            return smsTypeCode + "_" + mobile;
        }
        return smsTypeCode.toLowerCase() + "_" + mobile + "_" + appCode.toLowerCase().trim();
    }

    @Override
    public boolean isBlacklisted(String smsTypeCode, String mobile, String appCode) {

        if (StringUtils.isEmpty(smsTypeCode) || StringUtils.isEmpty(mobile)) {
            return Boolean.FALSE;
        }

        if (cache.getIfPresent(buildKey(smsTypeCode, mobile, "")) != null) {
            return Boolean.TRUE;
        }

        if (!StringUtils.isEmpty(appCode) && (cache.getIfPresent(buildKey(smsTypeCode, mobile, appCode)) != null)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    public void put(String smsTypeCode, String mobile, String appCode, long expireAt) {
        cache.put(buildKey(smsTypeCode, mobile, appCode), expireAt);
    }

    @Override
    public void remove(String smsTypeCode, String mobile, String appCode) {
        cache.invalidate(buildKey(smsTypeCode, mobile, appCode));
    }

    @Override
    public Integer getLastRefreshTime() {
        return lastRefreshTime;
    }

    @Override
    public void updateLastRefreshTime(Integer time) {
        this.lastRefreshTime = time;
    }

    @Override
    public boolean isAfterLastRefresh(Integer time) {
        if (Objects.isNull(time)) {
            return false;
        }
        return Objects.compare(time, lastRefreshTime, Integer::compare) > 0;
    }
}
