package com.xhqb.spectre.api.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;


@AllArgsConstructor
@NoArgsConstructor
@ToString
@Data
public class ShortenDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 源URL
     */
    @NotBlank(message = "源URL不能为空")
    @Size(max = 512, message = "源URL最大为{max}个字符")
    @URL(message = "源URL格式有误")
    private String srcUrl;

    /**
     * 有效期。1：90天；2：180天；3：365天；4：永久有效
     */
    @Range(min = 1, max = 4, message = "不合法有效期,请输入{min}-{max}之间的整数")
    @NotNull(message = "有效期不能为空")
    private Integer validPeriod;

}
