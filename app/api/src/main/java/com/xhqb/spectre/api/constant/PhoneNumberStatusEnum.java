package com.xhqb.spectre.api.constant;

/**
 * 发送结果状态类型
 */
public enum PhoneNumberStatusEnum {

    NOT_QUERIED(-1L, "NOT_QUERIED","未查询"),
    NORMAL(1L, "NORMAL", "正常"),
    SHUTDOWN(2L, "SHUTDOWN", "停机"),
    POWER_OFF(3L, "POWER_OFF", "关机"),
    NOT_EXIST(4L, "NOT_EXIST", "空号"),
    SUSPECTED_POWER_OFF(5L, "SUSPECTED_POWER_OFF", "疑似关机"),
    BUSY(6L, "BUSY", "忙"),
    UNKNOWN(99L, "UNKNOWN", "未知"),
    ;


    private Long code;

    private String status;

    private String desc;

    PhoneNumberStatusEnum(Long code, String status, String desc) {
        this.code = code;
        this.status = status;
        this.desc = desc;
    }

    public Long getCode() {
        return code;
    }

    private void setCode(Long code) {
        this.code = code;
    }


    public String getStatus() {
        return status;
    }


    private void setStatus(String message) {
        this.status = message;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code
     * @return
     */
    public static PhoneNumberStatusEnum getByCode(Long code) {
        for (PhoneNumberStatusEnum phoneNumberStatusEnum : values()) {
            if (phoneNumberStatusEnum.getCode().equals(code)) {
                return phoneNumberStatusEnum;
            }
        }
        return null;
    }

    public static PhoneNumberStatusEnum getByStatus(String status) {
        for (PhoneNumberStatusEnum phoneNumberStatusEnum : values()) {
            if (phoneNumberStatusEnum.getStatus().equals(status)) {
                return phoneNumberStatusEnum;
            }
        }
        return PhoneNumberStatusEnum.UNKNOWN;
    }

}
