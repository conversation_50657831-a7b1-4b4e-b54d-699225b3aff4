package com.xhqb.spectre.api.model.entity;

import com.xhqb.spectre.api.utils.DateTimeUtil;
import com.xhqb.spectre.common.dal.entity.TplDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TplInfoVO implements Serializable {

    private static final long serialVersionUID = 3390217985545624727L;
    
    private Integer id;

    private String code;

    private String smsTypeCode;

    private String title;

    private Integer signId;

    private String content;

    private String appCode;

    private Integer status;

    private String remark;

    private String creator;

    private String createTime;

    private String updater;

    private String updateTime;

    private Integer isDelete;

    private String signName;

    private String signCode;

    public static TplInfoVO buildTplInfoVO(TplDO tplDO, String signName, String signCode) {
        return TplInfoVO.builder()
                .id(tplDO.getId())
                .code(tplDO.getCode())
                .smsTypeCode(tplDO.getSmsTypeCode())
                .title(tplDO.getTitle())
                .signId(tplDO.getSignId())
                .content(tplDO.getContent())
                .appCode(tplDO.getAppCode())
                .status(tplDO.getStatus())
                .remark(tplDO.getRemark())
                .createTime(DateTimeUtil.queryDateToStr(tplDO.getCreateTime(), 12))
                .creator(tplDO.getCreator())
                .updateTime(DateTimeUtil.queryDateToStr(tplDO.getUpdateTime(), 12))
                .updater(tplDO.getUpdater())
                .isDelete(tplDO.getIsDelete())
                .signName(signName)
                .signCode(signCode)
                .build();
    }
}
