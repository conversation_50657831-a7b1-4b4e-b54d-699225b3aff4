package com.xhqb.spectre.api.service;

import com.xhqb.spectre.api.model.entity.MobileStatus;
import com.xhqb.spectre.common.dal.dto.*;

import java.util.List;
import java.util.Map;

public interface RedisService {

    AppData getAppInfoByKey(String appKey);

    TplData getTplDatagetTplByCode(String code);

    List<ChannelAccountDisableData> getChannelDisabledData(Integer accountId);

    boolean isBlack(String phone, String smsCode);

    boolean isWhite(String appCode, String cfgType, String phone);

    List<AppSendLimitData> getSendLimit(String appCode);

    /**
     * 批量保存数据到redis
     *
     * @param map
     */
    void batchSetRedis(Map<String, String> map);

    /**
     * 批量从redis中获取数据
     *
     * @param keys
     * @return
     */
    List<MobileStatus> batchGetRedis(List<String> keys);

    /**
     * 批量保存数据到redis
     *
     * @param list
     */
    void batchSetRedisPipeLined(List<MobileStatus> list);

    /**
     * 批量从redis中获取数据
     *
     * @param keys
     * @return
     */
    List<MobileStatus> batchGetRedisPipeLined(List<String> keys);

}
