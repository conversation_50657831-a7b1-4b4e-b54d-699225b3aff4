package com.xhqb.spectre.api.model.smsreq;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class MobileBlackDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 黑名单来源
     */
    @NotBlank(message = "黑名单来源不能为空")
    private String source;

    /**
     * 用户CID
     */
    @Size(max = 32, message = "CID最大为{max}个字符")
    private String cid;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    /**
     * 短信类型编码列表
     */
    @NotEmpty(message = "短信类型不能为空")
    private List<String> smsTypeCodeList;

    /**
     * 拉黑原因
     */
    @NotBlank(message = "拉黑原因不能为空")
    @Size(max = 256, message = "拉黑原因最大为{max}个字符")
    private String description;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 操作人
     */
    private String operator;

    /**
     *  过期时间
     */
    @NotBlank(message = "过期时间不能为空")
    private String expiredTime;

}
