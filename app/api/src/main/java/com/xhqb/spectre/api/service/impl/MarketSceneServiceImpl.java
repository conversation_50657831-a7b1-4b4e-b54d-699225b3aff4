package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.model.entity.MarketSceneVO;
import com.xhqb.spectre.api.service.MarketSceneService;
import com.xhqb.spectre.common.dal.entity.MarketSceneDO;
import com.xhqb.spectre.common.dal.mapper.MarketSceneMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@Slf4j
public class MarketSceneServiceImpl implements MarketSceneService {

    @Resource
    private MarketSceneMapper marketSceneMapper;

    /**
     * 查询所有的营销场景信息
     *
     * @param status 状态，0：无效，1：有效 , 为空查所有
     * @return
     */
    @Override
    public List<MarketSceneVO> listAll(Integer status) {
        List<MarketSceneDO> marketSceneList = marketSceneMapper.listAll(status);
        if (Objects.isNull(marketSceneList) || marketSceneList.isEmpty()) {
            return Collections.emptyList();
        }
        return marketSceneList.stream().map(MarketSceneVO::buildMarketSceneVO).collect(Collectors.toList());
    }
}
