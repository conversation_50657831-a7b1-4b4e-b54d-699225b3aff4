package com.xhqb.spectre.api.service.dispatcher;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.api.constant.LuciferConstant;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.common.mq.MessageMQ;
import io.prometheus.client.Collector;
import org.apache.pulsar.client.api.MessageId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@Component
public class DispatchSMSSendRecord2Q {
    private static final Logger logger = LoggerFactory.getLogger(DispatchSMSSendRecord2Q.class);

    @Resource
    private MQTemplate<String> mqTemplate;


    @Value("#{'${kael.mq.producers:}'.split(',')[3]}")
    private String collectSMSMessage;

    /**
     * 市场
     */
    @Value("#{'${kael.mq.producers:}'.split(',')[2]}")
    private String marketSMSMessage;

    /**
     * 通知
     */
    @Value("#{'${kael.mq.producers:}'.split(',')[0]}")
    private String notifySMSMessage;

    /**
     * 验证码
     */
    @Value("#{'${kael.mq.producers:}'.split(',')[1]}")
    private String verifySMSMessage;

    /**
     * @param marketMessageMQ
     * @return
     * @Description: 发送市场短信
     */
    public Boolean dispatchMarketSMSMessage2Q(MessageMQ<BaseBodyMessage> marketMessageMQ,Long sendTime) {
        Boolean dispatchFlag = dispatchSMSMessage2QAsync(marketSMSMessage, marketMessageMQ,sendTime);
        if (dispatchFlag != null) {
            return dispatchFlag;
        }
        return false;
    }

    /**
     * @param collectMessageMQ
     * @return
     * @Description: 发送催收短信
     */
    public Boolean dispatchCollectSMSMessage2Q(MessageMQ<BaseBodyMessage> collectMessageMQ,Long sendTime) {
        Boolean dispatchFlag = dispatchSMSMessage2QAsync(collectSMSMessage, collectMessageMQ,sendTime);
        if (dispatchFlag != null) {
            return dispatchFlag;
        }
        return false;
    }

    /**
     * @param notifyMessageMQ
     * @return
     * @Description: 发送通知短信
     */
    public Boolean dispatchNotifySMSMessage2Q(MessageMQ<BaseBodyMessage> notifyMessageMQ,Long sendTime) {
        Boolean dispatchFlag = dispatchSMSMessage2QAsync(notifySMSMessage, notifyMessageMQ, sendTime);
        if (dispatchFlag != null) {
            return dispatchFlag;
        }
        return false;
    }

    /**
     * @param verifyBodyMessage
     * @return
     * @Description: 发送验证码短信
     */
    public Boolean dispatchVerifySMSMessage2Q(MessageMQ<BaseBodyMessage> verifyBodyMessage) {
        Boolean dispatchFlag = dispatchSMSMessage2QAsync(verifySMSMessage, verifyBodyMessage, null);
        if (dispatchFlag != null) {
            return dispatchFlag;
        }
        return false;
    }

    private Boolean dispatchSMSMessage2Q(String messageMQTopic, MessageMQ<BaseBodyMessage> bodyMessage) {
        Integer retryNum = 1;
        while (retryNum <= SmsApiApplicationConstant.TRY_TIMES) {
            try {
                MessageId mesID = mqTemplate.send(messageMQTopic, JSONObject.toJSONString(bodyMessage));
//                logger.info("sms send object {}, MsgID: {}", bodyMessage, mesID);
                retryNum++;
                return Boolean.TRUE;
            } catch (Exception e) {
                retryNum++;
                logger.error("message send to mq exception：{}", e.getMessage());
                try {
                    TimeUnit.SECONDS.sleep(SmsApiApplicationConstant.INTERVAL_TIME);
                } catch (InterruptedException interruptedException) {
                    // logger.error("message send to mq exception：{}", interruptedException.getMessage());
                    Thread.currentThread().interrupt();
                }
                // continue;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 异步发送
     *
     * @param messageMQTopic
     * @param bodyMessage
     * @return
     */
    private Boolean dispatchSMSMessage2QAsync(String messageMQTopic, MessageMQ<BaseBodyMessage> bodyMessage,Long sendTime) {
        long start = System.nanoTime();
        LuciferConstant.MQ_SEND_COUNTER.labels(bodyMessage.getSmsCode()).inc();
        if (sendTime != null){
            // 延时发送
            mqTemplate.createMessage(messageMQTopic, JSONObject.toJSONString(bodyMessage))
                    .deliverAfter(sendTime,TimeUnit.MILLISECONDS)
                    .sendAsync().thenAccept(msgId -> {
                        LuciferConstant.MQ_SEND_TIME.labels(bodyMessage.getSmsCode())
                                .observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
//                        logger.info("sms send object Delayed {}, MsgID: {}", bodyMessage, msgId);
                    })
                    .exceptionally((Function<Throwable, Void>) e -> {
                        logger.error(e.getMessage(), e);
                        return null;
                    });
        }else{
            // 立即发送
            mqTemplate.sendAsync(messageMQTopic, JSONObject.toJSONString(bodyMessage)).thenAccept(msgId -> {
                        LuciferConstant.MQ_SEND_TIME.labels(bodyMessage.getSmsCode())
                                .observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
//                        logger.info("sms send object {}, MsgID: {}", bodyMessage, msgId);
                    })
                    .exceptionally((Function<Throwable, Void>) e -> {
                        logger.error(e.getMessage(), e);
                        return null;
                    });
        }

        return Boolean.TRUE;
    }

}
