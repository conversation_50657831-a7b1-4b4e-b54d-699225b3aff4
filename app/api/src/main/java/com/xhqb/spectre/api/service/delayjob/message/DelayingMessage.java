package com.xhqb.spectre.api.service.delayjob.message;

import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DelayingMessage<T> implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 延时消息id
     */
    String id;

    /**
     * 延时时间 被消费时间,取当前时间戳+延迟时间
     */
    private Long delayTime;

    /**
     *  是否批量发送
     */
    // private Boolean batchFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private T customMessage;

}
