package com.xhqb.spectre.api.service;

import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.common.mq.MessageMQ;

public interface SMSSenderService {

    Boolean sendMarketSMSMessage(MessageMQ<BaseBodyMessage> marketBodyMessageMQ, Boolean batchFlag);

    Boolean sendNotifySMSMessage(MessageMQ<BaseBodyMessage> notifyBodyMessageMQ, Boolean batchFlag);

    Boolean sendCollectSMSMessage(MessageMQ<BaseBodyMessage> collectMessageMQ, Boolean batchFlag);

    Boolean sendVerifySMSMessage(MessageMQ<BaseBodyMessage> verifyBodyMessageMQ);

}
