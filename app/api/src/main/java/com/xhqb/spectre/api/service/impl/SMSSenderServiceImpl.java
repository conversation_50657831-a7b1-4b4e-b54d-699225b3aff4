package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.service.SMSSenderService;
import com.xhqb.spectre.api.service.delayjob.producer.DelayingSMSProducer;
import com.xhqb.spectre.api.service.dispatcher.DispatchSMSSendRecord2Q;
import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.common.mq.MessageMQ;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class SMSSenderServiceImpl implements SMSSenderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SMSSenderServiceImpl.class);

    @Resource
    DispatchSMSSendRecord2Q dispatchSMSRecord2Q;

    @Resource
    DelayingSMSProducer delayingSMSProducer;

    @Override
    public Boolean sendMarketSMSMessage(MessageMQ<BaseBodyMessage> marketBodyMessageMQ, Boolean batchFlag) {
        Boolean sendResult = Boolean.FALSE;
        Long jobSendTime = calculateDelayedTime(marketBodyMessageMQ.getSendTime());
        if (jobSendTime == 0L) {
            //立即发送，投递到消息队列
            sendResult = dispatchSMSRecord2Q.dispatchMarketSMSMessage2Q(marketBodyMessageMQ, null);
        } else {
            sendResult = dispatchSMSRecord2Q.dispatchMarketSMSMessage2Q(marketBodyMessageMQ, jobSendTime);
        }
        return sendResult;
    }

    @Override
    public Boolean sendNotifySMSMessage(MessageMQ<BaseBodyMessage> notifyBodyMessageMQ, Boolean batchFlag) {
        Boolean sendResult = Boolean.FALSE;
        Long jobSendTime = calculateDelayedTime(notifyBodyMessageMQ.getSendTime());
        //单条发送逻辑
        if (jobSendTime == 0L) {
            //立即发送，投递到消息队列
            sendResult = dispatchSMSRecord2Q.dispatchNotifySMSMessage2Q(notifyBodyMessageMQ, null);
        } else {
            //延时发送
            sendResult = dispatchSMSRecord2Q.dispatchNotifySMSMessage2Q(notifyBodyMessageMQ, jobSendTime);
        }
        return sendResult;
    }

    @Override
    public Boolean sendCollectSMSMessage(MessageMQ<BaseBodyMessage> collectMessageMQ, Boolean batchFlag) {
        Boolean sendResult = Boolean.FALSE;
        Long jobSendTime = calculateDelayedTime(collectMessageMQ.getSendTime());
        if (jobSendTime == 0L) {
            //立即发送，投递到消息队列
            sendResult = dispatchSMSRecord2Q.dispatchCollectSMSMessage2Q(collectMessageMQ, null);
        } else {
            //延时发送
            sendResult = dispatchSMSRecord2Q.dispatchCollectSMSMessage2Q(collectMessageMQ, jobSendTime);
        }
        return sendResult;
    }

    @Override
    public Boolean sendVerifySMSMessage(MessageMQ<BaseBodyMessage> verifyBodyMessageMQ) {
        return dispatchSMSRecord2Q.dispatchVerifySMSMessage2Q(verifyBodyMessageMQ);
    }


    /**
     * @param sendTime 时间戳
     * @return
     */
    private static Long calculateDelayedTime(String sendTime) {
        if (sendTime == null) {
            return 0L;
        }
        Long delayTs = Long.valueOf(sendTime);
        Long currentTs = System.currentTimeMillis();
        Long sendDelayTime = delayTs - currentTs;
        LOGGER.info("SMS send Time: {}, Current Time: {}, Delayed Time: {}", delayTs, currentTs, sendDelayTime);
        if (sendDelayTime > 0L) {
            return sendDelayTime;
        }
        return 0L;
    }
}
