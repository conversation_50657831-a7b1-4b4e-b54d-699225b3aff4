package com.xhqb.spectre.api.model.entity;

import com.xhqb.spectre.api.utils.CommonUtil;
import com.xhqb.spectre.api.utils.DateTimeUtil;
import com.xhqb.spectre.common.dal.entity.TplDisableDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplDisableVO implements Serializable {
    private static final long serialVersionUID = -2051297494803520283L;

    private List<String> ispList;

    private List<AreaVO> areaList;

    private String startTime;

    private String endTime;

    public static TplDisableVO buildTplDisableVO(TplDisableDO item) {
        return TplDisableVO.builder()
                .ispList(CommonUtil.strToList(item.getIsps()))
                .areaList(CommonUtil.areaToList(item.getAreas()))
                .startTime(DateTimeUtil.intToString(item.getStartTime()))
                .endTime(DateTimeUtil.intToString(item.getEndTime()))
                .build();
    }

}
