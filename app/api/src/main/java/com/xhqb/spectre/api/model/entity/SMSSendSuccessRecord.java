package com.xhqb.spectre.api.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SMSSendSuccessRecord implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 投递时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;
}
