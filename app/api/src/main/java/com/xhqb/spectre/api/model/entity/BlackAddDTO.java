package com.xhqb.spectre.api.model.entity;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class BlackAddDTO {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    /**
     * 黑名单来源：
     * crm 新增场景 默认:客户主动上诉 customer_appeal
     */
    private String source;

    /**
     * 用户cid
     */
    private String cid;

    /**
     * 短信类型（market,collector,notify）
     * crm 新增场景 type=collector
     */
    @NotBlank(message = "短信类型不能为空")
    private String type;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;

    /**
     * 拉黑原因
     */
    @NotBlank(message = "拉黑原因不能为空")
    private String blackReason;

    /**
     * 过期时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String expirationTime;

}
