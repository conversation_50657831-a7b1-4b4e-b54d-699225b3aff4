package com.xhqb.spectre.api.controller;

import com.xhqb.spectre.api.config.VenusConfig;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.controller.common.CommonResult;
import com.xhqb.spectre.api.model.entity.QueryOrderVO;
import com.xhqb.spectre.api.model.smsreq.QueryOrderRequestDTO;
import com.xhqb.spectre.api.service.SmsQueryService;
import com.xhqb.spectre.api.utils.AesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 短信open api相关接口
 *
 * <AUTHOR>
 * @date 2024/05/22
 */
@RestController
@RequestMapping("/openapi/spectre/v3")
@Slf4j
public class SpectreOpenApiController {

    private final SmsQueryService smsQueryService;
    private final VenusConfig venusConfig;

    public SpectreOpenApiController(SmsQueryService smsQueryService, VenusConfig venusConfig) {
        this.smsQueryService = smsQueryService;
        this.venusConfig = venusConfig;
    }

    @PostMapping("/queryOrder")
    public CommonResult<List<QueryOrderVO>> queryOrder(@RequestHeader Map<String, String> headerMap, @RequestBody QueryOrderRequestDTO request) {
        request.setAppCode(headerMap.get(SmsApiApplicationConstant.PARAM_APP_KEY.toLowerCase()));
        if (CollectionUtils.isEmpty(venusConfig.getOpenApiAppCodes()) || !venusConfig.getOpenApiAppCodes().contains(request.getAppCode())) {
            log.warn("appCode is {}", request.getAppCode());
            return CommonResult.failed("请求参数错误");
        }

        if (CollectionUtils.isEmpty(request.getRequestIds())) {
            return CommonResult.failed("外部流水号不能为空");
        }
        long startTime = System.currentTimeMillis();
        List<QueryOrderVO> orderList = smsQueryService.queryOrder(request.getRequestIds(), request.getAppCode());
        if (!CollectionUtils.isEmpty(orderList)) {
            orderList.forEach(vo -> vo.setMobile(AesUtil.encrypt(vo.getMobile(), venusConfig.getOpenApiAesKey())));
        }
        log.info("状态查询完成|appCode:{}|cost:{}", request.getAppCode(), System.currentTimeMillis() - startTime);
        return CommonResult.success(orderList);

    }


}
