package com.xhqb.spectre.api.service.delayjob;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.service.delayjob.message.CollectDelayingMessage;
import com.xhqb.spectre.api.service.delayjob.message.MarketDelayingMessage;
import com.xhqb.spectre.api.service.delayjob.message.NotifyDelayingMessage;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class SMSDelayingQueueService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SMSDelayingQueueService.class);

    private static ObjectMapper mapper = Jackson2ObjectMapperBuilder.json().build();

    @Resource
    RedisTemplate redisTemplate;

    /**
     * 插入市场类短信定时发送
     *
     * @param message
     * @return
     */
    @SneakyThrows
    public Boolean pushMarketDelayingMessage(MarketDelayingMessage message) {
        Boolean addFlag = redisTemplate.opsForZSet().add(SmsApiApplicationConstant.MARKET_QUEUE_NAME, mapper.writeValueAsString(message), message.getDelayTime());
        return addFlag;
    }

    /**
     * 移除市场类短信定时发送
     *
     * @param message
     * @return
     */
    @SneakyThrows
    public Boolean removeMarketDelayingMessage(MarketDelayingMessage message) {
        Long remove = redisTemplate.opsForZSet().remove(SmsApiApplicationConstant.MARKET_QUEUE_NAME, mapper.writeValueAsString(message));
        return remove > 0 ? true : false;
    }


    /**
     * 拉取最新需要
     * 被消费的市场类短信
     * rangeByScore 根据score范围获取 0-当前时间戳可以拉取当前时间及以前的需要被消费的消息
     *
     * @return
     */
    public List<MarketDelayingMessage> pullMarketDelayingMessage() {
        Set<String> strings = redisTemplate.opsForZSet().rangeByScore(SmsApiApplicationConstant.MARKET_QUEUE_NAME, 0, System.currentTimeMillis());
        if (strings == null) {
            return null;
        }
        List<MarketDelayingMessage> msgList = strings.stream().map(msg -> {
            MarketDelayingMessage message = null;
            try {
                message = mapper.readValue(msg, MarketDelayingMessage.class);
            } catch (JsonProcessingException e) {
                LOGGER.error("Delaying Job Json Process Exception: {}", e.getMessage());
            }
            return message;
        }).collect(Collectors.toList());
        return msgList;
    }


    /**
     * 插入通知类类短信定时发送
     *
     * @param message
     * @return
     */
    @SneakyThrows
    public Boolean pushNotifyDelayingMessage(NotifyDelayingMessage message) {
        Boolean addFlag = redisTemplate.opsForZSet().add(SmsApiApplicationConstant.NOTIFY_QUEUE_NAME, mapper.writeValueAsString(message), message.getDelayTime());
        return addFlag;
    }

    /**
     * 移除通知类短信定时发送
     *
     * @param message
     * @return
     */
    @SneakyThrows
    public Boolean removeNotifyDelayingMessage(NotifyDelayingMessage message) {
        Long remove = redisTemplate.opsForZSet().remove(SmsApiApplicationConstant.NOTIFY_QUEUE_NAME, mapper.writeValueAsString(message));
        return remove > 0 ? true : false;
    }


    /**
     * 拉取最新需要
     * 被消费的通知类短信
     * rangeByScore 根据score范围获取 0-当前时间戳可以拉取当前时间及以前的需要被消费的消息
     *
     * @return
     */
    public List<NotifyDelayingMessage> pullNotifyDelayingMessage() {
        Set<String> strings = redisTemplate.opsForZSet().rangeByScore(SmsApiApplicationConstant.NOTIFY_QUEUE_NAME, 0, System.currentTimeMillis());
        if (strings == null) {
            return null;
        }
        List<NotifyDelayingMessage> msgList = strings.stream().map(msg -> {
            NotifyDelayingMessage message = null;
            try {
                message = mapper.readValue(msg, NotifyDelayingMessage.class);
            } catch (JsonProcessingException e) {
                LOGGER.error("Delaying Job Json Process Exception: {}", e.getMessage());
            }
            return message;
        }).collect(Collectors.toList());
        return msgList;
    }

    /**
     * 插入催收类短信定时发送
     *
     * @param message
     * @return
     */
    @SneakyThrows
    public Boolean pushCollectDelayingMessage(CollectDelayingMessage message) {
        Boolean addFlag = redisTemplate.opsForZSet().add(SmsApiApplicationConstant.COLLECT_QUEUE_NAME, mapper.writeValueAsString(message), message.getDelayTime());
        return addFlag;
    }

    /**
     * 移除催收类短信定时发送
     *
     * @param message
     * @return
     */
    @SneakyThrows
    public Boolean removeCollectDelayingMessage(CollectDelayingMessage message) {
        Long remove = redisTemplate.opsForZSet().remove(SmsApiApplicationConstant.COLLECT_QUEUE_NAME, mapper.writeValueAsString(message));
        return remove > 0 ? true : false;
    }


    /**
     * 拉取最新需要
     * 被消费的催收类短信
     * rangeByScore 根据score范围获取 0-当前时间戳可以拉取当前时间及以前的需要被消费的消息
     *
     * @return
     */
    public List<CollectDelayingMessage> pullCollectDelayingMessage() {
        Set<String> strings = redisTemplate.opsForZSet().rangeByScore(SmsApiApplicationConstant.COLLECT_QUEUE_NAME, 0, System.currentTimeMillis());
        if (strings == null) {
            return null;
        }
        List<CollectDelayingMessage> msgList = strings.stream().map(msg -> {
            CollectDelayingMessage message = null;
            try {
                message = mapper.readValue(msg, CollectDelayingMessage.class);
            } catch (JsonProcessingException e) {
                LOGGER.error("Delaying Job Json Process Exception: {}", e.getMessage());
            }
            return message;
        }).collect(Collectors.toList());
        return msgList;
    }
}
