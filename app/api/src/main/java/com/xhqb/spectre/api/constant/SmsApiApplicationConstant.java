package com.xhqb.spectre.api.constant;

import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 短信平台公共参数常量
 */
public interface SmsApiApplicationConstant {

    /**
     * 应用程序名称
     */
    String SMSAPI_APPLICATION_NAME = "sms-api";

    /**
     * 消息请求ID： 唯一号，后面基于此完成短信业务的串联码
     */
    String SMSAPI_REQUEST_ID_SEQ = "smsapi:requestID";

    /**
     * 延时任务ID： 唯一号，后面基于此完成短信业务的串联码
     */
    String SMSAPI_DELAYING_SEND_JOB_ID_SEQ = "smsapi:delayingID";

    /**
     * 验证码的identificationCode：
     */
    String SMSAPI_IDENTIFICATION_CODE_SEQ = "smsapi:identificationCodeID";

    /**
     * 定时发送任务队列
     */
    String COLLECT_QUEUE_NAME = "collect:message:queue";

    String NOTIFY_QUEUE_NAME = "notify:message:queue";

    String MARKET_QUEUE_NAME = "market:message:queue";

    String MOBILE_WHITE_PREFIX = "smsv3:mobilewhite:";

    String VERIFY_CODE_PREFIX = "smsv3:verifycode:";

    String PARAM_SIGN_KEY = "sign";

    String PARAM_APP_KEY = "appKey";
    // 当前时间
    String PARAM_TIMESTAMP = "timestamp";
    // 随机数
    String PARAM_NONCE = "nonce";

    String PARAM_REQUESTID = "requestId";

    String PARAM_TPLCODE = "tplCode";

    String PARAM_SIGNCODE = "signCode";


    /**
     * 批量发送的最大号码记录数
     */
    Integer ALLOW_SUBMIT_MAX = 100;

    //最大重试次数
    Integer TRY_TIMES = 3;
    //重试间隔时间单位秒
    Integer INTERVAL_TIME = 2;

    //默认分片
    Integer DEFAULT_SLICE_NUMBER = 1;

    Integer DEFAULT_REQSRC = 1;

    //频率限制参数
    String APP_SEND_LIMIT_APP_MAX_COUNT_DAY = "appMaxCountDay";
    String APP_SEND_LIMIT_MOBILE_MAZX_COUNT_HALF_MINUTE = "mobileMaxCountHalfMinute";

    String APP_SEND_LIMIT_MOBILE_MAX_COUNT_HOUR = "mobileMaxCountHour";
    String APP_SEND_LIMIT_MOBILE_MAX_COUNT_DAY = "mobileMaxCountDay";

    String APP_SEND_LIMIT_MOBILE_SAME_CONTENT_MAX_CYCLE = "mobileSameContentMaxCycle";
    String APP_SEND_LIMIT_MOBILE_MAX_COUNT_SAME_CONTENT_CYCLE = "mobileMaxCountSameContentCycle";

    String APP_SEND_LIMIT_MOBILE_MAX_COUNT_VERIFY = "mobileMaxCountVerify";

    // String SMS_SEND_LIMIT_BLOCKED = "";

    //验证码过期参数
    Long VERIFIY_CODE_TIMEOUT = 600L;
    TimeUnit VERIFY_CODE_TIMEUNIT_SECONDS = TimeUnit.SECONDS;

    //SendStatus状态值
    Integer SMS_API_CALL_FAILED = 0;
    Integer SMS_API_CALL_INTERFACE_SUCCESS = 1;
    Integer SMS_API_CALL_BLOCKED = 2;
    Integer SMS_API_CALL_PART_SUCCESS = 3;
    Integer INTERFACE_CALL_ALL_SUCCESS = 4;


    //短信返回
    String SUCCESS_ALL_SMS = "短信发送成功";

    String SUCCESS_PARTLY_SMS = "批量短信部分记录未成功发送";

    //短信返回
    String SUCCESS_SINGLE_FAILED = "短信因内部原因";

    // 验证码处理
    String VERIFYCODE_NOT_VALID = "验证码无效，请重新获取短信验证码";

    String VERIFYCODE_FAILED = "验证码比对失败，请确认验证码是否正确";

    String VERIFYCODE_SUCCESS = "验证码验证成功";


    String SMS_API_DEFAULT_USER = "smsv2";

    String SMS_API_VERSION = "v2";


    String WHITEBOX_APP_SEND_LIMIT = "appSendLimit";

    String AREA_DISABLE_PROVINCE = "default";

    String TPL_CONTENT_PARAM = "\\[\\*\\]";

    // 默认验证码长度
    Integer SMS_VERIFY_CODE_LEN = 4;

    String DEFAULT_GATEWAY_CODE = "106";

    // 1天最多发送短信条数 设置为0不限制
    int DEFAULT_APPMAXCOUNTDAY = 0;

    int SEND_TYPE_CURRENT = 1;
    int SEND_TYPE_DALY = 2;

    // 默认签名code
    String SIGN_CODE_DEFAULT = "BGALL";

    Long PHONE_STATUS_CODE = -1L; // 未查询
    Long PHONE_STATUS_CODE0 = 0L; // 空号
    Long PHONE_STATUS_CODE1 = 1L; // 正常
    Long PHONE_STATUS_CODE2 = 2L; // 停机
    Long PHONE_STATUS_CODE3 = 3L; // 库无
    Long PHONE_STATUS_CODE4 = 4L; // 沉默号
    Long PHONE_STATUS_CODE5 = 5L; // 风险号
    Long PHONE_STATUS_CODE6 = 6L; // 关机
    Long PHONE_STATUS_CODE7 = 7L; // 疑似关机
    Long PHONE_STATUS_CODE8 = 8L; // 忙
    Long PHONE_STATUS_CODE9 = 9L; // 未知
    Long PHONE_STATUS_CODE99 = 99L; // 未知

    /**
     * 手机状态保存到redis中的key
     */
    String REDISPHONESTATUSKEY = "phoneStatus:";

    /**
     * 按短信内容发送时最大支持的号码个数
     */
    Integer ALLOW_SUBMIT_MAX_CONTENT_PHONE = 100;

    /**
     * 模版前缀
     **/
    String PREFIX_SMS_CONTENT_TPL_CODE = "http_";
    String SUFFIX_SMS_CONTENT_TPL_CODE = "_tpl";

    String TPL_CONTENT_PARAM2 = "(\\$\\{)([\\s\\S]*?)}";

    String TPL_PARAM_SYMBOL = "[*]";

    //模板参数格式包含两种（1：[*]；2：${XXX}）
    String TPL_PARAM_SYMBOL_REGEX = "\\[\\*]";
    String TPL_PARAM_SYMBOL_REGEX2 = "\\$\\{[\\w|\u4e00-\u9fa5|-]+}";
    Pattern TPL_PARAM_SYMBOL_PATTERN2 = Pattern.compile(TPL_PARAM_SYMBOL_REGEX2);

    String TPL_PARAM_SYMBOL_REPLACE = "\\.{1,20}";

    String CONTENT_PARAMS_PHONE_KEY = "phone";

    // 是否支持短信内容api
    Integer SUPPORT_CONTENT_API_FLAG0 = 0; // 不支持
    Integer SUPPORT_CONTENT_API_FLAG1 = 1; // 支持并检查模版内容
    Integer SUPPORT_CONTENT_API_FLAG2 = 2; // 支持并不检测

    String TPL_DATA_SPLIT_STR = ":";

}
