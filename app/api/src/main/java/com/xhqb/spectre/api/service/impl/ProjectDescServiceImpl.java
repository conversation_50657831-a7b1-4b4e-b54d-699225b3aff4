package com.xhqb.spectre.api.service.impl;

import com.xhqb.spectre.api.model.entity.ProjectDescVO;
import com.xhqb.spectre.api.service.ProjectDescService;
import com.xhqb.spectre.common.dal.entity.ProjectDescDO;
import com.xhqb.spectre.common.dal.mapper.ProjectDescMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProjectDescServiceImpl implements ProjectDescService {

    @Autowired
    private ProjectDescMapper projectDescMapper;

    @Override
    public List<ProjectDescVO> listAll(Integer status) {
        List<ProjectDescDO> dos = projectDescMapper.selectEnum(status);
        return dos.stream().map(ProjectDescVO::buildVO).collect(Collectors.toList());
    }
}
