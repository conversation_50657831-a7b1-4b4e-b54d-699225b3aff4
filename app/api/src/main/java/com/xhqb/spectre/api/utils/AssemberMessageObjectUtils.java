package com.xhqb.spectre.api.utils;


import com.xhqb.spectre.api.constant.SMSSendFailTypeEnum;
import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import com.xhqb.spectre.api.model.entity.SMSMessageMQAssembler;
import com.xhqb.spectre.api.model.entity.SMSSendFailedRecord;
import com.xhqb.spectre.api.model.entity.SMSSendSuccessRecord;
import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.common.mq.MessageMQ;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * 消息体构建
 * 1、组装MessageMQ实体
 * 2、组装成功、失败实体
 */
public class AssemberMessageObjectUtils {

    private static Logger LOGGER = LoggerFactory.getLogger(AssemberMessageObjectUtils.class.getName());

    /**
     * 组装下发给Dispatcher服务的MessageMQ
     *
     * @param smsMessageMQAssember
     * @return
     */
    public static MessageMQ<BaseBodyMessage> buildSMSBodyMessageMQ(SMSMessageMQAssembler smsMessageMQAssember) {
        MessageMQ<BaseBodyMessage> messageMQ = new MessageMQ<>();

        messageMQ.setAppCode(smsMessageMQAssember.getAppCode());
        messageMQ.setPhone(smsMessageMQAssember.getPhone());
        messageMQ.setProvince(smsMessageMQAssember.getProvince());
        messageMQ.setCity(smsMessageMQAssember.getCity());
        messageMQ.setIsp(smsMessageMQAssember.getIsp());
        messageMQ.setSmsCode(smsMessageMQAssember.getSmsCode());
        messageMQ.setChannelCodeSet(smsMessageMQAssember.getChannelCodeSet());
        messageMQ.setOrderId(smsMessageMQAssember.getOrderId());
        messageMQ.setSignName(smsMessageMQAssember.getSignName());
        messageMQ.setSliceId(smsMessageMQAssember.getSliceId());
        messageMQ.setSendTime(smsMessageMQAssember.getSendTime());
        messageMQ.setReceiveTime(smsMessageMQAssember.getReceiveTime());
        messageMQ.setTplCode(smsMessageMQAssember.getTplCode());
        messageMQ.setContent(smsMessageMQAssember.getContent());
        messageMQ.setParamMap(smsMessageMQAssember.getParamMap());
        messageMQ.setReqSrc(smsMessageMQAssember.getReqSrc());
        messageMQ.setRequestId(smsMessageMQAssember.getRequestId());
        messageMQ.setBatchId(smsMessageMQAssember.getBatchId());
        messageMQ.setSendType(SmsApiApplicationConstant.SEND_TYPE_CURRENT);
        messageMQ.setPhoneStatus(smsMessageMQAssember.getPhoneStatus());
        if (!StringUtils.isBlank(smsMessageMQAssember.getSendTime())) {
            messageMQ.setSendType(SmsApiApplicationConstant.SEND_TYPE_DALY);
        }

        messageMQ.setBizBatchId(smsMessageMQAssember.getBizBatchId());
        messageMQ.setCallMetis(smsMessageMQAssember.getCallMetis());

        return messageMQ;
    }

    /**
     * 构建短信发送失败记录
     *
     * @param tplCode
     * @param appCode
     * @param phoneNumber
     * @param smsSendFailTypeEnum
     * @return
     */
    public static SMSSendFailedRecord buildSMSSendFailedRecord(String tplCode, String appCode, String phoneNumber, SMSSendFailTypeEnum smsSendFailTypeEnum) {
        SMSSendFailedRecord smsSendFailedRecord = new SMSSendFailedRecord();
        smsSendFailedRecord.setPhoneNumber(phoneNumber);
        smsSendFailedRecord.setFileCode(String.valueOf(smsSendFailTypeEnum.getStatusCode()));
        smsSendFailedRecord.setFileMsg(smsSendFailTypeEnum.getStatusMsg());
        return smsSendFailedRecord;
    }

    /**
     * 构建短信成功发送记录
     *
     * @param tplCode
     * @param appId
     * @param phoneNumber
     * @return
     */
    public static SMSSendSuccessRecord buildSMSSendSuccessRecord(String tplCode, String appId, String phoneNumber) {
        SMSSendSuccessRecord smsSendSuccessRecord = new SMSSendSuccessRecord();
        smsSendSuccessRecord.setPhoneNumber(phoneNumber);
        smsSendSuccessRecord.setSendTime(new Date());
        return smsSendSuccessRecord;
    }

}
