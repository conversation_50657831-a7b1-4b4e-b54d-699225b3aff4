package com.xhqb.spectre.api.thread;



import com.xhqb.spectre.common.exception.MsgSenderException;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @desc: 线程池拒绝策略
 * @author: cl
 * @date: 2022-04-20
 */

public class WaitExecutionHandler implements RejectedExecutionHandler {

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        try {
            executor.getQueue().put(r);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new MsgSenderException("线程池拒绝策略异常");
        }
    }

}
