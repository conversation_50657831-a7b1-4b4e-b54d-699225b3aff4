package com.xhqb.spectre.api.controller;

import com.xhqb.spectre.api.controller.common.CommonResult;
import com.xhqb.spectre.api.model.smsresp.AppVO;
import com.xhqb.spectre.api.service.AppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/spectre/v3/app")
@Slf4j
public class AppController {
    @Autowired
    private AppService appService;

    @GetMapping("")
    public CommonResult<List<AppVO>> all() {
        List<AppVO> list = appService.getAll();
        return CommonResult.success(list);
    }
}
