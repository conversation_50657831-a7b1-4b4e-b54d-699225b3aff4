package com.xhqb.spectre.api.service;


import com.xhqb.spectre.api.model.smsreq.BaseSMSReqDTO;
import com.xhqb.spectre.api.model.smsresp.BaseSMSResultVO;


/**
 * 短信发送流程节点
 */
public interface AssemblerSMSService {

    /**
     * 发送短信
     * 备注： 返回置后续更改为枚举类型、更便于进行故障识别
     *
     * @param
     * @param
     * @return
     */
    Boolean sendSMSTask(BaseSMSReqDTO baseSMSReqDTO, BaseSMSResultVO baseSMSResultVO);


}
