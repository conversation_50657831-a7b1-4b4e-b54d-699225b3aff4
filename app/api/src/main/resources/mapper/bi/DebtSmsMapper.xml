<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.api.bi.mapper.DebtSmsMapper">

    <resultMap id="BaseResultMap" type="com.xhqb.spectre.api.bi.entity.DebtSmsDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="contractNo" column="contract_no" jdbcType="VARCHAR"/>
            <result property="debtLabel" column="debt_label" jdbcType="INTEGER"/>
            <result property="mobileMd5" column="mobile_md5" jdbcType="VARCHAR"/>
            <result property="smsOrderId" column="sms_order_id" jdbcType="VARCHAR"/>
            <result property="smsRequestId" column="sms_request_id" jdbcType="VARCHAR"/>
            <result property="channelMsgId" column="channel_msg_id" jdbcType="VARCHAR"/>
            <result property="channelAccountId" column="channel_account_id" jdbcType="INTEGER"/>
            <result property="tplCode" column="tpl_code" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="requestTime" column="request_time" jdbcType="TIMESTAMP"/>
            <result property="sendStatus" column="send_status" jdbcType="INTEGER"/>
            <result property="sendTime" column="send_time" jdbcType="TIMESTAMP"/>
            <result property="reportStatus" column="report_status" jdbcType="INTEGER"/>
            <result property="reportTime" column="report_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,contract_no,debt_label,
        mobile_md5,sms_order_id,sms_request_id,
        channel_msg_id,channel_account_id,tpl_code,
        content,request_time,send_status,
        send_time,report_status,report_time,
        create_time,update_time,is_delete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_debt_sms
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_debt_sms
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhqb.spectre.api.bi.entity.DebtSmsDO" useGeneratedKeys="true">
        insert into t_debt_sms
        ( id,contract_no,debt_label
        ,mobile_md5,sms_order_id,sms_request_id
        ,channel_msg_id,channel_account_id,tpl_code
        ,content,request_time,send_status
        ,send_time,report_status,report_time
        ,create_time,update_time,is_delete
        )
        values (#{id,jdbcType=BIGINT},#{contractNo,jdbcType=VARCHAR},#{debtLabel,jdbcType=INTEGER}
        ,#{mobileMd5,jdbcType=VARCHAR},#{smsOrderId,jdbcType=VARCHAR},#{smsRequestId,jdbcType=VARCHAR}
        ,#{channelMsgId,jdbcType=VARCHAR},#{channelAccountId,jdbcType=INTEGER},#{tplCode,jdbcType=VARCHAR}
        ,#{content,jdbcType=VARCHAR},#{requestTime,jdbcType=TIMESTAMP},#{sendStatus,jdbcType=INTEGER}
        ,#{sendTime,jdbcType=TIMESTAMP},#{reportStatus,jdbcType=INTEGER},#{reportTime,jdbcType=TIMESTAMP}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP},#{isDelete,jdbcType=TINYINT}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhqb.spectre.api.bi.entity.DebtSmsDO" useGeneratedKeys="true">
        insert into t_debt_sms
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="contractNo != null">contract_no,</if>
                <if test="debtLabel != null">debt_label,</if>
                <if test="mobileMd5 != null">mobile_md5,</if>
                <if test="smsOrderId != null">sms_order_id,</if>
                <if test="smsRequestId != null">sms_request_id,</if>
                <if test="channelMsgId != null">channel_msg_id,</if>
                <if test="channelAccountId != null">channel_account_id,</if>
                <if test="tplCode != null">tpl_code,</if>
                <if test="content != null">content,</if>
                <if test="requestTime != null">request_time,</if>
                <if test="sendStatus != null">send_status,</if>
                <if test="sendTime != null">send_time,</if>
                <if test="reportStatus != null">report_status,</if>
                <if test="reportTime != null">report_time,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="isDelete != null">is_delete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="contractNo != null">#{contractNo,jdbcType=VARCHAR},</if>
                <if test="debtLabel != null">#{debtLabel,jdbcType=INTEGER},</if>
                <if test="mobileMd5 != null">#{mobileMd5,jdbcType=VARCHAR},</if>
                <if test="smsOrderId != null">#{smsOrderId,jdbcType=VARCHAR},</if>
                <if test="smsRequestId != null">#{smsRequestId,jdbcType=VARCHAR},</if>
                <if test="channelMsgId != null">#{channelMsgId,jdbcType=VARCHAR},</if>
                <if test="channelAccountId != null">#{channelAccountId,jdbcType=INTEGER},</if>
                <if test="tplCode != null">#{tplCode,jdbcType=VARCHAR},</if>
                <if test="content != null">#{content,jdbcType=VARCHAR},</if>
                <if test="requestTime != null">#{requestTime,jdbcType=TIMESTAMP},</if>
                <if test="sendStatus != null">#{sendStatus,jdbcType=INTEGER},</if>
                <if test="sendTime != null">#{sendTime,jdbcType=TIMESTAMP},</if>
                <if test="reportStatus != null">#{reportStatus,jdbcType=INTEGER},</if>
                <if test="reportTime != null">#{reportTime,jdbcType=TIMESTAMP},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="isDelete != null">#{isDelete,jdbcType=TINYINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.api.bi.entity.DebtSmsDO">
        update t_debt_sms
        <set>
                <if test="contractNo != null">
                    contract_no = #{contractNo,jdbcType=VARCHAR},
                </if>
                <if test="debtLabel != null">
                    debt_label = #{debtLabel,jdbcType=INTEGER},
                </if>
                <if test="mobileMd5 != null">
                    mobile_md5 = #{mobileMd5,jdbcType=VARCHAR},
                </if>
                <if test="smsOrderId != null">
                    sms_order_id = #{smsOrderId,jdbcType=VARCHAR},
                </if>
                <if test="smsRequestId != null">
                    sms_request_id = #{smsRequestId,jdbcType=VARCHAR},
                </if>
                <if test="channelMsgId != null">
                    channel_msg_id = #{channelMsgId,jdbcType=VARCHAR},
                </if>
                <if test="channelAccountId != null">
                    channel_account_id = #{channelAccountId,jdbcType=INTEGER},
                </if>
                <if test="tplCode != null">
                    tpl_code = #{tplCode,jdbcType=VARCHAR},
                </if>
                <if test="content != null">
                    content = #{content,jdbcType=VARCHAR},
                </if>
                <if test="requestTime != null">
                    request_time = #{requestTime,jdbcType=TIMESTAMP},
                </if>
                <if test="sendStatus != null">
                    send_status = #{sendStatus,jdbcType=INTEGER},
                </if>
                <if test="sendTime != null">
                    send_time = #{sendTime,jdbcType=TIMESTAMP},
                </if>
                <if test="reportStatus != null">
                    report_status = #{reportStatus,jdbcType=INTEGER},
                </if>
                <if test="reportTime != null">
                    report_time = #{reportTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="isDelete != null">
                    is_delete = #{isDelete,jdbcType=TINYINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.api.bi.entity.DebtSmsDO">
        update t_debt_sms
        set 
            contract_no =  #{contractNo,jdbcType=VARCHAR},
            debt_label =  #{debtLabel,jdbcType=INTEGER},
            mobile_md5 =  #{mobileMd5,jdbcType=VARCHAR},
            sms_order_id =  #{smsOrderId,jdbcType=VARCHAR},
            sms_request_id =  #{smsRequestId,jdbcType=VARCHAR},
            channel_msg_id =  #{channelMsgId,jdbcType=VARCHAR},
            channel_account_id =  #{channelAccountId,jdbcType=INTEGER},
            tpl_code =  #{tplCode,jdbcType=VARCHAR},
            content =  #{content,jdbcType=VARCHAR},
            request_time =  #{requestTime,jdbcType=TIMESTAMP},
            send_status =  #{sendStatus,jdbcType=INTEGER},
            send_time =  #{sendTime,jdbcType=TIMESTAMP},
            report_status =  #{reportStatus,jdbcType=INTEGER},
            report_time =  #{reportTime,jdbcType=TIMESTAMP},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            is_delete =  #{isDelete,jdbcType=TINYINT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <select id="selectByContractNo"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_debt_sms
        where
            contract_no in
        <foreach item="contractNo" index="index" collection="contractNos" open="(" separator="," close=")">
            #{contractNo,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>
