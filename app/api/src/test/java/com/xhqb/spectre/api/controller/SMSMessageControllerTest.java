//package com.xhqb.spectre.api.controller;
//
//import com.google.gson.Gson;
//import com.xhqb.spectre.api.model.smsreq.SingleSMSReqDTO;
//import org.junit.After;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.runners.MockitoJUnitRunner;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.http.MediaType;
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.test.web.servlet.MvcResult;
//import org.springframework.test.web.servlet.ResultActions;
//import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
//import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
//import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
//import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
//import org.springframework.test.web.servlet.setup.MockMvcBuilders;
//import org.springframework.web.context.WebApplicationContext;
//
//import java.util.ArrayList;
//import java.util.List;
//
//
//@RunWith(MockitoJUnitRunner.class)
//@SpringBootTest
//public class SMSMessageControllerTest {
//
//    private MockMvc mockMvc;
//
//    @Autowired
//    private WebApplicationContext wac; // 注入WebApplicationContext
//
//    @InjectMocks
//    private SpectreApiController spectreApiController;
//
//
//    @Before
//    public void before() throws Exception {
//        //使用 WebApplicationContext 构建 MockMvc
//        this.mockMvc = MockMvcBuilders.standaloneSetup(spectreApiController).build();
//
////        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
//    }
//
//    @After
//    public void after() throws Exception {
//
//    }
//
//    /**
//     * Method: sendVerifyCodeSMS(VerifyCodeSMSReqDTO verifyCodeRequest)
//     */
//    @Test
//    public void testSendVerifyCodeSMS() throws Exception {
//
////       MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/add")
////                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
////                //form表单格式传参
////                .param("id", "4")
////                .param("name", "junit test")
////                .param("password", "111")
////                .param("mobilePhone", "18523980000")
////                .characterEncoding("utf-8")
////                .accept(MediaType.APPLICATION_JSON_UTF8_VALUE);
////
////        ResultActions result = mvc.perform(requestBuilder);
////
////        MvcResult mvcResult = result.andExpect(MockMvcResultMatchers.status().isOk())
////                .andDo(MockMvcResultHandlers.print())
////                .andReturn();// 返回执行请求的结果
////
////        System.out.println("response------------------:"+mvcResult.getResponse().getContentAsString());
//
//
//    }
//
//    /**
//     * Method: checkVerifyCode(VerifyCodeCheckReqDTO verifyCodeCheckRequest)
//     */
//    @Test
//    public void testCheckVerifyCode() throws Exception {
//        //TODO: Test goes here...
//
//    }
//
//    /**
//     * Method: sendSingleSMS(SingleSMSReqDTO singleSMSRequest)
//     */
//    @Test
//    public void testSendSingleSMS() throws Exception {
//
//        List<String> paramMap = new ArrayList<>();
//        paramMap.add("123");
//
//        SingleSMSReqDTO singleSMSReqDTO = new SingleSMSReqDTO();
//        singleSMSReqDTO.setAppCode("test");
//        singleSMSReqDTO.setPhoneNumbers("15118003062");
//        singleSMSReqDTO.setTplCode("test_002");
//        singleSMSReqDTO.setSign("test");
//        singleSMSReqDTO.setParamMap(paramMap);
//
//        Gson gson = new Gson();
//        System.out.println(gson.toJson(singleSMSReqDTO));
//
//        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/api/spectre/v3/sendSMS")
//                .contentType(MediaType.APPLICATION_JSON)
//                .accept(MediaType.APPLICATION_JSON)
//                .content(gson.toJson(singleSMSReqDTO));
//
//        ResultActions result = mockMvc.perform(requestBuilder);
//
//        MvcResult mvcResult = result.andExpect(MockMvcResultMatchers.status().isOk())
//                .andDo(MockMvcResultHandlers.print())
//                .andReturn();// 返回执行请求的结果
//
//        System.out.println("response------------------:" + mvcResult.getResponse().getContentAsString());
//
//    }
//
//    /**
//     * Method: sendBatchSMS(BatchSMSReqDTO batchSMSRequest)
//     */
//    @Test
//    public void testSendBatchSMS() throws Exception {
//        //TODO: Test goes here...
//
//    }
//}
