//package com.xhqb.spectre.api.utils;
//
//import org.junit.After;
//import org.junit.Before;
//import org.junit.runner.RunWith;
//import org.mockito.runners.MockitoJUnitRunner;
//
//@RunWith(MockitoJUnitRunner.class)
//public class CmppMsgIdUtilTest {
//
//    @Before
//    public void before() throws Exception {
//    }
//
//    @After
//    public void after() throws Exception {
//    }
//
////    public void getId() {
////        long start = System.currentTimeMillis();
////        for (int i = 0; i < 100000000; i++) {
////            System.out.println(CmppMsgIdUtils.getMsgId());
////        }
////        System.out.println(System.currentTimeMillis() - start + "ms");
////    }
//
//}
