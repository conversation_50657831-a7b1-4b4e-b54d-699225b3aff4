//package com.xhqb.spectre.api.service;
//
//import com.alibaba.fastjson.JSON;
//import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
//import com.xhqb.spectre.common.constant.RedisKeys;
//import com.xhqb.spectre.common.dal.dto.AreaData;
//import com.xhqb.spectre.common.dal.dto.TplChannelData;
//import com.xhqb.spectre.common.dal.dto.TplData;
//import com.xhqb.spectre.common.dal.dto.TplDisableData;
//import com.xhqb.spectre.common.dal.entity.SignDO;
//import com.xhqb.spectre.common.dal.entity.TplDO;
//import com.xhqb.spectre.common.dal.mapper.SignMapper;
//import org.junit.runner.RunWith;
//import org.mockito.Mock;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.springframework.data.redis.connection.RedisConnection;
//import org.springframework.data.redis.connection.RedisConnectionFactory;
//import org.springframework.data.redis.core.*;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import static org.mockito.Matchers.*;
//import static org.mockito.Mockito.*;
//
//@RunWith(PowerMockRunner.class)
////@PrepareForTest({MarketMessageServiceImpl.class,AbstractDispatchBootstrap.class})
//public abstract class RedisTemplateTest {
//
//    @Mock
//    public RedisTemplate redisTemplate;
//
//    @Mock
//    public SignMapper signMapper;
//
//    public void setUp() {
//        ValueOperations valueOperations = mock(ValueOperations.class);
//        SetOperations setOperations = mock(SetOperations.class);
//
//        HashOperations hashOperations = mock(HashOperations.class);
//        ListOperations listOperations = redisTemplate.opsForList();
//        ZSetOperations zSetOperations = redisTemplate.opsForZSet();
//
//        when(redisTemplate.opsForSet()).thenReturn(setOperations);
//        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
//        when(redisTemplate.opsForHash()).thenReturn(hashOperations);
//        when(redisTemplate.opsForList()).thenReturn(listOperations);
//        when(redisTemplate.opsForZSet()).thenReturn(zSetOperations);
//
//        doNothing().when(hashOperations).put(anyString(), anyString(), anyString());
//
//        when(hashOperations.delete(anyString(), anyString())).thenReturn(1l);
//
//        //白名单
//        when(hashOperations.get(eq(SmsApiApplicationConstant.MOBILE_WHITE_PREFIX + "appMaxCountDay"), any()))
//                .thenReturn(
//                        "allowed");
//
//        //黑名单
//        when(valueOperations.get(eq(SmsApiApplicationConstant.VERIFY_CODE_PREFIX + "222222222222222222" + "12322234" + "13800000001")))
//                .thenReturn(
//                        "666888");
//
//        TplDO tplDO = new TplDO();
//        tplDO.setCode("test_001");
//        tplDO.setId(1112);
//        tplDO.setSmsTypeCode("market");
//        tplDO.setSignId(3);
//        tplDO.setContent("{1}，您的账户有{2}元可用，提现可得最高100元现金大奖！戳 xh1.cn/HYWd 回T退订");
//        tplDO.setAppCode("test");
//
//        TplData tplData = buildTplCacheData(tplDO);
//        redisTemplate.opsForHash().put(RedisKeys.AppKeys.APP_HASH_KEY, tplData.getCode(), JSON.toJSONString(tplData));
//
//        RedisOperations redisOperations = mock(RedisOperations.class);
//        RedisConnection redisConnection = mock(RedisConnection.class);
//        RedisConnectionFactory redisConnectionFactory = mock(RedisConnectionFactory.class);
//        when(redisTemplate.getConnectionFactory()).thenReturn(redisConnectionFactory);
//        when(valueOperations.getOperations()).thenReturn(redisOperations);
//        when(redisTemplate.getConnectionFactory().getConnection()).thenReturn(redisConnection);
//    }
//
//    private TplData buildTplCacheData(TplDO tplDO) {
//        //签名
//        SignDO signDO = signMapper.selectByPrimaryKey(tplDO.getSignId());
//        List<String> isplist = new ArrayList<>();
//        isplist.add("移动");
//        isplist.add("联通");
//
//        List<AreaData> areaList = new ArrayList<>();
//        AreaData areaData = new AreaData();
//        areaData.setCity("");
//        areaData.setProvince("全国");
//        areaList.add(areaData);
//
//        //渠道信息
//        List<TplChannelData> channelList = new ArrayList<>();
//        TplChannelData tplChannelData = new TplChannelData();
//        tplChannelData.setChannelAccountId(3);
//        tplChannelData.setChannelTplId("test_001");
//        tplChannelData.setIspList(isplist);
//        tplChannelData.setAreaFilterType(1);
//        tplChannelData.setAreaList(areaList);
//        tplChannelData.setWeight(90);
//        tplChannelData.setRemark("测试");
//        channelList.add(tplChannelData);
//
//        //模板屏蔽信息
//        List<AreaData> disAreaList = new ArrayList<>();
//        AreaData areaDataDis = new AreaData();
//        areaData.setCity("");
//        areaData.setProvince("北京");
//        disAreaList.add(areaDataDis);
//
//        List<TplDisableData> disableList = new ArrayList<>();
//        TplDisableData tplDisableData = new TplDisableData();
//        tplDisableData.setIspList(isplist);
//        tplDisableData.setAreaList(disAreaList);
//        tplDisableData.setStartTime("2021-10-01 00:00:00");
//        tplDisableData.setEndTime("2021-10-02 00:00:00");
//
//        return TplData.builder()
//                .code(tplDO.getCode())
//                .smsTypeCode(tplDO.getSmsTypeCode())
//                .appCode(tplDO.getAppCode())
//                .signId(tplDO.getSignId())
//                .signName(signDO.getName())
//                .content(tplDO.getContent())
//                .channelInfoList(channelList)
//                .disableInfoList(disableList)
//                .build();
//    }
//}
