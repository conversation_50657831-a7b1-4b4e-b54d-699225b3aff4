//package com.xhqb.spectre.api.utils;
//
//
//import com.xhqb.spectre.api.model.entity.PhoneIsp;
//import org.junit.After;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.runners.MockitoJUnitRunner;
//
//import javax.xml.transform.Source;
//
//@RunWith(MockitoJUnitRunner.class)
//public class PhoneSearchFastUtilTest {
//
//    @Before
//    public void before() throws Exception {
//    }
//
//    @After
//    public void after() throws Exception {
//    }
//
//    @Test
//    public void searchPhone() throws Exception {
//        PhoneSearchFastUtil finder = PhoneSearchFastUtil.getInstance();
//
//        String phone = "15118003062";
//        PhoneIsp phoneIsp = finder.getIsp(phone);
//        System.out.println(phone);
//        System.out.println(phoneIsp.toString());
//
//        /*
//         * 省 市 邮政编码 电话区号 地区编码
//         *  河北|唐山|063000|0315|130200|电信
//         *  北京|北京|100000|010|110100|电信
//         *  广东|深圳|518000|0755|440300|移动
//         */
//    }
//
//}
