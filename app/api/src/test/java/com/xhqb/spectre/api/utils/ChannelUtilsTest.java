package com.xhqb.spectre.api.utils;

import com.xhqb.spectre.api.constant.SmsApiApplicationConstant;
import org.junit.Test;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

public class ChannelUtilsTest {

    private List<Map<String, String>> testParams;
    
    @Before
    public void setUp() {
        testParams = new ArrayList<>();

        Map<String, String> paramMap1 = new HashMap<>();
        paramMap1.put(SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY, "13800138000");
        paramMap1.put("name", "张三");
        paramMap1.put("amount", "100.00");
        paramMap1.put("code", "ABC123");
        testParams.add(paramMap1);
        
        Map<String, String> paramMap2 = new HashMap<>();
        paramMap2.put(SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY, "13900139000");
        paramMap2.put("name", "李四");
        paramMap2.put("amount", "200.00");
        paramMap2.put("code", "DEF456");
        testParams.add(paramMap2);
    }

    @Test
    public void testExtractPlaceholderValues_NormalCase() {
        String content = "尊敬的${name}，您的订单金额为${amount}元，验证码为${code}。";
        String phone = "13800138000";
        
        List<String> result = ChannelUtils.extractPlaceholderValues(testParams, content, phone);
        
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("张三", result.get(0));
        assertEquals("100.00", result.get(1));
        assertEquals("ABC123", result.get(2));
    }

    @Test
    public void testExtractPlaceholderValues_PhoneNotFound() {
        String content = "尊敬的${name}，您的订单金额为${amount}元。";
        String phone = "13700137000"; // 不存在的手机号
        
        List<String> result = ChannelUtils.extractPlaceholderValues(testParams, content, phone);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testExtractPlaceholderValues_EmptyContent() {
        String content = "";
        String phone = "13800138000";
        
        List<String> result = ChannelUtils.extractPlaceholderValues(testParams, content, phone);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testExtractPlaceholderValues_NullContent() {
        String phone = "13800138000";
        
        List<String> result = ChannelUtils.extractPlaceholderValues(testParams, null, phone);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testExtractPlaceholderValues_EmptyPhone() {
        String content = "尊敬的${name}，您的订单金额为${amount}元。";
        String phone = "";
        
        List<String> result = ChannelUtils.extractPlaceholderValues(testParams, content, phone);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testExtractPlaceholderValues_NullPhone() {
        String content = "尊敬的${name}，您的订单金额为${amount}元。";
        
        List<String> result = ChannelUtils.extractPlaceholderValues(testParams, content, null);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testExtractPlaceholderValues_NullParams() {
        String content = "尊敬的${name}，您的订单金额为${amount}元。";
        String phone = "13800138000";
        
        List<String> result = ChannelUtils.extractPlaceholderValues(null, content, phone);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testExtractPlaceholderValues_EmptyParams() {
        String content = "尊敬的${name}，您的订单金额为${amount}元。";
        String phone = "13800138000";
        List<Map<String, String>> emptyParams = new ArrayList<>();
        
        List<String> result = ChannelUtils.extractPlaceholderValues(emptyParams, content, phone);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testExtractPlaceholderValues_MissingParamValue() {
        String content = "尊敬的${name}，您的订单金额为${amount}元，订单号为${orderNo}。";
        String phone = "13800138000"; // orderNo 在 paramMap 中不存在
        
        List<String> result = ChannelUtils.extractPlaceholderValues(testParams, content, phone);
        
        assertNotNull(result);
        assertEquals(2, result.size()); // orderNo 没有值，所以被忽略
        assertEquals("张三", result.get(0));
        assertEquals("100.00", result.get(1));
    }

    @Test
    public void testExtractPlaceholderValues_PhoneKeyIgnored() {
        String content = "尊敬的${phone}，您的订单金额为${amount}元。";
        String phone = "13800138000";
        
        List<String> result = ChannelUtils.extractPlaceholderValues(testParams, content, phone);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("100.00", result.get(0));
    }

    @Test
    public void testExtractPlaceholderValues_NoPlaceholders() {
        String content = "尊敬的用户，您的订单金额为100元。";
        String phone = "13800138000";
        
        List<String> result = ChannelUtils.extractPlaceholderValues(testParams, content, phone);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testExtractPlaceholderValues_PhoneCaseInsensitive() {
        String content = "尊敬的${name}，您的订单金额为${amount}元。";
        String phone = "13800138000";

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put(SmsApiApplicationConstant.CONTENT_PARAMS_PHONE_KEY, "13800138000");
        paramMap.put("name", "王五");
        paramMap.put("amount", "300.00");
        
        List<Map<String, String>> params = new ArrayList<>();
        params.add(paramMap);
        
        ArrayList<String> result = ChannelUtils.extractPlaceholderValues(params, content, phone);
        
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("王五", result.get(0));
        assertEquals("300.00", result.get(1));
    }
}