//package com.xhqb.spectre.api.service;
//
//import com.xhqb.kael.sequencegenerator.DistributedSequence;
//import com.xhqb.spectre.api.model.smsreq.SingleSMSReqDTO;
//import com.xhqb.spectre.api.model.smsresp.BaseSMSResultVO;
//import com.xhqb.spectre.api.model.smsresp.SingleSMSResultVO;
//import com.xhqb.spectre.api.service.impl.SMSMessageManagerServiceImpl;
//import com.xhqb.spectre.api.service.impl.assembler.SingleSMSAssemblerServiceImpl;
//import com.xhqb.spectre.api.utils.PhoneSearchFastUtil;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.runners.MockitoJUnitRunner;
//import org.powermock.api.support.membermodification.MemberModifier;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//
//import static org.mockito.Matchers.anyString;
//import static org.mockito.Mockito.when;
//
//@RunWith(MockitoJUnitRunner.class)
//@PrepareForTest({SMSMessageManagerServiceImpl.class, PhoneSearchFastUtil.class})
//public class SMSMessageManagerServiceTest extends RedisTemplateTest {
//
//    @InjectMocks
//    private SMSMessageManagerServiceImpl smsMessageManagerService;
//
//    /**
//     * 短信发送类型：1、验证码； 2： 单条短信； 3： 批量短信
//     */
//    @Mock
//    SMSSendServiceLoader smsSendServiceLoader;
//
//    /**
//     * 请求ID唯一号生成器
//     */
//    @Mock
//    private DistributedSequence smsRequestIDSeqService;
//
//    /**
//     * identificationCode短信验证码生成器
//     */
//    @Mock
//    private DistributedSequence smsIdentificationCodeSeqService;
//
//    /**
//     * 频率限制检查服务
//     */
//    @Mock
//    FrequencyLimitService frequencyLimitService;
//
//    @Mock
//    SingleSMSAssemblerServiceImpl singleSMSAssemblerService;
//
//    @Mock
//    MemoryDataService memoryDataService;
//
//    @Test
//    public void testDealSingleCodeSms() throws Exception {
//        MemberModifier.field(SMSMessageManagerServiceImpl.class, "smsSendServiceLoader").set(
//                smsMessageManagerService, smsSendServiceLoader);
//
//        MemberModifier.field(SMSMessageManagerServiceImpl.class, "smsRequestIDSeqService").set(
//                smsMessageManagerService, smsRequestIDSeqService);
//
//        MemberModifier.field(SMSMessageManagerServiceImpl.class, "frequencyLimitService").set(
//                smsMessageManagerService, frequencyLimitService);
//
//        MemberModifier.field(SMSMessageManagerServiceImpl.class, "memoryDataService").set(smsMessageManagerService, memoryDataService);
//
//        SingleSMSReqDTO singleSMSReqDTO = new SingleSMSReqDTO();
//
//        singleSMSReqDTO.setAppCode("test");
//        singleSMSReqDTO.setTplCode("test_001");
//        singleSMSReqDTO.setPhoneNumbers("15118003062");
//        singleSMSReqDTO.setSign("sign");
//
//
//        when(smsIdentificationCodeSeqService.nextStr(anyString())).thenReturn("222222222");
//        when(smsRequestIDSeqService.nextStr(anyString())).thenReturn("111111");
//        when(smsSendServiceLoader.loadEndpoint(singleSMSReqDTO)).thenReturn(singleSMSAssemblerService);
//        BaseSMSResultVO<SingleSMSResultVO> resultVO = smsMessageManagerService.dealSingleSms(singleSMSReqDTO);
//        smsMessageManagerService.dealSingleSms(singleSMSReqDTO);
//        System.out.println(resultVO);
//    }
//
//
//}
//
