package com.xhqb.spectre.api.utils;

import com.xhqb.spectre.api.model.smsreq.SingleSMSReqDTO;
import com.xhqb.spectre.common.dal.dto.TplData;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

class TemplateUtilTest {
    private static final String PARSE_SEQUENTIAL_PLACE_HOLDER_TPL = "<PERSON>'s got a [*] hand,he'll look [*] the room,he [*] tell you his [*].";
    private static final String KEYED_PLACE_HOLDER_TPL = "Robert's got a ${var1} hand,he'll look ${var2} the room,he ${var3} tell you his ${var4}.";

    private static final String CONTENT = "<PERSON>'s got a quick hand,he'll look round the room,he won't tell you his plan.";


    @Test
    void testCountKeyedPlaceholders() {
        // Verify the results
        assertThat(TemplateUtil.countKeyedPlaceholders(KEYED_PLACE_HOLDER_TPL)).isEqualTo(4);
        assertThat(TemplateUtil.countKeyedPlaceholders(PARSE_SEQUENTIAL_PLACE_HOLDER_TPL)).isZero();
        assertThat(TemplateUtil.countKeyedPlaceholders(CONTENT)).isZero();
        assertThat(TemplateUtil.countKeyedPlaceholders("Robert's got a ${var1} hand")).isEqualTo(1);
        assertThat(TemplateUtil.countKeyedPlaceholders("Robert's got a ${var1} hand,he'll look ${var2} the room")).isEqualTo(2);
    }

    @Test
    void testCountSequentialPlaceholders() {
        assertThat(TemplateUtil.countSequentialPlaceholders(PARSE_SEQUENTIAL_PLACE_HOLDER_TPL)).isEqualTo(4);
    }

    @Test
    void testParseKeyedPlaceholders() {
        // Setup
        final Map<String, String> keyValuePairs = new HashMap<>();
        keyValuePairs.put("var1", "quick");
        keyValuePairs.put("var2", "round");
        keyValuePairs.put("var3", "won't");
        keyValuePairs.put("var4", "plan");


        // Run the test
        final String result = TemplateUtil.parseKeyedPlaceholders(KEYED_PLACE_HOLDER_TPL, keyValuePairs);

        // Verify the results
        assertThat(result).isEqualTo(CONTENT);
    }

    @Test
    void testParseSequentialPlaceholders() {
        assertThat(TemplateUtil.parseSequentialPlaceholders(PARSE_SEQUENTIAL_PLACE_HOLDER_TPL,
                "quick" ,"round", "won't", "plan")).isEqualTo(CONTENT);
    }

    @Test
    void testHasNonEmptyPlaceholderValues() {
        // Setup
        final Map<String, String> keyValuePairs = new HashMap<>();
        keyValuePairs.put("var1", "quick");
        keyValuePairs.put("var2", "round");
        keyValuePairs.put("var3", "won't");
        keyValuePairs.put("var5", "plan");

        // Run the test
        final Boolean result = TemplateUtil.hasAllPlaceholdersWithPairs(KEYED_PLACE_HOLDER_TPL, keyValuePairs);

        // Verify the results

        assertThat(result).isFalse();

        assertThat(TemplateUtil.hasAllPlaceholdersWithPairs(CONTENT, keyValuePairs)).isTrue();
        assertThat(TemplateUtil.hasAllPlaceholdersWithPairs(PARSE_SEQUENTIAL_PLACE_HOLDER_TPL, keyValuePairs)).isTrue();
    }

    @Test
    void testHasNonEmptyPlaceholderValues2() {

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("var1", "quick");
        paramMap.put("var2", "round");
        paramMap.put("var3", "won't");
        paramMap.put("var4", "plan");
        paramMap.put("phone", "15000000000");
        Map<String, String> paramMap2 = new HashMap<>();
        paramMap2.put("var1", "quick");
        paramMap2.put("var2", "round");
        paramMap2.put("var3", "won't");
        paramMap2.put("var5", "plan");
        paramMap2.put("phone", "15000000001");

        assertThat(TemplateUtil.hasAllPlaceholdersWithPairs(KEYED_PLACE_HOLDER_TPL, paramMap)).isTrue();
        assertThat(TemplateUtil.hasAllPlaceholdersWithPairs(KEYED_PLACE_HOLDER_TPL, paramMap2)).isFalse();
    }

    @Test
    void testValidateTplParams_NoPlaceholders() {
        // Setup
        TplData tplData = new TplData();
        tplData.setContent(CONTENT);
        SingleSMSReqDTO singleSMSReqDTO = new SingleSMSReqDTO();
        
        // Run the test
        boolean result = TemplateUtil.validateTplParams(tplData, "13800000001", singleSMSReqDTO);
        
        // Verify
        assertThat(result).isTrue();
    }

    @Test
    void testValidateTplParams_SequentialPlaceholders_ValidParams() {
        // Setup
        TplData tplData = new TplData();
        tplData.setContent(PARSE_SEQUENTIAL_PLACE_HOLDER_TPL);
        SingleSMSReqDTO singleSMSReqDTO = new SingleSMSReqDTO();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("13800000001", "quick,round,won't,plan");
        singleSMSReqDTO.setParamMap(paramMap);
        
        // Run the test
        boolean result = TemplateUtil.validateTplParams(tplData, "13800000001", singleSMSReqDTO);
        
        // Verify
        assertThat(result).isTrue();
    }

    @Test
    void testValidateTplParams_SequentialPlaceholders_InsufficientParams() {
        // Setup
        TplData tplData = new TplData();
        tplData.setContent(PARSE_SEQUENTIAL_PLACE_HOLDER_TPL);
        SingleSMSReqDTO singleSMSReqDTO = new SingleSMSReqDTO();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("13800000001", "quick,round");
        singleSMSReqDTO.setParamMap(paramMap);
        
        // Run the test
        boolean result = TemplateUtil.validateTplParams(tplData, "13800000001", singleSMSReqDTO);
        
        // Verify
        assertThat(result).isFalse();
    }

    @Test
    void testValidateTplParams_KeyedPlaceholders_ValidParams() {
        // Setup
        TplData tplData = new TplData();
        tplData.setContent(KEYED_PLACE_HOLDER_TPL);
        SingleSMSReqDTO singleSMSReqDTO = new SingleSMSReqDTO();
        List<Map<String, String>> paramList = new ArrayList<>();
        Map<String, String> params = new HashMap<>();
        params.put("phone", "13800000001");
        params.put("var1", "quick");
        params.put("var2", "round");
        params.put("var3", "won't");
        params.put("var4", "plan");
        paramList.add(params);
        singleSMSReqDTO.setParamList(paramList);
        
        // Run the test
        boolean result = TemplateUtil.validateTplParams(tplData, "13800000001", singleSMSReqDTO);
        
        // Verify
        assertThat(result).isTrue();
    }

    @Test
    void testValidateTplParams_KeyedPlaceholders_MissingParams() {
        // Setup
        TplData tplData = new TplData();
        tplData.setContent(KEYED_PLACE_HOLDER_TPL);
        SingleSMSReqDTO singleSMSReqDTO = new SingleSMSReqDTO();
        List<Map<String, String>> paramList = new ArrayList<>();
        Map<String, String> params = new HashMap<>();
        params.put("phone", "13800000001");
        params.put("var1", "quick");
        params.put("var2", "round");
        // Missing var3 and var4
        paramList.add(params);
        singleSMSReqDTO.setParamList(paramList);
        
        // Run the test
        boolean result = TemplateUtil.validateTplParams(tplData, "13800000001", singleSMSReqDTO);
        
        // Verify
        assertThat(result).isFalse();
    }

    @Test
    void testValidateTplParams_MixedPlaceholders_ShouldFail() {
        // Setup - template with both sequential and keyed placeholders
        TplData tplData = new TplData();
        tplData.setContent("Hello [*] and ${var1}");
        SingleSMSReqDTO singleSMSReqDTO = new SingleSMSReqDTO();
        
        // Run the test
        boolean result = TemplateUtil.validateTplParams(tplData, "13800000001", singleSMSReqDTO);
        
        // Verify
        assertThat(result).isFalse();
    }

    @Test
    void testValidateTplParams_KeyedPlaceholders_SequentialParamMap() {
        // Setup - using paramMap for keyed placeholders
        TplData tplData = new TplData();
        tplData.setContent(KEYED_PLACE_HOLDER_TPL);
        SingleSMSReqDTO singleSMSReqDTO = new SingleSMSReqDTO();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("13800000001", "quick,round,won't,1,2");
        singleSMSReqDTO.setParamMap(paramMap);
        
        // Run the test
        boolean result = TemplateUtil.validateTplParams(tplData, "13800000001", singleSMSReqDTO);
        
        // Verify
        assertThat(result).isTrue();
    }
}
