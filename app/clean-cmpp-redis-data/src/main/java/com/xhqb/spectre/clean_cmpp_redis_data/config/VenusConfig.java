package com.xhqb.spectre.clean_cmpp_redis_data.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/30 19:26
 * @Description:
 */
@Component
@Data
public class VenusConfig {

    /**
     * 是否开启限流
     */
    @Value("${sendLimit.enabled:true}")
    private Boolean sendLimitEnabled;

    /**
     * 催收
     */
    @Value("#{'${kael.mq.producers:}'.split(',')[3]}")
    private String mqCollectSMSMessage;

    /**
     * 市场
     */
    @Value("#{'${kael.mq.producers:}'.split(',')[2]}")
    private String mqMarketSMSMessage;

    /**
     * 通知
     */
    @Value("#{'${kael.mq.producers:}'.split(',')[0]}")
    private String mqNotifySMSMessage;

    /**
     * 验证码
     */
    @Value("#{'${kael.mq.producers:}'.split(',')[1]}")
    private String mqVerifySMSMessage;

    /**
     * 送达通知
     */
    @Value("#{'${kael.mq.producers:}'.split(',')[4]}")
    private String mqDeliverSMSMessage;
}
