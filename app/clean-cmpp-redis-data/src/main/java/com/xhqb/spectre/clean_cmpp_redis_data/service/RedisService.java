package com.xhqb.spectre.clean_cmpp_redis_data.service;

import com.xhqb.spectre.clean_cmpp_redis_data.model.entity.CMPPGatewayMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class RedisService {

    private final String CMPP_GATEWAY_KEY_PREFIX = "Spectre:CMPPGateway:";
    private final String CMPP_GATEWAY_MSG_KEY_PREFIX = CMPP_GATEWAY_KEY_PREFIX + "Msg:";
    private final String CMPP_GATEWAY_USER_MSG_KEY_PREFIX = CMPP_GATEWAY_KEY_PREFIX + "UserMsg:";
    private final String CMPP_GATEWAY_USER_SEESION_PREFIX = CMPP_GATEWAY_KEY_PREFIX + "Session:";

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public void checkDeliverUserMsg(String userName) {
        String key = CMPP_GATEWAY_USER_MSG_KEY_PREFIX + userName;
        Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(key);
        for (Object k : entries.keySet()) {
            if (k instanceof String) {
                String msgId = (String) k;
                String v = (String) entries.get(k);
                if (v.equals("2")) {
                    CMPPGatewayMsg msg = getMsg(msgId);
                    if (msg == null) {
                        log.info("redis: delete {} {}", key, k);
                        stringRedisTemplate.opsForHash().delete(key, k);
                    }
                }
                else {
                    String msgKey = CMPP_GATEWAY_MSG_KEY_PREFIX + msgId;
                    if (!stringRedisTemplate.hasKey(msgKey)) {
                        log.info("redis: delete {} {}", key, k);
                        stringRedisTemplate.opsForHash().delete(key, k);
                    }
                }
            }
        }
    }

    public CMPPGatewayMsg getMsg(String msgId) {
        String key = CMPP_GATEWAY_MSG_KEY_PREFIX + msgId;
        Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(key);
        if (entries.isEmpty()) {
            return null;
        }

        CMPPGatewayMsg msg = new CMPPGatewayMsg();
        msg.setMsgId(msgId);
        for (Object k : entries.keySet()) {
            if(k instanceof String) {
                if(k.equals("Session")) {
                    String session = (String) entries.get(k);
                    msg.setSession(session != null ? session : "");
                }
                else if (k.equals("UserName")) {
                    String userName = (String) entries.get(k);
                    msg.setUserName(userName != null ? userName : "");
                }
                else if (k.equals("DestId")) {
                    String destId = (String) entries.get(k);
                    msg.setDestId(destId != null ? destId : "");
                }
                else if (k.equals("DestterminalId")) {
                    String destterminalId = (String) entries.get(k);
                    msg.setDestterminalId(destterminalId != null ? destterminalId : "");
                }
                else if (k.equals("SubmitTime")) {
                    String submitTime = (String) entries.get(k);
                    msg.setSubmitTime(submitTime != null ? submitTime : "");
                }
                else if (k.equals("DoneTime")) {
                    String doneTime = (String) entries.get(k);
                    msg.setDoneTime(doneTime != null ? doneTime : "");
                }
                else if (k.equals("State")) {
                    String state = (String) entries.get(k);
                    msg.setState(state != null ? state : "");
                }
            }
        }

        return msg;
    }
}