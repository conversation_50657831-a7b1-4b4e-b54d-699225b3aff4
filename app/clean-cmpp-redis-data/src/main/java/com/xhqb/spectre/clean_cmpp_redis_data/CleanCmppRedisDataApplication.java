package com.xhqb.spectre.clean_cmpp_redis_data;

import com.xhqb.spectre.clean_cmpp_redis_data.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@MapperScan("com.xhqb.spectre.common.dal.mapper")
@Slf4j
public class CleanCmppRedisDataApplication implements CommandLineRunner {

    @Value("${cmpp.username}")
    private String username;

    @Autowired
    private RedisService redisService;

    public static void main(String[] args) {
        SpringApplication.run(CleanCmppRedisDataApplication.class, args);
    }

    @Override
    public void run(String... args) {
        redisService.checkDeliverUserMsg(username);
    }

}
