<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>app</artifactId>
        <groupId>com.xhqb.spectre</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>spectre-receipthttp</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.xhqb.spectre</groupId>
            <artifactId>service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.spectre</groupId>
            <artifactId>channel</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xhqb</groupId>
            <artifactId>kael-mq-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.spectre</groupId>
            <artifactId>common</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- <dependency>
             <groupId>com.xhqb.smsv2</groupId>
             <artifactId>exchange-boshitong</artifactId>
             <version>${project.version}</version>
         </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-sequence</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-elasticjob</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-infra</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.6.2</version>
        </dependency>

        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>-->
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-venus</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.6.2</version>
        </dependency>
        <!-- mybatis-plus 依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-druid</artifactId>
        </dependency>


        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

    </dependencies>

</project>