//package com.xhqb.spectre.httpreceipt.service;
//
//import com.alibaba.fastjson.JSONObject;
//import com.xhqb.spectre.httpreceipt.ReceiptApplication;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.Map;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {ReceiptApplication.class})
//public class DeliveryServiceTest {
//
//    @Autowired
//    DeliveryService deliveryService;
//
//    @Test
//    public void testReceive() throws Exception {
//        String str = "{\"receiveTime\":\"2021-09-10 11:59:52\",\"phoneNumber\":\"***********\",\"modelId\":38312,\"userAccount\":\"5114100dfae93a1010aab29b7ee7a700\",\"outOrderId\":\"***************\",\"sign\":\"ef8a2f4541262f1083efe76f370ea88f\",\"signatureId\":0,\"sendStatus\":0,\"message\":\"其它上游原因失败：HD:0005\",\"userId\":\"\",\"cmppSpid\":\"\",\"timestamp\":*************}";
//        Map<String, Object> map = JSONObject.parseObject(str);
//        deliveryService.receive("shanyun", map);
//    }
//}
