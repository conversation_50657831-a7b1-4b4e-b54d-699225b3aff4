//package com.xhqb.spectre.httpreceipt.consumer;
//
//import com.xhqb.spectre.httpreceipt.ReceiptApplication;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {ReceiptApplication.class})
//public class HttpConsumerTest {
//
//    @Autowired
//    HttpConsumer httpConsumer;
//
//    @Test
//    public void testHttpDeliveryConsumer() throws Exception {
//        String msg = "{\"billCount\":1,\"channelCode\":\"shanyun\",\"channelMsgId\":\"684769889224065\",\"code\":\"0\",\"desc\":\"其它上游原因失败：HD:0005\",\"mobile\":\"15026552734\",\"realSendTime\":1631246392000,\"reportTime\":1631246392000,\"status\":2}";
//        httpConsumer.httpDeliveryConsumer(msg);
//    }
//
//
//    @Test
//    public void testParseMessage() throws Exception {
//        //TODO: Test goes here...
//    }
//}
