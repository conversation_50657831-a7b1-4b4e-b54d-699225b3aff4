
#mybatis.mapperLocations=classpath:com.xhqb.smsv2.core.dao.mapper/*Mapper.xml
mybatis.typeAliasesPackage=com.xhqb.smsv2.core.dto
mybatis-plus.mapper-locations=classpath:com.xhqb.smsv2.core.dao.mapper/*Mapper.xml
#æ°æ®åºè¿æ¥
#kael.datasource.druid.resource-id = smsv2-receipt
#kael.config.resources.druid.smsv2-receipt.url=********************************************************************************************************************************
#kael.config.resources.druid.smsv2-receipt.username=xhtest
#kael.config.resources.druid.smsv2-receipt.password=xh_test
#kael.config.resources.druid.smsv2-receipt.connectionProperties=config.decrypt=false;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAI6fuRC58lLwqw9jwGnQ6oSgUW/lu/pVN5cEFjt/Dr9H/u1gw4cbqKFrUUMH9rK/Hj9/6N8DiuEPPyVxzVtTuJkC
#
#
#template.refresh-interval=5
#
#kael.sequence.url=**********:2181
#kael.sequence.namespace=smsv2
#kael.sequence.env=dev
#
#kael.mq.producers=1300455117/test1/sms-channelReceipt
#kael.mq.consumers=1300455117/test1/sms-channelReceipt
##kael.mq.producers=1300455117/dev/sms-channelReceipt-dev
##kael.mq.consumers=1300455117/dev/sms-channelReceipt-dev
#
#logging.level.root= INFO
##cmppåæ§åå¥å®æ¶ä»»å¡
#elastic-job.common-config.overwrite=true
#elastic-job.reg-config.server-lists=**********:2181
#elastic-job.jobs.msgInsertCmppReceiptJob.cron=0 */2 * * * ?
#elastic-job.jobs.msgInsertCmppReceiptJob.sharding-total-count=1
#elastic-job.jobs.msgInsertCmppReceiptJob.sharding-item-parameters=0=A
#elastic-job.jobs.msgInsertCmppReceiptJob.disabled=false
#elastic-job.jobs.msgInsertCmppReceiptJob.description="msgInsertCmppReceiptJob"
#
#elastic-job.jobs.smsInsertDetailJob.cron=0 0 */1 * * ?
#elastic-job.jobs.smsInsertDetailJob.sharding-total-count=1
#elastic-job.jobs.smsInsertDetailJob.sharding-item-parameters=0=A
#elastic-job.jobs.smsInsertDetailJob.disabled=false
#elastic-job.jobs.smsInsertDetailJob.description="smsInsertDetailJob"
#ç¯å¢éç½®
venus.stack=dev1
venus.meta=http://**********:8080,http://*********:8080
venus.appId=spectre-receipthttp
venus.env=DEV


#spring.redis.database=0
## Redisæå¡å¨å°å
#spring.redis.host=redisdev1.xhdev.xyz
## Redisæå¡å¨è¿æ¥ç«¯å£
#spring.redis.port=6379
## é¾æ¥è¶æ¶æ¶é´ åä½ msï¼æ¯«ç§ï¼
#spring.redis.timeout=3000
################# Redis çº¿ç¨æ± è®¾ç½® ##############
## è¿æ¥æ± æå¤§è¿æ¥æ°ï¼ä½¿ç¨è´å¼è¡¨ç¤ºæ²¡æéå¶ï¼ é»è®¤ 8
#spring.redis.lettuce.pool.max-active=8
## è¿æ¥æ± æå¤§é»å¡ç­å¾æ¶é´ï¼ä½¿ç¨è´å¼è¡¨ç¤ºæ²¡æéå¶ï¼ é»è®¤ -1
#spring.redis.lettuce.pool.max-wait=-1
## è¿æ¥æ± ä¸­çæå¤§ç©ºé²è¿æ¥ é»è®¤ 8
#spring.redis.lettuce.pool.max-idle=8
## è¿æ¥æ± ä¸­çæå°ç©ºé²è¿æ¥ é»è®¤ 0
#spring.redis.lettuce.pool.min-idle=0
#
#spring.application.name=smsv2-receipt
#cmpp.channel.platform=Tencent,miaoXin,kaiFeng
#sms.detail.num=1
#server:
#port: 8080
#servlet:
#context-path: /smsv2-receipt


