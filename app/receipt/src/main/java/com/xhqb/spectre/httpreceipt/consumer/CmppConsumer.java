package com.xhqb.spectre.httpreceipt.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.sequencegenerator.DistributedSequence;
import com.xhqb.spectre.channel.http.context.ChannelContext;
import com.xhqb.spectre.common.dal.entity.SmsReceiptDO;
import com.xhqb.spectre.httpreceipt.receiptDto.dto.CmppReceiptBean;
import com.xhqb.spectre.httpreceipt.service.DeliveryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
public class CmppConsumer {

    @Autowired
    DeliveryService deliveryService;

    @Autowired
    ChannelContext channelContext;

    @Autowired
    private DistributedSequence primaryKeyGenerator;

    //spectre-xhcmpp-deliver
    //@MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[1]}", clazz = String.class)
    public void cmppDeliveryConsumer(String mqMessage) {
        try {
            CmppReceiptBean bean = JSONObject.parseObject(mqMessage, CmppReceiptBean.class);
            SmsReceiptDO smsReceiptDO = parseMessage(bean);
            deliveryService.updateReceipt(smsReceiptDO);
        } catch (Exception e) {
            log.error("cmpp回执接收失败;", e);
        }
    }

    private SmsReceiptDO parseMessage(CmppReceiptBean bean) {

        Date date = new Date();
        SmsReceiptDO smsReceiptDO = new SmsReceiptDO();
        return smsReceiptDO;
    }
}
