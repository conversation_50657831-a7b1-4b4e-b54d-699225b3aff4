package com.xhqb.spectre.httpreceipt.service;


import com.xhqb.spectre.common.dal.entity.ChannelAccountDO;
import com.xhqb.spectre.httpreceipt.receiptDto.request.ReceiptRequest;
import com.xhqb.spectre.httpreceipt.receiptDto.result.ReceiptResult;

public interface ReceIptService {

    ReceiptResult quereypartnerByPartnerName(ReceiptRequest receiptRequest);

    ReceiptResult insertMsgSendReceipt(ReceiptRequest receiptRequest);

    ReceiptResult insertBatchMsgSendReceipt(ReceiptRequest receiptRequest);

    ReceiptResult batchSaveMsgSendReceip(ReceiptRequest receiptRequest);

    ChannelAccountDO queryPartnerType(String msgId, String partnerName);
}
