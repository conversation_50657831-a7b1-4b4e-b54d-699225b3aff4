//package com.xhqb.spectre.httpreceipt.job;
//
//import com.dangdang.ddframe.job.api.ShardingContext;
//import com.dangdang.ddframe.job.api.simple.SimpleJob;
//import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//@Component
//@Job("smsInsertDetailJob")
//public class SmsInsertDetailJob implements SimpleJob {
//
//    private static final Logger logger = LoggerFactory.getLogger(SmsInsertDetailJob.class);
//    @Value("${sms.detail.num}")
//    private int smsDetailNum;
//    @Autowired
//    private SmsDetailService smsDetailService;
//
//    @Override
//    public void execute(ShardingContext shardingContext) {
//        logger.info("短信详情入库job开始");
////        smsDetailService.inserSmsDeatilInfo(smsDetailNum);
//
//        logger.info("短信详情入库job结束");
//    }
//}
