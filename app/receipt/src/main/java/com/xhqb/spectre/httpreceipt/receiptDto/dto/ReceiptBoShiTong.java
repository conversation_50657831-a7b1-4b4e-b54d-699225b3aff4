package com.xhqb.spectre.httpreceipt.receiptDto.dto;

public class ReceiptBoShiTong {
    //批次号
    private String msgid;
    //发送号码
    private String dstphone;
    //状态：DELIVRD(成功),其他均为失败
    private String state;
    //状态返回时间，格式：yyyyMMddhhmmss
    private String logtime;
    //当前推送的是该长短信第几条
    private String pk_number;
    //长短信拆分总条数
    private String  pk_total;

    public String getMsgid() {
        return msgid;
    }

    public void setMsgid(String msgid) {
        this.msgid = msgid;
    }

    public String getDstphone() {
        return dstphone;
    }

    public void setDstphone(String dstphone) {
        this.dstphone = dstphone;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getLogtime() {
        return logtime;
    }

    public void setLogtime(String logtime) {
        this.logtime = logtime;
    }

    public String getPk_total() {
        return pk_total;
    }

    public void setPk_total(String pk_total) {
        this.pk_total = pk_total;
    }

    public String getPk_number() {
        return pk_number;
    }

    public void setPk_number(String pk_number) {
        this.pk_number = pk_number;
    }
}
