package com.xhqb.spectre.httpreceipt.receiptDto.request;


import com.xhqb.spectre.httpreceipt.receiptDto.dto.ReceiptDto;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

@Data
public class ReceiptRequest implements Serializable {
    private static final long serialVersionUID = 4024091619592312927L;

    private String channelCode;

    private Integer partnerId;

    private ReceiptDto receiptDto;

    private List<ReceiptDto> receiptDtoLisst;

    private String msgId;

    /**
     * 重写
     * @return
     */
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
