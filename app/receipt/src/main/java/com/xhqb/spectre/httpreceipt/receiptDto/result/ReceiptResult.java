package com.xhqb.spectre.httpreceipt.receiptDto.result;

import com.xhqb.spectre.common.dal.entity.ChannelAccountDO;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReceiptResult implements Serializable {

    private static final long serialVersionUID = -5358257782041741740L;

    private ChannelAccountDO PartnerBase;

    private String message;

    private boolean success;

    public ReceiptResult(boolean success, String message){
        this.success=success;
        this.message=message;
    }
}
