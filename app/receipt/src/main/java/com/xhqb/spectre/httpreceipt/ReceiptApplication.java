package com.xhqb.spectre.httpreceipt;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * javadoc
 */
@SpringBootApplication
@ComponentScan("com.xhqb.spectre")
@MapperScan("com.xhqb.spectre.common.dal")
@EnableAutoConfiguration(exclude={DataSourceAutoConfiguration.class})
public class ReceiptApplication {

    public static void main(String[] args) {
        SpringApplication.run(ReceiptApplication.class, args);
    }
}
