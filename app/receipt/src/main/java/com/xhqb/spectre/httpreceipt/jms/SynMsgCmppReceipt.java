//package com.xhqb.spectre.httpreceipt.jms;
//
//import com.alibaba.fastjson.JSONObject;
//import com.xhqb.kael.mq.MQMessage;
//import com.xhqb.kael.mq.annotation.MQConsumer;
//import com.xhqb.spectre.common.enums.MsgSendResultEnum;
//import com.xhqb.spectre.common.exception.MsgSenderException;
//import com.xhqb.spectre.httpreceipt.receiptDto.dto.CmppReceiptBean;
//import com.xhqb.spectre.httpreceipt.receiptDto.dto.ReceiptDto;
//import com.xhqb.spectre.httpreceipt.receiptDto.request.ReceiptRequest;
//import com.xhqb.spectre.httpreceipt.receiptDto.result.ReceiptResult;
//import com.xhqb.spectre.httpreceipt.service.ReceIptService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.InitializingBean;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StringUtils;
//
//import java.util.Date;
//
///**
// * cmpp短信回执
// */
//@Component
//public class SynMsgCmppReceipt implements InitializingBean {
//
//    private static final Logger logger = LoggerFactory.getLogger(SynMsgCmppReceipt.class);
//    @Autowired
//    private ReceIptService receiptService;
//    @Autowired
//    private RedisTemplate redisTemplate;
//
//    private static final String SQS_SMS_TENCNET_RECEIPT = "SQS_SMS_TENCNET_RECEIPT";
//
//
//
//    @MQConsumer(topic= "#{'${kael.mq.consumers:}'.split(',')[0]}", clazz=String.class)
//    public void execute(MQMessage<String> mqMessage){
//        if (mqMessage == null) {
//            logger.info("cmpp回执任务消息为空");
//            return;
//        }
//        String jmsMessageId = mqMessage.getKey();
//        logger.info("输出jmsMessageId：{}", jmsMessageId);
//        logger.info("进入cmpp回执任务，消息参数：{}", mqMessage.getValue());
//        /*if (CommonRedisUtil.redisSingleValueIsExist(redisTemplate.opsForValue(), jmsMessageId, SQS_SMS_TENCNET_RECEIPT)) {
//            logger.info("CallBackClueService repeat request...msgacknowledge");
//            return;
//        }*/
//        try {
//
//            CmppReceiptBean tencentReceiptBean = JSONObject.parseObject(mqMessage.getValue(), CmppReceiptBean.class);
//            if (StringUtils.isEmpty(tencentReceiptBean.getAddition())) {
//                throw new MsgSenderException(MsgSendResultEnum.MISS_PARAM, "addition不能为空");
//            }
//            if (StringUtils.isEmpty(tencentReceiptBean.getPhone())) {
//                throw new MsgSenderException(MsgSendResultEnum.MISS_PARAM, "phone不能为空");
//            }
//            if (StringUtils.isEmpty(tencentReceiptBean.getOperatorStatus())) {
//                throw new MsgSenderException(MsgSendResultEnum.MISS_PARAM, "operatorStatus不能为空");
//            }
//            if (StringUtils.isEmpty(tencentReceiptBean.getPlatformStatus())) {
//                throw new MsgSenderException(MsgSendResultEnum.MISS_PARAM, "platformStatus不能为空");
//            }
//            if (StringUtils.isEmpty(tencentReceiptBean.getPartnerPlatform())) {
//                throw new MsgSenderException(MsgSendResultEnum.MISS_PARAM, "PartnerPlatform不能为空");
//            }
//            if (StringUtils.isEmpty(tencentReceiptBean.getMsgIndex())) {
//                throw new MsgSenderException(MsgSendResultEnum.MISS_PARAM, "MsgIndex不能为空");
//            }
//
//            ReceiptRequest receiptRequest = tencnetRequestPar(tencentReceiptBean);
//            ReceiptResult receiptResult = receiptService.insertMsgSendReceipt(receiptRequest);
//            if (!receiptResult.isSuccess()) {
//                logger.info("短信回执保存失败了");
//            }
//        } catch (Exception ex) {
//           logger.error("cmpp短信回执消息处理异常了:{}", ex.getMessage());
//        }
//
//    }
//
//
//    public ReceiptRequest tencnetRequestPar(CmppReceiptBean tencentReceiptBean){
//        ReceiptRequest receiptRequest = new ReceiptRequest();
//        ReceiptDto dto = new ReceiptDto();
//        //JSONObject jsonObject = JSONObject.parseObject(tencentReceiptBean.getAddition());
//        //dto.setMsgId(jsonObject.getString("msgId"));
//        dto.setMsgId(tencentReceiptBean.getAddition());
//        //如是长短信将存多条
//        dto.setLength(1);
//        dto.setMobile(tencentReceiptBean.getPhone());
//        dto.setOperatorStatus(tencentReceiptBean.getOperatorStatus());
//        dto.setPartnerStatus(tencentReceiptBean.getPlatformStatus());
//        dto.setOperatorReportTime(new Date());
//        dto.setPartnerReportTime(new Date());
//        dto.setCmppMsgIndex(tencentReceiptBean.getMsgIndex());
//        dto.setSendTime(new Date());
//        dto.setSendCode(tencentReceiptBean.getPlatformStatus());
//        receiptRequest.setReceiptDto(dto);
//        receiptRequest.setChannelCode(tencentReceiptBean.getPartnerPlatform());
//        return receiptRequest;
//    }
//
//    @Override
//    public void afterPropertiesSet() throws Exception {
//
//    }
//}
