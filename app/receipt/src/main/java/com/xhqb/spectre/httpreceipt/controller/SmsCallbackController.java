//package com.xhqb.spectre.httpreceipt.controller;
//
//import com.alibaba.fastjson.JSONObject;
//import com.xhqb.spectre.common.dal.entity.ChannelAccountDO;
//import com.xhqb.spectre.common.enums.ExchangeProviderEnum;
//import com.xhqb.spectre.httpreceipt.receiptDto.dto.ReceiptBoShiTong;
//import com.xhqb.spectre.httpreceipt.receiptDto.dto.ReceiptDto;
//import com.xhqb.spectre.httpreceipt.receiptDto.dto.ReceiptSantong;
//import com.xhqb.spectre.httpreceipt.receiptDto.request.ReceiptRequest;
//import com.xhqb.spectre.httpreceipt.receiptDto.result.ReceiptResult;
//import com.xhqb.spectre.httpreceipt.service.ReceIptService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.util.CollectionUtils;
//import org.springframework.web.bind.annotation.*;
//
//import java.text.ParseException;
//import java.text.SimpleDateFormat;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//@RestController
//@RequestMapping("/sms/v2")
//public class SmsCallbackController {
//
//    private final static Logger logger = LoggerFactory.getLogger(SmsCallbackController.class);
//
//    private static final String CHUANGLAN_RECEIPT_SUCCESS_CODE = "DELIVRD";
//
//    private static final String CHUANGLAN_SMS_RECEIPT_RESULT_CODE = "clcode";
//
//    private static final String CHUANGLAN_SMS_RECEIPT_RESULT_SUCCESS_MSG = "000000";
//
//    private static final String CHUANGLAN_SMS_RECEIPT_RESULT_FAIL_MSG = "111111";
//
//    private static final String SANTONG_SMS_RECEIPT_PLATFORM_FAIL_CODE = "1";
//
//    private static final String SMS_RECEIPT_DEFAULT_LENGTH = "1";
//
//    private static final String SANTONG_SMS_RECEIPT_RESULT_CODE = "status";
//
//    private static final String SANTONG_SMS_RECEIPT_RESULT_SUCCESS_MSG = "success";
//
//    private static final String SANTONG_SMS_RECEIPT_RESULT_FAIL_MSG = "fail";
//
//    private static final String COMMON_SMS_RECEIPT_RESULT_SUCCESS_MSG = "success";
//
//    private static final String COMMON_SMS_RECEIPT_RESULT_FAIL_MSG = "fail";
//
//    @Autowired
//    private ReceIptService receIptService;
//
//
//    @PostMapping(value = "/callback/{channelCode}")
//    @ResponseBody
//    public String postReceipt(@PathVariable String channelCode, @RequestParam Map<String,Object> paramRequest){
//
//        return recpitPartners(channelCode, paramRequest);
//    }
//    @GetMapping(value = "/callback/{channelCode}")
//    @ResponseBody
//    public String getReceipt(@PathVariable String channelCode, @RequestParam Map<String,Object> paramRequest){
//
//        return recpitPartners(channelCode, paramRequest);
//    }
//    public String recpitPartners(String channelCode, Map<String,Object> paramRequest){
//
//        String sendReceiptResult = null;
//        if (ExchangeProviderEnum.CHUANGLAN.getProviderName().equals(channelCode)){
//            ReceiptRequest receiptRequest = getMsgSendReceiptRequestFromChuanglan(paramRequest, channelCode);
//            if (null == receiptRequest) {
//                Map<String, Object> map = new HashMap<>();
//                map.put(CHUANGLAN_SMS_RECEIPT_RESULT_CODE, CHUANGLAN_SMS_RECEIPT_RESULT_FAIL_MSG);
//                JSONObject json = new JSONObject(map);
//                return json.toJSONString();
//            }
//            ReceiptResult receiptResult = receIptService.insertMsgSendReceipt(receiptRequest);
//            sendReceiptResult = getResultForChuanglan(receiptResult);
//        } else if (ExchangeProviderEnum.SANTONG.getProviderName().equals(channelCode)){
//            ReceiptRequest msgSendReceiptRequest = getMsgSendReceiptRequestFromSantong(paramRequest, channelCode);
//            if (msgSendReceiptRequest != null) {
//                ReceiptResult receiptResult = receIptService.insertBatchMsgSendReceipt(msgSendReceiptRequest);
//                sendReceiptResult = getResultForSantong(receiptResult);
//            }
//        } else if(ExchangeProviderEnum.BOSHITONG.getProviderName().equals(channelCode)){
//            ReceiptRequest msgSendReceiptRequest = getMsgSendReceiptRequestFromBoShiTong(paramRequest, channelCode);
//            if (msgSendReceiptRequest != null){
//                ReceiptResult receiptResult = receIptService.insertBatchMsgSendReceipt(msgSendReceiptRequest);
//                sendReceiptResult = getResultForBoShiTong(receiptResult);
//            }
//        } else {
//            logger.error("未知渠道, channelCode={}, paramRequest={}", channelCode, paramRequest.toString());
//            sendReceiptResult = COMMON_SMS_RECEIPT_RESULT_FAIL_MSG;
//        }
//        return sendReceiptResult;
//    }
//    private ReceiptRequest getMsgSendReceiptRequestFromChuanglan(Map<String,Object> paramRequest, String channelCode) {
//        logger.info("创蓝回执参数:{}", paramRequest.toString());
//        // 创蓝回执接口文档店址：https://zz.253.com/api_doc/kai-fa-yin-dao.html
//        ReceiptRequest receiptRequest = new ReceiptRequest();
//        ReceiptDto sendReceipt = new ReceiptDto();
//        sendReceipt.setMsgId((String)paramRequest.get("msgid"));
//        String reportTime = (String) paramRequest.get("reportTime");
//        SimpleDateFormat reportTimeSdf = new SimpleDateFormat( "yyMMddHHmm" );
//
//        try {
//            //运营商时间
//            sendReceipt.setOperatorReportTime(reportTimeSdf.parse(reportTime));
//            sendReceipt.setSendTime(reportTimeSdf.parse(reportTime));
//        } catch (ParseException e) {
//            logger.warn("chuanglan date parse error,reportTime:{}", reportTime);
//        }
//        String notifyTime = (String) paramRequest.get("notifyTime");
//        SimpleDateFormat notifyTimeSdf = new SimpleDateFormat( "yyMMddHHmmss" );
//        try {
//            //供应商时间
//            sendReceipt.setPartnerReportTime(notifyTimeSdf.parse(notifyTime));
//        } catch (ParseException e) {
//            logger.warn("chuanglan date parse error,notifyTime:{}", notifyTime);
//        }
//        sendReceipt.setMobile((String) paramRequest.get("mobile"));
//        String status = (String)paramRequest.get("status");
//        sendReceipt.setSendCode((String)paramRequest.get("status"));
//
//        if (CHUANGLAN_RECEIPT_SUCCESS_CODE.equals(status)){
//            sendReceipt.setPartnerStatus(status);
//        }else {
//            sendReceipt.setOperatorStatus(status);
//        }
//        sendReceipt.setLength(Integer.valueOf((String)paramRequest.get("length")));
//
//        receiptRequest.setChannelCode(channelCode);
//        ChannelAccountDO partnerBase = queryPartnerId(paramRequest.get("msgid").toString(), channelCode);
//        if (null == partnerBase) {
//            logger.warn("通过msgId未能查询到供应商ID,msgId:{},channelCode:{} ",paramRequest.get("msgid").toString(), channelCode);
//            return null;
//        }
//        sendReceipt.setPartnerId(partnerBase.getId());
//        receiptRequest.setReceiptDto(sendReceipt);
//        return receiptRequest;
//    }
//
//    private String getResultForChuanglan(ReceiptResult result){
//        Map<String, Object> map = new HashMap<>();
//        if (result.isSuccess()){
//            map.put(CHUANGLAN_SMS_RECEIPT_RESULT_CODE, CHUANGLAN_SMS_RECEIPT_RESULT_SUCCESS_MSG);
//        }else {
//            map.put(CHUANGLAN_SMS_RECEIPT_RESULT_CODE, CHUANGLAN_SMS_RECEIPT_RESULT_FAIL_MSG);
//        }
//        JSONObject json = new JSONObject(map);
//        return json.toJSONString();
//    }
//
//    private ReceiptRequest getMsgSendReceiptRequestFromSantong(Map<String,Object> paramRequest, String channelCode){
//        ReceiptRequest receiptRequest = null;
//        try {
//            ReceiptSantong receiptSantong = JSONObject.parseObject((String) paramRequest.get("report"), ReceiptSantong.class);
//            logger.info("三通回执参数:{}", receiptSantong.toString());
//            List<ReceiptSantong.ReportSantong> reportSantongs = receiptSantong.getReports();
//            if (CollectionUtils.isEmpty(reportSantongs)) {
//                logger.warn("report of santong is empty");
//                return receiptRequest;
//            }
//            receiptRequest = new ReceiptRequest();
//            receiptRequest.setChannelCode(channelCode);
//           /* ReceiptResult receiptResult = receIptService.quereypartnerByPartnerName(receiptRequest);
//            if(!receiptResult.isSuccess()) {
//                logger.error("短信回执根据code查询通道失败，【回执保存失败--】，channelCode：【{}】", channelCode);
//                return null;
//            }*/
//
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            List<ReceiptDto> list = reportSantongs.stream().map(s -> {
//                ReceiptDto sendReceiptDto = new ReceiptDto();
//                sendReceiptDto.setMsgId(s.getMsgId());
//                sendReceiptDto.setMobile(s.getPhone());
//                sendReceiptDto.setStatus(s.getStatus());
//                sendReceiptDto.setSendCode(s.getStatus());
//                ChannelAccountDO partnerBase = queryPartnerId(s.getMsgId(),channelCode);
//                if (null == partnerBase) {
//                    logger.warn("通过msgId未能查询到供应商ID,msgId:{},channelCode:{} ",s.getMsgId(), channelCode);
//                }
//                if (SANTONG_SMS_RECEIPT_PLATFORM_FAIL_CODE.equals(s.getStatus())) {
//                    sendReceiptDto.setPartnerStatus(s.getWgcode());
//                } else {
//                    sendReceiptDto.setOperatorStatus(s.getWgcode());
//                }
//                sendReceiptDto.setPartnerId(partnerBase.getId());
//                try {
//                    sendReceiptDto.setOperatorReportTime(simpleDateFormat.parse(s.getTime()));
//                    sendReceiptDto.setSendTime(simpleDateFormat.parse(s.getSendTime()));
//                } catch (ParseException e) {
//                    logger.warn("santong date parse error,date:{}", s.getTime());
//                }
//                sendReceiptDto.setLength(Integer.valueOf(SMS_RECEIPT_DEFAULT_LENGTH));
//                sendReceiptDto.setMsgIndex(s.getSmsIndex());
//                return sendReceiptDto;
//            }).collect(Collectors.toList());
//            receiptRequest.setReceiptDtoLisst(list);
//
//        } catch (Exception e) {
//            logger.error("santong json error, json:{}", paramRequest.get("report"));
//            logger.error("Exception:{}",e.getMessage());
//        }
//        return receiptRequest;
//    }
//
//    private String getResultForSantong(ReceiptResult result){
//        Map<String, Object> map = new HashMap<>();
//        if (result.isSuccess()){
//            map.put(SANTONG_SMS_RECEIPT_RESULT_CODE, SANTONG_SMS_RECEIPT_RESULT_SUCCESS_MSG);
//        }else {
//            map.put(SANTONG_SMS_RECEIPT_RESULT_CODE, SANTONG_SMS_RECEIPT_RESULT_FAIL_MSG);
//        }
//        JSONObject json = new JSONObject(map);
//        return json.toJSONString();
//    }
//
//
//    private ReceiptRequest getMsgSendReceiptRequestFromBoShiTong(Map<String,Object> paramRequest, String channelCode) {
//        ReceiptRequest receiptRequest = null;
//        //String contentStr = JSON.toJSONString(paramRequest.get("content"));
//        List<ReceiptBoShiTong> boShiTongList = JSONObject.parseArray((String) paramRequest.get("content"),  ReceiptBoShiTong.class);
//        logger.info("博士通回执参数:{}", paramRequest.get("content").toString());
//        if (CollectionUtils.isEmpty(boShiTongList)) {
//            logger.error("report of boshitong is empty");
//            return  receiptRequest;
//        }
//        receiptRequest = new ReceiptRequest();
//        receiptRequest.setChannelCode(channelCode);
//
//
//        List<ReceiptDto> msgSendReceiptDtoList = boShiTongList.stream().map(receiptBoShiTong -> {
//            ReceiptDto msgSendReceiptDto = new ReceiptDto();
//            msgSendReceiptDto.setMsgId(receiptBoShiTong.getMsgid());
//            ChannelAccountDO partnerBase = queryPartnerId(receiptBoShiTong.getMsgid(),channelCode);
//            if (null == partnerBase) {
//                logger.warn("通过msgId未能查询到供应商ID,msgId:{},channelCode:{} ",receiptBoShiTong.getMsgid(), channelCode);
//            }
//            msgSendReceiptDto.setLength(Integer.valueOf(SMS_RECEIPT_DEFAULT_LENGTH));//默认条数
//            msgSendReceiptDto.setMsgIndex(Integer.valueOf(receiptBoShiTong.getPk_number()));//当前条数
//            msgSendReceiptDto.setStatus(receiptBoShiTong.getState());
//            msgSendReceiptDto.setSendCode(receiptBoShiTong.getState());
//            msgSendReceiptDto.setMobile(receiptBoShiTong.getDstphone());
//            msgSendReceiptDto.setPartnerId(partnerBase.getId());
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
//            try {
//                msgSendReceiptDto.setOperatorReportTime(simpleDateFormat.parse(receiptBoShiTong.getLogtime()));
//                msgSendReceiptDto.setSendTime(simpleDateFormat.parse(receiptBoShiTong.getLogtime()));
//            } catch (ParseException e) {
//                logger.warn("xuanwu date parse error,date:{}", receiptBoShiTong.getLogtime());
//            }
//            return msgSendReceiptDto;
//        }).collect(Collectors.toList());
//        receiptRequest.setReceiptDtoLisst(msgSendReceiptDtoList);
//
//        //receiptRequest.setChannelCode(channelCode);
//        return receiptRequest;
//    }
//    private String getResultForBoShiTong(ReceiptResult result){
//        if (result.isSuccess()){
//            return COMMON_SMS_RECEIPT_RESULT_SUCCESS_MSG;
//        } else {
//            return COMMON_SMS_RECEIPT_RESULT_FAIL_MSG;
//        }
//    }
//
//    /**
//     * 查询供应商id
//     * @param msgId
//     * @param partnerName
//     * @return
//     */
//    public ChannelAccountDO queryPartnerId(String msgId, String partnerName){
//        ChannelAccountDO partnerBase = receIptService.queryPartnerType(msgId,partnerName);
//        return partnerBase;
//    }
//}
