package com.xhqb.spectre.httpreceipt.controller;

import com.google.common.collect.ImmutableMap;
import com.xhqb.spectre.common.enums.ExchangeProviderEnum;
import com.xhqb.spectre.httpreceipt.service.DeliveryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/v3")
@Slf4j
public class CallbackController {

    @Autowired
    private DeliveryService deliveryService;

    @PostMapping(value = "/callback/shanyun")
    @ResponseBody
    public ImmutableMap<String, Object> postReceipt(@RequestParam Map<String, Object> params) {
        log.info("收到闪云回执请求; params={}", params);
        Boolean bool = deliveryService.receive(ExchangeProviderEnum.SHANYUN.getProviderName(), params);
        return ImmutableMap.of("statusReturn", bool.toString());
    }
}
