package com.xhqb.spectre.httpreceipt.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.sequencegenerator.DistributedSequence;
import com.xhqb.spectre.channel.http.context.ChannelContext;
import com.xhqb.spectre.common.dal.entity.HttpRecord;
import com.xhqb.spectre.common.dal.entity.SmsReceiptDO;
import com.xhqb.spectre.httpreceipt.service.DeliveryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Slf4j
public class HttpConsumer {

    @Autowired
    DeliveryService deliveryService;

    @Autowired
    ChannelContext channelContext;

    @Autowired
    private DistributedSequence primaryKeyGenerator;

    //spectre-send-deliver
    //@MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[0]}", clazz = String.class)
    public void httpDeliveryConsumer(String mqMessage) {
        try {
            HttpRecord httpRecord = JSONObject.parseObject(mqMessage, HttpRecord.class);
            SmsReceiptDO smsReceiptDO = parseMessage(httpRecord);
            deliveryService.updateReceipt(smsReceiptDO);
        } catch (Exception e) {
            log.error("http回执接收失败;", e);
        }
    }

    private SmsReceiptDO parseMessage(HttpRecord httpRecord) {

        Date date = new Date();
        SmsReceiptDO smsReceiptDO = new SmsReceiptDO();
        return smsReceiptDO;
    }
}
