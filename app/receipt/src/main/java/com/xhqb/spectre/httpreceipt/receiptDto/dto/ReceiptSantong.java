package com.xhqb.spectre.httpreceipt.receiptDto.dto;

import com.xhqb.kael.util.tostring.XhJsonToStringStyle;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.List;

public class ReceiptSantong {

    private String result;

    private String desc;

    private List<ReportSantong> reports;

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<ReportSantong> getReports() {
        return reports;
    }

    public void setReports(List<ReportSantong> reports) {
        this.reports = reports;
    }

    /**
     * report
     */
    public class ReportSantong {

        private String msgId;

        private String phone;

        private String status;

        private String desc;

        private String wgcode;

        private String time;

        private String sendTime;

        private int smsCount;

        private int smsIndex;

        public String getMsgId() {
            return msgId;
        }

        public void setMsgId(String msgId) {
            this.msgId = msgId;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getWgcode() {
            return wgcode;
        }

        public void setWgcode(String wgcode) {
            this.wgcode = wgcode;
        }

        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        public int getSmsCount() {
            return smsCount;
        }

        public void setSmsCount(int smsCount) {
            this.smsCount = smsCount;
        }

        public int getSmsIndex() {
            return smsIndex;
        }

        public String getSendTime() {
            return sendTime;
        }

        public void setSendTime(String sendTime) {
            this.sendTime = sendTime;
        }

        public void setSmsIndex(int smsIndex) {
            this.smsIndex = smsIndex;
        }



        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this, XhJsonToStringStyle.XH_JSON_STYLE);
        }
    }
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, XhJsonToStringStyle.XH_JSON_STYLE);
    }
}
