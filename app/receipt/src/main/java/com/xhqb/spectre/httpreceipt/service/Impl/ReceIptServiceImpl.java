//package com.xhqb.spectre.httpreceipt.service.Impl;
//
//import com.xhqb.kael.sequencegenerator.DistributedSequence;
//import com.xhqb.spectre.common.dal.entity.ChannelAccountDO;
//import com.xhqb.spectre.common.dal.entity.SendReceiptDO;
//import com.xhqb.spectre.common.dal.entity.TplDO;
//import com.xhqb.spectre.common.dal.mapper.ChannelAccountMapper;
//import com.xhqb.spectre.common.dal.mapper.SendReceiptMapper;
//import com.xhqb.spectre.common.dal.mapper.TplMapper;
//import com.xhqb.spectre.common.enums.ExchangeProviderEnum;
//import com.xhqb.spectre.common.enums.MsgSendReceiptStatusEnum;
//import com.xhqb.spectre.common.enums.ResultEnum;
//import com.xhqb.spectre.httpreceipt.receiptDto.dto.ReceiptDto;
//import com.xhqb.spectre.httpreceipt.receiptDto.request.ReceiptRequest;
//import com.xhqb.spectre.httpreceipt.receiptDto.result.ReceiptResult;
//import com.xhqb.spectre.httpreceipt.service.ReceIptService;
//import com.xhqb.spectre.service.util.CommonRedisUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.data.redis.core.ListOperations;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.stereotype.Service;
//import org.springframework.util.StringUtils;
//
//import java.util.ArrayList;
//import java.util.Iterator;
//import java.util.List;
//
//@Service
//public class ReceIptServiceImpl implements ReceIptService {
//
//    private final static Logger logger = LoggerFactory.getLogger(ReceIptServiceImpl.class);
//
//    private static final String SYSTEM_NAME_PREFIX = "sms";
//
//    private static final String CALLBACK_RECEIPT = "callback_receipt";
//
//    private static final String REDIS_KEY_SEPARATOR = ":";
//
//    private static final String CHUANGLAN_RECEIPT_SUCCESS_CODE = "DELIVRD";
//    private static final String BOSHITONG_RECEIPT_SUCCESS_CODE = "DELIVRD";
//    private static final String TENCNET_SUCCESS_CODE = "DELIVRD";
//    @Autowired
//    private DistributedSequence primaryKeyGenerator;
////    @Autowired
////    private StatusCodeMapper statusCodeMapper;
//
//    @Autowired
//    private TplMapper templateMapper;
//
//    private static final String CREATOR = "SYSTEM";
//
//    private static final String MODIFIER = "SYSTEM";
//
//    private static final String REDIS_KEY_CMPP = "batch:sms:cmpp:receipt";
//    /**
//     * 玄武成功
//     */
//    private static final String XUANWU_RECEIPT_SUCCESS_CODE = "0";
//    /**
//     * 三通成功
//     */
//    private static final String SANTONG_RECEIPT_SUCCESS_STATUS_CODE = "0";
//    /**
//     * 三通供应商失败
//     */
//    private static final String SANTONG_RECEIPT_PARTNER_FAIL_CODE = "1";
//
//    private static final String BOSHITONG_PARTNER_PLATFORM_STATUS_SIGN = "ERR-";
//
//    @Autowired
//    private SendReceiptMapper sendReceiptMapper;
//
//    @Value("#{'${cmpp.channel.platform}'.split(',')}")
//    private List<String> cmppChannelPlatform;
//    @Autowired
//    private RedisTemplate redisTemplate;
//
//    @Autowired
//    private ChannelAccountMapper channelAccountMapper;
//    @Override
//    public ReceiptResult quereypartnerByPartnerName(ReceiptRequest receiptRequest) {
//        ReceiptResult receiptResult = new ReceiptResult(true, ResultEnum.SUCCUSS_RESPONSE.getDescription());
//
//        try {
//            if (StringUtils.isEmpty(receiptRequest.getChannelCode())) {
//                logger.error("查询供应商信息的参数缺失：{}", receiptRequest.toString());
//                return new ReceiptResult(false, ResultEnum.PARAMETER_LACK.getDescription());
//            }
//            ChannelAccountDO partnerBase = channelAccountMapper.queryPartnerByName(receiptRequest.getChannelCode(),null);
//            if (null == partnerBase) {
//                logger.error("通过合作商名称没有查询到数据，参数：{}", receiptRequest.toString());
//                return new ReceiptResult(false, ResultEnum.DATA_EMPTY.getDescription());
//            }
//            receiptResult.setPartnerBase(partnerBase);
//        } catch (Exception ex) {
//            logger.error("查询供应商异常了:{}", ex.getMessage());
//            return new ReceiptResult(false, ResultEnum.SYSTEM_FAILUER.getDescription());
//        }
//        return receiptResult;
//    }
//
//    @Override
//    public ReceiptResult insertMsgSendReceipt(ReceiptRequest receiptRequest) {
//
//        ReceiptResult receiptResult = new ReceiptResult(true, ResultEnum.SAVE_SUCCUSS.getDescription());
//        try {
//            if (receiptRequest == null) {
//                return new ReceiptResult(false, ResultEnum.PARAMETER_LACK.getDescription());
//            }
//            logger.info("短信回执单条入库:{}", receiptRequest.toString());
//            String receiptRedisKey = getReceiptRedisKey(receiptRequest);
//            if (CommonRedisUtil.redisSingleValueIsExist(redisTemplate.opsForValue(), receiptRedisKey, 1)) {
//                logger.info("该回执请求正在处理中，request={}", receiptRequest.toString());
//                return new ReceiptResult(false, ResultEnum.REPEAT_REQ.getDescription());
//            }
//            ReceiptDto receiptDto = receiptRequest.getReceiptDto();
//            String xhStatusCode = getXhStatusCode(receiptDto, receiptRequest.getChannelCode());
//            if (xhStatusCode == null) {
//                logger.error("未知的回执状态, request={}", receiptRequest.toString());
//                return new ReceiptResult(false, ResultEnum.UNKNOWN_STATUS.getDescription());
//            }
//            SendReceiptDO sendReceiptDO = new SendReceiptDO();
//            if (cmppChannelPlatform.contains(receiptRequest.getChannelCode())) {
//                logger.info("cmpp参数构建准备发Q入库：{}", receiptRequest.toString());
//                ChannelAccountDO partnerBase = queryPartnerType(receiptRequest.getReceiptDto().getMsgId(), receiptRequest.getChannelCode());
//                if (null == partnerBase) {
//                    logger.warn("通过msgId未能查询到供应商ID,msgId:{},channelCode:{} ",receiptRequest.getMsgId(), receiptRequest.getChannelCode());
//                    return null;
//                }
//                //通过code获取运营商id
//                Integer partnerId = partnerBase.getId();
//                ReceiptDto dto = new ReceiptDto();
//                dto.createForInsert(primaryKeyGenerator.nextStr(ReceiptDto.SEQ_NAME),
//                        null,
//                        receiptDto.getMsgId(),
//                        receiptDto.getLength(),
//                        receiptDto.getOperatorReportTime(),
//                        receiptDto.getPartnerReportTime(),
//                        partnerId,
//                        receiptDto.getMobile(),
//                        xhStatusCode,
//                        receiptDto.getPartnerStatus(),
//                        receiptDto.getOperatorStatus(),
//                        receiptDto.getSendTime(),
//                        receiptDto.getSendCode()
//                );
//                ListOperations<String,Object> opsForList  = redisTemplate.opsForList();
//                opsForList.rightPush(REDIS_KEY_CMPP, dto);
//                return receiptResult;
//            }
//            sendReceiptDO.createForInsert(primaryKeyGenerator.nextStr(sendReceiptDO.SEQ_NAME),receiptDto.getMsgId(),
//                    receiptDto.getLength(),
//                    receiptDto.getOperatorReportTime(),
//                    receiptDto.getPartnerReportTime(),
//                    receiptDto.getPartnerId().toString(),
//                    receiptDto.getMobile(),
//                    xhStatusCode,
//                    receiptDto.getPartnerStatus(),
//                    receiptDto.getOperatorStatus(),
//                    receiptDto.getSendTime(),
//                    receiptDto.getSendCode());
//            sendReceiptMapper.insert(sendReceiptDO);
//        } catch (Exception ex) {
//            logger.error("短信回执保存异常了:{}", ex.getMessage());
//            return new ReceiptResult(false,ResultEnum.SYSTEM_FAILUER.getDescription());
//        }
//        return receiptResult;
//    }
//
//    @Override
//    public ReceiptResult insertBatchMsgSendReceipt(ReceiptRequest request) {
//        logger.info("短信回执批量入库参数：{}", request.toString());
//        ReceiptResult receiptResult = new ReceiptResult(true,"操纵成功");
//        try {
//            List<ReceiptDto> msgSendReceiptDtoList = request.getReceiptDtoLisst();
//            //过滤重复回执，并返回通过名单
//            filterRepeatReceipt(msgSendReceiptDtoList, request.getChannelCode());
//            //过滤后检查msgSendReceiptDtoList
//            if (msgSendReceiptDtoList == null || msgSendReceiptDtoList.isEmpty()) {
//                logger.info("该回执请求正在处理中，request={}", request.toString());
//                return new ReceiptResult(false, ResultEnum.REPEAT_REQ.getDescription());
//            }
//
//            //获取msgSendReceiptList
//            List<SendReceiptDO> msgSendReceiptDOList = getMsgSendReceiptList(msgSendReceiptDtoList, request.getChannelCode());
//            //保存日志
//            sendReceiptMapper.insertBatch(msgSendReceiptDOList);
//        } catch (Exception ex) {
//            logger.error("短信回执批量保存异常了:{}", ex.getMessage());
//            return new ReceiptResult(false, ResultEnum.SYSTEM_FAILUER.getDescription());
//        }
//
//        return receiptResult;
//    }
//
//    @Override
//    public ReceiptResult batchSaveMsgSendReceip(ReceiptRequest receiptRequest) {
//        ReceiptResult receiptResult = new ReceiptResult(true, ResultEnum.SAVE_SUCCUSS.getDescription());
//        try {
//            List<SendReceiptDO> msgSendReceiptDOList = new ArrayList<>();
//            for (ReceiptDto msgSendReceiptDto : receiptRequest.getReceiptDtoLisst()) {
//                SendReceiptDO msgSendReceiptDO = new SendReceiptDO();
//                BeanUtils.copyProperties(msgSendReceiptDto, msgSendReceiptDO);
//                msgSendReceiptDOList.add(msgSendReceiptDO);
//            }
//            sendReceiptMapper.insertBatch(msgSendReceiptDOList);
//        } catch (Exception ex) {
//            logger.error("批量保存回执异常了：{}", ex.getMessage());
//            return new ReceiptResult(false, ResultEnum.SYSTEM_FAILUER.getDescription());
//        }
//        return receiptResult;
//    }
//
//    @Override
//    public ChannelAccountDO queryPartnerType(String msgId,String partnerName) {
//
//        TplDO template = templateMapper.queryTemplateType(msgId);
//        if (null == template) {
//            logger.warn("通过msgid没有查询到相关log数据");
//            return new ChannelAccountDO();
//        }
//        ChannelAccountDO partnerBase = channelAccountMapper.queryPartnerByName(partnerName, template.getSmsTypeCode());
//        return partnerBase;
//    }
//
//
//    private String getXhStatusCode(ReceiptDto receiptDto, String partnerPlatform){
//        if (ExchangeProviderEnum.CHUANGLAN.getProviderName().equals(partnerPlatform)){
//            return getXhStatusCodeFromChuanglan(receiptDto);
//        }
//        if (ExchangeProviderEnum.SANTONG.getProviderName().equals(partnerPlatform)){
//            return getXhStatusCodeFromSantong(receiptDto);
//        }
//        if (ExchangeProviderEnum.XUANWU.getProviderName().equals(partnerPlatform)){
//            return getXhStatusCodeFromXuanwu(receiptDto);
//        }
//        if (ExchangeProviderEnum.BOSHITONG.getProviderName().equals(partnerPlatform)){
//            String xhStatus = getXhStatusCodeFromBoshitong(receiptDto);
//            if (MsgSendReceiptStatusEnum.SUCCESS.getCode().equals(xhStatus)
//                    || MsgSendReceiptStatusEnum.PARTNER_PLATFORM_FAIL.getCode().equals(xhStatus)){
//                receiptDto.setPartnerStatus(receiptDto.getStatus());
//            }else if (MsgSendReceiptStatusEnum.OPERATOR_PLATFORM_FAIL.getCode().equals(xhStatus)){
//                receiptDto.setOperatorStatus(receiptDto.getStatus());
//            }
//            return xhStatus;
//        }
//        if (cmppChannelPlatform.contains(partnerPlatform)) {
//            return getXhStatusFromTencent(receiptDto);
//        }
//        return null;
//    }
//
//
//    private String getReceiptRedisKey(ReceiptRequest request){
//        List list = new ArrayList<String>();
//        list.add(SYSTEM_NAME_PREFIX);
//        list.add(CALLBACK_RECEIPT);
//        list.add(request.getChannelCode());
//        ReceiptDto receiptDto = request.getReceiptDto();
//        list.add(receiptDto.getMsgId());
//        if (cmppChannelPlatform.contains(request.getChannelCode())) {
//            list.add(receiptDto.getCmppMsgIndex());
//        }
//        //如果长短信有下标，加入下标作为判断重复参数
//        if (receiptDto.getMsgIndex() != null){
//            list.add(receiptDto.getMsgIndex().toString());
//        }
//        list.add(receiptDto.getMobile());
//        return getRedisKey(list);
//    }
//
//    private String getRedisKey(List<String> list){
//        StringBuilder sb = new StringBuilder();
//        for (int i = 0; i < list.size(); i++) {
//            sb.append(list.get(i));
//            if (i < list.size() - 1) {
//                sb.append(REDIS_KEY_SEPARATOR);
//            }
//        }
//        return sb.toString();
//    }
//
//    private String getXhStatusCodeFromChuanglan(ReceiptDto receiptDto){
//        if (CHUANGLAN_RECEIPT_SUCCESS_CODE.equals(receiptDto.getPartnerStatus())){
//            return MsgSendReceiptStatusEnum.SUCCESS.getCode();
//        }else {
//            return MsgSendReceiptStatusEnum.OPERATOR_PLATFORM_FAIL.getCode();
//        }
//    }
//
//    private String getXhStatusCodeFromSantong(ReceiptDto dto){
//        if (SANTONG_RECEIPT_SUCCESS_STATUS_CODE.equals(dto.getStatus())){
//            return MsgSendReceiptStatusEnum.SUCCESS.getCode();
//        } else if (SANTONG_RECEIPT_PARTNER_FAIL_CODE.equals(dto.getStatus())){
//            return MsgSendReceiptStatusEnum.PARTNER_PLATFORM_FAIL.getCode();
//        } else {
//            return MsgSendReceiptStatusEnum.OPERATOR_PLATFORM_FAIL.getCode();
//        }
//    }
//
//    /**
//     * 根据玄武返回状态判断XhStatus
//     * 玄武只有供应商状态码,要查询具体运营商返回状态需联系玄武
//     * @param dto
//     * @return
//     */
//    private String getXhStatusCodeFromXuanwu(ReceiptDto dto){
//        if (XUANWU_RECEIPT_SUCCESS_CODE.equals(dto.getPartnerStatus())){
//            return MsgSendReceiptStatusEnum.SUCCESS.getCode();
//        } else {
//            return MsgSendReceiptStatusEnum.PARTNER_PLATFORM_FAIL.getCode();
//        }
//    }
//    private String getXhStatusCodeFromBoshitong(ReceiptDto dto){
//        String status = dto.getStatus();
//        if (BOSHITONG_RECEIPT_SUCCESS_CODE.equals(status)){
//            return MsgSendReceiptStatusEnum.SUCCESS.getCode();
//        }
//        if (status.contains(BOSHITONG_PARTNER_PLATFORM_STATUS_SIGN)){//博士通自己定义的状态码都是BST开头
//            return MsgSendReceiptStatusEnum.PARTNER_PLATFORM_FAIL.getCode();
//        } else {
//            return MsgSendReceiptStatusEnum.OPERATOR_PLATFORM_FAIL.getCode();
//        }
//    }
//
//    private String getXhStatusFromTencent(ReceiptDto dto){
//        //供应商状态 成功状态：0
//        String tencentStatus  = dto.getPartnerStatus();
//        //运营商状态 成功：DELIVRD
//        String operatorStatus = dto.getOperatorStatus();
//
//        if (TENCNET_SUCCESS_CODE.equals(operatorStatus)) {
//            return MsgSendReceiptStatusEnum.SUCCESS.getCode();
//        } else {
//            return MsgSendReceiptStatusEnum.PARTNER_PLATFORM_FAIL.getCode();
//        }
//    }
//
////    public String getXhStatusCode(String partnerPlatform, String partnerPlatformStatus){
////        //check
////
////        if (StringUtils.isEmpty(partnerPlatform) || StringUtils.isEmpty(partnerPlatformStatus)) {
////            return null;
////        }
////
////        //生成redisKey
////        //从缓存里查
////        //缓存没有去数据库
////        StatusCode statusCode = new StatusCode();
////        statusCode.setThirdpartyCode(partnerPlatform);
////        statusCode.setStatusCode(partnerPlatformStatus);
////        List<StatusCode> statusCodeList = statusCodeMapper.selectListByParams(statusCode);
////        if (statusCodeList != null && statusCodeList.size() > 0){
////            return statusCodeList.get(0).getStatusCode();
////        }
////        return null;
////    }
//
//    /**
//     * 过滤重复回执
//     * @param msgSendReceiptDtoList
//     * @param channelCode
//     */
//    private void filterRepeatReceipt(List<ReceiptDto> msgSendReceiptDtoList, String channelCode){
//
//        Iterator<ReceiptDto> iterator = msgSendReceiptDtoList.iterator();
//        while (iterator.hasNext()){
//            ReceiptDto dto = iterator.next();
//            String receiptRedisKey = getReceiptRedisKey(dto, channelCode);
//            if (CommonRedisUtil.redisSingleValueIsExist(redisTemplate.opsForValue(), receiptRedisKey, receiptRedisKey)) {
//                iterator.remove();
//                continue;
//            }
//        }
//    }
//
//    private String getReceiptRedisKey(ReceiptDto receiptDto, String partnerPlatform){
//        List list = new ArrayList<String>();
//        list.add(SYSTEM_NAME_PREFIX);
//        list.add(CALLBACK_RECEIPT);
//        list.add(partnerPlatform);
//        list.add(receiptDto.getMsgId());
//        //如果长短信有下标，加入下标作为判断重复参数
//        if (receiptDto.getMsgIndex() != null){
//            list.add(receiptDto.getMsgIndex().toString());
//        }
//        list.add(receiptDto.getMobile());
//        return getRedisKey(list);
//    }
//
//    private List<SendReceiptDO> getMsgSendReceiptList(List<ReceiptDto> msgSendReceiptDtoList, String partnerPlatform) {
//        List<SendReceiptDO> msgSendReceiptDOList = new ArrayList<>();
//        for (ReceiptDto msgSendReceiptDto : msgSendReceiptDtoList) {
//            String xhStatusCode = getXhStatusCode(msgSendReceiptDto, partnerPlatform);
//            if (xhStatusCode == null) {
//                logger.warn("insertBatchMsgSendReceipt warn, 未知的回执状态, msgSendReceiptDto={}, partnerPlatform={} ", msgSendReceiptDto.toString(), partnerPlatform);
//                continue;
//            }
//            SendReceiptDO msgSendReceiptDO = new SendReceiptDO();
//            msgSendReceiptDO.createForInsert(primaryKeyGenerator.nextStr(msgSendReceiptDto.SEQ_NAME),
//                    msgSendReceiptDto.getMsgId(),
//                    msgSendReceiptDto.getLength(),
//                    msgSendReceiptDto.getOperatorReportTime(),
//                    msgSendReceiptDto.getPartnerReportTime(),
//                    partnerPlatform,
//                    msgSendReceiptDto.getMobile(),
//                    xhStatusCode,
//                    msgSendReceiptDto.getPartnerStatus(),
//                    msgSendReceiptDto.getOperatorStatus(),
//                    msgSendReceiptDto.getSendTime(),
//                    msgSendReceiptDto.getSendCode()
//                    );
//            msgSendReceiptDOList.add(msgSendReceiptDO);
//        }
//        return msgSendReceiptDOList;
//    }
//}
