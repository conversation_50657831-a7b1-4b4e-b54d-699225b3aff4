package com.xhqb.spectre.httpreceipt.receiptDto.dto;

import java.io.Serializable;
import java.util.Date;

public class ReceiptDto implements Serializable {

    private static final long serialVersionUID = -8819000782378410219L;

    public static final String SEQ_NAME = "receiptDtoSequenceId";


    private String id;

    private String cmppMsgIndex;

    private Integer msgIndex;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.send_log_id
     *
     * @mbggenerated
     */
    private String sendLogId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.template_id
     *
     * @mbggenerated
     */
    private String templateId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.business_group
     *
     * @mbggenerated
     */
    private String businessGroup;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.msg_id
     *
     * @mbggenerated
     */
    private String msgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.mobile
     *
     * @mbggenerated
     */
    private String mobile;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.partner_id
     *
     * @mbggenerated
     */
    private Integer partnerId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.send_time
     *
     * @mbggenerated
     */
    private Date sendTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.send_code
     *
     * @mbggenerated
     */
    private String sendCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.status
     *
     * @mbggenerated
     */
    private String status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.partner_status
     *
     * @mbggenerated
     */
    private String partnerStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.operator_status
     *
     * @mbggenerated
     */
    private String operatorStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.partner_report_time
     *
     * @mbggenerated
     */
    private Date partnerReportTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.operator_report_time
     *
     * @mbggenerated
     */
    private Date operatorReportTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.length
     *
     * @mbggenerated
     */
    private Integer length;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.extend_param
     *
     * @mbggenerated
     */
    private String extendParam;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.creator
     *
     * @mbggenerated
     */
    private String creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.modify_time
     *
     * @mbggenerated
     */
    private Date modifyTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_send_receipt.modifier
     *
     * @mbggenerated
     */
    private String modifier;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.id
     *
     * @return the value of t_send_receipt.id
     *
     * @mbggenerated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.id
     *
     * @param id the value for t_send_receipt.id
     *
     * @mbggenerated
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.send_log_id
     *
     * @return the value of t_send_receipt.send_log_id
     *
     * @mbggenerated
     */
    public String getSendLogId() {
        return sendLogId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.send_log_id
     *
     * @param sendLogId the value for t_send_receipt.send_log_id
     *
     * @mbggenerated
     */
    public void setSendLogId(String sendLogId) {
        this.sendLogId = sendLogId == null ? null : sendLogId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.template_id
     *
     * @return the value of t_send_receipt.template_id
     *
     * @mbggenerated
     */
    public String getTemplateId() {
        return templateId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.template_id
     *
     * @param templateId the value for t_send_receipt.template_id
     *
     * @mbggenerated
     */
    public void setTemplateId(String templateId) {
        this.templateId = templateId == null ? null : templateId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.business_group
     *
     * @return the value of t_send_receipt.business_group
     *
     * @mbggenerated
     */
    public String getBusinessGroup() {
        return businessGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.business_group
     *
     * @param businessGroup the value for t_send_receipt.business_group
     *
     * @mbggenerated
     */
    public void setBusinessGroup(String businessGroup) {
        this.businessGroup = businessGroup == null ? null : businessGroup.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.msg_id
     *
     * @return the value of t_send_receipt.msg_id
     *
     * @mbggenerated
     */
    public String getMsgId() {
        return msgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.msg_id
     *
     * @param msgId the value for t_send_receipt.msg_id
     *
     * @mbggenerated
     */
    public void setMsgId(String msgId) {
        this.msgId = msgId == null ? null : msgId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.mobile
     *
     * @return the value of t_send_receipt.mobile
     *
     * @mbggenerated
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.mobile
     *
     * @param mobile the value for t_send_receipt.mobile
     *
     * @mbggenerated
     */
    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }


    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.send_time
     *
     * @return the value of t_send_receipt.send_time
     *
     * @mbggenerated
     */
    public Date getSendTime() {
        return sendTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.send_time
     *
     * @param sendTime the value for t_send_receipt.send_time
     *
     * @mbggenerated
     */
    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.send_code
     *
     * @return the value of t_send_receipt.send_code
     *
     * @mbggenerated
     */
    public String getSendCode() {
        return sendCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.send_code
     *
     * @param sendCode the value for t_send_receipt.send_code
     *
     * @mbggenerated
     */
    public void setSendCode(String sendCode) {
        this.sendCode = sendCode == null ? null : sendCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.status
     *
     * @return the value of t_send_receipt.status
     *
     * @mbggenerated
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.status
     *
     * @param status the value for t_send_receipt.status
     *
     * @mbggenerated
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.partner_status
     *
     * @return the value of t_send_receipt.partner_status
     *
     * @mbggenerated
     */
    public String getPartnerStatus() {
        return partnerStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.partner_status
     *
     * @param partnerStatus the value for t_send_receipt.partner_status
     *
     * @mbggenerated
     */
    public void setPartnerStatus(String partnerStatus) {
        this.partnerStatus = partnerStatus == null ? null : partnerStatus.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.operator_status
     *
     * @return the value of t_send_receipt.operator_status
     *
     * @mbggenerated
     */
    public String getOperatorStatus() {
        return operatorStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.operator_status
     *
     * @param operatorStatus the value for t_send_receipt.operator_status
     *
     * @mbggenerated
     */
    public void setOperatorStatus(String operatorStatus) {
        this.operatorStatus = operatorStatus == null ? null : operatorStatus.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.partner_report_time
     *
     * @return the value of t_send_receipt.partner_report_time
     *
     * @mbggenerated
     */
    public Date getPartnerReportTime() {
        return partnerReportTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.partner_report_time
     *
     * @param partnerReportTime the value for t_send_receipt.partner_report_time
     *
     * @mbggenerated
     */
    public void setPartnerReportTime(Date partnerReportTime) {
        this.partnerReportTime = partnerReportTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.operator_report_time
     *
     * @return the value of t_send_receipt.operator_report_time
     *
     * @mbggenerated
     */
    public Date getOperatorReportTime() {
        return operatorReportTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.operator_report_time
     *
     * @param operatorReportTime the value for t_send_receipt.operator_report_time
     *
     * @mbggenerated
     */
    public void setOperatorReportTime(Date operatorReportTime) {
        this.operatorReportTime = operatorReportTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.length
     *
     * @return the value of t_send_receipt.length
     *
     * @mbggenerated
     */
    public Integer getLength() {
        return length;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.length
     *
     * @param length the value for t_send_receipt.length
     *
     * @mbggenerated
     */
    public void setLength(Integer length) {
        this.length = length;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.extend_param
     *
     * @return the value of t_send_receipt.extend_param
     *
     * @mbggenerated
     */
    public String getExtendParam() {
        return extendParam;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.extend_param
     *
     * @param extendParam the value for t_send_receipt.extend_param
     *
     * @mbggenerated
     */
    public void setExtendParam(String extendParam) {
        this.extendParam = extendParam == null ? null : extendParam.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.create_time
     *
     * @return the value of t_send_receipt.create_time
     *
     * @mbggenerated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.create_time
     *
     * @param createTime the value for t_send_receipt.create_time
     *
     * @mbggenerated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.creator
     *
     * @return the value of t_send_receipt.creator
     *
     * @mbggenerated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.creator
     *
     * @param creator the value for t_send_receipt.creator
     *
     * @mbggenerated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.modify_time
     *
     * @return the value of t_send_receipt.modify_time
     *
     * @mbggenerated
     */
    public Date getModifyTime() {
        return modifyTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.modify_time
     *
     * @param modifyTime the value for t_send_receipt.modify_time
     *
     * @mbggenerated
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_send_receipt.modifier
     *
     * @return the value of t_send_receipt.modifier
     *
     * @mbggenerated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_send_receipt.modifier
     *
     * @param modifier the value for t_send_receipt.modifier
     *
     * @mbggenerated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    public String getCmppMsgIndex() {
        return cmppMsgIndex;
    }

    public void setCmppMsgIndex(String cmppMsgIndex) {
        this.cmppMsgIndex = cmppMsgIndex;
    }

    public Integer getMsgIndex() {
        return msgIndex;
    }

    public void setMsgIndex(Integer msgIndex) {
        this.msgIndex = msgIndex;
    }

    public void createForInsert(String id, String businessGroup, String msgId, Integer length, Date operatorReportTime,
                                Date partnerReportTime, Integer partnerId, String mobile, String status,
                                String partnerStatus, String operatorStatus, Date sendTime, String sendCode){
        this.id = id;
        this.businessGroup = businessGroup;
        this.msgId = msgId;
        this.length = length;
        this.operatorReportTime = operatorReportTime;
        this.partnerReportTime = partnerReportTime;
        this.partnerId = partnerId;
        this.mobile = mobile;
        this.status = status;
        this.partnerStatus = partnerStatus;
        this.operatorStatus = operatorStatus;
        this.sendTime = sendTime;
        this.sendCode = sendCode;
        Date now = new Date();
        this.createTime = now;
        this.modifyTime = now;
        this.creator = "SYSTEM";
        this.modifier = "SYSTEM";
    };
}
