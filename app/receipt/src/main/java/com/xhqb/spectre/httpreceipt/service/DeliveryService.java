package com.xhqb.spectre.httpreceipt.service;

import com.alibaba.fastjson.JSON;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.kael.sequencegenerator.DistributedSequence;
import com.xhqb.spectre.channel.http.context.ChannelContext;
import com.xhqb.spectre.channel.http.service.IRecipient;
import com.xhqb.spectre.common.dal.entity.HttpRecord;
import com.xhqb.spectre.common.dal.entity.SmsReceiptDO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.mapper.SmsReceiptMapper;
import com.xhqb.spectre.common.dal.mapper.SmsOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class DeliveryService {

    @Autowired
    ChannelContext channelContext;

    @Autowired
    private MQTemplate<String> mqTemplate;

    @Autowired
    private DistributedSequence primaryKeyGenerator;

    @Autowired
    private SmsReceiptMapper smsReceiptMapper;

    @Autowired
    private SmsOrderMapper smsOrderMapper;

    //spectre-send-deliver
    @Value("#{'${kael.mq.producers:}'.split(',')[0]}")
    private String spectreSendDeliver;

    public boolean receive(String channelCode, Map<String, Object> params) {
        IRecipient recipient = channelContext.getRecipient(channelCode);
        List<HttpRecord> httpRecordList = recipient.receiveReport(params);
        if (httpRecordList == null) return true;
        try {
            log.info("入库操作; size={}", httpRecordList.size());
            httpRecordList.forEach(r -> {
                String json = JSON.toJSONString(r);
                log.info("入队; r={}", json);
                mqTemplate.send(spectreSendDeliver, json);
            });
        } catch (Exception e) {
            log.error("入库异常 reportDTOList={}", httpRecordList, e);
            return false;
        }
        return true;
    }

    @Transactional
    public void updateReceipt(SmsReceiptDO smsReceiptDO) {
//        try {
//            smsReceiptMapper.insertSelective(smsReceiptDO);
//            log.debug("sendReceiptDO={}", smsReceiptDO);
//            SmsOrderDO smsOrderDO = SmsOrderDO.builder()
//                .channelMsgId(smsReceiptDO.getChannelMsgId())
//                .mobile(smsReceiptDO.getMobile())
//                .reportCode("")
//                .build();
//            log.debug("smsOrderDO={}", smsOrderDO);
//            smsOrderMapper.updateByMsgSelective(smsOrderDO);
//        } catch (Exception e) {
//            log.error("回执更新数据库异常;", e);
//        }
    }
}
