//package com.xhqb.spectre.httpreceipt.job;
//
//import com.dangdang.ddframe.job.api.ShardingContext;
//import com.dangdang.ddframe.job.api.simple.SimpleJob;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
//import com.xhqb.spectre.httpreceipt.receiptDto.dto.ReceiptDto;
//import com.xhqb.spectre.httpreceipt.receiptDto.request.ReceiptRequest;
//import com.xhqb.spectre.httpreceipt.receiptDto.result.ReceiptResult;
//import com.xhqb.spectre.httpreceipt.service.ReceIptService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.core.ListOperations;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Component
//@Job("msgInsertCmppReceiptJob")
//public class MsgInsertCmppReceiptJob implements SimpleJob {
//
//    private static final Logger logger = LoggerFactory.getLogger(MsgInsertCmppReceiptJob.class);
//
//    private static final String REDIS_KEY_CMPP = "batch:sms:cmpp:receipt";
//
//    @Autowired
//    private ReceIptService receIptService;
//    @Autowired
//    private RedisTemplate redisTemplate;
//
//    @Override
//    public void execute(ShardingContext shardingContext) {
//        logger.info("cmppd短信回执star");
//        try {
//
//            ListOperations<String, Object> opsForList =  redisTemplate.opsForList();
//            long size = opsForList.size(REDIS_KEY_CMPP);
//            logger.info("定时任务输出size:{}条", size);
//            if (size > 0) {
//                //从第一条开始获取
//                List<Object> objectList = opsForList.range(REDIS_KEY_CMPP, 0, size);
//                logger.info("objectList.size():{}", objectList.size());
//                List<ReceiptDto> msgSendReceiptDtoList = new ArrayList<>();
//                for (Object object : objectList) {
//                    ObjectMapper objectMapper = new ObjectMapper();
//                    ReceiptDto msgSendReceiptDto  = objectMapper.convertValue(object, ReceiptDto.class);
//                    msgSendReceiptDtoList.add(msgSendReceiptDto);
//                    //删除缓存里已入库的数据 0：删除缓存中所有跟value一致的值
//                    opsForList.remove(REDIS_KEY_CMPP, 0, msgSendReceiptDto);
//                }
//                logger.info("{}回执删除后缓存目前的的条数：{}", msgSendReceiptDtoList.get(0).getPartnerId(), opsForList.size(REDIS_KEY_CMPP));
//                ReceiptRequest msgSendReceiptRequest = new ReceiptRequest();
//                msgSendReceiptRequest.setReceiptDtoLisst(msgSendReceiptDtoList);
//                ReceiptResult receiptResult = receIptService.batchSaveMsgSendReceip(msgSendReceiptRequest);
//                if (!receiptResult.isSuccess()) {
//                    logger.error("{}回执入库失败", msgSendReceiptDtoList.get(0).getPartnerId());
//                }
//            } else {
//                logger.info("cmpp回执没有缓存数据");
//            }
//        } catch (Exception ex) {
//            logger.error("cmpp定时保存回执异常了，ex:{}" ,ex.getMessage());
//        }
//        logger.info("cmpp回执保存正常结束");
//    }
//}
