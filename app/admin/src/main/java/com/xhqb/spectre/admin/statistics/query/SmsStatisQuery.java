package com.xhqb.spectre.admin.statistics.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.*;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/28 15:18
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsStatisQuery implements Serializable {

    private static final long serialVersionUID = 7194409690687757232L;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 批次ID
     */
    private Integer batchId;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 运营商
     */
    private String isp;

    private PageParameter pageParameter;
}
