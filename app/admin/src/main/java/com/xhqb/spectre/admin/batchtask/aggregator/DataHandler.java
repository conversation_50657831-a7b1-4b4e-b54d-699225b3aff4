package com.xhqb.spectre.admin.batchtask.aggregator;

import org.springframework.core.Ordered;

/**
 * 数据来源处理器
 * <p>
 * 该处理器主要服务于 DataAggregate 聚合数据的来源
 * <p>
 * P -> 数据来源查询参数
 * R -> 数据查询结果
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
public interface DataHandler<P, R> extends Ordered {

    /**
     * 数据来源查询
     *
     * @param condition
     * @param smsTypeCode
     * @return
     */
    R query(P condition, String smsTypeCode);
}
