package com.xhqb.spectre.admin.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xhqb.spectre.admin.bidata.entity.ChannelMonthlyStatementDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelMonthlyStatementVO {


    @ExcelIgnore
    private String channelCode;
    /**
     * 渠道编码
     */
    @ExcelProperty(value = "供应商", index = 0)
    private String channelCodeName;

    @ExcelIgnore
    private String smsTypeCode;

    /**
     * 统计月份
     */
    @ExcelProperty(value = "日期", index = 1)
    private String statDate;

    /**
     * 短信类型编码
     */
    @ExcelProperty(value = "短信类型", index = 2)
    private String smsTypeCodeName;

    /**
     * 签名
     */
    @ExcelProperty(value = "签名", index = 3)
    private String signName;


    /**
     * 单价
     */
    @ExcelProperty(value = "单价", index = 4)
    private BigDecimal price;

    /**
     * 计费条数
     */
    @ExcelProperty(value = "计费条数", index = 5)
    private Integer totalBillCount;
    /**
     * 总费用
     */
    @ExcelProperty(value = "费用", index = 6)
    private BigDecimal totalPriceCount;

    /**
     * 触达率
     */
    @ExcelProperty(value = "触达率", index = 7)
    private String reachRate;

    public static ChannelMonthlyStatementVO convert(ChannelMonthlyStatementDO channelMonthlyStatementDO) {

        BigDecimal price = BigDecimal.ZERO;
        if (channelMonthlyStatementDO.getTotalBillCount() != null && channelMonthlyStatementDO.getTotalBillCount() > 0) {
            BigDecimal totalPrice = new BigDecimal(channelMonthlyStatementDO.getTotalPriceCount());
            BigDecimal totalBill = new BigDecimal(channelMonthlyStatementDO.getTotalBillCount());

            BigDecimal priceInFen = totalPrice.divide(totalBill, 3, RoundingMode.HALF_UP);

            //单位为元
            price = priceInFen.divide(new BigDecimal(1000), 3, RoundingMode.HALF_UP);
        }


        BigDecimal totalPrice = BigDecimal.ZERO;
        if (channelMonthlyStatementDO.getTotalPriceCount() != null) {
            totalPrice = new BigDecimal(channelMonthlyStatementDO.getTotalPriceCount())
                    .divide(new BigDecimal(1000), 3, RoundingMode.HALF_UP);
        }

        String reachRateStr = "0%";
        if (channelMonthlyStatementDO.getReachRate() != null) {
            DecimalFormat percentFormat = new DecimalFormat("0%");
            reachRateStr = percentFormat.format(channelMonthlyStatementDO.getReachRate());
        }

        return ChannelMonthlyStatementVO.builder().statDate(channelMonthlyStatementDO.getStatDate())
                .channelCode(channelMonthlyStatementDO.getChannelCode())
                .smsTypeCode(channelMonthlyStatementDO.getSmsTypeCode())
                .price(price)
                .totalBillCount(channelMonthlyStatementDO.getTotalBillCount())
                .totalPriceCount(totalPrice)
                .reachRate(reachRateStr)
                .signName(channelMonthlyStatementDO.getSignName())
                .build();
    }

}
