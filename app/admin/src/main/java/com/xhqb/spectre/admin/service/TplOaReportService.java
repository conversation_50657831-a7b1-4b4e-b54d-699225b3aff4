package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.vo.TplOaReportVO;
import com.xhqb.spectre.admin.service.oa.vo.FlowDataAllInfo;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.TplOaReportQuery;

import java.util.List;
import java.util.Set;

public interface TplOaReportService {
    CommonPager<TplOaReportVO> page(TplOaReportQuery query);

    Set<String> getApprovedAndApprovingContentSet();

    List<FlowDataAllInfo> approve(String flowId,String userId);
}
