package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.service.BatchTestLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 群发测试日志
 *
 * <AUTHOR>
 * @date 2021/12/15
 */
@RestController
@RequestMapping("/batchTestLog")
@Slf4j
public class BatchTestLogController {


    @Resource
    private BatchTestLogService batchTestLogService;

    /**
     * 查询测试日志
     *
     * @param taskId
     * @return
     */
    @GetMapping("/queryTestLog")
    public AdminResult queryTestLog(Integer taskId) {
        log.info("查询测试日志,taskId = {}", taskId);
        return AdminResult.success(batchTestLogService.queryTestLog(taskId));
    }

}
