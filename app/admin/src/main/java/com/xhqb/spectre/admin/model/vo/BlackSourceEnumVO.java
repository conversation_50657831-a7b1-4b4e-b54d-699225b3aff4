package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.BlackSourceDO;
import com.xhqb.spectre.common.dal.entity.SignDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlackSourceEnumVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String name;

    private String code;


    public static BlackSourceEnumVO buildBlackSourceEnumVO(BlackSourceDO blackSourceDO) {
        return BlackSourceEnumVO.builder()
                .id(blackSourceDO.getId())
                .name(blackSourceDO.getName())
                .code(blackSourceDO.getCode())
                .build();
    }
}
