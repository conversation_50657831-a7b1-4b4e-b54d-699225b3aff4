package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.MobileBlackDTO;
import com.xhqb.spectre.admin.model.vo.MobileBlackVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.MobileBlackQuery;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/26 20:46
 * @Description:
 */
public interface MobileBlackService {

    CommonPager<MobileBlackVO> listByPage(MobileBlackQuery mobileBlackQuery);

    MobileBlackVO getById(Integer id);

    void create(MobileBlackDTO mobileBlackDTO);

    void delete(Integer id);

    void batchDelete(List<Integer> idList);


}
