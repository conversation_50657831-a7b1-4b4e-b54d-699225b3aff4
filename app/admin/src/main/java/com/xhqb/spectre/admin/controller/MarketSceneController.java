package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.dto.MarketSceneDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.MarketSceneVO;
import com.xhqb.spectre.admin.service.MarketSceneService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.MarketSceneQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 模板营销场景管理
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
@RestController
@RequestMapping("/marketScene")
@Slf4j
public class MarketSceneController {

    @Resource
    private MarketSceneService marketSceneService;

    /**
     * 营销场景列表展示
     *
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(MarketSceneQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<MarketSceneVO> commonPager = marketSceneService.listByPage(query);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询营销场景详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult detail(@PathVariable("id") Integer id) {
        return AdminResult.success(marketSceneService.getById(id));
    }

    /**
     * 新增营销场景
     *
     * @param marketSceneDTO
     * @return
     */
    @PostMapping("")
    public AdminResult create(@RequestBody MarketSceneDTO marketSceneDTO) {
        log.info("新增营销场景请求 = {}", marketSceneDTO);
        marketSceneService.create(marketSceneDTO);
        return AdminResult.success();
    }

    /**
     * 更新营销场景
     *
     * @param id
     * @param marketSceneDTO
     * @return
     */
    @PutMapping("/{id}")
    public AdminResult updateApp(@PathVariable("id") Integer id, @RequestBody MarketSceneDTO marketSceneDTO) {
        log.info("更新营销场景请求 = {}, id = {}", marketSceneDTO, id);
        marketSceneService.update(id, marketSceneDTO);
        return AdminResult.success();
    }

    /**
     * 启用营销场景
     *
     * @param id
     * @return
     */
    @PostMapping("/enable/{id}")
    public AdminResult enableProjectDesc(@PathVariable("id") Integer id) {
        log.info("启用营销场景请求 = {}", id);
        marketSceneService.status(id, 1);
        return AdminResult.success();
    }

    /**
     * 停用营销场景
     *
     * @param id
     * @return
     */
    @PostMapping("/disable/{id}")
    public AdminResult disableProjectDesc(@PathVariable("id") Integer id) {
        log.info("停用营销场景请求 = {}", id);
        marketSceneService.status(id, 0);
        return AdminResult.success();
    }

    /**
     * 删除营销场景
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public AdminResult deleteProjectDesc(@PathVariable("id") Integer id) {
        log.info("删除营销场景请求 = {}", id);
        marketSceneService.deleteById(id);
        return AdminResult.success();
    }

    /**
     * 下拉列表展示营销场景
     *
     * @return
     */
    @GetMapping("/enum")
    public AdminResult queryEnum() {
        return AdminResult.success(marketSceneService.listAll(1));
    }
}
