package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.model.dto.TypeWeightDTO;
import com.xhqb.spectre.common.dal.entity.test.TestTplDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ChannelTestTplDetailVO extends TestTplDO {
    /**
     * 测试号类型 0:空号，1:正常号,2:黑名单
     */
    private List<TypeWeightDTO> typeWeightDTOS;

    /**
     * 短信模版code
     */
    private String tplCode;

    /**
     * 短信模版名称
     */
    private String tplName;

    /**
     * 签名
     */
    private Integer signId;
}
