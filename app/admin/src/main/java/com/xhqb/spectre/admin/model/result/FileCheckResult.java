package com.xhqb.spectre.admin.model.result;

import com.xhqb.spectre.admin.model.user.url.UserShortUrlFileInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
public class FileCheckResult implements Serializable {

    /**
     * 总上传数据个数
     */
    private Integer total;

    /**
     * 有效数据个数
     */
    private Integer validCount;

    /**
     * 列表头
     */
    private List<String> titleList;

    /**
     * 无效数据个数
     */
    private Integer invalidCount;

    /**
     * 无效数据(展示前30个 可支持配置)
     */
    private List<String> invalidData;

    /**
     * 有效数量列表
     */
    private List<UserShortUrlFileInfo> validList;


    /**
     * 添加解析的内容信息
     *
     * @param excel
     * @return
     */
    public void addExcel(UserShortUrlFileInfo excel) {
        if (Objects.isNull(validList)) {
            validList = new ArrayList<>();
        }
        validList.add(excel);
    }

}
