package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.dto.FailResendDTO;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.FailResendDetailVO;
import com.xhqb.spectre.admin.model.vo.FailResendVO;
import com.xhqb.spectre.admin.model.vo.ReportVO;
import com.xhqb.spectre.admin.service.FailResendService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.FailResendQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 失败补发
 *
 * <AUTHOR>
 * @date 2025/7/30 11:05
 */
@RestController
@RequestMapping("/failResend")
@Slf4j
public class FailResendController {

    @Resource
    private FailResendService failResendService;

    /**
     * 分页查询
     *
     * @param failResendQuery 查询条件
     * @param pageNum         页码
     * @param pageSize        每页数量
     * @return 分页结果
     */
    @GetMapping("")
    public CommonResult<CommonPager<FailResendVO>> page(@ModelAttribute FailResendQuery failResendQuery, Integer pageNum, Integer pageSize) {
        failResendQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        return CommonResult.success(failResendService.page(failResendQuery));
    }

    /**
     * 新增失败补发
     *
     * @param failResendDTO 新增参数
     * @return 新增结果-策略ID
     */
    @PostMapping("/add")
    public CommonResult<String> add(@RequestBody FailResendDTO failResendDTO) {
        return CommonResult.success(failResendService.add(failResendDTO));
    }

    /**
     * update失败补发
     *
     * @param failResendDTO 更新参数
     * @return 更新结果-策略ID
     */
    @PostMapping("/update")
    public CommonResult<String> update(@RequestBody FailResendDTO failResendDTO) {
        return CommonResult.success(failResendService.update(failResendDTO));
    }

    /**
     * 删除失败补发
     *
     * @param strategyId 策略ID
     * @return 删除结果
     */
    @PostMapping("/delete/{strategyId}")
    public CommonResult<String> delete(@PathVariable("strategyId") String strategyId) {
        return CommonResult.success(failResendService.delete(strategyId));
    }

    /**
     * 获取失败补发详情
     *
     * @param strategyId 策略ID
     * @return 详情
     */
    @GetMapping("/detail")
    public CommonResult<FailResendDetailVO> detail(@RequestParam("strategyId") String strategyId) {
        return CommonResult.success(failResendService.detail(strategyId));
    }

    /**
     * 启用失败补发
     *
     * @param strategyId 策略ID
     * @return 启用结果
     */
    @GetMapping("/enable")
    public CommonResult<String> enable(@RequestParam("strategyId") String strategyId) {
        return CommonResult.success(failResendService.enable(strategyId));
    }

    /**
     * 禁用失败补发
     *
     * @param strategyId 策略ID
     * @return 禁用结果
     */
    @GetMapping("/disable")
    public CommonResult<String> disable(@RequestParam("strategyId") String strategyId) {
        return CommonResult.success(failResendService.disable(strategyId));
    }
}
