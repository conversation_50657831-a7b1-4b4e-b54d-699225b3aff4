package com.xhqb.spectre.admin.util;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.time.temporal.IsoFields;
import java.time.temporal.WeekFields;
import java.util.*;

public class MonthWeekUtils {
    public static final DateTimeFormatter YM_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM");
    public static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static final DateTimeFormatter MONTH_DAY_FORMAT = DateTimeFormatter.ofPattern("MMdd");

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DateRange {
        private String startDate;
        private String endDate;
    }

    public static List<Map<String, Object>> getRecent13MonthsWithWeeks() {
        List<Map<String, Object>> result = new ArrayList<>();
        YearMonth current = YearMonth.now();

        for (int i = 0; i < 13; i++) {
            YearMonth targetMonth = current.minusMonths(i);
            Map<String, Object> monthData = new HashMap<>();
            String ymStr = targetMonth.toString(); // "2025-06"
            String name = ymStr.substring(2, 4) + ymStr.substring(5, 7); // "2506"

            monthData.put("monthCode", name);
            monthData.put("month", ymStr);
            monthData.put("weeks", getWeeksOfMonth(targetMonth));

            result.add(monthData);
        }

        return result;
    }

    private static List<String> getWeeksOfMonth(YearMonth yearMonth) {
        LocalDate firstDay = yearMonth.atDay(1);
        LocalDate lastDay = yearMonth.atEndOfMonth();
        LocalDate current = firstDay;
        List<String> weeks = new LinkedList<>();

        while (!current.isAfter(lastDay)) {
            int weekNumber = current.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
            int weekYear = current.get(IsoFields.WEEK_BASED_YEAR);
            String weekCode = String.format("%02dW%02d", weekYear % 100, weekNumber);
            if (!weeks.contains(weekCode)) {
                weeks.add(weekCode);
            }
            current = current.plusDays(1);
        }

        return weeks;
    }

    /**
     * 获取某个月的开始和结束日期（字符串）
     */
    public static DateRange getMonthStartAndEnd(String yearMonthStr) {
        YearMonth ym = YearMonth.parse(yearMonthStr, YM_FORMAT);
        String start = ym.atDay(1).format(DATE_FORMAT);
        String end = ym.atEndOfMonth().format(DATE_FORMAT);
        return new DateRange(start, end);
    }

    /**
     * 获取某一周的开始和结束日期(周一-周日)
     */
    public static DateRange getWeekStartAndEnd(String weekCode) {
        return getWeekStartAndEnd(weekCode, DATE_FORMAT);
    }

    public static DateRange getWeekStartAndEnd(String weekCode, DateTimeFormatter formatter) {
        int year = 2000 + Integer.parseInt(weekCode.substring(0, 2));
        int week = Integer.parseInt(weekCode.substring(3));
        LocalDate firstDayOfWeek = LocalDate
                .of(year, 1, 4)
                .with(IsoFields.WEEK_OF_WEEK_BASED_YEAR, week)
                .with(ChronoField.DAY_OF_WEEK, 1);

        LocalDate lastDayOfWeek = firstDayOfWeek.plusDays(6);
        return new DateRange(
                firstDayOfWeek.format(formatter),
                lastDayOfWeek.format(formatter)
        );
    }

    /**
     * 获取当前月份之前12个月中最早一个月的第一天（共13个月）
     * 例如当前是 2025-06，则返回 2024-06-01
     */
    public static String getEarliestStartDateOfLast12Months() {
        YearMonth targetMonth = YearMonth.now().minusMonths(12);
        return targetMonth.atDay(1).format(DATE_FORMAT);
    }

    /**
     * 获取当前时间起向前连续指定月数的第一个月第一天
     */
    public static String getStartDateMonthsAgo(int months) {
        return LocalDate.now().minusMonths(months).withDayOfMonth(1).format(DATE_FORMAT);
    }

    public static String getStartDateOfWeeksAgo(Integer weeksToSubtract) {
        LocalDate twelveWeeksAgo = LocalDate.now().minusWeeks(weeksToSubtract);
        LocalDate weekStart = twelveWeeksAgo.with(WeekFields.ISO.dayOfWeek(), 1);
        return weekStart.format(DATE_FORMAT);
    }

    /**
     * 获取最近4周 ["25W23", "25W22", "25W21", "25W20"]
     */
    public static List<String> getRecent4Weeks() {
        return getRecentForWeeks(4);
    }

    /**
     * 获取指定周数
     * @param num 周数
     * @return
     */
    public static List<String> getRecentForWeeks(int num) {
        List<String> weeks = new ArrayList<>();
        LocalDate now = LocalDate.now();

        for (int i = 0; i < num; i++) {
            LocalDate weekDate = now.minusWeeks(i);
            int weekNum = weekDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
            int weekYear = weekDate.get(IsoFields.WEEK_BASED_YEAR);
            String weekCode = String.format("%02dW%02d", weekYear % 100, weekNum);

            if (!weeks.contains(weekCode)) {
                weeks.add(weekCode);
            }
        }

        return weeks;
    }


    /**
     * 获取当前日期
     * @return
     */
    public static String getCurrentDate() {
        return LocalDate.now().format(DATE_FORMAT);
    }


    /**
     * 将 "YYWNN" 格式字符串转换为该周第一天的日期（周一）
     * 返回格式：yyyy-MM-dd
     */
    public static String getFirstDayOfWeekString(String yearWeek) {

        // 提取两位年份和周数
        int year = Integer.parseInt(yearWeek.substring(0, 2));
        int week = Integer.parseInt(yearWeek.substring(3, 5));

        // 转换为四位年份（假设是 21 世纪）
        year += 2000;

        // 获取该年第 'week' 周的第一个星期一
        LocalDate firstDayOfWeek = LocalDate.of(year, 1, 1)
                .plusWeeks(week - 1);

        // 如果一年的第一周从周四开始或之后算作第一周（ISO 周标准），可使用下面方式：
        LocalDate isoWeekStart = LocalDate.of(year, 1, 1)
                .with(WeekFields.ISO.weekOfYear(), week)
                .with(WeekFields.ISO.dayOfWeek(), 1); // ISO 周从周一开始

        return isoWeekStart.toString(); // 默认格式就是 yyyy-MM-dd
    }

    /**
     * 获取两个日期之间的所有月份
     * @param start
     * @param end
     * @return
     */
    public static List<String> getMonthsBetween(String start, String end) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        YearMonth startYearMonth = YearMonth.parse(start, formatter);
        YearMonth endYearMonth = YearMonth.parse(end, formatter);

        List<String> months = new ArrayList<>();
        YearMonth current = startYearMonth;

        while (!current.isAfter(endYearMonth)) {
            months.add(current.format(formatter));
            current = current.plusMonths(1);
        }

        return months;
    }

    /**
     * 获取两个日期之间的所有周
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<String> getWeeksBetween(String startDate, String endDate) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate start = LocalDate.parse(startDate, inputFormatter);
        LocalDate end = LocalDate.parse(endDate, inputFormatter);

        List<String> weeks = new ArrayList<>();

        LocalDate current = start;
        while (!current.isAfter(end)) {
            int year = current.get(ChronoField.YEAR);
            int weekOfYear = current.get(WeekFields.ISO.weekOfYear());
            String weekStr = String.format("%dW%02d", year % 100, weekOfYear); // 格式如 25W20
            if (!weeks.contains(weekStr)) {
                weeks.add(weekStr);
            }
            current = current.plusDays(1);
        }

        return weeks;
    }
}
