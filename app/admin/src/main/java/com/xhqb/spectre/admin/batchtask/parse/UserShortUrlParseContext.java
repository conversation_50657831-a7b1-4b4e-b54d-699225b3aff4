package com.xhqb.spectre.admin.batchtask.parse;

import lombok.Data;

import java.io.InputStream;
import java.io.Serializable;

@Data
public class UserShortUrlParseContext implements Serializable {


    /**
     * 处理的文件名称
     */
    private final String fileName;
    /**
     * 处理文件输入流
     */
    private final transient InputStream inputStream;
    /**
     * 文件存储地址
     */
    private final String saveUrl;

    public UserShortUrlParseContext(String fileName, InputStream inputStream, String saveUrl) {
        this.fileName = fileName;
        this.inputStream = inputStream;
        this.saveUrl = saveUrl;
    }
}
