package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.ChannelTestTplDetailVO;
import com.xhqb.spectre.admin.model.vo.ChannelTestTplVO;
import com.xhqb.spectre.admin.service.ChannelTestTplService;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ChannelTestTplQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import com.xhqb.spectre.admin.model.dto.ChannelTestTplDTO;


import javax.annotation.Resource;

/*
 * @Author: yangjiqiu
 * @Date: 2024/05/21 09:44
 * @Description: 渠道测试模版
 */
@RestController
@RequestMapping("/channelTestTpl")
@Slf4j
public class ChannelTestTplController {

    @Resource
    private ChannelTestTplService channelTestTplService;

    /**
     * 分页列表
     * @param channelTestTplQuery 查询参数
     * @param pageNum 当前页
     * @param pageSize 页大小
     * @return 分页列表
     */
    @GetMapping("")
    public CommonResult<CommonPager<ChannelTestTplVO>> listByPage(@ModelAttribute ChannelTestTplQuery channelTestTplQuery, Integer pageNum, Integer pageSize) {
        channelTestTplQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        log.info("渠道测试模版分页参数 listByPage:{}", channelTestTplQuery);
        return CommonResult.success(channelTestTplService.listByPage(channelTestTplQuery));
    }

    /**
     * 新增
     * @param channelTestTplDTO 新增参数
     * @return id
     */
    @PostMapping("/add")
    public CommonResult<Long> add(@RequestBody ChannelTestTplDTO channelTestTplDTO) {
        log.info("渠道测试模版新增 add:{}", channelTestTplDTO);
        ValidatorUtil.validate(channelTestTplDTO);
        return CommonResult.success(channelTestTplService.add(channelTestTplDTO));
    }

    // 编辑
    @PostMapping("/update")
    public CommonResult<Long> update(@RequestBody ChannelTestTplDTO channelTestTplDTO){
        log.info("渠道测试模版编辑 update:{}", channelTestTplDTO);
        ValidatorUtil.validate(channelTestTplDTO);
        return CommonResult.success(channelTestTplService.update(channelTestTplDTO));
    }

    // 查看
    @GetMapping("/detail/{id}")
    public CommonResult<ChannelTestTplDetailVO> detail(@PathVariable("id") Long id){
        log.info("渠道测试模版查看 detail:{}", id);
        return CommonResult.success(channelTestTplService.detail(id));
    }
    /**
     * 启用
     * @param id 主键id
     * @return 无
     */
    @PostMapping("/enable/{id}")
    public CommonResult<Void> enableSign(@PathVariable("id") Long id) {
        log.info("启用模版, id: {}", id);
        channelTestTplService.enable(id);
        return CommonResult.success();
    }

    /**
     * 停用
     * @param id 主键id
     * @return 无
     */
    @PostMapping("/disable/{id}")
    public CommonResult<Void>  disableSign(@PathVariable("id") Long id) {
        log.info("停用模版, id: {}", id);
        channelTestTplService.disable(id);
        return CommonResult.success();
    }




}
