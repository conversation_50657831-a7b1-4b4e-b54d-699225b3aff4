package com.xhqb.spectre.admin.statistics.mapper;

import com.xhqb.spectre.admin.statistics.entity.ClassEnumDO;
import com.xhqb.spectre.admin.statistics.entity.SmsStatisByDayDO;
import com.xhqb.spectre.admin.statistics.entity.SmsStatisByDayKey;
import com.xhqb.spectre.admin.statistics.entity.SmsStatisSumDO;
import com.xhqb.spectre.admin.statistics.query.SmsStatisQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SmsStatisByDayMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_class_statis_byday
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(SmsStatisByDayKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_class_statis_byday
     *
     * @mbggenerated
     */
    int insert(SmsStatisByDayDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_class_statis_byday
     *
     * @mbggenerated
     */
    int insertSelective(SmsStatisByDayDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_class_statis_byday
     *
     * @mbggenerated
     */
    SmsStatisByDayDO selectByPrimaryKey(SmsStatisByDayKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_class_statis_byday
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SmsStatisByDayDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_class_statis_byday
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SmsStatisByDayDO record);

    List<SmsStatisSumDO> selectSumList(SmsStatisQuery query);

    List<SmsStatisSumDO> selectSumListByPage(SmsStatisQuery query);

    Integer selectSumListCount(SmsStatisQuery query);

    List<SmsStatisSumDO> selectSumListByMonth(SmsStatisQuery query);

    List<SmsStatisSumDO> selectClassSum(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("columnName") String columnName);

    List<SmsStatisSumDO> selectClassSumList(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("columnName") String columnName);

    List<ClassEnumDO> selectClassEnum(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("columnName") String columnName);
}