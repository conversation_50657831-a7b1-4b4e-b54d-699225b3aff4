package com.xhqb.spectre.admin.controller;


import cn.hutool.json.JSONUtil;
import com.xhqb.spectre.admin.bidata.model.SendFormDO;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.MultipleCompareVO;
import com.xhqb.spectre.admin.model.vo.SendDetailVO;
import com.xhqb.spectre.admin.service.BidataSendStatService;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.MultipleCompareQuery;
import com.xhqb.spectre.common.dal.query.ReachRateQuery;
import com.xhqb.spectre.common.dal.query.SendQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 发送统计
 *
 * <AUTHOR>
 * @date 2024/10/31
 */
@RestController
@RequestMapping("/sendStat")
@Slf4j

public class BidataSendStatController {


    @Resource
    private BidataSendStatService bidataSendStatService;


    /**
     * 获取概览数据
     * <p>
     * key: 日期类型（昨日：yesterday 7日：weeks 上月: lastMonth） value: 数据 发送量、触达量、触达率
     *
     * @return 包含概览数据的CommonResult对象，数据以Map<String, List<Object>>形式返回
     */
    @PostMapping("/overview")
    public CommonResult<Map<String, List<Object>>> overview() {
        return CommonResult.success(bidataSendStatService.overview());
    }


    /**
     * 发送top10统计信息
     *
     * @param query 查询条件
     * @return CommonResultMap<String, List < SendFormDO>> 返回包含top10统计信息的CommonResult对象
     */
    @PostMapping("/sendTopTen")
    public CommonResult<Map<String, List<SendFormDO>>> sendTopTen(@RequestBody SendQuery query) {
        ValidatorUtil.validate(query);
        return CommonResult.success(bidataSendStatService.sendTopTen(query));
    }


    /**
     * 获取发送详情分页信息
     *
     * @param query    查询条件对象，包含各种查询参数
     * @param pageNum   当前页码
     * @param pageSize 每页显示的数据条数
     * @return 返回包含发送详情分页信息的CommonResult对象，成功时包含CommonPager<SendDetailVO>类型的数据
     */
    @GetMapping("/sendDetail")
    public CommonResult<CommonPager<SendDetailVO>> sendDetail(@ModelAttribute SendQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        ValidatorUtil.validate(query);
        return CommonResult.success(bidataSendStatService.sendDetail(query));
    }


    /**
     * 计算触达率
     *
     * @param query 查询条件
     * @return 返回触达率结果，封装在CommonResult中
     */
    @PostMapping("/reachRate")
    public CommonResult<Map<String, List<Object>>> reachRate(@RequestBody ReachRateQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        ValidatorUtil.validate(query);
        return CommonResult.success(bidataSendStatService.reachRate(query));
    }


    /**
     * 多条件(模板、渠道)比较
     *
     * @param query    MultipleCompareQuery对象，包含查询条件
     * @param pageNum   当前页码
     * @param pageSize 每页显示数量
     * @return CommonResult对象，包含查询结果
     */
    @PostMapping("/multipleCompare")
    public CommonResult<MultipleCompareVO> multipleCompare(@RequestBody MultipleCompareQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        ValidatorUtil.validate(query);
        return CommonResult.success(bidataSendStatService.multipleCompare(query));
    }


    /**
     * 获取概览数据
     * @param query 概览数据查询条件
     * @return CommonResult对象，包含概览数据
     */
    @PostMapping("/overviewByQuery")
    public CommonResult<List<Object>> overviewByQuery(@RequestBody SendQuery query) {
        log.info("根据查询条件获取概览数据 overviewByQuery:{}", JSONUtil.toJsonStr(query));
        return CommonResult.success(bidataSendStatService.overviewByQuery(query));
    }

}
