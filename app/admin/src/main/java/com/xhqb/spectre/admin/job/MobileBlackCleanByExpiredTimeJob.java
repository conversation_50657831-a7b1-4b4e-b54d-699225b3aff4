package com.xhqb.spectre.admin.job;

import cn.hutool.core.collection.CollectionUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.common.dal.entity.MobileBlackDO;
import com.xhqb.spectre.common.dal.mapper.MobileBlackMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 手机号黑名单过期时间清理任务 1小时执行一次 再某小时35分执行（如：2024-11-28 18:35:00 、2024-11-28 19:35:00）
 * elastic-job.jobs.mobileBlackCleanByExpiredTimeJob.cron=0 35 * * * ?
 * elastic-job.jobs.mobileBlackCleanByExpiredTimeJob.sharding-total-count=1
 * elastic-job.jobs.mobileBlackCleanByExpiredTimeJob.sharding-item-parameters=0=A
 * elastic-job.jobs.mobileBlackCleanByExpiredTimeJob.disabled=true
 * elastic-job.jobs.mobileBlackCleanByExpiredTimeJob.description=手机号黑名单过期时间清理任务
 *
 * <AUTHOR>
 */

@Component
@Job("mobileBlackCleanByExpiredTimeJob")
@Slf4j
public class MobileBlackCleanByExpiredTimeJob implements SimpleJob {

    @Resource
    private MobileBlackMapper mobileBlackMapper;

    @Override
    public void execute(ShardingContext shardingContext) {
        long start = System.currentTimeMillis();
        log.info("手机号黑名单过期时间清理任务开始");
        List<MobileBlackDO> expiredDataList = mobileBlackMapper.selectExpiredDataByExpiredTime();
        if (CollectionUtil.isEmpty(expiredDataList)) {
            log.info("手机号黑名单过期数据expiredDataList is empty，无需清理");
            return;
        }

        mobileBlackMapper.deleteByIdList(expiredDataList.stream().map(MobileBlackDO::getId).collect(Collectors.toList()), "system");
        long end = System.currentTimeMillis();
        log.info("手机号黑名单过期时间清理任务结束，耗时：{}ms", end - start);
    }
}
