package com.xhqb.spectre.admin.service.detection;

import lombok.Data;
import org.springframework.http.HttpHeaders;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/06/17
 */
@Data
public class AuthenticationHeader {

    @NotEmpty(message = "应用不能为空")
    private String appCode;
    @NotEmpty(message = "请求时间不能够为空")
    private String timestamp;
    @NotEmpty(message = "请求随机编码不能够为空")
    private String nonce;
    @NotEmpty(message = "签名信息不能够为空")
    private String signCode;

    protected HttpHeaders httpHeaders;

    public AuthenticationHeader() {
        this.httpHeaders = new HttpHeaders();
    }

    @Override
    public String toString() {
        return "AuthenticationHeader{" +
            "appCode='" + appCode + '\'' +
            ", timestamp='" + timestamp + '\'' +
            ", nonce='" + nonce + '\'' +
            ", signCode='" + signCode + '\'' +
            '}';
    }
}
