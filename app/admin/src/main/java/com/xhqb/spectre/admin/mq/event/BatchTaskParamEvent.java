package com.xhqb.spectre.admin.mq.event;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.mq.message.BatchTaskParamMessage;
import org.springframework.context.ApplicationEvent;

/**
 * 发送消息完成之后的事件通知
 *
 * <AUTHOR>
 * @date 2021/9/26
 */
public class BatchTaskParamEvent extends ApplicationEvent {

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 群发批次下的参数ID
     */
    private Integer taskParamId;

    /**
     * 已发送的成功条数
     */
    private Integer total;

    /**
     * 发送成功数量
     */
    private Integer sendSuccess;

    public BatchTaskParamEvent(Object source, TaskParamResult taskParamResult) {
        super(source);
        this.total = taskParamResult.getTotal();
        this.sendSuccess = taskParamResult.getSuccess();
        this.taskId = getBatchTaskParamMessage().getTaskId();
        this.taskParamId = getBatchTaskParamMessage().getTaskParamId();
    }

    /**
     * 获取到发送消息对象
     *
     * @return
     */
    public BatchTaskParamMessage getBatchTaskParamMessage() {
        return (BatchTaskParamMessage) super.getSource();
    }

    /**
     * 获取到已经发送成功的数量
     *
     * @return
     */
    public Integer getSendTotalCount() {
        return total;
    }

    /**
     * 获取发送成功的数量
     *
     * @return
     */
    public Integer getRealSendCount() {
        return sendSuccess;
    }

    /**
     * 获取到当前时间的任务ID
     *
     * @return
     */
    public Integer getTaskId() {
        return taskId;
    }

    /**
     * 当前param主键
     *
     * @return
     */
    public Integer getTaskParamId() {
        return taskParamId;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
