package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.TplDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplInfoVO implements Serializable {
    private static final long serialVersionUID = 393042060994661887L;

    private Integer id;

    private String code;

    private String smsTypeCode;

    private String title;

    private Integer signId;

    private String content;

    private String appCode;

    private Integer status;

    private String remark;

    private String creator;

    private String createTime;

    private String updater;

    private String updateTime;

    private Integer isDelete;

    private String signName;

    public static TplInfoVO buildTplInfoVO(TplDO tplDO, String signName) {
        return TplInfoVO.builder()
            .id(tplDO.getId())
            .code(tplDO.getCode())
            .smsTypeCode(tplDO.getSmsTypeCode())
            .title(tplDO.getTitle())
            .signId(tplDO.getSignId())
            .content(tplDO.getContent())
            .appCode(tplDO.getAppCode())
            .status(tplDO.getStatus())
            .remark(tplDO.getRemark())
            .createTime(DateUtil.dateToString(tplDO.getCreateTime()))
            .creator(tplDO.getCreator())
            .updateTime(DateUtil.dateToString(tplDO.getUpdateTime()))
            .updater(tplDO.getUpdater())
            .isDelete(tplDO.getIsDelete())
            .signName(signName)
            .build();
    }
}
