package com.xhqb.spectre.admin.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 模板参数工具类
 */
public class TplParamUtil {

    private static final String TPL_PARAM_SYMBOL = "[*]";

    private static final String TPL_CONTENT_PARAM2 = "(\\$\\{)([\\s\\S]*?)}";

    /**
     * 计算字符串中 [*] 形式的占位符个数
     * @param content 模板内容
     * @return
     */
    public static int countSequentialPlaceholders(String content) {
        if (content == null) {
            return 0;
        }

        int count = 0;
        int idx = 0;
        while ((idx = content.indexOf(TPL_PARAM_SYMBOL, idx)) != -1) {
            count++;
            idx += TPL_PARAM_SYMBOL.length();
        }
        return count;
    }

    /**
     * 计算字符串中 ${key} 形式的占位符个数
     * @param content 模板内容
     * @return
     */
    public static int countKeyedPlaceholders(String content) {
        if (content == null || content.isEmpty()) {
            return 0;
        }

        // 定义正则表达式
        Pattern pattern = Pattern.compile(TPL_CONTENT_PARAM2);
        Matcher matcher = pattern.matcher(content);

        int count = 0;
        while (matcher.find()) {
            count++;
        }

        return count;
    }
}
