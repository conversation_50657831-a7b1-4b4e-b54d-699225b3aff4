package com.xhqb.spectre.admin.batchtask.strategy.impl;

import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.parse.ParseContext;
import com.xhqb.spectre.admin.batchtask.strategy.LoadStrategy;
import com.xhqb.spectre.admin.batchtask.upload.UploadContext;
import com.xhqb.spectre.admin.batchtask.upload.UploadHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * Web文件上传策略
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
@Component
@Slf4j
public class WebLoadStrategy implements LoadStrategy<MultipartFile> {

    @Autowired
    private UploadHandler uploadHandler;

    /**
     * 策略名称 用于策略加载
     *
     * @return
     */
    @Override
    public String named() {
        return BatchTaskConstants.StrategyNamed.WEB_STRATEGY_NAME;
    }


    /**
     * 加载文件策略
     *
     * @param request 一个文件上传的对象 MultipartFile
     * @return
     * @throws IOException
     */
    @Override
    public ParseContext load(MultipartFile request) throws IOException {
        // 文件解析的流
        InputStream inputStream = request.getInputStream();
        String fileName = request.getOriginalFilename();
        // 文件上传的流
        InputStream uploadStream = request.getInputStream();
        // String saveUrl = this.doUpload(fileName, uploadStream);
        return new ParseContext(fileName, inputStream, null, uploadStream);
    }

    /**
     * 做文件上传
     *
     * @param fileName
     * @param uploadStream
     * @return 返回文件上传之后的地址
     */
    private String doUpload(String fileName, InputStream uploadStream) {
        return uploadHandler.handler(new UploadContext(fileName, uploadStream));
    }
}
