package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.common.validation.ValidPath;
import com.xhqb.spectre.common.validation.ValidQuery;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;


@Data
public class UpdateWxUrlLinkDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 小程序页面路径
     */
    @ValidPath(message = "小程序路径格式错误")
    private String path;

    /**
     * 查询参数
     */
    @ValidQuery(message = "查询参数格式错误")
    private String query;

    /**
     * 使用场景描述
     */
    @Size(max = 256, message = "描述最大为{max}个字符")
    private String linkDesc;
}
