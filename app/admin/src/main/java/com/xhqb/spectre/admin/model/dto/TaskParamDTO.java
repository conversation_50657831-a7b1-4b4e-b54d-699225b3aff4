package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 群发任务参数DTO
 *
 * <AUTHOR>
 * @date 2021/10/21
 */
@Data
public class TaskParamDTO implements Serializable {

    /**
     * 批次号
     */
    @NotNull(message = "批次号不能够为空")
    private Integer taskId;

    /**
     * 发送状态
     */
    private Integer sendStatus;

    /**
     * 文件md5值
     */
    private String fileMd5;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 参数JSON
     */
    private String paramJsonArray;

    /**
     * 删除标记
     */
    private Integer isDelete;

    /**
     * 文件切分起始位置
     */
    private Integer startOffset;

    /**
     * 文件切分结束位置
     */
    private Integer endOffset;
}
