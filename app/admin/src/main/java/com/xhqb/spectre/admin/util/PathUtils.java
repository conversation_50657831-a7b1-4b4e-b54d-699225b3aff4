package com.xhqb.spectre.admin.util;

import org.springframework.util.PropertyPlaceholderHelper;

import java.util.Map;

/**
 * 路径占位符
 *
 * <AUTHOR>
 * @date 2021/10/11
 */
public class PathUtils {
    private static PropertyPlaceholderHelper PATH_HELPER = new PropertyPlaceholderHelper(
            "{", "}", ":", false);

    /**
     * 替换路径占位符
     *
     * @param path
     * @param mapping
     * @return
     */
    public static String replace(String path, Map<String, String> mapping) {
        return PATH_HELPER.replacePlaceholders(path, placeholderName -> mapping.get(placeholderName));
    }

}
