package com.xhqb.spectre.admin.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.xhqb.spectre.admin.enums.RespCodeEnum;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.ReachStatDTO;
import com.xhqb.spectre.admin.model.dto.UpdateChannelPriceDTO;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.*;
import com.xhqb.spectre.admin.service.BidataReachStatService;
import com.xhqb.spectre.admin.service.ChannelSmsTypePriceLogService;
import com.xhqb.spectre.admin.util.FileResponseUtil;
import com.xhqb.spectre.admin.util.MonthWeekUtils;
import com.xhqb.spectre.admin.util.excel.DataUtils;
import com.xhqb.spectre.admin.util.excel.HeaderUtils;
import com.xhqb.spectre.admin.util.excel.MultiLevelHeader;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 短信供应商统计
 */
@RestController
@RequestMapping("/reachStat")
@Slf4j
public class BidataReachStatController {
    @Resource
    private BidataReachStatService bidataReachStatService;

    @Resource
    private ChannelSmsTypePriceLogService channelSmsTypePriceLogService;

    public static boolean isValidYearMonthFormat(String dateStr) {
        return dateStr != null && dateStr.matches("\\d{4}-((0[1-9])|(1[0-2]))");
    }

    public static boolean isValidYearWeekFormat(String weekStr) {
        return weekStr != null && weekStr.matches("(?:[0-9]{2}W(?:0[1-9]|[1-4][0-9]|5[0-3]))");
    }

    /**
     * m1-1-渠道条件展示
     *
     * @return
     */
    @GetMapping("/channel")
    public CommonResult<StatChannelVO> getStatChannelList() {
        return CommonResult.success(bidataReachStatService.getStatChannelList());
    }

    /**
     * m2-1-日期条件展示
     *
     * @return
     */
    @GetMapping("/date")
    public CommonResult<List<Map<String, Object>>> getStatSignList() {
        return CommonResult.success(bidataReachStatService.getStatSignList());
    }

    /**
     * m3-1-短信类型条件展示
     *
     * @return
     */
    @GetMapping("/smsType")
    public CommonResult<StatSmsTypeVO> getStatSmsTypeList() {
        return CommonResult.success(bidataReachStatService.getStatSmsTypeList());
    }

    /**
     * m1-2-根据渠道统计
     *
     * @return
     */
    @PostMapping("/reachStatByChannel")
    public CommonResult<DataListVO> getReachStatByChannel(@RequestBody ReachStatDTO reachStatDTO) {
        if (reachStatDTO.getType() == null || reachStatDTO.getType() == 1) {
            List<MonoReachStatVO> monoReachStatVOList = bidataReachStatService.getMonthStatsByChannel(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(monoReachStatVOList)
                    .totalCount(monoReachStatVOList.size())
                    .build();
            return CommonResult.success(dataListVO);
        } else {
            List<MonoReachStatVO> monoReachStatVOList = bidataReachStatService.getWeekStatsByChannel(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(monoReachStatVOList)
                    .totalCount(monoReachStatVOList.size())
                    .build();
            return CommonResult.success(dataListVO);
        }
    }


    /**
     * 供应商纵向-导出
     */
    @GetMapping("/reachStatByChannel/download")
    public void downloadReachStatByChannel(HttpServletResponse response,
                                   @RequestParam(name = "type", defaultValue = "1") int type,
                                   @RequestParam(name = "channelCode") String channelCode,
                                   @RequestParam(name = "signList", required = false) List<String> signList,
                                   @RequestParam(name = "smsTypeCodes", required = false) List<String> smsTypeCodes) {
        try {
            log.info("导出渠道纵向, type={}, channelCode={}", type, channelCode);
            ReachStatDTO reachStatDTO = ReachStatDTO.builder()
                    .type(type)
                    .channelCode(channelCode)
                    .signList(signList)
                    .smsTypeCodes(smsTypeCodes)
                    .build();
            List<MonoReachStatVO> result;
            if (reachStatDTO.getType() == null || reachStatDTO.getType() == 1) {
                result = bidataReachStatService.getMonthStatsByChannel(reachStatDTO);
            } else {
                result = bidataReachStatService.getWeekStatsByChannel(reachStatDTO);
            }

            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileName = channelCode + dateStr;

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    FileResponseUtil.buildContentDisposition(fileName, FileResponseUtil.XLSX));

            EasyExcel.write(response.getOutputStream(), MonoReachStatVO.class)
                    .sheet("sheet1")
                    .doWrite(result);
        } catch (Exception e) {
            log.error("导出数据失败", e);
            throw new BizException("导出失败");
        }
    }

    /**
     * m2-2-根据日期统计
     *
     * @return
     */
    @PostMapping("/reachStatByDate")
    public CommonResult<DataListVO> getReachStatByDate(@RequestBody ReachStatDTO reachStatDTO) {
        if (isValidYearMonthFormat(reachStatDTO.getStatDate())) {

            List<MonoReachStatVO> monoReachStatVOList = bidataReachStatService.getMonthStatsByDate(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(monoReachStatVOList)
                    .totalCount(monoReachStatVOList.size())
                    .build();
            return CommonResult.success(dataListVO);
        }
        if (isValidYearWeekFormat(reachStatDTO.getStatDate())) {
            List<MonoReachStatVO> monoReachStatVOList = bidataReachStatService.getWeekStatsByDate(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(monoReachStatVOList)
                    .totalCount(monoReachStatVOList.size())
                    .build();
            return CommonResult.success(dataListVO);
        }
        return CommonResult.make(RespCodeEnum.PARAM_ERROR.getCode(), "日期格式错误");
    }

    /**
     * 供应商横向-导出
     */
    @GetMapping("/reachStatByDate/download")
    public void downloadReachStatByDate(HttpServletResponse response,
                                           @RequestParam(name = "statDate") String statDate,
                                           @RequestParam(name = "signList", required = false) List<String> signList,
                                           @RequestParam(name = "smsTypeCodes", required = false) List<String> smsTypeCodes) {
        try {
            log.info("导出渠道横向, statDate={}", statDate);
            ReachStatDTO reachStatDTO = ReachStatDTO.builder()
                    .statDate(statDate)
                    .signList(signList)
                    .smsTypeCodes(smsTypeCodes)
                    .build();
            List<MonoReachStatVO> result;
            if (isValidYearMonthFormat(reachStatDTO.getStatDate())) {
                result = bidataReachStatService.getMonthStatsByDate(reachStatDTO);
            } else {
                result = bidataReachStatService.getWeekStatsByDate(reachStatDTO);
            }

            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileName = dateStr;

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    FileResponseUtil.buildContentDisposition(fileName, FileResponseUtil.XLSX));

            EasyExcel.write(response.getOutputStream(), MonoReachStatVO.class)
                    .sheet("sheet1")
                    .doWrite(result);
        } catch (Exception e) {
            log.error("导出数据失败", e);
            throw new BizException("导出失败");
        }
    }

    /**
     * m3-2-根据类型统计
     *
     * @return
     */
    @PostMapping("/reachStatByType")
    public CommonResult<DataListVO> getReachStatByType(@RequestBody ReachStatDTO reachStatDTO) {
        if (reachStatDTO.getType() == null || reachStatDTO.getType() == 1) {
            List<PolyReachStatVO> list = bidataReachStatService.getMonthStatsByType(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(list)
                    .totalCount(list.size())
                    .build();
            return CommonResult.success(dataListVO);
        } else {
            List<PolyReachStatVO> list = bidataReachStatService.getWeekStatsByType(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(list)
                    .totalCount(list.size())
                    .build();
            return CommonResult.success(dataListVO);
        }
    }

    /**
     * 供应商横纵-下载
     *
     **/
    @GetMapping("/reachStatByType/download")
    public void downloadReachStatByType(HttpServletResponse response,
                                                            @RequestParam(name = "type", defaultValue = "1") int type,
                                                            @RequestParam(name = "signList", required = false) List<String> signList,
                                                            @RequestParam(name = "smsTypeCodes", required = false) List<String> smsTypeCodes) {
        ReachStatDTO reachStatDTO = ReachStatDTO.builder()
                .type(type)
                .signList(signList)
                .smsTypeCodes(smsTypeCodes)
                .build();
        List<PolyReachStatVO> dataList;
        if (reachStatDTO.getType() == null || reachStatDTO.getType() == 1) {
            dataList = bidataReachStatService.getMonthStatsByType(reachStatDTO);

        } else {
            dataList = bidataReachStatService.getWeekStatsByType(reachStatDTO);
        }
        try {
            // 获取数据
            List<String> statDates = dataList.get(0).getStatDate();

            // 定义多级表头
            List<MultiLevelHeader> headers = Arrays.asList(
                    new MultiLevelHeader("供应商", "channelCodeName"),
                    //new MultiLevelHeader("触达量", "reachCount", statDates),
                    new MultiLevelHeader("计费量", "reachBillCount", statDates),
                    new MultiLevelHeader("触达率", "reachRate", statDates)
            );

            List<List<String>> head = HeaderUtils.buildMultiLevelHeaders(headers);

            // 构建数据行
            List<List<Object>> dataRows = DataUtils.buildDataRows(dataList, headers);


            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileName = dateStr;

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    FileResponseUtil.buildContentDisposition(fileName, FileResponseUtil.XLSX));

            // 导出 Excel
            EasyExcel.write(response.getOutputStream())
                    .head(head)
                    .sheet("sheet1")
                    .doWrite(dataRows);

            log.info("导出完成");
        } catch (Exception e) {
            log.error("导出数据失败", e);
            throw new BizException("导出失败");
        }
    }


    /**
     * 获取子统计
     *
     * @param reachStatDTO statDate 日期月/周: 2025-01, 25W22
     * @return
     */
    @PostMapping("/subReachStats")
    public CommonResult<SubDataListVO> getSubReachStats(@RequestBody ReachStatDTO reachStatDTO) {
        if (!CollectionUtils.isEmpty(reachStatDTO.getChannelCodes())) {
            reachStatDTO.setChannelCode(null);
        }
        if (isValidYearMonthFormat(reachStatDTO.getStatDate())) {

            List<SubReachStatVO> list = bidataReachStatService.getMonthSubReachStats(reachStatDTO);

            // 总触达量
            Integer totalReachCount = list.stream()
                    .map(SubReachStatVO::getReachCount)
                    .filter(Objects::nonNull)
                    .reduce(0, Integer::sum);

            Integer totalReachBillCount = list.stream()
                    .map(SubReachStatVO::getReachBillCount)
                    .filter(Objects::nonNull)
                    .reduce(0, Integer::sum);

            BigDecimal totalPriceCount = list.stream()
                    .map(SubReachStatVO::getPriceCount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            Double avgReachRateValue = list.stream()
                    .map(vo -> {
                        String rateStr = vo.getReachRate();
                        if (rateStr == null || !rateStr.contains("%")) return 0.0;
                        try {
                            return Double.parseDouble(rateStr.replace("%", "")) / 100;
                        } catch (NumberFormatException e) {
                            return 0.0;
                        }
                    })
                    .reduce(Double::sum)
                    .map(sum -> sum / list.size())
                    .orElse(0.0);

            String avgReachRate = String.format("%.0f%%", avgReachRateValue * 100);


            Long signCount = list.stream()
                    .map(SubReachStatVO::getSignName)
                    .filter(Objects::nonNull)
                    .distinct()
                    .count();


            SubDataListVO dataListVO = SubDataListVO.builder()
                    .dataList(list)
                    .totalCount(list.size())
                    .totalReachCount(totalReachCount)
                    .totalReachBillCount(totalReachBillCount)
                    .avgReachRate(avgReachRate)
                    .signCount(signCount.intValue())
                    .totalPriceCount(totalPriceCount)
                    .build();
            if (StringUtils.isEmpty(reachStatDTO.getChannelCode())) {
                List<SubReachStatVO> cleanedList = list.stream().peek(vo -> vo.setChannelCodeName("")).collect(Collectors.toList());
                dataListVO.setDataList(cleanedList);
            }

            return CommonResult.success(dataListVO);
        }
        if (isValidYearWeekFormat(reachStatDTO.getStatDate())) {
            List<SubReachStatVO> list = bidataReachStatService.getWeekSubReachStats(reachStatDTO);

            // 总触达量
            Integer totalReachCount = list.stream()
                    .map(SubReachStatVO::getReachCount)
                    .filter(Objects::nonNull)
                    .reduce(0, Integer::sum);

            Integer totalReachBillCount = list.stream()
                    .map(SubReachStatVO::getReachBillCount)
                    .filter(Objects::nonNull)
                    .reduce(0, Integer::sum);

            BigDecimal totalPriceCount = list.stream()
                    .map(SubReachStatVO::getPriceCount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            Double avgReachRateValue = list.stream()
                    .map(vo -> {
                        String rateStr = vo.getReachRate();
                        if (rateStr == null || !rateStr.contains("%")) return 0.0;
                        try {
                            return Double.parseDouble(rateStr.replace("%", "")) / 100;
                        } catch (NumberFormatException e) {
                            return 0.0;
                        }
                    })
                    .reduce(Double::sum)
                    .map(sum -> sum / list.size())
                    .orElse(0.0);

            String avgReachRate = String.format("%.2f%%", avgReachRateValue * 100);


            Long signCount = list.stream()
                    .map(SubReachStatVO::getSignName)
                    .filter(Objects::nonNull)
                    .distinct()
                    .count();

            SubDataListVO dataListVO = SubDataListVO.builder()
                    .dataList(list)
                    .totalCount(list.size())
                    .totalReachCount(totalReachCount)
                    .totalReachBillCount(totalReachBillCount)
                    .avgReachRate(avgReachRate)
                    .signCount(signCount.intValue())
                    .totalPriceCount(totalPriceCount)
                    .build();

            if (StringUtils.isEmpty(reachStatDTO.getChannelCode())) {
                List<SubReachStatVO> cleanedList = list.stream().peek(vo -> vo.setChannelCodeName("")).collect(Collectors.toList());
                dataListVO.setDataList(cleanedList);
            }
            return CommonResult.success(dataListVO);
        }
        return CommonResult.make(RespCodeEnum.PARAM_ERROR.getCode(), "日期格式错误");
    }

    @GetMapping("/subReachStats/download")
    public void downloadByTplHeat(HttpServletResponse response,
                                  @RequestParam String statDate,
                                  @RequestParam(required = false) String channelCode,
                                  @RequestParam(required = false) String smsTypeCode,
                                  @RequestParam(required = false) List<String> channelCodes,
                                  @RequestParam(required = false) List<String> signList) {

        List<SubReachStatVO> list = new ArrayList<>();
        if (isValidYearMonthFormat(statDate)) {
            ReachStatDTO reachStatDTO = ReachStatDTO.builder()
                    .statDate(statDate)
                    .channelCode(channelCode)
                    .smsTypeCode(smsTypeCode)
                    .channelCodes(channelCodes)
                    .signList(signList)
                    .build();
            list = bidataReachStatService.getMonthSubReachStats(reachStatDTO);

        }

        if (isValidYearWeekFormat(statDate)) {
            ReachStatDTO reachStatDTO = ReachStatDTO.builder()
                    .statDate(statDate)
                    .channelCode(channelCode)
                    .smsTypeCode(smsTypeCode)
                    .channelCodes(channelCodes)
                    .signList(signList)
                    .build();
            list = bidataReachStatService.getWeekSubReachStats(reachStatDTO);
        }

        try {
            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileName = "sub_" + dateStr;

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    FileResponseUtil.buildContentDisposition(fileName, FileResponseUtil.XLSX));

            if (StringUtils.isEmpty(channelCode)) {
                List<SubAllReachStatVO> allList = list.stream()
                        .map(SubAllReachStatVO::convert)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                EasyExcel.write(response.getOutputStream(), SubAllReachStatVO.class)
                        .excludeColumnFieldNames(Arrays.asList("channelCode", "channelCodeName"))
                        .sheet("sheet1")
                        .doWrite(allList);
            } else {
                EasyExcel.write(response.getOutputStream(), SubReachStatVO.class)
                        .sheet("sheet1")
                        .doWrite(list);
            }

        } catch (Exception e) {
            log.error("导出数据失败", e);
            throw new BizException("导出失败");
        }
    }

    /**
     * m4-1-异常统计
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/monitor/abnormalReachRate")
    public CommonResult<TplReachRateVO> getAbnormalTplReachRates(Integer pageNum,
                                                                 Integer pageSize) {
        return CommonResult.success(bidataReachStatService.getAbnormalTplReachRates(new PageParameter(pageNum, pageSize)));
    }

    /**
     * m4-2-模板渠道差异
     *
     * @param statDate 周:25W22
     * @return
     */
    @GetMapping("/monitor/abnormalTplChannel")
    public CommonResult<TplStatVO> getAbnormalTplStats(
            @RequestParam String statDate,
            @RequestParam(defaultValue = "0.2") double rateGap,
            @RequestParam(defaultValue = "100") int minSendCount
    ) {
        if (!isValidYearWeekFormat(statDate)) {
            return CommonResult.make(RespCodeEnum.PARAM_ERROR.getCode(), "日期格式错误");
        }
        return CommonResult.success(bidataReachStatService.getAbnormalTplChannelReachRate(statDate, rateGap, minSendCount));
    }

    /**
     * m4-3-获取最近四周的日期
     */
    @GetMapping("/monitor/topTplChannel")
    public CommonResult<TplStatVO> getTopTplByChannel(
            @RequestParam String smsTypeCode,
            @RequestParam String statDate
    ) {

        if (!isValidYearWeekFormat(statDate)) {
            return CommonResult.make(RespCodeEnum.PARAM_ERROR.getCode(), "日期格式错误");
        }
        ReachStatDTO reachStatDTO = ReachStatDTO.builder().smsTypeCode(smsTypeCode).statDate(statDate).build();
        TplStatVO result = bidataReachStatService.queryTplChannelStats(reachStatDTO);
        return CommonResult.success(result);
    }

    /**
     * m4-4-获取最近四周的日期
     */
    @GetMapping("/recent4Weeks")
    public CommonResult<List<String>> getRecent4Weeks() {
        return CommonResult.success(MonthWeekUtils.getRecent4Weeks());
    }

    /**
     * m4-5-获取最近13周的日期
     *
     * @return
     */
    @GetMapping("/recent13Weeks")
    public CommonResult<List<WeekStatVO>> getRecent13Weeks() {
        List<String> dateStr = MonthWeekUtils.getRecentForWeeks(13);
        List<WeekStatVO> weekStatVOList = dateStr.stream().map(WeekStatVO::build).collect(Collectors.toList());
        return CommonResult.success(weekStatVOList);
    }


    @GetMapping("/tpl-heat/overall")
    public CommonResult<CommonPager<TplHeatStatVO>> getOverallByTplHeat(
            @RequestParam(required = false) String tplCode,
            @RequestParam(required = false) String creator,
            @RequestParam String smsTypeCode,
            @RequestParam String startDate,
            @RequestParam String endDate
    ) {
        List<TplHeatStatVO> result = bidataReachStatService.getOverallByTplHeat(startDate, endDate, smsTypeCode, tplCode, creator);
        return CommonResult.success(PageResultUtils.result(
                () -> result == null ? 0 : result.size(),
                () -> result));
    }

    // 区分模板的接口
    @GetMapping("/tpl-heat")
    public CommonResult<CommonPager<TplHeatStatVO>> getStatByTplHeat(
            @RequestParam(required = false) String tplCode,
            @RequestParam(required = false) String creator,
            @RequestParam String smsTypeCode,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize
    ) {
        CommonPager<TplHeatStatVO> result = bidataReachStatService.getStatByTplHeat(startDate,
                endDate, smsTypeCode, tplCode, creator,
                new PageParameter(pageNum, pageSize));
        return CommonResult.success(result);
    }

    /**
     * 模板热度-导出
     * @param response
     * @param tplCode
     * @param creator
     * @param smsTypeCode
     * @param startDate
     * @param endDate
     */
    @GetMapping("/tpl-heat/download")
    public void downloadByTplHeat(HttpServletResponse response,
                                  @RequestParam(required = false) String tplCode,
                                  @RequestParam(required = false) String creator,
                                  @RequestParam String smsTypeCode,
                                  @RequestParam String startDate,
                                  @RequestParam String endDate) {
        try {
            log.info("导出模板热度请求信息, startDate={}, endDate={}, smsTypeCode={}", startDate, startDate, smsTypeCode);
            List<TplHeatStatVO> result = bidataReachStatService.getStatByTplHeat(startDate,
                    endDate, smsTypeCode, tplCode, creator);

            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileName = "tpl_" + dateStr;

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    FileResponseUtil.buildContentDisposition(fileName, FileResponseUtil.XLSX));

            EasyExcel.write(response.getOutputStream(), TplHeatStatVO.class)
                    .sheet("sheet1")
                    .doWrite(result);
        } catch (Exception e) {
            log.error("导出数据失败", e);
            throw new BizException("导出失败");
        }
    }


    /**
     * 渠道纵向费用-列表
     *
     * @return
     */
    @PostMapping("/charge/channel")
    public CommonResult<DataListVO> getChargeByChannel(@RequestBody ReachStatDTO reachStatDTO) {
        if (reachStatDTO.getType() == null || reachStatDTO.getType() == 1) {
            List<MonoPriceStatVO> monoReachStatVOList = bidataReachStatService.getMonthPriceStatsByChannel(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(monoReachStatVOList)
                    .totalCount(monoReachStatVOList.size())
                    .build();
            return CommonResult.success(dataListVO);
        } else {
            List<MonoPriceStatVO> monoReachStatVOList = bidataReachStatService.getWeekPriceStatsByChannel(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(monoReachStatVOList)
                    .totalCount(monoReachStatVOList.size())
                    .build();
            return CommonResult.success(dataListVO);
        }
    }

    /**
     * 渠道纵向费用-导出
     */
    @GetMapping("/charge/channel/download")
    public void downloadChannelCharge(HttpServletResponse response,
                                      @RequestParam(name = "type", defaultValue = "1") int type,
                                      @RequestParam(name = "channelCode") String channelCode,
                                      @RequestParam(name = "signList", required = false) List<String> signList,
                                      @RequestParam(name = "smsTypeCodes", required = false) List<String> smsTypeCodes) {
        try {
            log.info("导出渠道纵向费用, type={}, channelCode={}", type, channelCode);
            ReachStatDTO reachStatDTO = ReachStatDTO.builder()
                    .type(type)
                    .channelCode(channelCode)
                    .signList(signList)
                    .smsTypeCodes(smsTypeCodes)
                    .build();
            List<MonoPriceStatVO> result;
            if (reachStatDTO.getType() == null || reachStatDTO.getType() == 1) {
                result = bidataReachStatService.getMonthPriceStatsByChannel(reachStatDTO);
            } else {
                result = bidataReachStatService.getWeekPriceStatsByChannel(reachStatDTO);
            }

            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileName = channelCode + dateStr;

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    FileResponseUtil.buildContentDisposition(fileName, FileResponseUtil.XLSX));

            EasyExcel.write(response.getOutputStream(), MonoPriceStatVO.class)
                    .sheet("sheet1")
                    .doWrite(result);
        } catch (Exception e) {
            log.error("导出数据失败", e);
            throw new BizException("导出失败");
        }
    }


    /**
     * 供应商横向费用-列表
     *
     * @return
     */
    @PostMapping("/charge/date")
    public CommonResult<DataListVO> getChargeByDate(@RequestBody ReachStatDTO reachStatDTO) {
        if (isValidYearMonthFormat(reachStatDTO.getStatDate())) {

            List<MonoPriceStatVO> monoReachStatVOList = bidataReachStatService.getMonthPriceStatsByDate(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(monoReachStatVOList)
                    .totalCount(monoReachStatVOList.size())
                    .build();
            return CommonResult.success(dataListVO);
        }
        if (isValidYearWeekFormat(reachStatDTO.getStatDate())) {
            List<MonoPriceStatVO> monoReachStatVOList = bidataReachStatService.getWeekPriceStatsByDate(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(monoReachStatVOList)
                    .totalCount(monoReachStatVOList.size())
                    .build();
            return CommonResult.success(dataListVO);
        }
        return CommonResult.make(RespCodeEnum.PARAM_ERROR.getCode(), "日期格式错误");
    }

    /**
     * 供应商横向费用-导出
     */
    @GetMapping("/charge/date/download")
    public void downloadDateCharge(HttpServletResponse response,
                                      @RequestParam(name = "statDate") String statDate,
                                      @RequestParam(name = "signList", required = false) List<String> signList,
                                      @RequestParam(name = "smsTypeCodes", required = false) List<String> smsTypeCodes) {
        try {
            log.info("导出渠道横向费用, statDate={}", statDate);
            ReachStatDTO reachStatDTO = ReachStatDTO.builder()
                    .statDate(statDate)
                    .signList(signList)
                    .smsTypeCodes(smsTypeCodes)
                    .build();
            List<MonoPriceStatVO> result;
            if (isValidYearMonthFormat(reachStatDTO.getStatDate())) {
                result = bidataReachStatService.getMonthPriceStatsByDate(reachStatDTO);
            } else {
                result = bidataReachStatService.getWeekPriceStatsByDate(reachStatDTO);
            }

            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileName = statDate + dateStr;

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    FileResponseUtil.buildContentDisposition(fileName, FileResponseUtil.XLSX));

            EasyExcel.write(response.getOutputStream(), MonoPriceStatVO.class)
                    .sheet("sheet1")
                    .doWrite(result);
        } catch (Exception e) {
            log.error("导出数据失败", e);
            throw new BizException("导出失败");
        }
    }

    /**
     * 供应商纵横费用-列表
     *
     * @return
     */
    @PostMapping("/charge/type")
    public CommonResult<DataListVO> getChargeByType(@RequestBody ReachStatDTO reachStatDTO) {
        if (reachStatDTO.getType() == null || reachStatDTO.getType() == 1) {
            List<PolyPriceStatVO> list = bidataReachStatService.getMonthPriceStatsByType(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(list)
                    .totalCount(list.size())
                    .build();
            return CommonResult.success(dataListVO);
        } else {
            List<PolyPriceStatVO> list = bidataReachStatService.getWeekPriceStatsByType(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(list)
                    .totalCount(list.size())
                    .build();
            return CommonResult.success(dataListVO);
        }
    }


    /**
     * 供应商横纵-下载
     *
     **/
    @GetMapping("/charge/type/download")
    public void downloadChargeByType(HttpServletResponse response,
                                        @RequestParam(name = "type", defaultValue = "1") int type,
                                        @RequestParam(name = "signList", required = false) List<String> signList,
                                        @RequestParam(name = "smsTypeCode", required = false) String smsTypeCode) {
        ReachStatDTO reachStatDTO = ReachStatDTO.builder()
                .type(type)
                .signList(signList)
                .smsTypeCode(smsTypeCode)
                .build();
        List<PolyPriceStatVO> dataList;
        if (reachStatDTO.getType() == null || reachStatDTO.getType() == 1) {
            dataList = bidataReachStatService.getMonthPriceStatsByType(reachStatDTO);

        } else {
            dataList = bidataReachStatService.getWeekPriceStatsByType(reachStatDTO);
        }
        try {
            // 获取数据
            List<String> statDates = dataList.get(0).getStatDate();

            // 定义多级表头
            List<MultiLevelHeader> headers = Arrays.asList(
                    new MultiLevelHeader("供应商", "channelCodeName"),
                    new MultiLevelHeader("触达率", "reachRate", statDates),
                    new MultiLevelHeader("费用", "priceCount", statDates),
                    new MultiLevelHeader("计费量(条)", "reachBillCount", statDates)
            );

            List<List<String>> head = HeaderUtils.buildMultiLevelHeaders(headers);

            // 构建数据行
            List<List<Object>> dataRows = DataUtils.buildDataRows(dataList, headers);


            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileName = dateStr;

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    FileResponseUtil.buildContentDisposition(fileName, FileResponseUtil.XLSX));

            // 导出 Excel
            EasyExcel.write(response.getOutputStream())
                    .head(head)
                    .sheet("sheet1")
                    .doWrite(dataRows);

            log.info("导出完成");
        } catch (Exception e) {
            log.error("导出数据失败", e);
            throw new BizException("导出失败");
        }
    }


    /**
     * 费用纵向-列表
     * @param reachStatDTO
     * @return
     */
    @PostMapping("/charge")
    public CommonResult<DataListVO> getCharge(@RequestBody ReachStatDTO reachStatDTO) {
        if (reachStatDTO.getType() == null || reachStatDTO.getType() == 1) {
            List<MonoPriceStatVO> monoReachStatVOList = bidataReachStatService.getMonthPriceStats(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(monoReachStatVOList)
                    .totalCount(monoReachStatVOList.size())
                    .build();
            return CommonResult.success(dataListVO);
        } else {
            List<MonoPriceStatVO> monoReachStatVOList = bidataReachStatService.getWeekPriceStats(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(monoReachStatVOList)
                    .totalCount(monoReachStatVOList.size())
                    .build();
            return CommonResult.success(dataListVO);
        }
    }

    /**
     * 纵向费用-导出
     */
    @GetMapping("/charge/download")
    public void downloadCharge(HttpServletResponse response,
                                      @RequestParam(name = "type", defaultValue = "1") int type,
                                      @RequestParam(name = "channelCodes") List<String> channelCodes,
                                      @RequestParam(name = "signList", required = false) List<String> signList,
                                      @RequestParam(name = "smsTypeCodes", required = false) List<String> smsTypeCodes) {
        try {
            log.info("导出纵向费用, type={}, channelCode={}", type, channelCodes);
            ReachStatDTO reachStatDTO = ReachStatDTO.builder()
                    .type(type)
                    .channelCodes(channelCodes)
                    .signList(signList)
                    .smsTypeCodes(smsTypeCodes)
                    .build();
            List<MonoPriceStatVO> result;
            if (reachStatDTO.getType() == null || reachStatDTO.getType() == 1) {
                result = bidataReachStatService.getMonthPriceStats(reachStatDTO);
            } else {
                result = bidataReachStatService.getWeekPriceStats(reachStatDTO);
            }

            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileName = "charge-" + dateStr;

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    FileResponseUtil.buildContentDisposition(fileName, FileResponseUtil.XLSX));

            EasyExcel.write(response.getOutputStream(), MonoPriceStatVO.class)
                    .excludeColumnFieldNames(Arrays.asList("channelCode"))
                    .sheet("sheet1")
                    .doWrite(result);
        } catch (Exception e) {
            log.error("导出数据失败", e);
            throw new BizException("导出失败");
        }
    }

    /**
     * 计费量纵向-列表
     * @param reachStatDTO
     * @return
     */
    @PostMapping("/bill")
    public CommonResult<DataListVO> getBill(@RequestBody ReachStatDTO reachStatDTO) {
        if (reachStatDTO.getType() == null || reachStatDTO.getType() == 1) {
            List<MonoBillStatVO> monoReachStatVOList = bidataReachStatService.getMonthBillStats(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(monoReachStatVOList)
                    .totalCount(monoReachStatVOList.size())
                    .build();
            return CommonResult.success(dataListVO);
        } else {
            List<MonoBillStatVO> monoReachStatVOList = bidataReachStatService.getWeekBillStats(reachStatDTO);
            DataListVO dataListVO = DataListVO.builder()
                    .dataList(monoReachStatVOList)
                    .totalCount(monoReachStatVOList.size())
                    .build();
            return CommonResult.success(dataListVO);
        }
    }

    /**
     * 计费量纵向-导出
     */
    @GetMapping("/bill/download")
    public void downloadBill(HttpServletResponse response,
                               @RequestParam(name = "type", defaultValue = "1") int type,
                               @RequestParam(name = "channelCodes") List<String> channelCodes,
                               @RequestParam(name = "signList", required = false) List<String> signList,
                               @RequestParam(name = "smsTypeCodes", required = false) List<String> smsTypeCodes) {
        try {
            log.info("导出纵向费用, type={}, channelCode={}", type, channelCodes);
            ReachStatDTO reachStatDTO = ReachStatDTO.builder()
                    .type(type)
                    .channelCodes(channelCodes)
                    .signList(signList)
                    .smsTypeCodes(smsTypeCodes)
                    .build();
            List<MonoBillStatVO> result;
            if (reachStatDTO.getType() == null || reachStatDTO.getType() == 1) {
                result = bidataReachStatService.getMonthBillStats(reachStatDTO);
            } else {
                result = bidataReachStatService.getWeekBillStats(reachStatDTO);
            }

            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileName = "charge-" + dateStr;

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    FileResponseUtil.buildContentDisposition(fileName, FileResponseUtil.XLSX));

            EasyExcel.write(response.getOutputStream(), MonoBillStatVO.class)
                    .excludeColumnFieldNames(Arrays.asList("channelCode", "channelCodeName"))
                    .sheet("sheet1")
                    .doWrite(result);
        } catch (Exception e) {
            log.error("导出数据失败", e);
            throw new BizException("导出失败");
        }
    }

    /**
     * 对账单-列表
     *
     * @param reachStatDTO
     * @return
     */
    @PostMapping("/monthly-statement/stats")
    public CommonResult<SubDataListVO> getMonthlyStatementStats(@RequestBody ReachStatDTO reachStatDTO) {
        List<ChannelMonthlyStatementVO> result = bidataReachStatService.getMonthlyStatementStats(reachStatDTO);

        //过滤掉发送成功, 但触达为0的情况
        List<ChannelMonthlyStatementVO> filteredResult = Optional.ofNullable(result)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> item.getTotalBillCount() != null && item.getTotalBillCount() != 0)
                .collect(Collectors.toList());

        Integer totalReachBillCount = filteredResult.stream()
                .map(ChannelMonthlyStatementVO::getTotalBillCount)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);

        BigDecimal totalPriceCount = filteredResult.stream()
                .map(ChannelMonthlyStatementVO::getTotalPriceCount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        SubDataListVO dataListVO = SubDataListVO.builder()
                .dataList(filteredResult)
                .totalCount(filteredResult.size())
                .totalReachBillCount(totalReachBillCount)
                .totalPriceCount(totalPriceCount)
                .build();
        return CommonResult.success(dataListVO);
    }

    /**
     * 对账单-详情记录
     *
     * @param reachStatDTO
     * @return
     */
    @PostMapping("/monthly-statement/records")
    public CommonResult<CommonPager<ChannelMonthlyStatementVO>> getMonthlyStatementRecords(@RequestBody ReachStatDTO reachStatDTO) {
        List<ChannelMonthlyStatementVO> result = bidataReachStatService.getMonthlyStatementRecords(reachStatDTO);

        //过滤掉发送成功, 但触达为0的情况
        List<ChannelMonthlyStatementVO> filteredResult = Optional.ofNullable(result)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> item.getTotalBillCount() != null && item.getTotalBillCount() != 0)
                .collect(Collectors.toList());
        return CommonResult.success(PageResultUtils.result(
                filteredResult::size,
                () -> filteredResult));
    }


    /**
     * 对账单-导出
     */
    @GetMapping("/monthly-statement/download")
    public void downloadByMonthlyStatement(HttpServletResponse response,
                                           @RequestParam String statDate,
                                           @RequestParam String channelCode,
                                           @RequestParam(name = "signList", required = false) List<String> signList) {
        try {
            log.info("导出对账单, statDate={}, channelCode={}", statDate, channelCode);
            ReachStatDTO reachStatDTO = ReachStatDTO.builder()
                    .statDate(statDate)
                    .channelCode(channelCode)
                    .build();
            if (signList != null && !signList.isEmpty()) {
                reachStatDTO.setSignList(signList);
            }
            List<ChannelMonthlyStatementVO> sheet1 = bidataReachStatService.getMonthlyStatementStats(reachStatDTO);
            List<ChannelMonthlyStatementVO> sheet2 = bidataReachStatService.getMonthlyStatementRecords(reachStatDTO);

            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileName = channelCode + dateStr;

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    FileResponseUtil.buildContentDisposition(fileName, FileResponseUtil.XLSX));

            try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).head(ChannelMonthlyStatementVO.class).build()) {

                if (!CollectionUtils.isEmpty(sheet1)) {
                    WriteSheet sheetA = EasyExcel.writerSheet("sheet1").build();
                    excelWriter.write(sheet1, sheetA);
                }

                if (!CollectionUtils.isEmpty(sheet2)) {
                    WriteSheet sheetB = EasyExcel.writerSheet("sheet2").build();
                    excelWriter.write(sheet2, sheetB);
                }

            }

        } catch (Exception e) {
            log.error("导出数据失败", e);
            throw new BizException("导出失败");
        }
    }

    @GetMapping("/monitor/top50")
    public CommonResult<TopTplStatVO> getTopTplByChannel(
            @RequestParam(required = false) List<String> smsTypeCodes,
            @RequestParam String statDate
    ) {

        ReachStatDTO reachStatDTO = ReachStatDTO.builder().smsTypeCodes(smsTypeCodes).statDate(statDate).build();
        if (isValidYearMonthFormat(statDate)) {
            MonthWeekUtils.DateRange dateRange = MonthWeekUtils.getMonthStartAndEnd(statDate);
            reachStatDTO.setStartDate(dateRange.getStartDate());
            reachStatDTO.setEndDate(dateRange.getEndDate());
        } else if (isValidYearWeekFormat(statDate)) {
            MonthWeekUtils.DateRange dateRange = MonthWeekUtils.getWeekStartAndEnd(statDate);
            reachStatDTO.setStartDate(dateRange.getStartDate());
            reachStatDTO.setEndDate(dateRange.getEndDate());
        } else {
            return CommonResult.make(RespCodeEnum.PARAM_ERROR.getCode(), "日期格式错误");
        }
        TopTplStatVO result = bidataReachStatService.queryTopTplChannelReachRate(reachStatDTO);
        return CommonResult.success(result);
    }


    @PostMapping("/channel-price/update-all")
    public CommonResult<Void> updateAll() {
        channelSmsTypePriceLogService.updateAllRecordsWithLatestPrice();
        return CommonResult.success();
    }

    @PostMapping("/channel-price")
    public CommonResult<Void> updateByDate(@RequestBody UpdateChannelPriceDTO updateChannelPriceDTO) {
        log.info("更新渠道价格, updateChannelPriceDTO: {}", updateChannelPriceDTO);
        LocalDate statDate = LocalDate.parse(updateChannelPriceDTO.getDate());
        channelSmsTypePriceLogService.updateRecordsByDate(statDate);
        return CommonResult.success();
    }

}
