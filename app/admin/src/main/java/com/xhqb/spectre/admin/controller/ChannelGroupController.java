package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.model.dto.ChannelGroupDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ChannelGroupVO;
import com.xhqb.spectre.admin.service.ChannelGroupService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ChannelGroupQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2022/4/11 10:45
 * @Description:
 */
@RestController
@RequestMapping("/channelGroup")
@Slf4j
public class ChannelGroupController {

    @Autowired
    private ChannelGroupService channelGroupService;

    /**
     * 分页查询
     *
     * @param query
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(@ModelAttribute ChannelGroupQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<ChannelGroupVO> commonPager = channelGroupService.listByPage(query);
        return AdminResult.success(commonPager);
    }

    /**
     * 详情查询
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(channelGroupService.getById(id));
    }

    /**
     * 添加
     *
     * @param dto
     * @return
     */
    @PostMapping("")
    public AdminResult create(@RequestBody ChannelGroupDTO dto) {
        log.info("create channelGroup, dto: {}", JSON.toJSONString(dto));
        channelGroupService.create(dto);
        return AdminResult.success();
    }

    /**
     * 更新
     *
     * @param id
     * @param dto
     * @return
     */
    @PutMapping("/{id}")
    public AdminResult update(@PathVariable("id") Integer id, @RequestBody ChannelGroupDTO dto) {
        dto.setId(id);
        log.info("update channelGroup, dto: {}", JSON.toJSONString(dto));
        channelGroupService.update(dto);
        return AdminResult.success();
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public AdminResult delete(@PathVariable("id") Integer id) {
        log.info("delete channelGroup, id: {}", id);
        channelGroupService.delete(id);
        return AdminResult.success();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    @PostMapping("/batchDelete")
    public AdminResult batchDelete(@RequestBody List<Integer> idList) {
        log.info("batchDelete channelGroup, idList: {}", JSON.toJSONString(idList));
        channelGroupService.batchDelete(idList);
        return AdminResult.success();
    }

    /**
     * 根据短信类型获取列表
     *
     * @param smsTypeCode
     * @return
     */
    @GetMapping("/getListBySmsTypeAndSign")
    public AdminResult getListBySmsType(String smsTypeCode, Integer signId) {
        return AdminResult.success(channelGroupService.getListBySmsType(smsTypeCode, signId));
    }
}
