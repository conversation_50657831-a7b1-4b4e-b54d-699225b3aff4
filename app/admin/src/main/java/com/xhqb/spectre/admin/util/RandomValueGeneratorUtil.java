package com.xhqb.spectre.admin.util;

import com.xhqb.spectre.admin.exception.BizException;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public final class RandomValueGeneratorUtil {

    /**
     * 生成包含指定数量随机值的列表。
     *
     * @param requestList 请求list
     * @param totalSize   需要生成的随机值总数
     * @return 包含随机值的列表
     */
    public static List<String> randomValues(List<String> requestList, int totalSize) {
        // 输入验证
        validateInput(requestList, totalSize);
        List<String> resultList = new ArrayList<>();
        for (int i = 0; i < totalSize; i++) {
            int randomIndex = (int) (Math.random() * requestList.size());
            resultList.add(requestList.get(randomIndex));
        }
        return resultList;
    }

    public static List<String> randomValues(List<String> requestList, int totalSize, int maxTimes) {
        // 输入验证
        validateInput(requestList, totalSize);
        Set<String> resultSet = new HashSet<>();
        for (int i = 0; i < totalSize; i++) {
            int times = 0;
            int randomIndex = (int) (Math.random() * requestList.size());
            if (!resultSet.contains(requestList.get(randomIndex))) {
                resultSet.add(requestList.get(randomIndex));
                continue;
            }
            while (times < maxTimes) {
                randomIndex = (int) (Math.random() * requestList.size());
                String randomMobile = requestList.get(randomIndex);
                log.info("第:{}个,随机数:{},随机值重复，尝试次数：{}", i, randomMobile, times);
                if (!resultSet.contains(randomMobile)) {
                    resultSet.add(randomMobile);
                    break;
                } else {
                    times++;
                }
            }
        }
        return new ArrayList<>(resultSet);
    }


    private static void validateInput(List<String> requestList, int totalSize) {
        if (requestList == null || requestList.isEmpty()) {
            throw new BizException("测试变量或者手机号不足");
        }
        if (totalSize < 0) {
            throw new BizException("总大小不能小于0");
        }
    }
}
