package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.TaskParamDTO;
import com.xhqb.spectre.admin.model.vo.BatchTaskParamVO;
import com.xhqb.spectre.admin.mq.consumer.handler.BatchTaskParamConsumerHandler;
import com.xhqb.spectre.admin.mq.message.BatchTaskParamMessage;
import com.xhqb.spectre.admin.service.BatchTaskParamService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.BatchTaskDO;
import com.xhqb.spectre.common.dal.entity.BatchTaskParamDO;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.mapper.BatchTaskMapper;
import com.xhqb.spectre.common.dal.mapper.BatchTaskParamMapper;
import com.xhqb.spectre.common.dal.mapper.SignMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.BatchTaskParamQuery;
import com.xhqb.spectre.common.enums.BatchTaskParamStatusEnum;
import com.xhqb.spectre.common.enums.BatchTaskStatusEnum;
import com.xhqb.spectre.common.enums.DeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 群发参数服务
 *
 * <AUTHOR>
 * @date 2021/10/21
 */
@Service
@Slf4j
public class BatchTaskParamServiceImpl implements BatchTaskParamService {

    /**
     * replay 调用线程池
     */
    private static final ExecutorService TASK_REPLAY_POOL = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 2,
            100,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("task-replay-pool-%d").build()
    );

    @Resource
    private BatchTaskParamMapper batchTaskParamMapper;
    @Resource
    private BatchTaskMapper batchTaskMapper;
    @Resource
    private BatchTaskParamConsumerHandler batchTaskParamConsumerHandler;
    @Resource
    private SignMapper signMapper;
    @Resource
    private TplMapper tplMapper;

    /**
     * 分页查询群发参数任务列表
     *
     * @param batchTaskParamQuery
     * @return
     */
    @Override
    public CommonPager<BatchTaskParamVO> listByPage(BatchTaskParamQuery batchTaskParamQuery) {
        return PageResultUtils.result(
                () -> batchTaskParamMapper.countByQuery(batchTaskParamQuery),
                () -> batchTaskParamMapper.selectByQuery(batchTaskParamQuery).stream().map(BatchTaskParamVO::buildListQuery).collect(Collectors.toList())
        );
    }

    /**
     * 根据群发任务ID查询批量参数信息
     *
     * @param taskId
     * @param needParamJson 是否需要查询mapping数据信息
     * @return
     */
    @Override
    public List<BatchTaskParamVO> getByTaskId(Integer taskId, Boolean needParamJson) {
        List<BatchTaskParamDO> batchTaskParamList = batchTaskParamMapper.selectByTaskIdAndMapping(taskId, Objects.equals(needParamJson, true) ? 1 : null);
        if (CommonUtil.isEmpty(batchTaskParamList)) {
            return null;
        }
        return batchTaskParamList.stream().map(BatchTaskParamVO::buildInfoQuery).collect(Collectors.toList());
    }

    /**
     * 更新群发短信任务参数信息
     *
     * @param id
     * @param taskParamDTO
     */
    @Override
    public void update(Integer id, TaskParamDTO taskParamDTO) {
        //参数格式校验
        ValidatorUtil.validate(taskParamDTO);
        BatchTaskParamDO batchTaskParamDO = new BatchTaskParamDO();
        batchTaskParamDO.setId(id);
        batchTaskParamDO.setSendStatus(taskParamDTO.getSendStatus());
        batchTaskParamDO.setStartOffset(taskParamDTO.getStartOffset());
        batchTaskParamDO.setEndOffset(taskParamDTO.getEndOffset());
        batchTaskParamDO.setIsDelete(taskParamDTO.getIsDelete());
        batchTaskParamDO.setParamJsonArray(taskParamDTO.getParamJsonArray());
        batchTaskParamDO.setFileMd5(taskParamDTO.getFileMd5());
        batchTaskParamMapper.updateByPrimaryKeySelective(batchTaskParamDO);
    }

    /**
     * 删除群发任务参数
     * <p>
     * 将is_delete打上删除标记
     *
     * @param id
     */
    @Override
    public void delete(Integer id) {
        BatchTaskParamDO batchTaskParamDO = new BatchTaskParamDO();
        batchTaskParamDO.setId(id);
        batchTaskParamDO.setIsDelete(DeleteEnum.DELETED.getCode());
        batchTaskParamMapper.updateByPrimaryKeySelective(batchTaskParamDO);
        log.info("删除群发任务参数成功 = {}", JSON.toJSONString(batchTaskParamDO));
    }

    /**
     * 重置群发参数状态
     * <p>
     * 会将deleted状态设置为0
     * 并将任务状态设置为0
     *
     * @param id
     */
    @Override
    public void reset(Integer id) {
        BatchTaskParamDO batchTaskParamDO = new BatchTaskParamDO();
        batchTaskParamDO.setId(id);
        batchTaskParamDO.setIsDelete(DeleteEnum.NORMAL.getCode());
        batchTaskParamDO.setSendStatus(BatchTaskParamStatusEnum.UN_SEND.getCode());
        batchTaskParamMapper.updateByPrimaryKeySelective(batchTaskParamDO);
        log.info("重置群发参数状态成功 = {}", JSON.toJSONString(batchTaskParamDO));
    }

    /**
     * 群发参数重新发送处理
     *
     * @param id
     */
    @Override
    public void replay(Integer id) {
        BatchTaskParamDO batchTaskParamDO = batchTaskParamMapper.selectByPrimaryKey(id);
        if (Objects.isNull(batchTaskParamDO)) {
            throw new BizException("未找到群发参数信息");
        }

        Integer sendStatus = batchTaskParamDO.getSendStatus();
        if (!Objects.equals(sendStatus, BatchTaskParamStatusEnum.UN_SEND.getCode())) {
            throw new BizException("群发参数状态不是为未发送状态");
        }

        BatchTaskDO batchTaskDO = batchTaskMapper.selectByPrimaryKey(batchTaskParamDO.getTaskId());
        if (Objects.isNull(batchTaskDO)) {
            throw new BizException("群发任务不存在");
        }

        // 补发时验证群发任务状态
        // 取消、未提交、已提交状态不能够进行补发
        Integer taskStatus = batchTaskDO.getStatus();
        if (Objects.equals(taskStatus, BatchTaskStatusEnum.CANCELLED.getCode())) {
            // 取消时 不能够发送
            throw new BizException("群发任务为已取消,不能够再重发");
        }

        if (Objects.equals(taskStatus, BatchTaskStatusEnum.UN_COMMIT.getCode())) {
            // 未提交时不能够发送
            throw new BizException("群发任务为未提交,不需要进行重发操作");
        }

        if (Objects.equals(taskStatus, BatchTaskStatusEnum.COMMITTED.getCode())) {
            // 已提交状态不需要重发
            throw new BizException("群发任务为已提交,不需要进行重发操作");
        }

        SignDO signDO = signMapper.selectByPrimaryKey(batchTaskDO.getSignId());
        if (Objects.isNull(signDO)) {
            throw new BizException("当前群发任务的签名信息不存在");
        }

        if (!Objects.equals(CommonConstant.STATUS_VALID, signDO.getStatus())) {
            throw new BizException("当前群发任务的签名状态无效");
        }

        TplDO tplDO = tplMapper.selectByPrimaryKey(batchTaskDO.getTplId());
        if (Objects.isNull(tplDO)) {
            throw new BizException("当前群发任务的模板信息不存在");
        }

        if (!Objects.equals(CommonConstant.STATUS_VALID, tplDO.getStatus())) {
            throw new BizException("当前群发任务的模板状态无效");
        }

        // 补发群发分片任务消息
        BatchTaskParamMessage batchTaskParamMessage = BatchTaskParamMessage.builder()
                .taskId(batchTaskDO.getId())
                .taskParamId(batchTaskParamDO.getId())
                .appCode(batchTaskDO.getAppCode())
                .tplCode(tplDO.getCode())
                .signName(signDO.getName())
                .smsCodeType(tplDO.getSmsTypeCode())
                // 设置模板内容类型 2022-02-22
                .tplType(batchTaskDO.getTplType())
                .build();

        String message = JSON.toJSONString(batchTaskParamMessage);
        TASK_REPLAY_POOL.execute(() -> batchTaskParamConsumerHandler.handler(message));
        log.info("补发群发分片任务提交成功 = {}", message);
    }

    /**
     * 保存或更新
     *
     * @param batchTaskParamDO
     */
    @Override
    public void saveOrUpdate(BatchTaskParamDO batchTaskParamDO) {
        Integer id = batchTaskParamDO.getId();
        if (Objects.isNull(id) || id <= 0) {
            batchTaskParamDO.setId(null);
            batchTaskParamMapper.insertSelective(batchTaskParamDO);
            return;
        }
        batchTaskParamMapper.updateByPrimaryKeySelective(batchTaskParamDO);
    }
}
