package com.xhqb.spectre.admin.openapi.constants;

/**
 * 开放接口应用常量
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
public interface OpenApiConstants {

    interface Redis {
        /**
         * redis前缀
         */
        String PREFIX = "spectre:admin:openapi:";
        /**
         * 随机数缓存(string类型)
         */
        String NONCE_KEY_STR = PREFIX + "nonce:";
    }

    /**
     * 请求头
     */
    interface Header {
        /**
         * 签名
         */
        String SIGN = "sign";
        /**
         * app key
         */
        String APP_KEY = "appKey".toLowerCase();
        /**
         * 时间搓
         */
        String TIMESTAMP = "timestamp";
        /**
         * 随机数
         */
        String NONCE = "nonce";
    }

}
