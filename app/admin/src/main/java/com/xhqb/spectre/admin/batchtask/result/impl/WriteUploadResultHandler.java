package com.xhqb.spectre.admin.batchtask.result.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.result.UploadResultContext;
import com.xhqb.spectre.admin.batchtask.result.UploadResultHandler;
import com.xhqb.spectre.admin.model.dto.BatchTaskParamDTO;
import com.xhqb.spectre.admin.model.vo.BatchTaskUploadVO;
import com.xhqb.spectre.admin.model.vo.batchtask.ParamItemVO;
import com.xhqb.spectre.admin.model.vo.batchtask.QueryTaskSegmentVO;
import com.xhqb.spectre.admin.model.vo.batchtask.UploadQueryVO;
import com.xhqb.spectre.admin.service.BatchTaskService;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.BatchTaskParamDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 将上传文件内容分片写入数据库
 * <p>
 * 写入的数据就是 UploadQueryVO 这个对象的数据
 * 主要包含文件检测结果，以及文件分片索引
 * <p>
 * 分片内容就是BatchTaskParamDTO 对象
 *
 * <AUTHOR>
 * @date 2021/10/1
 */
@Component
@Slf4j
public class WriteUploadResultHandler implements UploadResultHandler<BatchTaskUploadVO, Void> {

    /**
     * 短信json参数配置数组，最大1000条记录
     */
    @Value("${spectre.admin.taskParamLength:1000}")
    private Integer taskParamLength;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private BatchTaskService batchTaskService;

    /**
     * 上传结果处理
     * <p>
     * 将上传文件内容分片写入redis缓存
     * <p>
     * 写入的数据就是 UploadQueryVO 这个对象的数据
     * 主要包含文件检测结果，以及文件分片索引
     * <p>
     * 分片内容就是BatchTaskParamDTO 对象
     *
     * @param context
     * @return
     */
    @Override
    public Void handler(UploadResultContext<BatchTaskUploadVO> context) {
        String taskNo = context.getTaskNo();
        BatchTaskUploadVO uploadVO = context.getData();
        // 查询结果
        UploadQueryVO uploadQueryVO = new UploadQueryVO();
        uploadQueryVO.setStatus(BatchTaskConstants.UploadQueryStatus.COMPLETE);
        uploadQueryVO.setCheckResult(uploadVO.getCheckResult());

        long start = System.currentTimeMillis();
        log.info("上传文件内容开始进行分片处理,taskNo = {}", taskNo);
        BatchTaskParamDTO taskParamItem = uploadVO.getTaskParamItem();
        List<ParamItemVO> paramList = taskParamItem.getParamList();
        String fileMd5 = taskParamItem.getFileMd5();
        List<Integer> segmentList;
        if (Objects.isNull(paramList) || paramList.isEmpty()) {
            // 数据为空表示没有有效的数据
            // 直接写入数据库
            BatchTaskParamDO batchTaskParamDO = buildBatchTaskParamDO(fileMd5, Lists.newArrayList());
            segmentList = batchTaskService.batchInsertParamList(Lists.newArrayList(batchTaskParamDO));
        } else {
            // 分批次写入数据库
            segmentList = this.doSegment(paramList, fileMd5);
        }
        log.info("上传文件内容分片处理完成,耗时 = {}, segmentList = {}, taskNo = {}", (System.currentTimeMillis() - start), segmentList, taskNo);

        QueryTaskSegmentVO queryTaskSegmentVO = new QueryTaskSegmentVO();
        queryTaskSegmentVO.setTaskNo(taskNo);
        queryTaskSegmentVO.setSegmentList(segmentList);
        queryTaskSegmentVO.setFileMd5(taskParamItem.getFileMd5());
        uploadQueryVO.setTaskParamItem(queryTaskSegmentVO);

        start = System.currentTimeMillis();
        log.info("上传文件分片信息开始写入缓存, taskNo = {}", taskNo);
        String redisKey = RedisKeys.BatchTaskKeys.BATCH_TASK_UPLOAD_RESULT_STR_KEY + ":" + taskNo;
        // 写入文件上传结果
        stringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(uploadQueryVO), BatchTaskConstants.Commons.UPLOAD_RESULT_CACHE_EXPIRE, TimeUnit.MINUTES);
        log.info("上传文件分片信息开始写入缓存完成,耗时 = {}, 分片数量 = {}, taskNo = {}", (System.currentTimeMillis() - start), segmentList, taskNo);
        return null;
    }

    /**
     * 做数据切分
     *
     * @param paramList
     * @param fileMd5
     * @return
     */
    private List<Integer> doSegment(List<ParamItemVO> paramList, String fileMd5) {
        // 计算分段数据量
        int paramSize = paramList.size();
        int segment = paramSize / taskParamLength;
        List<BatchTaskParamDO> resultList = new ArrayList<>(segment + 1);
        int startOffset = 0;
        int endOffset = 0;
        BatchTaskParamDO batchTaskParamDO;
        for (int i = 0; i < segment; i++) {
            startOffset = i * taskParamLength;
            endOffset = (i + 1) * taskParamLength;
            batchTaskParamDO = buildBatchTaskParamDO(fileMd5, paramList.subList(startOffset, endOffset));
            batchTaskParamDO.setStartOffset(startOffset);
            batchTaskParamDO.setEndOffset(endOffset);
            resultList.add(batchTaskParamDO);
        }

        if (paramSize % taskParamLength != 0) {
            startOffset = (paramSize / taskParamLength) * taskParamLength;
            endOffset = paramSize;
            batchTaskParamDO = buildBatchTaskParamDO(fileMd5, paramList.subList(startOffset, endOffset));
            batchTaskParamDO.setStartOffset(startOffset);
            batchTaskParamDO.setEndOffset(endOffset);
            resultList.add(batchTaskParamDO);
        }

        // 群发参数入库
        // 返回切片ID
        return batchTaskService.batchInsertParamList(resultList);
    }

    /**
     * 构建任务参数对象
     *
     * @param fileMd5
     * @param paramList
     * @return
     */
    private BatchTaskParamDO buildBatchTaskParamDO(String fileMd5, List<ParamItemVO> paramList) {
        return BatchTaskParamDO.builder()
                // 任务ID
                .taskId(0)
                // 上传名单文件的md5值
                .fileMd5(fileMd5)
                .paramJsonArray(JSON.toJSONString(paramList))
                .build();
    }

}
