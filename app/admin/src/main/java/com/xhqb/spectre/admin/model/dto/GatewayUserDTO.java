package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.GatewayUserDO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 网关账号管理(王建政)
 *
 * <AUTHOR>
 * @date 2021/11/8
 */
@Data
public class GatewayUserDTO implements Serializable {

    private static final long serialVersionUID = 6153345194848075L;

    /**
     * 分组名称
     */
    @NotBlank(message = "分组名称不能为空")
    private String groupName;
    /**
     * 企业账号
     */
    private String userName;
    /**
     * 企业密码
     */
    @NotBlank(message = "企业密码不能为空")
    private String password;
    /**
     * 企业代码
     */
    @NotBlank(message = "企业代码不能为空")
    private String spCode;
    /**
     * 服务代码
     */
    @NotBlank(message = "服务代码不能为空")
    private String serviceId;
    /**
     * 信息内容来源
     */
    @NotBlank(message = "信息内容来源不能为空")
    private String msgSrc;

    /**
     * 签名ID列表
     */
    @NotEmpty(message = "支持的签名不能为空")
    private List<Integer> signIdList;

    /**
     * 接入IP白名单列表
     */
    private List<String> whiteIpList;

    /**
     * 模板文案检测，1：开启；0：不开启
     */
    private Integer checkTplFlag;

    public static GatewayUserDO toDO(GatewayUserDTO gatewayUserDTO) {
        return GatewayUserDO.builder()
                // 分组名称
                .groupName(gatewayUserDTO.getGroupName())
                // 企业账号
                .userName(gatewayUserDTO.getUserName())
                // 企业密码
                .password(gatewayUserDTO.getPassword())
                // 企业代码
                .spCode(gatewayUserDTO.getSpCode())
                // 服务代码
                .serviceId(gatewayUserDTO.getServiceId())
                // 信息内容来源
                .msgSrc(gatewayUserDTO.getMsgSrc())
                // 模板编码（格式：用户名_tpl）
                .tplCode("cmpp_" + gatewayUserDTO.getUserName() + "_tpl")
                // 签名ID列表
                .signIdList(gatewayUserDTO.getSignIdList().stream().map(String::valueOf).collect(Collectors.joining(",")))
                // 接入IP白名单列表
                .whiteIpList(CollectionUtils.isNotEmpty(gatewayUserDTO.getWhiteIpList()) ? String.join(",", gatewayUserDTO.getWhiteIpList()) : "")
                // 模板文案检测，1：开启；0：不开启
                .isCheckTpl(gatewayUserDTO.getCheckTplFlag())
                // 设置 更新人
                .updater(SsoUserInfoUtil.getUserName())
                .build();
    }
}
