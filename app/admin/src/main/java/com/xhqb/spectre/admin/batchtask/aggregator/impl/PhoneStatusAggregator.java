package com.xhqb.spectre.admin.batchtask.aggregator.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xhqb.spectre.admin.batchtask.aggregator.AbstractDataAggregator;
import com.xhqb.spectre.admin.batchtask.aggregator.AggregatorResult;
import com.xhqb.spectre.admin.batchtask.aggregator.AsyncTask;
import com.xhqb.spectre.admin.batchtask.cid.CidStrategyFactory;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.parse.ContentItem;
import com.xhqb.spectre.admin.batchtask.send.MessageSendResult;
import com.xhqb.spectre.admin.batchtask.send.SpectreApiBooster;
import com.xhqb.spectre.admin.batchtask.send.query.*;
import com.xhqb.spectre.admin.batchtask.validate.ValidateContext;
import com.xhqb.spectre.admin.batchtask.validate.ValidateResult;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 手机号码状态聚合器
 *
 * <AUTHOR>
 * @date 2022/1/18
 */
@Component
@Slf4j
public class PhoneStatusAggregator extends AbstractDataAggregator implements AsyncTask.AsyncTaskHandler {

    /**
     * 最大的查询手机号码
     */
    private static final int MAX_PHONE_NUM = 100;

    /**
     * 手机号码状态检测
     */
    private static final ExecutorService PHONE_STATUS_POOL = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 2,
            100,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("phone-status-pool-%d").build()
    );

    @Resource
    private PhoneStatusQuerySender phoneStatusQuerySender;
    @Autowired
    private VenusConfig venusConfig;
    @Resource
    private SpectreApiBooster spectreApiBooster;
    @Resource
    private CidStrategyFactory cidStrategyFactory;

    /**
     * 返回聚合结果
     *
     * @param sourceDataList
     * @param validateContext
     * @param validateResult
     * @return
     */
    @Override
    protected AggregatorResult doAggregate(List<ContentItem> sourceDataList, ValidateContext validateContext, ValidateResult validateResult) {
        long start = System.currentTimeMillis();
        // 原始数据数量
        int sourceSize = sourceDataList.size();
        AggregatorResult aggregatorResult;
        if (sourceSize <= MAX_PHONE_NUM) {
            // 数据量小于切分数量 不需要异步处理
            aggregatorResult = new AggregatorResult();
            this.asyncTaskHandler(sourceDataList, validateContext, validateResult, aggregatorResult);
            log.info("手机号码数据检测同步耗时 = {}, fileName = {}, fileMd5 = {}", (System.currentTimeMillis() - start), validateContext.getFileName(), validateContext.getFileMd5());
            return aggregatorResult;
        }

        // 切割片段
        int segment = sourceSize / MAX_PHONE_NUM;
        // 总共做任务的数量
        int latchCount = sourceSize % MAX_PHONE_NUM != 0 ? segment + 1 : segment;
        CountDownLatch latch = new CountDownLatch(latchCount);
        List<AggregatorResult> aggregatorResultList = new ArrayList<>(segment + 1);
        for (int i = 0; i < segment; i++) {
            List<ContentItem> contentItemList = sourceDataList.subList(i * MAX_PHONE_NUM, (i + 1) * MAX_PHONE_NUM);
            aggregatorResult = new AggregatorResult();
            aggregatorResultList.add(aggregatorResult);
            PHONE_STATUS_POOL.execute(new AsyncTask(latch, contentItemList, validateContext, validateResult, aggregatorResult, this));
        }

        if (sourceSize % MAX_PHONE_NUM != 0) {
            List<ContentItem> contentItemList = sourceDataList.subList((sourceSize / MAX_PHONE_NUM) * MAX_PHONE_NUM, sourceSize);
            aggregatorResult = new AggregatorResult();
            aggregatorResultList.add(aggregatorResult);
            PHONE_STATUS_POOL.execute(new AsyncTask(latch, contentItemList, validateContext, validateResult, aggregatorResult, this));
        }

        try {
            latch.await();
        } catch (Exception e) {
            throw new RuntimeException("手机号码状态检测处理失败");
        }
        // 做聚合操作
        aggregatorResult = AggregatorResult.toAggregatorResult(aggregatorResultList, sourceSize);
        log.info("手机号码状态检测异步耗时 = {}, fileName = {}, fileMd5 = {}", (System.currentTimeMillis() - start), validateContext.getFileName(), validateContext.getFileMd5());
        return aggregatorResult;
    }

    /**
     * 异步任务处理方法
     * <p>
     * 查询号码状态
     *
     * @param contentItemList
     * @param validateContext
     * @param validateResult
     * @param aggregatorResult
     */
    @Override
    public void asyncTaskHandler(List<ContentItem> contentItemList, ValidateContext validateContext, ValidateResult validateResult, AggregatorResult aggregatorResult) {
        // 做cid/mobile数据映射
        Map<String, String> mobileMapping = toHashMap(contentItemList, validateContext, validateResult);

        // 查询手机状态
        List<PhoneResult> phoneResults = queryPhoneStatus(mobileMapping);
        if (CommonUtil.isEmpty(phoneResults)) {
            // 所有数据有效
            aggregatorResult.setValidateList(contentItemList);
            aggregatorResult.setPhoneEmptyList(new ArrayList<>());
            aggregatorResult.setPhoneHaltList(new ArrayList<>());
            return;
        }

        // 待过滤的手机号码状态
        // List<String> phoneStatusList = cidStrategyFactory.toListByKey(CidStrategyEnum.PHONE_STATUS.getCode());
        List<String> phoneStatusList = validateContext.getPhoneStatusList();
        List<String> phoneEmptyGroupList = new ArrayList<>();
        List<String> phoneHaltGroupList = new ArrayList<>();
        String hitStrategyName;
        for (PhoneResult phoneResult : phoneResults) {
            hitStrategyName = getHitStrategyName(phoneResult, phoneStatusList);
            if (StringUtils.isBlank(hitStrategyName)) {
                continue;
            }

            if (StringUtils.equals(hitStrategyName, PhoneStatusMapping.PHONE_EMPTY.getName())) {
                // 空号
                phoneEmptyGroupList.add(phoneResult.getMobile());
            } else if (StringUtils.equals(hitStrategyName, PhoneStatusMapping.PHONE_HALT.getName())) {
                // 停机
                phoneHaltGroupList.add(phoneResult.getMobile());
            }
        }


        // 空号数据
        List<ContentItem> phoneEmptyList = new ArrayList<>(phoneEmptyGroupList.size());
        // 停机数据
        List<ContentItem> phoneHaltList = new ArrayList<>(phoneHaltGroupList.size());
        // 存在的有效数据
        List<ContentItem> validList = contentItemList.stream().filter(s -> {
            // 从手机映射中获取到手机号码
            String phone = mobileMapping.get(s.getContent());

            if (phoneEmptyGroupList.contains(phone)) {
                phoneEmptyList.add(s);
                return false;
            }

            if (phoneHaltGroupList.contains(phone)) {
                phoneHaltList.add(s);
                return false;
            }

            // 有效的数据
            return true;
        }).collect(Collectors.toList());

        aggregatorResult.setPhoneEmptyList(phoneEmptyList);
        aggregatorResult.setPhoneHaltList(phoneHaltList);
        aggregatorResult.setValidateList(validList);
    }


    /**
     * 对cid/mobile数据做映射 转换成hashmap
     *
     * @param contentItemList
     * @param validateContext
     * @param validateResult
     * @return
     */
    private Map<String, String> toHashMap(List<ContentItem> contentItemList, ValidateContext validateContext, ValidateResult validateResult) {
        // 当前数据类型是否属于cid
        boolean isCid = StringUtils.equalsIgnoreCase(validateContext.getContentType(), BatchTaskConstants.DataType.CID);
        // 手机号码与cid映射
        // key -> cid/mobile  value -> mobile
        Map<String, String> mobileMapping = new HashMap<>(contentItemList.size());
        String content;
        String mobile;
        for (ContentItem contentItem : contentItemList) {
            content = contentItem.getContent();
            mobile = isCid ? validateResult.getMobile(content) : content;
            mobileMapping.put(content, mobile);
        }
        return mobileMapping;
    }

    /**
     * 查询手机状态
     *
     * @param mobileMapping
     * @return 返回null 则表示查询失败 不进行数据校验
     */
    private List<PhoneResult> queryPhoneStatus(Map<String, String> mobileMapping) {
        long start = System.currentTimeMillis();
        PhoneStatusRequest messageRequest = new PhoneStatusRequest();
        messageRequest.setAppCode(venusConfig.getDefaultAppCode());
        messageRequest.setAppSecret(venusConfig.getDefaultAppSecret());
        messageRequest.setRequestId(spectreApiBooster.getNonce());
        messageRequest.setPhoneNumbers(String.join(",", mobileMapping.values()));
        try {
            // 如果请求失败 那么则认为所有号码都正常
            MessageSendResult<PhoneStatusResponse> result = phoneStatusQuerySender.send(messageRequest);
            if (venusConfig.isLogEnable()) {
                log.info("查询手机状态送请求结果 = {},请求参数 = {},cost = {}", JSON.toJSONString(result), messageRequest, (System.currentTimeMillis() - start));
            }

            PhoneStatusResponse data = result.getData();
            if (Objects.isNull(data)) {
                log.warn("查询手机状态返回data数据为空 = {},请求参数 = {},cost = {}", JSON.toJSONString(result), messageRequest, (System.currentTimeMillis() - start));
                return null;
            }

            return data.getPhoneResult();
        } catch (Exception e) {
            log.warn("查询手机发送状态请求失败,请求参数 = {}", messageRequest, e);
        }
        return null;
    }


    /**
     * 获取到策略命中的名称
     *
     * @param phoneResult
     * @param phoneStatusList
     * @return
     */
    private String getHitStrategyName(PhoneResult phoneResult, List<String> phoneStatusList) {
        if (CommonUtil.isEmpty(phoneStatusList)) {
            return null;
        }

        if (Objects.isNull(phoneResult)) {
            return null;
        }

        Integer status = phoneResult.getStatus();
        // 配置的策略名称
        PhoneStatusMapping phoneStatusMapping = PhoneStatusMapping.getByStatus(status);

        if (Objects.isNull(phoneStatusMapping)) {
            return null;
        }

        String strategyName = phoneStatusMapping.getName();
        // 只有配置了的手机状态策略策略才进行命中操作
        return phoneStatusList.contains(strategyName) ? strategyName : null;
    }

}
