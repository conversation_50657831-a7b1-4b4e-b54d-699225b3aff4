package com.xhqb.spectre.admin.statistics.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyan<PERSON>ong
 * @Date: 2021/11/3 10:23
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsStatisSumDO implements Serializable {

    private static final long serialVersionUID = 2751449372754259551L;

    /**
     * 统计时间（3种时间维度：日期、时间、月份）
     */
    private String date;

    /**
     * 指标值
     */
    private String classValue;

    /**
     * 发送量
     */
    private Integer sendCount;

    /**
     * 计费量
     */
    private Integer billCount;
}
