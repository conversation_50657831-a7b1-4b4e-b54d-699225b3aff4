package com.xhqb.spectre.admin.batchtask.aggregator.datasource;

import com.xhqb.spectre.admin.batchtask.aggregator.CustomerDataHandler;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.model.vo.batchtask.CustomerVO;
import com.xhqb.spectre.admin.service.CifEnterpriseCustomerService;
import com.xhqb.spectre.admin.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * cifdb.t_enterprise_customer_base数据查询处理
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Component
public class CifEnterpriseCustomerDataHandler implements CustomerDataHandler {

    /**
     * 当前只支持 竹叶小微 的签名
     */
    private static final String SIGN_NAME = "【竹叶小微】".trim();

    @Resource
    private CifEnterpriseCustomerService cifEnterpriseCustomerService;

    /**
     * 数据来源查询
     *
     * @param cidList     cid 列表
     * @param smsTypeCode 短信类型
     * @return
     */
    @Override
    public Map<String, CustomerVO> query(List<String> cidList, String smsTypeCode) {
        if (CommonUtil.isEmpty(cidList)) {
            return null;
        }
        return cifEnterpriseCustomerService.mapByCidList(cidList, smsTypeCode);
    }

    /**
     * 判断当前处理器是否支持指定的签名名称
     *
     * @param signName
     * @return
     */
    @Override
    public boolean supports(String signName) {
        // 竹叶小微
        return StringUtils.equalsIgnoreCase(SIGN_NAME, signName);
    }

    @Override
    public int getOrder() {
        return BatchTaskConstants.DataHandlerOrdered.CIF_ENTERPRISE_CUSTOMER;
    }
}
