package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.vo.BatchTaskLogVO;
import com.xhqb.spectre.common.dal.entity.BatchTaskLogDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.BatchTaskLogQuery;

import java.util.List;

/**
 * 群发任务流水日志表
 *
 * <AUTHOR>
 * @date 2021/12/1
 */
public interface BatchTaskLogService {

    /**
     * 批量插入任务流水信息
     *
     * @param batchTaskLogList
     */
    void batchInsert(List<BatchTaskLogDO> batchTaskLogList);

    /**
     * 分页查询群发任务日志
     *
     * @param batchTaskLogQuery
     * @return
     */
    CommonPager<BatchTaskLogVO> listByPage(BatchTaskLogQuery batchTaskLogQuery);

    /**
     * 根据群发任务ID查询群发日志
     *
     * @param taskId
     * @return
     */
    List<BatchTaskLogVO> getByTaskId(Integer taskId);
}
