package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.TplOaReportVO;
import com.xhqb.spectre.admin.service.FeiShuAlert;
import com.xhqb.spectre.admin.service.TplOaReportService;
import com.xhqb.spectre.admin.service.oa.vo.FlowDataAllInfo;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.TplOaReportQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/*
 * @Author: yang<PERSON><PERSON><PERSON>
 * @Date: 2024/11/05 16:09
 * @Description: 短信模板内容OA 报备
 */
@RestController
@RequestMapping("/tplOaReport")
@Slf4j
public class TplOaReportController {


    @Resource
    private TplOaReportService tplOaReportService;

    @Resource
    private FeiShuAlert feiShuAlert;

    /**
     * 分页查询OA流程审批记录信息
     *
     * @param query    查询条件对象，包含报告类型、状态等过滤条件
     * @param pageNum  当前页码
     * @param pageSize 每页显示记录数
     * @return 包含分页结果和数据的CommonResult对象，若查询成功则返回成功状态和分页后的OA流程审批记录信息
     */
    @GetMapping("/page")
    public CommonResult<CommonPager<TplOaReportVO>> page(@ModelAttribute TplOaReportQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        return CommonResult.success(tplOaReportService.page(query));
    }

    /**
     * 审批流程记录信息
     *
     * @param flowId 审批流程的ID
     * @return 返回一个包含审批结果的CommonResult对象，如果审批成功，则包含List<FlowDataAllInfo>对象
     */
    @GetMapping("/approve")
    public CommonResult<List<FlowDataAllInfo>> approve(String flowId, String userId) {
        return CommonResult.success(tplOaReportService.approve(flowId, userId));
    }

    @PostMapping("/testNotify")
    public CommonResult<String> testNotify(String strategyId, @RequestBody Map<String, Object> dataMap) {
        feiShuAlert.sendFeiShuAlert(dataMap, strategyId);
        return CommonResult.success();
    }


}
