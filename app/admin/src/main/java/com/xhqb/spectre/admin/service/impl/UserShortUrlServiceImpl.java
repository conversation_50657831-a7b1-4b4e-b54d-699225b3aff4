package com.xhqb.spectre.admin.service.impl;


import cn.hutool.core.date.DateUnit;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.huaweicloud.sdk.core.utils.JsonUtils;
import com.xhqb.kael.util.Md5Utils;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.batchtask.parse.ParseHandler;
import com.xhqb.spectre.admin.batchtask.parse.UserShortUrlParseContext;
import com.xhqb.spectre.admin.batchtask.upload.cos.S3Helper;
import com.xhqb.spectre.admin.batchtask.utils.UploadFileUtils;
import com.xhqb.spectre.admin.cif.entity.CifCustomerBaseDO;
import com.xhqb.spectre.admin.cif.mapper.CifCustomerBaseMapper;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.UserShortUrlDTO;
import com.xhqb.spectre.admin.model.dto.UserShortUrlDataDTO;
import com.xhqb.spectre.admin.model.dto.UserShortUrlRequest;
import com.xhqb.spectre.admin.model.result.FileCheckResult;
import com.xhqb.spectre.admin.model.user.url.UserShortUrlFileInfo;
import com.xhqb.spectre.admin.model.vo.UserShortUrlResponse;
import com.xhqb.spectre.admin.model.vo.UserShortUrlVO;
import com.xhqb.spectre.admin.service.UserShortUrlService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.UserShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.UserShortUrlDOMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.UserShortUrlQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@Slf4j
public class UserShortUrlServiceImpl implements UserShortUrlService {

    @Resource
    private ObtainShortCodeService obtainShortCodeService;

    @Resource
    private UserShortUrlDOMapper userShortUrlMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private ParseHandler parseHandler;

    @Resource
    private S3Helper s3Helper;

    @Resource
    private CifCustomerBaseMapper cifCustomerBaseMapper;

    private static final String SLASH_BAR_STR = "/";

    @Override
    public CommonPager<UserShortUrlVO> page(UserShortUrlQuery userShortUrlQuery) {
        return PageResultUtils.result(
                () -> userShortUrlMapper.countByQuery(userShortUrlQuery),
                () -> userShortUrlMapper.selectByQuery(userShortUrlQuery)
                        .stream().map(this::buildUserShortUrlVO).collect(Collectors.toList())
        );
    }

    @Override
    public String add(UserShortUrlDTO userShortUrlDTO) {
        UserShortUrlRequest userShortUrlRequest = new UserShortUrlRequest();
        UserShortUrlDataDTO userShortUrlDataDTO = new UserShortUrlDataDTO();
        userShortUrlRequest.setTplCode(userShortUrlDTO.getTplCode());
        userShortUrlDataDTO.setMobile(userShortUrlDTO.getMobile());
        userShortUrlDataDTO.setCid(userShortUrlDTO.getCid());
        userShortUrlRequest.setDataList(Collections.singletonList(userShortUrlDataDTO));
        List<UserShortUrlResponse> responseList = obtainShortCodeService.handler(userShortUrlRequest).stream()
                .filter(response -> Objects.equals(response.getMobile(), userShortUrlDTO.getMobile())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(responseList)) {
            throw new BizException("obtain short code error");
        }
        return "success";
    }

    @Override
    public UserShortUrlVO detail(Long id) {
        UserShortUrlDO model = userShortUrlMapper.selectByPrimaryKey(id);
        if (Objects.isNull(model)) {
            throw new BizException("user short url not exist");
        }

        return buildUserShortUrlVO(model);
    }

    @Override
    public FileCheckResult fileCheckResult(String fileName) {
        FileCheckResult fileCheckResult = this.parse(fileName);
        // 打印解析结果
        log.info("fileCheckResult:{}", JsonLogUtil.toJSONString(fileCheckResult));
        log.info("key:{}", RedisKeys.ADMIN_CACHE_KEY_PREFIX + fileName);
        FileCheckResult tempFileCheckResult = JsonUtils.toObject(JsonUtils.toJSON(fileCheckResult), FileCheckResult.class);
        tempFileCheckResult.setValidList(Collections.emptyList());
        redisTemplate.opsForValue().setIfAbsent(RedisKeys.ADMIN_CACHE_KEY_PREFIX + fileName, JSON.toJSONString(fileCheckResult));
        redisTemplate.expire(RedisKeys.ADMIN_CACHE_KEY_PREFIX + fileName, 300000, TimeUnit.MILLISECONDS);
        return tempFileCheckResult;
    }

    @Override
    public String fileUploadAdd(String fileName) {
        log.info("key:{}", RedisKeys.ADMIN_CACHE_KEY_PREFIX + fileName);
        Object jsonResult = redisTemplate.opsForValue().get(RedisKeys.ADMIN_CACHE_KEY_PREFIX + fileName);
        log.info("jsonResult:{}", JsonLogUtil.toJSONString(jsonResult));
        FileCheckResult validateResult = JSON.parseObject((String) jsonResult, FileCheckResult.class);
        if (Objects.isNull(validateResult)) {
            log.info("用户短链上传加载失败");
            return "fail";
        }
        List<UserShortUrlFileInfo> tempList = filterAndUpdateMobileInfo(validateResult);

        UserShortUrlRequest userShortUrlRequest = new UserShortUrlRequest();
        if (CollectionUtils.isNotEmpty(tempList)) {
            List<UserShortUrlDataDTO> dataList = new ArrayList<>(tempList.size());
            userShortUrlRequest.setTplCode(tempList.get(0).getTplCode());
            tempList.forEach(userShortUrlFileInfo -> {
                UserShortUrlDataDTO userShortUrlDTO = new UserShortUrlDataDTO();
                userShortUrlDTO.setMobile(userShortUrlFileInfo.getMobile());
                userShortUrlDTO.setCid(userShortUrlFileInfo.getCid());
                dataList.add(userShortUrlDTO);
            });
            userShortUrlRequest.setDataList(dataList);
        }
        List<UserShortUrlResponse> responseList = null;
        try {
            responseList = obtainShortCodeService.handler(userShortUrlRequest);
        } catch (Exception e) {
            log.info("obtain short code error");
        }
        if (CollectionUtils.isEmpty(responseList)) {
            return "fail";
        }
        return "success";
    }


    @Override
    public String downLoad(UserShortUrlQuery query) {
        List<UserShortUrlVO> userShortUrlVOList = userShortUrlMapper.selectByQuery(query)
                .stream().map(this::downLoadUserShortUrlVO)
                .collect(Collectors.toList());

        Comparator<UserShortUrlVO> cidComparator = Comparator.comparing(
                userShortUrlVO -> Objects.isNull(userShortUrlVO.getCid()) ? 1 : 0,
                Comparator.nullsLast(Integer::compareTo)
        );
        List<UserShortUrlVO> resultList = userShortUrlVOList.stream()
                .sorted(cidComparator)
                .collect(Collectors.toList());

        log.info("userShortUrlVOList:{}", JsonLogUtil.toJSONString(userShortUrlVOList));
        // 2. 上传文件到cos
        File tmpFile = null;
        String url = null;
        try {
            UploadFileUtils.createDir();
            String fileName = UploadFileUtils.TMP_SAVE_DIR + SLASH_BAR_STR + "user-short-url" + System.currentTimeMillis() + ".xlsx";
            EasyExcelFactory.write(fileName, UserShortUrlVO.class)
                    .sheet("用户短链")
                    .doWrite(resultList);

            tmpFile = readFile(fileName);
            fileName = UploadFileUtils.getOriginalFilename(tmpFile);
            log.info(tmpFile.getAbsolutePath());
            String cosFileName;
            try (InputStream uploadStream = Files.newInputStream(tmpFile.toPath())) {
                String md5 = SecureUtil.md5(UUID.randomUUID() + tmpFile.getName());
                // cos文件名称 md前2位/md5值
                cosFileName = UploadFileUtils.genCosObjectName(md5 + "." + UploadFileUtils.getFileSuffix(fileName));
                url = s3Helper.upload(cosFileName, uploadStream);
            }
            url = s3Helper.preAdminUrl(cosFileName);
        } catch (Exception e) {
            log.warn("文件加载错误" + e);
        } finally {
            if (Objects.nonNull(tmpFile)) {
                tmpFile.deleteOnExit();
            }
        }

        if (Objects.isNull(url)) {
            throw new BizException("上传文件失败");
        }

        // 3. 返回cos文件路径
        return url;
    }

    @Override
    public String queryMobile(Long id) {
        if (Objects.nonNull(id)) {
            UserShortUrlDO model = userShortUrlMapper.selectByPrimaryKey(id);
            if (Objects.nonNull(model)) {
                return model.getMobile();
            }
        }
        return null;
    }

    public File readFile(String pathName) {
        return new File(pathName);
    }


    private UserShortUrlVO downLoadUserShortUrlVO(UserShortUrlDO userShortUrlDO) {
        UserShortUrlVO userShortUrlVO = new UserShortUrlVO();
        BeanUtils.copyProperties(userShortUrlDO, userShortUrlVO);
        userShortUrlVO.setMobile(Md5Utils.md5AsBase64(userShortUrlDO.getMobile().getBytes()));
        return userShortUrlVO;
    }


    private UserShortUrlVO buildUserShortUrlVO(UserShortUrlDO model) {
        UserShortUrlVO userShortUrlVO = new UserShortUrlVO();
        BeanUtils.copyProperties(model, userShortUrlVO);
        // 计算有效天数
        Date currentDate = new Date();
        Date expiredDate = DateUtil.smallToDate(model.getExpiredDate());
        if (expiredDate == null) {
            throw new BizException("ShortUrlTplDO的expiredDate不可为null");
        }
        int expiredTag = 0;
        long between = DateUtil.between(currentDate, expiredDate, DateUnit.DAY);
        if (between <= 0) {
            expiredTag = 1;
            between = 0;
        }
        userShortUrlVO.setExpiredTag(expiredTag);
        userShortUrlVO.setValidPeriod(between);
        userShortUrlVO.setMobile(CommonUtil.maskMobile(model.getMobile()));
        return userShortUrlVO;
    }

    private FileCheckResult parse(String fileName) {

        // 从s3下载文件
        log.info("s3下载到本地 保存到临时文件 |fileName:{}", fileName);
        File file = s3Helper.download(fileName);
        // 解析文件
        log.info("解析文件 |fileName:{}", fileName);
        String nowFileName = file.getName();

        FileCheckResult fileCheckResult;
        InputStream inputStream;
        try {
            inputStream = new FileInputStream(file);
            UserShortUrlParseContext context = new UserShortUrlParseContext(nowFileName, inputStream, "");
            // excel解析文件
            log.info("excel解析文件 文件名|fileName:{}", nowFileName);
            fileCheckResult = this.preParse(context);
        } catch (FileNotFoundException e) {
            log.warn("没有找到文件", e);
            throw new BizException("保存文件失败 (在文件解析中)");
        } finally {
            file.deleteOnExit();
        }

        return fileCheckResult;
    }

    private FileCheckResult preParse(UserShortUrlParseContext context) {
        FileCheckResult fileCheckResult = new FileCheckResult();
        try {
            fileCheckResult = parseHandler.parse(context, fileCheckResult);
        } catch (IOException e) {
            throw new BizException(e);
        }

        // 由于未做检验 直接初始化 total = 有效list size ｜ 无效list size 0
        int size = fileCheckResult.getValidList().size();
        fileCheckResult.setTotal(size);
        fileCheckResult.setValidCount(size);
        fileCheckResult.setInvalidCount(0);
        return fileCheckResult;
    }

    /**
     * 根据文件检查结果过滤并更新用户短链接文件信息
     *
     * @param validateResult 文件检查结果对象，包含用户短链接文件信息的有效列表
     * @return 更新后的用户短链接文件信息列表
     *
     * 方法说明：
     * 该方法接收一个文件检查结果对象作为参数，遍历其中的有效用户短链接文件信息列表。
     * 对于每个用户短链接文件信息对象：
     * 1. 如果其cid和mobile都为空，则跳过当前对象，继续处理下一个。
     * 2. 如果mobile不为空，则尝试通过mobile查找对应的Customer信息。
     *    - 使用cifCustomerBaseMapper通过mobile查询Customer信息列表，并过滤出mobile完全匹配的对象。
     *    - 如果找到了对应的Customer，则将该Customer的cid设置到当前用户短链接文件信息对象中。
     *    - 将处理后的用户短链接文件信息对象添加到临时列表中。
     * 3. 如果cid不为空，则尝试通过cid查找对应的Customer信息。
     *    - 使用cifCustomerBaseMapper通过cid查询Customer信息。
     *    - 如果找到了对应的Customer，则将该Customer的mobile设置到当前用户短链接文件信息对象中。
     *    - 将处理后的用户短链接文件信息对象添加到临时列表中。
     * 最后，返回更新后的用户短链接文件信息列表。
     */
    private List<UserShortUrlFileInfo> filterAndUpdateMobileInfo(FileCheckResult validateResult) {
        List<UserShortUrlFileInfo> validList = validateResult.getValidList();
        List<UserShortUrlFileInfo> tempList = new ArrayList<>();

        for (UserShortUrlFileInfo userShortUrlFileInfo : validList) {
            // cid 和 mobile 都不为空时，才进行后续操作
            if (ObjectUtils.isEmpty(userShortUrlFileInfo.getCid()) && ObjectUtils.isEmpty(userShortUrlFileInfo.getMobile())) {
                continue;
            }

            // 如果mobile不为空，则尝试通过mobile查找对应的Customer信息
            if (!ObjectUtils.isEmpty(userShortUrlFileInfo.getMobile())) {
                // 使用cifCustomerBaseMapper通过mobile查询Customer信息
                // 并过滤出mobile完全匹配的对象
                CifCustomerBaseDO customer = cifCustomerBaseMapper.selectByMobileList(Collections.singletonList(userShortUrlFileInfo.getMobile()))
                        .stream().filter(c -> Objects.equals(c.getMobilePhone(), userShortUrlFileInfo.getMobile())).findFirst().orElse(null);
                // 如果找到了对应的Customer，则设置其cid到userShortUrlFileInfo中
                if (Objects.nonNull(customer)) {
                    userShortUrlFileInfo.setCid(customer.getId());
                }
                // 将处理后的userShortUrlFileInfo添加到tempList中
                tempList.add(userShortUrlFileInfo);
                continue;
            }

            // 如果cid不为空，则尝试通过cid查找对应的Customer信息
            if (!ObjectUtils.isEmpty(userShortUrlFileInfo.getCid())) {
                // 使用cifCustomerBaseMapper通过cid查询Customer信息
                CifCustomerBaseDO customer = cifCustomerBaseMapper.selectByPrimaryKey(userShortUrlFileInfo.getCid());
                // 如果找到了对应的Customer，则设置其mobile到userShortUrlFileInfo中
                if (Objects.nonNull(customer)) {
                    userShortUrlFileInfo.setMobile(customer.getMobilePhone());
                    // 将处理后的userShortUrlFileInfo添加到tempList中
                    tempList.add(userShortUrlFileInfo);
                }
            }
        }
        return tempList;
    }
}
