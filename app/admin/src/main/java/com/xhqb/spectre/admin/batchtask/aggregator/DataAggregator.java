package com.xhqb.spectre.admin.batchtask.aggregator;

import com.xhqb.spectre.admin.batchtask.validate.ValidateContext;
import com.xhqb.spectre.admin.batchtask.validate.ValidateResult;

/**
 * 数据聚合器 主要用于生成有效和无效数据集合
 * <p>
 * 聚合多个数据,目前接口只设计用于批量任务数据聚合
 * cif 或者未来微信openid等
 *
 * <AUTHOR>
 * @date 2021/9/28
 */
public interface DataAggregator {
    /**
     * 聚合操作
     *
     * @param validateContext
     * @param validateResult
     */
    void aggregate(ValidateContext validateContext, ValidateResult validateResult);
}
