package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class StatisticsData {
    private long t1SendCount = 0L;
    private long t1ReachCount = 0L;
    private long t2SendCount = 0L;
    private long t2ReachCount = 0L;
    private long t3SendCount = 0L;
    private long t3ReachCount = 0L;
    private Map<String, Double> ispRateMap = new HashMap<>();

    public void addT1Data(long sendCount, long reachCount) {
        this.t1SendCount += sendCount;
        this.t1ReachCount += reachCount;
    }

    public void addT2Data(long sendCount, long reachCount) {
        this.t2SendCount += sendCount;
        this.t2ReachCount += reachCount;
    }

    public void addT3Data(long sendCount, long reachCount) {
        this.t3SendCount += sendCount;
        this.t3ReachCount += reachCount;
    }
}
