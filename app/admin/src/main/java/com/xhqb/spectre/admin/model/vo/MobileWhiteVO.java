package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.MobileWhiteDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 17:20
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MobileWhiteVO implements Serializable {

    private static final long serialVersionUID = -8190040931697061485L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 限流规则类型
     */
    private String cfgType;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新人
     */
    private String updater;

    public static MobileWhiteVO buildMobileWhiteVO(MobileWhiteDO item, boolean isMobileMask) {
        String mobile = isMobileMask ? CommonUtil.maskMobile(item.getMobile()) : item.getMobile();
        return MobileWhiteVO.builder()
                .id(item.getId())
                .appCode(item.getAppCode())
                .cfgType(item.getCfgType())
                .mobile(mobile)
                .description(item.getDescription())
                .createTime(DateUtil.dateToString(item.getCreateTime()))
                .creator(item.getCreator())
                .updateTime(DateUtil.dateToString(item.getUpdateTime()))
                .updater(item.getUpdater())
                .build();
    }
}
