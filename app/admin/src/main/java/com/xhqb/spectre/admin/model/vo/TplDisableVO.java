package com.xhqb.spectre.admin.model.vo;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.TplDisableDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/21 11:23
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplDisableVO implements Serializable {

    private static final long serialVersionUID = -6927743949231285076L;

    private List<String> ispList;

    private List<AreaVO> areaList;

    private String startTime;

    private String endTime;

    public static TplDisableVO buildTplDisableVO(TplDisableDO item) {
        return TplDisableVO.builder()
                .ispList(CommonUtil.ispToList(item.getIsps()))
                .areaList(CommonUtil.areaToList(item.getAreas()))
                .startTime(DateUtil.intToString(item.getStartTime()))
                .endTime(DateUtil.intToString(item.getEndTime()))
                .build();
    }
}
