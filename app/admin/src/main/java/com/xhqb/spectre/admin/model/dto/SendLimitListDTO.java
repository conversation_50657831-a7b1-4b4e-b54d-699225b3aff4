package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 10:30
 * @Description:
 */
@Data
public class SendLimitListDTO implements Serializable {

    private static final long serialVersionUID = -325721528862475905L;

    /**
     * 同一手机号30秒内最大接收短信条数
     */
    @NotNull(message = "同一手机号30秒内最大接收短信条数不能为空")
    @Min(value = 0, message = "同一手机号30秒内最大接收短信条数不能小于0")
    private Integer mobileMaxCountHalfMinute;

    /**
     * 同一手机号1小时内最大接收短信条数
     */
    @NotNull(message = "同一手机号1小时内最大接收短信条数不能为空")
    @Min(value = 0, message = "同一手机号1小时内最大接收短信条数不能小于0")
    private Integer mobileMaxCountHour;

    /**
     * 同一手机号1天内最大接收短信条数
     */
    @NotNull(message = "同一手机号1天内最大接收短信条数不能为空")
    @Min(value = 0, message = "同一手机号1天内最大接收短信条数不能小于0")
    private Integer mobileMaxCountDay;

    /**
     * 同一手机号1天内最大接收相同短信条数
     */
    @NotNull(message = "同一手机号1天内最大接收相同短信条数不能为空")
    @Min(value = 0, message = "同一手机号1天内最大接收相同短信条数不能小于0")
    private Integer mobileSameContentMaxCycle;

    /**
     * 同一手机号1天内最大接收验证码短信条数
     */
    @NotNull(message = "同一手机号1天内最大接收验证码短信条数不能为空")
    @Min(value = 0, message = "同一手机号1天内最大接收验证码短信条数不能小于0")
    private Integer mobileMaxCountVerify;
}
