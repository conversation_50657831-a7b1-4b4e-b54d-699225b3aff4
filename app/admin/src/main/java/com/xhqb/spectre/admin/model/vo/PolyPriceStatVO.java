package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.bidata.entity.TypePriceStatDO;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PolyPriceStatVO {
    /**
     * 统计日期
     * 月份
     */
    private List<String> statDate;
    /**
     * 渠道编码
     */
    private String channelCode;

    private String channelCodeName;

    private List<BigDecimal> priceCount;

    private List<Integer> reachBillCount;

    private List<String> reachRate;

    public void add(TypePriceStatDO typePriceStatDO) {
        Integer li = typePriceStatDO.getPriceCount();
        BigDecimal yuan = li == null
                ? BigDecimal.ZERO
                : new BigDecimal(li).divide(new BigDecimal(1000), 3, RoundingMode.HALF_UP);

        this.statDate.add(typePriceStatDO.getStatDate());
        this.priceCount.add(yuan);
        this.reachBillCount.add(typePriceStatDO.getReachBillCount());
        this.reachRate.add(CommonUtil.double2String(typePriceStatDO.getReachRate()));
    }



}
