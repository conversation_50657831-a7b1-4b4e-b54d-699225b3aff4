package com.xhqb.spectre.admin.controller;

import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.model.dto.ChannelMobileBatchEditStatusDTO;
import com.xhqb.spectre.admin.model.dto.ChannelMobileSyncDTO;
import com.xhqb.spectre.admin.model.dto.ChannelTestDeleteDTO;
import com.xhqb.spectre.admin.model.dto.ChannelTestMobileDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.ChannelTestMobileVO;
import com.xhqb.spectre.admin.service.ChannelTestMobileService;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ChannelTestMobileQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/*
 * @Author: yangjiqiu
 * @Date: 2024/05/21 09:44
 * @Description:渠道测试库
 */
@RestController
@RequestMapping("/channelTestMobile")
@Slf4j
public class ChannelTestMobileController {

    @Resource
    private ChannelTestMobileService channelTestMobileService;

    /**
     * 测试库分页列表
     *
     * @param channelTestMobileQuery 查询参数
     * @param pageNum                当前页
     * @param pageSize               页面大小
     * @return 分页列表
     */
    @GetMapping("")
    public CommonResult<CommonPager<ChannelTestMobileVO>> listByPage(@ModelAttribute ChannelTestMobileQuery channelTestMobileQuery, Integer pageNum, Integer pageSize) {
        channelTestMobileQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        log.info("渠道测试库分页查询参数 channelTestMobileQuery:{}", JsonLogUtil.toJSONString(channelTestMobileQuery));
        return CommonResult.success(channelTestMobileService.listByPage(channelTestMobileQuery));
    }

    /**
     * 新增渠道测试库
     *
     * @param channelTestMobileDTO 新增参数
     * @return 新增大小
     */
    @PostMapping("/add")
    public CommonResult<Integer> add(@RequestBody ChannelTestMobileDTO channelTestMobileDTO) {
        log.info("新增渠道测试库 channelTestMobileDTO:{}", JsonLogUtil.toJSONString(channelTestMobileDTO));
        //参数格式校验
        ValidatorUtil.validate(channelTestMobileDTO);
        return CommonResult.success(channelTestMobileService.add(channelTestMobileDTO));
    }


    /**
     * 删除渠道测试库
     *
     * @param channelTestDeleteDTO 删除ids
     * @return 删除数量
     */
    @PostMapping("/delete")
    public CommonResult<Integer> delete(@RequestBody ChannelTestDeleteDTO channelTestDeleteDTO) {
        log.info("删除渠道测试库 channelTestMobileDTO:{}", JsonLogUtil.toJSONString(channelTestDeleteDTO));
        return CommonResult.success(channelTestMobileService.delete(channelTestDeleteDTO));
    }

    @PostMapping("/sync")
    public CommonResult<Integer> sync(@RequestBody ChannelMobileSyncDTO channelMobileSyncDTO) {
        log.info("同步渠道测试库 channelMobileSyncDTO:{}", JsonLogUtil.toJSONString(channelMobileSyncDTO));
        ValidatorUtil.validate(channelMobileSyncDTO);
        return CommonResult.success(channelTestMobileService.sync(channelMobileSyncDTO));
    }

    @PostMapping("/batchEditStatus")
    public CommonResult<String> batchEditStatus(@RequestBody ChannelMobileBatchEditStatusDTO batchEditStatusDTO){
        log.info("批量修改手机号状态 batchEditStatusDTO:{}", JsonLogUtil.toJSONString(batchEditStatusDTO));
        return CommonResult.success(channelTestMobileService.batchEditStatus(batchEditStatusDTO));
    }

}
