package com.xhqb.spectre.admin.statistics.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SmsStatisByHourKey implements Serializable {

    private static final long serialVersionUID = -3289365432081371205L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byhour.date
     *
     * @mbggenerated
     */
    private Date date;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byhour.class1
     *
     * @mbggenerated
     */
    private Integer class1;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byhour.class2
     *
     * @mbggenerated
     */
    private String class2;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byhour.class3
     *
     * @mbggenerated
     */
    private String class3;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byhour.class4
     *
     * @mbggenerated
     */
    private String class4;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byhour.class5
     *
     * @mbggenerated
     */
    private String class5;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byhour.class6
     *
     * @mbggenerated
     */
    private String class6;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byhour.class7
     *
     * @mbggenerated
     */
    private String class7;

}