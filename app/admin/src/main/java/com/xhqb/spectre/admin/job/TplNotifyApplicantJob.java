package com.xhqb.spectre.admin.job;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.service.FeiShuAlert;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.entity.oa.TplContent;
import com.xhqb.spectre.common.dal.mapper.SignMapper;
import com.xhqb.spectre.common.dal.mapper.TplContentMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 模板创建通过告知申请人
 * elastic-job.jobs.tplNotifyApplicantJob.cron=0 0/5 * * * ?
 * elastic-job.jobs.tplNotifyApplicantJob.sharding-total-count=1
 * elastic-job.jobs.tplNotifyApplicantJob.sharding-item-parameters=0=A
 * elastic-job.jobs.tplNotifyApplicantJob.disabled=true
 * elastic-job.jobs.tplNotifyApplicantJob.description=模板创建通过告知申请人
 *
 * <AUTHOR>
 */

@Component
@Job("tplNotifyApplicantJob")
@Slf4j
public class TplNotifyApplicantJob implements SimpleJob {

    @Resource
    private TplMapper tplMapper;
    @Resource
    private TplContentMapper tplContentMapper;
    @Resource
    private SignMapper signMapper;
    @Resource
    private VenusConfig venusConfig;
    @Resource
    private FeiShuAlert feiShuAlert;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("TplNotifyApplicantJob 执行");
        // 来源: 报备同步 sync 且 开启 且 未发送通知
        List<TplDO> modelList = tplMapper.selectEffectiveNotify("sync", 0, 1);

        if (CollectionUtil.isEmpty(modelList)) {
            return;
        }

        List<String> reportId = modelList.stream().map(TplDO::getReportId).collect(Collectors.toList());


        Map<String, List<TplContent>> creatorMap = tplContentMapper.selectByContentIdList(reportId).stream()
                .collect(Collectors.groupingBy(TplContent::getCreator));
        if (CollectionUtil.isEmpty(creatorMap)) {
            return;
        }

        List<SignDO> signDOList = signMapper.selectEnum(1);
        Map<String, String> signMap = signDOList.stream().collect(Collectors.toMap(s -> s.getId() + "", SignDO::getName));


        for (Map.Entry<String, List<TplContent>> entry : creatorMap.entrySet()) {
            // 申请人
            String applicant = entry.getKey();
            // 发送信息：报备 id、报备文案、短信编码
            List<JSONObject> messageList = new ArrayList<>();
            List<TplContent> contentList = entry.getValue();
            if (CollectionUtil.isEmpty(contentList)) {
                log.info("申请人[{}]没有报备信息", applicant);
                continue;
            }
            List<String> keyRepordIdList = contentList.stream()
                    .map(TplContent::getContentId).collect(Collectors.toList());
            for (TplContent tplContent : contentList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("reportId", tplContent.getContentId());
                jsonObject.put("content", signMap.getOrDefault(tplContent.getSignId(), "") + tplContent.getOriginalContent());
                jsonObject.put("code", tplContent.getTplCode());
                messageList.add(jsonObject);
            }

            // todo:发送飞书通知
            log.info("发送飞书通知给申请人[{}], 内容size[{}]", applicant, messageList.size());
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("reports", messageList);
            dataMap.put("atUserList", CollectionUtil.newArrayList(applicant));
            try {
                feiShuAlert.sendFeiShuAlert(dataMap, venusConfig.getTplNotifyApplicantStrategyId());
                tplMapper.updateNotifySendStatusByReportIds(keyRepordIdList,1);
            } catch (Exception e) {
                log.error("发送飞书通知给申请人[{}]失败", applicant, e);
            }

        }
    }


}
