package com.xhqb.spectre.admin.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.batchtask.io.UploadFileUtils;
import com.xhqb.spectre.admin.batchtask.upload.cos.S3Helper;
import com.xhqb.spectre.admin.bidata.entity.ReachRateDO;
import com.xhqb.spectre.admin.bidata.mapper.BidataPlatformSendStatMapper;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.service.FeiShuAlert;
import com.xhqb.spectre.admin.service.impl.TplServiceImpl;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.mapper.SignMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.NoSuchFileException;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 触达率过低通知
 */
@Component
@Job("reachRateLowNotifyJob")
@Slf4j
public class ReachRateLowNotifyJob implements SimpleJob {

    @Resource
    private VenusConfig venusConfig;

    @Resource
    private BidataPlatformSendStatMapper bidataPlatformSendStatMapper;

    @Resource
    private TplMapper tplMapper;

    @Resource
    private TplServiceImpl tplService;

    @Resource
    private FeiShuAlert feiShuAlert;
    @Resource
    private SignMapper signMapper;
    @Resource
    private S3Helper s3Helper;

    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            lowNotify();
        } catch (Exception e) {
            log.error("触达率过低通知异常", e);
        }
    }

    private void lowNotify() {
        // （类型=营销、通知）
        List<String> smsTypeList = Arrays.stream(venusConfig.getReachRateLowNotifySmsType().split(",")).collect(Collectors.toList());
        // 时间 D-1
        Date yesterdayStart = DateUtil.beginOfDay(DateUtil.yesterday());
        String formatYesterdayStart = DateUtil.format(yesterdayStart, "yyyy-MM-dd");
        List<ReachRateDO> reachRateDOList = bidataPlatformSendStatMapper.selectReachRateByTimeAndType(formatYesterdayStart, smsTypeList);

        log.info("触达率过低通知：reachRateDOList={} | reachRateThreshold:{} | tplReachCountLimit:{}", JsonLogUtil.toJSONString(reachRateDOList), venusConfig.getReachRateThreshold(), venusConfig.getTplReachCountLimit());

        reachRateDOList = reachRateDOList.stream()
                .filter(reachRateDO -> reachRateDO.getReachRate() < venusConfig.getReachRateThreshold())
                .filter(reachRateDO -> reachRateDO.getSendCount() >= venusConfig.getTplReachCountLimit())
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(reachRateDOList)) {
            log.info("触达率过低通知：没有触达率过低的模板");
            return;
        }
        log.info("触达率过低通知：size：{}", reachRateDOList.size());

        Map<Integer, String> signMap = signMapper.selectAll().stream().collect(Collectors.toMap(SignDO::getId, SignDO::getName));

        Map<String, ReachRateDO> tplDataMap = reachRateDOList.stream()
                .collect(Collectors.toMap(ReachRateDO::getTplCode, v -> v));
        List<String> tplCodeList = new ArrayList<>(tplDataMap.keySet());
        List<TplDO> tplDOList = tplMapper.selectByCodeList(tplCodeList);

        List<JSONObject> dataList = new ArrayList<>();
        for (TplDO tplDO : tplDOList) {
            tplService.setCreatorByReportId(tplDO);
            ReachRateDO reachRateDO = tplDataMap.get(tplDO.getCode());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", tplDO.getCode());
            jsonObject.put("content", CommonUtil.escapeJsonAndRemoveNewlines(signMap.getOrDefault(tplDO.getSignId(), "") + tplDO.getContent()));
            jsonObject.put("content2", signMap.getOrDefault(tplDO.getSignId(), "") + tplDO.getContent());
            jsonObject.put("creator", tplDO.getCreator());
            jsonObject.put("sendCount", reachRateDO.getSendCount());
            jsonObject.put("yesReachRate", reachRateDO.getReachRate() * 100);
            dataList.add(jsonObject);
        }

        List<String> atUserList = new ArrayList<>();
        atUserList.add(venusConfig.getTplNotifyUser());
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", dataList);
        dataMap.put("atUserList", atUserList);

        String filePath = String.format("/tmp/upload/%s", System.currentTimeMillis() + ".xlsx");
        dataMap.put("url", exportTplReportToExcelAndGetUrl(dataList, filePath));

        feiShuAlert.sendFeiShuAlert(dataMap, venusConfig.getTplReachRateLowNotifyStrategyId());
    }

    private String getUrl(String filePath) {
        String url = "";
        try {
            File tmpFile = UploadFileUtils.readFile(filePath);
            String fileName = UploadFileUtils.getOriginalFilename(tmpFile);
            InputStream uploadStream = Files.newInputStream(tmpFile.toPath());
            String md5 = SecureUtil.md5(UUID.randomUUID() + tmpFile.getName());
            String cosFileName = UploadFileUtils.genCosObjectName(md5 + "." + UploadFileUtils.getFileSuffix(fileName));
            s3Helper.upload(cosFileName, uploadStream);
            url = s3Helper.preAdminUrlThreeDay(cosFileName);
        } catch (Exception e) {
            log.warn("文件加载错误", e);
        }
        return url;
    }

    public String exportTplReportToExcelAndGetUrl(List<JSONObject> dataList, String filePath) {
        if (CollectionUtil.isEmpty(dataList)) {
            log.warn("导出数据为空，跳过Excel生成");
            return "";
        }

        // 定义表头映射（中英文对照）
        Map<String, String> headerAlias = new LinkedHashMap<String, String>() {{
            put("code", "模板编码");
            put("content2", "报备文案");
            put("creator", "创建人");
            put("sendCount", "昨日发送量");
            put("yesReachRate", "昨日触达率(%)");
        }};
        try (ExcelWriter writer = ExcelUtil.getWriter(filePath)) {
            // 1. 基础配置
            writer.setOnlyAlias(true);
            // 2. 设置表头
            writer.writeHeadRow(Arrays.asList("模板编码", "报备文案", "创建人","昨日发送量","昨日触达率(%)"));

            // 3. 批量写入数据（优化性能）
            List<List<String>> rows = dataList.stream()
                    .map(obj -> headerAlias.keySet().stream()
                            .map(obj::getString)
                            .collect(Collectors.toList()))
                    .collect(Collectors.toList());
            writer.write(rows);

            // 5. 设置列宽和格式
            writer.autoSizeColumnAll();
            log.info("Excel文件生成成功: {}", filePath);

        } catch (Exception e) {
            log.warn("导出Excel文件失败 | 路径: {} | 错误: ", filePath, e);
        }
        return getUrl(filePath);
    }
}
