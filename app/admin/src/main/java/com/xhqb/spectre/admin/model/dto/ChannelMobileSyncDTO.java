package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ChannelMobileSyncDTO {
    /**
     * 开始时间
     */
    @NotNull(message = "startTime不能为空")
    private String startTime;
    /**
     * 结束时间
     */
    @NotNull(message = "endTime不能为空")
    private String endTime;

    /**
     * 发送开始时间
     */

    private long startTimestamp;

    /**
     * 发送结束时间
     */
    private long endTimestamp;

    /**
     * 个数
     */
    private Integer num;

    /**
     * 空号检测标志
     */
    private Integer detectTag;

    /**
     * 失效时间
     */
    @NotNull(message = "expiredTime不能为空")
    private String expiredTime;

    private PageParameter pageParameter;
}
