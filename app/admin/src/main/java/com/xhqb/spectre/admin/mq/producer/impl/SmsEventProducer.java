package com.xhqb.spectre.admin.mq.producer.impl;

import com.xhqb.spectre.admin.mq.producer.AbstractMessageProducer;
import com.xhqb.spectre.common.message.SmsEventMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 短信事件生产者
 *
 */
@Component
public class SmsEventProducer extends AbstractMessageProducer<SmsEventMessage> {

    /**
     * spectre-sms-event
     */
    @Value("${tdmq.producers.smsEvent}")
    private String smsEventTopic;


    protected String getTopic() {
        return smsEventTopic;
    }
}
