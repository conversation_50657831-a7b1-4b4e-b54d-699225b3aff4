package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.admin.model.vo.batchtask.ParamItemVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 批量任务参数对象
 *
 * <AUTHOR>
 * @date 2021/9/22
 */
@Data
public class BatchTaskParamDTO implements Serializable {

    /**
     * 任务参数ID
     */
    private Integer taskParamId;
    /**
     * 文件的MD5值
     */
    private String fileMd5;
    /**
     * 短信json参数配置数组(由上传文件的头信息组成)
     */
    private List<ParamItemVO> paramList;
}
