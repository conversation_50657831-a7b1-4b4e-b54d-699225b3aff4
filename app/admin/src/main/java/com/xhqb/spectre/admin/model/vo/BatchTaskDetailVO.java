package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.model.vo.batchtask.CheckResultVO;
import com.xhqb.spectre.admin.model.vo.batchtask.QueryTaskSegmentVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量任务详情
 *
 * <AUTHOR>
 * @date 2021/9/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskDetailVO extends BatchTaskVO {

    /**
     * 文件检测结果列表
     */
    private List<CheckResultVO> checkResultList;
    /**
     * 短信群发任务的参数 返回查询分片对象
     * 如果是已经存在的数据信息 则返回taskParamId和fileMd5
     */
    private List<QueryTaskSegmentVO> taskParamList;
}
