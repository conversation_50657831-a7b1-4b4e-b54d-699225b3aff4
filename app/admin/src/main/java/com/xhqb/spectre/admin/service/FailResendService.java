package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.FailResendDTO;
import com.xhqb.spectre.admin.model.vo.FailResendDetailVO;
import com.xhqb.spectre.admin.model.vo.FailResendVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.FailResendQuery;

public interface FailResendService {
    CommonPager<FailResendVO> page(FailResendQuery failResendQuery);

    String add(FailResendDTO failResendDTO);

    String update(FailResendDTO failResendDTO);

    String delete(String strategyId);

    FailResendDetailVO detail(String strategyId);

    String enable(String strategyId);

    String disable(String strategyId);
}
