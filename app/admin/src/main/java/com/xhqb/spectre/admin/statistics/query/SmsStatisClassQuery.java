package com.xhqb.spectre.admin.statistics.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/1 16:43
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsStatisClassQuery implements Serializable {

    private static final long serialVersionUID = 1151037735106937585L;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 枚举类型。1：批次ID；2：短信类型编码；3：模板编码；4：渠道编码；5：省份；6：城市；7：运营商
     */
    private Integer type;
}
