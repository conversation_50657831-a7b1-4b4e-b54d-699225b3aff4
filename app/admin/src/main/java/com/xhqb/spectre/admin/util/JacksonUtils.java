package com.xhqb.spectre.admin.util;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Objects;

/**
 * Jackson 工具
 *
 * <AUTHOR>
 * @date 2021/10/2
 */
public class JacksonUtils {
    private final static ObjectMapper MAPPER = new ObjectMapper();

    /**
     * 日起格式化
     */
    private static final String STANDARD_FORMAT = "yyyy-MM-dd HH:mm:ss";

    static {
        //对象的所有字段全部列入
        MAPPER.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        //取消默认转换timestamps形式
        MAPPER.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        //忽略空Bean转json的错误
        MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        //所有的日期格式都统一为以下的样式，即yyyy-MM-dd HH:mm:ss
        MAPPER.setDateFormat(new SimpleDateFormat(STANDARD_FORMAT));
        //忽略 在json字符串中存在，但是在java对象中不存在对应属性的情况。防止错误
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 将对象转换成JSON字符串
     *
     * @param data
     * @param <T>
     * @return
     */
    public static <T> String toJSONString(T data) {
        if (Objects.isNull(data)) {
            return null;
        }
        try {
            return MAPPER.writeValueAsString(data);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 转换成对象
     *
     * @param str
     * @param typeReference
     * @param <T>
     * @return
     */
    public static <T> T parseObject(String str, TypeReference<T> typeReference) {
        if (StringUtils.isEmpty(str) || Objects.isNull(typeReference)) {
            return null;
        }

        try {
            return (T) (typeReference.getType().equals(String.class) ? str : MAPPER.readValue(str, typeReference));
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

}
