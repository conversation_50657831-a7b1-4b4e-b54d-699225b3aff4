package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.AppDailyStatsChartVO;
import com.xhqb.spectre.admin.model.vo.AppDailyStatsVO;
import com.xhqb.spectre.admin.service.AppDailyStatsService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.AppDailyStatsQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 应用每日统计
 */
@RestController
@RequestMapping("/appDailyStats")
@Slf4j
public class AppDailyStatsController {

    @Autowired
    private AppDailyStatsService appDailyStatsService;

    /**
     * 概览
     * @param appDailyStatsQuery
     * @return
     */
    @GetMapping("/gw/overview")
    public CommonResult<AppDailyStatsChartVO> overview(@ModelAttribute AppDailyStatsQuery appDailyStatsQuery) {
        appDailyStatsQuery.setAccountCategory(1);
        log.info("查询应用每日概览; queryAppList:{}", appDailyStatsQuery);
        return CommonResult.success(appDailyStatsService.overview(appDailyStatsQuery));
    }

    /**
     * 查询列表
     *
     * @param appDailyStatsQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/gw")
    public AdminResult queryAppList(@ModelAttribute AppDailyStatsQuery appDailyStatsQuery,
                                    Integer pageNum,
                                    Integer pageSize) {
        log.info("查询应用每日统计; queryAppList:{}", appDailyStatsQuery);
        appDailyStatsQuery.setAccountCategory(1);
        appDailyStatsQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<AppDailyStatsVO> commonPager = appDailyStatsService.listByPage(appDailyStatsQuery);
        return AdminResult.success(commonPager);
    }
}
