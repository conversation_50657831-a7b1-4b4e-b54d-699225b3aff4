package com.xhqb.spectre.admin.batchtask;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xhqb.spectre.admin.batchtask.send.CompositeMessageResult;
import com.xhqb.spectre.admin.batchtask.send.MessageSendResult;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryContext;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryResult;
import com.xhqb.spectre.admin.batchtask.send.record.MessageSendFailedRecord;
import com.xhqb.spectre.admin.batchtask.send.single.SingleMessageRequest;
import com.xhqb.spectre.admin.batchtask.send.single.SingleMessageResponse;
import com.xhqb.spectre.admin.batchtask.send.single.SingleMessageSender;
import com.xhqb.spectre.admin.batchtask.send.verify.VerifyCodeRequest;
import com.xhqb.spectre.admin.batchtask.send.verify.VerifyCodeResponse;
import com.xhqb.spectre.admin.batchtask.send.verify.VerifyCodeSender;
import com.xhqb.spectre.admin.batchtask.support.BatchMessageRequestBuilderSupport;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.AppDO;
import com.xhqb.spectre.common.dal.mapper.AppMapper;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;

/**
 * 消息发送
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Configuration
@Slf4j
public class MessageSendFactory {

    /**
     * http 调用线程池
     */
    private static final ExecutorService HTTP_INVOKER_POOL = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 2,
            100,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("http-invoker-pool-%d").build()
    );


    @Resource
    private SingleMessageSender singleMessageSender;
    @Resource
    private AppMapper appMapper;
    @Autowired
    private VenusConfig venusConfig;
    @Resource
    private VerifyCodeSender verifyCodeSender;

    @Resource
    private BatchMessageRequestBuilderSupport batchMessageRequestBuilderSupport;

    /**
     * 单个消息发送请求
     *
     * @param context
     * @return 返回推送接口响应结果
     */
    public List<SingleFactoryResult> singleSendMessage(SingleFactoryContext context) {
        List<SingleFactoryResult> factoryResultList = Lists.newArrayList();
        // 构建单个发送消息的请求体
        // 验证码只能单个单个手机号发送
        List<SingleMessageRequest> requestList;
        String smsCodeType = context.getSmsCodeType();

        if (StringUtils.equals(smsCodeType, MessageTypeEnum.VERIFY.getMessageType())) {
            // 验证码 使用验证码发送接口
            requestList = this.buildSingleMessageRequest(context);
        } else {
            // 其他方式采用批量发送
            requestList = this.buildBatchMessageRequest(context);
        }

        if (requestList.isEmpty()) {
            // 没有数据 不进行数据请求操作
            return factoryResultList;
        }

        int requestSize = requestList.size();
        if (requestSize == 1) {
            // 单条数据 不需要开启异步方式
            SingleFactoryResult factoryResult = this.doHttpInvoke(requestList.get(0));
            factoryResultList.add(factoryResult);
            return factoryResultList;
        }

        // 多条数据 需要进行异步发送
        CountDownLatch latch = new CountDownLatch(requestSize);
        for (SingleMessageRequest request : requestList) {
            HTTP_INVOKER_POOL.execute(new HttpInvokerTask(latch, factoryResultList, request));
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        return factoryResultList;
    }

    /**
     * 构建单个发送消息列表
     *
     * @param context
     * @return
     */
    private List<SingleMessageRequest> buildSingleMessageRequest(SingleFactoryContext context) {
        List<SingleMessageRequest> requestList = Lists.newArrayList();
        List<String> mobileList = context.getMobileList();
        List<List<String>> paramMapList = context.getParamMapList();
        if (Objects.isNull(paramMapList)) {
            paramMapList = Lists.newArrayList();
            context.setParamMapList(paramMapList);
        }
        int mobileSize = mobileList.size();
        // 是否存在手机号与参数一一对应的情况
        boolean isOneToOne = mobileSize == paramMapList.size();
        String appSecret = this.getAppSecret(context.getAppCode());
        SingleMessageRequest request;
        for (int i = 0; i < mobileSize; i++) {
            request = new SingleMessageRequest();
            //业务应用编码
            request.setAppCode(context.getAppCode());
            // 加签秘钥
            request.setAppSecret(appSecret);
            // tplCode 模板编号
            request.setTplCode(context.getTplCode());
            // 签名名称
            request.setSignName(context.getSignName());
            // 手机号码
            request.setPhoneNumbers(mobileList.get(i));
            // 短信类型
            request.setSmsCodeType(context.getSmsCodeType());
            // 渠道关联账号的ID(用于测试短信时会传入该ID)
            request.setChannelAccountId(context.getChannelAccountId());
            // 设置批次号
            request.setBatchId(context.getBatchId());
            // 设置签名编码
            request.setSignCode(context.getSignCode());
            // 设置requestId 2021-12-14
            request.setRequestId(context.getRequestId());
            // 请求参数
            // 如果存在手机号与参数一一对应的关系 则从paramMapList相应的位置中获取参数
            // 否则只获取第一个参数
            if (!CommonUtil.isEmpty(paramMapList)) {
                // 放置手机与参数关系
                request.put(mobileList.get(i), paramMapList.get(isOneToOne ? i : 0));
            } else {
                // 放入空参数
                request.put(mobileList.get(i));
            }
            requestList.add(request);
        }
        return requestList;
    }

    /**
     * 构建批量参数请求
     *
     * @param context
     * @return
     */
    private List<SingleMessageRequest> buildBatchMessageRequest(SingleFactoryContext context) {

        List<Map<String, String>> placeHolderList = context.getPlaceHolderList();
        if (!CommonUtil.isEmpty(placeHolderList)) {
            // 使用占位符模板构建短信消息
            return batchMessageRequestBuilderSupport.placeholderBuilder(context, this);
        }

        List<String> mobileList = context.getMobileList();
        List<SingleMessageRequest> requestList = new ArrayList<>();
        List<List<String>> paramMapList = context.getParamMapList();
        if (Objects.isNull(paramMapList)) {
            paramMapList = Lists.newArrayList();
            context.setParamMapList(paramMapList);
        }
        int mobileSize = mobileList.size();
        // 是否存在手机号与参数一一对应的情况
        boolean isOneToOne = mobileSize == paramMapList.size();
        String appSecret = this.getAppSecret(context.getAppCode());

        boolean isNeedPartition = mobileList.size() > venusConfig.getApiMobileGroupCount();
        // 分组手机号码 批量提交
        List<List<String>> mobileGroupList;
        if (!isNeedPartition) {
            // 不需要进行分割
            mobileGroupList = Lists.newArrayList();
            mobileGroupList.add(mobileList);
        } else {
            // 进行数据分割
            mobileGroupList = Lists.partition(mobileList, venusConfig.getApiMobileGroupCount());
        }
        // 参数分组
        List<List<List<String>>> paramGroupList = null;
        // 所有参数一样的参数情况
        List<String> allSameParam = null;

        if (isOneToOne) {
            // 参数与手机号一一对应
            if (isNeedPartition) {
                paramGroupList = Lists.partition(paramMapList, venusConfig.getApiMobileGroupCount());
            } else {
                paramGroupList = Lists.newArrayList();
                paramGroupList.add(paramMapList);
            }
        } else {
            // 表示所有参数都一样,取第一个列表的参数作为所有请求的参数
            allSameParam = CommonUtil.isEmpty(paramMapList) ? null : Lists.newArrayList(String.join(",", paramMapList.get(0)));
        }

        SingleMessageRequest request;
        for (int i = 0; i < mobileGroupList.size(); i++) {
            List<String> mobileGroup = mobileGroupList.get(i);
            request = new SingleMessageRequest();
            //业务应用编码
            request.setAppCode(context.getAppCode());
            // 加签秘钥
            request.setAppSecret(appSecret);
            // tplCode 模板编号
            request.setTplCode(context.getTplCode());
            // 签名名称
            request.setSignName(context.getSignName());
            // 手机号码
            request.setPhoneNumbers(String.join(",", mobileGroup));
            // 短信类型
            request.setSmsCodeType(context.getSmsCodeType());
            // 渠道关联账号的ID(用于测试短信时会传入该ID)
            request.setChannelAccountId(context.getChannelAccountId());
            // 设置批次号
            request.setBatchId(context.getBatchId());
            // 设置签名编码
            request.setSignCode(context.getSignCode());
            // 设置requestId 2021-12-14
            request.setRequestId(context.getRequestId());
            // api
            request.setApi(context.getApi());
            if (!CommonUtil.isEmpty(allSameParam)) {
                // 不等于空表示所有请求参数都一样
                request.put(mobileGroup, allSameParam);
            } else {
                // 需要批量设置请求参数
                request.putGroup(mobileGroup, this.getParamList(paramGroupList, i));
            }
            requestList.add(request);
        }

        return requestList;
    }

    /**
     * 获取参数列表
     *
     * @param paramGroupList
     * @param groupIndex
     * @return
     */
    private List<List<String>> getParamList(List<List<List<String>>> paramGroupList, int groupIndex) {
        if (CommonUtil.isEmpty(paramGroupList)) {
            return null;
        }

        return paramGroupList.get(groupIndex);
    }


    /**
     * 具体发送http请求的任务
     *
     * @param request
     * @return
     */
    private SingleFactoryResult doHttpInvoke(SingleMessageRequest request) {
        SingleFactoryResult factoryResult = new SingleFactoryResult();
        // 默认为失败状态
        factoryResult.setSuccess(false);
        // factoryResult.setParamMapList(request.getParamMap());
        String smsCodeType = request.getSmsCodeType();
        if (StringUtils.equals(smsCodeType, MessageTypeEnum.VERIFY.getMessageType())) {
            // 验证码 使用验证码发送接口
            return doVerifySend(request, factoryResult);
        }

        if (!StringUtils.equals(smsCodeType, MessageTypeEnum.MARKET.getMessageType())) {
            // 不是营销类型 需要将短信类型设置为空
            request.setSmsCodeType(null);
        }

        // 使用单个消息发送接口
        return doSingleSend(request, factoryResult);
    }

    /**
     * 单个消息发送接口
     *
     * @param request
     * @param factoryResult
     * @return
     */
    private SingleFactoryResult doSingleSend(SingleMessageRequest request, SingleFactoryResult factoryResult) {
        try {
            long start = System.currentTimeMillis();
            int mobileCount = request.getPhoneNumbers().split(",").length;
            factoryResult.setSuccessCount(0);
            factoryResult.setFailCount(mobileCount);
            factoryResult.setTotalCount(mobileCount);
            MessageSendResult<CompositeMessageResult<SingleMessageResponse>> sendResult = singleMessageSender.send(request);
            if (venusConfig.isLogEnable()) {
                log.info("单个短信发送请求结果 = {},请求参数 = {},cost = {}", JSON.toJSONString(sendResult), request, (System.currentTimeMillis() - start));
            }
            if (!sendResult.isSuccess()) {
                // 处理失败
                log.info("单个短信发送http请求失败,api = {}, 请求参数 = {}", singleMessageSender.getSingleApi(request.getApi()), JSON.toJSONString(sendResult));
                // factoryResult.setMessage(sendResult.getMsg());
                factoryResult.setFailureSMSRecord(createFailedRecord(request, sendResult.getMsg()));
                return factoryResult;
            }

            // 响应成功则 需要判断发送结果
            CompositeMessageResult<SingleMessageResponse> messageResult = sendResult.getData();
            if (Objects.isNull(messageResult)) {
                // 数据格式为空 则认为发送失败
                // factoryResult.setMessage("推送接口响应data为空");
                factoryResult.setFailureSMSRecord(createFailedRecord(request, "推送接口响应data为空"));
                return factoryResult;
            }
            // 获取到sms结果
            SingleMessageResponse smsSendResult = messageResult.getSmsSendResult();
            if (Objects.isNull(smsSendResult)) {
                // smsSendResult 为空 也认为发送失败
                // factoryResult.setMessage("推送接口响应smsSendResult为空");
                factoryResult.setFailureSMSRecord(createFailedRecord(request, "推送接口响应smsSendResult为空"));
                return factoryResult;
            }

            Integer failure = smsSendResult.getFailure();
            Integer success = smsSendResult.getSuccess();

            if (Objects.nonNull(failure)) {
                factoryResult.setFailCount(failure);
            }


            if (Objects.nonNull(success)) {
                factoryResult.setSuccessCount(success);
            }

            List<MessageSendFailedRecord> failureSMSRecord = smsSendResult.getFailureSMSRecord();

            if (!CommonUtil.isEmpty(failureSMSRecord)) {
                // 包含失败  则认为失败
                factoryResult.setFailureSMSRecord(failureSMSRecord);
                return factoryResult;
            }

            // 短信发送成功
            factoryResult.setMessage("success");
            factoryResult.setSuccess(true);
        } catch (Exception e) {
            log.error("单个消息发送http请求失败,request = {}", JSON.toJSONString(request), e);
            factoryResult.setFailureSMSRecord(createFailedRecord(request, "单个消息发送http请求失败"));
        }
        return factoryResult;
    }


    /**
     * 验证码发送
     *
     * @param request
     * @param factoryResult 需要设置最终发送结果
     * @return
     */
    private SingleFactoryResult doVerifySend(SingleMessageRequest request, SingleFactoryResult factoryResult) {
        try {
            long start = System.currentTimeMillis();
            factoryResult.setTotalCount(1);
            factoryResult.setFailCount(1);
            MessageSendResult<VerifyCodeResponse> sendResult = verifyCodeSender.send(new VerifyCodeRequest(request));
            if (venusConfig.isLogEnable()) {
                log.info("验证码发送请求结果 = {},请求参数 = {},cost = {}", JSON.toJSONString(sendResult), request, (System.currentTimeMillis() - start));
            }
            if (!sendResult.isSuccess()) {
                // 处理失败
                log.info("验证码发送http请求失败,api = {}, 请求参数 = {}", singleMessageSender.getSingleApi(request.getApi()), JSON.toJSONString(sendResult));
                factoryResult.setFailureSMSRecord(createFailedRecord(request, "验证码发送http请求失败"));
                return factoryResult;
            }
            // 短信发送成功
            factoryResult.setMessage("success");
            factoryResult.setSuccess(true);
            factoryResult.setFailCount(0);
            factoryResult.setSuccessCount(1);
        } catch (Exception e) {
            log.error("验证码消息发送http请求失败,request = {}", JSON.toJSONString(request), e);
            factoryResult.setFailureSMSRecord(createFailedRecord(request, "验证码发送http请求失败"));
        }
        return factoryResult;
    }

    /**
     * 创建发送失败记录信息
     *
     * @param request
     * @param msg
     * @return
     */
    private List<MessageSendFailedRecord> createFailedRecord(SingleMessageRequest request, String msg) {
        String phoneNumbers = request.getPhoneNumbers();
        String[] phoneGroup = phoneNumbers.split(",");
        List<MessageSendFailedRecord> failedRecordList = new ArrayList<>(phoneGroup.length);
        MessageSendFailedRecord messageSendFailedRecord;
        for (int i = 0; i < phoneGroup.length; i++) {
            messageSendFailedRecord = new MessageSendFailedRecord();
            messageSendFailedRecord.setPhoneNumber(phoneGroup[i]);
            messageSendFailedRecord.setFileMsg(msg);
            failedRecordList.add(messageSendFailedRecord);
        }
        return failedRecordList;
    }

    /**
     * 获取到应用加签的秘钥
     *
     * @param appCode
     * @return
     */
    public String getAppSecret(String appCode) {
        AppDO appDO = appMapper.selectByCode(appCode);
        if (Objects.isNull(appDO)) {
            log.warn("根据appCode未查询到应用信息, appCode = {}", appCode);
            return null;
        }
        return appDO.getSkey();
    }

    /**
     * http 调用器
     */
    private class HttpInvokerTask implements Runnable {

        /**
         * 锁计数器
         */
        private CountDownLatch latch;
        /**
         * 结果接受列表
         */
        private List<SingleFactoryResult> factoryResultList;
        /**
         * 单个消息发送请求内容
         */
        private SingleMessageRequest singleMessageRequest;

        public HttpInvokerTask(CountDownLatch latch, List<SingleFactoryResult> factoryResultList, SingleMessageRequest singleMessageRequest) {
            this.latch = latch;
            this.factoryResultList = factoryResultList;
            this.singleMessageRequest = singleMessageRequest;
        }

        @Override
        public void run() {
            try {
                SingleFactoryResult factoryResult = doHttpInvoke(this.singleMessageRequest);
                synchronized (latch) {
                    factoryResultList.add(factoryResult);
                }
            } finally {
                latch.countDown();
            }
        }


    }


}
