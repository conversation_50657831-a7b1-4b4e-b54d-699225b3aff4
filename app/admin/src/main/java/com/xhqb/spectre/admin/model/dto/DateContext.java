package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import java.util.Date;

@Data
public class DateContext {

    private final Date t1Date;
    private final Date t2Date;
    private final Date t3Date;
    private final String t1DateStr;
    private final String t2DateStr;
    private final String t3DateStr;

    public DateContext(Date t1Date, Date t2Date, Date t3Date,
                       String t1DateStr, String t2DateStr, String t3DateStr) {
        this.t1Date = t1Date;
        this.t2Date = t2Date;
        this.t3Date = t3Date;
        this.t1DateStr = t1DateStr;
        this.t2DateStr = t2DateStr;
        this.t3DateStr = t3DateStr;
    }
}
