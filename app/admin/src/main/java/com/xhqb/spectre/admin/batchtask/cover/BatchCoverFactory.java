package com.xhqb.spectre.admin.batchtask.cover;

import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xhqb.spectre.admin.batchtask.aggregator.CustomerDataHandler;
import com.xhqb.spectre.admin.batchtask.aggregator.DataHandlerFactory;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.batchtask.CustomerVO;
import com.xhqb.spectre.admin.model.vo.batchtask.ParamItemVO;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.BatchTaskDO;
import com.xhqb.spectre.common.dal.entity.BatchTaskParamDO;
import com.xhqb.spectre.common.dal.entity.BatchTaskReportDO;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.mapper.BatchTaskMapper;
import com.xhqb.spectre.common.dal.mapper.BatchTaskParamMapper;
import com.xhqb.spectre.common.dal.mapper.BatchTaskReportMapper;
import com.xhqb.spectre.common.dal.mapper.SignMapper;
import com.xhqb.spectre.common.enums.DeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 群发任务兜底处理
 * <p>
 * 兜底实现逻辑
 * 1. 传入群发任务ID
 * 2. 根据群发任务ID查询t_batch_task_report里success=0的t_batch_task_param对应的切片数据
 * 3. 解析第2步获取的切片数据
 * 3.1 若手机号都正常，则重新生成一个批次
 * 3.2 若手机号不存在，并包含cid数据，则根据cid重新获取手机号码，重新重新生成一个批次
 *
 * <AUTHOR>
 * @date 2021/11/23
 */
@Component
@Slf4j
public class BatchCoverFactory {

    /**
     * cover 调用线程池
     */
    private static final ExecutorService BATCH_COVER_POOL = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 2,
            100,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("batch-cover-pool-%d").build());

    @Resource
    private BatchTaskMapper batchTaskMapper;
    @Resource
    private BatchTaskReportMapper batchTaskReportMapper;
    @Resource
    private BatchTaskParamMapper batchTaskParamMapper;
    @Resource
    private SignMapper signMapper;
    @Resource
    private DataHandlerFactory dataHandlerFactory;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 兜底处理
     *
     * @param taskId 群发任务的ID
     */
    public AdminResult coverHandler(Integer taskId) {
        // 添加兜底任务处理标记
        // 兜底只要 处理一个taskId就可以了
        if (!addCoverKey(taskId)) {
            return failure("群发任务正在兜底处理中", null);
        }

        BatchTaskDO batchTaskDO = batchTaskMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(batchTaskDO)) {
            log.warn("兜底处理失败,群发任务ID不存在,taskId = {}", taskId);
            return failure("群发任务ID不存在", taskId);
        }

        if (Objects.equals(DeleteEnum.DELETED.getCode(), batchTaskDO.getIsDelete())) {
            log.warn("兜底处理失败,群发任务已删除,taskId = {}", taskId);
            return failure("群发任务已删除", taskId);
        }

        SignDO signDO = signMapper.selectByPrimaryKey(batchTaskDO.getSignId());
        if (Objects.isNull(signDO) || Objects.equals(signDO.getIsDelete(), DeleteEnum.DELETED.getCode())) {
            log.warn("兜底处理失败,群发签名不存在,taskId = {},signId = {}", taskId, batchTaskDO.getSignId());
            return failure("签名不存在", taskId);
        }


        // 查询t_batch_task_report里success=0的t_batch_task_param对应的切片数据
        List<BatchTaskReportDO> batchTaskReportList = batchTaskReportMapper.selectByTaskId(taskId);
        if (CommonUtil.isEmpty(batchTaskReportList)) {
            log.warn("兜底处理失败,未查询到群发报表数据,taskId = {}", taskId);
            return failure("未查询到群发报表数据", taskId);
        }

        Set<Integer> paramIdList = batchTaskReportList.stream()
                .filter(s -> Objects.equals(s.getSuccess(), 0))
                .map(s -> s.getTaskParamId()).collect(Collectors.toSet());

        if (CommonUtil.isEmpty(paramIdList)) {
            log.warn("兜底处理失败,未查找到success=0的报表信息,taskId = {}", taskId);
            return failure("未查找到success=0的报表信息", taskId);
        }

        final String smsTypeCode = batchTaskDO.getSmsTypeCode();
        // 解析第2步获取的切片数据
        BATCH_COVER_POOL.execute(() -> {
            long start = System.currentTimeMillis();
            try {
                batchCover(paramIdList, taskId, signDO.getName(), smsTypeCode);
            } catch (Exception e) {
                log.error("兜底处理失败,taskId = {}", taskId, e);
            } finally {
                // 兜底任务做完，移除兜底任务
                removeCoverKey(taskId);
            }
            log.info("兜底处理耗时 = {}, taskId = {}", (System.currentTimeMillis() - start), taskId);
        });
        return AdminResult.success("已成功加入兜底后台处理");
    }

    /**
     * 群发兜底
     *
     * @param paramIdList
     * @param taskId
     * @param signName
     * @param smsTypeCode
     */
    private void batchCover(Set<Integer> paramIdList, Integer taskId, String signName, String smsTypeCode) {
        for (Integer paramId : paramIdList) {
            try {
                doBatchCover(paramId, taskId, signName, smsTypeCode);
            } catch (Exception e) {
                log.error("做兜底操作doBatchCover失败,paramId ={},taskId = {}", paramId, taskId, e);
            }
        }
    }

    /**
     * 做兜底操作
     *
     * @param paramId
     * @param taskId
     * @param signName
     * @param smsTypeCode
     */
    private void doBatchCover(Integer paramId, Integer taskId, String signName, String smsTypeCode) {
        BatchTaskParamDO batchTaskParamDO = batchTaskParamMapper.selectByPrimaryKey(paramId);
        if (Objects.isNull(batchTaskParamDO)) {
            log.warn("兜底处理未查找到群发分片内容,paramId = {},taskId = {}", paramId, taskId);
            return;
        }

        String paramJsonArray = batchTaskParamDO.getParamJsonArray();
        if (StringUtils.isBlank(paramJsonArray)) {
            log.warn("兜底处理群发分片paramJsonArray字符串为空,paramId = {},taskId = {}", paramId, taskId);
            return;
        }

        List<ParamItemVO> array = doParseParam(batchTaskParamDO, signName, smsTypeCode);
        if (Objects.isNull(array) || array.isEmpty()) {
            log.warn("兜底处理群发分片paramJsonArray为空,paramId = {},taskId = {}", paramId, taskId);
            return;
        }


        // 新生成群发切片数据
        BatchTaskParamDO paramDO = BatchTaskParamDO.builder()
                // 任务ID
                .taskId(taskId)
                // 上传名单文件的md5值
                .fileMd5(batchTaskParamDO.getFileMd5())
                .startOffset(batchTaskParamDO.getStartOffset())
                .endOffset(batchTaskParamDO.getEndOffset())
                .paramJsonArray(JSON.toJSONString(array))
                .build();
        batchTaskParamMapper.insertSelective(paramDO);
        log.info("兜底处理群发分片复制完成,taskId = {}, oldParamId = {},newParamId = {}", taskId, paramId, paramDO.getId());
    }

    /**
     * 做参数解析
     *
     * @param batchTaskParamDO
     * @param signName
     * @param smsTypeCode
     * @return
     */
    private List<ParamItemVO> doParseParam(BatchTaskParamDO batchTaskParamDO, String signName, String smsTypeCode) {
        Integer taskId = batchTaskParamDO.getTaskId();
        Integer paramId = batchTaskParamDO.getId();
        String paramJsonArray = batchTaskParamDO.getParamJsonArray();
        List<ParamItemVO> array = filterBySkipSend(paramJsonArray);
        if (Objects.isNull(array) || array.isEmpty()) {
            log.warn("兜底处理群发分片paramJsonArray不为数组,paramJsonArray ={}, paramId = {}, taskId = {}", paramJsonArray, paramId, taskId);
            return null;
        }
        ParamItemVO paramItemVO;
        String mobile;
        String cid;
        // cid 映射
        // key -> cid value -> cid具体值信息
        Map<String, ParamItemVO> cidMapping = new HashMap<>(16);
        Iterator<ParamItemVO> iterator = array.iterator();
        while (iterator.hasNext()) {
            paramItemVO = iterator.next();
            if (Objects.isNull(paramItemVO)) {
                continue;
            }

            mobile = paramItemVO.get(BatchTaskConstants.DataType.MOBILE);
            if (StringUtils.isNotBlank(mobile)) {
                // 手机号不为空
                continue;
            }

            // 手机号为空时 则获取cid数据
            cid = paramItemVO.get(BatchTaskConstants.DataType.CID);
            if (StringUtils.isNotBlank(cid)) {
                // cid不为空,手机号为空
                cidMapping.put(cid, paramItemVO);
            }
            // 删除数据 如果cid存在的话，待cid查询重新设置数据
            iterator.remove();
        }

        List<ParamItemVO> jsonObjectList = this.doQueryByCid(signName, cidMapping, batchTaskParamDO, smsTypeCode);
        if (!CommonUtil.isEmpty(jsonObjectList)) {
            array.addAll(jsonObjectList);
        }

        return array;
    }

    /**
     * 过滤掉包含skipSend分片数据
     *
     * @param paramJsonArray
     * @return
     */
    private List<ParamItemVO> filterBySkipSend(String paramJsonArray) {
        List<ParamItemVO> array = JSON.parseArray(paramJsonArray, ParamItemVO.class);
        if (Objects.isNull(array)) {
            return null;
        }

        return array.stream().filter(s -> !hasSkipSend(s)).collect(Collectors.toList());
    }

    /**
     * 判断是否包含skipSend属性
     *
     * @param paramItemVO
     * @return true 包含skip属性
     */
    private boolean hasSkipSend(ParamItemVO paramItemVO) {
        if (Objects.isNull(paramItemVO)) {
            // 为空则过滤掉
            return true;
        }
        try {
            String skipSend = paramItemVO.get(BatchTaskConstants.DataType.SKIP_SEND);
            return StringUtils.equalsAnyIgnoreCase(skipSend, "true");
        } catch (Exception e) {
            log.warn("batchCoverFactory判断是否包含skipSend属性失败,paramItemVO ={}", JSON.toJSONString(paramItemVO), e);
        }
        return false;
    }

    /**
     * 做cid查询 重新赋值
     *
     * @param signName
     * @param cidMapping
     * @param batchTaskParamDO
     * @param smsTypeCode
     * @return
     */
    private List<ParamItemVO> doQueryByCid(String signName, Map<String, ParamItemVO> cidMapping, BatchTaskParamDO batchTaskParamDO, String smsTypeCode) {
        if (Objects.isNull(cidMapping) || cidMapping.isEmpty()) {
            return null;
        }
        List<String> cidList = cidMapping.keySet().stream().collect(Collectors.toList());
        Integer taskId = batchTaskParamDO.getTaskId();
        Integer paramId = batchTaskParamDO.getId();
        CustomerDataHandler customerDataHandler = dataHandlerFactory.getCustomerDataHandler(signName);
        log.info("兜底处理聚合器使用 customerDataHandler = {},signName = {},taskId = {}", customerDataHandler.getClass(), signName, taskId);
        Map<String, CustomerVO> customerVOMap = customerDataHandler.query(cidList, smsTypeCode);
        if (Objects.isNull(customerVOMap)) {
            // 未查询到cid数据
            log.warn("未查询到cid数据信息,cidList = {},taskId = {},paramId = {}", JSON.toJSONString(cidList), taskId, paramId);
            return null;
        }

        List<ParamItemVO> resultList = new ArrayList<>();
        CustomerVO customerVO;
        ParamItemVO paramItemVO;
        for (String cid : cidList) {
            customerVO = customerVOMap.get(cid);
            if (Objects.isNull(customerVO)) {
                log.warn("根据cid未查询到用户信息, cid = {},taskId = {},paramId = {}", cid, taskId, paramId);
                continue;
            }

            paramItemVO = cidMapping.get(cid);
            paramItemVO.put(BatchTaskConstants.DataType.MOBILE, customerVO.getMobile());
            paramItemVO.put(BatchTaskConstants.DataType.APPLY_LOAN_RESULT, customerVO.getApplyLoanResult());
            resultList.add(paramItemVO);
        }

        return resultList;
    }


    /**
     * 错误消息
     *
     * @param message
     * @param taskId  群发任务不为空 则需要删除兜底缓存
     * @return
     */
    private AdminResult failure(String message, Integer taskId) {
        if (Objects.nonNull(taskId)) {
            removeCoverKey(taskId);
        }
        return AdminResult.error(message);
    }


    /**
     * 移除兜底缓存
     *
     * @param taskId
     */
    public void removeCoverKey(Integer taskId) {
        stringRedisTemplate.delete(RedisKeys.BatchTaskKeys.BATCH_TASK_COVER_STR_KEY + ":" + taskId);
    }

    /**
     * 添加兜底key
     * <p>
     * 后台页面操作 暂时不考虑并发问题
     *
     * @param taskId
     * @return 返回true表示兜底key加入缓存成功
     */
    private synchronized boolean addCoverKey(Integer taskId) {
        if (isCovering(taskId)) {
            return false;
        }
        stringRedisTemplate.opsForValue().set(RedisKeys.BatchTaskKeys.BATCH_TASK_COVER_STR_KEY + ":" + taskId, String.valueOf(taskId), 30, TimeUnit.MINUTES);
        return true;
    }

    /**
     * 判断是否正在兜底处理
     * <p>
     * 后台页面操作 暂时不考虑并发问题
     *
     * @param taskId
     * @return
     */
    private boolean isCovering(Integer taskId) {
        String value = stringRedisTemplate.opsForValue().get(RedisKeys.BatchTaskKeys.BATCH_TASK_COVER_STR_KEY + ":" + taskId);
        return StringUtils.isNotBlank(value);
    }
}
