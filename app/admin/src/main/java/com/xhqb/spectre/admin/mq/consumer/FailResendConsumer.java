package com.xhqb.spectre.admin.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.MQMessage;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.admin.service.FailResendRecordService;
import com.xhqb.spectre.common.dal.dto.mq.CmppDeliverResqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 补发消费者
 */
@Component
@Slf4j
public class FailResendConsumer {
    
    @Resource
    private FailResendRecordService failResendRecordService;
    
    /**
     * 处理补发落库
     * 
     * @param message mq消息
     */
    @MQConsumer(topic = "${tdmq.producers.failResend}",
            subscriptionType = SubscriptionType.Shared,
            clazz = String.class,
            ackTimeout = 60L,
            maxRedeliverCount = 1,
            negativeAckRedeliveryDelay = 60,
            receiverQueueSize = 100)
    public void execute(MQMessage<String> message) {
        try {
            log.debug("接收补发处理消息; messageId={}, message={}", message.getMessageId(), message.getValue());
            if (message.getRedeliveryCount() == 0) {
                CmppDeliverResqDTO cmppDeliverResqDTO = JSONObject.parseObject(message.getValue(), CmppDeliverResqDTO.class);
                if (cmppDeliverResqDTO == null) {
                    log.warn("解析补发消息失败, message: {}", message.getValue());
                    return;
                }

                log.debug("处理补发消息, orderId={}, tplCode={}, resend={}, reportStatus={}, messageId={}",
                        cmppDeliverResqDTO.getOrderId(), cmppDeliverResqDTO.getTplCode(),
                        cmppDeliverResqDTO.getResend(), cmppDeliverResqDTO.getReportStatus(),
                        message.getMessageId());

                failResendRecordService.processFailResend(cmppDeliverResqDTO);
            }
        } catch (Exception e) {
            log.error("处理补发消息异常, message: {}", message, e);
        }
    }
}
