package com.xhqb.spectre.admin.config.properties;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/17 18:13
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ConfigurationProperties(prefix = "kael.datasource.druid.xh-cif")
public class CifDruidProperties extends DruidProperties {
}
