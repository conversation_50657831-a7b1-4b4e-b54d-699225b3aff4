package com.xhqb.spectre.admin.mq.message;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 消息发送
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskParamMessage implements Serializable {
    /**
     * 群发任务的批次号
     */
    private Integer taskId;
    /**
     * 群发批次下的参数ID
     */
    private Integer taskParamId;
    /**
     * 应用编码
     */
    private String appCode;
    /**
     * 模板编码
     */
    private String tplCode;
    /**
     * 应用签名名称
     */
    private String signName;
    /**
     * 短信类型[通过查看api实现逻辑，短信类型为市场营销时必须填写，其他类型不需要设置值]
     * <p>
     * 参考 SMSMessageManagerServiceImpl.checkTplData
     */
    private String smsCodeType;
    /**
     * 签名code，为空则使用小花钱包签名
     */
    private String signCode;
    /**
     * 模板类型 0->[*]占位符  1->${xx}名称占位符
     */
    private Integer tplType;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
