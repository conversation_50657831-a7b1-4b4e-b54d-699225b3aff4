package com.xhqb.spectre.admin.batchtask.parse;

import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 内容分组工具
 * <p>
 * 文件格式约定：
 * 若要同时包含cid和mobile关键字，只能放在第一和第二列
 *
 * <AUTHOR>
 * @date 2022/7/26
 */
public class ResultGroupFactory {

    private static final String[] KEY_WORDS = {BatchTaskConstants.DataType.MOBILE,
            BatchTaskConstants.DataType.CID};


    /**
     * 是否是组合模式(同时包含cid和mobile关键字)
     */
    private boolean complexMode;

    /**
     * 解析结果集列表
     */
    private List<ParseResult> parseResultList;

    /**
     * 第一列关键字解析内容(一定不为空)
     */
    private ParseResult parseResult1;
    /**
     * 第二列解关键字析内容(complexMode=true时不为空)
     */
    private ParseResult parseResult2;

    /**
     * 解析内容分组
     *
     * @param result 原始解析内容
     * @param header 文件标题
     */
    public ResultGroupFactory(ParseResult result, List<String> header) {
        this.init(result, header);
    }

    /**
     * 初始化分组数据
     *
     * @param header
     */
    private void init(ParseResult result, List<String> header) {
        this.parseResultList = new ArrayList<>();
        this.complexMode = this.isComplexMode(header);
        // 第一列关键字解析结果
        parseResult1 = result.copy();

        if (!complexMode) {
            // 非组合模式
            parseResult1.setTitleList(header);
            parseResult1.setContentType(header.get(0));
            this.parseResultList.add(parseResult1);
            return;
        }


        List<String> titleList1 = this.skipByIndex(header, 1);
        parseResult1.setTitleList(titleList1);
        parseResult1.setContentType(titleList1.get(0));
        this.parseResultList.add(parseResult1);

        // 第二列关键字解析内容
        parseResult2 = result.copy();
        List<String> titleList2 = this.skipByIndex(header, 0);
        parseResult2.setTitleList(titleList2);
        parseResult2.setContentType(titleList2.get(0));
        this.parseResultList.add(parseResult2);
    }

    /**
     * 解析具体内容
     * <p>
     * 根据complexMode分别存入parseResult1和parseResult2
     *
     * @param columnList
     */
    public void parseContent(List<String> columnList) {
        if (!this.complexMode) {
            // 非组合模式
            ContentItem contentItem = new ContentItem(columnList.get(0), columnList);
            // 非组合模式默认使用parseResult1结果集进行收集处理
            parseResult1.addContentItem(contentItem);
            return;
        }

        // 组合模式
        // 第一列
        String column1 = columnList.get(0);
        // 第二列
        String column2 = columnList.get(1);

        // 第一列数据不为空 或者第二列数据为空 则收集第一列数据
        // 否则收集第二列数据
        if (StringUtils.isNotBlank(column1) || StringUtils.isBlank(column2)) {
            // 收集第一列数据(过滤掉第二列关键字数据)
            ContentItem contentItem = new ContentItem(columnList.get(0), skipByIndex(columnList, 1));
            parseResult1.addContentItem(contentItem);
            return;
        }

        // 收集第二列数据(过滤掉第一列的数据)
        ContentItem contentItem = new ContentItem(columnList.get(1), skipByIndex(columnList, 0));
        parseResult2.addContentItem(contentItem);
    }


    /**
     * 判断是否是组合模式
     *
     * @param header
     * @return
     */
    private boolean isComplexMode(List<String> header) {
        boolean hasMore = header.size() > 1;
        if (!hasMore) {
            return false;
        }

        // 第一列
        String col1 = header.get(0);
        // 第二列
        String col2 = header.get(1);
        // 第一列和第二列同时包含关键字则为组合模式
        return StringUtils.equalsAny(col1, KEY_WORDS) && StringUtils.equalsAny(col2, KEY_WORDS);
    }

    /**
     * @param dataList
     * @param idx      下标 ，注意下标从0开始
     * @param <T>
     * @return
     */
    private <T> List<T> skipByIndex(List<T> dataList, int... idx) {
        List<Integer> indexList = Arrays.stream(idx).boxed().collect(Collectors.toList());
        List<T> result = new ArrayList<>(dataList.size());
        for (int i = 0; i < dataList.size(); i++) {
            if (indexList.contains(i)) {
                continue;
            }
            result.add(dataList.get(i));
        }
        return result;
    }

    public boolean isComplexMode() {
        return complexMode;
    }

    public List<ParseResult> getParseResultList() {
        return parseResultList;
    }
}
