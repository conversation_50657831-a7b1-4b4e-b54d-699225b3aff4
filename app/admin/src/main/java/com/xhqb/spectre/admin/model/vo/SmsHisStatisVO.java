package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.SmsHisStatisDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 短信历史发送量统计表
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsHisStatisVO implements Serializable {

    /**
     * 类型编码
     */
    private String code;

    /**
     * 累积发送总量
     */
    private Long hisSendTotal;
    /**
     * 累积计费总量
     */
    private Long hisBillingTotal;
    /**
     * 上月发送量
     */
    private Long lastmonthSendTotal;
    /**
     * 上月计费量
     */
    private Long lastmonthBillingTotal;
    /**
     * 本月发送量
     */
    private Long curmonthSendTotal;
    /**
     * 本月计费量
     */
    private Long curmonthBillingTotal;
    /**
     * 昨日发送量
     */
    private Long lastdaySendTotal;
    /**
     * 昨日计费量
     */
    private Long lastdayBillingTotal;
    /**
     * 今日发送量
     */
    private Long todaySendTotal;
    /**
     * 今日计费总量
     */
    private Long todayBillingTotal;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 查询列表数据展现
     *
     * @param smsDayStatisDO
     * @return
     */
    public static SmsHisStatisVO buildListQuery(SmsHisStatisDO smsDayStatisDO) {
        return SmsHisStatisVO.builder()
                // 类型编码
                .code(smsDayStatisDO.getCode())
                // 累积发送总量
                .hisSendTotal(smsDayStatisDO.getHisSendTotal())
                // 累积计费总量
                .hisBillingTotal(smsDayStatisDO.getHisBillingTotal())
                // 上月发送量
                .lastmonthSendTotal(smsDayStatisDO.getLastmonthSendTotal())
                // 上月计费量
                .lastmonthBillingTotal(smsDayStatisDO.getLastmonthBillingTotal())
                // 本月发送量
                .curmonthSendTotal(smsDayStatisDO.getCurmonthSendTotal())
                // 本月计费量
                .curmonthBillingTotal(smsDayStatisDO.getCurmonthBillingTotal())
                // 昨日发送量
                .lastdaySendTotal(smsDayStatisDO.getLastdaySendTotal())
                // 昨日计费量
                .lastdayBillingTotal(smsDayStatisDO.getLastdayBillingTotal())
                // 今日发送量
                .todaySendTotal(smsDayStatisDO.getTodaySendTotal())
                // 今日计费总量
                .todayBillingTotal(smsDayStatisDO.getTodayBillingTotal())
                // 创建时间
                .createTime(smsDayStatisDO.getCreateTime())
                // 更新时间
                .updateTime(smsDayStatisDO.getUpdateTime())
                .build();
    }

}
