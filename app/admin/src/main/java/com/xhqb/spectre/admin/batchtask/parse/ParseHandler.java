package com.xhqb.spectre.admin.batchtask.parse;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.xhqb.spectre.admin.model.result.FileCheckResult;
import com.xhqb.spectre.admin.model.user.url.UserShortUrlFileInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class ParseHandler {
    public FileCheckResult parse(UserShortUrlParseContext context, FileCheckResult result) throws IOException {

        InputStream inputStream = context.getInputStream();
        try {
            EasyExcelFactory.read(inputStream, UserShortUrlFileInfo.class, new AnalysisEventListener<Object>() {

                @Override
                public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                    List<String> paramList = new LinkedList<>(headMap.values());
                    result.setTitleList(paramList);
                }

                @Override
                public void invoke(Object data, AnalysisContext context) {
                    UserShortUrlFileInfo userShortUrlFileInfo = (UserShortUrlFileInfo) data;
                    log.info("解析到一条数据:{}", Objects.toString(userShortUrlFileInfo));
                    result.addExcel(userShortUrlFileInfo);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("文件上传EasyExcel解析完成");
                }
            }).sheet(0).doRead();
        } finally {
            inputStream.close();
        }
        return result;
    }
}
