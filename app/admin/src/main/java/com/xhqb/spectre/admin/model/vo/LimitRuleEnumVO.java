package com.xhqb.spectre.admin.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/2 14:49
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LimitRuleEnumVO implements Serializable {

    private static final long serialVersionUID = -1026179752661103393L;

    private String code;

    private String name;

    public static LimitRuleEnumVO buildLimitRuleEnumVO(String code, String name) {
        return LimitRuleEnumVO.builder()
                .code(code)
                .name(name)
                .build();
    }
}
