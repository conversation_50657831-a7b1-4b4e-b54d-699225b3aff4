package com.xhqb.spectre.admin.service.impl;


import cn.hutool.core.date.DateUtil;
import com.xhqb.spectre.admin.constant.MobileBlackConstant;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.MobileBlackDTO;
import com.xhqb.spectre.admin.model.vo.MobileBlackVO;
import com.xhqb.spectre.admin.service.MobileBlackService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.MobileBlackDO;
import com.xhqb.spectre.common.dal.mapper.MobileBlackMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.MobileBlackQuery;
import com.xhqb.spectre.common.enums.BlacklistActionEnum;
import com.xhqb.spectre.common.enums.BlacklistTypeEnum;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import com.xhqb.spectre.common.service.SmsEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/26 20:46
 * @Description:
 */
@Service
@Slf4j
public class MobileBlackServiceImpl implements MobileBlackService {
    @Resource
    private MobileBlackMapper mobileBlackMapper;

    @Autowired
    @Qualifier("adminSmsEventService")
    private SmsEventService smsEventService;

    /**
     * 分页查询黑名单
     *
     * @param mobileBlackQuery
     * @return
     */
    @Override
    public CommonPager<MobileBlackVO> listByPage(MobileBlackQuery mobileBlackQuery) {
        return PageResultUtils.result(
                () -> mobileBlackMapper.countByQuery(mobileBlackQuery),
                () -> mobileBlackMapper.selectByQuery(mobileBlackQuery).stream()
                        .map(item -> MobileBlackVO.buildMobileBlackVO(item, true)).collect(Collectors.toList())
        );
    }

    /**
     * 查询黑名单信息
     *
     * @param id
     * @return
     */
    @Override
    public MobileBlackVO getById(Integer id) {
        MobileBlackDO mobileBlackDO = validateAndSelectById(id);
        return MobileBlackVO.buildMobileBlackVO(mobileBlackDO, false);
    }

    /**
     * 添加黑名单
     *
     * @param addDTO
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void create(MobileBlackDTO addDTO) {
        //校验
        checkAddParam(addDTO);

        //写入黑名单数据
        String currentUser = SsoUserInfoUtil.getUserName();
        List<String> smsTypeCodeList = addDTO.getSmsTypeCodeList();

        List<String> mobiles = addDTO.getMobileList();

        mobiles.forEach(mobile -> smsTypeCodeList.forEach(smsTypeCode -> {
            MobileBlackDO exist = mobileBlackMapper.selectByMobileAndSmsType(mobile, smsTypeCode);
            if (Objects.isNull(exist)) {
                MobileBlackDO tmpDO = buildMobileBlackDO(addDTO, smsTypeCode, currentUser, 0, mobile);
                mobileBlackMapper.insertSelective(tmpDO);

                sendBlacklistAddEvent(mobile, smsTypeCode, addDTO.getDescription(), currentUser);
            } else {
                if (isManualAdd(exist)) {
                    //人工添加的黑名单，才能覆盖
                    MobileBlackDO tmpDO = buildMobileBlackDO(addDTO, smsTypeCode, currentUser, exist.getId(), mobile);
                    mobileBlackMapper.updateByPrimaryKeySelective(tmpDO);

                    sendBlacklistAddEvent(mobile, smsTypeCode, addDTO.getDescription(), currentUser);
                }
            }
        }));
    }

    /**
     * 删除黑名单
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void delete(Integer id) {
        MobileBlackDO mobileBlackDO = validateAndSelectById(id);
        if (!isManualAdd(mobileBlackDO)) {
            throw new BizException("非人工添加的黑名单，不能删除");
        }

        //删除记录
        String currentUser = SsoUserInfoUtil.getUserName();
        mobileBlackMapper.delete(id, currentUser);

        sendBlacklistRemoveEvent(mobileBlackDO.getMobile(), mobileBlackDO.getSmsTypeCode(), "管理员删除", currentUser);

    }

    /**
     * 批量删除黑名单
     *
     * @param idList
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void batchDelete(List<Integer> idList) {
        //校验
        if (CollectionUtils.isEmpty(idList)) {
            throw new BizException("待删除的黑名单ID不能为空");
        }
        List<MobileBlackDO> mobileBlackDOList = mobileBlackMapper.selectByIdList(idList);
        Map<Integer, MobileBlackDO> map = mobileBlackDOList.stream().collect(Collectors.toMap(MobileBlackDO::getId, Function.identity()));
        idList.forEach(id -> {
            if (!map.containsKey(id)) {
                throw new BizException("未找到黑名单，ID：" + id);
            }
            if (!isManualAdd(map.get(id))) {
                throw new BizException("非人工添加的黑名单，不能删除，ID：" + id);
            }
        });

        //批量删除
        String currentUser = SsoUserInfoUtil.getUserName();
        mobileBlackMapper.deleteByIdList(idList, currentUser);

        idList.forEach(id -> {
            MobileBlackDO mobileBlackDO = map.get(id);
            sendBlacklistRemoveEvent(mobileBlackDO.getMobile(), mobileBlackDO.getSmsTypeCode(), "管理员批量删除", currentUser);
        });

    }

    private MobileBlackDO validateAndSelectById(Integer id) {
        MobileBlackDO mobileBlackDO = mobileBlackMapper.selectByPrimaryKey(id);
        if (Objects.isNull(mobileBlackDO)) {
            throw new BizException("未找到该黑名单");
        }
        return mobileBlackDO;
    }

    private void checkAddParam(MobileBlackDTO mobileBlackDTO) {
        //参数格式校验
        ValidatorUtil.validate(mobileBlackDTO);

        mobileBlackDTO.getMobileList().forEach(mobile -> {
            if (!Pattern.matches("^1[3-9]\\d{9}$", mobile)) {
                throw new BizException("手机号格式不正确：" + mobile);
            }
        });

        //短信类型校验
        mobileBlackDTO.getSmsTypeCodeList().forEach(item -> {
            if (!MessageTypeEnum.contains(item)) {
                throw new BizException("不支持该短信类型编码：" + item);
            }
        });
    }

    private MobileBlackDO buildMobileBlackDO(MobileBlackDTO addDTO, String smsTypeCode, String currentUser, Integer id, String mobile) {
        MobileBlackDO mobileBlackDO = MobileBlackDO.builder()
                .cid(addDTO.getCid())
                .mobile(mobile)
                .source(addDTO.getSource())
                .smsTypeCode(smsTypeCode)
                .description(addDTO.getDescription())
                .addType(MobileBlackConstant.ADD_TYPE_MANUAL)
                .updater(currentUser)
                .expiredTime(DateUtil.parseDateTime(addDTO.getExpiredTime()))
                .build();
        if (id > 0) {
            //更新
            mobileBlackDO.setId(id);
        } else {
            //新增
            mobileBlackDO.setCreator(currentUser);
        }
        return mobileBlackDO;
    }

    private boolean isManualAdd(MobileBlackDO mobileBlackDO) {
        return mobileBlackDO.getAddType().equals(MobileBlackConstant.ADD_TYPE_MANUAL);
    }

    private String buildCacheValue(String smsTypeCode, String mobile) {
        return smsTypeCode + "_" + mobile;
    }

    private void sendBlacklistAddEvent(String mobile, String smsTypeCode, String reason, String operator) {
        try {
            smsEventService.sendBlacklistEvent(
                mobile,
                BlacklistActionEnum.ADD,
                BlacklistTypeEnum.ADMIN_OPERATION,
                smsTypeCode,
                reason,
                operator,
                "admin-manual-add"
            );
        } catch (Exception e) {
            log.error("发送加入黑名单事件失败: mobile={}, smsType={}, error={}", mobile, smsTypeCode, e.getMessage(), e);
        }
    }

    private void sendBlacklistRemoveEvent(String mobile, String smsTypeCode, String reason, String operator) {
        try {
            smsEventService.sendBlacklistEvent(
                mobile,
                BlacklistActionEnum.REMOVE,
                BlacklistTypeEnum.ADMIN_OPERATION,
                smsTypeCode,
                reason,
                operator,
                "admin-manual-remove"
            );
        } catch (Exception e) {
            log.error("发送退出黑名单事件失败: mobile={}, smsType={}, error={}", mobile, smsTypeCode, e.getMessage(), e);
        }
    }


}
