package com.xhqb.spectre.admin.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.service.AutoTestTaskInvokeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 自动测试任务Job
 *
 * <AUTHOR>
 * @date 2025-07-09 13:55:16
 */
@Slf4j
@Component
@Job("autoTestTaskJob")
public class AutoTestTaskJob implements SimpleJob {

    @Resource
    private AutoTestTaskInvokeService autoTestTaskInvokeService;

    @Override
    public void execute(ShardingContext shardingContext) {
        long timeMillis = System.currentTimeMillis();
        this.autoTestTaskInvokeService.invoke();
        log.info("auto test job invoke end. time consume {}ms", (System.currentTimeMillis() - timeMillis));

    }
}
