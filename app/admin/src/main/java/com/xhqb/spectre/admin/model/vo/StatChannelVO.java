package com.xhqb.spectre.admin.model.vo;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
@Builder
public class StatChannelVO {

    private List<SubChannel> months;

    private List<SubChannel> weeks;

    @Data
    @Builder
    public static class SubChannel {
        /**
         * 渠道编码
         */
        private String channelCode;

        private String channelCodeName;
        private Set<String> signList;

        public void addSign(String signName) {
            signList.add(signName);
        }
    }


    @Data
    @Builder
    public static class Sign {
        private Integer id;
        private String signName;
    }


}
