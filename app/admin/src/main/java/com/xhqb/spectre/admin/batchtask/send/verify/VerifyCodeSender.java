package com.xhqb.spectre.admin.batchtask.send.verify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.send.MessageSendResult;
import com.xhqb.spectre.admin.batchtask.send.MessageSender;
import com.xhqb.spectre.admin.batchtask.send.SpectreApiBooster;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.constant.Apis;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 验证码发送
 *
 * <AUTHOR>
 * @date 2021/10/14
 */
@Component
@Slf4j
public class VerifyCodeSender implements MessageSender<VerifyCodeRequest, VerifyCodeResponse> {

    @Autowired
    private VenusConfig venusConfig;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private SpectreApiBooster spectreApiBooster;

    /**
     * 发送消息处理
     *
     * @param messageRequest
     * @return
     * @throws Exception
     */
    @Override
    public MessageSendResult<VerifyCodeResponse> send(VerifyCodeRequest messageRequest) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        // 填充签名请求头
        spectreApiBooster.boost(messageRequest, headers);
        headers.add(BatchTaskConstants.HttpHeader.CONTENT_TYPE, BatchTaskConstants.HttpHeader.JSON_TYPE);
        HttpEntity<String> requestEntity = new HttpEntity<>(messageRequest.toJSONString(), headers);
        ResponseEntity<MessageSendResult> response = restTemplate.exchange(this.getVerifyCodeApi(), HttpMethod.POST, requestEntity, MessageSendResult.class);
        if (!Objects.equals(response.getStatusCode(), HttpStatus.OK)) {
            return response.getBody();
        }

        String s = JSON.toJSONString(response.getBody());
        return JSON.parseObject(s, new TypeReference<MessageSendResult<VerifyCodeResponse>>() {
        });
    }


    /**
     * 验证码发送地址
     *
     * @return
     */
    public String getVerifyCodeApi() {
        return venusConfig.getSpectreApiHost() + Apis.SpectreApi.VERIFY_CODE_API_NAME;
    }
}
