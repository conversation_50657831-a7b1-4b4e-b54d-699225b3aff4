package com.xhqb.spectre.admin.statistics.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsHisStatisDO implements Serializable {

    private static final long serialVersionUID = -1393094822723908077L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.code
     *
     * @mbggenerated
     */
    private String code;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.his_send_total
     *
     * @mbggenerated
     */
    private Long hisSendTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.his_billing_total
     *
     * @mbggenerated
     */
    private Long hisBillingTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.lastmonth_send_total
     *
     * @mbggenerated
     */
    private Long lastmonthSendTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.lastmonth_billing_total
     *
     * @mbggenerated
     */
    private Long lastmonthBillingTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.curmonth_send_total
     *
     * @mbggenerated
     */
    private Long curmonthSendTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.curmonth_billing_total
     *
     * @mbggenerated
     */
    private Long curmonthBillingTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.lastday_send_total
     *
     * @mbggenerated
     */
    private Long lastdaySendTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.lastday_billing_total
     *
     * @mbggenerated
     */
    private Long lastdayBillingTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.today_send_total
     *
     * @mbggenerated
     */
    private Long todaySendTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.today_billing_total
     *
     * @mbggenerated
     */
    private Long todayBillingTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

}