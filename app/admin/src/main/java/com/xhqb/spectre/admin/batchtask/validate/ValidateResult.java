package com.xhqb.spectre.admin.batchtask.validate;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.parse.ContentItem;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 校验结果
 * 文件检测结果：
 * 共上传170条数据，
 * 有效CID100个
 * 无效CID10个，分别为：
 * 1122，333，444
 * 参数漏填10个，分别为：
 * 455，55
 * 重复20个，分别为：
 * 66，77
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
@Data
@Slf4j
public class ValidateResult implements Serializable {

    /**
     * 总数据
     */
    private Integer total;
    /**
     * 有效数量列表
     */
    private List<ContentItem> validList;
    /**
     * 无效数量列表
     */
    private List<ContentItem> badList;
    /**
     * 重复数量列表
     */
    private List<ContentItem> repeatList;
    /**
     * 缺少参数数量列表
     */
    private List<ContentItem> missList;

    /**
     * cid数据与手机号映射关系
     */
    private Map<String, String> cidMobileMapping;

    /**
     * 空号数据
     */
    private List<ContentItem> phoneEmptyList;
    /**
     * 停机数据
     */
    private List<ContentItem> phoneHaltList;


    public ValidateResult() {
        // 优化点 设置合适的容器大小 防止map自动扩容
        this.cidMobileMapping = new ConcurrentHashMap<>(128);
    }

    /**
     * cid与mobile映射关系
     *
     * @param cid
     * @param mobile
     * @return
     */
    public ValidateResult addMapping(String cid, String mobile) {
        this.cidMobileMapping.putIfAbsent(cid, mobile);
        return this;
    }

    /**
     * 根据cid值获取手机号
     *
     * @param cid
     * @return
     */
    public String getMobile(String cid) {
        String[] array = this.doSplit(cid);
        if (Objects.isNull(array) || array.length != BatchTaskConstants.Commons.LENGTH_TWO) {
            log.info("未获取到手机号信息 = {},cid = {},mapping = {}", JSON.toJSONString(array), cid, this.cidMobileMapping.get(cid));
            return "";
        }
        return array[0];
    }

    /**
     * 获取到完件信息
     *
     * @param cid
     * @return
     */
    public String getApplyLoanResult(String cid) {
        String[] array = this.doSplit(cid);
        if (Objects.isNull(array) || array.length != BatchTaskConstants.Commons.LENGTH_TWO) {
            log.info("未获取到完件信息 = {},cid = {},mapping = {}", JSON.toJSONString(array), cid, this.cidMobileMapping.get(cid));
            return "";
        }
        return array[1];
    }

    /**
     * 做数据分割
     *
     * @param cid
     * @return
     */
    private String[] doSplit(String cid) {
        // mobile@f@applyLoanResult
        String mapping = this.cidMobileMapping.get(cid);
        if (StringUtils.isBlank(mapping)) {
            return null;
        }
        return mapping.split(BatchTaskConstants.Commons.SPLIT_FLAG);
    }
}
