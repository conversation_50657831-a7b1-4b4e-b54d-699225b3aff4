package com.xhqb.spectre.admin.util;

import com.xhqb.spectre.common.dal.dto.DailyStats;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
public class DailyStatsCacheUtil {
    /**
     * gw级指标缓存key前缀
     */
    public static final String GW_KEY_PREFIX = "spectre:gw_stats:";

    public static final String APP_KEY_PREFIX = "spectre:app_stats:";

    private RedisTemplate<String, Object> redisTemplate;

    private HashOperations<String, String, Object> hashOps;

    public DailyStatsCacheUtil(RedisTemplate<String, Object> redisTemplate) {

        this.redisTemplate = redisTemplate;
        hashOps = redisTemplate.opsForHash();
    }

    /**
     * 获取 Redis Key
     *
     * @param prefix  前缀
     * @param groupName 分组名
     * @param username 账户 IDD
     * @param statDate  统计日期
     * @return Redis Key
     */
    private String getRedisKey(String prefix, String groupName, String username, LocalDate statDate) {
        String statDateStr = statDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String key = prefix + groupName + ":" + username + ":" + statDateStr;
        if (Boolean.FALSE.equals(redisTemplate.hasKey(key))) {
            hashOps.put(key, "init", statDateStr);
            setKeyExpiration(key, 7L, TimeUnit.DAYS);
        }
        return key;
    }


    /**
     * 设置键的过期时间
     *
     * @param key     键
     * @param timeout 过期时间
     * @param unit    时间单位
     */
    private void setKeyExpiration(String key, long timeout, TimeUnit unit) {
        redisTemplate.expire(key, timeout, unit);
    }


    /**
     * 获取某个 accountId 当天的所有统计数据，并封装为 DailyStats 对象
     *
     * @param username 账户 ID
     * @param statDate  统计日期
     * @return DailyStats 对象
     */
    public DailyStats getDailyStats(String prefix, String groupName, String username, LocalDate statDate) {
        String key = getRedisKey(prefix, groupName, username, statDate);
        Map<String, Object> stats = hashOps.entries(key);

        DailyStats dailyStats = new DailyStats(username, statDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        // 遍历 Redis 中的字段和值
        for (Map.Entry<String, Object> entry : stats.entrySet()) {
            String field = entry.getKey();
            String value = entry.getValue().toString();

            if (field.startsWith("tpl:")) {
                // 解析模板维度的统计信息
                String[] parts = field.split(":");
                if (parts.length == 4) {
                    String tplCode = parts[1];
                    String errCode = parts[3];

                    dailyStats.addTemplateStats(tplCode, errCode, Integer.parseInt(value));
                }
            }
        }

        return dailyStats;
    }
}

