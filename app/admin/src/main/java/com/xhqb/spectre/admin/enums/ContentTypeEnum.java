package com.xhqb.spectre.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ContentTypeEnum {

    OUT("OUT", 1, "外部短信"),
    IN("IN", 0, "内部短信");
    private final String code;
    private final Integer oaCode;
    private final String desc;


    public static Integer getOaCodeByCode(String code) {
        for (ContentTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value.getOaCode();
            }
        }
        return 0;
    }
}
