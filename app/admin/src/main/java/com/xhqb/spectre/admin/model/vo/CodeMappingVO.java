package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.CodeMappingDO;
import com.xhqb.spectre.common.dal.entity.ErrorCodeDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 映射码
 *
 * <AUTHOR>
 * @date 2021/10/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CodeMappingVO implements Serializable {

    /**
     * 渠道code
     */
    private String channelCode;
    /**
     * 错误码类型 submit->短信发送 deliver->短信回执
     */
    private String type;
    /**
     * 渠道错误码编码
     */
    private String channelErrCode;
    /**
     * xh错误码编码
     */
    private Integer xhErrCode;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 小花错误码描述
     */
    private String xhErrCodeDesc;


    /**
     * 查询列表数据展现
     *
     * @param codeMappingDO
     * @return
     */
    public static CodeMappingVO buildListQuery(CodeMappingDO codeMappingDO) {
        CodeMappingVO codeMappingVO = CodeMappingVO.builder()
                // 渠道code
                .channelCode(codeMappingDO.getChannelCode())
                // 错误码类型 submit->短信发送 deliver->短信回执
                .type(codeMappingDO.getType())
                // 渠道错误码编码
                .channelErrCode(codeMappingDO.getChannelErrCode())
                // xh错误码编码
                .xhErrCode(codeMappingDO.getXhErrCode())
                // 创建时间
                .createTime(codeMappingDO.getCreateTime())
                // 更新时间
                .updateTime(codeMappingDO.getUpdateTime())
                .build();
        ErrorCodeDO errorCodeDO = codeMappingDO.getErrorCodeDO();
        if (Objects.nonNull(errorCodeDO)) {
            codeMappingVO.setXhErrCodeDesc(errorCodeDO.getCodeDesc());
        }
        return codeMappingVO;
    }

    /**
     * 查询数据详情展现
     *
     * @param codeMappingDO
     * @return
     */
    public static CodeMappingVO buildInfoQuery(CodeMappingDO codeMappingDO) {
        return buildListQuery(codeMappingDO);
    }
}
