package com.xhqb.spectre.admin.bidata.entity;

import lombok.Data;

/**
 * 供应商纵向触达统计
 */
@Data
public class DateReachStatDO {
    /**
     * 统计日期
     * 月份
     */
    private String statWeek;
    /**
     * 渠道编码
     */
    private String channelCode;

    private Integer verifyReachCount;
    private Integer notifyReachCount;
    private Integer marketReachCount;
    private Integer collectorReachCount;
    private Integer lightCollectorReachCount;
    private Integer severeCollectorReachCount;
    private Integer debtSwapReachCount;

    private Integer verifyReachBillCount;
    private Integer notifyReachBillCount;
    private Integer marketReachBillCount;
    private Integer collectorReachBillCount;
    private Integer lightCollectorReachBillCount;
    private Integer severeCollectorReachBillCount;
    private Integer debtSwapReachBillCount;

    private Double verifyReachRate;
    private Double notifyReachRate;
    private Double marketReachRate;
    private Double collectorReachRate;
    private Double lightCollectorReachRate;
    private Double severeCollectorReachRate;
    private Double debtSwapReachRate;

    /**
     * 总触达量
     */
    private Integer totalReachCount;

    /**
     *  平均触达率
     */
    private Double avgReachRate;


    /**
     * 总计费条数量
     */
    private Integer totalReachBillCount;
}
