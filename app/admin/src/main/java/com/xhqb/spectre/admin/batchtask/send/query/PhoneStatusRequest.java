package com.xhqb.spectre.admin.batchtask.send.query;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.batchtask.send.MessageRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 手机内容发送状态请求
 *
 * <AUTHOR>
 * @date 2022/1/12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PhoneStatusRequest extends MessageRequest {

    /**
     * 待查询的手机号码，多个手机号码已逗号分割
     */
    private String phoneNumbers;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
