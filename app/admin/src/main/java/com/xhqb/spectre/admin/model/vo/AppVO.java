package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.AppDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/21 17:21
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppVO implements Serializable {

    private static final long serialVersionUID = 1956084078890609472L;

    private Integer id;

    private String code;

    private String name;

    private String description;

    private String skey;

//    private String cbUrl;

    /**
     * 短信内容API类型，0：不支持；1：支持；2：支持并且免模板检测
     */
    private Integer contentApiType;

    private String createTime;

    private String creator;

    private String updateTime;

    private String updater;

    public static AppVO buildAppVO(AppDO appDO) {
        return AppVO.builder()
                .id(appDO.getId())
                .code(appDO.getCode())
                .name(appDO.getName())
                .description(appDO.getDescription())
                .skey(appDO.getSkey())
//                .cbUrl(appDO.getCbUrl())
                .contentApiType(appDO.getContentApiType())
                .createTime(DateUtil.dateToString(appDO.getCreateTime()))
                .creator(appDO.getCreator())
                .updateTime(DateUtil.dateToString(appDO.getUpdateTime()))
                .updater(appDO.getUpdater())
                .build();
    }
}
