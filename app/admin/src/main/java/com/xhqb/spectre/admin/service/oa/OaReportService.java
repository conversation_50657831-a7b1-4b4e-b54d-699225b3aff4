package com.xhqb.spectre.admin.service.oa;

import com.xhqb.spectre.admin.model.dto.OaReportDto;
import com.xhqb.spectre.admin.model.dto.OaUpdateReportDto;
import com.xhqb.spectre.admin.model.vo.InExportVO;
import com.xhqb.spectre.admin.model.vo.OutExportVO;
import com.xhqb.spectre.admin.model.vo.ReportVO;
import com.xhqb.spectre.admin.service.oa.vo.FlowDataAllInfo;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.OaReportQuery;

import java.util.List;

public interface OaReportService {
    String saveOaInReport(OaReportDto oaReportDto);

    String saveOaOutReport(OaReportDto oaReportDto);

    String submitOaInReport(OaReportDto oaReportDto);

    String submitOaOutReport(OaReportDto oaReportDto);

    String batchReportOaOutReport(List<String> contentList);

    String batchReportOaInReport(List<String> contentList);

    CommonPager<ReportVO> page(OaReportQuery oaReportQuery);

    String update(OaUpdateReportDto oaUpdateReportDto);

    List<FlowDataAllInfo> approve(String contentId);

    String syncTpl(String contentId);

    String delete(String contentId);

    ReportVO detail(String contentId);

    /**
     * 下载报备数据
     * @param oaReportQuery
     * @return
     */
    List<InExportVO> inExport(OaReportQuery oaReportQuery);

    List<OutExportVO> outExport(OaReportQuery oaReportQuery);
}
