package com.xhqb.spectre.admin.batchtask.upload.impl;

import com.xhqb.spectre.admin.batchtask.upload.UploadContext;
import com.xhqb.spectre.admin.batchtask.upload.UploadHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * 本地环境上传处理器
 * <p>
 * 本地环境无法连上cos服务器 所以本地文件上传只是生成一个随机名称
 *
 * <AUTHOR>
 * @date 2021/9/22
 */
@Slf4j
public class LocalUploadHandler implements UploadHandler {

    /**
     * 文件上传处理
     *
     * @param context
     * @return 返回文件上传地址
     */
    @Override
    public String handler(UploadContext context) {
        log.info("当前使用的是本地文件上传处理器 = {}", context.getFileName());
        return System.currentTimeMillis() + "_" + context.getFileName();
    }
}
