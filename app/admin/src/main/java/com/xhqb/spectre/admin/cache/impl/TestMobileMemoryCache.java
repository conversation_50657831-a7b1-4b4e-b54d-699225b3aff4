package com.xhqb.spectre.admin.cache.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.admin.cache.AbstractMemoryCache;
import com.xhqb.spectre.common.dal.entity.test.TestMobileDO;
import com.xhqb.spectre.common.dal.mapper.TestMobileMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TestMobileMemoryCache extends AbstractMemoryCache<TestMobileDO> {


    // key type value List<mobile>
    private static final Map<Integer, List<String>> typeMap = new HashMap<>();
    private static final Map<String, String> paramMap = new HashMap<>();


    @Resource
    private TestMobileMapper testMobileMapper;

    @Override
    protected List<TestMobileDO> loadCache() {
        log.info("TestMobileDO :load cache");
        List<TestMobileDO> mobileDOList = testMobileMapper.selectAll();
        if (CollectionUtils.isNotEmpty(mobileDOList)) {
            Map<Integer, List<String>> listMap = mobileDOList.stream().collect(Collectors.groupingBy(TestMobileDO::getType, Collectors.mapping(TestMobileDO::getMobile, Collectors.toList())));
            mobileDOList.forEach(mobileDO -> paramMap.put(mobileDO.getMobile(), mobileDO.getParam()));
            typeMap.putAll(listMap);
        }
        return mobileDOList;
    }

    @Override
    protected String tableName() {
        return "t_test_mobile";
    }


    public Map<Integer, List<String>> getTypeMap() {
        return new HashMap<>(typeMap);
    }


    public Map<String, String> getParamMap() {
        return new HashMap<>(paramMap);
    }

    public List<String> getValues(Integer type) {
        return typeMap.get(type);
    }

    public List<String> getAllValues() {
        return typeMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
    }

    public Integer getType(String mobile) {
        for (Map.Entry<Integer, List<String>> entry : typeMap.entrySet()) {
            if (entry.getValue().contains(mobile)) {
                return entry.getKey();
            }
        }
        return null;
    }

    public Map<String,List<String>> paramList(List<String> mobileList) {
        Map<String,List<String>> resultMap = new HashMap<>();
        mobileList.forEach(mobile -> {
            String params = paramMap.get(mobile);
            if(StringUtils.isNotEmpty(params)) {
                JSONObject jsonObject = JSON.parseObject(params);
                jsonObject.forEach((key,value) -> {
                    List<String> valueList = resultMap.get(key);
                    if (CollectionUtils.isEmpty(valueList)) {
                        valueList = new ArrayList<>();
                    }
                    valueList.add((String) value);
                    resultMap.put(key,valueList);
                });
            }
        });
        return resultMap;
    }

}
