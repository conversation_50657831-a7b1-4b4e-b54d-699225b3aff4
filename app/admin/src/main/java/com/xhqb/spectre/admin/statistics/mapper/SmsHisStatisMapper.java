package com.xhqb.spectre.admin.statistics.mapper;


import com.xhqb.spectre.admin.statistics.entity.SmsHisStatisDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SmsHisStatisMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_his_statis
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(String code);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_his_statis
     *
     * @mbggenerated
     */
    int insert(SmsHisStatisDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_his_statis
     *
     * @mbggenerated
     */
    int insertSelective(SmsHisStatisDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_his_statis
     *
     * @mbggenerated
     */
    SmsHisStatisDO selectByPrimaryKey(String code);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_his_statis
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SmsHisStatisDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_his_statis
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SmsHisStatisDO record);

    List<SmsHisStatisDO> selectAll();
}