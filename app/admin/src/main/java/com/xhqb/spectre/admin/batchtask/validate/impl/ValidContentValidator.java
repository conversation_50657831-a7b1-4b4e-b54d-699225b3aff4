package com.xhqb.spectre.admin.batchtask.validate.impl;

import com.xhqb.spectre.admin.batchtask.aggregator.impl.CidDataAggregator;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.parse.ContentItem;
import com.xhqb.spectre.admin.batchtask.validate.ContentValidator;
import com.xhqb.spectre.admin.batchtask.validate.ValidateContext;
import com.xhqb.spectre.admin.batchtask.validate.ValidateResult;
import com.xhqb.spectre.admin.service.CifCustomerBaseService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 有效性判断：CID的有效性、手机只判断格式
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
@Component
public class ValidContentValidator implements ContentValidator {

    /**
     * 手机号码长度
     */
    private static final int MOBILE_LENGTH = 11;

    /**
     * 手机号码正则表达式
     * ^(1[3-9]\d{9}$)
     */
    @Value("${spectre.admin.mobileRegex:^(1[3-9]\\d{9}$)}")
    private String mobileRegex;
    /**
     * 手机正则表达式
     */
    private Pattern mobilePattern;

    @Autowired
    private CifCustomerBaseService cifCustomerBaseService;
    @Resource
    private CidDataAggregator cidDataAggregator;

    /**
     * 初始化验证必要资源
     */
    @PostConstruct
    public void init() {
        mobilePattern = Pattern.compile(mobileRegex);
    }

    /**
     * 数据验证
     *
     * @param validateContext
     * @param validateResult
     */
    @Override
    public void validate(ValidateContext validateContext, ValidateResult validateResult) {
        // 设置参与验证的总记录数
        validateResult.setTotal(validateContext.getContentItemList().size());
        final String contentType = validateContext.getContentType();
        if (StringUtils.equalsIgnoreCase(contentType, BatchTaskConstants.DataType.CID)) {
            // CID校验
            // 设置了无效数据和有效待验证数据
            cidDataAggregator.aggregate(validateContext, validateResult);
            return;
        }

        // 无效的数据
        List<ContentItem> badList = new ArrayList<>();
        // 过滤之后有效的数据
        List<ContentItem> validateList = validateContext.getValidateList().stream().filter(s -> {
            if (!doValidate(contentType, s, validateResult)) {
                // 无效
                badList.add(s);
                return false;
            }
            // 有效
            return true;
        }).collect(Collectors.toList());
        // 绑定结果数据信息
        // 设置无效数据
        validateResult.setBadList(badList);
        // 设置上下文待验证的数据
        validateContext.setValidateList(validateList);
    }

    /**
     * 做数据有效性验证
     *
     * @param contentType
     * @param contentItem
     * @param validateResult
     * @return
     */
    private boolean doValidate(String contentType, ContentItem contentItem, ValidateResult validateResult) {
//        if (StringUtils.equalsIgnoreCase(contentType, BatchTaskConstants.DataType.CID)) {
//            return doCidValidate(contentItem, validateResult, null);
//        }
        if (StringUtils.equalsIgnoreCase(contentType, BatchTaskConstants.DataType.MOBILE)) {
            return doMobileValidate(contentItem);
        }
        return false;
    }

    /**
     * 做手机号校验
     *
     * @param contentItem
     * @return true表示数据校验通过
     */
    private boolean doMobileValidate(ContentItem contentItem) {
        String mobile = contentItem.getContent();
        if (StringUtils.isBlank(mobile) || !Objects.equals(mobile.length(), MOBILE_LENGTH)) {
            // 手机号码为空 或者手机号码长度不为11位
            // 手机号码非法
            return false;
        }
        // 正则表达式验证手机号码是否正确
        return mobilePattern.matcher(mobile).matches();
    }

    @Override
    public int getOrder() {
        return BatchTaskConstants.ValidatorOrdered.VALID;
    }

}
