package com.xhqb.spectre.admin.batchtask.strategy.impl;

import com.xhqb.spectre.admin.batchtask.codec.FileMd5Codec;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.parse.ParseContext;
import com.xhqb.spectre.admin.batchtask.strategy.LoadStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

/**
 * URL文件加载策略
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
@Component
@Slf4j
public class UrlLoadStrategy implements LoadStrategy<String> {

    private static final String HTTP_STR = "http";

    /**
     * 策略名称 用于策略加载
     *
     * @return
     */
    @Override
    public String named() {
        return BatchTaskConstants.StrategyNamed.URL_STRATEGY_NAME;
    }

    /**
     * 加载文件策略
     *
     * @param request 一个文件地址
     * @return
     * @throws IOException
     */
    @Override
    public ParseContext load(String request) throws IOException {
        URL url = new URL(request);
        InputStream inputStream = url.openStream();
        String fileName = request.substring(request.lastIndexOf('/') + 1);
        return new ParseContext(fileName, inputStream, request, null);
    }
}
