package com.xhqb.spectre.admin.bidata.mapper;

import com.xhqb.spectre.admin.bidata.entity.ReachRateDO;
import com.xhqb.spectre.admin.bidata.model.SendCountTopDO;
import com.xhqb.spectre.admin.bidata.model.SendIspCodeDataDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * bidata t_sms_platform_send_stat 处理
 *
 * <AUTHOR>
 * @date 2025/07/23
 */
public interface BidataPlatformSendStatMapper {

    List<ReachRateDO> selectReachRateByTimeAndType(@Param("startDate") String startDate,
                                                   @Param("smsTypeCodes") List<String> smsType);

    List<String> selectTplCodeByDate(@Param("t1Date") String t1Date,@Param("topN") int topN);

    /**
     * 根据创建者的模板编码获取TOP发送量数据
     * @param tplCodeList 模板编码列表
     * @param t1Date T-1日期
     * @param topN TOP数量
     * @return TOP发送量模板编码列表
     */
    List<String> selectTopTplCodesByCreatorTplCodes(@Param("tplCodeList") List<String> tplCodeList, @Param("t1Date") String t1Date, @Param("topN") int topN);


    List<SendCountTopDO> selectSendCountTopByTplCodes(@Param("tplCodes") List<String> tplCodeList, @Param("t3Date") String t3Date);

    List<SendIspCodeDataDO> selectDataByTimeAndIspCode(@Param("startDate") Date startDate,
                                                       @Param("endDate") Date endDate);



}
