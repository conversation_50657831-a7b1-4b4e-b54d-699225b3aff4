package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 大数据批量任务处理
 *
 * <AUTHOR>
 * @date 2021/10/28
 */
@Data
public class BatchTaskBigdataDTO implements Serializable {

    /**
     * 业务应用CODE
     */
    @NotEmpty(message = "业务应用编码不能够为空")
    private String appCode;

    /**
     * 模板ID
     */
    @NotNull(message = "模板ID不能够为空")
    @Min(value = 1, message = "模板ID不合法")
    private Integer tplId;

    /**
     * 文件url地址
     */
    @NotEmpty(message = "文件地址不能够为空")
    private String fileUrl;

    /**
     * 状态 ，0：待处理；1：已处理；2：处理失败；3：处理中 ；4：已废弃(删除) ；5：已超时(taskNo查询一直处于处理中)
     */
    private Integer status;

    /**
     * 设定发送时间(unix时间戳,单位秒),设置为0表示立即发送
     */
    private Date sendTime;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 任务编号，由批量任务手动写入
     */
    private String taskNo;

    /**
     * 群发任务批次号，由批次任务写入
     */
    private Integer taskId;


}
