package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.model.vo.AutoTestTaskVO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskVO;
import com.xhqb.spectre.admin.service.AutoTestTaskService;
import com.xhqb.spectre.admin.service.test.tool.TestContentTaskService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.AutoTestBindDO;
import com.xhqb.spectre.common.dal.entity.AutoTestTaskDO;
import com.xhqb.spectre.common.dal.mapper.AutoTestBindMapper;
import com.xhqb.spectre.common.dal.mapper.AutoTestTaskMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.AutoTestOverrideQuery;
import com.xhqb.spectre.common.dal.query.AutoTestTaskQuery;
import com.xhqb.spectre.common.dal.query.TestContentTaskQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AutoTestTaskServiceImpl implements AutoTestTaskService {

    @Resource
    private AutoTestTaskMapper autoTestTaskMapper;
    @Resource
    private AutoTestBindMapper autoTestBindMapper;
    @Resource
    private TestContentTaskService testContentTaskService;

    @Override
    public CommonPager<AutoTestTaskDO> list(AutoTestTaskQuery query) {
        Integer count = this.autoTestTaskMapper.countByQuery(query);
        List<AutoTestTaskDO> entities = this.autoTestTaskMapper.selectByQuery(query);
        entities.forEach(item -> item.setInvokeCount(this.autoTestBindMapper.countByAutoTestTaskId(item.getId())));
        return new CommonPager<>(count, entities);
    }

    @Override
    public AutoTestTaskDO detail(Long id) {
        return this.autoTestTaskMapper.selectById(id);
    }

    @Override
    public void add(AutoTestTaskVO vo) {
        AutoTestTaskDO entity = this.transModel(vo, true);
        this.autoTestTaskMapper.insert(entity);
    }

    @Override
    public void edit(AutoTestTaskVO vo) {
        Assert.notNull(vo.getId(), "id can not be null");
        AutoTestTaskDO entity = this.transModel(vo, false);
        this.autoTestTaskMapper.updateById(entity);
    }

    @Override
    public CommonPager<TestContentTaskVO> override(AutoTestOverrideQuery query) {
        Assert.notNull(query.getAutoTestTaskId(), "autoTestTaskId can not be null");
        List<AutoTestBindDO> autoTestBindDOS = this.autoTestBindMapper.selectByAutoTestTaskId(query.getAutoTestTaskId());
        List<String> list = autoTestBindDOS.stream().map(AutoTestBindDO::getTestContentTaskId).collect(Collectors.toList());

        TestContentTaskVO total = new TestContentTaskVO();

        if (list.isEmpty()) {
            total.setReachRate(0.0);
            total.setAppReportRate(0.0);
            return new CommonPager<>(0, Collections.singletonList(total));
        }

        TestContentTaskQuery testContentTaskQuery = new TestContentTaskQuery();
        testContentTaskQuery.setTaskIdList(list);

        CommonPager<TestContentTaskVO> contentTaskPage = this.testContentTaskService.listByPage(testContentTaskQuery);
        List<TestContentTaskVO> dataList = contentTaskPage.getDataList();
        if (Objects.nonNull(dataList) && !dataList.isEmpty()) {
            OptionalDouble reachRateAverage = dataList.stream().mapToDouble(TestContentTaskVO::getReachRate).average();
            OptionalDouble reportRateAverage = dataList.stream().mapToDouble(TestContentTaskVO::getAppReportRate).average();
            total.setReachRate(reachRateAverage.orElse(0.0));
            total.setAppReportRate(reportRateAverage.orElse(0.0));
        }
        BeanUtils.copyProperties(query, testContentTaskQuery);
        testContentTaskQuery.setTaskIdList(list);

        Map<String, String> collect = autoTestBindDOS.stream().collect(Collectors.toMap(AutoTestBindDO::getTestContentTaskId, AutoTestBindDO::getTplCodes));

        CommonPager<TestContentTaskVO> page = this.testContentTaskService.listByPage(testContentTaskQuery);
        List<TestContentTaskVO> totalList = new ArrayList<>();
        totalList.add(total);
        totalList.addAll(page.getDataList());
        page.setDataList(totalList);
        totalList.forEach(item -> item.setTplCodes(collect.get(item.getTaskId())));
        return page;
    }

    private AutoTestTaskDO transModel(AutoTestTaskVO vo, Boolean isCreate) {
        Date now = new Date();

        AutoTestTaskDO ans = new AutoTestTaskDO();
        BeanUtils.copyProperties(vo, ans);

        ans.setUpdater(SsoUserInfoUtil.getUserName());
        ans.setUpdateTime(now);

        if (isCreate) {
            ans.setId(null);
            ans.setIsDelete(0);
            ans.setCreateTime(now);
            ans.setCreator(SsoUserInfoUtil.getUserName());
        }
        return ans;
    }
}
