package com.xhqb.spectre.admin.batchtask.strategy;

import lombok.Builder;
import lombok.Data;

import java.io.File;
import java.io.Serializable;

/**
 * 文件加载上下文信息
 * <p>
 * 主要控制是否需要进行文件上传处理
 *
 * <AUTHOR>
 * @date 2021/10/28
 */
@Data
@Builder
public class FileLoadContext implements Serializable {

    /**
     * 待处理的文件
     */
    private File file;
    /**
     * 是否过滤掉cos文件上传 true->是 false->否
     */
    private Boolean filterCos;
    /**
     * 上传文件前缀信息
     * 若未设置 那么则使用当前时间作为前缀
     * 主要是为了区分由哪个模块写入的数据
     */
    private String namePrefix;

    public FileLoadContext() {
    }

    public FileLoadContext(File file, Boolean filterCos) {
        this.file = file;
        this.filterCos = filterCos;
    }

    public FileLoadContext(File file, Boolean filterCos, String namePrefix) {
        this.file = file;
        this.filterCos = filterCos;
        this.namePrefix = namePrefix;
    }
}
