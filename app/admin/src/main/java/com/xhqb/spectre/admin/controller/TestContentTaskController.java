package com.xhqb.spectre.admin.controller;

import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.model.dto.TestContentTaskDTO;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.TestContentTaskDetailTypeVO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskDetailVO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskOverviewVO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskVO;
import com.xhqb.spectre.admin.service.test.tool.TestContentTaskService;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.TestContentTaskQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 测试文案
 */

@RestController
@RequestMapping("/testContentTask")
@Slf4j
public class TestContentTaskController {

    @Resource
    private TestContentTaskService testContentTaskService;

    /**
     * 分页查询测试文案任务列表
     *
     * @param testContentTaskQuery 包含查询条件的对象，用于过滤测试文案任务数据
     * @param pageNum              当前页码，表示要查询的页数
     * @param pageSize             每页显示的记录数，表示每页返回的数据条数
     * @return 返回分页后的测试文案任务数据，包含分页信息和数据列表
     */
    @GetMapping("/listByPage")
    public CommonResult<CommonPager<TestContentTaskVO>> listByPage(@ModelAttribute TestContentTaskQuery testContentTaskQuery, Integer pageNum, Integer pageSize) {
        testContentTaskQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        log.info("测试文案任务分页查询参数 listByPage:{}", JsonLogUtil.toJSONString(testContentTaskQuery));
        return CommonResult.success(testContentTaskService.listByPage(testContentTaskQuery));
    }

    /**
     * 查看测试任务基础信息
     *
     * @param taskId 任务id
     * @return 测试任务基础信息
     */
    @GetMapping("/detail/{taskId}")
    public CommonResult<TestContentTaskVO> detail(@PathVariable("taskId") String taskId) {
        log.info("查看测试任务基础信息 detail:{}", taskId);
        return CommonResult.success(testContentTaskService.detail(taskId));
    }

    /**
     * 查看测试概览
     *
     * @param taskId 任务 id
     * @return 测试任务概览
     */
    @GetMapping("/overview/{taskId}")
    public CommonResult<TestContentTaskOverviewVO> overview(@PathVariable("taskId") String taskId) {
        log.info("查看测试任务概览信息 overview:{}", taskId);
        return CommonResult.success(testContentTaskService.overview(taskId));
    }

    /**
     * 查看测试品牌明细
     */
    @GetMapping("/detailBrand/{taskId}")
    public CommonResult<List<TestContentTaskDetailVO>> detailBrand(@PathVariable("taskId") String taskId) {
        log.info("查看测试任务品牌明细信息 detailBrand:{}", taskId);
        return CommonResult.success(testContentTaskService.detailBrand(taskId));
    }


    /**
     * 添加
     *
     * @param testContentTaskDTO 新增参数
     * @return 任务 Id
     */
    @PostMapping("/add")
    public CommonResult<String> add(@RequestBody TestContentTaskDTO testContentTaskDTO) {
        log.info("添加测试文案任务 add:{}", JsonLogUtil.toJSONString(testContentTaskDTO));
        ValidatorUtil.validate(testContentTaskDTO);
        return CommonResult.success(testContentTaskService.add(testContentTaskDTO));
    }

    /**
     * 编辑
     *
     * @param testContentTaskDTO 编辑的测试文案任务数据
     * @return 任务 id
     */
    @PostMapping("/update")
    public CommonResult<String> update(@RequestBody TestContentTaskDTO testContentTaskDTO) {
        log.info("编辑测试文案任务 update:{}", JsonLogUtil.toJSONString(testContentTaskDTO));
        ValidatorUtil.validate(testContentTaskDTO);
        return CommonResult.success(testContentTaskService.update(testContentTaskDTO));
    }

    /**
     * 执行任务
     *
     * @param taskId 任务 id
     * @return 执行结果
     */
    @PostMapping("/executeTask/{taskId}")
    public CommonResult<String> executeTask(@PathVariable("taskId") String taskId) {
        log.info("测试任务执行 taskId:{}", taskId);
        return CommonResult.success(testContentTaskService.executeTask(taskId));
    }

    /**
     * 刷新任务
     *
     * @param taskId 任务 id
     * @return 执行结果
     */
    @PostMapping("/refresh/{taskId}")
    public CommonResult<String> refresh(@PathVariable("taskId") String taskId) {
        log.info("刷新测试任务");
        testContentTaskService.updateTaskRecordReportStatus(taskId);
        return CommonResult.success();
    }

    /**
     * 取消任务
     */
    @PostMapping("/cancel/{taskId}")
    public CommonResult<String> cancel(@PathVariable("taskId") String taskId) {
        log.info("取消测试任务 taskId:{}", taskId);
        testContentTaskService.cancel(taskId);
        return CommonResult.success(taskId);
    }

    /**
     * 查看测试品牌明细分类table
     */
    @GetMapping("/detail/type/{taskId}")
    public CommonResult<TestContentTaskDetailTypeVO> detailType(@PathVariable("taskId") String taskId) {
        log.info("查看测试任务品牌明细分类 detailType｜taskId:{}", taskId);
        return CommonResult.success(testContentTaskService.detailType(taskId));
    }


}
