package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.SmsTypeDisableDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.SmsTypeDisableVO;
import com.xhqb.spectre.admin.service.SmsTypeDisableService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.SmsTypeDisableQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2022/2/17 14:02
 * @Description:
 */
@RestController
@RequestMapping("/smsTypeDisable")
@Slf4j
public class SmsTypeDisableController {

    @Autowired
    private SmsTypeDisableService smsTypeDisableService;

    /**
     * 列表查询
     *
     * @param query
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(@ModelAttribute SmsTypeDisableQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<SmsTypeDisableVO> commonPager = smsTypeDisableService.listByPage(query);
        return AdminResult.success(commonPager);
    }

    /**
     * 详情查询
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(smsTypeDisableService.getById(id));
    }

    /**
     * 创建
     *
     * @param smsTypeDisableDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_SMS_TYPE_DISABLE)
    public AdminResult create(@RequestBody SmsTypeDisableDTO smsTypeDisableDTO) {
        log.info("create smsTypeDisable, smsTypeDisableDTO: {}", JSON.toJSONString(smsTypeDisableDTO));
        smsTypeDisableService.create(smsTypeDisableDTO);
        return AdminResult.success();
    }

    /**
     * 更新
     *
     * @param id
     * @param smsTypeDisableDTO
     * @return
     */
    @PutMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_SMS_TYPE_DISABLE)
    public AdminResult update(@PathVariable("id") Integer id, @RequestBody SmsTypeDisableDTO smsTypeDisableDTO) {
        smsTypeDisableDTO.setId(id);
        log.info("update smsTypeDisable, smsTypeDisableDTO: {}", JSON.toJSONString(smsTypeDisableDTO));
        smsTypeDisableService.update(smsTypeDisableDTO);
        return AdminResult.success();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    @PostMapping("/batchDelete")
    @LogOpTime(OpLogConstant.MODULE_SMS_TYPE_DISABLE)
    public AdminResult batchDelete(@RequestBody List<Integer> idList) {
        log.info("batchDelete smsTypeDisable, idList: {}", JSON.toJSONString(idList));
        smsTypeDisableService.batchDelete(idList);
        return AdminResult.success();
    }
}
