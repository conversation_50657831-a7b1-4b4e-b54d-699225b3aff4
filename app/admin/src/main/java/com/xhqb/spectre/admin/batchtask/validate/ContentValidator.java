package com.xhqb.spectre.admin.batchtask.validate;

import org.springframework.core.Ordered;

/**
 * 群发短信任务上传内容验证器
 * 有效性判断：CID的有效性、手机只判断格式
 * 参数漏填：过滤漏填参数条目
 * 去重判断：手机号或CID的去重
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
public interface ContentValidator extends Ordered {

    /**
     * 数据验证
     *
     * @param validateContext
     * @param validateResult
     */
    void validate(ValidateContext validateContext, ValidateResult validateResult);
}
