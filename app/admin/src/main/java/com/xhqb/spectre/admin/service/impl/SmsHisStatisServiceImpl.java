package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.model.vo.SmsHisStatisVO;
import com.xhqb.spectre.admin.service.SmsHisStatisService;
import com.xhqb.spectre.common.dal.mapper.SmsHisStatisOldMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.SmsHisStatisQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * 短信历史发送量统计表
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
@Service
@Slf4j
public class SmsHisStatisServiceImpl implements SmsHisStatisService {

    @Resource
    private SmsHisStatisOldMapper smsHisStatisOldMapper;

    /**
     * 分页查询短信历史发送量统计列表
     *
     * @param smsHisStatisQuery
     * @return
     */
    @Override
    public CommonPager<SmsHisStatisVO> listByPage(SmsHisStatisQuery smsHisStatisQuery) {
        return PageResultUtils.result(
                () -> smsHisStatisOldMapper.countByQuery(smsHisStatisQuery),
                () -> smsHisStatisOldMapper.selectByQuery(smsHisStatisQuery).stream().map(SmsHisStatisVO::buildListQuery).collect(Collectors.toList())
        );
    }
}
