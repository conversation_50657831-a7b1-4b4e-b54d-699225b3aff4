package com.xhqb.spectre.admin.mq.consumer.handler;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.model.vo.batchtask.ParamItemVO;
import com.xhqb.spectre.admin.mq.consumer.ConsumerHandler;
import com.xhqb.spectre.admin.mq.event.BatchTaskParamEvent;
import com.xhqb.spectre.admin.mq.event.TaskParamResult;
import com.xhqb.spectre.admin.mq.event.listener.BatchTaskParamEventListener;
import com.xhqb.spectre.admin.mq.message.BatchTaskParamMessage;
import com.xhqb.spectre.admin.mq.message.BatchTaskSubmitMessage;
import com.xhqb.spectre.admin.mq.producer.impl.BatchTaskParamProducer;
import com.xhqb.spectre.admin.mq.send.SenderContext;
import com.xhqb.spectre.admin.service.BatchTaskLogService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.*;
import com.xhqb.spectre.common.dal.mapper.BatchTaskMapper;
import com.xhqb.spectre.common.dal.mapper.BatchTaskParamMapper;
import com.xhqb.spectre.common.dal.mapper.SignMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import com.xhqb.spectre.common.enums.BatchTaskParamStatusEnum;
import com.xhqb.spectre.common.enums.BatchTaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 群发任务提交消费处理器
 * 1. 需要验证任务状态是否属于已提交状态,否则不进行相关处理
 * 2. 查询当前群发任务下所有未删除并且状态为未发送的数据信息
 * 3. 更新群发任务状态为发送中
 * 3. 每一条 t_batch_task_param记录产生一条 spectre-send-deliver 消息
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Component
@Slf4j
public class BatchTaskSubmitConsumerChandler implements ConsumerHandler {

    @Resource
    private BatchTaskMapper batchTaskMapper;
    @Resource
    private BatchTaskParamMapper batchTaskParamMapper;
    @Resource
    private BatchTaskParamProducer batchTaskParamProducer;
    @Resource
    private BatchTaskParamEventListener batchTaskParamEventListener;
    @Resource
    private TplMapper tplMapper;
    @Resource
    private SignMapper signMapper;
    @Resource
    private BatchTaskLogService batchTaskLogService;

    /**
     * 消费消息处理
     *
     * @param message
     */
    @Override
    public void handler(String message) {
        long start = System.currentTimeMillis();
        log.info("群发任务提交消费处理器收到消息 = {}", message);
        BatchTaskSubmitMessage batchTaskSubmitMessage = JSON.parseObject(message, BatchTaskSubmitMessage.class);
        BatchTaskDO batchTaskDO = this.checkBatchTaskStatus(batchTaskSubmitMessage);
        if (Objects.isNull(batchTaskDO)) {
            // 群发任务验证失败 忽略该消息
            return;
        }

        TplDO tplDO = this.checkTplStatus(batchTaskSubmitMessage, batchTaskDO);
        if (Objects.isNull(tplDO)) {
            // 模板验证失败 忽略该消息
            return;
        }
        SignDO signDO = this.checkSignStatus(batchTaskSubmitMessage, batchTaskDO);
        if (Objects.isNull(signDO)) {
            // 签名验证失败 忽略该消息
            return;
        }

        List<BatchTaskParamDO> taskParamList = this.getAllTaskParamByTaskId(batchTaskSubmitMessage);
        if (Objects.isNull(taskParamList)) {
            // 没查询到群发任务参数 忽略该消息
            return;
        }

        // 更新群发任务状态为发送中
        if (!this.updateBatchTaskStatus(batchTaskSubmitMessage, batchTaskDO)) {
            // 更新群发任务状态失败
            return;
        }

        // 将待处理的任务数量写入缓存
        batchTaskParamEventListener.writeTaskCountToCache(batchTaskSubmitMessage.getId(), taskParamList.size());

        SenderContext<BatchTaskParamMessage> senderContext;
        // 产生群发任务参数消息
        BatchTaskParamMessage batchTaskParamMessage;
        for (BatchTaskParamDO taskParamDO : taskParamList) {
            batchTaskParamMessage = BatchTaskParamMessage.builder()
                    .taskId(batchTaskSubmitMessage.getId())
                    .taskParamId(taskParamDO.getId())
                    .appCode(batchTaskDO.getAppCode())
                    .tplCode(tplDO.getCode())
                    .signName(signDO.getName())
                    // 设置签名编码
                    .signCode(signDO.getCode())
                    .smsCodeType(tplDO.getSmsTypeCode())
                    // 设置模板内容类型 2022-02-22
                    .tplType(batchTaskDO.getTplType())
                    .build();

            senderContext = new SenderContext<>(batchTaskParamMessage);
            // 发送消息
            try {
                batchTaskParamProducer.send(senderContext);
            } catch (Exception e) {
                log.error("群发任务发送单个消息失败,batchTaskParamMessage = {}", batchTaskParamMessage, e);
                // 保存群发日志
                saveBatchTaskLog(batchTaskParamMessage);
                // 发送失败通知
                batchTaskParamEventListener.publish(new BatchTaskParamEvent(batchTaskParamMessage, new TaskParamResult(0, 0)));
            }
        }
        log.info("群发任务提交消费处理器处理完成,batchTaskSubmitMessage = {},耗时 = {}", message, (System.currentTimeMillis() - start));
    }

    /**
     * 验证群发任务状态是否为已提交状态
     *
     * @param batchTaskSubmitMessage
     * @return 返回验证通过的群发任务信息，否则返回null
     */
    private BatchTaskDO checkBatchTaskStatus(BatchTaskSubmitMessage batchTaskSubmitMessage) {
        BatchTaskDO batchTaskDO = batchTaskMapper.selectByPrimaryKey(batchTaskSubmitMessage.getId());
        if (Objects.isNull(batchTaskDO)) {
            log.warn("群发任务不存在, batchTaskSubmitMessage = {}", batchTaskSubmitMessage);
            return null;
        }

        if (!Objects.equals(batchTaskDO.getStatus(), BatchTaskStatusEnum.COMMITTED.getCode())) {
            log.warn("群发任务状态未是已提交状态,status = {}, batchTaskSubmitMessage = {}", batchTaskDO.getStatus(), batchTaskSubmitMessage);
            return null;
        }
        // 群发任务状态正常
        return batchTaskDO;
    }

    /**
     * 验证消息模板状态
     *
     * @param batchTaskSubmitMessage
     * @param batchTaskDO
     * @return 返回验证通过的模板对象，否则返回null
     */
    private TplDO checkTplStatus(BatchTaskSubmitMessage batchTaskSubmitMessage, BatchTaskDO batchTaskDO) {
        TplDO tplDO = tplMapper.selectByPrimaryKey(batchTaskDO.getTplId());
        if (Objects.isNull(tplDO)) {
            log.warn("当前群发任务的模板信息不存在 = {},tplId = {}", batchTaskSubmitMessage, batchTaskDO.getTplId());
            return null;
        }

        if (!Objects.equals(CommonConstant.STATUS_VALID, tplDO.getStatus())) {
            log.warn("当前群发任务的模板状态无效,status = {},batchTaskSubmitMessage = {},tplId = {}", tplDO.getStatus(), batchTaskSubmitMessage, batchTaskDO.getTplId());
            return null;
        }
        // 状态正常
        return tplDO;
    }

    /**
     * 验证签名状态
     *
     * @param batchTaskSubmitMessage
     * @param batchTaskDO
     * @return 返回验证通过的签名对象，否则返回null
     */
    private SignDO checkSignStatus(BatchTaskSubmitMessage batchTaskSubmitMessage, BatchTaskDO batchTaskDO) {
        SignDO signDO = signMapper.selectByPrimaryKey(batchTaskDO.getSignId());
        if (Objects.isNull(signDO)) {
            log.warn("当前群发任务的签名信息不存在 = {},signId = {}", batchTaskSubmitMessage, batchTaskDO.getSignId());
            return null;
        }
        if (!Objects.equals(CommonConstant.STATUS_VALID, signDO.getStatus())) {
            log.warn("当前群发任务的签名状态无效,status = {},batchTaskParamMessage = {},signId = {}", signDO.getStatus(), batchTaskSubmitMessage, batchTaskDO.getSignId());
            return null;
        }
        // 状态正常
        return signDO;
    }

    /**
     * 根据任务ID 查询所有状态为未发送的状态
     *
     * @param batchTaskSubmitMessage
     * @return
     */
    private List<BatchTaskParamDO> getAllTaskParamByTaskId(BatchTaskSubmitMessage batchTaskSubmitMessage) {
        // 查询所有状态为未发送的状态
        // 该查询不会将paramArray大字段数据信息查询出来
        List<BatchTaskParamDO> batchTaskParamList = batchTaskParamMapper.selectByTaskIdAndStatus(batchTaskSubmitMessage.getId(), BatchTaskParamStatusEnum.UN_SEND.getCode(), null);
        if (Objects.isNull(batchTaskParamList) || batchTaskParamList.isEmpty()) {
            log.warn("未查询到群发任务参数列表,batchTaskSubmitMessage = {}", batchTaskSubmitMessage);
            return null;
        }
        // 返回查询到的批量参数列表
        return batchTaskParamList;
    }

    /**
     * 更新群发任务状态为发送中，并设置当前时间为开始发送时间
     *
     * @param batchTaskSubmitMessage
     * @param batchTaskDO
     * @return
     */
    private boolean updateBatchTaskStatus(BatchTaskSubmitMessage batchTaskSubmitMessage, BatchTaskDO batchTaskDO) {
        BatchTaskDO updateResult = new BatchTaskDO();
        updateResult.setId(batchTaskSubmitMessage.getId());
        updateResult.setStatus(BatchTaskStatusEnum.SENDING.getCode());
        // 开始发送的时间
        updateResult.setSendStartTime(DateUtil.getNow());
        updateResult.setVersion(batchTaskDO.getVersion());
        int affectedRows = batchTaskMapper.updateByStatusWithId(updateResult.getId(), updateResult.getStatus(), updateResult.getSendStartTime(), batchTaskDO.getStatus());
        if (affectedRows <= 0) {
            log.warn("更新群发任务状态为发送中失败,batchTaskSubmitMessage = {}, updateResult = {}", batchTaskSubmitMessage, JSON.toJSONString(updateResult));
            return false;
        }
        return true;
    }

    /**
     * 保存群发日志
     *
     * @param batchTaskParamMessage
     */
    private void saveBatchTaskLog(BatchTaskParamMessage batchTaskParamMessage) {
        try {
            doSaveBatchTaskLog(batchTaskParamMessage);
        } catch (Exception e) {
            log.warn("发送MQ失败保存群发日志失败,batchTaskParamMessage = {}", JSON.toJSONString(batchTaskParamMessage), e);
        }
    }

    /**
     * 保存群发日志
     *
     * @param batchTaskParamMessage
     */
    private void doSaveBatchTaskLog(BatchTaskParamMessage batchTaskParamMessage) {
        BatchTaskParamDO batchTaskParamDO = batchTaskParamMapper.selectByPrimaryKey(batchTaskParamMessage.getTaskParamId());
        long start = System.currentTimeMillis();
        String paramJsonArray = batchTaskParamDO.getParamJsonArray();
        List<ParamItemVO> paramItemList = JSON.parseArray(paramJsonArray, ParamItemVO.class);
        // 过滤掉包含sendSkip标记的数据 2021-11-24
        paramItemList = paramItemList.stream()
                .filter(s -> !StringUtils.equalsAnyIgnoreCase(s.get(BatchTaskConstants.DataType.SKIP_SEND), "true"))
                .collect(Collectors.toList());
        if (CommonUtil.isEmpty(paramItemList)) {
            // 没有数据 则不需要进行日志保存操作
            log.warn("发送MQ失败,当前群发参数数据为空,不保存群发日志信息 = {},batchTaskParamMessage ={}", paramJsonArray, JSON.toJSONString(batchTaskParamMessage));
            return;
        }

        // 群发日志列表
        List<BatchTaskLogDO> taskLogList = new ArrayList<>(paramItemList.size());

        BatchTaskLogDO batchTaskLogDO = null;
        String cid = null;
        String mobile = null;
        for (ParamItemVO paramItemVO : paramItemList) {
            cid = paramItemVO.get(BatchTaskConstants.DataType.CID);
            if (StringUtils.isBlank(cid)) {
                cid = "";
            }
            mobile = paramItemVO.get(BatchTaskConstants.DataType.MOBILE);
            if (StringUtils.isBlank(mobile)) {
                mobile = "";
            }
            batchTaskLogDO = BatchTaskLogDO.builder()
                    .taskId(batchTaskParamMessage.getTaskId())
                    .taskParamId(batchTaskParamMessage.getTaskParamId())
                    .cid(cid)
                    .mobile(mobile)
                    .param(JSON.toJSONString(paramItemVO))
                    .build();

            if (StringUtils.isBlank(mobile)) {
                // 手机号
                batchTaskLogDO.setStatus(BatchTaskConstants.TaskLog.STATUS_ERROR);
                batchTaskLogDO.setDescription("手机号码为空");
                batchTaskLogDO.setErrCode("e0000");
                taskLogList.add(batchTaskLogDO);
                continue;
            }
            batchTaskLogDO.setStatus(BatchTaskConstants.TaskLog.STATUS_ERROR);
            batchTaskLogDO.setDescription("发送MQ失败");
            batchTaskLogDO.setErrCode("e0001");
            taskLogList.add(batchTaskLogDO);
        }
        batchTaskLogService.batchInsert(taskLogList);
        log.info("发送MQ保存群发日志记录数据库耗时 = {},batchTaskParamMessage = {}", (System.currentTimeMillis() - start), JSON.toJSONString(batchTaskParamMessage));
    }
}
