package com.xhqb.spectre.admin.job;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.service.oa.OaBizService;


import com.xhqb.spectre.common.dal.entity.oa.TplOaApprove;
import com.xhqb.spectre.common.dal.mapper.TplContentMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import com.xhqb.spectre.common.dal.mapper.TplOaApproveMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 短信内容拉取审批记录更新状态和内容 job
 * elastic-job.jobs.tplContentApproveJob.cron=0 0/5 * * * ?
 * elastic-job.jobs.tplContentApproveJob.sharding-total-count=1
 * elastic-job.jobs.tplContentApproveJob.sharding-item-parameters=0=A
 * elastic-job.jobs.tplContentApproveJob.disabled=true
 * elastic-job.jobs.tplContentApproveJob.description=短信内容拉取审批记录更新状态和内容
 *
 * <AUTHOR>
 */

@Component
@Job("tplContentApproveJob")
@Slf4j
public class TplContentApproveJob implements SimpleJob {

    @Resource
    private OaBizService oaBizService;

    @Resource
    private TplOaApproveMapper tplOaApproveMapper;

    @Resource
    private TplMapper tplMapper;

    @Resource
    private TplContentMapper tplContentMapper;

    @Resource
    private TplContentApproveJob thisProxy;

    @Resource
    private VenusConfig venusConfig;


    @Override
    public void execute(ShardingContext shardingContext) {

        // 获取审批中的记录
        List<TplOaApprove> tplOaApproveList = tplOaApproveMapper.selectByStatusList(Collections.singletonList(1));

        if (CollectionUtil.isEmpty(tplOaApproveList)) {
            log.info("没有审核之中的模板内容");
            return;
        }

        if (venusConfig.isLogEnable()) {
            log.info("审批中的记录 flowIds:{}", JSON.toJSONString(tplOaApproveList.stream().map(TplOaApprove::getFlowId).collect(Collectors.toList())));
        }

        tplOaApproveList.forEach(tplOaApprove -> {
            String flowId = tplOaApprove.getFlowId();
            String userId = tplOaApprove.getUserId();
            // 判断是否归档
            boolean filedTag = false;
            try {
                filedTag = oaBizService.getFlowDataAllInfo(flowId, userId).stream()
                        .anyMatch(flowDataAllInfo -> Objects.equals(flowDataAllInfo.getIsRemark(), 4));
            } catch (Exception e) {
                log.error("flowId:{} | 获取审批记录失败", flowId, e);
            }
            if (filedTag) {
                thisProxy.updateApproveRecordAndTpl(flowId, userId);
            }

        });


    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "spectreTransactionManager")
    public void updateApproveRecordAndTpl(String flowId, String userId) {
        // 拉取归档短信内容
        Map<String, String> flowContentMap = oaBizService.getFlowContentMap(flowId, userId);

        if (CollectionUtil.isEmpty(flowContentMap)) {
            log.error("flowId:{} | 拉取归档短信内容为空", flowId);
            return;
        }

        if (venusConfig.isLogEnable()) {
            log.info("flowContentMap:{}", JSON.toJSONString(flowContentMap));
        }

        // 更新审批状态--- 报备记录状态 和 模板的审批状态
        tplOaApproveMapper.updateStatusByFlowId(flowId, 3);
        for (Map.Entry<String, String> entry : flowContentMap.entrySet()) {
            String contentId = entry.getKey();
            String content = entry.getValue();
            tplContentMapper.updateByContentId(contentId, content, 3);
            tplMapper.updateByContentId(contentId, content, 3);
        }

    }
}
