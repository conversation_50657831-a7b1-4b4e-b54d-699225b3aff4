package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


@Data
public class TplChannelWeightDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer channelAccountId;

    @NotNull(message = "权重不能为空")
    @Min(value = 0, message = "权重不能小于0")
    @Max(value = 100, message = "权重不能大于100")
    private Integer weight;
}
