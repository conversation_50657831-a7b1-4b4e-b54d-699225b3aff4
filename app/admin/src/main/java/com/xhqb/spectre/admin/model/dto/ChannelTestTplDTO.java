package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ChannelTestTplDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 渠道账号id
     */
    @NotNull(message = "渠道账号id不能为空")
    private Integer channelAccountId;

    /**
     * 测试模板名称
     */
    @NotBlank(message = "测试模板名称不能为空")
    private String name;

    /**
     * 短信模版id
     */
    private Integer smsTplId;

    /**
     * 参数 1,2,3
     */
    private String params;

    /**
     * 检测频次
     */
    private Integer checkTimes;

    /**
     * 号码个数
     */
    private Integer mobileCount;

    /**
     * 号码来源
     */
    private Integer source;

    /**
     * 号码来源为自定义 手机号
     */
    private String mobiles;

    /**
     * 启用状态(0:未启用 1:启用)
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 最大测试次数
     */
    @NotNull(message = "最大测试次数不能为空")
    private Integer maxTimes;

    /**
     * 测试时间段
     */
    @NotBlank(message = "测试时间段不能为空")
    private String timePeriod;

    /**
     * 测试号类型
     */
    private List<TypeWeightDTO> typeWeightDTOS;

    /**
     * 通知账号
     */
    @NotBlank(message = "通知账号不能为空")
    private String notifyAccount;

    /**
     * 短信类型
     */
    private String smsTypeCode;

    /**
     * 发送量级
     */
    private Integer senderLevel;
}
