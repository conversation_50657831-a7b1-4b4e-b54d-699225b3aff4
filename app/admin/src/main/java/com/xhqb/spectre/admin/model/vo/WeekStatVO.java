package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.MonthWeekUtils;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class WeekStatVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private String week;
    private String startDate;
    private String endDate;

    public static WeekStatVO build(String week) {

        MonthWeekUtils.DateRange range = MonthWeekUtils.getWeekStartAndEnd(week, MonthWeekUtils.MONTH_DAY_FORMAT);
        return WeekStatVO.builder()
                .week(week)
                .startDate(range.getStartDate())
                .endDate(range.getEndDate())
                .build();
    }
}
