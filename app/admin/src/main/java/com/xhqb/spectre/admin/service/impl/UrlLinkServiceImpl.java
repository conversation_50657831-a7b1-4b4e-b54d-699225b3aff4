package com.xhqb.spectre.admin.service.impl;

import com.xhqb.poseidon.starter.api.core.ActionResult;
import com.xhqb.poseidon.starter.api.core.push.ma.UrlLinkRequestDTO;
import com.xhqb.poseidon.starter.api.push.ma.WxUrlLinkService;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.ShortUrlDTO;
import com.xhqb.spectre.admin.model.dto.UpdateWxUrlLinkDTO;
import com.xhqb.spectre.admin.model.dto.WxUrlLinkDTO;
import com.xhqb.spectre.admin.model.vo.ShortUrlVO;
import com.xhqb.spectre.admin.model.vo.WxUrlLinkVO;
import com.xhqb.spectre.admin.service.ShortUrlService;
import com.xhqb.spectre.admin.service.UrlLinkService;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.WxUrlLinkDO;
import com.xhqb.spectre.common.dal.entity.WxUrlLinkFlowDO;
import com.xhqb.spectre.common.dal.mapper.WxUrlLinkFlowMapper;
import com.xhqb.spectre.common.dal.mapper.WxUrlLinkMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.WxUrlLinkQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UrlLinkServiceImpl implements UrlLinkService {

    @Autowired
    private WxUrlLinkService wxUrlLinkService;


    @Autowired
    private WxUrlLinkMapper wxUrlLinkMapper;

    @Autowired
    private WxUrlLinkFlowMapper wxUrlLinkFlowMapper;
    
    @Autowired
    private ShortUrlService shortUrlService;

    @Autowired
    private VenusConfig venusConfig;

    /**
     * 域名格式校验
     */
    public static final String URL_REGEX = "^(https?://)?([\\w.-]+)\\.([a-zA-Z]{2,})([/\\w.-]*)*(/?)$";

    /**
     * 匹配域名
     */
    private static final String DOMAIN_REGEX = "^(?:https?://)?(?:[^@/\\n]+@)?(?:www\\.)?([^:/?#]+)";

    /**
     *  匹配协议头，如http://或https://开头部分
     */
    private static final String PROTOCOL_REGEX = "^(https?://)";

    /**
     * 查询短链列表
     *
     * @param wxUrlLinkQuery
     * @return
     */
    @Override
    public CommonPager<WxUrlLinkVO> listByPage(WxUrlLinkQuery wxUrlLinkQuery) {
        try {
            if (StringUtils.isNotBlank(wxUrlLinkQuery.getId())) {
                Long.valueOf(wxUrlLinkQuery.getId());
            }
        } catch (NumberFormatException e) {
            return new CommonPager<>(0, Collections.emptyList());
        }

        return PageResultUtils.result(
                () -> wxUrlLinkMapper.countByQuery(wxUrlLinkQuery),
                () -> wxUrlLinkMapper.selectByQuery(wxUrlLinkQuery).stream()
                        .map(WxUrlLinkVO::buildUrlLinkVO).collect(Collectors.toList())
        );
    }

    /**
     * 查询短链详情
     *
     * @param id
     * @return
     */
    @Override
    public WxUrlLinkVO queryInfo(Long id) {
        WxUrlLinkDO wxUrlLinkDO = validateAndSelectById(id);
        return WxUrlLinkVO.buildUrlLinkVO(wxUrlLinkDO);
    }

    /**
     * 生成短链
     *
     * @param wxUrlLinkDTO
     * @return
     */
    @Override
    public void create(WxUrlLinkDTO wxUrlLinkDTO) {
        //校验
        ValidatorUtil.validate(wxUrlLinkDTO);
        Date generateDate = new Date();

        //生成短链编码
        String urlLink = generateShortUrl(wxUrlLinkDTO.getAppid(),
                wxUrlLinkDTO.getEnvVersion(),
                wxUrlLinkDTO.getPath(),
                wxUrlLinkDTO.getQuery());

        //保存
        String currentUser = SsoUserInfoUtil.getUserName();
        WxUrlLinkDO wxUrlLinkDO = WxUrlLinkDO.builder()
                .shortUrl("")
                .shortCode("")
                .urlLink(urlLink)
                .linkDesc(wxUrlLinkDTO.getLinkDesc())
                .appid(wxUrlLinkDTO.getAppid())
                .envVersion(wxUrlLinkDTO.getEnvVersion())
                .path(wxUrlLinkDTO.getPath())
                .query(wxUrlLinkDTO.getQuery())
                .status(1)
                .generateDate(generateDate)
                .creator(currentUser)
                .updater(currentUser)
                .build();
        wxUrlLinkMapper.insertSelective(wxUrlLinkDO);
        WxUrlLinkFlowDO flowDO = WxUrlLinkFlowDO.builder()
                .wxUrlLinkId(wxUrlLinkDO.getId())
                .urlLink(urlLink)
                .generateDate(generateDate)
                .build();
        wxUrlLinkFlowMapper.insertSelective(flowDO);
    }

    /**
     * 更新短链
     *
     * @param id
     * @param updateWxUrlLinkDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Long id, UpdateWxUrlLinkDTO updateWxUrlLinkDTO) {
        WxUrlLinkDO exist = validateAndSelectById(id);

        //校验
        ValidatorUtil.validate(updateWxUrlLinkDTO);

        // 更新短链信息
        WxUrlLinkDO updateItem = WxUrlLinkDO.builder()
                .id(id)
                .updater(SsoUserInfoUtil.getUserName())
                .build();

        if (StringUtils.isNotBlank(updateWxUrlLinkDTO.getLinkDesc())) {
            updateItem.setLinkDesc(updateWxUrlLinkDTO.getLinkDesc());
        }

        if (!exist.getPath().equals(updateWxUrlLinkDTO.getPath())
                || !exist.getQuery().equals(updateWxUrlLinkDTO.getQuery())) {
            Date generateDate = new Date();

            //生成短链编码

            String urlLink = generateShortUrl(exist.getAppid(), exist.getEnvVersion(),
                    updateWxUrlLinkDTO.getPath(), updateWxUrlLinkDTO.getQuery());
            updateItem.setPath(updateWxUrlLinkDTO.getPath());
            updateItem.setQuery(updateWxUrlLinkDTO.getQuery());
            updateItem.setUrlLink(urlLink);
            updateItem.setGenerateDate(generateDate);

            WxUrlLinkFlowDO flowDO = WxUrlLinkFlowDO.builder()
                    .wxUrlLinkId(updateItem.getId())
                    .urlLink(urlLink)
                    .generateDate(generateDate)
                    .build();
            wxUrlLinkFlowMapper.insertSelective(flowDO);

            //更新短链的源链接
            ShortUrlDTO shortUrlDTO = ShortUrlDTO.builder().srcUrl(urlLink).build();
            log.info("新生成短链接更新至短地址; shortUrlDTO:{}", shortUrlDTO);
            shortUrlService.update(exist.getShortCode(), shortUrlDTO);
        }

        log.info("更新小程序短链信息; updateItem:{}", updateItem);
        wxUrlLinkMapper.updateByPrimaryKeySelective(updateItem);;
    }



    private static boolean isValidUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return false;
        }
        return url.matches(URL_REGEX);
    }

    private static String extractDomain(String url) {
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(DOMAIN_REGEX);
        java.util.regex.Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return matcher.group(1); // 提取域名
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindShortUrl(Long id, String shortUrl) {

        WxUrlLinkDO original = validateAndSelectById(id);
        WxUrlLinkDO updateItem = WxUrlLinkDO.builder()
                .id(id)
                .updater(SsoUserInfoUtil.getUserName())
                .build();
        if (StringUtils.isBlank(shortUrl)) {

            if (StringUtils.isNotEmpty(original.getShortUrl())) {
                throw new BizException("短链不能为空");
            }

            //新生成短链编码
            ShortUrlDTO shortUrlDTO = new ShortUrlDTO();
            shortUrlDTO.setValidPeriod(4);
            shortUrlDTO.setSrcUrl(original.getUrlLink());
            shortUrlDTO.setDescription(original.getLinkDesc());
            String shortCode = shortUrlService.innerCreate(shortUrlDTO);
            if (StringUtils.isBlank(shortCode)) {
                throw new BizException("生成短链异常");
            }

            ShortUrlVO shortUrlVO = shortUrlService.queryInfo(shortCode);

            shortUrl = buildShortUrl(shortCode);
            updateItem.setShortCode(shortCode);
            updateItem.setShortUrl(shortUrl);
            updateItem.setShortUrlId(shortUrlVO.getId());
        } else {
            if (!isValidUrl(shortUrl)) {
                log.info("短链格式不正确; id={}, shortUrl={}", id, shortUrl);
                throw new BizException("短链格式不正确");
            }
            if(!extractDomain(shortUrl).equalsIgnoreCase(venusConfig.getShortUrlDomain())) {
                log.info("短链域名不正确; id={}, shortUrl={}", id, shortUrl);
                throw new BizException("短链域名不正确");
            }
            shortUrl = shortUrl.replaceAll(PROTOCOL_REGEX, "");

            String shortCode = extractAfterLastSlash(shortUrl);

            // 校验短链编码是否为字母数字组合
            if (!isAlphanumeric(shortCode)) {
                log.info("短链编码不正确; id={}, shortCode={}", id, shortCode);
                throw new BizException("短链编码不正确");
            }

            ShortUrlVO shortUrlVO = shortUrlService.queryInfo(shortCode);
            ShortUrlDTO shortUrlDTO = ShortUrlDTO.builder()
                    .srcUrl(original.getUrlLink())
                    .description(original.getLinkDesc())
                    .validPeriod(4)
                    .build();
            log.info("更新短链地址; wxUrlId={}, shortUrlId={}, shortUrlVO={}, shortUrlDTO={}",
                    id, shortUrlVO.getId(), shortUrlVO, shortUrlDTO);
            shortUrlService.update(shortUrlVO.getId(), shortUrlDTO);

            updateItem.setShortUrlId(shortUrlVO.getId());
            updateItem.setShortCode(shortUrlVO.getShortCode());
            updateItem.setShortUrl(shortUrlVO.getShortUrl());
        }

        log.info("更新小程序短链信息; urlLink={}, updateItem={}", original.getUrlLink(), updateItem);
        wxUrlLinkMapper.updateByPrimaryKeySelective(updateItem);
    }

    private static boolean isAlphanumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return str.matches("^[a-zA-Z0-9]+$");
    }

    private static String extractAfterLastSlash(String input) {
        if (input == null || input.isEmpty()) {
            return null;
        }
        int lastIndex = input.lastIndexOf('/');
        if (lastIndex == -1) {
            return "";
        }
        return input.substring(lastIndex + 1);
    }


    /**
     * 启用短链
     *
     * @param id id
     */
    @Override
    public void enable(Long id) {
        WxUrlLinkDO wxUrlLinkDO = validateAndSelectById(id);
        if (!wxUrlLinkDO.isDisabled()) {
            throw new BizException("该小程序短链不处于停用状态，不能启用");
        }
        //修改状态为启用
        wxUrlLinkMapper.enable(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 停用短链
     *
     * @param id
     */
    @Override
    public void disable(Long id) {
        WxUrlLinkDO wxUrlLinkDO = validateAndSelectById(id);
        if (!wxUrlLinkDO.isEnabled()) {
            throw new BizException("该小程序短链不处于启用状态，不能停用");
        }
        //修改状态为停用
        wxUrlLinkMapper.disable(id, SsoUserInfoUtil.getUserName());
    }

    private String buildShortUrl(String shortCode) {
        return venusConfig.getShortUrlDomain() + "/" + shortCode;
    }

    private WxUrlLinkDO validateAndSelectById(Long id) {
        WxUrlLinkDO wxUrlLinkDO = wxUrlLinkMapper.selectByPrimaryKey(id);
        if (Objects.isNull(wxUrlLinkDO)) {
            throw new BizException("未找到该短链接配置");
        }
        return wxUrlLinkDO;
    }

    /**
     * 获取过期的小程序短链列表
     * @return
     */
    public List<WxUrlLinkDO> getExpiredUrlLinks() {
        WxUrlLinkQuery wxUrlLinkQuery = WxUrlLinkQuery.builder().status(1).build();
        List<WxUrlLinkDO> wxUrlLinkDOList = wxUrlLinkMapper.selectByQuery(wxUrlLinkQuery);
        if (wxUrlLinkDOList.isEmpty()) {
            log.info("小程序短链列表为空，无需刷新");
            return wxUrlLinkDOList;
        }
        return wxUrlLinkDOList.stream().filter(wxUrlLinkDO -> {
            if (wxUrlLinkDO.getGenerateDate() == null) {
                return true;
            }
            // 判断是否过期
            Date afterDays = DateUtil.addDaysToDate(wxUrlLinkDO.getGenerateDate(),
                    venusConfig.getUrlLinkExpiredDays());
            return afterDays.before(new Date());
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refresh(WxUrlLinkDO wxUrlLinkDO) {
        Date generateDate = new Date();
        String urlLink = generateShortUrl(wxUrlLinkDO.getAppid(),
                wxUrlLinkDO.getEnvVersion(),
                wxUrlLinkDO.getPath(),
                wxUrlLinkDO.getQuery());

        WxUrlLinkDO updateItem = WxUrlLinkDO.builder()
                .id(wxUrlLinkDO.getId())
                .urlLink(urlLink)
                .generateDate(generateDate)
                .build();


        log.info("更新新生成短链接; updateItem:{}", updateItem);
        WxUrlLinkFlowDO flowDO = WxUrlLinkFlowDO.builder()
                .wxUrlLinkId(wxUrlLinkDO.getId())
                .urlLink(urlLink)
                .generateDate(generateDate)
                .build();
        wxUrlLinkFlowMapper.insertSelective(flowDO);
        wxUrlLinkMapper.updateByPrimaryKeySelective(updateItem);
        //更新短链的源链接
        ShortUrlDTO shortUrlDTO = ShortUrlDTO.builder().srcUrl(urlLink).build();
        log.info("刷新新生成短链接更新至短地址; shortUrlId:{}, shortUrlDTO:{}, updateItem:{}",
                wxUrlLinkDO.getShortUrlId(), shortUrlDTO, updateItem);
        shortUrlService.update(wxUrlLinkDO.getShortUrlId(), shortUrlDTO);
    }

    /**
     * 生成短链
     * @param appid
     * @param envVersion
     * @param path
     * @param query
     * @return
     * @throws BizException
     */
    public String generateShortUrl(String appid, String envVersion, String path, String query) throws BizException {
        //生成短链编码
        UrlLinkRequestDTO urlLinkRequestDTO = UrlLinkRequestDTO.builder()
                .appId(appid)
                .envVersion(envVersion)
                .path(path)
                .query(query)
                .expireInterval(CommonConstant.EXPIRE_INTERVAL)
                .build();
        ActionResult<String> result = wxUrlLinkService.generate(urlLinkRequestDTO);
        if (!result.isSuccess()) {
            throw new BizException(result.getMessage());
        }
        return result.getData();
    }
}
