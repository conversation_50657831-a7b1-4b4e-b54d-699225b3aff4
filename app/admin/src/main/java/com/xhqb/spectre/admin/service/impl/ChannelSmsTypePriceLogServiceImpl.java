package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.bidata.mapper.BidataSendStatMapper;
import com.xhqb.spectre.admin.service.ChannelSmsTypePriceLogService;
import com.xhqb.spectre.common.dal.entity.ChannelSmsTypePriceLogDO;
import com.xhqb.spectre.common.dal.mapper.ChannelSmsTypePriceLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ChannelSmsTypePriceLogServiceImpl implements ChannelSmsTypePriceLogService {
    @Autowired
    private ChannelSmsTypePriceLogMapper channelSmsTypePriceLogMapper;

    @Autowired
    private BidataSendStatMapper bidataSendStatMapper;

    public static String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }
    /**
     * 获取所有渠道最新的价格,更新统计表
     */
    @Override
    public void updateAllRecordsWithLatestPrice() {
        log.info("开始更新所有通道短信类型价格记录");
        List<ChannelSmsTypePriceLogDO> latestPricesByDefault = channelSmsTypePriceLogMapper.getLatestPricesByDefault();

        if (latestPricesByDefault == null || latestPricesByDefault.isEmpty()) {
            log.warn("未查询到最新的渠道短信价格记录");
            return;
        }
        log.info("查询到{}条最新的渠道短信价格记录", latestPricesByDefault.size());

        for (ChannelSmsTypePriceLogDO smsTypePriceLogDO : latestPricesByDefault) {
            String statDate = formatDate(smsTypePriceLogDO.getEffectiveTime());
            log.info("更新所有通道短信类型变更前价格记录, 变更日:{}, 渠道:{}, 类型:{}, 价格:{}",
                    statDate, smsTypePriceLogDO.getChannelCode(), smsTypePriceLogDO.getSmsTypeCode(), smsTypePriceLogDO.getPerPrice());
            bidataSendStatMapper.updatePriceByChannelAndTypeAndDateBefore(
                    statDate,
                    smsTypePriceLogDO.getChannelCode(),
                    smsTypePriceLogDO.getSmsTypeCode(),
                    smsTypePriceLogDO.getPerPrice(),
                    null);
            log.info("更新所有通道短信类型变更后价格记录, 变更日:{}, 渠道:{}, 类型:{}, 价格:{}",
                    statDate, smsTypePriceLogDO.getChannelCode(), smsTypePriceLogDO.getSmsTypeCode(), smsTypePriceLogDO.getCurPrice());
            bidataSendStatMapper.updatePriceByChannelAndTypeAndDateFrom(
                    statDate,
                    smsTypePriceLogDO.getChannelCode(),
                    smsTypePriceLogDO.getSmsTypeCode(),
                    smsTypePriceLogDO.getCurPrice(),
                    null);

        }

        List<ChannelSmsTypePriceLogDO> latestPricesBySign = channelSmsTypePriceLogMapper.getLatestPricesBySign();

        if (latestPricesBySign == null || latestPricesBySign.isEmpty()) {
            log.warn("未查询到最新的渠道短信价格记录");
            return;
        }
        log.info("查询到{}条最新的渠道短信价格记录", latestPricesBySign.size());

        for (ChannelSmsTypePriceLogDO smsTypePriceLogDO : latestPricesBySign) {
            String statDate = formatDate(smsTypePriceLogDO.getEffectiveTime());
            log.info("更新指定签名短信类型变更前价格记录, 变更日:{}, 渠道:{}, 类型:{}, 签名:{}, 价格:{}",
                    statDate, smsTypePriceLogDO.getChannelCode(), smsTypePriceLogDO.getSmsTypeCode(),
                    smsTypePriceLogDO.getSignName(), smsTypePriceLogDO.getPerPrice());
            bidataSendStatMapper.updatePriceByChannelAndTypeAndDateBefore(
                    statDate,
                    smsTypePriceLogDO.getChannelCode(),
                    smsTypePriceLogDO.getSmsTypeCode(),
                    smsTypePriceLogDO.getPerPrice(),
                    smsTypePriceLogDO.getSignName());
            log.info("更新指定签名短信类型变更后价格记录, 变更日:{}, 渠道:{}, 类型:{}, 签名:{}, 价格:{}",
                    statDate, smsTypePriceLogDO.getChannelCode(), smsTypePriceLogDO.getSmsTypeCode(),
                    smsTypePriceLogDO.getSignName(), smsTypePriceLogDO.getCurPrice());
            bidataSendStatMapper.updatePriceByChannelAndTypeAndDateFrom(
                    statDate,
                    smsTypePriceLogDO.getChannelCode(),
                    smsTypePriceLogDO.getSmsTypeCode(),
                    smsTypePriceLogDO.getCurPrice(),
                    smsTypePriceLogDO.getSignName());

        }
    }

    /**
     * 指定日期,更新统计表
     * @param statDate 统计日期
     */
    @Override
    public void updateRecordsByDate(LocalDate statDate) {
        List<ChannelSmsTypePriceLogDO> latestPricesByDefault = channelSmsTypePriceLogMapper.getLatestPricesByDefault();
        String date = statDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
        log.info("更新通道短信类型价格记录, 日期:{}", date);

        if (latestPricesByDefault == null || latestPricesByDefault.isEmpty()) {
            log.warn("未查询到最新的渠道短信价格记录，跳过更新");
            return;
        }
        for (ChannelSmsTypePriceLogDO smsTypePriceLogDO : latestPricesByDefault) {
            log.info("更新通道短信类型价格记录, 通道:{}, 类型:{}, 价格:{}",
                    smsTypePriceLogDO.getChannelCode(),
                    smsTypePriceLogDO.getSmsTypeCode(),
                    smsTypePriceLogDO.getCurPrice());
            bidataSendStatMapper.updatePriceByChannelAndTypeAndDateFrom(
                    date,
                    smsTypePriceLogDO.getChannelCode(),
                    smsTypePriceLogDO.getSmsTypeCode(),
                    smsTypePriceLogDO.getCurPrice(),
                    null
            );
        }

        List<ChannelSmsTypePriceLogDO> latestPricesBySign = channelSmsTypePriceLogMapper.getLatestPricesBySign();

        if (latestPricesBySign == null || latestPricesBySign.isEmpty()) {
            log.warn("未查询到最新的渠道短信价格记录，跳过更新");
            return;
        }
        for (ChannelSmsTypePriceLogDO smsTypePriceLogDO : latestPricesBySign) {
            log.info("更新通道短信类型价格记录, 通道:{}, 类型:{}, 签名:{}, 价格:{}",
                    smsTypePriceLogDO.getChannelCode(),
                    smsTypePriceLogDO.getSmsTypeCode(),
                    smsTypePriceLogDO.getSignName(),
                    smsTypePriceLogDO.getCurPrice());
            bidataSendStatMapper.updatePriceByChannelAndTypeAndDateFrom(
                    date,
                    smsTypePriceLogDO.getChannelCode(),
                    smsTypePriceLogDO.getSmsTypeCode(),
                    smsTypePriceLogDO.getCurPrice(),
                    smsTypePriceLogDO.getSignName()
            );
        }
    }
}
