package com.xhqb.spectre.admin.job;

import cn.hutool.core.date.DateUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.service.ShortUrlBrandStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 短链访问品牌统计
 */
@Component
@Job("shortUrlBrandStatJob")
@Slf4j
public class ShortUrlBrandStatJob implements SimpleJob {

    @Resource
    private ShortUrlBrandStatService shortUrlBrandStatService;
    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            shortUrlBrandStatService.runJob(DateUtil.yesterday());
        } catch (Exception e) {
            log.error("短链访问品牌统计异常", e);
        }
    }
}
