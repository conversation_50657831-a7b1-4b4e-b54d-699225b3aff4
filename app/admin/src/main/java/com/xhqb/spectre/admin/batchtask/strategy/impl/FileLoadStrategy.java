package com.xhqb.spectre.admin.batchtask.strategy.impl;

import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.io.UploadFileUtils;
import com.xhqb.spectre.admin.batchtask.parse.ParseContext;
import com.xhqb.spectre.admin.batchtask.strategy.FileLoadContext;
import com.xhqb.spectre.admin.batchtask.strategy.LoadStrategy;
import com.xhqb.spectre.admin.batchtask.upload.UploadContext;
import com.xhqb.spectre.admin.batchtask.upload.UploadHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

/**
 * 异步文件上传策略
 *
 * <AUTHOR>
 * @date 2021/9/29
 */
@Component
@Slf4j
public class FileLoadStrategy implements LoadStrategy<FileLoadContext> {

    @Autowired
    private UploadHandler uploadHandler;

    @Override
    public String named() {
        return BatchTaskConstants.StrategyNamed.FILE_STRATEGY_NAME;
    }

    /**
     * 加载文件策略
     *
     * @param context
     * @return
     * @throws IOException
     */
    @Override
    public ParseContext load(FileLoadContext context) throws IOException {
        File request = context.getFile();
        String fileName = UploadFileUtils.getOriginalFilename(request);
        // 文件上传的流
        InputStream uploadStream = null;
        //String saveUrl = this.doUpload(fileName, uploadStream, context);
        String saveUrl = this.getSaveUrl(fileName, context);
        if (StringUtils.isBlank(saveUrl)) {
            uploadStream = new FileInputStream(request);
        }
        return new ParseContext(fileName, new FileInputStream(request), saveUrl, uploadStream);
    }

    /**
     * 做文件上传
     *
     * @param fileName
     * @param uploadStream
     * @param context
     * @return 返回文件上传之后的地址
     */
    private String doUpload(String fileName, InputStream uploadStream, FileLoadContext context) {
        // 是否需要过滤掉cos文件上传
        boolean isFilterCos = Objects.equals(true, context.getFilterCos());
        if (!isFilterCos) {
            // 不过滤 那么则使用cos进行文件上传
            return uploadHandler.handler(new UploadContext(fileName, uploadStream));
        }

        // 动态生成文件下载地址
        String namePrefix = context.getNamePrefix();
        if (StringUtils.isBlank(namePrefix)) {
            namePrefix = System.nanoTime() + "";
        }
        return namePrefix + BatchTaskConstants.Commons.COS_FILE_NAME_SPLIT + fileName;
    }

    private String getSaveUrl(String fileName, FileLoadContext context) {
        // 是否需要过滤掉cos文件上传
        boolean isFilterCos = Objects.equals(true, context.getFilterCos());
        if (!isFilterCos) {
            // 不过滤 那么则使用cos进行文件上传
            return null;
        }
        // 动态生成文件下载地址
        String namePrefix = context.getNamePrefix();
        if (StringUtils.isBlank(namePrefix)) {
            namePrefix = System.nanoTime() + "";
        }
        return namePrefix + BatchTaskConstants.Commons.COS_FILE_NAME_SPLIT + fileName;
    }
}
