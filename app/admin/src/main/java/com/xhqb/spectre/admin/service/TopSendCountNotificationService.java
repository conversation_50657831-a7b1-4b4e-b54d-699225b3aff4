package com.xhqb.spectre.admin.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.util.StringUtils;
import com.xhqb.spectre.admin.bidata.mapper.BidataPlatformSendStatMapper;
import com.xhqb.spectre.admin.bidata.model.SendCountTopDO;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.model.dto.DateContext;
import com.xhqb.spectre.admin.model.dto.IspData;
import com.xhqb.spectre.admin.model.dto.StatisticsData;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.entity.oa.TplContent;
import com.xhqb.spectre.common.dal.mapper.TplContentMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import com.xhqb.spectre.common.dal.query.OaReportQuery;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * TOP发送量通知服务
 * 抽离两个Job的公共逻辑
 */
@Service
@Slf4j
public class TopSendCountNotificationService {

    @Resource
    private BidataPlatformSendStatMapper bidataPlatformSendStatMapper;

    @Resource
    private VenusConfig venusConfig;

    @Resource
    private TplMapper tplMapper;

    @Resource
    private TplContentMapper tplContentMapper;

    /**
     * 初始化日期上下文
     */
    public DateContext initializeDateContext() {
        Date beginDate = DateUtil.beginOfDay(new Date());
        Date t1Date = DateUtil.offsetDay(beginDate, -1);
        Date t2Date = DateUtil.offsetDay(beginDate, -2);
        Date t3Date = DateUtil.offsetDay(beginDate, -3);

        String t1DateStr = DateUtil.format(t1Date, "yyyy-MM-dd");
        String t2DateStr = DateUtil.format(t2Date, "yyyy-MM-dd");
        String t3DateStr = DateUtil.format(t3Date, "yyyy-MM-dd");

        return new DateContext(t1Date, t2Date, t3Date, t1DateStr, t2DateStr, t3DateStr);
    }

    /**
     * 解析运营商代码配置
     */
    public Set<String> parseIspCodes() {
        String ispCodeAllConfig = venusConfig.getIspCodeAllConfig();
        return Arrays.stream(ispCodeAllConfig.split(","))
                .map(String::trim)
                .collect(Collectors.toSet());
    }

    /**
     * 获取SendCountTOP数量配置
     */
    public int getSendCountTopN() {
        return venusConfig.getTopNForSendCount();
    }

    /**
     * 获取运营商TOP数量配置
     */
    public int getTopNIspCode() {
        return venusConfig.getTopNIspCode();
    }

    /**
     * 获取全局TOP模板编码列表
     */
    public List<String> getGlobalTopTplCodes(DateContext dateContext, int topN) {
        return bidataPlatformSendStatMapper.selectTplCodeByDate(dateContext.getT1DateStr(), topN);
    }

    /**
     * 获取创建者与模板编码的映射关系（只获取status=3的数据）
     */
    public Map<String, List<String>> getCreatorTplCodesMapping() {
        OaReportQuery query = OaReportQuery.builder()
                .status("3")  // 只获取状态为3的数据
                .pageParameter(null)
                .build();

        List<TplContent> allTplContents = tplContentMapper.selectByQuery(query);

        return allTplContents.stream()
                .filter(tplContent -> StringUtils.isNotEmpty(tplContent.getCreator())
                        && StringUtils.isNotEmpty(tplContent.getTplCode()))
                .collect(Collectors.groupingBy(
                        TplContent::getCreator,
                        Collectors.mapping(TplContent::getTplCode,
                                Collectors.collectingAndThen(Collectors.toList(),
                                        list -> list.stream().distinct().collect(Collectors.toList())))
                ));
    }

    /**
     * 根据创建者的模板编码获取TOP模板编码
     */
    public List<String> getCreatorTopTplCodes(List<String> creatorTplCodes, DateContext dateContext, int topN) {
        return bidataPlatformSendStatMapper.selectTopTplCodesByCreatorTplCodes(
                creatorTplCodes, dateContext.getT1DateStr(), topN);
    }

    /**
     * 获取统计数据
     */
    public List<SendCountTopDO> getStatisticsData(List<String> tplCodeList, DateContext dateContext) {
        return bidataPlatformSendStatMapper.selectSendCountTopByTplCodes(tplCodeList, dateContext.getT3DateStr());
    }

    /**
     * 计算统计数据
     */
    public StatisticsData calculateStatistics(
            List<SendCountTopDO> tplDataList,
            DateContext dateContext,
            Set<String> ispCodeSet) {

        StatisticsData statistics = new StatisticsData();

        // 使用Map来优化ISP数据聚合，避免重复的stream操作
        Map<String, IspData> ispDataMap = new HashMap<>();

        // 一次遍历完成所有数据的统计
        for (SendCountTopDO data : tplDataList) {
            String statDate = data.getStatDate();
            long sendCount = data.getSendCount();
            long reachCount = data.getReachCount();

            // 统计总发送量和触达量
            if (statDate.equals(dateContext.getT1DateStr())) {
                statistics.addT1Data(sendCount, reachCount);
            } else if (statDate.equals(dateContext.getT2DateStr())) {
                statistics.addT2Data(sendCount, reachCount);
            } else if (statDate.equals(dateContext.getT3DateStr())) {
                statistics.addT3Data(sendCount, reachCount);
            }

            // 统计运营商数据
            String ispCode = data.getIspCode();
            if (ispCodeSet.contains(ispCode)) {
                String key = statDate + "-" + ispCode;
                IspData ispData = ispDataMap.computeIfAbsent(key, k -> new IspData());
                ispData.addData(sendCount, reachCount);
            }
        }

        // 计算ISP触达率
        Map<String, Double> ispRateMap = new HashMap<>();
        for (Map.Entry<String, IspData> entry : ispDataMap.entrySet()) {
            IspData ispData = entry.getValue();
            double reachRate = ispData.getSendCount() > 0
                    ? CommonUtil.realDivision(ispData.getReachCount(), ispData.getSendCount())
                    : 0.0;
            ispRateMap.put(entry.getKey(), reachRate);
        }

        statistics.setIspRateMap(ispRateMap);
        return statistics;
    }

    /**
     * 构建基础消息对象（不包含creator信息）
     */
    public JSONObject buildBaseMessage(
            String tplCode,
            StatisticsData statistics,
            DateContext dateContext,
            Set<String> ispCodeSet) {

        JSONObject message = new JSONObject();

        // 添加ISP触达率数据
        for (String ispCode : ispCodeSet) {
            String key = dateContext.getT1DateStr() + "-" + ispCode;
            Double rate = statistics.getIspRateMap().get(key);
            message.put(getIspCode(ispCode), rate == null ? 0.0 : rate * 100);
        }

        // 计算各日期的触达率
        double t1ReachRate = statistics.getT1SendCount() > 0
                ? CommonUtil.realDivision(statistics.getT1ReachCount(), statistics.getT1SendCount())
                : 0.0;
        double t2ReachRate = statistics.getT2SendCount() > 0
                ? CommonUtil.realDivision(statistics.getT2ReachCount(), statistics.getT2SendCount())
                : 0.0;
        double t3ReachRate = statistics.getT3SendCount() > 0
                ? CommonUtil.realDivision(statistics.getT3ReachCount(), statistics.getT3SendCount())
                : 0.0;

        // 构建完整消息
        message.put("tplCode", tplCode);
        message.put("t1SendCount", statistics.getT1SendCount());
        message.put("t2SendCount", statistics.getT2SendCount());
        message.put("t3SendCount", statistics.getT3SendCount());
        message.put("t1ReachRate", t1ReachRate * 100);
        message.put("t2ReachRate", t2ReachRate * 100);
        message.put("t3ReachRate", t3ReachRate * 100);

        return message;
    }

    /**
     * 构建模板信息映射表，键为模板编码，值为创建者
     */
    public Map<String, String> createTplInfoMap(List<String> tplCodeList) {
        List<TplDO> tplDOS = tplMapper.selectByCodeList(tplCodeList);

        List<TplDO> originList = tplDOS.stream()
                .filter(tplDO -> StringUtils.isEmpty(tplDO.getReportId()))
                .collect(Collectors.toList());

        List<String> reportIdList = tplDOS.stream()
                .map(TplDO::getReportId)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());

        Map<String, String> resultMap = new HashMap<>(tplCodeList.size());
        if (!CollectionUtils.isEmpty(reportIdList)) {
            List<TplContent> reportList = tplContentMapper.selectByContentIdList(reportIdList);
            for (TplContent tplContent : reportList) {
                resultMap.put(tplContent.getTplCode(), tplContent.getCreator());
            }
        }
        for (TplDO tplDO : originList) {
            resultMap.put(tplDO.getCode(), tplDO.getCreator());
        }
        return resultMap;
    }

    /**
     * 解析用户名和邮箱映射
     */
    public Map<String, String> parseUserNameAndEmailMapping() {
        String userNameAndEmail = venusConfig.getUserNameAndEmail();
        Map<String, String> userNameAndEmailMap = new HashMap<>();

        if (StringUtils.isNotEmpty(userNameAndEmail)) {
            String[] dataInfo = userNameAndEmail.split(",");
            for (String item : dataInfo) {
                String[] info = item.split("=");
                if (info.length == 2) {
                    userNameAndEmailMap.put(info[0], info[1]);
                }
            }
        }
        return userNameAndEmailMap;
    }

    /**
     * 获取邮箱地址
     */
    public String getEmailAddress(Map<String, String> userNameAndEmailMap, String creator) {
        return userNameAndEmailMap.getOrDefault(creator, creator + "@xhqb.com");
    }

    /**
     * 处理数据并构建消息列表（全局TOP模式）
     */
    public List<JSONObject> processGlobalTopData(
            List<String> tplCodeList,
            List<SendCountTopDO> allDataList,
            Map<String, String> tplCodeAndCreatorMap,
            DateContext dateContext,
            Set<String> ispCodeSet) {

        // 按模板代码分组数据
        Map<String, List<SendCountTopDO>> tplCodeMap = allDataList.stream()
                .collect(Collectors.groupingBy(SendCountTopDO::getTplCode));

        List<JSONObject> messageList = new ArrayList<>(tplCodeList.size());

        for (String tplCode : tplCodeList) {
            List<SendCountTopDO> tplDataList = tplCodeMap.getOrDefault(tplCode, Collections.emptyList());

            // 计算统计数据
            StatisticsData statistics = calculateStatistics(tplDataList, dateContext, ispCodeSet);

            // 构建消息对象
            JSONObject message = buildBaseMessage(tplCode, statistics, dateContext, ispCodeSet);
            message.put("creator", tplCodeAndCreatorMap.getOrDefault(tplCode, ""));
            messageList.add(message);
        }

        return messageList;
    }

    /**
     * 处理创建者数据（按创建者TOP模式）
     */
    public List<JSONObject> processCreatorData(
            List<String> topTplCodes,
            DateContext dateContext,
            Set<String> ispCodeSet) {

        // 获取统计数据
        List<SendCountTopDO> allDataList = getStatisticsData(topTplCodes, dateContext);

        // 按模板代码分组数据
        Map<String, List<SendCountTopDO>> tplCodeMap = allDataList.stream()
                .collect(Collectors.groupingBy(SendCountTopDO::getTplCode));

        List<JSONObject> messageList = new ArrayList<>(topTplCodes.size());

        for (String tplCode : topTplCodes) {
            List<SendCountTopDO> tplDataList = tplCodeMap.getOrDefault(tplCode, Collections.emptyList());

            // 计算统计数据
            StatisticsData statistics = calculateStatistics(tplDataList, dateContext, ispCodeSet);

            // 构建消息对象
            JSONObject message = buildBaseMessage(tplCode, statistics, dateContext, ispCodeSet);
            messageList.add(message);
        }

        return messageList;
    }

    public String getIspCode(String ispCodeName) {
        if (Objects.equals("移动", ispCodeName)) {
            return "CM";
        } else if (Objects.equals("电信", ispCodeName)) {
            return "CT";
        } else {
            return "CU";
        }
    }
}
