package com.xhqb.spectre.admin.openapi.common;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 响应编码定义
 * 400 001
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
@Getter
@AllArgsConstructor
public enum CodeDefinition {

    /**
     * 处理成功
     */
    SUCCESS("200", "Success"),
    UNKNOWN("400999", "未知异常"),
    UNAUTHORIZED("401001", "应用未被授权"),
    EMPTY_APP_KEY("400001", "appKey不能够为空"),
    EMPTY_TIMESTAMP("400002", "timestamp不能够为空"),
    EMPTY_NONCE("400003", "nonce不能够为空"),
    EMPTY_SIGN("400004", "sign不能够为空"),
    TIMESTAMP_ILLEGAL("400005", "timestamp不合法"),
    TIMESTAMP_EXPIRE("400006", "timestamp已过期"),
    NONCE_LENGTH_OVER("400007", "nonce长度不能够超过32位"),
    NONCE_REPEAT("400008", "nonce重复"),
    SIGN_ILLEGAL("400009", "签名不合法");


    private String code;
    private String msg;


    /**
     * 根据code获取到编码定义对象
     *
     * @param code
     * @return
     */
    public static CodeDefinition getDefinition(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (CodeDefinition definition : values()) {
            if (StringUtils.equals(definition.getCode(), code)) {
                return definition;
            }
        }
        return null;
    }


    /**
     * 根据code获取到描述信息
     *
     * @param code
     * @return
     */
    public static String getMessage(String code) {
        CodeDefinition definition = getDefinition(code);
        if (Objects.isNull(definition)) {
            return null;
        }
        return definition.getMsg();
    }
}
