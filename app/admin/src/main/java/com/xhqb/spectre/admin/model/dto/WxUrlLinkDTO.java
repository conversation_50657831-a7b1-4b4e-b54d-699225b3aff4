package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.common.validation.ValidPath;
import com.xhqb.spectre.common.validation.ValidQuery;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;


@Data
public class WxUrlLinkDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * appid
     */
    @NotBlank(message = "appid不能为空")
    private String appid;

    /**
     * 环境变量
     */
    @NotBlank(message = "环境变量不能为空")
    private String envVersion;

    /**
     * 小程序页面路径
     */
    @ValidPath(message = "小程序路径格式错误")
    private String path;

    /**
     * 查询参数
     */
    @ValidQuery(message = "查询参数格式错误")
    private String query;


    /**
     * 使用场景描述
     */
    @NotBlank(message = "场景描述不能为空")
    @Size(max = 256, message = "描述最大为{max}个字符")
    private String linkDesc;
}
