package com.xhqb.spectre.admin.statistics.vo;

import com.xhqb.spectre.admin.statistics.entity.SmsStatisSumDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/2 10:58
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsStatisClassSumVO implements Serializable {

    private static final long serialVersionUID = 2864586321288218470L;

    /**
     * 统计维度值
     */
    private String classValue;

    /**
     * 发送量
     */
    private Integer sendCount;

    /**
     * 计费量
     */
    private Integer billCount;

    public static SmsStatisClassSumVO build(SmsStatisSumDO item) {
        return SmsStatisClassSumVO.builder()
                .classValue(item.getClassValue())
                .sendCount(item.getSendCount())
                .billCount(item.getBillCount())
                .build();
    }
}
