package com.xhqb.spectre.admin.openapi.controller;

import com.xhqb.spectre.admin.model.vo.TplInfoVO;
import com.xhqb.spectre.admin.model.vo.TplVO;
import com.xhqb.spectre.admin.openapi.common.ActionResult;
import com.xhqb.spectre.admin.openapi.request.QueryTplRequest;
import com.xhqb.spectre.admin.service.TplService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.TplQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 短信模板开放接口
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
@RestController
@RequestMapping("/open/api/tpl")
@Slf4j
public class OpenApiTplController {

    @Resource
    private TplService tplService;

    /**
     * 模板列表查询
     *
     * @param tplQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public ActionResult<CommonPager<TplVO>> queryTplList(@ModelAttribute TplQuery tplQuery, Integer pageNum, Integer pageSize) {
        tplQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<TplVO> commonPager = tplService.listByPage(tplQuery);
        return ActionResult.success(commonPager);
    }

    /**
     * 查询模板详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ActionResult<TplVO> queryTplInfo(@PathVariable("id") Integer id) {
        return ActionResult.success(tplService.getById(id));
    }

    /**
     * 查询模版详情
     */
    @PostMapping("/code")
    public ActionResult<TplVO> queryTpl(@RequestBody QueryTplRequest request) {
        TplVO vo = tplService.queryTpl(request.getTplCode(), request.getSignName());
        return ActionResult.success(vo);
    }

    /**
     * 返回指定应用下所有模版
     * @param appCode 应用ID
     * @return 模版详情list
     */
    @GetMapping("/all/{appCode}")
    public ActionResult<List<TplInfoVO>> queryByAppCode(@PathVariable("appCode") String appCode) {
        List<TplInfoVO> tplVOList = tplService.queryByAppCode(appCode);
        return ActionResult.success(tplVOList);
    }
}
