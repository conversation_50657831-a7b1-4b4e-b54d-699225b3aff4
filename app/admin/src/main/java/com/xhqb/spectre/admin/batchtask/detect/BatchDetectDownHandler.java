package com.xhqb.spectre.admin.batchtask.detect;

import com.alibaba.excel.EasyExcel;
import com.google.common.collect.Lists;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.openapi.utils.ServletUtils;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.BatchDetectDO;
import com.xhqb.spectre.common.dal.mapper.BatchDetectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 群发文件探测结果下载处理
 *
 * <AUTHOR>
 * @date 2022/2/22
 */
@Component
@Slf4j
public class BatchDetectDownHandler {

    /**
     * 一次查询5000条数据
     */
    private static final int MAX_QUERY_SIZE = 5000;

    @Resource
    private BatchDetectMapper batchDetectMapper;

    /**
     * 文件检测内容结果下载
     *
     * @param taskId
     * @param downType
     * @param response
     * @throws Exception
     */
    public void down(Integer taskId, Integer downType, HttpServletResponse response) throws Exception {
        log.info("收到文件检测内容结果下载请求,taskId = {}, downType = {}", taskId, downType);
        List<BatchDetectDO> batchDetectList = this.scan(taskId, downType);
        if (CommonUtil.isEmpty(batchDetectList)) {
            log.warn("导出群发文件检测数据,未查询到群发文件检测记录, taskId = {}, downType = {}", taskId, downType);
            ServletUtils.write(response, AdminResult.error("未查询到群发文件检测记录"));
            return;
        }

        response.setCharacterEncoding("utf-8");
        response.setContentType("application/vnd.ms-excel");

        // excel头部信息
        List<List<String>> header = this.createHeader();
        // excel内容列表
        List<List<String>> dataList = this.createDataList(batchDetectList);

        String fileName = taskId + "_文件检测结果_" + "_" + System.currentTimeMillis();
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream())
                .useDefaultStyle(false)
                .head(header)
                .sheet("Sheet1")
                .doWrite(dataList);
    }


    /**
     * 创建excel头部信息
     *
     * @return
     */
    private List<List<String>> createHeader() {
        List<List<String>> header = new ArrayList<>();
        header.add(Lists.newArrayList("cid"));
        header.add(Lists.newArrayList("remark"));
        header.add(Lists.newArrayList("检测类型"));
        return header;
    }

    /**
     * 创建excel内容数据列表
     *
     * @param batchDetectList
     * @return
     */
    private List<List<String>> createDataList(List<BatchDetectDO> batchDetectList) {
        List<List<String>> dataList = new ArrayList<>(batchDetectList.size());
        List<String> data;
        for (BatchDetectDO batchDetectDO : batchDetectList) {
            data = this.createData(batchDetectDO);
            if (!CommonUtil.isEmpty(data)) {
                dataList.add(data);
            }
        }
        return dataList;
    }

    /**
     * 创建数据
     *
     * @param batchDetectDO
     * @return
     */
    private List<String> createData(BatchDetectDO batchDetectDO) {
        List<String> colList = new ArrayList<>();
        // 第一列的数据
        colList.add(batchDetectDO.getContent());
        colList.add(batchDetectDO.getRemark());
        colList.add(this.getDetectTypeName(batchDetectDO.getType()));
        return colList;
    }


    /**
     * 扫描出待导出的数据信息
     *
     * @param taskId
     * @param downType
     * @return
     */
    private List<BatchDetectDO> scan(Integer taskId, Integer downType) {
        List<BatchDetectDO> container = this.createContainer(taskId, downType);
        Integer lastId = 0;
        do {
            lastId = query(taskId, lastId, container, downType);
        } while (lastId != null);
        return container;
    }

    /**
     * 查询待导出的数据
     *
     * @param taskId
     * @param lastId
     * @param container
     * @return
     */
    private Integer query(Integer taskId, Integer lastId, List<BatchDetectDO> container, Integer downType) {
        List<BatchDetectDO> batchDetectList = batchDetectMapper.exportQuery(taskId, lastId, MAX_QUERY_SIZE, downType);
        if (CommonUtil.isEmpty(batchDetectList)) {
            return null;
        }
        container.addAll(batchDetectList);
        return batchDetectList.get(batchDetectList.size() - 1).getId();
    }

    /**
     * 创建数据容器
     *
     * @param taskId
     * @param downType
     * @return
     */
    private List<BatchDetectDO> createContainer(Integer taskId, Integer downType) {
        Integer count = batchDetectMapper.exportCount(taskId, downType);
        if (Objects.isNull(count) || count <= 0) {
            return new ArrayList<>();
        }
        return new ArrayList<>(count);
    }


    /**
     * 获取到检测类型名称
     *
     * @param detectType
     * @return
     */
    private String getDetectTypeName(Integer detectType) {
        // 检测类型 0->无效CID 1->空号类型 2->停机类型 3->参数缺失 4->数据重复
        if (Objects.isNull(detectType) || Objects.equals(detectType, BatchTaskConstants.DetectType.BAD_TYPE)) {
            return "无效CID";
        }

        if (Objects.equals(detectType, BatchTaskConstants.DetectType.PHONE_EMPTY_TYPE)) {
            return "空号类型";
        }

        if (Objects.equals(detectType, BatchTaskConstants.DetectType.PHONE_HALT_TYPE)) {
            return "停机类型";
        }

        if (Objects.equals(detectType, BatchTaskConstants.DetectType.MISS_TYPE)) {
            return "缺失类型";
        }

        if (Objects.equals(detectType, BatchTaskConstants.DetectType.REPEAT_TYPE)) {
            return "重复类型";
        }

        return "无效CID";
    }
}
