package com.xhqb.spectre.admin.config;

import com.xhqb.spectre.common.strategy.FailResendSceneStrategy;
import com.xhqb.spectre.common.strategy.FailResendSceneStrategyFactory;
import com.xhqb.spectre.common.strategy.impl.CarrierSceneStrategy;
import com.xhqb.spectre.common.strategy.impl.ChannelSceneStrategy;
import com.xhqb.spectre.common.strategy.impl.RegionSceneStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * 补发策略配置
 */
@Configuration
@Slf4j
public class FailResendStrategyConfig {

    @Bean
    public CarrierSceneStrategy carrierSceneStrategy() {
        log.info("初始化运营商场景策略Bean");
        return new CarrierSceneStrategy();
    }

    @Bean
    public ChannelSceneStrategy channelSceneStrategy() {
        log.info("初始化渠道场景策略Bean");
        return new ChannelSceneStrategy();
    }

    @Bean
    public RegionSceneStrategy regionSceneStrategy() {
        log.info("初始化地区场景策略Bean");
        return new RegionSceneStrategy();
    }

    @Bean
    public FailResendSceneStrategyFactory failResendSceneStrategyFactory(
            CarrierSceneStrategy carrierSceneStrategy,
            ChannelSceneStrategy channelSceneStrategy,
            RegionSceneStrategy regionSceneStrategy) {
        
        log.info("初始化补发场景策略工厂Bean");
        FailResendSceneStrategyFactory factory = new FailResendSceneStrategyFactory();

        List<FailResendSceneStrategy> strategies = Arrays.asList(
                carrierSceneStrategy,
                channelSceneStrategy,
                regionSceneStrategy
        );

        factory.initStrategies(strategies);
        log.info("补发场景策略工厂Bean初始化完成");
        return factory;
    }
}
