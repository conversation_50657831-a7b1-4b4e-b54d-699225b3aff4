package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 18:01
 * @Description:
 */
@Data
public class AreaDTO implements Serializable {

    private static final long serialVersionUID = -5126081390918053214L;

    @NotBlank(message = "地域ID不能为空")
    private String id;

    @NotNull(message = "省份不能为空")
    private String province;

    @NotNull(message = "城市不能为空")
    private String city;

    @NotNull(message = "省份简名不能为空")
    private String provinceShortName;

    @NotNull(message = "城市简名不能为空")
    private String cityShortName;
}
