package com.xhqb.spectre.admin.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.service.KoomessageTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
@Job("koomessageTplReportJob")
@Slf4j
public class KoomessageTplReportJob implements SimpleJob {
    @Resource
    private KoomessageTemplateService koomessageTemplateService;

    @Override
    public void execute(ShardingContext shardingContext) {
        long start = System.currentTimeMillis();
        log.info("开始读取koomessage模版报表");
        try {
            koomessageTemplateService.loadReports(null, 3);
        } catch (Exception e) {
            log.warn("读取koomessage模版报表失败", e);
        }
        log.info("读取koomessage模版报表结束,耗时 = {}", (System.currentTimeMillis() - start));
    }
}
