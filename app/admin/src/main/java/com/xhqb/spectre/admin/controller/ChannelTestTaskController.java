package com.xhqb.spectre.admin.controller;

import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.model.dto.TestTaskDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.ChannelTestTaskVO;
import com.xhqb.spectre.admin.service.ChannelTestTaskService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ChannelTestTaskQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

@RestController
@RequestMapping("/channelTestTask")
@Slf4j
public class ChannelTestTaskController {

    @Resource
    private ChannelTestTaskService channelTestTaskService;

    /**
     * 测试任务分页列表查询
     * @param channelTestTaskQuery 查询条件
     * @param pageNum 页码
     * @param pageSize 每页条数
     * @return 分页列表
     */
    @GetMapping("")
    public CommonResult<CommonPager<ChannelTestTaskVO>> listByPage(@ModelAttribute ChannelTestTaskQuery channelTestTaskQuery, Integer pageNum, Integer pageSize) {
        channelTestTaskQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        log.info("测试任务分页列表查询参数 channelTestTaskQuery:{}",JsonLogUtil.toJSONString(channelTestTaskQuery));
        return CommonResult.success(channelTestTaskService.listByPage(channelTestTaskQuery));
    }

    /**
     * 新增测试任务
     * @param testTaskDTO 新增任务参数
     * @return taskID
     */
    @PostMapping("/add")
    public CommonResult<String> add(@RequestBody TestTaskDTO testTaskDTO){
        log.info("测试任务新增任务参数 testTaskDTO:{}", JsonLogUtil.toJSONString(testTaskDTO));
        return CommonResult.success(channelTestTaskService.add(testTaskDTO));
    }

    /**
     * 测试任务取消
     * @param taskId 任务id
     * @return 操作结果
     */
    @PostMapping("/cancel")
    public CommonResult<String> cancel(String taskId){
        log.info("测试任务取消 taskId:{}",taskId);
        return CommonResult.success(channelTestTaskService.cancel(taskId));
    }

    // 执行
    @PostMapping("/execute")
    public CommonResult<List<String>> execute(String taskId){
        log.info("测试任务执行 taskId:{}",taskId);
        return CommonResult.success(channelTestTaskService.execute(taskId));
    }

    /**
     * 测试任务删除
     * @param taskId 任务id
     * @return  操作结果
     */
    @PostMapping("/delete")
    public CommonResult<String> delete(String taskId){
        log.info("测试任务删除 taskId:{}",taskId);
        return CommonResult.success(channelTestTaskService.delete(taskId));
    }

}
