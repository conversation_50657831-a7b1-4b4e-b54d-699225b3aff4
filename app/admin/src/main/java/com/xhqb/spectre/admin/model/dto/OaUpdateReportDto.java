package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class OaUpdateReportDto {

    /**
     * 内容 id
     */
    private String contentId;

    /**
     * 短信类型 营销
     */
    private String smsTypeCode;

    /**
     * 使用场景编码 已注册用户：registered 未注册用户: unregistered 已获额用户：paid'
     */
    private String sceneCode;

    /**
     * 签名 id
     */
    private String signId;


    /**
     * 短信文案
     */
    private String smsContent;

    /**
     * 标签
     * 默认无:0; 营销类通知:1;
     */
    private Integer tag;

    /**
     * 变量参数类型
     */
    private String paramType;

    /**
     * 变量参数长度
     */
    private String paramLen;

}
