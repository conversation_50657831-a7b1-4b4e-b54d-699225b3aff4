package com.xhqb.spectre.admin.constant;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 14:52
 * @Description:
 */
public final class CommonConstant {

    //通用状态值（1：有效；0：无效）
    public static final int STATUS_VALID = 1;
    public static final int STATUS_INVALID = 0;

    /**
     * 日期时间的正则表达式，匹配 yyyy-MM-dd HH:mm:ss
     */
    public static final String PATTERN_DATE_TIME = "^((\\d{2}(([02468][048])|([13579][26]))[\\-]((((0?[13578])|(1[02]))[\\-]((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-]((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-]((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-]((((0?[13578])|(1[02]))[\\-]((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-]((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-]((0?[1-9])|(1[0-9])|(2[0-8]))))))\\s(((0?[0-9])|([1][0-9])|([2][0-4]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9]))))$";

    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String PATTERN_MOBILE = "^1\\d{10}$";

    /**
     * 业务编码正则表达式（应用编码、渠道编码等）
     */
    public static final String PATTERN_CODE = "^(?![_|0-9|-])(?!.*?[_|-]$)[a-zA-Z0-9_-]+$";

    /**
     * 模板编码正则表达式
     */
    // public static final String TPL_PATTERN = "^(?![_|0-9|-])(?!.*?[_|-]$)[a-zA-Z0-9_-]+$";
    // 和前端验证模板编码规则保持一致
    public static final String TPL_PATTERN = "^(?![_|-])(?!.*?[_|-]$)[a-z@A-Z0-9_-]+$";

    /**
     * 小程序短链有效期（日）
     */
    public static final Integer EXPIRE_INTERVAL = 30;
}
