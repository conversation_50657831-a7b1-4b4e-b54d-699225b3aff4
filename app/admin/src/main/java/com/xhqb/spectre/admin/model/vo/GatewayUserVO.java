package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.GatewayUserDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 网关账号管理(王建政)
 *
 * <AUTHOR>
 * @date 2021/11/8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GatewayUserVO implements Serializable {

    private static final long serialVersionUID = -6768340889587042023L;

    /**
     * 主键
     */
    private Integer id;
    /**
     * 分组名称
     */
    private String groupName;
    /**
     * 企业账号
     */
    private String userName;
    /**
     * 企业密码
     */
    private String password;
    /**
     * 企业代码
     */
    private String spCode;
    /**
     * 服务代码
     */
    private String serviceId;
    /**
     * 信息内容来源,通常与username相同
     */
    private String msgSrc;
    /**
     * 模板code
     */
    private String tplCode;

    /**
     * 签名ID列表
     */
    private List<Integer> signIdList;

    /**
     * 接入IP白名单列表
     */
    private List<String> whiteIpList;

    /**
     * 模板文案检测，1：开启；0：不开启
     */
    private Integer checkTplFlag;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 查询列表数据展现
     *
     * @param gatewayUserDO
     * @return
     */
    public static GatewayUserVO buildListQuery(GatewayUserDO gatewayUserDO) {
        GatewayUserVO gatewayUserVO = buildInfoQuery(gatewayUserDO);
        // 列表展现 密码脱敏
        gatewayUserVO.setPassword("******");
        return gatewayUserVO;
    }

    /**
     * 查询数据详情展现
     *
     * @param gatewayUserDO
     * @return
     */
    public static GatewayUserVO buildInfoQuery(GatewayUserDO gatewayUserDO) {
        return GatewayUserVO.builder()
                // 主键ID
                .id(gatewayUserDO.getId())
                // 分组名称
                .groupName(gatewayUserDO.getGroupName())
                // 企业账号
                .userName(gatewayUserDO.getUserName())
                // 企业密码
                .password(gatewayUserDO.getPassword())
                // 企业代码
                .spCode(gatewayUserDO.getSpCode())
                // 服务代码
                .serviceId(gatewayUserDO.getServiceId())
                // 信息内容来源,通常与username相同
                .msgSrc(gatewayUserDO.getMsgSrc())
                // 模板ID
                .tplCode(gatewayUserDO.getTplCode())
                //签名ID列表
                .signIdList(StringUtils.isNotBlank(gatewayUserDO.getSignIdList()) ? Arrays.stream(gatewayUserDO.getSignIdList().split(","))
                        .map(Integer::valueOf).collect(Collectors.toList()) : Collections.emptyList())
                //接入IP白名单列表
                .whiteIpList(StringUtils.isNotBlank(gatewayUserDO.getWhiteIpList()) ? Arrays.stream(gatewayUserDO.getWhiteIpList().split(","))
                        .collect(Collectors.toList()) : Collections.emptyList())
                // 模板文案检测，1：开启；0：不开启
                .checkTplFlag(gatewayUserDO.getIsCheckTpl())
                //创建人
                .creator(gatewayUserDO.getCreator())
                // 创建时间
                .createTime(gatewayUserDO.getCreateTime())
                // 更新人
                .updater(gatewayUserDO.getUpdater())
                // 更新时间
                .updateTime(gatewayUserDO.getUpdateTime())
                .build();
    }

}
