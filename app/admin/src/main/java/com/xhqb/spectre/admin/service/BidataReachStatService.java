package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.ReachStatDTO;
import com.xhqb.spectre.admin.model.vo.*;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;

import java.util.List;
import java.util.Map;

/**
 * 渠道商横纵月统计
 *
 */
public interface BidataReachStatService {

    /**
     * 渠道搜索栏
     * @return
     */
    StatChannelVO getStatChannelList();

    /**
     * 指定日期搜索栏
     * @return
     */
    List<Map<String, Object>> getStatSignList();

    /**
     * 短信类型搜索栏
     * @return
     */
    StatSmsTypeVO getStatSmsTypeList();

    List<MonoReachStatVO> getMonthStatsByChannel(ReachStatDTO reachStatDTO);

    List<MonoReachStatVO> getWeekStatsByChannel(ReachStatDTO reachStatDTO);

    List<MonoReachStatVO> getMonthStatsByDate(ReachStatDTO reachStatDTO);

    List<MonoReachStatVO> getWeekStatsByDate(ReachStatDTO reachStatDTO);

    List<PolyReachStatVO> getMonthStatsByType(ReachStatDTO reachStatDTO);

    List<PolyReachStatVO> getWeekStatsByType(ReachStatDTO reachStatDTO);

    List<SubReachStatVO> getMonthSubReachStats(ReachStatDTO reachStatDTO);


    List<SubReachStatVO> getWeekSubReachStats(ReachStatDTO reachStatDTO);

    TplReachRateVO getAbnormalTplReachRates(PageParameter pageParameter);

    TplStatVO getAbnormalTplChannelReachRate(String stateDate,
                                                          double rateGap,
                                                          int minSendCount);

    TplStatVO queryTplChannelStats(ReachStatDTO reachStatDTO);


    List<TplHeatStatVO> getOverallByTplHeat(String startDate, String endDate, String smsTypeCode,
                                            String tplCode, String creator);

    CommonPager<TplHeatStatVO> getStatByTplHeat(String startDate, String endDate, String smsTypeCode,
                                                String tplCode, String creator, PageParameter pageParameter);

    List<TplHeatStatVO> getStatByTplHeat(String startDate, String endDate, String smsTypeCode,
                                         String tplCode, String creator);


    List<MonoPriceStatVO> getMonthPriceStatsByChannel(ReachStatDTO reachStatDTO);

    List<MonoPriceStatVO> getWeekPriceStatsByChannel(ReachStatDTO reachStatDTO);

    List<MonoPriceStatVO> getMonthPriceStatsByDate(ReachStatDTO reachStatDTO);

    List<MonoPriceStatVO> getWeekPriceStatsByDate(ReachStatDTO reachStatDTO);

    List<PolyPriceStatVO> getMonthPriceStatsByType(ReachStatDTO reachStatDTO);

    List<PolyPriceStatVO> getWeekPriceStatsByType(ReachStatDTO reachStatDTO);

    List<MonoPriceStatVO> getMonthPriceStats(ReachStatDTO reachStatDTO);

    List<MonoPriceStatVO> getWeekPriceStats(ReachStatDTO reachStatDTO);

    List<ChannelMonthlyStatementVO> getMonthlyStatementStats(ReachStatDTO reachStatDTO);

    List<ChannelMonthlyStatementVO> getMonthlyStatementRecords(ReachStatDTO reachStatDTO);

    TopTplStatVO queryTopTplChannelReachRate(ReachStatDTO reachStatDTO);

    List<MonoBillStatVO> getMonthBillStats(ReachStatDTO reachStatDTO);

    List<MonoBillStatVO> getWeekBillStats(ReachStatDTO reachStatDTO);
}
