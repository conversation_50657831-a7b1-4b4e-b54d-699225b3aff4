package com.xhqb.spectre.admin.batchtask.result;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 完件计数器
 *
 * <AUTHOR>
 * @date 2021/10/22
 */
public class ApplyLoanResultCounter {
    /**
     * 计数器
     */
    private AtomicInteger counter;
    /**
     * 完件过滤的cid数据列表
     */
    private List<String> cidList;

    public ApplyLoanResultCounter() {
        this.counter = new AtomicInteger();
        this.cidList = new ArrayList<>();
    }

    public AtomicInteger getCounter() {
        return counter;
    }

    public void setCounter(AtomicInteger counter) {
        this.counter = counter;
    }

    public List<String> getCidList() {
        return cidList;
    }

    public void setCidList(List<String> cidList) {
        this.cidList = cidList;
    }

    /**
     * 添加cid操作
     *
     * @param cid
     */
    public void add(String cid) {
        this.cidList.add(StringUtils.isNotBlank(cid) ? cid : "");
        this.counter.incrementAndGet();
    }

    /**
     * 获取cid数据信息
     *
     * @param index
     * @return
     */
    public String getCid(int index) {
        if (index >= this.cidList.size()) {
            return null;
        }
        return this.cidList.get(index);
    }

    @Override
    public String toString() {
        return "ApplyLoanResultCounter{" +
                "counter=" + counter +
                ", cidList=" + cidList +
                '}';
    }
}
