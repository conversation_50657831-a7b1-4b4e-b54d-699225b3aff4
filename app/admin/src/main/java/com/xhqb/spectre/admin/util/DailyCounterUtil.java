package com.xhqb.spectre.admin.util;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * 每日从01自增的序列号
 */
@Component
public class DailyCounterUtil {

    private final StringRedisTemplate redisTemplate;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    public DailyCounterUtil(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 获取当日格式化后的自增序列号
     *
     */
    public String getDailySequenceNumber(String prefix, int length) {
        String dateKey = LocalDate.now().format(DATE_FORMATTER);
        String redisKey = "counter:" + dateKey;

        // 先自增
        Long incr = redisTemplate.opsForValue().increment(redisKey);

        if (incr == null) {
            throw new RuntimeException("Redis increment failed");
        }

        if (incr == 1) {
            LocalDateTime tomorrowMidnight = LocalDateTime.now().plusDays(1).with(LocalTime.MIDNIGHT);
            long delaySeconds = LocalDateTime.now().until(tomorrowMidnight, java.time.temporal.ChronoUnit.SECONDS) + 3600;

            redisTemplate.expire(redisKey, delaySeconds, TimeUnit.SECONDS);
        }

        String formatted = String.format("%0" + length + "d", incr);
        return (prefix == null ? "" : prefix) + formatted;
    }

    /**
     * 获取当日格式化后的自增序列号
     */
    public String getDailySequenceNumber(int length) {
        return getDailySequenceNumber(null, length);
    }
}
