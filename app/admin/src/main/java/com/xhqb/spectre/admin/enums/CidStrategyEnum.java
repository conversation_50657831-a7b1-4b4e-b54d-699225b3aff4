package com.xhqb.spectre.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * cid完件策略枚举
 *
 * <AUTHOR>
 * @date 2021/11/26
 */
@Getter
@AllArgsConstructor
public enum CidStrategyEnum {

    USER_STATUS("userStatus", "用户状态"),
    APPLY_LOAN_RESULT("applyLoanResult", "用户完件"),
    BATCH_SUBMIT_CHECK("batchSubmitCheck", "群发提交检测"),
    PHONE_STATUS("phoneStatus", "手机状态Ò");

    private final String code;
    private final String description;
}
