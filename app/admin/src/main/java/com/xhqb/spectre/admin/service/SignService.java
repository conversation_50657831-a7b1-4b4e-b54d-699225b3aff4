package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.AddSignDTO;
import com.xhqb.spectre.admin.model.dto.UpdateSignDTO;
import com.xhqb.spectre.admin.model.vo.ChannelAccountEnumVO;
import com.xhqb.spectre.admin.model.vo.SignEnumVO;
import com.xhqb.spectre.admin.model.vo.SignVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.SignQuery;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 11:20
 * @Description:
 */
public interface SignService {

    CommonPager<SignVO> listByPage(SignQuery signQuery);

    SignVO getById(Integer id);

    void create(AddSignDTO addSignDTO);

    void update(UpdateSignDTO updateSignDTO);

    void enable(Integer id);

    void disable(Integer id);

    void delete(Integer id);

    List<SignEnumVO> queryEnum(Integer status);

    int findOrCreateMarketScene(String signCode,String name);
}
