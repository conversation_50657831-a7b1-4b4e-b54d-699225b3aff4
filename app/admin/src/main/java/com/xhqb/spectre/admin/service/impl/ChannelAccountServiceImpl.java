package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.constant.Apis;
import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.enums.ChannelAccountOpTypeEnum;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.ChannelAccountDTO;
import com.xhqb.spectre.admin.model.dto.CmppStatusDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ChannelAccountEnumVO;
import com.xhqb.spectre.admin.model.vo.ChannelAccountVO;
import com.xhqb.spectre.admin.model.vo.CmppAliveInfoVO;
import com.xhqb.spectre.admin.model.vo.CmppOpStatusVO;
import com.xhqb.spectre.admin.service.ChannelAccountService;
import com.xhqb.spectre.admin.util.PathUtils;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.dto.ChannelAccountRedisData;
import com.xhqb.spectre.common.dal.entity.*;
import com.xhqb.spectre.common.dal.mapper.*;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ChannelAccountQuery;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 11:22
 * @Description:
 */
@Slf4j
@Service
public class ChannelAccountServiceImpl implements ChannelAccountService {

    /**
     * 合法的长度
     */
    private static final int VALID_LENGTH = 2;

    @Autowired
    private ChannelAccountMapper channelAccountMapper;

    @Autowired
    private ChannelMapper channelMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private TplChannelMapper tplChannelMapper;

    @Autowired
    private TplMapper tplMapper;
    @Resource
    private RestTemplate restTemplate;
    @Autowired
    private VenusConfig venusConfig;

    @Resource
    private ChannelAccountLogMapper channelAccountLogMapper;

    /**
     * 查询账号枚举
     *
     * @param status
     * @return
     */
    @Override
    public List<ChannelAccountEnumVO> queryEnum(Integer status) {
        return channelAccountMapper.selectEnum(status).stream().map(ChannelAccountEnumVO::buildChannelAccountEnumVO).collect(Collectors.toList());
    }

    /**
     * 渠道账号查询列表
     *
     * @param channelAccountQuery
     * @return
     */
    @Override
    public CommonPager<ChannelAccountVO> listByPage(ChannelAccountQuery channelAccountQuery) {
        return PageResultUtils.result(
            () -> channelAccountMapper.countByQuery(channelAccountQuery),
            () -> channelAccountMapper.selectByQuery(channelAccountQuery).stream().map(ChannelAccountVO::buildListQuery).collect(Collectors.toList())
        );
    }

    /**
     * 根据ID查询渠道账号详情信息
     *
     * @param id
     * @return
     */
    @Override
    public ChannelAccountVO getById(Integer id) {
        ChannelAccountDO channelAccountDO = validateAndSelectById(id);
        return ChannelAccountVO.buildInfoQuery(channelAccountDO);
    }

    /**
     * 保存渠道账号信息
     *
     * @param channelAccountDTO
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void create(ChannelAccountDTO channelAccountDTO) {
        //参数校验
        checkCommonParam(channelAccountDTO);

        // 构建渠道账号信息
        ChannelAccountDO channelAccountDO = buildChannelAccountDO(channelAccountDTO, null, true);
        channelAccountMapper.insertSelective(channelAccountDO);

        // 记录操作流水 2022-04-14
        saveChannelAccountLog(channelAccountDO,ChannelAccountOpTypeEnum.ADD);

        // 写入缓存
        channelAccountToRedis(channelAccountDO);
    }

    /**
     * 更新渠道账号信息
     *
     * @param id
     * @param channelAccountDTO
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void update(Integer id, ChannelAccountDTO channelAccountDTO) {
        checkCommonParam(channelAccountDTO);
        // 检测渠道账号信息是否存在
        ChannelAccountDO dbAccountDO = validateAndSelectById(id);
        // 构建渠道账号信息
        ChannelAccountDO channelAccountDO = buildChannelAccountDO(channelAccountDTO, id, false);
        channelAccountMapper.updateByPrimaryKeySelective(channelAccountDO);
        channelAccountDO.setStatus(dbAccountDO.getStatus());

        // 获取到最新的渠道账号信息
        dbAccountDO = validateAndSelectById(id);
        // 记录操作流水 2022-04-14
        saveChannelAccountLog(dbAccountDO,ChannelAccountOpTypeEnum.UPDATE);

        // 写入缓存
        channelAccountToRedis(channelAccountDO);
    }

    /**
     * 启用渠道账号信息
     *
     * @param id
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void enable(Integer id) {
        ChannelAccountDO channelAccountDO = validateAndSelectById(id);
        if (!isDisabled(channelAccountDO)) {
            throw new BizException("渠道账号不处于停用状态，不能启用");
        }
        // 判断渠道是否存在
        checkChannel(channelAccountDO.getChannelCode());

        // 修改渠道账户状态为启用状态
        ChannelAccountDO enableDO = new ChannelAccountDO();
        enableDO.setId(channelAccountDO.getId());
        enableDO.setStatus(CommonConstant.STATUS_VALID);
        enableDO.setUpdater(SsoUserInfoUtil.getUserName());
        // 更新渠道用户账号为有效状态
        channelAccountMapper.updateByPrimaryKeySelective(enableDO);

        // 将最新的渠道账号信息写入到日志记录表
        channelAccountDO = validateAndSelectById(id);

        // 记录操作流水 2022-04-14
        saveChannelAccountLog(channelAccountDO,ChannelAccountOpTypeEnum.ENABLE);

        // 写入缓存
        channelAccountToRedis(channelAccountDO);
    }

    /**
     * 停用渠道账号信息
     *
     * @param id
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void disable(Integer id) {
        ChannelAccountDO channelAccountDO = validateAndSelectById(id);
        if (!isEnabled(channelAccountDO)) {
            throw new BizException("渠道账号不处于启用状态，不能停用");
        }
        //判断是否关联了可用的模板
        List<Integer> tplIdList = tplChannelMapper.selectByChannelAccountId(id).stream().map(TplChannelDO::getTplId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tplIdList)) {
            List<String> tplCodeList = tplMapper.selectByIdList(tplIdList).stream().filter(item -> item.getStatus().equals(CommonConstant.STATUS_VALID))
                .map(TplDO::getCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(tplCodeList)) {
                throw new BizException("有模板引用了该渠道账号，不能停用。模板列表：" + String.join("、", tplCodeList));
            }
        }

        // 修改渠道账户状态为停用状态
        ChannelAccountDO enableDO = new ChannelAccountDO();
        enableDO.setId(channelAccountDO.getId());
        enableDO.setStatus(CommonConstant.STATUS_INVALID);
        enableDO.setUpdater(SsoUserInfoUtil.getUserName());
        // 更新渠道用户账号为有效状态
        channelAccountMapper.updateByPrimaryKeySelective(enableDO);

        // 将最新的渠道账号信息写入到日志记录表
        channelAccountDO = validateAndSelectById(id);

        // 记录操作流水 2022-04-14
        saveChannelAccountLog(channelAccountDO,ChannelAccountOpTypeEnum.DISABLE);

        // 写入缓存
        channelAccountToRedis(channelAccountDO);
    }

    /**
     * 刷新渠道账号查询
     *
     * @param id
     * @param pageSize
     * @return
     */
    @Override
    public List<ChannelAccountDO> refreshCacheQuery(Integer id, Integer pageSize) {
        if (Objects.isNull(id)) {
            return null;
        }
        return channelAccountMapper.refreshCacheQuery(id, pageSize);
    }

    /**
     * 将渠道账号写入redis缓存
     *
     * @param channelAccountDO
     */
    @Override
    public void channelAccountToRedis(ChannelAccountDO channelAccountDO) {
        ChannelAccountRedisData channelAccountRedisData = ChannelAccountRedisData.buildRedisInfo(channelAccountDO);
        redisTemplate.opsForHash().put(RedisKeys.ChannelAccountKeys.CHANNEL_ACCOUNT_HASH_KEY, String.valueOf(channelAccountDO.getId()), JSON.toJSONString(channelAccountRedisData));
    }

    /**
     * 查询Cmpp账号的在线状态
     *
     * @param id
     * @return
     */
    @Override
    public List<CmppAliveInfoVO> queryCmppAliveInfo(Integer id) {
        validateAndSelectById(id);
        try {
            // cmpp ip 缓存key
            clearInvalidCmppIp(id);
        } catch (Exception e) {
            log.error("清除cmpp ip缓存失败, id = {}", id, e);
        }

        String aliveHashKey = MessageFormat.format(RedisKeys.CmppKeys.CHANNEL_ACCOUNT_STATUS_KEY, id);
        // key -> ip , value -> status,timestamp
        Map<String, String> cmppIpMap = redisTemplate.opsForHash().entries(aliveHashKey);
        if (Objects.isNull(cmppIpMap) || cmppIpMap.isEmpty()) {
            log.info("未查询到cmpp ip信息，id = {}, aliveHashKey = {}", id, aliveHashKey);
            return null;
        }

        return this.getCmppAliveInfoList(cmppIpMap, id);
    }

    /**
     * cmpp账号上线
     *
     * @param cmppStatusDTO
     * @return
     */
    @Override
    public AdminResult cmppOnline(CmppStatusDTO cmppStatusDTO) {
        Integer channelAccountId = cmppStatusDTO.getChannelAccountId();
        String cmppHostname = cmppStatusDTO.getCmppHostname();
        CmppOpStatusVO cmppOpStatusVO;
        long start = System.currentTimeMillis();
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        // 请勿轻易改变此提交方式，大部分的情况下，提交方式都是表单提交
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        HttpEntity<String> requestEntity = new HttpEntity<>("{}", headers);
        String channelRefreshApi = Apis.HTTP + cmppHostname + ":" + venusConfig.getReceiptCmppPort() + PathUtils.replace(Apis.ReceiptCmpp.CHANNEL_REFRESH_API, ImmutableMap.of("channelAccountId", channelAccountId + ""));
        ResponseEntity<String> response = null;
        try {
            response = restTemplate.exchange(channelRefreshApi, HttpMethod.POST, requestEntity, String.class);
            log.info("cmpp账号上线响应耗时 = {}, response = {}, channelAccountId = {},channelRefreshApi = {}", (System.currentTimeMillis() - start), response, channelAccountId, channelRefreshApi);
        } catch (Exception e) {
            log.error("cmpp账号上线失败, channelAccountId = {}, channelRefreshApi = {}", channelAccountId, channelRefreshApi, e);
        }

        boolean opStatus = false;
        cmppOpStatusVO = CmppOpStatusVO.builder()
            .hostname(cmppHostname)
            .build();
        if (Objects.isNull(response) || !Objects.equals(response.getStatusCode(), HttpStatus.OK)) {
            cmppOpStatusVO.setDescription("cmpp账号上线操作失败");
        } else {
            opStatus = cmppOpResultWrap(response.getBody(), cmppOpStatusVO);
        }

        cmppOpStatusVO.setOpStatus(opStatus ? 1 : 0);
        if (Objects.equals(opStatus, true)) {
            return AdminResult.success(cmppOpStatusVO);
        }

        // cmpp账号上线失败
        AdminResult adminResult = AdminResult.error("cmpp账号上线操作失败");
        adminResult.setData(cmppOpStatusVO);
        return adminResult;
    }

    /**
     * cmpp账号下线
     *
     * @param cmppStatusDTO
     * @return
     */
    @Override
    public AdminResult cmppOffline(CmppStatusDTO cmppStatusDTO) {
        Integer channelAccountId = cmppStatusDTO.getChannelAccountId();
        String cmppHostname = cmppStatusDTO.getCmppHostname();

        long start = System.currentTimeMillis();
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        // 请勿轻易改变此提交方式，大部分的情况下，提交方式都是表单提交
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        HttpEntity<String> requestEntity = new HttpEntity<>("{}", headers);
        String channelOfflineApi = Apis.HTTP + cmppHostname + ":" + venusConfig.getReceiptCmppPort() + PathUtils.replace(Apis.ReceiptCmpp.CHANNEL_OFFLINE_API, ImmutableMap.of("channelAccountId", channelAccountId + ""));
        ResponseEntity<String> response = null;
        try {
            response = restTemplate.exchange(channelOfflineApi, HttpMethod.POST, requestEntity, String.class);
            log.info("cmpp账号下线响应耗时 = {}, response = {}, channelAccountId = {},channelOfflineApi = {}", (System.currentTimeMillis() - start), response, channelAccountId, channelOfflineApi);
        } catch (Exception e) {
            log.info("cmpp账号下线失败,channelAccountId ={}, channelOfflineApi = {}", channelAccountId, channelOfflineApi, e);
        }

        boolean opStatus = false;
        CmppOpStatusVO cmppOpStatusVO = CmppOpStatusVO.builder()
            .hostname(cmppHostname)
            .build();
        if (Objects.isNull(response) || !Objects.equals(response.getStatusCode(), HttpStatus.OK)) {
            cmppOpStatusVO.setDescription("cmpp账号下线操作失败");
        } else {
            opStatus = cmppOpResultWrap(response.getBody(), cmppOpStatusVO);
        }

        cmppOpStatusVO.setOpStatus(opStatus ? 1 : 0);
        if (Objects.equals(opStatus, true)) {
            return AdminResult.success(cmppOpStatusVO);
        }

        // cmpp账号上线失败
        AdminResult adminResult = AdminResult.error("cmpp账号下线操作失败");
        adminResult.setData(cmppOpStatusVO);
        return adminResult;
    }

    /**
     * 初始化流水信息
     *
     * @return
     */
    @Override
    public AdminResult copy2Log() {
        int count = channelAccountLogMapper.selectCount();
        if(count > 0) {
            return AdminResult.error("数据已经初始化,不需要重复执行");
        }

        List<ChannelAccountDO> channelAccountList = channelAccountMapper.selectAll();
        for(ChannelAccountDO channelAccountDO : channelAccountList) {
            saveChannelAccountLog(channelAccountDO,ChannelAccountOpTypeEnum.ADD);
        }

        return AdminResult.success("数据初始化成功");
    }

    /**
     * cmpp 操作响应结果
     *
     * @param body
     * @param cmppOpStatusVO
     * @return true表示成功
     */
    private boolean cmppOpResultWrap(String body, CmppOpStatusVO cmppOpStatusVO) {
        JSONObject jsonObject = JSON.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (Objects.equals(success, true)) {
            cmppOpStatusVO.setDescription("Success");
            return true;
        }
        cmppOpStatusVO.setDescription(jsonObject.getString("resultCode") + "_" + jsonObject.getString("resultMsg"));
        return false;
    }

    /**
     * 校验请求参数
     *
     * @param channelAccountDTO
     */
    private void checkCommonParam(ChannelAccountDTO channelAccountDTO) {
        //参数格式校验
        ValidatorUtil.validate(channelAccountDTO);
        if (!MessageTypeEnum.contains(channelAccountDTO.getSmsTypeCode())) {
            throw new BizException("不支持该短信类型编码");
        }

        // 检测渠道信息是否存在
        checkChannel(channelAccountDTO.getChannelCode());
    }

    /**
     * 检测渠道信息是否存在
     *
     * @param channelCode
     */
    private void checkChannel(String channelCode) {
        ChannelDO channelDO = channelMapper.selectByCode(channelCode);
        if (Objects.isNull(channelDO)) {
            throw new BizException("渠道信息不存在");
        }
    }

    /**
     * 构建渠道账号数据库对象
     *
     * @param channelAccountDTO
     * @param id
     * @param isAdd
     * @return
     */
    private ChannelAccountDO buildChannelAccountDO(ChannelAccountDTO channelAccountDTO, Integer id, boolean isAdd) {
        String userName = SsoUserInfoUtil.getUserName();
        ChannelAccountDO channelAccountDO = ChannelAccountDO.builder()
            // 渠道编码
            .channelCode(channelAccountDTO.getChannelCode())
            // 短信类型编码
            .smsTypeCode(channelAccountDTO.getSmsTypeCode())
            // 账号名称
            .name(channelAccountDTO.getName())
            // 渠道账号
            .key(StringUtils.trim(channelAccountDTO.getKey()))
            // 渠道账号json配置参数
            .jsonMapping(channelAccountDTO.getJsonMapping())
            // 费率，千分位存储
            .price(channelAccountDTO.getPrice())
            // 运营商
            .isps(String.join(",", channelAccountDTO.getIspList()))
            // 地域过滤类型，1：包含；2：不包含
            .areaFilterType(channelAccountDTO.getAreaFilterType())
            // 地域列表，json数组结构
            .areas(JSON.toJSONString(channelAccountDTO.getAreaList()))
            // 协议，1：http；2：cmpp
            .protocol(channelAccountDTO.getProtocol())
            // 权重
            .weight(channelAccountDTO.getWeight())
            // 是否免模板审核，1：是；0：否
            .isTplFreeAudit(channelAccountDTO.getIsTplFreeAudit())
            // 支持的签名ID列表
            .signIds(channelAccountDTO.getSignIdList().stream().map(String::valueOf).collect(Collectors.joining(",")))
            // 备注
            .remark(channelAccountDTO.getRemark())
            .updater(userName)
            .build();
        if (isAdd) {
            // 状态，0：无效，1：有效
            channelAccountDO.setStatus(CommonConstant.STATUS_INVALID);
            channelAccountDO.setCreator(userName);
        } else {
            channelAccountDO.setId(id);
        }
        return channelAccountDO;
    }


    /**
     * 根据ID查询渠道账号信息
     *
     * @param id
     * @return
     */
    private ChannelAccountDO validateAndSelectById(Integer id) {
        ChannelAccountDO channelAccountDO = channelAccountMapper.selectByPrimaryKey(id);
        if (Objects.isNull(channelAccountDO)) {
            throw new BizException("未找到该渠道账号信息");
        }
        return channelAccountDO;
    }

    /**
     * 当前渠道账号是否处于启用状态
     *
     * @param channelAccountDO
     * @return
     */
    private boolean isEnabled(ChannelAccountDO channelAccountDO) {
        return channelAccountDO.getStatus().equals(CommonConstant.STATUS_VALID);
    }

    /**
     * 当前渠道账号是否处于停用状态
     *
     * @param channelAccountDO
     * @return
     */
    private boolean isDisabled(ChannelAccountDO channelAccountDO) {
        return channelAccountDO.getStatus().equals(CommonConstant.STATUS_INVALID);
    }

    /**
     * 获取到cmpp 机器信息列表
     * BOOTING(0L, "启动中"),
     * WORKING(1L, "运行中"),
     * SHUTDOWN(2L, "被动下线"),
     * ACCOUNT_ERROR(3L, "账号异常"),
     * OFFLINE(4L, "手动下线");
     *
     * @param cmppIpMap
     * @param id        账号ID
     * @return
     */
    private List<CmppAliveInfoVO> getCmppAliveInfoList(Map<String, String> cmppIpMap, Integer id) {
        Integer cmppStatus;
        Set<Map.Entry<String, String>> entries = cmppIpMap.entrySet();
        // 返回结果
        List<CmppAliveInfoVO> cmppList = new ArrayList<>(entries.size());
        for (Map.Entry<String, String> entry : entries) {
            String ip = entry.getKey();
            if (StringUtils.isBlank(ip)) {
                continue;
            }
            // 状态,时间搓
            String value = entry.getValue();
            if (StringUtils.isBlank(value)) {
                log.info("cmpp ip数据为空,ip ={}, value = {}, id = {}", ip, value, id);
                continue;
            }

            String[] split = StringUtils.split(value, ",");
            if (split.length != VALID_LENGTH) {
                log.info("cmpp ip数据不合法,ip ={}, value = {}, id = {}", ip, value, id);
                continue;
            }

            String status = split[0];
            String timestamp = split[1];
            // 判断时间搓是否过期，过期则删除
//            try {
//                int delta = (int) ((Long.parseLong(timestamp) - System.currentTimeMillis()) / 1000);
//                if (Math.abs(delta) > venusConfig.getCmppStatusThresholdSecond()) {
//                    log.info("cmpp ip数据已过期,ip ={}, value = {}, id = {},delta = {}", ip, value, id, delta);
//                    continue;
//                }
//            } catch (Exception e) {
//                log.error("cmpp ip数据timestamp不合法,ip ={}, value = {}, id = {}", ip, value, id, e);
//                continue;
//            }
            try {
                cmppStatus = Integer.parseInt(status);
            } catch (Exception e) {
                cmppStatus = 9;
                log.error("转换cmpp状态值失败,ip = {},value = {}, id = {}", ip, value, id, e);
            }

            Integer second = null;
            try {
                second = StringUtils.isNotBlank(timestamp) ? (int) (Long.parseLong(timestamp) / 1000) : null;
            } catch (Exception e) {
                log.error("转换cmpp时间失败,ip = {} timestamp ={}", ip, timestamp, e);
            }

            cmppList.add(CmppAliveInfoVO.buildCmppAliveInfoVO(ip, second, cmppStatus));
        }
        return cmppList;
    }

    /**
     * 清除无效的cmpp ip地址
     * <p>
     * 参考： com.xhqb.spectre.cmpp.cmpp20.client.ChannelManager.clearIp()
     *
     * @return 返回有效的ip
     */
    public Set<String> clearInvalidCmppIp(Integer id) {

        Long stamp = System.currentTimeMillis();
        // 账号缓存信息
        Set<String> keys = redisTemplate.opsForHash().keys(RedisKeys.ChannelAccountKeys.CHANNEL_ACCOUNT_HASH_KEY);
        if (Objects.isNull(keys)) {
            return null;
        }

        Set<String> ipSet = new HashSet<>();
        Set<String> activeSet = new HashSet<>();
        for (String accountId : keys) {
            String key = MessageFormat.format(RedisKeys.CmppKeys.CHANNEL_ACCOUNT_STATUS_KEY, accountId);
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
            entries.forEach((i, v) -> {
                ipSet.add(i.toString());
                String[] a = v.toString().split(",");
                if (Arrays.asList(a).size() < 2 || Long.valueOf(a[1]) > stamp - 180000L) {
                    activeSet.add(i.toString());
                }
            });
        }

        if (activeSet.size() > 0) {
            ipSet.removeAll(activeSet);
            for (String accountId : keys) {
                String statusKey = MessageFormat.format(RedisKeys.CmppKeys.CHANNEL_ACCOUNT_STATUS_KEY, accountId);
                for (Object anIpSet : ipSet) {
                    redisTemplate.opsForHash().delete(statusKey, anIpSet);
                }
            }

            //新创建渠道,添加缓存
            String currentStatusKey = MessageFormat.format(RedisKeys.CmppKeys.CHANNEL_ACCOUNT_STATUS_KEY, id);
            Map<Object, Object> map = redisTemplate.opsForHash().entries(currentStatusKey);
            if (map.size() == 0) {
                String currentTimeMillis = String.valueOf(System.currentTimeMillis());
                for (String activeIp : activeSet) {
                    redisTemplate.opsForHash().put(currentStatusKey, activeIp,
                        "4," + currentTimeMillis);
                }
            }
        }

        return ipSet;
    }

    /**
     * 保存渠道账号操作流水
     *
     * @param channelAccountDO
     * @param opType
     */
    private void saveChannelAccountLog(ChannelAccountDO channelAccountDO, ChannelAccountOpTypeEnum opType) {
        try {
            doSaveChannelAccountLog(channelAccountDO, opType);
        } catch (Exception e) {
            log.error("保存渠道账号操作流水失败, channelAccountDO = {}", JSON.toJSONString(channelAccountDO), e);
        }
    }

    /**
     * 具体保存操作账号操作流水逻辑
     *
     * @param channelAccountDO
     * @param opType
     */
    private void doSaveChannelAccountLog(ChannelAccountDO channelAccountDO, ChannelAccountOpTypeEnum opType) {
        ChannelAccountLogDO channelAccountLogDO = JSON.parseObject(JSON.toJSONString(channelAccountDO), ChannelAccountLogDO.class);
        channelAccountLogDO.setId(null);
        channelAccountLogDO.setCreateTime(null);
        channelAccountLogDO.setUpdateTime(null);
        channelAccountLogDO.setChannelAccountId(channelAccountDO.getId());
        channelAccountLogDO.setOpType(opType.getCode());
        channelAccountLogMapper.insertSelective(channelAccountLogDO);
    }


}
