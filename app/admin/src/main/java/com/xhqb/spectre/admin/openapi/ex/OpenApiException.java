package com.xhqb.spectre.admin.openapi.ex;

/**
 * <AUTHOR>
 * @date 2021/12/15
 */
public class OpenApiException extends RuntimeException{


    public OpenApiException() {
    }

    public OpenApiException(String message) {
        super(message);
    }

    public OpenApiException(String message, Throwable cause) {
        super(message, cause);
    }

    public OpenApiException(Throwable cause) {
        super(cause);
    }

    public OpenApiException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
