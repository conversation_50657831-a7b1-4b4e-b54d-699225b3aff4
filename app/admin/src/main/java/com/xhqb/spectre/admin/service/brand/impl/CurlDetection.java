package com.xhqb.spectre.admin.service.brand.impl;

import com.xhqb.spectre.admin.service.brand.BrandDetectionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * curl品牌
 */
@Component
@Slf4j
public class CurlDetection implements BrandDetectionStrategy {
    @Override
    public boolean isMatch(String brand, String os) {
        return brand.toLowerCase().startsWith("curl");
    }

    @Override
    public String getMappedBrand() {
        return "curl";
    }
}