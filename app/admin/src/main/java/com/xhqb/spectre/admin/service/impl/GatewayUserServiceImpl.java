package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.GatewayUserDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.GatewayUserVO;
import com.xhqb.spectre.admin.service.GatewayUserService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.GatewayUserDO;
import com.xhqb.spectre.common.dal.mapper.GatewayUserMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.GatewayUserQuery;
import com.xhqb.spectre.common.enums.DeleteEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 网关账号管理(王建政)
 *
 * <AUTHOR>
 * @date 2021/11/8
 */
@Service
public class GatewayUserServiceImpl implements GatewayUserService {

    @Resource
    private GatewayUserMapper gatewayUserMapper;

    /**
     * 分页查询网关分配账号列表
     *
     * @param gatewayUserQuery
     * @return
     */
    @Override
    public CommonPager<GatewayUserVO> listByPage(GatewayUserQuery gatewayUserQuery) {
        return PageResultUtils.result(
                () -> gatewayUserMapper.countByQuery(gatewayUserQuery),
                () -> gatewayUserMapper.selectByQuery(gatewayUserQuery).stream().map(GatewayUserVO::buildListQuery).collect(Collectors.toList())
        );
    }

    /**
     * 根据ID查询网关账号详情
     *
     * @param id
     * @return
     */
    @Override
    public GatewayUserVO getById(Integer id) {
        GatewayUserDO gatewayUserDO = selectById(id);
        return GatewayUserVO.buildInfoQuery(gatewayUserDO);
    }

    /**
     * 添加网关账号信息
     *
     * @param gatewayUserDTO
     */
    @Override
    public void create(GatewayUserDTO gatewayUserDTO) {
        ValidatorUtil.validate(gatewayUserDTO);
        if(StringUtils.isBlank(gatewayUserDTO.getUserName())){
            throw new BizException("企业账号不能为空");
        }

        GatewayUserDO gatewayUserDO = GatewayUserDTO.toDO(gatewayUserDTO);
        gatewayUserDO.setCreator(SsoUserInfoUtil.getUserName());
        gatewayUserMapper.insertSelective(gatewayUserDO);
    }

    /**
     * 更新网关账号信息
     *
     * @param id
     * @param gatewayUserDTO
     */
    @Override
    public void update(Integer id, GatewayUserDTO gatewayUserDTO) {
        ValidatorUtil.validate(gatewayUserDTO);
        selectById(id);

        GatewayUserDO gatewayUserDO = GatewayUserDTO.toDO(gatewayUserDTO);
        gatewayUserDO.setId(id);
        //不更新用户名和模板编码
        gatewayUserDO.setUserName(null);
        gatewayUserDO.setTplCode(null);
        gatewayUserMapper.updateByPrimaryKeySelective(gatewayUserDO);
    }

    /**
     * 删除网关账号信息
     *
     * @param id
     * @return
     */
    @Override
    public AdminResult delete(Integer id) {
        GatewayUserDO gatewayUserDO = selectById(id);
        GatewayUserDO result = new GatewayUserDO();
        result.setId(gatewayUserDO.getId());
        result.setIsDelete(DeleteEnum.DELETED.getCode());
        result.setUpdater(SsoUserInfoUtil.getUserName());
        gatewayUserMapper.updateByPrimaryKeySelective(result);
        return AdminResult.success();
    }

    /**
     * 根据ID查询网关账号信息
     *
     * @param id
     * @return
     */
    private GatewayUserDO selectById(Integer id) {
        GatewayUserDO gatewayUserDO = gatewayUserMapper.selectByPrimaryKey(id);
        if (Objects.isNull(gatewayUserDO)) {
            throw new BizException("未找到网关账号信息");
        }
        return gatewayUserDO;
    }
}
