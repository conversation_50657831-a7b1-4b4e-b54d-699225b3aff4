package com.xhqb.spectre.admin.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * cmpp 操作状态信息
 *
 * <AUTHOR>
 * @date 2021/10/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmppOpStatusVO implements Serializable {

    /**
     * 主机地址
     */
    private String hostname;
    /**
     * 操作状态  1->成功 0->失败
     */
    private Integer opStatus;
    /**
     * 描述信息
     */
    private String description;
}
