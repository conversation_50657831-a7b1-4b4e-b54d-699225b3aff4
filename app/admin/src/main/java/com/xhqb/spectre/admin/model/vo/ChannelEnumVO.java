package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.ChannelDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 11:25
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelEnumVO implements Serializable {

    private static final long serialVersionUID = 6154341641273378819L;

    private String code;

    private String name;

    public static ChannelEnumVO buildChannelEnumVO(ChannelDO channelDO) {
        return ChannelEnumVO.builder()
                .code(channelDO.getCode())
                .name(channelDO.getName())
                .build();
    }
}
