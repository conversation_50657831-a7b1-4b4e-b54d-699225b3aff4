package com.xhqb.spectre.admin.statistics.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SmsStatisByDayKey implements Serializable {

    private static final long serialVersionUID = 6572951561717969632L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.date
     *
     * @mbggenerated
     */
    private Date date;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.class1
     *
     * @mbggenerated
     */
    private Integer class1;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.class2
     *
     * @mbggenerated
     */
    private String class2;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.class3
     *
     * @mbggenerated
     */
    private String class3;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.class4
     *
     * @mbggenerated
     */
    private String class4;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.class5
     *
     * @mbggenerated
     */
    private String class5;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.class6
     *
     * @mbggenerated
     */
    private String class6;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.class7
     *
     * @mbggenerated
     */
    private String class7;

}