package com.xhqb.spectre.admin.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.service.SmsStatisticsService;
import com.xhqb.spectre.admin.statistics.entity.ClassEnumDO;
import com.xhqb.spectre.admin.statistics.entity.SmsHisStatisDO;
import com.xhqb.spectre.admin.statistics.mapper.SmsHisStatisMapper;
import com.xhqb.spectre.admin.statistics.mapper.SmsStatisByDayMapper;
import com.xhqb.spectre.admin.statistics.mapper.SmsStatisByHourMapper;
import com.xhqb.spectre.admin.statistics.query.SmsStatisClassQuery;
import com.xhqb.spectre.admin.statistics.query.SmsStatisQuery;
import com.xhqb.spectre.admin.statistics.vo.*;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.ChannelDO;
import com.xhqb.spectre.common.dal.mapper.ChannelMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/28 15:58
 * @Description:
 */
@Service
@Slf4j
public class SmsStatisticsServiceImpl implements SmsStatisticsService {

    @Autowired
    private SmsStatisByDayMapper statisByDayMapper;

    @Autowired
    private SmsStatisByHourMapper statisByHourMapper;

    @Autowired
    private SmsHisStatisMapper hisStatisMapper;

    @Autowired
    private ChannelMapper channelMapper;

    /**
     * 按天统计的数据缓存（T+1更新，因此可以缓存）
     */
    private final Cache<String, Object> dataCache = CacheBuilder.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    /**
     * 渠道信息缓存，用于转换渠道编码
     */
    private final Cache<String, Map<String, String>> channelCache = CacheBuilder.newBuilder()
            .maximumSize(5)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    /**
     * 按天查询数据汇总
     *
     * @param query
     * @return
     */
    @Override
    public List<SmsStatisSumVO> queryByDay(SmsStatisQuery query) {
        //参数校验
        checkParam(query.getStartDate(), query.getEndDate(), 1);

        //先从缓存获取
        String cacheKey = buildCacheKey(query, "Day");
        List<SmsStatisSumVO> list = (List<SmsStatisSumVO>) dataCache.getIfPresent(cacheKey);
        if (Objects.nonNull(list)) {
            return list;
        }

        //获取所有月份时间点
        List<String> dateList = DateUtil.completeDateList(query.getStartDate(), query.getEndDate());

        //获取汇总数据
        Map<String, SmsStatisSumVO> dataMap = statisByDayMapper.selectSumList(query)
                .stream().map(SmsStatisSumVO::build).collect(Collectors.toMap(SmsStatisSumVO::getDate, Function.identity()));

        //补全每个时间点的数据，没有数据时设置为0
        list = new ArrayList<>();
        for (String date : dateList) {
            if (dataMap.containsKey(date)) {
                list.add(dataMap.get(date));
            } else {
                list.add(new SmsStatisSumVO(date, 0, 0));
            }
        }
        dataCache.put(cacheKey, list);

        return list;
    }

    /**
     * 按天查询数据统计列表，分页查询
     *
     * @param query
     * @return
     */
    @Override
    public CommonPager<SmsStatisDetailVO> queryPageByDay(SmsStatisQuery query) {
        //先从缓存获取
        String cacheKey = buildCacheKey(query, "Day", query.getPageParameter());
        CommonPager<SmsStatisDetailVO> commonPager = (CommonPager<SmsStatisDetailVO>) dataCache.getIfPresent(cacheKey);
        if (Objects.nonNull(commonPager)) {
            return commonPager;
        }

        //查询数据
        String channelName = getChannelName(query.getChannelCode());
        commonPager = PageResultUtils.result(
                () -> statisByDayMapper.selectSumListCount(query),
                () -> statisByDayMapper.selectSumListByPage(query).stream()
                        .map(item -> SmsStatisDetailVO.build(item, query, channelName)).collect(Collectors.toList())
        );
        dataCache.put(cacheKey, commonPager);

        return commonPager;
    }

    /**
     * 按月查询数据汇总
     *
     * @param query
     * @return
     */
    @Override
    public List<SmsStatisSumVO> queryByMonth(SmsStatisQuery query) {
        //参数校验
        checkParam(query.getStartDate(), query.getEndDate(), 3);

        //先从缓存获取
        String cacheKey = buildCacheKey(query, "Month");
        List<SmsStatisSumVO> list = (List<SmsStatisSumVO>) dataCache.getIfPresent(cacheKey);
        if (Objects.nonNull(list)) {
            return list;
        }

        //获取所有月份时间点
        List<String> monthList = DateUtil.completeMonthList(query.getStartDate(), query.getEndDate());

        //获取汇总数据
        query.setStartDate(query.getStartDate() + "-01");
        String lastDayOfMonth = DateUtil.getLastDayOfMonth(query.getEndDate());
        if (StringUtils.isEmpty(lastDayOfMonth)) {
            throw new BizException("系统异常");
        }
        query.setEndDate(lastDayOfMonth);
        Map<String, SmsStatisSumVO> dataMap = statisByDayMapper.selectSumListByMonth(query)
                .stream().map(SmsStatisSumVO::build).collect(Collectors.toMap(SmsStatisSumVO::getDate, Function.identity()));

        //补全每个时间点的数据，没有数据时设置为0
        list = new ArrayList<>();
        for (String month : monthList) {
            if (dataMap.containsKey(month)) {
                list.add(dataMap.get(month));
            } else {
                list.add(new SmsStatisSumVO(month, 0, 0));
            }
        }
        dataCache.put(cacheKey, list);

        return list;
    }

    /**
     * 查询时间范围内某一类型的数据汇总
     *
     * @param query
     * @return
     */
    @Override
    public List<SmsStatisClassSumVO> queryClassSumByDay(SmsStatisClassQuery query) {
        //参数校验
        checkParam(query, 1);

        //先从缓存读取
        String keyPrefix = "SumVO";
        String cacheKey = buildCacheKey(query, keyPrefix);
        List<SmsStatisClassSumVO> list = (List<SmsStatisClassSumVO>) dataCache.getIfPresent(cacheKey);
        if (Objects.nonNull(list)) {
            return list;
        }

        String columnName = "class" + query.getType();
        list = statisByDayMapper.selectClassSum(query.getStartDate(), query.getEndDate(), columnName)
                .stream().map(item -> {
                    if (isChannelType(query.getType())) {
                        item.setClassValue(getChannelName(item.getClassValue()));
                    }
                    return SmsStatisClassSumVO.build(item);
                }).collect(Collectors.toList());
        dataCache.put(cacheKey, list);

        return list;
    }

    /**
     * 按天查询某一类型的数据统计
     *
     * @param query
     * @return
     */
    @Override
    public List<SmsStatisClassSumListVO> queryClassSumListByDay(SmsStatisClassQuery query) {
        //参数校验
        checkParam(query, 1);

        //先从缓存读取
        String keyPrefix = "SumListVO";
        String cacheKey = buildCacheKey(query, keyPrefix);
        List<SmsStatisClassSumListVO> list = (List<SmsStatisClassSumListVO>) dataCache.getIfPresent(cacheKey);
        if (Objects.nonNull(list)) {
            return list;
        }

        //获取所有日期时间点
        List<String> dateList = DateUtil.completeDateList(query.getStartDate(), query.getEndDate());

        //获取统计维度枚举列表
        List<String> enumList = queryClassEnum(query);

        //获取汇总数据
        List<SmsStatisClassSumListVO> data = statisByDayMapper.selectClassSumList(query.getStartDate(), query.getEndDate(), getClassColumnName(query.getType()))
                .stream().map(item -> {
                    //转换渠道名称
                    if (isChannelType(query.getType())) {
                        item.setClassValue(getChannelName(item.getClassValue()));
                    }
                    return SmsStatisClassSumListVO.build(item);
                }).collect(Collectors.toList());
        Map<String, Map<String, SmsStatisClassSumListVO>> dataMap = buildDataMap(data);

        //补全时间点和维度
        list = new ArrayList<>();
        for (String date : dateList) {
            if (dataMap.containsKey(date)) {
                Map<String, SmsStatisClassSumListVO> innerMap = dataMap.get(date);
                list.addAll(innerMap.values());
                for (String enumValue : enumList) {
                    if (isChannelType(query.getType())) {
                        enumValue = getChannelName(enumValue);
                    }
                    if (!innerMap.containsKey(enumValue)) {
                        list.add(new SmsStatisClassSumListVO(date, enumValue, 0, 0));
                    }
                }
            } else {
                for (String enumValue : enumList) {
                    if (isChannelType(query.getType())) {
                        enumValue = getChannelName(enumValue);
                    }
                    list.add(new SmsStatisClassSumListVO(date, enumValue, 0, 0));
                }
            }
        }
        dataCache.put(cacheKey, list);

        return list;
    }

    /**
     * 查询历史发送统计
     *
     * @return
     */
    @Override
    public Map<String, SmsHisStatisVO> queryHisData() {
        List<SmsHisStatisDO> list = hisStatisMapper.selectAll();
        return list.stream().map(SmsHisStatisVO::buildSmsHisStatisVO).collect(Collectors.toMap(SmsHisStatisVO::getSmsTypeCode, Function.identity()));
    }

    /**
     * 查询各维度枚举
     *
     * @param query
     * @return
     */
    @Override
    public List<String> queryClassEnum(SmsStatisClassQuery query) {
        //参数校验
        checkParam(query, 1);

        //先从缓存读取
        String cacheKey = buildCacheKey(query, "Enum");
        List<String> enumList = (List<String>) dataCache.getIfPresent(cacheKey);
        if (Objects.nonNull(enumList)) {
            return enumList;
        }

        List<ClassEnumDO> list = statisByDayMapper.selectClassEnum(query.getStartDate(), query.getEndDate(), getClassColumnName(query.getType()));
        enumList = list.stream().map(ClassEnumDO::getEnumName).collect(Collectors.toList());
        dataCache.put(cacheKey, enumList);
        return enumList;
    }

    /**
     * 按小时查询数据统计
     *
     * @param query
     * @return
     */
    @Override
    public List<SmsStatisSumVO> queryByHour(SmsStatisQuery query) {
        //参数校验
        checkParam(query.getStartDate(), query.getEndDate(), 2);

        //获取所有小时时间点
        List<String> hourList = DateUtil.completeHourList(query.getStartDate(), query.getEndDate());

        //获取汇总数据
        Map<String, SmsStatisSumVO> dataMap = statisByHourMapper.selectSumList(query)
                .stream().map(SmsStatisSumVO::build).collect(Collectors.toMap(SmsStatisSumVO::getDate, Function.identity()));

        //补全每个时间点的数据，没有数据时设置为0
        List<SmsStatisSumVO> list = new ArrayList<>();
        for (String hour : hourList) {
            if (dataMap.containsKey(hour)) {
                list.add(dataMap.get(hour));
            } else {
                list.add(new SmsStatisSumVO(hour, 0, 0));
            }
        }

        return list;
    }

    /**
     * 按小时查询数据统计，分页查询
     *
     * @param query
     * @return
     */
    @Override
    public CommonPager<SmsStatisDetailVO> queryPageByHour(SmsStatisQuery query) {
        String channelName = getChannelName(query.getChannelCode());
        return PageResultUtils.result(
                () -> statisByHourMapper.selectSumListCount(query),
                () -> statisByHourMapper.selectSumListByPage(query).stream()
                        .map(item -> SmsStatisDetailVO.build(item, query, channelName)).collect(Collectors.toList())
        );
    }

    /**
     * 查询小时时间范围内某一类型的数据统计
     *
     * @param query
     * @return
     */
    @Override
    public List<SmsStatisClassSumVO> queryClassSumByHour(SmsStatisClassQuery query) {
        //参数校验
        checkParam(query, 2);

        return statisByHourMapper.selectClassSum(query.getStartDate(), query.getEndDate(), getClassColumnName(query.getType()))
                .stream().map(item -> {
                    if (isChannelType(query.getType())) {
                        item.setClassValue(getChannelName(item.getClassValue()));
                    }
                    return SmsStatisClassSumVO.build(item);
                }).collect(Collectors.toList());
    }

    /**
     * 按小时查询某一类型的数据统计
     *
     * @param query
     * @return
     */
    @Override
    public List<SmsStatisClassSumListVO> queryClassSumListByHour(SmsStatisClassQuery query) {
        //参数校验
        checkParam(query, 2);

        //获取所有小时时间点
        List<String> hourList = DateUtil.completeHourList(query.getStartDate(), query.getEndDate());

        //获取统计维度枚举列表
        List<String> enumList = queryClassEnum(new SmsStatisClassQuery(query.getStartDate().substring(0, 10), query.getEndDate().substring(0, 10), query.getType()));

        //获取汇总数据
        List<SmsStatisClassSumListVO> data = statisByHourMapper.selectClassSumList(query.getStartDate(), query.getEndDate(), getClassColumnName(query.getType()))
                .stream().map(item -> {
                    if (isChannelType(query.getType())) {
                        item.setClassValue(getChannelName(item.getClassValue()));
                    }
                    return SmsStatisClassSumListVO.build(item);
                }).collect(Collectors.toList());
        Map<String, Map<String, SmsStatisClassSumListVO>> dataMap = buildDataMap(data);

        //补全时间点和维度
        List<SmsStatisClassSumListVO> list = new ArrayList<>();
        for (String hour : hourList) {
            if (dataMap.containsKey(hour)) {
                Map<String, SmsStatisClassSumListVO> innerMap = dataMap.get(hour);
                list.addAll(innerMap.values());
                for (String enumValue : enumList) {
                    if (isChannelType(query.getType())) {
                        enumValue = getChannelName(enumValue);
                    }
                    if (!innerMap.containsKey(enumValue)) {
                        list.add(new SmsStatisClassSumListVO(hour, enumValue, 0, 0));
                    }
                }
            } else {
                for (String enumValue : enumList) {
                    if (isChannelType(query.getType())) {
                        enumValue = getChannelName(enumValue);
                    }
                    list.add(new SmsStatisClassSumListVO(hour, enumValue, 0, 0));
                }
            }
        }

        return list;
    }

    /**
     * 参数校验
     *
     * @param startDate
     * @param endDate
     * @param dateType  时间类型，1：日期；2：时间；3：月份
     */
    private void checkParam(String startDate, String endDate, int dateType) {
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            throw new BizException("开始时间和结束时间不能为空");
        }
        Date start, end;
        if (dateType == 1) {
            start = DateUtil.stringToDate(startDate + " 00:00:00");
            end = DateUtil.stringToDate(endDate + " 00:00:00");
        } else if (dateType == 2) {
            start = DateUtil.hourToDate(startDate);
            end = DateUtil.hourToDate(endDate);
        } else {
            start = DateUtil.stringToDate(startDate + "-01 00:00:00");
            end = DateUtil.stringToDate(endDate + "-01 00:00:00");
        }
        if (Objects.isNull(start) || Objects.isNull(end)) {
            throw new BizException("时间格式有误");
        }
        if (end.before(start)) {
            throw new BizException("开始时间不能大于结束时间");
        }
    }

    private void checkParam(SmsStatisClassQuery query, int dateType) {
        checkParam(query.getStartDate(), query.getEndDate(), dateType);

        Integer type = query.getType();
        if (Objects.isNull(type) || type < 1 || type > 7) {
            throw new BizException("统计维度有误");
        }
    }

    private String getClassColumnName(Integer type) {
        return "class" + type;
    }

    private String buildCacheKey(SmsStatisQuery query, String prefix) {
        return prefix + "_" + query.getStartDate() + "_" + query.getEndDate() + "_" + query.getBatchId() + "_" + query.getSmsTypeCode() + "_"
                + query.getTplCode() + "_" + query.getChannelCode() + "_" + query.getProvince() + "_" + query.getCity() + "_" + query.getIsp();
    }

    private String buildCacheKey(SmsStatisQuery query, String prefix, PageParameter pageParameter) {
        return buildCacheKey(query, prefix) + "_" + pageParameter.getOffset() + "_" + pageParameter.getPageSize();
    }

    private String buildCacheKey(SmsStatisClassQuery query, String prefix) {
        return prefix + "_" + query.getStartDate() + "_" + query.getEndDate() + "_" + query.getType();
    }

    private Map<String, Map<String, SmsStatisClassSumListVO>> buildDataMap(List<SmsStatisClassSumListVO> list) {
        Map<String, Map<String, SmsStatisClassSumListVO>> map = new HashMap<>();
        for (SmsStatisClassSumListVO item : list) {
            String date = item.getDate();
            String classValue = item.getClassValue();
            if (map.containsKey(date)) {
                map.get(date).put(classValue, item);
            } else {
                Map<String, SmsStatisClassSumListVO> tmpMap = new HashMap<>();
                tmpMap.put(classValue, item);
                map.put(date, tmpMap);
            }
        }
        return map;
    }

    /**
     * 获取渠道名称
     *
     * @param channelCode
     * @return
     */
    private String getChannelName(String channelCode) {
        if (StringUtils.isBlank(channelCode)) {
            return channelCode;
        }
        Map<String, String> channelMap = getChannelMap();
        return channelMap.getOrDefault(channelCode, channelCode);
    }

    private Map<String, String> getChannelMap() {
        String cacheKey = "channelMap";
        Map<String, String> channelMap = channelCache.getIfPresent(cacheKey);
        if (Objects.nonNull(channelMap)) {
            return channelMap;
        }
        channelMap = channelMapper.selectEnum().stream().collect(Collectors.toMap(ChannelDO::getCode, ChannelDO::getName));
        channelCache.put(cacheKey, channelMap);

        return channelMap;
    }

    private boolean isChannelType(int classType) {
        return classType == 4;
    }
}
