package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.GatewayTplMappingDTO;
import com.xhqb.spectre.admin.model.vo.GatewayTplMappingVO;
import com.xhqb.spectre.admin.model.vo.GatewayTplVO;
import com.xhqb.spectre.admin.service.GatewayTplMappingService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.GatewayTplMappingDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.entity.support.TplDOSupport;
import com.xhqb.spectre.common.dal.mapper.GatewayTplMappingMapper;
import com.xhqb.spectre.common.dal.mapper.GatewayUserMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.GatewayTplMappingQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.Max;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/26 17:00
 * @Description:
 */
@Service
public class GatewayTplMappingServiceImpl implements GatewayTplMappingService {

    @Autowired
    private GatewayTplMappingMapper gatewayTplMappingMapper;

    @Autowired
    private GatewayUserMapper gatewayUserMapper;

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    @Override
    public CommonPager<GatewayTplMappingVO> listByPage(GatewayTplMappingQuery query) {
        return PageResultUtils.result(
                () -> gatewayTplMappingMapper.countByQuery(query),
                () -> gatewayTplMappingMapper.selectByQuery(query).stream().map(GatewayTplMappingVO::build).collect(Collectors.toList())
        );
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @Override
    public GatewayTplMappingVO getById(Integer id) {
        return GatewayTplMappingVO.build(validateAndSelectById(id));
    }

    /**
     * 创建
     *
     * @param item
     */
    @Override
    public void create(GatewayTplMappingDTO item) {
        //参数格式校验
        ValidatorUtil.validate(item);

        //重复性校验
        GatewayTplMappingDO exist = gatewayTplMappingMapper.selectByTplIdAndContent(item.getTplId(), item.getTplContent());
        if (Objects.nonNull(exist)) {
            throw new BizException("该模板的文案已存在，请检查");
        }

        String userName = SsoUserInfoUtil.getUserName();
        GatewayTplMappingDO gatewayTplMappingDO = GatewayTplMappingDO.builder()
                .tplId(item.getTplId())
                .tplContent(item.getTplContent())
                .creator(userName)
                .updater(userName)
                .build();
        gatewayTplMappingMapper.insertSelective(gatewayTplMappingDO);
    }

    /**
     * 更新
     *
     * @param id
     * @param item
     */
    @Override
    public void update(Integer id, GatewayTplMappingDTO item) {
        //参数格式校验
        ValidatorUtil.validate(item);
        validateAndSelectById(id);

        //重复性校验
        GatewayTplMappingDO exist = gatewayTplMappingMapper.selectByTplIdAndContent(item.getTplId(), item.getTplContent());
        if (Objects.nonNull(exist) && !exist.getId().equals(id)) {
            throw new BizException("该模板的文案已存在，请检查");
        }

        GatewayTplMappingDO gatewayTplMappingDO = GatewayTplMappingDO.builder()
                .id(id)
                .tplId(item.getTplId())
                .tplContent(item.getTplContent())
                .updater(SsoUserInfoUtil.getUserName())
                .build();
        gatewayTplMappingMapper.updateByPrimaryKeySelective(gatewayTplMappingDO);
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    public void delete(Integer id) {
        validateAndSelectById(id);

        gatewayTplMappingMapper.delete(id, SsoUserInfoUtil.getUserName());
    }


    /**
     * 查询网关账号关联的模板列表
     *
     * @return
     */
    @Override
    public List<GatewayTplVO> queryTplList() {
        return gatewayUserMapper.selectTplList().stream().map(GatewayTplVO::build).collect(Collectors.toList());
    }

    private GatewayTplMappingDO validateAndSelectById(Integer id) {
        GatewayTplMappingDO gatewayTplMappingDO = gatewayTplMappingMapper.selectById(id);
        if (Objects.isNull(gatewayTplMappingDO)) {
            throw new BizException("未找到该模板映射");
        }
        return gatewayTplMappingDO;
    }


}
