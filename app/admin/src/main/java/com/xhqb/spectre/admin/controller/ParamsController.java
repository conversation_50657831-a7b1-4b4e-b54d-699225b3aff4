package com.xhqb.spectre.admin.controller;

import com.xhqb.kael.util.StringUtils;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.model.dto.ParamsDTO;
import com.xhqb.spectre.admin.model.dto.ParamsDeleteDTO;
import com.xhqb.spectre.admin.model.dto.ParamsValueDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.ParamsPageVO;
import com.xhqb.spectre.admin.model.vo.ParamsValuePageVO;
import com.xhqb.spectre.admin.service.ParamsService;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ParamsQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Optional;

@RestController
@RequestMapping("/params")
@Slf4j
public class ParamsController {


    @Resource
    private ParamsService paramsService;

    /**
     * 分页列表
     *
     * @param paramsQuery 查询条件
     * @param pageNum     页码
     * @param pageSize    页面大小
     * @return 分页列表
     */
    @GetMapping("/codePage")
    public CommonResult<CommonPager<ParamsPageVO>> codePage(@ModelAttribute ParamsQuery paramsQuery, Integer pageNum, Integer pageSize) {
        paramsQuery.setPageParameter(new PageParameter(pageNum, pageSize));
       if(StringUtils.isNotEmpty(paramsQuery.getCode())) {
           paramsQuery.setCodes(paramsQuery.getCode().split(","));
       }
        log.info("变量编码分页列表查询参数 paramsQuery:{}", paramsQuery);
        return CommonResult.success(paramsService.codePage(paramsQuery));
    }

    /**
     * 新增
     *
     * @param paramsDTO 新增参数
     * @return 新增结果
     */
    @PostMapping("/codeAdd")
    public CommonResult<String> codeAdd(@RequestBody ParamsDTO paramsDTO) {
        log.info("变量编码新增 paramsDTO:{}", JsonLogUtil.toJSONString(paramsDTO));
        ValidatorUtil.validate(paramsDTO);
        return CommonResult.success(paramsService.codeAdd(paramsDTO));
    }

    /**
     * 删除
     *
     * @param paramsDTO 删除参数
     * @return 删除结果
     */
    @PostMapping("/codeDelete")
    public CommonResult<String> codeDelete(@RequestBody ParamsDeleteDTO paramsDTO) {
        log.info("模版变量删除 paramsDTO:{}", JsonLogUtil.toJSONString(paramsDTO));
        return CommonResult.success(paramsService.codeDelete(paramsDTO));
    }


    @GetMapping("/valuePage")
    public CommonResult<CommonPager<ParamsValuePageVO>> valuePage(@ModelAttribute ParamsQuery paramsQuery, Integer pageNum, Integer pageSize) {
        paramsQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        if(StringUtils.isNotEmpty(paramsQuery.getCode())) {
            paramsQuery.setCodes(paramsQuery.getCode().split(","));
        }
        log.info("变量值分页列表查询参数 paramsQuery:{}", JsonLogUtil.toJSONString(paramsQuery));
        return CommonResult.success(paramsService.valuePage(paramsQuery));
    }

    @PostMapping("/valueAdd")
    public CommonResult<String> valueAdd(@RequestBody ParamsValueDTO paramsValueDTO) {
        log.info("变量值新增 paramsValueDTO:{}", JsonLogUtil.toJSONString(paramsValueDTO));
        ValidatorUtil.validate(paramsValueDTO);
        return CommonResult.success(paramsService.valueAdd(paramsValueDTO));
    }

    @PostMapping("/valueDelete")
    public CommonResult<String> valueDelete(@RequestBody ParamsDeleteDTO paramsDTO) {
        log.info("变量值删除 paramsDTO:{}", JsonLogUtil.toJSONString(paramsDTO));
        return CommonResult.success(paramsService.valueDelete(paramsDTO));
    }

}
