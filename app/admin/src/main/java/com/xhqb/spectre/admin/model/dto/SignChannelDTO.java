package com.xhqb.spectre.admin.model.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/18 11:52
 * @Description:
 */
@Data
public class SignChannelDTO implements Serializable {

    private static final long serialVersionUID = -4676927190869860677L;

    @NotNull(message = "渠道账号ID不能为空")
    private Integer channelAccountId;

    @Size(max = 64, message = "渠道签名ID最大为{max}个字符")
    private String channelSignId;

    @NotNull(message = "状态不能为空")
    @Range(min = 1, max = 2, message = "状态应为通过或驳回")
    private Integer status;

    @Size(max = 256, message = "备注最大为{max}个字符")
    private String remark;
}
