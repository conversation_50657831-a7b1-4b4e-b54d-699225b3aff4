package com.xhqb.spectre.admin.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/11 11:40
 * @Description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShortUrlDTO implements Serializable {

    private static final long serialVersionUID = -2493690573358588724L;

    /**
     * 源URL
     */
    @NotBlank(message = "源URL不能为空")
    @Size(max = 512, message = "源URL最大为{max}个字符")
    @URL(message = "源URL格式有误")
    private String srcUrl;

    /**
     * 有效期。1：90天；2：180天；3：365天；4：永久有效
     */
    private Integer validPeriod;

    /**
     * 描述
     */
    @Size(max = 256, message = "描述最大为{max}个字符")
    private String description;
}
