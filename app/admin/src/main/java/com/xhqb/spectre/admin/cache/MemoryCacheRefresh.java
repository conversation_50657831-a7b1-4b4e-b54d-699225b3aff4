package com.xhqb.spectre.admin.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 内存缓存刷新
 */
@Configuration
@EnableScheduling
@ConditionalOnProperty(prefix = "memory.cache.refresh", name = "enable")
@Slf4j
public class MemoryCacheRefresh {

    private final List<MemoryCache<?>> memoryCacheList;

    public MemoryCacheRefresh(List<MemoryCache<?>> memoryCacheList) {
        this.memoryCacheList = memoryCacheList;
    }

    /**
     * 初始化内存缓存数据
     */
    @PostConstruct
    public void init() {
        this.refresh();
        log.info("sms内存数据加载完成");
    }

    /**
     * 定时刷新
     */
    @Scheduled(cron = "${memory.cache.refresh.cron:0/30 * * * * ?}")
    public void scheduled() {
        this.refresh();
    }


    private void refresh() {
        for (MemoryCache<?> memoryCache : memoryCacheList) {
            this.doRefresh(memoryCache);
        }
    }

    private void doRefresh(MemoryCache<?> memoryCache) {
        try {
            memoryCache.cache(true);
        } catch (Exception e) {
            log.warn("sms内存缓存刷新失败,clazz = {}", memoryCache.getClass(), e);
        }
    }

}
