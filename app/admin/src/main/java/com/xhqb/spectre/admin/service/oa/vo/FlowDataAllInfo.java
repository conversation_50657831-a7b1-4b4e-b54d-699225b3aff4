package com.xhqb.spectre.admin.service.oa.vo;

import lombok.Data;

@Data
public class FlowDataAllInfo {

    /**
     * 主键 id
     */
    private long id;

    /**
     * 节点名称 法务
     */
    private String nodeName;

    /**
     * 节点 id
     */
    private String nodeId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户 id
     */
    private String userId;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 0:未查看 -1：已查看有反馈 -2：已查看
     */
    private Integer viewType;

    /**
     * 0 :节点操作人未提交，1：转发接收人未提交，2：已提交，4：已归档
     */
    private Integer isRemark;

    /**
     * 操作日期
     */
    private String operateDate;

    /**
     * 操作时间
     */
    private String operateTime;


    /**
     * 评论意见
     */
    private String remark;

}
