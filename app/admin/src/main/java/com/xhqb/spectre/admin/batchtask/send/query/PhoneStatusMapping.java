package com.xhqb.spectre.admin.batchtask.send.query;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 手机状态映射
 *
 * <AUTHOR>
 * @date 2022/1/18
 */
@Getter
@AllArgsConstructor
public enum PhoneStatusMapping {

    UN_QUERY("未查询", -1),
    PHONE_EMPTY("空号", 0),
    PHONE_NORMAL("正常", 1),
    PHONE_HALT("停机", 2),
    PHONE_NOT_EXIST("库无", 3),
    PHONE_SILENT("沉默号", 4),
    PHONE_RISK("风险号", 5),
    UNKNOWN("未知", 99),
    ;

    /**
     * 状态定义的cid策略名称
     */
    private final String name;
    /**
     * 对弈的手机状态值
     */
    private final Integer status;


    /**
     * 根据cid策略名称查询号码映射状态
     *
     * @param strategyName
     * @return
     */
    public static Integer getPhoneStatus(String strategyName) {
        for (PhoneStatusMapping item : values()) {
            if (StringUtils.equals(item.name, strategyName)) {
                return item.status;
            }
        }
        return null;
    }

    /**
     * 根据状态值 获取到状态策略名称
     *
     * @param status
     * @return
     */
    public static PhoneStatusMapping getByStatus(Integer status) {
        if (Objects.isNull(status)) {
            return null;
        }

        for (PhoneStatusMapping item : values()) {
            if (Objects.equals(item.status, status)) {
                return item;
            }
        }

        return null;
    }
}
