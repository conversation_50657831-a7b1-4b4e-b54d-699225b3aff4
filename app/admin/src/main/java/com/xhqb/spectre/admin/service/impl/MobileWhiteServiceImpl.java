package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.MobileWhiteDTO;
import com.xhqb.spectre.admin.model.vo.MobileWhiteVO;
import com.xhqb.spectre.admin.service.MobileWhiteService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.AppDO;
import com.xhqb.spectre.common.dal.entity.MobileWhiteDO;
import com.xhqb.spectre.common.dal.mapper.AppMapper;
import com.xhqb.spectre.common.dal.mapper.MobileWhiteMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.MobileWhiteQuery;
import com.xhqb.spectre.common.enums.AppSendLimitEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 10:22
 * @Description:
 */
@Service
public class MobileWhiteServiceImpl implements MobileWhiteService {

    @Autowired
    private MobileWhiteMapper mobileWhiteMapper;

    @Autowired
    private AppMapper appMapper;

    /**
     * 默认限流规则类型
     */
    private static final String DEFAULT_CFG_TYPE = "appSendLimit";

    /**
     * 查询白名单列表
     *
     * @param mobileWhiteQuery
     * @return
     */
    @Override
    public CommonPager<MobileWhiteVO> listByPage(MobileWhiteQuery mobileWhiteQuery) {
        return PageResultUtils.result(
                () -> mobileWhiteMapper.countByQuery(mobileWhiteQuery),
                () -> mobileWhiteMapper.selectByQuery(mobileWhiteQuery).stream()
                        .map(item -> MobileWhiteVO.buildMobileWhiteVO(item, true)).collect(Collectors.toList())
        );
    }

    /**
     * 查询白名单详情
     *
     * @param id
     * @return
     */
    @Override
    public MobileWhiteVO getById(Integer id) {
        MobileWhiteDO mobileWhiteDO = validateAndSelectById(id);
        return MobileWhiteVO.buildMobileWhiteVO(mobileWhiteDO, false);
    }

    /**
     * 添加白名单
     *
     * @param mobileWhiteDTO
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void create(MobileWhiteDTO mobileWhiteDTO) {
        //校验
        checkParam(mobileWhiteDTO);

        //写入白名单
        String appCode = mobileWhiteDTO.getAppCode();
        String cfgType = DEFAULT_CFG_TYPE;
        List<String> mobileList = mobileWhiteDTO.getMobileList();
        String description = mobileWhiteDTO.getDescription();
        String currentUser = SsoUserInfoUtil.getUserName();
        mobileList.forEach(mobile -> {
            //查询已有的白名单
            MobileWhiteDO exist = selectOne(appCode, cfgType, mobile);
            if (Objects.isNull(exist)) {
                //新增
                MobileWhiteDO mobileWhiteDO = MobileWhiteDO.builder()
                        .appCode(appCode)
                        .cfgType(cfgType)
                        .mobile(mobile)
                        .description(description)
                        .creator(currentUser)
                        .updater(currentUser)
                        .build();
                mobileWhiteMapper.insertSelective(mobileWhiteDO);
            } else {
                //更新
                MobileWhiteDO mobileWhiteDO = MobileWhiteDO.builder()
                        .id(exist.getId())
                        .appCode(appCode)
                        .cfgType(cfgType)
                        .mobile(mobile)
                        .description(description)
                        .updater(currentUser)
                        .build();
                mobileWhiteMapper.updateByPrimaryKeySelective(mobileWhiteDO);
            }
        });
    }

    /**
     * 删除白名单
     *
     * @param id
     */
    @Override
    public void delete(Integer id) {
        validateAndSelectById(id);

        //删除记录
        mobileWhiteMapper.delete(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 批量删除白名单
     *
     * @param idList
     */
    @Override
    public void batchDelete(List<Integer> idList) {
        //校验
        if (CollectionUtils.isEmpty(idList)) {
            throw new BizException("待删除的白名单ID不能为空");
        }
        //批量删除
        mobileWhiteMapper.deleteByIdList(idList, SsoUserInfoUtil.getUserName());
    }

    private MobileWhiteDO validateAndSelectById(Integer id) {
        MobileWhiteDO mobileWhiteDO = mobileWhiteMapper.selectByPrimaryKey(id);
        if (Objects.isNull(mobileWhiteDO)) {
            throw new BizException("未找到该白名单");
        }
        return mobileWhiteDO;
    }

    private void checkParam(MobileWhiteDTO mobileWhiteDTO) {
        //参数格式校验
        ValidatorUtil.validate(mobileWhiteDTO);

        //应用编码
        AppDO appDO = appMapper.selectByCode(mobileWhiteDTO.getAppCode());
        if (Objects.isNull(appDO)) {
            throw new BizException("应用编码有误，该业务应用不存在");
        }

        //限流规则类型
//        if (!AppSendLimitEnum.contains(mobileWhiteDTO.getCfgType())) {
//            throw new BizException("限流规则类型有误");
//        }

        //手机号
        mobileWhiteDTO.getMobileList().forEach(mobile -> {
            if (!CommonUtil.isLegalPhone(mobile)) {
                throw new BizException("手机号格式有误。手机号：" + mobile);
            }
        });
    }

    private MobileWhiteDO selectOne(String appCode, String cfgType, String mobile) {
        return mobileWhiteMapper.selectOne(MobileWhiteDO.builder()
                .appCode(appCode)
                .cfgType(cfgType)
                .mobile(mobile)
                .build());
    }
}
