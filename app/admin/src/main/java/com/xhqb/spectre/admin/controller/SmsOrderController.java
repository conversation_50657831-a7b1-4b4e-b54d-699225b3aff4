package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.service.SmsOrderService;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.SmsOrderQuery;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/23 17:32
 * @Description:
 */
@RestController
@RequestMapping("/smsOrder")
@Slf4j
public class SmsOrderController {

    @Autowired
    private SmsOrderService smsOrderService;

    /**
     * 查询发送记录总数
     *
     * @param smsOrderQuery
     * @return
     */
    @GetMapping("/queryTotalCount")
    public AdminResult queryTotalCount(@ModelAttribute SmsOrderQuery smsOrderQuery) {
        ValidatorUtil.validate(smsOrderQuery);
        return AdminResult.success(smsOrderService.queryTotalCount(smsOrderQuery));
    }

    /**
     * 查询发送记录列表
     *
     * @param smsOrderQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/queryList")
    public AdminResult queryList(@ModelAttribute SmsOrderQuery smsOrderQuery, Integer pageNum, Integer pageSize) {
        smsOrderQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        ValidatorUtil.validate(smsOrderQuery);
        return AdminResult.success(smsOrderService.listByPage(smsOrderQuery));
    }

    /**
     * 查询明文手机号
     *
     * @param orderId
     * @return
     */
    @GetMapping("/queryMobile")
    public AdminResult queryMobile(Long orderId) {
        return AdminResult.success(smsOrderService.queryMobile(orderId));
    }


    /**
     * 查询验证码发送记录总数
     *
     * @param smsOrderQuery
     * @return
     */
    @GetMapping("/verifyQueryTotalCount")
    public AdminResult verifyQueryTotalCount(@ModelAttribute SmsOrderQuery smsOrderQuery) {
        smsOrderQuery.setSmsTypeCode(MessageTypeEnum.VERIFY.getMessageType());
        return AdminResult.success(smsOrderService.queryTotalCount(smsOrderQuery));
    }

    /**
     * 查询验证码发送记录列表
     *
     * @param smsOrderQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/verifyQueryList")
    public AdminResult verifyQueryList(@ModelAttribute SmsOrderQuery smsOrderQuery, Integer pageNum, Integer pageSize) {
        smsOrderQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        smsOrderQuery.setSmsTypeCode(MessageTypeEnum.VERIFY.getMessageType());
        return AdminResult.success(smsOrderService.listByPage(smsOrderQuery));
    }

}
