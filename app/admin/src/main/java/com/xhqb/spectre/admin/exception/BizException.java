package com.xhqb.spectre.admin.exception;

import com.xhqb.spectre.admin.enums.RespCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@EqualsAndHashCode(callSuper = false)
public class BizException extends RuntimeException {

    private static final int PARAM_ERROR_CODE = 1000;

    private Integer code;
    private String msg;

    public BizException(Integer code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public BizException(Throwable cause) {
        super(cause);
        this.code = 1;
        this.msg = "";
    }


    public BizException(RespCodeEnum codeEnum) {
        super(codeEnum.getMsg());
        this.code = codeEnum.getCode();
        this.msg = codeEnum.getMsg();
    }

    public BizException(RespCodeEnum codeEnum, String msg) {
        super(msg);
        this.code = codeEnum.getCode();
        this.msg = msg;
    }

    public BizException(String msg) {
        super(msg);
        this.code = PARAM_ERROR_CODE;
        this.msg = msg;
    }
}
