package com.xhqb.spectre.admin.mq.producer.impl;

import com.xhqb.spectre.admin.mq.message.BatchTaskParamMessage;
import com.xhqb.spectre.admin.mq.producer.AbstractMessageProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 群发任务参数消息生产者
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Component
public class BatchTaskParamProducer extends AbstractMessageProducer<BatchTaskParamMessage> {

    /**
     * spectre-admin-batch-param topic信息
     */
    @Value("#{'${kael.mq.producers:}'.split(',')[1]}")
    private String taskParamTopic;

    /**
     * 设置生产者topic
     *
     * @return
     */
    @Override
    protected String getTopic() {
        return taskParamTopic;
    }
}
