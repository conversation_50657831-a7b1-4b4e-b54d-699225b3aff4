package com.xhqb.spectre.admin.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class FileResponseUtil {

    public static final String XLSX = ".xlsx";


    /**
     * 构建 Content-Disposition
     * @param fileName 导出的文件名
     * @param fileExtension 后缀带点(.txt)
     * @return
     */
    public static String buildContentDisposition(String fileName, String fileExtension) {
        try {
            String fullFileName = fileName + fileExtension;

            String encodedFileName = URLEncoder.encode(fullFileName, StandardCharsets.UTF_8.name())
                    .replaceAll("\\+", "%20");

            return "attachment; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName;

        } catch (UnsupportedEncodingException e) {
            return "attachment; filename=\"download" + fileExtension + "\"";
        }
    }
}
