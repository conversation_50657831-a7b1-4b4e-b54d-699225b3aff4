package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.readonly.vo.SmsReceiptPageVO;
import com.xhqb.spectre.admin.readonly.vo.SmsReceiptVO;
import com.xhqb.spectre.admin.readonly.mapper.SmsReceiptReadonlyMapper;
import com.xhqb.spectre.admin.service.SmsReceiptService;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.SmsReceiptDO;
import com.xhqb.spectre.common.dal.query.SmsReceiptQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/30 15:17
 * @Description:
 */
@Service
public class SmsReceiptServiceImpl implements SmsReceiptService {

    @Autowired
    private SmsReceiptReadonlyMapper smsReceiptReadonlyMapper;

    /**
     * 查询回执记录总数
     *
     * @param smsReceiptQuery
     * @return
     */
    @Override
    public Integer queryTotalCount(SmsReceiptQuery smsReceiptQuery) {
        checkParam(smsReceiptQuery);
        return smsReceiptReadonlyMapper.countByQuery(smsReceiptQuery);
    }

    /**
     * 查询回执记录列表
     *
     * @param smsReceiptQuery
     * @return
     */
    @Override
    public SmsReceiptPageVO listByPage(SmsReceiptQuery smsReceiptQuery) {
        boolean isQueryTotal = checkParam(smsReceiptQuery);
        List<SmsReceiptVO> smsReceiptVOList;
        Integer totalCount = 0;
        boolean hasNextPage = true;
        if (isQueryTotal) {
            totalCount = smsReceiptReadonlyMapper.countByQuery(smsReceiptQuery);
            smsReceiptVOList = smsReceiptReadonlyMapper.selectByQuery(smsReceiptQuery).stream()
                    .map(SmsReceiptVO::buildSmsReceiptVO).collect(Collectors.toList());
        } else {
            //假分页处理，每次多查询一条记录，判断有无下一页
            int realPageSize = smsReceiptQuery.getPageParameter().getPageSize() + 1;
            smsReceiptQuery.getPageParameter().setPageSize(realPageSize);
            List<SmsReceiptDO> smsReceiptDOList = smsReceiptReadonlyMapper.selectByQuery(smsReceiptQuery);
            if (smsReceiptDOList.size() < realPageSize) {
                hasNextPage = false;
            } else {
                //删除多查询出来的那条记录
                smsReceiptDOList.remove(realPageSize - 1);
            }
            smsReceiptVOList = smsReceiptDOList.stream().map(SmsReceiptVO::buildSmsReceiptVO).collect(Collectors.toList());
        }
        return SmsReceiptPageVO.builder()
                .dataList(smsReceiptVOList)
                .totalCount(totalCount)
                .returnTotal(isQueryTotal)
                .hasNextPage(hasNextPage)
                .build();
    }

    /**
     * 查询明文手机哈
     *
     * @param id
     * @return
     */
    @Override
    public String queryMobile(Long id) {
        if (Objects.isNull(id) || id == 0) {
            throw new BizException("ID不能为空");
        }
        SmsReceiptDO smsReceiptDO = smsReceiptReadonlyMapper.selectByPrimaryKey(id);
        if (Objects.isNull(smsReceiptDO)) {
            throw new BizException("未找到该回执记录");
        }
        return smsReceiptDO.getMobile();
    }

    /**
     * 参数校验
     *
     * @param smsReceiptQuery
     * @return boolean 是否查询总数，传入订单号或手机号时，才会去查询总数
     */
    private boolean checkParam(SmsReceiptQuery smsReceiptQuery) {
        if (StringUtils.isNotBlank(smsReceiptQuery.getOrderId()) || StringUtils.isNotBlank(smsReceiptQuery.getMobile())) {
            return true;
        }
        //订单号和手机号为空时，需校验时间
        String startTime = smsReceiptQuery.getStartTime();
        String endTime = smsReceiptQuery.getEndTime();
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            throw new BizException("订单号和手机号条件为空时，开始时间和结束时间必填");
        }
        Date start = DateUtil.stringToDate(startTime);
        Date end = DateUtil.stringToDate(endTime);
        if (Objects.isNull(start) || Objects.isNull(end)) {
            throw new BizException("时间格式有误");
        }
//        if (end.getTime() - start.getTime() > 7 * 86400 * 1000) {
//            throw new BizException("订单号和手机号条件为空时，查询时间跨度不能超过7天");
//        }
        return false;
    }
}
