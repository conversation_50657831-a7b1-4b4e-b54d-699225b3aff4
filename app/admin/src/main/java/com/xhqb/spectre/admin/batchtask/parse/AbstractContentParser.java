package com.xhqb.spectre.admin.batchtask.parse;

import com.xhqb.spectre.admin.batchtask.io.UploadFileUtils;
import com.xhqb.spectre.admin.batchtask.upload.UploadContext;
import com.xhqb.spectre.admin.batchtask.upload.UploadHandler;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 抽象内容解析器
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
public abstract class AbstractContentParser implements ContentParser {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private UploadHandler uploadHandler;

    /**
     * 当前解析器是否支持解析操作
     *
     * @param context
     * @return
     */
    @Override
    public boolean supports(ParseContext context) {
        String fileName = context.getFileName();
        return StringUtils.endsWithAny(fileName, getSupportSuffix());
    }

    /**
     * 具体内容解析操作
     *
     * @param context
     * @return
     * @throws Exception
     */
    @Override
    public List<ParseResult> parse(ParseContext context) throws Exception {
        ParseResult parseResult = new ParseResult();

        parseResult.setFileName(context.getFileName());
        parseResult.setFileType(UploadFileUtils.getFileSuffix(context.getFileName()));
        // 设置签名
        parseResult.setSignName(context.getSignName());
        // 设置手机状态校验列表标记
        parseResult.setPhoneStatusList(context.getPhoneStatusList());
        List<ParseResult> parseResultList = doParse(context, parseResult);

        // 解析完成之后进行文件上传处理 2022-05-30
        this.doUpload(context, parseResultList, parseResult.getFileName());

        return parseResultList;
    }

    /**
     * 进行文件上传处理
     * <p>
     * 主要填充saveUrl 和 fileMd5只等信息
     *
     * @param context
     * @param parseResultList
     * @param fileOriginalName
     */
    private void doUpload(ParseContext context, List<ParseResult> parseResultList, String fileOriginalName) {
        InputStream uploadStream = context.getUploadStream();
        if (Objects.isNull(uploadStream)) {
            String saveUrl = context.getSaveUrl();
            this.setSaveUrl(saveUrl, parseResultList, fileOriginalName);
            return;
        }

        // 做文件上传
        String fileName = context.getFileName();
        String saveUrl = uploadHandler.handler(new UploadContext(fileName, uploadStream));
        this.setSaveUrl(saveUrl, parseResultList, fileOriginalName);
    }

    private void setSaveUrl(String saveUrl, List<ParseResult> parseResultList, String fileOriginalName) {
        if (StringUtils.isBlank(saveUrl)) {
            logger.warn("群发文件上传saveUrl为空,fileName = {}", fileOriginalName);
            return;
        }
        // 文件上传路径
        String fileMd5 = DigestUtils.md5Hex(saveUrl);

        for (ParseResult parseResult : parseResultList) {
            // 设置文件md5值
            parseResult.setFileMd5(fileMd5);
            // 设置cos服务器保存文件名称
            parseResult.setSaveUrl(getSaveNameFromUrl(saveUrl));

            if (Objects.isNull(parseResult.getContentItemList())) {
                // 防止解析内容数据为空的情况(cid和mobile复合模式下其中一列完全为空时会有该情况)
                parseResult.setContentItemList(new ArrayList<>());
            }

        }
    }


    /**
     * 从保存文件路径地址中获取文件名称
     *
     * @param saveUrl
     * @return
     */
    private String getSaveNameFromUrl(String saveUrl) {
        if (StringUtils.isBlank(saveUrl)) {
            return saveUrl;
        }

        int index = saveUrl.lastIndexOf('/');
        if (index == -1) {
            return saveUrl;
        }
        try {
            return saveUrl.substring(index + 1);
        } catch (Exception e) {
            // ignore ex
        }
        return saveUrl;
    }

    /**
     * 获取到支持解析的文件后缀名
     *
     * @return
     */
    protected abstract String[] getSupportSuffix();

    /**
     * 真正做内容解析操作，并将处理结果写入到ParseResult对象中
     *
     * @param context
     * @param result
     * @throws Exception
     * @reutn 返回处理结果列表
     */
    protected abstract List<ParseResult> doParse(ParseContext context, ParseResult result) throws Exception;
}
