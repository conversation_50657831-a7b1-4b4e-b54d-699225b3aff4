package com.xhqb.spectre.admin.service.impl;

import cn.hutool.core.date.DateUnit;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.ShortUrlDTO;
import com.xhqb.spectre.admin.model.dto.ShortUrlTplDTO;
import com.xhqb.spectre.admin.model.vo.ShortUrlTplVO;
import com.xhqb.spectre.admin.service.ShortUrlService;
import com.xhqb.spectre.admin.service.ShortUrlTplService;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.ShortUrlTplDO;
import com.xhqb.spectre.common.dal.mapper.ShortUrlTplDOMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ShortUrlTplQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ShortUrlTplServiceImpl implements ShortUrlTplService {

    @Resource
    private VenusConfig venusConfig;
    @Resource
    private ShortUrlTplDOMapper shortUrlTplDOMapper;
    @Resource
    private ShortUrlService shortUrlService;

    @Override
    public CommonPager<ShortUrlTplVO> page(ShortUrlTplQuery shortUrlTplQuery) {
        return PageResultUtils.result(
                () -> shortUrlTplDOMapper.countByQuery(shortUrlTplQuery),
                () -> shortUrlTplDOMapper.selectByQuery(shortUrlTplQuery).stream()
                        .map(this::buildShortUrlTplVO).collect(Collectors.toList())
        );
    }

    @Override
    public ShortUrlTplVO detail(Integer id) {
        ShortUrlTplDO model = shortUrlTplDOMapper.selectByPrimaryKey(id);
        if (Objects.isNull(model)) {
            throw new BizException("模板不存在");
        }
        return this.buildShortUrlTplVO(model);
    }

    @Override
    public Integer add(ShortUrlTplDTO shortUrlTplDTO) {

        // 查看是否存在
        ShortUrlTplDO model = shortUrlTplDOMapper.selectByTplCode(shortUrlTplDTO.getTplCode());
        if (Objects.nonNull(model)) {
            throw new BizException("模板编码已存在");
        }
        // 新增一份短链配置
        ShortUrlDTO shortUrlDTO = new ShortUrlDTO();
        shortUrlDTO.setValidPeriod(4);
        shortUrlDTO.setSrcUrl(shortUrlTplDTO.getLongUrl());
        shortUrlDTO.setDescription(shortUrlTplDTO.getDescription());
        String shortCode = shortUrlService.innerCreate(shortUrlDTO);
        if (StringUtils.isBlank(shortCode)) {
            throw new BizException("生成短链code异常");
        }

        ShortUrlTplDO addModel = build(shortUrlTplDTO);
        addModel.setCreator(SsoUserInfoUtil.getUserName());
        addModel.setCreateTime(new Date());
        addModel.setShortCode(shortCode);
        addModel.setIsEncrypt(shortUrlTplDTO.getIsEncrypt());
        shortUrlTplDOMapper.insertSelective(addModel);

        return addModel.getId();
    }


    @Override
    public Integer update(ShortUrlTplDTO shortUrlTplDTO) {

        // 查看是否存在
        ShortUrlTplDO model = shortUrlTplDOMapper.selectByPrimaryKey(shortUrlTplDTO.getId());
        if (Objects.isNull(model)) {
            throw new BizException("模板不存在");
        }
        // 更新
        ShortUrlTplDO updateModel = build(shortUrlTplDTO);
        updateModel.setUpdater(SsoUserInfoUtil.getUserName());
        updateModel.setShortCode(model.getShortCode());
        updateModel.setIsEncrypt(shortUrlTplDTO.getIsEncrypt());
        if (shortUrlTplDOMapper.updateByPrimaryKeySelective(updateModel) <= 0) {
            throw new BizException("更新失败");
        }
        return shortUrlTplDTO.getId();
    }

    @Override
    public Integer delete(Integer id) {
        // 查看是否存在
        ShortUrlTplDO model = shortUrlTplDOMapper.selectByPrimaryKey(id);
        if (Objects.isNull(model)) {
            throw new BizException("模板不存在");
        }
        if (shortUrlTplDOMapper.updateDeleteTagById(id) <= 0) {
            throw new BizException("删除失败");
        }
        return id;
    }


    private ShortUrlTplDO build(ShortUrlTplDTO shortUrlTplDTO) {
        return ShortUrlTplDO.builder()
                .expiredDate(DateUtil.dateToString(DateUtils.addDays(new Date(), shortUrlTplDTO.getValidPeriod())))
                .tplCode(shortUrlTplDTO.getTplCode())
                .name(shortUrlTplDTO.getName())
                .description(shortUrlTplDTO.getDescription())
                .updater(SsoUserInfoUtil.getUserName())
                .updateTime(new Date())
                .isDelete(0)
                .clickCount(0)
                .ipClickCount(0)
                .longUrl(shortUrlTplDTO.getLongUrl())
                .id(shortUrlTplDTO.getId())
                .build();
    }

    public ShortUrlTplVO buildShortUrlTplVO(ShortUrlTplDO item) {
        ShortUrlTplVO target = new ShortUrlTplVO();
        BeanUtils.copyProperties(item, target);
        // 计算有效天数
        Date currentDate = new Date();
        Date expiredDate = DateUtil.smallToDate(item.getExpiredDate());
        if (expiredDate == null) {
            throw new BizException("ShortUrlTplDO的expiredDate不可为null");
        }
        int expiredTag = 0;
        long between = DateUtil.between(currentDate, expiredDate, DateUnit.DAY);
        if (between <= 0) {
            between = 0;
        }
        if (expiredDate.getTime() < currentDate.getTime()) {
            expiredTag = 1;
        }
        target.setExpiredTag(expiredTag);
        target.setValidPeriod(between);
        target.setShortUrl(buildShortUrl(item.getShortCode()));
        return target;
    }

    private String buildShortUrl(String shortCode) {
        return venusConfig.getShortUrlDomain() + "/" + shortCode;
    }


}
