package com.xhqb.spectre.admin.batchtask.utils;

import cn.hutool.crypto.SecureUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.UUID;

/**
 * 文件上传工具
 * <AUTHOR>
 */
public final class UploadFileUtils {

    /**
     * 临时文件存储位置
     */
    public static final String TMP_SAVE_DIR = "/tmp/upload/";

    private static final String DIR_DIVIDE = "/";

    private static final String FILE_DIVIDE = "_";

    private UploadFileUtils(){}

    /**
     * Web 保存文件
     *
     * @param request
     * @return
     */
    public static File saveFile(MultipartFile request) throws IOException {
        return doSaveFile(request.getInputStream(), request.getOriginalFilename());
    }

    /**
     * 保存文件
     *
     * @param in
     * @param fileName
     * @return
     * @throws IOException
     */
    public static File doSaveFile(InputStream in, String fileName) throws IOException {
        createDir();
        String toUseName = SecureUtil.md5(UUID.randomUUID().toString()) + "_" + fileName;
        File file = new File(TMP_SAVE_DIR + DIR_DIVIDE + toUseName);
        try (InputStream inputStream = in; OutputStream outputStream = new FileOutputStream(file)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
        }
        return file;
    }

    /**
     * 创建存储目录
     */
    public static void createDir() {
        File dir = new File(TMP_SAVE_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }

    /**
     * 获取到文件的后缀名称
     *
     * @param fileName
     * @return
     */
    public static String getFileSuffix(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "";
        }
        int index = fileName.lastIndexOf('.');
        if (index == -1) {
            return "";
        }
        return fileName.substring(index + 1);
    }

    /**
     * 生成cos的objectName
     * <p>
     * 取文件md5值 + 文件md5值 + 文件后缀名
     *
     * @param objectName
     * @return
     */
    public static String genCosObjectName(String objectName) {
        if (StringUtils.isBlank(objectName)) {
            throw new IllegalArgumentException("objectName不能够为空喔.");
        }

        int index = objectName.indexOf('/');
        if (index != -1) {
            // objectName 中包含了斜杠 则表示属于包含路径地址
            // 属于合法objectName 不需要自动填充
            return objectName;
        }

        // objectName中不包含斜杠，根据cos文件上传格式，该文件需要截取前两位
        // 组成一个路径objectName
        return objectName.substring(0, 2) + DIR_DIVIDE + objectName;
    }



    /**
     * 获取到原始文件名称
     *
     * @param file
     * @return
     */
    public static String getOriginalFilename(File file) {
        String name = file.getName();
        int index = name.indexOf(FILE_DIVIDE);
        if (index != -1) {
            return name.substring(index + 1);
        }
        return name;
    }
}
