package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.statistics.query.SmsStatisClassQuery;
import com.xhqb.spectre.admin.statistics.query.SmsStatisQuery;
import com.xhqb.spectre.admin.statistics.vo.*;
import com.xhqb.spectre.common.dal.page.CommonPager;

import java.util.List;
import java.util.Map;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/28 15:56
 * @Description:
 */
public interface SmsStatisticsService {

    List<SmsStatisSumVO> queryByDay(SmsStatisQuery query);

    CommonPager<SmsStatisDetailVO> queryPageByDay(SmsStatisQuery query);

    List<SmsStatisSumVO> queryByMonth(SmsStatisQuery query);

    List<SmsStatisClassSumVO> queryClassSumByDay(SmsStatisClassQuery query);

    List<SmsStatisClassSumListVO> queryClassSumListByDay(SmsStatisClassQuery query);

    Map<String, SmsHisStatisVO> queryHisData();

    List<String> queryClassEnum(SmsStatisClassQuery query);

    List<SmsStatisSumVO> queryByHour(SmsStatisQuery query);

    CommonPager<SmsStatisDetailVO> queryPageByHour(SmsStatisQuery query);

    List<SmsStatisClassSumVO> queryClassSumByHour(SmsStatisClassQuery query);

    List<SmsStatisClassSumListVO> queryClassSumListByHour(SmsStatisClassQuery query);
}
