package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.ChannelTestRecordVO;
import com.xhqb.spectre.admin.service.ChannelTestRecordService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ChannelTestRecordQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/channelTestRecord")
@Slf4j
public class ChannelTestRecordController {

    @Resource
    private ChannelTestRecordService channelTestRecordService;

    /**
     * 渠道测试任务记录分页列表查询
     * @param channelTestRecordQuery 查询参数
     * @param pageNum 页码
     * @param pageSize 当前页面大小
     * @return 分页列表
     */
    @GetMapping("")
    public CommonResult<CommonPager<ChannelTestRecordVO>> listByPage(@ModelAttribute ChannelTestRecordQuery channelTestRecordQuery, Integer pageNum, Integer pageSize) {
        channelTestRecordQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        log.info("渠道测试任务记录分页列表查询参数 listByPage:{}", channelTestRecordQuery);
        return CommonResult.success(channelTestRecordService.listByPage(channelTestRecordQuery));
    }


}
