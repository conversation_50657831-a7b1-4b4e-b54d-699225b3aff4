package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.ShortUrlDTO;
import com.xhqb.spectre.admin.model.vo.ShortUrlVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ShortUrlQuery;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/11 11:47
 * @Description:
 */
public interface ShortUrlService {

    CommonPager<ShortUrlVO> listByPage(ShortUrlQuery shortUrlQuery);

    ShortUrlVO queryInfo(Integer id);

    String create(ShortUrlDTO shortUrlDTO);

    String innerCreate(ShortUrlDTO shortUrlDTO);

    String update(Integer id, ShortUrlDTO shortUrlDTO);

    void enable(Integer id);

    void disable(Integer id);

    void delete(Integer id);

    ShortUrlVO queryInfo(String shortCode);

    String update(String shortCode, ShortUrlDTO shortUrlDTO);
}
