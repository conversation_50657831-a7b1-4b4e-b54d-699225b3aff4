package com.xhqb.spectre.admin.model.vo;


import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.entity.TplDisableDO;
import com.xhqb.spectre.common.dal.entity.support.TplChannelDOSupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/15 17:42
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplVO implements Serializable {

    private static final long serialVersionUID = -644167102793176644L;
    private Integer id;
    private String code;
    private String smsTypeCode;
    private String title;
    private Integer signId;
    private String content;
    private String appCode;
    private String remark;
    private Integer status;
    private List<TplChannelVO> channelInfoList;
    private List<TplDisableVO> disableInfoList;
    private String createTime;
    private String creator;
    private String updateTime;
    private String updater;
    /**
     * 审批状态
     */
    private Integer approveStatus;

    /**
     * 是否网关账号的模板
     */
    private boolean gatewayTplFlag;

    /**
     * 业务主线ID
     */
    private Integer businessLineId;
    /**
     * 营销场景ID
     */
    private Integer marketSceneId;
    /**
     * 报备 Id（内容 id）
     */
    private String reportId;

    /**
     * 来源 手工创建 create、报备同步 sync
     */
    private String source;

    /**
     * 通知发送状态 0：未发送，1：已发送
     */
    private Integer notifySendStatus;

    /**
     * 标签 0：无 1：营销类通知
     */
    private Integer tag;

    public static TplVO buildTplVO(TplDO tplDO, boolean isGatewayTpl) {
        return TplVO.builder()
                .id(tplDO.getId())
                .code(tplDO.getCode())
                .smsTypeCode(tplDO.getSmsTypeCode())
                .title(tplDO.getTitle())
                .signId(tplDO.getSignId())
                .content(tplDO.getContent())
                .appCode(tplDO.getAppCode())
                .remark(tplDO.getRemark())
                .status(tplDO.getStatus())
                .createTime(Objects.isNull(tplDO.getCreateTime()) ? "" :
                        DateUtil.dateToString(tplDO.getCreateTime()))
                .creator(tplDO.getCreator())
                .updateTime(Objects.isNull(tplDO.getCreateTime()) ? "" :
                        DateUtil.dateToString(tplDO.getUpdateTime()))
                .updater(tplDO.getUpdater())
                .gatewayTplFlag(isGatewayTpl)
                .approveStatus(tplDO.getApproveStatus())
                .reportId(tplDO.getReportId())
                .source(tplDO.getSource())
                .notifySendStatus(tplDO.getNotifySendStatus())
                .tag(tplDO.getTag())
                .build();
    }

    public static TplVO buildTplVOSupport(TplDO tplDO, List<TplChannelDOSupport> tplChannelDOList, List<TplDisableDO> tplDisableDOList, boolean isGatewayTpl) {
        TplVO tplVO = buildTplVO(tplDO, isGatewayTpl);
        tplVO.setChannelInfoList(tplChannelDOList.stream().map(TplChannelVO::buildTplChannelVOSupport).collect(Collectors.toList()));
        tplVO.setDisableInfoList(tplDisableDOList.stream().map(TplDisableVO::buildTplDisableVO).collect(Collectors.toList()));
        return tplVO;
    }
}
