package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.ChannelDTO;
import com.xhqb.spectre.admin.model.vo.ChannelEnumVO;
import com.xhqb.spectre.admin.model.vo.ChannelVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ChannelQuery;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 11:21
 * @Description:
 */
public interface ChannelService {

    List<ChannelEnumVO> queryEnum();

    CommonPager<ChannelVO> listByPage(ChannelQuery channelQuery);

    ChannelVO getById(Integer id);

    void create(ChannelDTO channelDTO);
}
