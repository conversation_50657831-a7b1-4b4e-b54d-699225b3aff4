package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.CidStrategyVO;
import com.xhqb.spectre.common.dal.dto.CidStrategyDTO;

import java.util.List;

/**
 * Cid校验策略表
 *
 * <AUTHOR>
 * @date 2021/11/26
 */
public interface CidStrategyService {

    /**
     * cid策略分组
     *
     * @param group
     * @return
     */
    AdminResult cidStrategyGroup(String group);

    /**
     * 更新cid策略信息
     *
     * @param group
     * @param cidStrategyList
     */
    void update(String group, List<CidStrategyDTO> cidStrategyList);

    /**
     * 根据Id查询详情信息
     *
     * @param id
     * @return
     */
    CidStrategyVO getById(Integer id);

    /**
     * 新增cid策略
     *
     * @param cidStrategyDTO
     */
    void create(CidStrategyDTO cidStrategyDTO);

    /**
     * 根据cid信息
     *
     * @param cidStrategyDTO
     */
    void update(CidStrategyDTO cidStrategyDTO);

    /**
     * 初始化策略分组
     */
    void initStrategyGroup();
}
