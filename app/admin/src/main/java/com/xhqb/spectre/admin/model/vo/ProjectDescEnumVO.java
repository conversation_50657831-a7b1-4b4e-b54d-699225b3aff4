package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.ProjectDescDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectDescEnumVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Integer id;

    private String description;

    private Integer status;

    public static ProjectDescEnumVO buildEnumVO(ProjectDescDO projectDescDO) {
        return ProjectDescEnumVO.builder()
                .id(projectDescDO.getId())
                .description(projectDescDO.getDescription())
                .status(projectDescDO.getStatus())
                .build();
    }
}
