package com.xhqb.spectre.admin.enums;

import com.xhqb.spectre.admin.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SmsTypeCodeEnum {
    MARKET("market", "营销"),
    VERIFY("verify", "验证码"),
    COLLECTOR("collector", "还款通知"),
    LIGHT_COLLECTOR("light_collector", "轻催"),
    SEVERE_COLLECTOR("severe_collector", "重催"),
    DEBT_SWAP("debt_swap", "债转"),
    NOTIFY("notify", "通知");
    private final String code;
    private final String name;

    public static SmsTypeCodeEnum getByCode(String code) {
        for (SmsTypeCodeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        throw new BizException("没有找到对应的枚举");
    }

    public static SmsTypeCodeEnum fromCode(String code) {
        for (SmsTypeCodeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
