package com.xhqb.spectre.admin.model.vo.batchtask;

import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.exception.BizException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 任务参数VO
 *
 * <AUTHOR>
 * @date 2021/9/21
 */
@Data
public class ParamItemVO extends LinkedHashMap<String, String> {

    /**
     * 创建ParamItemVO
     *
     * @return
     */
    public static ParamItemVO newParamItem() {
        return new ParamItemVO();
    }

    /**
     * 做集合内容映射
     * 将第一个集合的值作为map的key，将第二个集合的值作为map的value
     *
     * @param titleList   key 集合
     * @param contentList value 集合
     * @return
     */
    public ParamItemVO mapping(List<String> titleList, List<String> contentList) {
        this.doMappingCheck(titleList, contentList);
        int size = titleList.size();
        for (int i = 0; i < size; i++) {
            super.put(titleList.get(i), contentList.get(i));
        }
        return this;
    }

    /**
     * 做映射值时需要检测
     * 1.两个列表集合不能够为null
     * 2.两个列表集合不能够为空
     * 3.两个列表集合的数量量要一致
     *
     * @param titleList
     * @param contentList
     */
    private void doMappingCheck(List<String> titleList, List<String> contentList) {
        if (Objects.isNull(titleList) || Objects.isNull(contentList)) {
            throw new BizException("传入的集合不能够为空");
        }
        if (titleList.isEmpty() || contentList.isEmpty()) {
            throw new BizException("传入的集合必须必须要包含数据");
        }

        if (titleList.size() != contentList.size()) {
            throw new BizException("传入的集合数据量必须一致");
        }
    }

    /**
     * 将数据转换成list
     * 会过滤掉mobile 和 cid字段
     *
     * @return
     */
    public List<String> toParamList() {
        return super.entrySet().stream()
                .filter(s -> !StringUtils.equalsAnyIgnoreCase(s.getKey(),
                        BatchTaskConstants.DataType.CID,
                        BatchTaskConstants.DataType.MOBILE,
                        BatchTaskConstants.DataType.APPLY_LOAN_RESULT,
                        BatchTaskConstants.DataType.SKIP_SEND))
                .map(s -> s.getValue()).collect(Collectors.toList());
    }

    /**
     * 将参数数据转换成map集合
     * 会过滤掉mobile 和 cid字段
     *
     * @return
     */
    public Map<String, String> toParamMap() {
        Map<String, String> result = super.entrySet().stream().filter(s -> !StringUtils.equalsAnyIgnoreCase(s.getKey(),
                BatchTaskConstants.DataType.CID,
                BatchTaskConstants.DataType.MOBILE,
                BatchTaskConstants.DataType.APPLY_LOAN_RESULT,
                BatchTaskConstants.DataType.SKIP_SEND)).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        result.put("phone", super.get(BatchTaskConstants.DataType.MOBILE));
        return result;
    }
}
