package com.xhqb.spectre.admin.statistics.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsStatisByDayDO extends SmsStatisByDayKey implements Serializable {

    private static final long serialVersionUID = 5596293199927483895L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.f1
     *
     * @mbggenerated
     */
    private Integer f1;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.f2
     *
     * @mbggenerated
     */
    private Integer f2;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.f3
     *
     * @mbggenerated
     */
    private Integer f3;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.f4
     *
     * @mbggenerated
     */
    private Integer f4;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.f5
     *
     * @mbggenerated
     */
    private Integer f5;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_class_statis_byday.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;
}