package com.xhqb.spectre.admin.openapi.auth;

import com.xhqb.spectre.admin.openapi.common.ActionResult;
import com.xhqb.spectre.admin.openapi.common.CodeDefinition;
import com.xhqb.spectre.admin.openapi.constants.OpenApiConstants;
import com.xhqb.spectre.admin.openapi.service.OpenApiAppService;
import com.xhqb.spectre.admin.openapi.utils.ServletUtils;
import com.xhqb.spectre.admin.openapi.utils.SignUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * open api 使用授权
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
@Slf4j
public class OpenApiAccessInterceptor extends HandlerInterceptorAdapter {

    /**
     * 响应编码
     */
    private static final ThreadLocal<String> CODE_HOLDER = new ThreadLocal<>();

    /**
     * nonce随机数最大长度
     */
    private static final int MAX_NONCE_LENGTH = 32;

    /**
     * nonce缓存过期时间(秒)
     */
    @Value("${spectre.admin.openapi.nonceCacheTimeoutSecond:60}")
    private Long nonceCacheTimeoutSecond;

    /**
     * timestamp过期时间间隔(秒)
     * 默认为60s
     */
    @Value("${spectre.admin.openapi.timestampIntervalSecond:60}")
    private Integer timestampIntervalSecond;
    /**
     * 是否启用认证授权
     */
    @Value("${spectre.admin.openapi.authEnable:true}")
    private Boolean authEnable;

    /**
     * debug模式打印请求头信息
     */
    @Value("${spectre.admin.openapi.debug:false}")
    private Boolean debug;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private OpenApiAppService openApiAppService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!Objects.equals(authEnable, true)) {
            // 未开启认证授权
            return true;
        }
        this.debugPrint(request);

        try {
            boolean isAccess = this.doPreHandle(request);
            if (!isAccess) {
                // 未通过检测
                rejectResponse(response);
                return false;
            }
            return true;
        } finally {
            CODE_HOLDER.remove();
        }

    }

    /**
     * 做前置处理
     *
     * @param request
     * @return
     */
    private boolean doPreHandle(HttpServletRequest request) {
        RequestHeader header = ServletUtils.toHeader(request);
        // 检测必填参数
        if (!this.checkRequiredParams(header)) {
            return false;
        }

        // 检测时间是否过期
        if (!this.checkTimestamp(header)) {
            return false;
        }

        // 检测随机码是否正常
        if (!this.checkNonce(header)) {
            return false;
        }

        // sign值校验
        if (!this.checkSign(header)) {
            return false;
        }

        // 校验通过 记录nonce缓存
        this.recordNonce(header);
        return true;
    }

    /**
     * 检测必填参数
     *
     * @param header
     * @return
     */
    private boolean checkRequiredParams(RequestHeader header) {
        String appKey = header.getAppKey();
        if (StringUtils.isBlank(appKey)) {
            // 返回错误信息
            log.warn("未在请求头中设置appKey");
            CODE_HOLDER.set(CodeDefinition.EMPTY_APP_KEY.getCode());
            return false;
        }
        String timestamp = header.getTimestamp();
        if (StringUtils.isBlank(timestamp)) {
            // timestamp不能够为空
            log.warn("未在请求头中设置timestamp");
            CODE_HOLDER.set(CodeDefinition.EMPTY_TIMESTAMP.getCode());
            return false;
        }
        String nonce = header.getNonce();
        if (StringUtils.isBlank(nonce)) {
            // nonce不能为空
            log.warn("未在请求头中设置nonce");
            CODE_HOLDER.set(CodeDefinition.EMPTY_NONCE.getCode());
            return false;
        }

        String sign = header.getSign();
        if (StringUtils.isBlank(sign)) {
            // sign不能为空
            log.warn("未在请求头中设置sign");
            CODE_HOLDER.set(CodeDefinition.EMPTY_SIGN.getCode());
            return false;
        }

        String appSecret = openApiAppService.getAppSecret(appKey);
        if (StringUtils.isBlank(appSecret)) {
            // appSecret不存在
            log.warn("应用未被授权,appKey = {}", appKey);
            CODE_HOLDER.set(CodeDefinition.UNAUTHORIZED.getCode());
            return false;
        }

        return true;
    }

    /**
     * 检测timestamp是否过期
     *
     * @param header
     * @return
     */
    private boolean checkTimestamp(RequestHeader header) {
        String timestamp = header.getTimestamp();
        Long toUseSecond = null;
        try {
            toUseSecond = Long.parseLong(timestamp);
        } catch (Exception e) {
            log.warn("传入的timestamp不合法,timestamp = {}", timestamp, e);
        }
        if (Objects.isNull(toUseSecond)) {
            // timestamp不合法
            CODE_HOLDER.set(CodeDefinition.TIMESTAMP_ILLEGAL.getCode());
            return false;
        }

        long nowSecond = System.currentTimeMillis() / 1000L;
        long intervalSecond = Math.abs(nowSecond - toUseSecond);
        if (intervalSecond > timestampIntervalSecond) {
            // timestamp 已过期
            log.warn("timestamp已过期,nowSecond ={},intervalSecond = {},timestamp ={}", nowSecond, intervalSecond, timestamp);
            CODE_HOLDER.set(CodeDefinition.TIMESTAMP_EXPIRE.getCode());
            return false;
        }

        return true;
    }

    /**
     * 检测nonce是否合法
     * <p>
     * 不能重复，长度不能够超过32位
     *
     * @param header
     * @return
     */
    private boolean checkNonce(RequestHeader header) {
        String nonce = header.getNonce();
        if (nonce.length() > MAX_NONCE_LENGTH) {
            // 随机数大于最大长度
            log.warn("nonce长度大于最大长度32位, nonce ={}", nonce);
            CODE_HOLDER.set(CodeDefinition.NONCE_LENGTH_OVER.getCode());
            return false;
        }
        String appKey = header.getAppKey();
        // nonce redis 缓存
        String nonceKey = OpenApiConstants.Redis.NONCE_KEY_STR + appKey + ":" + nonce;
        if (Objects.equals(stringRedisTemplate.hasKey(nonceKey), true)) {
            // nonce重复
            log.warn("nonce数据重复,nonce ={}", nonce);
            CODE_HOLDER.set(CodeDefinition.NONCE_REPEAT.getCode());
            return false;
        }

        return true;
    }

    /**
     * 签名校验
     *
     * @param header
     * @return
     */
    private boolean checkSign(RequestHeader header) {
        String appKey = header.getAppKey();
        String appSecret = openApiAppService.getAppSecret(appKey);
        String timestamp = header.getTimestamp();
        String nonce = header.getNonce();
        String headerSign = header.getSign();
        String checkSign = SignUtils.getSign(appKey, nonce, timestamp, appSecret);
        if (!StringUtils.equals(headerSign, checkSign)) {
            // sign值校验失败
            log.warn("sign值校验失败,headerSign = {},checkSign ={},appKey = {}", headerSign, checkSign, appKey);
            CODE_HOLDER.set(CodeDefinition.SIGN_ILLEGAL.getCode());
            return false;
        }
        return true;
    }

    /**
     * 记录nonce到缓存中 防止nonce重复请求
     *
     * @param header
     */
    private void recordNonce(RequestHeader header) {
        String appKey = header.getAppKey();
        String nonce = header.getNonce();
        // nonce redis 缓存
        String nonceKey = OpenApiConstants.Redis.NONCE_KEY_STR + appKey + ":" + nonce;
        stringRedisTemplate.opsForValue().set(nonceKey, nonce, nonceCacheTimeoutSecond, TimeUnit.SECONDS);
    }

    /**
     * 异常响应
     *
     * @param response
     */
    private void rejectResponse(HttpServletResponse response) {
        String code = CODE_HOLDER.get();
        if (StringUtils.isBlank(code)) {
            code = CodeDefinition.UNKNOWN.getCode();
        }

        String message = CodeDefinition.getMessage(code);
        if (StringUtils.isBlank(message)) {
            message = "未定义编码异常";
        }

        ActionResult<String> rejectResult = new ActionResult<>(code, message, null);
        ServletUtils.write(response, rejectResult);
    }


    /**
     * debug模式打印请求信息
     *
     * @param request
     */
    private void debugPrint(HttpServletRequest request) {
        if (!this.isDebug()) {
            return;
        }

        String requestURI = request.getRequestURI();
        String remoteAddr = request.getRemoteAddr();
        RequestHeader header = ServletUtils.toHeader(request);
        log.info("=================== start >>>>>>");
        log.info("=================== requestURI = {}", requestURI);
        log.info("=================== remoteAddr = {}", remoteAddr);
        log.info("=================== header = {}", header);
        log.info("=================== end >>>>>>");
    }

    /**
     * 当前是否处于debug模式
     *
     * @return
     */
    private boolean isDebug() {
        return Objects.equals(true, this.debug);
    }
}
