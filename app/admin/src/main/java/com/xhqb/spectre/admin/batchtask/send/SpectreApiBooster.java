package com.xhqb.spectre.admin.batchtask.send;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadLocalRandom;

/**
 * api 请求包装
 * <p>
 * 按照spectre api提供的文档进行加签操作
 * <p>
 * 加签逻辑
 * 1. 拼接字待加签的字符串 appKey+appCode+nonce+NonceValue+timestamp+TimeValue+appSecret
 * 2. 根据第1步生成的字符串进行sha1生成签名
 *
 * <AUTHOR>
 * @date 2021/10/11
 */
@Component
@Slf4j
public class SpectreApiBooster {

    /**
     * 最大的随机数长度
     */
    private static final int MAX_NONCE_LENGTH = 32;

    /**
     * 请求头 存储appCode
     */
    private static final String H_APP_CODE = "appKey";
    /**
     * 随机数
     */
    private static final String H_NONCE = "nonce";
    /**
     * 当前时间戳 (unix time 单位秒)
     */
    private static final String H_TIMESTAMP = "timestamp";
    /**
     * 请求头签名信息
     */
    private static final String H_SIGN = "sign";
    /**
     * 请求头requestId
     */
    private static final String H_REQUEST_ID = "requestId";

    /**
     * 随机数seed
     */
    private static final long DEFAULT_BOUND = 100000L;

    /**
     * 设置http请求头
     * <p>
     * 增强 spectre api 加签功能
     *
     * @param messageRequest
     * @param headers
     */
    public void boost(MessageRequest messageRequest, HttpHeaders headers) {
        String appCode = messageRequest.getAppCode();
        String appSecret = messageRequest.getAppSecret();
        String nonce = this.getNonce();
        String timestamp = this.getTimestamp();
        headers.add(H_APP_CODE, appCode);
        headers.add(H_NONCE, nonce);
        headers.add(H_TIMESTAMP, timestamp);
        headers.add(H_SIGN, this.getSign(appCode, nonce, timestamp, appSecret));

        String requestId = messageRequest.getRequestId();
        if (StringUtils.isNotBlank(requestId)) {
            headers.add(H_REQUEST_ID, requestId);
        }

    }

    /**
     * 获取到签名值
     *
     * @param appCode   应用编码
     * @param nonce     随机数
     * @param timestamp unix time 时间搓 (秒)
     * @param appSecret 应用秘钥
     * @return
     */
    public String getSign(String appCode, String nonce, String timestamp, String appSecret) {
        StringBuilder sb = new StringBuilder();
        sb.append(H_APP_CODE).append(appCode);
        sb.append(H_NONCE).append(nonce);
        sb.append(H_TIMESTAMP).append(timestamp);
        sb.append(appSecret);
        return DigestUtils.sha1Hex(sb.toString());
    }

    /**
     * 获取到随机数
     *
     * @return
     */
    public String getNonce() {
        try {
            // 防止随机数重复
            double ranValue = ThreadLocalRandom.current().nextDouble();
            String nonce = System.currentTimeMillis() + "" + String.valueOf(ranValue).substring(2);
            if (nonce.length() > MAX_NONCE_LENGTH) {
                nonce = nonce.substring(0, MAX_NONCE_LENGTH);
            }
            return nonce;
        } catch (Exception e) {
            log.warn("获取到随机数Nonce失败", e);
        }
        // 兜底返回
        return (ThreadLocalRandom.current().nextLong(DEFAULT_BOUND)) + "" + System.nanoTime();
    }

    /**
     * 获取到当前的时间搓
     *
     * @return
     */
    private String getTimestamp() {
        return (System.currentTimeMillis() / 1000) + "";
    }

    /**
     * 生成随机数
     *
     * @return
     */
    public String randomValue() {
        return getNonce();
    }
}
