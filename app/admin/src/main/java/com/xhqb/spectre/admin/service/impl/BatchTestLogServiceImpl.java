package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.model.vo.BatchTestLogVO;
import com.xhqb.spectre.admin.service.BatchTestLogService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.BatchTestLogDO;
import com.xhqb.spectre.common.dal.mapper.BatchTestLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 群发测试日志
 *
 * <AUTHOR>
 * @date 2021/12/15
 */
@Service
@Slf4j
public class BatchTestLogServiceImpl implements BatchTestLogService {

    @Resource
    private BatchTestLogMapper batchTestLogMapper;

    /**
     * 根据群发id查询群发测试日志
     *
     * @param taskId
     * @return
     */
    @Override
    public List<BatchTestLogVO> queryTestLog(Integer taskId) {
        List<BatchTestLogDO> batchTestLogList = batchTestLogMapper.queryTestLog(taskId);
        if (CommonUtil.isEmpty(batchTestLogList)) {
            return null;
        }
        return batchTestLogList.stream().map(BatchTestLogVO::buildInfoQuery).collect(Collectors.toList());
    }
}
