package com.xhqb.spectre.admin.service.test.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.batchtask.MessageSendFactory;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryContext;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryResult;
import com.xhqb.spectre.admin.cache.impl.ParamsCodeMemoryCache;
import com.xhqb.spectre.admin.cache.impl.ParamsValueMemoryCache;
import com.xhqb.spectre.admin.cache.impl.TestMobileMemoryCache;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.constant.Apis;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.TypeWeightDTO;
import com.xhqb.spectre.admin.service.detection.EmptyNumberDetection;
import com.xhqb.spectre.admin.util.RandomValueGeneratorUtil;
import com.xhqb.spectre.common.dal.entity.ParamsCodeDO;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.entity.test.TestTaskDO;
import com.xhqb.spectre.common.dal.entity.test.TestTaskLogDO;
import com.xhqb.spectre.common.dal.entity.test.TestTplDO;
import com.xhqb.spectre.common.dal.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TestTaskService {

    @Resource
    private TplMapper tplMapper;
    @Resource
    private TestTplMapper testTplMapper;
    @Resource
    private TestTaskLogMapper taskLogMapper;
    @Resource
    private TestTaskMapper taskMapper;
    @Resource
    private ParamsValueMemoryCache paramsValueMemoryCache;

    @Resource
    private ParamsCodeMemoryCache paramsCodeMemoryCache;

    @Resource
    private TestMobileMemoryCache testMobileMemoryCache;
    @Resource
    private SignMapper signMapper;
    @Resource
    private MessageSendFactory messageSendFactory;
    @Resource
    private VenusConfig venusConfig;
    @Resource
    private EmptyNumberDetection emptyNumberDetection;

    public List<String> execute(TestTaskDO taskDO) {
        // 测试模版
        TestTplDO testTplDO = testTplMapper.selectByPrimaryKey(taskDO.getTplId());
        // 短信模版
        TplDO tplDO = tplMapper.selectByPrimaryKey(testTplDO.getSmsTplId());
        // 下发明细
        List<TestTaskLogDO> taskLogDOList = taskLogMapper.selectByTaskId(taskDO.getTaskId());
        List<List<String>> paramMapList = new ArrayList<>();
        List<String> mobileTmpList = new ArrayList<>();
        for (TestTaskLogDO taskLogDO : taskLogDOList) {
            mobileTmpList.add(taskLogDO.getMobile());
            paramMapList.add(Arrays.stream(taskLogDO.getParams().split(",")).collect(Collectors.toList()));
        }
        SingleFactoryContext singleFactoryContext = new SingleFactoryContext();
        singleFactoryContext.setAppCode(tplDO.getAppCode());
        singleFactoryContext.setTplCode(tplDO.getCode());
        singleFactoryContext.setMobileList(mobileTmpList);
        //构造短信参数
        singleFactoryContext.setParamMapList(paramMapList);
        // 营销设置设置smsTypeCode值
        singleFactoryContext.setSmsCodeType(tplDO.getSmsTypeCode());
        singleFactoryContext.setChannelAccountId(testTplDO.getChannelAccountId());
        singleFactoryContext.setRequestId(taskDO.getTaskId());
        singleFactoryContext.setApi(Apis.SpectreApi.TEST_TASK_API_NAME);
        Integer signId = tplDO.getSignId();
        SignDO signDO = signMapper.selectByPrimaryKey(signId);
        if (Objects.nonNull(signDO)) {
            // 设置签名编码
            singleFactoryContext.setSignCode(signDO.getCode());
        }
        log.info("execute 执行参数：{}", JSONUtil.toJsonStr(singleFactoryContext));
        List<SingleFactoryResult> factoryResultList = messageSendFactory.singleSendMessage(singleFactoryContext);

        // 返回发送失败的手机号码
        List<String> allFailMobile = SingleFactoryResult.getAllFailMobile(factoryResultList);
        taskLogDOList.forEach(taskLogDO -> {
            if (allFailMobile.contains(taskLogDO.getMobile())) {
                taskLogDO.setSendStatus(1);
            } else {
                taskLogDO.setSendStatus(0);
            }
            taskLogDO.setUpdateTime(new Date());
        });
        // 更新发送状态
        taskLogDOList.forEach(testTaskLogDO -> taskLogMapper.updateSendStatus(testTaskLogDO));
        return allFailMobile;
    }

    public List<TestTaskLogDO> createTaskLog(TestTaskDO taskDO) {
        // 根据测试模版id 获取基本信息
        TestTplDO testTplDO = testTplMapper.selectByPrimaryKey(taskDO.getTplId());
        //检验
        check(testTplDO);
        // 构建手机号
        List<String> mobileList = buildMobile(testTplDO).stream().distinct().collect(Collectors.toList());
        log.info("手机号：{}", JSONUtil.toJsonStr(mobileList));
        // 构建短信参数
        List<List<String>> paramList = buildParam(testTplDO, mobileList);
        log.info("短信参数：{}", JSONUtil.toJsonStr(paramList));
        List<TestTaskLogDO> taskLogDOList = new ArrayList<>();
        for (int i = 0; i < mobileList.size(); i++) {
            TestTaskLogDO taskLogDO = new TestTaskLogDO();
            taskLogDO.setMobile(mobileList.get(i));
            List<String> singleParams = new ArrayList<>();
            for (List<String> strings : paramList) {
                singleParams.add(strings.get(i));
            }
            taskLogDO.setParams(String.join(",", singleParams));
            taskLogDO.setTaskId(taskDO.getTaskId());
            taskLogDO.setChannelAccountId(testTplDO.getChannelAccountId());
            taskLogDO.setTplId(testTplDO.getId());
            taskLogDO.setSendStatus(-1);
            taskLogDO.setCreateTime(new Date());
            taskLogDO.setUpdateTime(new Date());
            taskLogDOList.add(taskLogDO);
        }
        log.info("模板个数:{} | 短信参数个数:{} | 手机号个数:{}", testTplDO.getMobileCount(), paramList.size(), mobileList.size());
        return taskLogDOList;
    }

    private List<String> buildMobile(TestTplDO testTplDO) {
        List<String> resultList = new ArrayList<>();
        if (testTplDO.getSource() == 1) {
            // 1. 根据模版配置的参数获取手机号
            String mobiles = testTplDO.getMobiles();
            if (Strings.isBlank(mobiles)) {
                throw new BizException("手机号不能为空");
            }
            Collections.addAll(resultList, mobiles.split(","));
            return resultList;
        } else if (testTplDO.getSource() == 0) {
            // 2. 随机生成手机号
            List<String> modelMobileList = buildModelMobileList(testTplDO);
            Map<Integer, List<String>> memoryCacheMap = testMobileMemoryCache.getTypeMap();
            for (Map.Entry<Integer, List<String>> entry : memoryCacheMap.entrySet()) {
                List<String> value = entry.getValue();
                if (CollectionUtil.isNotEmpty(value) && CollectionUtil.isNotEmpty(modelMobileList)) {
                    value.removeAll(modelMobileList);
                }
            }
            String typeWeight = testTplDO.getTypeWeight();
            List<TypeWeightDTO> typeWeightDTOS = JSONUtil.toList(testTplDO.getTypeWeight(), TypeWeightDTO.class);
            Integer mobileCount = testTplDO.getMobileCount();
            int sum = typeWeightDTOS.stream().mapToInt(TypeWeightDTO::getWeight).sum();
            List<String> emptyNumList = new ArrayList<>();
            for (TypeWeightDTO typeWeightDTO : typeWeightDTOS) {
                if (typeWeightDTO.getWeight() == 0) {
                    continue;
                }
                List<String> values = memoryCacheMap.get(typeWeightDTO.getType());
                log.info("type:{} | values:{}", typeWeightDTO.getType(), JSONUtil.toJsonStr(values));
                double sumWeight = (double) typeWeightDTO.getWeight() / sum;
                int typeCount = (int) ((double) mobileCount * sumWeight);
                log.info("type:{} | sumWeight:{} | typeCount:{}", typeWeightDTO.getType(), sumWeight, typeCount);
                List<String> randomValues = RandomValueGeneratorUtil.randomValues(values, typeCount, 3);
                if (Objects.equals(typeWeightDTO.getType(), 0) && venusConfig.getEmptyDetectEnable()) {
                    randomValues = emptyNumberDetection.sendDetect(randomValues);
                }
                resultList.addAll(randomValues);
            }
        }
        return resultList;
    }

    private List<String> buildModelMobileList(TestTplDO testTplDO) {
        // 获取当天tplId --> 对应有那些taskId ---> 再接着找到对应的 mobile ---> 再剔除掉
        Date startTime = DateUtil.beginOfDay(new Date());
        Date endTime = DateUtil.endOfDay(new Date());
        List<String> taskIdList = taskMapper.selectByTplIdAndTime(testTplDO.getId(), startTime, endTime)
                .stream().map(TestTaskDO::getTaskId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(taskIdList)) {
            return new ArrayList<>();
        }
        return taskLogMapper.selectByTaskIdList(taskIdList)
                .stream().map(TestTaskLogDO::getMobile).collect(Collectors.toList());
    }

    private List<List<String>> buildParam(TestTplDO testTplDO, List<String> mobileList) {
        Map<String, List<String>> basicParamMap = testMobileMemoryCache.paramList(mobileList);
        log.info("basicParamMap:{}", JsonLogUtil.toJSONString(basicParamMap));
        String params = testTplDO.getParams();
        List<List<String>> resultList = new LinkedList<>();
        if (Strings.isBlank(params)) {
            return resultList;
        }
        String[] paramArray = params.split(",");
        for (String tempParam : paramArray) {
            // 已经知道 是否是固化参数
            // 是的
            ParamsCodeDO paramsCodeDO = paramsCodeMemoryCache.cache().stream()
                    .filter(codeDO -> Objects.equals("basic", codeDO.getType()))
                    .filter(codeDO -> Objects.equals(tempParam, codeDO.getCode())).findFirst().orElse(null);
            if (Objects.nonNull(paramsCodeDO)) {
                resultList.add(basicParamMap.get(tempParam));
                continue;
            }
            // 不是保持原样
            List<String> tempParamValues = paramsValueMemoryCache.getValues(tempParam);
            List<String> resultValues = RandomValueGeneratorUtil.randomValues(tempParamValues, mobileList.size());
            resultList.add(resultValues);
        }
        return resultList;
    }

    public void check(TestTplDO testTplDO) {
        String[] timeSplit = testTplDO.getTimePeriod().split("-");
        DateTime todayStartTime = DateUtil.parse(DateUtil.today() + " " + timeSplit[0]);
        DateTime todayEndTime = DateUtil.parse(DateUtil.today() + " " + timeSplit[1]);
        // cur时间在不在这个中间
        long curTime = System.currentTimeMillis();
        if (curTime < todayStartTime.getTime() || curTime > todayEndTime.getTime()) {
            throw new BizException("当前时间不在发送时间段内");
        }
        // 最大次数校验
        List<TestTaskDO> testTaskDOList = taskMapper.selectByTplId(testTplDO.getId());
        if (testTaskDOList.size() > testTplDO.getMaxTimes()) {
            throw new BizException("当前任务关联测试模版发送次数超过最大次数");
        }
    }
}
