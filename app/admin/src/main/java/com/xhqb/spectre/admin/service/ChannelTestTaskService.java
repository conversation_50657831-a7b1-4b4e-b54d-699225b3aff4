package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.TestTaskDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ChannelTestTaskVO;
import com.xhqb.spectre.admin.mq.message.ChannelTestData;
import com.xhqb.spectre.common.dal.dto.mq.ApiChannelMessage;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ChannelTestTaskQuery;

import java.util.List;

public interface ChannelTestTaskService {
    CommonPager<ChannelTestTaskVO> listByPage(ChannelTestTaskQuery channelTestTaskQuery);

    String add(TestTaskDTO testTaskDTO);

    String cancel(String taskId);

    String delete(String taskId);

    List<String> execute(String taskId);

    List<String> apiChannelTestTask(List<ApiChannelMessage> apiChannelMessageList );
}
