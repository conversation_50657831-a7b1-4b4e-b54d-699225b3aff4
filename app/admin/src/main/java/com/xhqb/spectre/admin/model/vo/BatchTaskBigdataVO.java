package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.BatchTaskBigdataDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 大数据群发任务
 *
 * <AUTHOR>
 * @date 2021/10/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskBigdataVO implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 业务应用CODE
     */
    private String appCode;

    /**
     * 模板ID
     */
    private Integer tplId;

    /**
     * 文件url地址
     */
    private String fileUrl;

    /**
     * 状态 ，0：待处理；1：已处理；2：处理失败；3：处理中 ；4：已废弃(删除) ；5：已超时(taskNo查询一直处于处理中)
     */
    private Integer status;

    /**
     * 设定发送时间(unix时间戳,单位秒),设置为0表示立即发送
     */
    private Date sendTime;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 任务编号，由批量任务手动写入
     */
    private String taskNo;
    /**
     * 群发任务批次号，由批次任务写入
     */
    private Integer taskId;

    /**
     * 查询列表数据展现
     *
     * @param batchTaskBigdataDO
     * @return
     */
    public static BatchTaskBigdataVO buildListQuery(BatchTaskBigdataDO batchTaskBigdataDO) {
        return BatchTaskBigdataVO.builder()
                // 主键
                .id(batchTaskBigdataDO.getId())
                // 业务应用CODE
                .appCode(batchTaskBigdataDO.getAppCode())
                // 模板ID
                .tplId(batchTaskBigdataDO.getTplId())
                // 文件url地址
                .fileUrl(batchTaskBigdataDO.getFileUrl())
                // 状态 ，0：待处理；1：已处理；2：处理失败；3：处理中 ；4：已废弃
                .status(batchTaskBigdataDO.getStatus())
                // 设定发送时间(unix时间戳,单位秒),设置为0表示立即发送
                .sendTime(DateUtil.intToDate(batchTaskBigdataDO.getSendTime()))
                // 描述信息
                .description(batchTaskBigdataDO.getDescription())
                // 创建人
                .creator(batchTaskBigdataDO.getCreator())
                // 创建时间
                .createTime(batchTaskBigdataDO.getCreateTime())
                // 修改人
                .updater(batchTaskBigdataDO.getUpdater())
                // 更新时间
                .updateTime(batchTaskBigdataDO.getUpdateTime())
                // 任务编号，由批量任务手动写入
                .taskNo(batchTaskBigdataDO.getTaskNo())
                // 群发任务批次号，由批次任务写入
                .taskId(batchTaskBigdataDO.getTaskId())
                .build();
    }

    /**
     * 查询数据详情展现
     *
     * @param batchTaskBigdataDO
     * @return
     */
    public static BatchTaskBigdataVO buildInfoQuery(BatchTaskBigdataDO batchTaskBigdataDO) {
        return buildListQuery(batchTaskBigdataDO);
    }
}
