package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/7 14:57
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmppAliveInfoVO implements Serializable {

    private static final long serialVersionUID = -2539376010458801425L;

    /**
     * 主机地址
     */
    private String hostname;

    /**
     * 连接创建时间
     */
    private String startTime;

    /**
     * cmpp状态 1->在线 0->离线
     */
    private Integer cmppStatus;

    public static CmppAliveInfoVO buildCmppAliveInfoVO(String hostname, Integer startTime, Integer cmppStatus) {
        return CmppAliveInfoVO.builder()
                .hostname(hostname)
                .startTime(DateUtil.intToString(startTime))
                .cmppStatus(cmppStatus)
                .build();
    }
}
