package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.model.vo.BatchTaskLogVO;
import com.xhqb.spectre.admin.service.BatchTaskLogService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.BatchTaskLogDO;
import com.xhqb.spectre.common.dal.mapper.BatchTaskLogMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.BatchTaskLogQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 群发任务流水日志表
 *
 * <AUTHOR>
 * @date 2021/12/1
 */
@Service
@Slf4j
public class BatchTaskLogServiceImpl implements BatchTaskLogService {

    @Resource
    private BatchTaskLogMapper batchTaskLogMapper;

    /**
     * 批量插入任务流水信息
     *
     * @param batchTaskLogList
     */
    @Override
    public void batchInsert(List<BatchTaskLogDO> batchTaskLogList) {
        if (CommonUtil.isEmpty(batchTaskLogList)) {
            log.warn("批量插入群发任务流水信息为空");
            return;
        }

        try {
            batchTaskLogMapper.batchInsert(batchTaskLogList);
        } catch (Exception e) {
            log.error("批量插入群发任务流水信息失败,batchTaskLogList = {}", JSON.toJSONString(batchTaskLogList), e);
        }
    }

    /**
     * 分页查询群发任务日志
     *
     * @param batchTaskLogQuery
     * @return
     */
    @Override
    public CommonPager<BatchTaskLogVO> listByPage(BatchTaskLogQuery batchTaskLogQuery) {
        return PageResultUtils.result(
                () -> batchTaskLogMapper.countByQuery(batchTaskLogQuery),
                () -> batchTaskLogMapper.selectByQuery(batchTaskLogQuery).stream().map(BatchTaskLogVO::buildListQuery).collect(Collectors.toList())
        );
    }

    /**
     * 根据群发任务ID查询群发日志
     *
     * @param taskId
     * @return
     */
    @Override
    public List<BatchTaskLogVO> getByTaskId(Integer taskId) {
        if (Objects.isNull(taskId)) {
            return null;
        }
        return batchTaskLogMapper.selectByTaskId(taskId).stream().map(BatchTaskLogVO::buildListQuery).collect(Collectors.toList());
    }
}
