package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 错误码
 *
 * <AUTHOR>
 * @date 2021/10/20
 */
@Data
public class ErrorCodeDTO implements Serializable {

    /**
     * 错误码类型 submit->短信发送 deliver->短信回执
     */
    @NotNull
    @Pattern(regexp = "(submit)|(deliver)", message = "错误码类型只能为submit或者deliver")
    private String type;
    /**
     * 错误码编码
     */
    private Integer xhErrCode;
    /**
     * 错误码描述
     */
    @NotBlank(message = "错误码描述不能够为空")
    private String codeDesc;
    /**
     * 是否重发 0->否 1->是
     */
    private Integer retry;
}
