package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.readonly.vo.SmsReceiptPageVO;
import com.xhqb.spectre.common.dal.query.SmsReceiptQuery;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/30 15:17
 * @Description:
 */
public interface SmsReceiptService {

    Integer queryTotalCount(SmsReceiptQuery smsReceiptQuery);

    SmsReceiptPageVO listByPage(SmsReceiptQuery smsReceiptQuery);

    String queryMobile(Long id);
}
