package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.AddProjectDescDTO;
import com.xhqb.spectre.admin.model.dto.UpdateProjectDescDTO;
import com.xhqb.spectre.admin.model.vo.ProjectDescEnumVO;
import com.xhqb.spectre.admin.model.vo.ProjectDescVO;
import com.xhqb.spectre.admin.service.ProjectDescService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.ProjectDescDO;
import com.xhqb.spectre.common.dal.mapper.ProjectDescMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ProjectDescQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProjectDescServiceImpl implements ProjectDescService {

    @Autowired
    private ProjectDescMapper projectDescMapper;

    /**
     * 查询项目用途列表
     *
     * @param projectDescQuery
     * @return
     */
    @Override
    public CommonPager<ProjectDescVO> listByPage(ProjectDescQuery projectDescQuery) {
        return PageResultUtils.result(
                () -> projectDescMapper.countByQuery(projectDescQuery),
                () -> projectDescMapper.selectByQuery(projectDescQuery).stream().map(ProjectDescVO::buildVO).collect(Collectors.toList())
        );
    }

    /**
     * 查询签名详情
     *
     * @param id
     * @return
     */
    @Override
    public ProjectDescVO getById(Integer id) {
        ProjectDescDO projectDescDO = validateAndSelectById(id);
        return ProjectDescVO.buildVO(projectDescDO);
    }

    /**
     * 添加签名
     *
     * @param addProjectDescDTO
     */
    @Override
    public void create(AddProjectDescDTO addProjectDescDTO) {
        //校验
        checkAddParam(addProjectDescDTO);

        //写入签名信息
        ProjectDescDO projectDescDO = buildDO(addProjectDescDTO);
        projectDescMapper.insertSelective(projectDescDO);
    }

    /**
     * 编辑签名
     *
     * @param updateProjectDescDTO
     */
    @Override
    public void update(UpdateProjectDescDTO updateProjectDescDTO) {
        //校验
        checkUpdateParam(updateProjectDescDTO);

        //写入签名信息
        ProjectDescDO projectDescDO = buildDO(updateProjectDescDTO);
        projectDescMapper.updateByPrimaryKeySelective(projectDescDO);
    }

    /**
     * 启用签名
     *
     * @param id
     */
    @Override
    public void enable(Integer id) {
        ProjectDescDO projectDescDO = validateAndSelectById(id);
        if (!isDisabled(projectDescDO)) {
            throw new BizException("项目用途不处于停用状态，不能启用");
        }

        //修改状态为启用
        projectDescMapper.enable(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 停用签名
     *
     * @param id
     */
    @Override
    public void disable(Integer id) {
        ProjectDescDO projectDescDO = validateAndSelectById(id);
        if (!isEnabled(projectDescDO)) {
            throw new BizException("项目用途不处于启用状态，不能停用");
        }
        //修改状态为停用
        projectDescMapper.disable(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 删除签名
     *
     * @param id
     */
    @Override
    public void delete(Integer id) {
        ProjectDescDO projectDescDO= validateAndSelectById(id);
        if (!isDisabled(projectDescDO)) {
            throw new BizException("项目用途不处于停用状态，不能删除");
        }
        //删除记录
        projectDescMapper.delete(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 查询签名枚举
     *
     * @param status
     * @return
     */
    @Override
    public List<ProjectDescEnumVO> queryEnum(Integer status) {
        return projectDescMapper.selectEnum(status).stream().map(ProjectDescEnumVO::buildEnumVO).collect(Collectors.toList());
    }

    private ProjectDescDO validateAndSelectById(Integer id) {
        ProjectDescDO projectDescDO = projectDescMapper.selectByPrimaryKey(id);
        if (Objects.isNull(projectDescDO)) {
            throw new BizException("未找到项目用途");
        }
        return projectDescDO;
    }

    private void checkAddParam(AddProjectDescDTO addProjectDescDTO) {
        //参数格式校验
        ValidatorUtil.validate(addProjectDescDTO);

        //名称校验
        ProjectDescDO exist = projectDescMapper.selectByDescription(addProjectDescDTO.getDescription());
        if (Objects.nonNull(exist)) {
            throw new BizException("项目用途已存在");
        }
    }

    private void checkUpdateParam(UpdateProjectDescDTO updateProjectDescDTO) {
        //参数格式校验
        ValidatorUtil.validate(updateProjectDescDTO);

        //校验签名是否存在
        validateAndSelectById(updateProjectDescDTO.getId());
    }

    private ProjectDescDO buildDO(AddProjectDescDTO addProjectDescDTO) {
        String userName = SsoUserInfoUtil.getUserName();
        return ProjectDescDO.builder()
                .description(addProjectDescDTO.getDescription())
                .status(addProjectDescDTO.getStatus())
                .creator(userName)
                .updater(userName)
                .build();
    }

    private ProjectDescDO buildDO(UpdateProjectDescDTO updateProjectDescDTO) {
        return ProjectDescDO.builder()
                .id(updateProjectDescDTO.getId())
                .description(updateProjectDescDTO.getDescription())
                .updater(SsoUserInfoUtil.getUserName())
                .build();
    }

    private boolean isEnabled(ProjectDescDO projectDescDO) {
        return projectDescDO.getStatus().equals(CommonConstant.STATUS_VALID);
    }

    private boolean isDisabled(ProjectDescDO projectDescDO) {
        return projectDescDO.getStatus().equals(CommonConstant.STATUS_INVALID);
    }
}
