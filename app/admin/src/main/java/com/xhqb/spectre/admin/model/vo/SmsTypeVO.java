package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsTypeVO {

    private String name;

    private String code;

    public static SmsTypeVO toSmsTypeVO(MessageTypeEnum typeEnum) {
        return SmsTypeVO.builder()
                .name(typeEnum.getDescription())
                .code(typeEnum.getMessageType())
                .build();
    }
}
