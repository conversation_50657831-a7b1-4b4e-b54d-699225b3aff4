package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.vo.SmsDayStatisVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.SmsDayStatisQuery;

/**
 * 短信发送量日概况
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
public interface SmsDayStatisService {

    /**
     * 分页查询短信发送量日概况列表
     *
     * @param smsDayStatisQuery
     * @return
     */
    CommonPager<SmsDayStatisVO> listByPage(SmsDayStatisQuery smsDayStatisQuery);
}
