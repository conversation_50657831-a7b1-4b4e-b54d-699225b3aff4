package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.admin.model.vo.batchtask.CheckResultVO;
import com.xhqb.spectre.admin.model.vo.batchtask.QueryTaskSegmentVO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 群发短信DTO
 *
 * <AUTHOR>
 * @date 2021/9/17
 */
@Data
public class BatchTaskDTO implements Serializable {

    /**
     * 应用编码
     */
    @NotBlank(message = "应用编码不能为空")
    private String appCode;

    /**
     * 模板ID
     */
    @NotNull(message = "模板ID不能为空")
    private Integer tplId;
    /**
     * 签名ID
     */
    private Integer signId;
    /**
     * 短信类型编码
     */
    @NotBlank(message = "短信类型编码不能为空")
    private String smsTypeCode;
    /**
     * 内容
     */
    @NotBlank(message = "短信内容不能为空")
    private String content;
    /**
     * 名单中的用户标识类型，1：cid；2：手机号
     */
    private Integer userIdType;
    /**
     * 发送类型，1：立即发送；2：定时发送
     */
    @NotNull(message = "发送类型不能为空")
    private Integer sendType;
    /**
     * 定时发送时间
     */
    private Date sendTime;
    /**
     * 备注，发送原因
     */
    private String remark;
    /**
     * 文件检测结果列表
     */
    private List<CheckResultVO> checkResultList;
    /**
     * 短信群发任务的参数分片
     * 参考 BatchTaskParamDTO
     */
    private List<QueryTaskSegmentVO> taskParamList;

    /**
     * 大数据任务ID
     */
    private Integer bigdataId;
    /**
     * 项目用途
     */
    private String projectDesc;

    /**
     * 用户分组
     */
    private String userGroup;

    /**
     * 分发速率，0表示不限速，正数表示每秒处理的号码数
     */
    private Integer limitRate;

}
