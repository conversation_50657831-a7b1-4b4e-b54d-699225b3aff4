package com.xhqb.spectre.admin.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/29 14:05
 * @Description:
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface LogOpTime {

    //所属模块，1：业务应用；2：渠道；3：渠道账号；4：签名；5：模板；6：渠道账号屏蔽；7：黑名单；8：白名单；9：应用限流；10：错误码映射；
    // 11：短链接配置；12：网关模板文案映射；13：网关用户；14：短信类型屏蔽
    int value() default 0;
}
