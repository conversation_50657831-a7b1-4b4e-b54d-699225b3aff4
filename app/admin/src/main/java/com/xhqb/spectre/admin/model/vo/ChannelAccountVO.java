package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.ChannelAccountDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 账号信息
 *
 * <AUTHOR>
 * @date 2021/9/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelAccountVO implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 短信类型编码
     */
    private String smsTypeCode;
    /**
     * 账号名称
     */
    private String name;
    /**
     * 渠道账号
     */
    private String key;
    /**
     * 渠道账号json配置参数
     */
    private String jsonMapping;
    /**
     * 费率，千分位存储
     */
    private Integer price;
    /**
     * 协议，1：http；2：cmpp
     */
    private Integer protocol;
    /**
     * 运营商列表
     */
    private List<String> ispList;
    /**
     * 地域过滤类型，1：包含；2：不包含
     */
    private Integer areaFilterType;
    /**
     * 地域列表，json数组结构
     */
    private List<AreaVO> areaList;
    /**
     * 权重(综合分)
     */
    private Integer weight;
    /**
     * 状态，0：无效，1：有效
     */
    private Integer status;
    /**
     * 是否免模板审核，1：是；0：否
     */
    private Integer isTplFreeAudit;
    /**
     * 支持的签名ID列表
     */
    private List<Integer> signIdList;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateTime;

    /*
      =============================================
      =============================================
      =============================================
     */

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 查询列表数据展现
     *
     * @param channelAccountDO
     * @return
     */
    public static ChannelAccountVO buildListQuery(ChannelAccountDO channelAccountDO) {
        return buildInfoQuery(channelAccountDO);
    }


    /**
     * 查询数据详情展现
     *
     * @param channelAccountDO
     * @return
     */
    public static ChannelAccountVO buildInfoQuery(ChannelAccountDO channelAccountDO) {

        return ChannelAccountVO.builder()
                // 主键
                .id(channelAccountDO.getId())
                // 渠道编码
                .channelCode(channelAccountDO.getChannelCode())
                // 短信类型编码
                .smsTypeCode(channelAccountDO.getSmsTypeCode())
                // 渠道名称
                .channelName(channelAccountDO.getName())
                // 账号名称
                .name(channelAccountDO.getName())
                // 渠道账号
                .key(channelAccountDO.getKey())
                // 渠道账号json配置参数
                .jsonMapping(channelAccountDO.getJsonMapping())
                // 费率，千分位存储
                .price(channelAccountDO.getPrice())
                // 协议，1：http；2：cmpp
                .protocol(channelAccountDO.getProtocol())
                // 运营商列表
                .ispList(CommonUtil.ispToList(channelAccountDO.getIsps()))
                // 地域过滤类型，1：包含；2：不包含
                .areaFilterType(channelAccountDO.getAreaFilterType())
                // 地域列表，json数组结构
                .areaList(CommonUtil.areaToList(channelAccountDO.getAreas()))
                // 权重
                .weight(channelAccountDO.getWeight())
                // 状态，0：无效，1：有效
                .status(channelAccountDO.getStatus())
                // 是否免模板审核，1：是；0：否
                .isTplFreeAudit(channelAccountDO.getIsTplFreeAudit())
                // 支持的签名ID列表
                .signIdList(StringUtils.isNotBlank(channelAccountDO.getSignIds()) ? Arrays.stream(channelAccountDO.getSignIds().split(","))
                        .map(Integer::valueOf).collect(Collectors.toList()) : Collections.emptyList())
                // 备注
                .remark(channelAccountDO.getRemark())
                // 创建人
                .creator(channelAccountDO.getCreator())
                // 创建时间
                .createTime(channelAccountDO.getCreateTime())
                // 修改人
                .updater(channelAccountDO.getUpdater())
                // 更新时间
                .updateTime(channelAccountDO.getUpdateTime())
                .build();
    }


}
