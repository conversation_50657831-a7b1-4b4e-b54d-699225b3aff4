package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.MarketSceneDTO;
import com.xhqb.spectre.admin.model.vo.MarketSceneVO;
import com.xhqb.spectre.admin.service.MarketSceneService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.MarketSceneDO;
import com.xhqb.spectre.common.dal.mapper.MarketSceneMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.MarketSceneQuery;
import com.xhqb.spectre.common.enums.DeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 营销场景服务
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
@Service
@Slf4j
public class MarketSceneServiceImpl implements MarketSceneService {

    @Resource
    private MarketSceneMapper marketSceneMapper;

    /**
     * 分页查询营销场景列表
     *
     * @param marketSceneQuery
     * @return
     */
    @Override
    public CommonPager<MarketSceneVO> listByPage(MarketSceneQuery marketSceneQuery) {
        return PageResultUtils.result(
                () -> marketSceneMapper.countByQuery(marketSceneQuery),
                () -> marketSceneMapper.selectByQuery(marketSceneQuery).stream().map(MarketSceneVO::buildMarketSceneVO).collect(Collectors.toList())
        );
    }

    /**
     * 查询营销场景详情
     *
     * @param id
     * @return
     */
    @Override
    public MarketSceneVO getById(Integer id) {
        MarketSceneDO marketScene = marketSceneMapper.selectByPrimaryKey(id);
        return MarketSceneVO.buildMarketSceneVO(marketScene);
    }

    /**
     * 新增营销场景
     *
     * @param marketSceneDTO
     * @return 返回营销场景ID
     */
    @Override
    public Integer create(MarketSceneDTO marketSceneDTO) {
        //参数格式校验
        ValidatorUtil.validate(marketSceneDTO);
        String currentUser = SsoUserInfoUtil.getUserName();
        MarketSceneDO marketSceneDO = MarketSceneDO.builder()
                .name(marketSceneDTO.getName())
                .status(marketSceneDTO.getStatus())
                .creator(currentUser)
                .updater(currentUser)
                .build();
        marketSceneMapper.insertSelective(marketSceneDO);
        return marketSceneDO.getId();
    }

    /**
     * 更新营销场景
     *
     * @param id
     * @param marketSceneDTO
     * @return
     */
    @Override
    public Integer update(Integer id, MarketSceneDTO marketSceneDTO) {
        if (Objects.isNull(marketSceneMapper.selectByPrimaryKey(id))) {
            throw new BizException("营销场景不存在");
        }
        //参数格式校验
        ValidatorUtil.validate(marketSceneDTO);
        String currentUser = SsoUserInfoUtil.getUserName();
        MarketSceneDO marketSceneDO = MarketSceneDO.builder()
                .id(id)
                .name(marketSceneDTO.getName())
                .status(marketSceneDTO.getStatus())
                .creator(currentUser)
                .updater(currentUser)
                .build();
        marketSceneMapper.updateByPrimaryKeySelective(marketSceneDO);
        return id;
    }

    /**
     * 营销场景状态调整
     *
     * @param id
     * @param status 状态，0：无效，1：有效
     * @return
     */
    @Override
    public Integer status(Integer id, int status) {
        if (Objects.isNull(marketSceneMapper.selectByPrimaryKey(id))) {
            throw new BizException("营销场景不存在");
        }
        MarketSceneDO marketSceneDO = MarketSceneDO.builder()
                .id(id)
                .status(status)
                .updater(SsoUserInfoUtil.getUserName())
                .build();
        marketSceneMapper.updateByPrimaryKeySelective(marketSceneDO);
        return id;
    }

    /**
     * 删除营销场景
     *
     * @param id
     * @return
     */
    @Override
    public Integer deleteById(Integer id) {
        if (Objects.isNull(marketSceneMapper.selectByPrimaryKey(id))) {
            throw new BizException("营销场景不存在");
        }
        MarketSceneDO marketSceneDO = MarketSceneDO.builder()
                .id(id)
                .isDelete(DeleteEnum.DELETED.getCode())
                .updater(SsoUserInfoUtil.getUserName())
                .build();
        marketSceneMapper.updateByPrimaryKeySelective(marketSceneDO);
        return id;
    }

    /**
     * 查询所有的营销场景信息
     *
     * @param status 状态，0：无效，1：有效 , 为空查所有
     * @return
     */
    @Override
    public List<MarketSceneVO> listAll(Integer status) {
        List<MarketSceneDO> marketSceneList = marketSceneMapper.listAll(status);
        if (CommonUtil.isEmpty(marketSceneList)) {
            return Collections.emptyList();
        }
        return marketSceneList.stream().map(MarketSceneVO::buildMarketSceneVO).collect(Collectors.toList());
    }

    /**
     * 新增营销场景与模板的关联关系
     *
     * @param tplId
     * @param marketSceneId
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void addMarketSceneTpl(Integer tplId, Integer marketSceneId) {
        if (Objects.isNull(tplId) || Objects.isNull(marketSceneId)) {
            log.warn("新增营销场景与模板的关联关系缺少必填参数, tplId = {}, marketSceneId = {}", tplId, marketSceneId);
            return;
        }
        String creator = SsoUserInfoUtil.getUserName();
        // 首先查看市场营销与模板的关联关系是否存在，不存在则进行保存;
        // 存在一个并且与当前marketSceneId一致，则不做任何操作;
        // 否则进行先删除 再做保存处理逻辑
        List<Integer> marketSceneIdList = marketSceneMapper.selectMarketSceneIdByTplId(tplId);
        if (CommonUtil.isEmpty(marketSceneIdList)) {
            marketSceneMapper.insertMarketSceneTpl(tplId, marketSceneId, creator);
            return;
        }

        if (marketSceneIdList.size() == 1 && Objects.equals(marketSceneId, marketSceneIdList.get(0))) {
            return;
        }

        marketSceneMapper.deleteMarketSceneTpl(tplId, creator);
        marketSceneMapper.insertMarketSceneTpl(tplId, marketSceneId, creator);
    }

    /**
     * 根据模板ID查询营销场景ID
     *
     * @param tplId
     * @return
     */
    @Override
    public Integer getMarketSceneIdByTpl(Integer tplId) {
        List<Integer> marketSceneIdList = marketSceneMapper.selectMarketSceneIdByTplId(tplId);
        return CollectionUtils.isNotEmpty(marketSceneIdList) ? marketSceneIdList.get(0) : null;
    }

    @Override
    public int findOrCreateMarketScene(String marketSceneName) {
        MarketSceneDO modelDO = marketSceneMapper.selectByName(marketSceneName);
        if (Objects.nonNull(modelDO)) {
            return modelDO.getId();
        }
        String currentUser = SsoUserInfoUtil.getUserName();
        MarketSceneDO marketSceneDO = MarketSceneDO.builder()
                .name(marketSceneName)
                .status(1)
                .creator(currentUser)
                .updater(currentUser)
                .build();
        marketSceneMapper.insertSelective(marketSceneDO);
        return marketSceneDO.getId();
    }

    @Override
    public String getMarketSceneByTpl(Integer tplId) {
        List<Integer> marketSceneIdList = marketSceneMapper.selectMarketSceneIdByTplId(tplId);
        Integer marketSceneId = CollectionUtils.isNotEmpty(marketSceneIdList) ? marketSceneIdList.get(0) : null;
        if (Objects.isNull(marketSceneId)) {
            return null;
        }
        MarketSceneDO marketSceneDO = marketSceneMapper.selectByPrimaryKey(marketSceneId);
        return Objects.isNull(marketSceneDO) ? null : marketSceneDO.getName();
    }
}
