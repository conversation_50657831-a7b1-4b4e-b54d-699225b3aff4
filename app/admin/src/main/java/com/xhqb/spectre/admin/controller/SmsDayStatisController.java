package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.SmsDayStatisVO;
import com.xhqb.spectre.admin.service.SmsDayStatisService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.SmsDayStatisQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * 短信发送量日概况
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
@RestController
@RequestMapping("/smsDayStatis")
@Slf4j
public class SmsDayStatisController {

    @Resource
    private SmsDayStatisService smsDayStatisService;

    /**
     * 查询短信发送量日概况列表
     *
     * @param smsDayStatisQuery 短信发送量日概况查询条件
     * @param code              多个以逗号分割
     * @param pageNum           当前页码
     * @param pageSize          一页显示的记录数
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(SmsDayStatisQuery smsDayStatisQuery, String code, Integer pageNum, Integer pageSize) {
        if (StringUtils.isNotBlank(code)) {
            smsDayStatisQuery.setCodeList(Arrays.asList(StringUtils.split(code, ",")));
        }
        smsDayStatisQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<SmsDayStatisVO> commonPager = smsDayStatisService.listByPage(smsDayStatisQuery);
        return AdminResult.success(commonPager);
    }
}
