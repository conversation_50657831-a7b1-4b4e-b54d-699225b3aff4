package com.xhqb.spectre.admin.statistics.vo;

import com.xhqb.spectre.admin.statistics.entity.SmsStatisSumDO;
import com.xhqb.spectre.admin.statistics.query.SmsStatisQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/8 10:26
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsStatisDetailVO implements Serializable {

    private static final long serialVersionUID = 862170296247254871L;

    private static final String STRING_DEFAULT_ALL = "ALL";
    private static final int INT_DEFAULT_ALL = -1;

    /**
     * 统计时间
     */
    private String date;

    /**
     * 批次ID
     */
    private Integer batchId;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 运营商
     */
    private String isp;

    /**
     * 发送量
     */
    private Integer sendCount;

    /**
     * 计费量
     */
    private Integer billCount;

    public static SmsStatisDetailVO build(SmsStatisSumDO item, SmsStatisQuery query, String channelName) {
        return SmsStatisDetailVO.builder()
                .date(item.getDate())
                .batchId(Objects.nonNull(query.getBatchId()) ? query.getBatchId() : INT_DEFAULT_ALL)
                .smsTypeCode(StringUtils.isNotBlank(query.getSmsTypeCode()) ? query.getSmsTypeCode() : STRING_DEFAULT_ALL)
                .tplCode(StringUtils.isNotBlank(query.getTplCode()) ? query.getTplCode() : STRING_DEFAULT_ALL)
                .channelCode(StringUtils.isNotBlank(channelName) ? channelName : STRING_DEFAULT_ALL)
                .province(StringUtils.isNotBlank(query.getProvince()) ? query.getProvince() : STRING_DEFAULT_ALL)
                .city(StringUtils.isNotBlank(query.getCity()) ? query.getCity() : STRING_DEFAULT_ALL)
                .isp(StringUtils.isNotBlank(query.getIsp()) ? query.getIsp() : STRING_DEFAULT_ALL)
                .sendCount(item.getSendCount())
                .billCount(item.getBillCount())
                .build();
    }
}
