package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

@Data
public class FailResendRuleDTO {

    /**
     * 补发场景(0:运营商,1:渠道,2:地区) 一期 默认：0：运营商
     */
    private String sceneType = "0";

    /**
     * 补发场景值(多个值用用英文逗号隔开)
     */
    private String sceneValue;

    /**
     * 规则类型（0：指定单模板发送 1：多模板分流发送）
     */
    private String ruleType;

    /**
     * 规则配置信息[{"tpl_code":"","sign_code":"","weight":""}]
     */
    private String ruleValue;
}
