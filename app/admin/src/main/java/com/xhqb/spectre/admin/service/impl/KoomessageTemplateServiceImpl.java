package com.xhqb.spectre.admin.service.impl;

import com.huaweicloud.sdk.koomessage.v1.KooMessageClient;
import com.huaweicloud.sdk.koomessage.v1.model.*;
import com.xhqb.spectre.admin.service.KoomessageTemplateService;
import com.xhqb.spectre.common.dal.entity.KoomessageTplReportDO;
import com.xhqb.spectre.common.dal.mapper.KoomessageTplReportMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * kooMessage
 * 查询模板报表
 * huawei cloud: <a href="https://support.huaweicloud.com/koomessage/index.html">doc</a>
 */
@Slf4j
@Service
public class KoomessageTemplateServiceImpl implements KoomessageTemplateService {

    @Resource
    private KooMessageClient kooMessageClient;

    @Resource
    private KoomessageTplReportMapper koomessageTplReportMapper;

    private static final int DEFAULT_LIMIT = 100;

    @Override
    public void loadReports(String exactDate, int recentDays) {
        List<AIMTemplateReport> reports = new ArrayList<>();
        List<AIMTemplate> all = getAll();
        List<String> allIds = all.stream()
                .filter(a -> a.getTplState() == 1)
                .map(AIMTemplate::getTplId)
                .collect(Collectors.toList());
        Map<String, AIMTemplate> aimTemplateMap = all.stream()
                .filter(a -> a.getTplState() == 1) //只取启用的模板
                .collect(Collectors.toMap(AIMTemplate::getTplId, aimTemplate -> aimTemplate));
        List<List<String>> slices = splitList(allIds, DEFAULT_LIMIT);

        List<String> dates = new ArrayList<>();
        if (!StringUtils.isEmpty(exactDate)) {
            dates.add(exactDate);
        } else {
            dates = getLastNDays(recentDays);
        }
        log.info("模板总数:{}, 获取天数:{}, 切片数:{}", allIds.size(), dates.size(), slices.size());
        for (String date : dates) {
            for (List<String> tplIds : slices) {
                ListAimTemplateReportsResponse response = getListAimTemplateReports(tplIds, date);
                reports.addAll(response.getTemplateReports());
            }
        }
        List<KoomessageTplReportDO> koomessageTplReportDOList = reports.stream().map(r -> {
            AIMTemplate aimTemplate = aimTemplateMap.get(r.getTplId());
            return buildDO(r, aimTemplate.getTplName());
        }).filter(Objects::nonNull).collect(Collectors.toList());
        updateReports(koomessageTplReportDOList);
    }

    public void updateReports(List<KoomessageTplReportDO> koomessageTplReportDOList) {
        for (KoomessageTplReportDO newRecord : koomessageTplReportDOList) {
            KoomessageTplReportDO oldRecord = koomessageTplReportMapper.selectByReportDate(newRecord.getTplId(),
                    newRecord.getReportDate());
            if (oldRecord != null) {
                if (updateRecord(oldRecord, newRecord)) {
                    koomessageTplReportMapper.updateByPrimaryKeySelective(oldRecord);
                }
            } else {
                koomessageTplReportMapper.insertSelective(newRecord);
            }
        }
    }

    private boolean updateRecord(KoomessageTplReportDO oldRecord, KoomessageTplReportDO newRecord) {
        boolean upd = false;
        if (!oldRecord.getResolvingTimes().equals(oldRecord.getResolvingTimes()) ||
                !oldRecord.getExposeUv().equals(oldRecord.getExposeUv()) ||
                !oldRecord.getExposePv().equals(oldRecord.getExposePv()) ||
                !oldRecord.getClickUv().equals(oldRecord.getClickUv()) ||
                !oldRecord.getClickPv().equals(oldRecord.getClickPv())
        ) {
            upd = true;
        }

        oldRecord.setResolvingTimes(oldRecord.getResolvingTimes());
        oldRecord.setExposeUv(newRecord.getExposeUv());
        oldRecord.setExposePv(newRecord.getExposePv());
        oldRecord.setClickUv(newRecord.getClickUv());
        oldRecord.setClickPv(newRecord.getClickPv());
        oldRecord.setUpdateTime(new Date());
        return upd;
    }

    public KoomessageTplReportDO buildDO(AIMTemplateReport report, String tplName) {

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        try {
            Date date = dateFormat.parse(report.getStartTime());
            return KoomessageTplReportDO.builder()
                    .tplId(report.getTplId())
                    .tplName(tplName)
                    .reportDate(date)
                    .resolvingTimes(report.getResolvingTimes())
                    .exposeUv(report.getExposeUv())
                    .exposePv(report.getExposePv())
                    .clickUv(report.getClickUv())
                    .clickPv(report.getClickPv())
                    .build();
        } catch (ParseException e) {
            log.error("日期解析错误, report={}", report);
            return null;
        }
    }

    private List<AIMTemplate> getAll() {
        List<AIMTemplate> templates = new ArrayList<>();
        int offset = 0;
        boolean r = true;
        while (r) {
            ListAimTemplatesResponse response = getAIMTemplates(offset, DEFAULT_LIMIT);
            templates.addAll(response.getTemplates());
            r = response.getPageInfo().getTotal() > DEFAULT_LIMIT + offset;
        }
        return templates;
    }

    private ListAimTemplatesResponse getAIMTemplates(int offset, int limit) {
        ListAimTemplatesRequest request = new ListAimTemplatesRequest();
        request.setOffset(offset);
        request.setLimit(limit);
        return kooMessageClient.listAimTemplates(request);
    }

    private ListAimTemplateReportsResponse getListAimTemplateReports(List<String> tplIds, String date) {
        ListAimTemplateReportsRequestBody body = new ListAimTemplateReportsRequestBody();
        body.setTplIds(tplIds);
        body.setBeginTime(date + "T00:00:00.001Z");
        body.setEndTime(date + "T23:59:59.999Z");
        body.setOffset(0);
        body.setLimit(DEFAULT_LIMIT);
        ListAimTemplateReportsRequest request = new ListAimTemplateReportsRequest();
        request.withBody(body);
        return kooMessageClient.listAimTemplateReports(request);
    }

    public static List<List<String>> splitList(List<String> originalList, int chunkSize) {
        List<List<String>> listOfLists = new ArrayList<>();

        for (int i = 0; i < originalList.size(); i += chunkSize) {
            int end = Math.min(originalList.size(), i + chunkSize);
            List<String> chunk = new ArrayList<>(originalList.subList(i, end));
            listOfLists.add(chunk);
        }

        return listOfLists;
    }

    public static List<String> getLastNDays(int n) {
        List<String> recentDays = new ArrayList<>();
        LocalDate currentDate = LocalDate.now();

        for (int i = 0; i < n; i++) {
            LocalDate previousDate = currentDate.minusDays(i);
            String formattedDate = previousDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            recentDays.add(formattedDate);
        }
        return recentDays;
    }

    public static String parseDate(String dateString) {
        try {
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
            LocalDateTime dateTime = LocalDateTime.parse(dateString, inputFormatter);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return dateTime.format(outputFormatter);
        } catch (DateTimeParseException e) {
            log.warn("日期解析失败");
            return null;
        }
    }
}
