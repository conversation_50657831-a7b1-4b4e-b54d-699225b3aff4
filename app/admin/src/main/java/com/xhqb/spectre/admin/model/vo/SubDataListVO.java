package com.xhqb.spectre.admin.model.vo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder(toBuilder = true)
public class SubDataListVO {
    private Integer totalCount;
    private Object dataList;

    /**
     * 总触达量
     */
    private Integer totalReachCount;

    /**
     * 总计费量
     */
    private Integer totalReachBillCount;
    /**
     * 平均触达率
     */
    private String avgReachRate;

    /**
     * 总签名数
     */
    private Integer signCount;

    /**
     * 总费用
     */
    private BigDecimal totalPriceCount;
}
