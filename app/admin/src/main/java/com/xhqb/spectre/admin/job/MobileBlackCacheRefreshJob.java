package com.xhqb.spectre.admin.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.MobileBlackDO;
import com.xhqb.spectre.common.dal.mapper.MobileBlackMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/26 14:12
 * @Description: 黑名单缓存刷新任务
 */
@Component
@Job("mobileBlackCacheRefreshJob")
@Slf4j
public class MobileBlackCacheRefreshJob implements SimpleJob {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private MobileBlackMapper mobileBlackMapper;

    private static final int PAGE_SIZE = 1000;

    /**
     * 黑名单缓存刷新任务
     *
     * @param shardingContext
     */
    @Override
    public void execute(ShardingContext shardingContext) {
        //刷新模板信息缓存
        log.info("手机号黑名单缓存刷新开始");
        refreshMobileBlackCache();
        log.info("手机号黑名单缓存刷新结束");
    }

    /**
     * 刷新黑名单缓存
     */
    private void refreshMobileBlackCache() {
        //分批获取黑名单
        int offset = 0;
        while (true) {
            List<MobileBlackDO> blackList = mobileBlackMapper.selectByPage(offset, PAGE_SIZE);
            if (blackList.size() > 0) {
                //写入redis
                redisTemplate.executePipelined((RedisCallback<?>) connection -> {
                    byte[] keyBytes = RedisKeys.MobileBlackKeys.MOBILE_BLACK_SET_KEY.getBytes();
                    blackList.forEach(item -> connection.setCommands().sAdd(keyBytes, buildCacheValue(item).getBytes()));
                    return null;
                });
            }
            if (blackList.size() < PAGE_SIZE) {
                break;
            }
            offset += PAGE_SIZE;
        }
    }

    private String buildCacheValue(MobileBlackDO item) {
        String value = item.getSmsTypeCode() + "_" + item.getMobile();
        String appCode = item.getAppCode();
        if (StringUtils.isBlank(appCode)) {
            return value;
        }
        return value + "_" + appCode;
    }
}
