package com.xhqb.spectre.admin.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.xhqb.kael.boot.autoconfigure.druid.DatasourceConfigUtils;
import com.xhqb.kael.boot.autoconfigure.druid.DefaultConnectionProperties;
import com.xhqb.kael.boot.autoconfigure.druid.DruidConnectionProperties;
import com.xhqb.spectre.admin.config.properties.CifDruidProperties;
import com.xhqb.spectre.admin.util.CommonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.List;

/**
 * cifdb 数据库配置
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Configuration
@MapperScan(basePackages = "com.xhqb.spectre.admin.cif.mapper", sqlSessionTemplateRef = "cifSqlSessionTemplate")
@EnableConfigurationProperties(CifDruidProperties.class)
public class CifDataSourceConfig {

    /**
     * cif数据源
     *
     * @param cifDruidProperties
     * @return
     */
    @Bean(name = "cifDataSource")
    public DataSource cifDataSource(CifDruidProperties cifDruidProperties) {
        DruidConnectionProperties defaultProperties = DruidConnectionProperties.withDefault(new DefaultConnectionProperties());
        BeanUtils.copyProperties(cifDruidProperties, defaultProperties, CommonUtil.getNullPropertyNames(cifDruidProperties));
        DruidDataSource dataSource = DatasourceConfigUtils.createDataSource(defaultProperties);
        dataSource.setName("cif");
        return dataSource;
    }

    /**
     * session factory
     *
     * @param dataSource
     * @param interceptorObjectProvider
     * @return
     * @throws Exception
     */
    @Bean(name = "cifSqlSessionFactory")
    public SqlSessionFactory cifSqlSessionFactory(@Qualifier("cifDataSource") DataSource dataSource, ObjectProvider<List<Interceptor>> interceptorObjectProvider) throws Exception {
        final SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        //设置加解密插件
        List<Interceptor> interceptorList = interceptorObjectProvider.getIfAvailable();
        if (CollectionUtils.isNotEmpty(interceptorList)) {
            bean.setPlugins(interceptorList.toArray(new Interceptor[0]));
        }
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/cif/*Mapper.xml"));
        return bean.getObject();
    }

    /**
     * transaction manager
     *
     * @param dataSource
     * @return
     */
    @Bean(name = "cifTransactionManager")
    public DataSourceTransactionManager cifTransactionManager(@Qualifier("cifDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * build sql session template
     *
     * @param sqlSessionFactory
     * @return
     */
    @Bean(name = "cifSqlSessionTemplate")
    public SqlSessionTemplate cifSqlSessionTemplate(@Qualifier("cifSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
