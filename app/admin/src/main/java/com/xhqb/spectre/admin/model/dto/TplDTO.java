package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 17:59
 * @Description:
 */
@Data
public class TplDTO implements Serializable {

    private static final long serialVersionUID = 4771706492089095122L;

    @NotBlank(message = "短信类型编码不能为空")
    private String smsTypeCode;

    @NotBlank(message = "模板名称不能为空")
    @Size(max = 64, message = "模板名称最大为{max}个字符")
    private String title;

    @NotNull(message = "签名ID不能为空")
    private Integer signId;

    @NotBlank(message = "模板内容不能为空")
    @Size(max = 1024, message = "模板内容最大为{max}个字符")
    private String content;

    @NotBlank(message = "授权应用不能为空")
    private String appCode;

    //    @NotBlank(message = "备注不能为空")
    @Size(max = 256, message = "备注最大为{max}个字符")
    private String remark;

    /**
     * 标签，1：营销类
     */
    private Integer tag;

    /**
     * 业务主线ID
     */
    private Integer businessLineId;
    /**
     * 营销场景ID
     */
    private Integer marketSceneId;
}
