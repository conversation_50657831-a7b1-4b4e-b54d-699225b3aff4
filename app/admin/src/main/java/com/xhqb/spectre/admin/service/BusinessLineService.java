package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.BusinessLineDTO;
import com.xhqb.spectre.admin.model.vo.BusinessLineVO;
import com.xhqb.spectre.common.dal.entity.BusinessLineDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.BusinessLineQuery;

import java.util.List;

/**
 * 业务线服务
 *
 * <AUTHOR>
 * @date 2022/9/23
 */
public interface BusinessLineService {

    /**
     * 分页查询业务线列表
     *
     * @param businessLineQuery
     * @return
     */
    CommonPager<BusinessLineVO> listByPage(BusinessLineQuery businessLineQuery);

    /**
     * 查询所有的业务信息信息
     *
     * @param status 状态，0：无效，1：有效 , 为空查所有
     * @return
     */
    List<BusinessLineVO> listAll(Integer status);

    /**
     * 查询业务线详情
     *
     * @param id
     * @return
     */
    BusinessLineVO getById(Integer id);

    /**
     * 新增业务线
     *
     * @param businessLineDTO
     * @return 返回新增业务线ID
     */
    Integer create(BusinessLineDTO businessLineDTO);

    /**
     * 更新业务线
     *
     * @param id
     * @param businessLineDTO
     * @return
     */
    Integer update(Integer id, BusinessLineDTO businessLineDTO);

    /**
     * 业务线状态调整
     *
     * @param id
     * @param status 状态，0：无效，1：有效
     * @return
     */
    Integer status(Integer id, int status);

    /**
     * 删除业务线
     *
     * @param id
     * @return
     */
    Integer deleteById(Integer id);

    /**
     * 新增业务线与模板的关联关系
     *
     * @param tplId
     * @param businessLineId
     */
    void addBusinessLineTpl(Integer tplId, Integer businessLineId);

    /**
     * 根据模板ID查询到业务线ID
     *
     * @param tplId
     * @return
     */
    Integer getBusinessLineIdByTpl(Integer tplId);

    int findOrCreateBusinessLine(String businessLineName);

    String getBusinessLineByTpl(Integer tplId);
}
