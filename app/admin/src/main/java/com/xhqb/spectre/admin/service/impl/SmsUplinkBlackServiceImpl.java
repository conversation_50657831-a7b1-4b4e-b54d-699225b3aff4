package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.service.SmsUplinkIndexService;
import com.xhqb.spectre.common.dal.mapper.SmsUplinkIndexMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/8/19 16:24
 **/
@Service
public class SmsUplinkBlackServiceImpl implements SmsUplinkIndexService {
    @Resource
    private SmsUplinkIndexMapper smsUplinkIndexMapper;

    /**
     * 查询定时任务上一次上行短信id
     *
     * @return id
     */
    @Override
    public Long selectLastSmsUplinkId() {
        return smsUplinkIndexMapper.selectSmsUplinkId();
    }

    /**
     * 将定时任务上行短信id改为最新值
     *
     * @param id
     */
    @Override
    public void updateUplinkLastId(Long id) {
       smsUplinkIndexMapper.updateUplinkLastId(id);
    }
}
