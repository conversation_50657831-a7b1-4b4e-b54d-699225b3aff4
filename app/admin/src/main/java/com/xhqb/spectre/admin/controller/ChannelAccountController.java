package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.job.AdminCacheRefreshJob;
import com.xhqb.spectre.admin.model.dto.ChannelAccountDTO;
import com.xhqb.spectre.admin.model.dto.CmppStatusDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ChannelAccountVO;
import com.xhqb.spectre.admin.service.ChannelAccountService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ChannelAccountQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 11:20
 * @Description:
 */
@RestController
@RequestMapping("/channelAccount")
@Slf4j
public class ChannelAccountController {

    @Autowired
    private ChannelAccountService channelAccountService;
    @Autowired
    private AdminCacheRefreshJob adminCacheRefreshJob;

    /**
     * 查询渠道账号枚举
     *
     * @param status
     * @return
     */
    @GetMapping("/enum")
    public AdminResult queryEnum(Integer status) {
        return AdminResult.success(channelAccountService.queryEnum(status));
    }

    /**
     * 渠道账号查询列表
     *
     * @param channelAccountQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(ChannelAccountQuery channelAccountQuery, Integer pageNum, Integer pageSize) {
        channelAccountQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<ChannelAccountVO> commonPager = channelAccountService.listByPage(channelAccountQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 渠道账号详情接口
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(channelAccountService.getById(id));
    }


    /**
     * 添加渠道账号信息
     *
     * @param channelAccountDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_CHANNEL_ACCOUNT)
    public AdminResult createInfo(@RequestBody ChannelAccountDTO channelAccountDTO) {
        log.info("create channelAccountDTO = {}", JSON.toJSONString(channelAccountDTO));
        channelAccountService.create(channelAccountDTO);
        return AdminResult.success();
    }

    /**
     * 更新渠道账号信息
     *
     * @param id
     * @param channelAccountDTO
     * @return
     */
    @PutMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_CHANNEL_ACCOUNT)
    public AdminResult updateInfo(@PathVariable("id") Integer id, @RequestBody ChannelAccountDTO channelAccountDTO) {
        log.info("update channelAccountDTO = {}, id = {}", JSON.toJSONString(channelAccountDTO), id);
        channelAccountService.update(id, channelAccountDTO);
        return AdminResult.success();
    }

    /**
     * 启用渠道账号信息
     *
     * @param id
     * @return
     */
    @PostMapping("/enable/{id}")
    @LogOpTime(OpLogConstant.MODULE_CHANNEL_ACCOUNT)
    public AdminResult enableInfo(@PathVariable("id") Integer id) {
        log.info("enable channelAccount, id = {}", id);
        channelAccountService.enable(id);
        return AdminResult.success();
    }

    /**
     * 停用渠道账号信息
     *
     * @param id
     * @return
     */
    @PostMapping("/disable/{id}")
    @LogOpTime(OpLogConstant.MODULE_CHANNEL_ACCOUNT)
    public AdminResult disableInfo(@PathVariable("id") Integer id) {
        log.info("disable channelAccount, id = {}", id);
        channelAccountService.disable(id);
        return AdminResult.success();
    }

    /**
     * 查询Cmpp账号的在线状态
     *
     * @param id
     * @return
     */
    @GetMapping("/queryCmppAliveInfo/{id}")
    public AdminResult queryCmppAliveInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(channelAccountService.queryCmppAliveInfo(id));
    }

    /**
     * 刷新缓存
     *
     * @return
     */
    @GetMapping("refreshAccountToCache")
    public AdminResult refreshAccountToCache() {
        adminCacheRefreshJob.refreshChannelAccountCache();
        return AdminResult.success();
    }

    /**
     * cmpp账号上线
     *
     * @return
     */
    @PostMapping("/cmpp/online")
    public AdminResult cmppOnline(@RequestBody CmppStatusDTO cmppStatusDTO) {
        return channelAccountService.cmppOnline(cmppStatusDTO);
    }

    /**
     * cmpp账号下线
     *
     * @param cmppStatusDTO
     * @return
     */
    @PostMapping("/cmpp/offline")
    public AdminResult cmppOffline(@RequestBody CmppStatusDTO cmppStatusDTO) {
        return channelAccountService.cmppOffline(cmppStatusDTO);
    }

    /**
     * 初始化流水表数据
     *
     * @return
     */
    @GetMapping("/copy2Log")
    public AdminResult copy2Log() {
        AdminResult adminResult = channelAccountService.copy2Log();
        log.info("初始化流水表响应 = {}, userName = {}",JSON.toJSONString(adminResult), SsoUserInfoUtil.getUserName());
        return adminResult;
    }

}
