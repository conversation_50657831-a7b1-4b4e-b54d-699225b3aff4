package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.MarketSceneDTO;
import com.xhqb.spectre.admin.model.vo.MarketSceneVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.MarketSceneQuery;

import java.util.List;

/**
 * 营销场景服务
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
public interface MarketSceneService {

    /**
     * 分页查询营销场景列表
     *
     * @param marketSceneQuery
     * @return
     */
    CommonPager<MarketSceneVO> listByPage(MarketSceneQuery marketSceneQuery);


    /**
     * 查询营销场景详情
     *
     * @param id
     * @return
     */
    MarketSceneVO getById(Integer id);

    /**
     * 新增营销场景
     *
     * @param marketSceneDTO
     * @return 返回营销场景ID
     */
    Integer create(MarketSceneDTO marketSceneDTO);

    /**
     * 更新营销场景
     *
     * @param id
     * @param marketSceneDTO
     * @return
     */
    Integer update(Integer id, MarketSceneDTO marketSceneDTO);

    /**
     * 营销场景状态调整
     *
     * @param id
     * @param status 状态，0：无效，1：有效
     * @return
     */
    Integer status(Integer id, int status);

    /**
     * 删除营销场景
     *
     * @param id
     * @return
     */
    Integer deleteById(Integer id);

    /**
     * 查询所有的营销场景信息
     *
     * @param status 状态，0：无效，1：有效 , 为空查所有
     * @return
     */
    List<MarketSceneVO> listAll(Integer status);


    /**
     * 新增营销场景与模板的关联关系
     *
     * @param tplId
     * @param marketSceneId
     */
    void addMarketSceneTpl(Integer tplId, Integer marketSceneId);

    /**
     * 根据模板ID查询营销场景ID
     *
     * @param tplId
     * @return
     */
    Integer getMarketSceneIdByTpl(Integer tplId);

    int findOrCreateMarketScene(String marketSceneName);

    String getMarketSceneByTpl(Integer tplId);
}
