package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.admin.constant.CommonConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/24 16:48
 * @Description:
 */
@Data
public class ChannelDTO implements Serializable {

    private static final long serialVersionUID = 7892315213380587575L;

    @NotBlank(message = "渠道英文名不能为空")
    @Size(max = 32, message = "渠道英文名最大为{max}个字符")
    @Pattern(regexp = CommonConstant.PATTERN_CODE, message = "渠道英文名格式有误")
    private String code;

    @NotBlank(message = "渠道中文名不能为空")
    @Size(max = 64, message = "渠道中文名最大为{max}个字符")
    private String name;
}
