package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.model.vo.AppDailyStatsChartVO;
import com.xhqb.spectre.admin.model.vo.AppDailyStatsVO;
import com.xhqb.spectre.admin.service.AppDailyStatsService;
import com.xhqb.spectre.admin.util.DailyStatsCacheUtil;
import com.xhqb.spectre.common.dal.dto.DailyStats;
import com.xhqb.spectre.common.dal.entity.AppDailyStatsDO;
import com.xhqb.spectre.common.dal.entity.GatewayUserDO;
import com.xhqb.spectre.common.dal.mapper.AppDailyStatsMapper;
import com.xhqb.spectre.common.dal.mapper.GatewayUserMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.AppDailyStatsQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AppDailyStatsServiceImpl implements AppDailyStatsService {

    @Autowired
    private AppDailyStatsMapper appDailyStatsMapper;

    @Autowired
    private GatewayUserMapper gatewayUserMapper;

    @Autowired
    private DailyStatsCacheUtil dailyStatsCacheUtil;

    private static final String SUCCESS_STACK = "成功量";

    private static final String FAILURE_STACK = "失败量";

    // 在类中添加 SimpleDateFormat 实例
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM-dd");



    private Date convertLocalDateToDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    @Override
    public void saveDailyStats() {
        LocalDate today = LocalDate.now();
        Date todayAsDate = convertLocalDateToDate(today);
        List<GatewayUserDO> gatewayUserList = gatewayUserMapper.selectAll();
        for (GatewayUserDO gatewayUser : gatewayUserList) {
            DailyStats dailyStats = dailyStatsCacheUtil.getDailyStats(DailyStatsCacheUtil.GW_KEY_PREFIX,
                    gatewayUser.getGroupName(),
                    String.valueOf(gatewayUser.getUserName()),
                    today);
            if (dailyStats != null) {
                for (DailyStats.TemplateStats templateStats : dailyStats.getTemplateStatsList()) {
                    AppDailyStatsDO appDailyStatsDO = AppDailyStatsDO.builder()
                            .accountId(gatewayUser.getId())
                            .accountCategory(1)
                            .statDate(todayAsDate)
                            .tplMappingId(0)
                            .tplCode(templateStats.getTplCode())
                            .errCode(templateStats.getErrCode())
                            .totalRequests(templateStats.getRequests())
                            .status(Objects.equals(templateStats.getErrCode(), "0") ? 1 : 0)
                            .build();
                    log.info("保存统计数据: {}", appDailyStatsDO);
                    appDailyStatsMapper.saveOrUpdate(appDailyStatsDO);
                }
            }
        }
    }

    @Override
    public AppDailyStatsChartVO overview(AppDailyStatsQuery appDailyStatsQuery) {
        List<AppDailyStatsDO> statsList = appDailyStatsMapper.selectByQuery(appDailyStatsQuery);
        List<String> xData = statsList.stream()
                .map(stat -> DATE_FORMAT.format(stat.getStatDate()))
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        // 构建 yData
        Map<String, AppDailyStatsChartVO.SeriesData> successSeriesMap = new HashMap<>(); // 成功量系列
        Map<String, AppDailyStatsChartVO.SeriesData> failureSeriesMap = new HashMap<>(); // 失败量系列

        for (AppDailyStatsDO stat : statsList) {
            String dateKey = DATE_FORMAT.format(stat.getStatDate());

            // 成功量处理
            if (stat.getStatus() == 1) {
                String tplCode = stat.getTplCode();
                successSeriesMap.putIfAbsent(tplCode, createSeriesData(tplCode, SUCCESS_STACK, xData));
                int index = xData.indexOf(dateKey);
                successSeriesMap.get(tplCode).getData().set(index, stat.getTotalRequests());
            } else {
                String errCode = stat.getErrCode();
                failureSeriesMap.putIfAbsent(errCode, createSeriesData(errCode, FAILURE_STACK, xData));
                int index = xData.indexOf(dateKey);
                failureSeriesMap.get(errCode).getData().set(index, stat.getTotalRequests());
            }

        }

        // 合并 yData
        List<AppDailyStatsChartVO.SeriesData> yData = new ArrayList<>();
        yData.addAll(successSeriesMap.values());
        yData.addAll(failureSeriesMap.values());

        // 构造 ChartData 对象
        return AppDailyStatsChartVO.builder()
                .xData(xData)
                .yData(yData)
                .build();
    }

    private AppDailyStatsChartVO.SeriesData createSeriesData(String name, String stack, List<String> xData) {
        return AppDailyStatsChartVO.SeriesData.builder()
                .name(name)
                .stack(stack)
                .data(new ArrayList<>(Collections.nCopies(xData.size(), "-")))
                .build();
    }

    @Override
    public CommonPager<AppDailyStatsVO> listByPage(AppDailyStatsQuery appDailyStatsQuery) {
        return PageResultUtils.result(
                () -> appDailyStatsMapper.countByQuery(appDailyStatsQuery),
                () -> appDailyStatsMapper.selectByQuery(appDailyStatsQuery)
                        .stream()
                        .map(AppDailyStatsVO::buildAppDailyStatsVO)
                        .collect(Collectors.toList())
        );
    }
}
