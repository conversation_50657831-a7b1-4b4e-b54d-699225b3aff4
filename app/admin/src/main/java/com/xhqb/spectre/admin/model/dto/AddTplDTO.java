package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.admin.constant.CommonConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 15:27
 * @Description:
 */
@Data
public class AddTplDTO extends TplDTO {

    private static final long serialVersionUID = 7694292749493617069L;

    @NotBlank(message = "模板编码不能为空")
    @Size(max = 128, message = "模板编码最大为{max}个字符")
    @Pattern(regexp = CommonConstant.TPL_PATTERN, message = "模板编码格式有误")
    private String code;
}
