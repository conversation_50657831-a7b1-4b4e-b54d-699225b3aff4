package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.batchtask.MessageSendFactory;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryContext;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryResult;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.service.FailResendExecutionService;
import com.xhqb.spectre.admin.service.FailResendStatusService;
import com.xhqb.spectre.common.dal.entity.FailResendRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 补发执行服务实现类
 */
@Service
@Slf4j
public class FailResendExecutionServiceImpl implements FailResendExecutionService {

    @Resource
    private FailResendStatusService failResendStatusService;

    @Resource
    private MessageSendFactory messageSendFactory;


    @Resource
    private VenusConfig venusConfig;


    @Override
    public boolean executeFailResend(FailResendRecordDO record) {
        if (record == null) {
            log.warn("补发记录为空");
            return false;
        }

        Long recordId = record.getId();

        try {
            log.debug("开始执行补发, recordId: {}, tplCode: {}, mobile: {}",
                    recordId, record.getTplCode(), record.getMobile());

            if (!failResendStatusService.startProcessing(recordId)) {
                log.warn("补发失败, 非待补发状态, recordId: {}", recordId);
                failResendStatusService.markFailed(recordId);
                return false;
            }

            SingleFactoryContext context = buildSingleFactoryContext(record);
            if (context == null) {
                log.warn("补发失败, 缺少必要字段, recordId: {}", recordId);
                failResendStatusService.markFailed(recordId);
                return false;
            }

            List<SingleFactoryResult> sendResults = messageSendFactory.singleSendMessage(context);

            return processSendResults(recordId, sendResults);

        } catch (Exception e) {
            failResendStatusService.markFailed(recordId);
            log.error("补发失败, 执行异常, recordId: {}", recordId, e);
            return false;
        }
    }


    /**
     * 构建单条短信发送上下文
     * 使用MessageSendFactory.singleSendMessage的方式
     *
     * @param record 补发记录
     * @return 发送上下文
     */
    private SingleFactoryContext buildSingleFactoryContext(FailResendRecordDO record) {
        try {
            if (StringUtils.isBlank(record.getTplCode()) || StringUtils.isBlank(record.getMobile())) {
                log.warn("补发失败, 缺少必要字段 recordId: {}, tplCode: {}, mobile: {}",
                        record.getId(), record.getTplCode(), record.getMobile());
                return null;
            }

            SingleFactoryContext.SingleFactoryContextBuilder builder = SingleFactoryContext.builder()
                    .appCode(venusConfig.getResendAppCode())
                    .tplCode(record.getTplCode())
                    .mobileList(Collections.singletonList(record.getMobile()))
                    .requestId("RS" + record.getOriginalOrderId());

            if (StringUtils.isNotBlank(record.getOriginalParameter())) {
                String[] params = record.getOriginalParameter().split(",");
                builder.paramMapList(Collections.singletonList(Arrays.asList(params)));
            }


            SingleFactoryContext context = builder.build();

            log.debug("构建补发单条短信上下文, recordId: {}, tplCode: {}, mobile: {}, parameter: {}",
                    record.getId(), context.getTplCode(), record.getMobile(),
                    record.getOriginalParameter());

            return context;

        } catch (Exception e) {
            log.error("构建单条短信上下文异常, recordId: {}", record.getId(), e);
            return null;
        }
    }

    /**
     * 处理单条发送结果
     *
     * @param recordId    记录ID
     * @param sendResults 发送结果列表
     * @return 是否成功
     */
    private boolean processSendResults(Long recordId, List<SingleFactoryResult> sendResults) {
        try {
            if (sendResults == null || sendResults.isEmpty()) {
                failResendStatusService.markFailed(recordId);
                log.warn("补发执行失败, recordId: {}, 发送结果为空", recordId);
                return false;
            }

            SingleFactoryResult firstResult = sendResults.get(0);
            if (Boolean.TRUE.equals(firstResult.getSuccess())) {
                failResendStatusService.markSuccess(recordId);
                log.debug("补发执行成功, recordId: {}, 接口结果成功: {}", recordId, firstResult.getMessage());
                return true;
            } else {
                String errorMsg = firstResult.getMessage() != null ? firstResult.getMessage() : "未知错误";
                failResendStatusService.markFailed(recordId);
                log.warn("补发执行失败, recordId: {}, 接口结果失败: {}", recordId, errorMsg);
                return false;
            }
            

        } catch (Exception e) {
            failResendStatusService.markFailed(recordId);
            log.warn("补发执行失败, recordId: {}, 处理发送结果异常", recordId, e);
            return false;
        }
    }

}
