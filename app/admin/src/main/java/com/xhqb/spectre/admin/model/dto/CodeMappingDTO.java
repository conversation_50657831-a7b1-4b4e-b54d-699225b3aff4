package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 映射码
 *
 * <AUTHOR>
 * @date 2021/10/20
 */
@Data
public class CodeMappingDTO implements Serializable {

    /**
     * 渠道code
     */
    @NotNull(message = "渠道code不能够为空")
    private String channelCode;
    /**
     * 错误码类型 submit->短信发送 deliver->短信回执
     */
    @NotNull
    @Pattern(regexp = "(submit)|(deliver)", message = "错误码类型只能为submit或者deliver")
    private String type;
    /**
     * 渠道错误码编码
     */
    @NotNull(message = "渠道错误码编码不能为空")
    private String channelErrCode;
    /**
     * xh错误码编码
     */
    @NotNull(message = "xh错误码编码不能够为空")
    private Integer xhErrCode;

}
