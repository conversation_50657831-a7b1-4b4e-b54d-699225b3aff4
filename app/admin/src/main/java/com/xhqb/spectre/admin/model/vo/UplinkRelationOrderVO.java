package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.enums.PhoneStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/6/9
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UplinkRelationOrderVO implements Serializable {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 渠道msgid
     */
    private String channelMsgId;

    /**
     * 应用ID
     */
    private String appCode;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道账号ID
     */
    private Integer channelAccountId;

    /**
     * 签名
     */
    private String signName;

    /**
     * 手机号（掩码返回）
     */
    private String mobile;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 处理分片ID
     */
    private Integer sliceId;

    /**
     * 省
     */
    private String provinceShortName;

    /**
     * 市
     */
    private String cityShortName;

    /**
     * 运营商
     */
    private String ispCode;

    /**
     * 群发批次ID
     */
    private Integer batchId;

    /**
     * 计费条数
     */
    private Integer billCount;

    /**
     * 发送类型 1-立即发送，2-定时
     */
    private Integer sendType;

    /**
     * 请求来源，1：http；2：cmpp
     */
    private Integer reqSrc;

    /**
     * 重发次数
     */
    private Integer resend;

    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 发送状态，0：成功，-1：新创建，1：失败
     */
    private Integer sendStatus;

    /**
     * 短信发送状态码
     */
    private String sendCode;

    /**
     * 短信发送状态描述
     */
    private String sendDesc;

    /**
     * 接收到的提交状态时间
     */
    private String recvSubmitTime;

    /**
     * 接收回执时间
     */
    private String reportTime;

    /**
     * 回执状态，0：成功，-1：未知，1：失败
     */
    private Integer reportStatus;

    /**
     * 短信回执状态码
     */
    private String reportCode;

    /**
     * 短信回执状态描述
     */
    private String reportDesc;

    /**
     * Submit提交时间
     */
    private String submitTime;

    /**
     * 发送完成时间
     */
    private String doneTime;

    /**
     * 接收到submit状态次数
     */
    private Integer submitResqCount;

    /**
     * 接收到devliver回执次数
     */
    private Integer reportCount;

    /**
     * 参数信息
     */
    private String parameter;

    /**
     * 手机号状态
     */
    private String phoneStatus;

    /**
     * 更新版本号
     */
    private Integer casVersion;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 群发创建者
     */
    private String batchCreator;

    public static UplinkRelationOrderVO buildSmsOrderVO(SmsOrderDO smsOrderDO) {
        return UplinkRelationOrderVO.builder()
                .orderId(String.valueOf(smsOrderDO.getOrderId()))
                .requestId(smsOrderDO.getRequestId())
                .channelMsgId(smsOrderDO.getChannelMsgId())
                .appCode(smsOrderDO.getAppCode())
                .tplCode(smsOrderDO.getTplCode())
                .smsTypeCode(smsOrderDO.getSmsTypeCode())
                .channelCode(smsOrderDO.getChannelCode())
                .channelAccountId(smsOrderDO.getChannelAccountId())
                .signName(smsOrderDO.getSignName())
                .mobile(CommonUtil.maskMobile(smsOrderDO.getMobile()))
                .content(smsOrderDO.getSignName() + smsOrderDO.getContent())
                .sliceId(smsOrderDO.getSliceId())
                .provinceShortName(smsOrderDO.getProvinceShortName())
                .cityShortName(smsOrderDO.getCityShortName())
                .ispCode(smsOrderDO.getIspCode())
                .batchId(smsOrderDO.getBatchId())
                .billCount(smsOrderDO.getBillCount())
                .sendType(smsOrderDO.getSendType())
                .reqSrc(smsOrderDO.getReqSrc())
                .resend(smsOrderDO.getResend())
                .sendTime(DateUtil.intToString(smsOrderDO.getSendTime()))
                .sendStatus(formatStatus(smsOrderDO.getSendStatus()))
                .sendCode(smsOrderDO.getSendCode())
                .sendDesc(smsOrderDO.getSendDesc())
                .recvSubmitTime(DateUtil.intToString(smsOrderDO.getRecvSubmitTime()))
                .reportTime(DateUtil.intToString(smsOrderDO.getReportTime()))
                .reportStatus(formatStatus(smsOrderDO.getReportStatus()))
                .reportCode(smsOrderDO.getReportCode())
                .reportDesc(smsOrderDO.getReportDesc())
                .submitTime(DateUtil.intToString(smsOrderDO.getSubmitTime()))
                .doneTime(DateUtil.intToString(smsOrderDO.getDoneTime()))
                .parameter(smsOrderDO.getParameter())
                .submitResqCount(smsOrderDO.getSubmitResqCount())
                .reportCount(smsOrderDO.getReportCount())
                .phoneStatus(PhoneStatusEnum.getDescription(smsOrderDO.getPhoneStatus()))
                .casVersion(smsOrderDO.getCasVersion())
                .createTime(DateUtil.dateToString(smsOrderDO.getCreateTime()))
                .updateTime(DateUtil.dateToString(smsOrderDO.getUpdateTime()))
                .build();
    }

    /**
     * 格式化状态值，将失败状态统一为1，传给前端页面
     *
     * @param status
     * @return
     */
    private static int formatStatus(Integer status) {
        return status > 0 ? 1 : status;
    }
}
