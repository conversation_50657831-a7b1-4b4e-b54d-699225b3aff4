package com.xhqb.spectre.admin.bidata.entity;

import lombok.Data;

import java.util.Date;

@Data
public class SendStatDO {

    /**
     * 主键 id
     */
    private Long id;

    /**
     * 统计时间
     */
    private String  statDate;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * isp
     */
    private String ispCode;

    /**
     * 短信类型
     */
    private String smsTypeCode;

    /**
     * 省
     */
    private String provinceShortName;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 发送量
     */
    private Long sendCount;

    /**
     * 触达量
     */
    private Long reachCount;

    /**
     * 首次触达量
     */
    private Long firstReachCount;

    /**
     * 是否删除 0未删除 1已删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
