package com.xhqb.spectre.admin.mq.message;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 群发任务提交消费
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskSubmitMessage implements Serializable {

    /**
     * 批次号
     */
    private Integer id;
    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 模板ID
     */
    private Integer tplId;
    /**
     * 签名ID
     */
    private Integer signId;
    /**
     * 短信类型编码
     */
    private String smsTypeCode;
    /**
     * 名单中的用户标识类型，1：cid；2：手机号
     */
    private Integer userIdType;
    /**
     * 发送类型，1：立即发送；2：定时发送
     */
    private Integer sendType;
    /**
     * 定时发送时间
     */
    private Date sendTime;
    /**
     * 备注，发送原因
     */
    private String remark;
    /**
     * 群发任务订单号
     */
    private String orderId;
    /**
     * 数据版本号
     */
    private Integer version;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateTime;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
