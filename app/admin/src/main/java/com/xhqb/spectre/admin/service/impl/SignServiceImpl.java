package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.AddSignDTO;
import com.xhqb.spectre.admin.model.dto.UpdateSignDTO;
import com.xhqb.spectre.admin.model.vo.SignEnumVO;
import com.xhqb.spectre.admin.model.vo.SignVO;
import com.xhqb.spectre.admin.service.SignService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.mapper.SignMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.SignQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 11:21
 * @Description:
 */
@Slf4j
@Service
public class SignServiceImpl implements SignService {

    @Autowired
    private SignMapper signMapper;

    @Autowired
    private TplMapper tplMapper;

    /**
     * 查询签名列表
     *
     * @param signQuery
     * @return
     */
    @Override
    public CommonPager<SignVO> listByPage(SignQuery signQuery) {
        return PageResultUtils.result(
                () -> signMapper.countByQuery(signQuery),
                () -> signMapper.selectByQuery(signQuery).stream().map(SignVO::buildSignVO).collect(Collectors.toList())
        );
    }

    /**
     * 查询签名详情
     *
     * @param id
     * @return
     */
    @Override
    public SignVO getById(Integer id) {
        SignDO signDO = validateAndSelectById(id);
        return SignVO.buildSignVO(signDO);
    }

    /**
     * 添加签名
     *
     * @param addSignDTO
     */
    @Override
    public void create(AddSignDTO addSignDTO) {
        //校验
        checkAddParam(addSignDTO);

        //写入签名信息
        SignDO signDO = buildSignDO(addSignDTO);
        signMapper.insertSelective(signDO);
    }

    /**
     * 编辑签名
     *
     * @param updateSignDTO
     */
    @Override
    public void update(UpdateSignDTO updateSignDTO) {
        //校验
        checkUpdateParam(updateSignDTO);

        //写入签名信息
        SignDO signDO = buildSignDO(updateSignDTO);
        signMapper.updateByPrimaryKeySelective(signDO);
    }

    /**
     * 启用签名
     *
     * @param id
     */
    @Override
    public void enable(Integer id) {
        SignDO signDO = validateAndSelectById(id);
        if (!isDisabled(signDO)) {
            throw new BizException("签名不处于停用状态，不能启用");
        }

        //修改状态为启用
        signMapper.enable(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 停用签名
     *
     * @param id
     */
    @Override
    public void disable(Integer id) {
        SignDO signDO = validateAndSelectById(id);
        if (!isEnabled(signDO)) {
            throw new BizException("签名不处于启用状态，不能停用");
        }
        //判断是否有模板引用该签名
        List<TplDO> tplDOList = tplMapper.selectEnum("", "", CommonConstant.STATUS_VALID, id);
        if (!CollectionUtils.isEmpty(tplDOList)) {
            throw new BizException("以下模板引用了该签名，请先停用模板。模板列表：" + tplDOList.stream().map(TplDO::getCode).collect(Collectors.joining("、")));
        }

        //修改状态为停用
        signMapper.disable(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 删除签名
     *
     * @param id
     */
    @Override
    public void delete(Integer id) {
        SignDO signDO = validateAndSelectById(id);
        if (!isDisabled(signDO)) {
            throw new BizException("签名不处于停用状态，不能删除");
        }
        //删除记录
        signMapper.delete(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 查询签名枚举
     *
     * @param status
     * @return
     */
    @Override
    public List<SignEnumVO> queryEnum(Integer status) {
        return signMapper.selectEnum(status).stream().map(SignEnumVO::buildSignEnumVO).collect(Collectors.toList());
    }

    @Override
    public int findOrCreateMarketScene(String signCode,String name) {
        SignDO signDO = signMapper.selectByCode(signCode);
        if(Objects.nonNull(signDO)){
            return signDO.getId();
        }
        String userName = SsoUserInfoUtil.getUserName();
        SignDO insertDO = new SignDO();
        insertDO.setCreator(userName);
        insertDO.setUpdater(userName);
        insertDO.setCode(signCode);
        insertDO.setName(name);

        signMapper.insertSelective(insertDO);
        return insertDO.getId();
    }

    private SignDO validateAndSelectById(Integer id) {
        SignDO signDO = signMapper.selectByPrimaryKey(id);
        if (Objects.isNull(signDO)) {
            throw new BizException("未找到该签名");
        }
        return signDO;
    }

    private void checkAddParam(AddSignDTO addSignDTO) {
        //参数格式校验
        ValidatorUtil.validate(addSignDTO);

        //名称校验
        SignDO exist = signMapper.selectByName(addSignDTO.getName());
        if (Objects.nonNull(exist)) {
            throw new BizException("签名名称已存在");
        }
        //编码校验
        exist = signMapper.selectByCode(addSignDTO.getCode());
        if(Objects.nonNull(exist)){
            throw new BizException("签名编码已存在");
        }
    }

    private void checkUpdateParam(UpdateSignDTO updateSignDTO) {
        //参数格式校验
        ValidatorUtil.validate(updateSignDTO);

        //校验签名是否存在
        validateAndSelectById(updateSignDTO.getId());
    }

    private SignDO buildSignDO(AddSignDTO addSignDTO) {
        String userName = SsoUserInfoUtil.getUserName();
        return SignDO.builder()
                .name(addSignDTO.getName())
                .description(addSignDTO.getDescription())
                .code(addSignDTO.getCode())
                .creator(userName)
                .updater(userName)
                .build();
    }

    private SignDO buildSignDO(UpdateSignDTO updateSignDTO) {
        return SignDO.builder()
                .id(updateSignDTO.getId())
                .description(updateSignDTO.getDescription())
                .updater(SsoUserInfoUtil.getUserName())
                .build();
    }

    private boolean isEnabled(SignDO signDO) {
        return signDO.getStatus().equals(CommonConstant.STATUS_VALID);
    }

    private boolean isDisabled(SignDO signDO) {
        return signDO.getStatus().equals(CommonConstant.STATUS_INVALID);
    }
}
