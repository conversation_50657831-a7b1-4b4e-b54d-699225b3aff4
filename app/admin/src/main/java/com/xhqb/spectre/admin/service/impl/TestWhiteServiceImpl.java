package com.xhqb.spectre.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.TestWhiteDTO;
import com.xhqb.spectre.admin.model.vo.TestWhiteVO;
import com.xhqb.spectre.admin.service.test.tool.TestWhiteService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.test.tool.TestWhiteDO;
import com.xhqb.spectre.common.dal.mapper.TestWhiteMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.TestWhiteQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TestWhiteServiceImpl implements TestWhiteService {

    @Resource
    private TestWhiteMapper testWhiteMapper;

    @Override
    public CommonPager<TestWhiteVO> listByPage(TestWhiteQuery testWhiteQuery) {
        return PageResultUtils.result(
                () -> testWhiteMapper.countByQuery(testWhiteQuery),
                () -> testWhiteMapper.selectByQuery(testWhiteQuery)
                        .stream().map(this::buildVO)
                        .collect(Collectors.toList())
        );
    }

    @Override
    public TestWhiteVO detail(Integer id) {
        TestWhiteDO testWhiteDO = testWhiteMapper.selectByPrimaryKey(id);
        if (testWhiteDO != null) {
            return buildVO(testWhiteDO);
        }
        return null;
    }

    @Override
    public Integer delete(Integer id) {
        TestWhiteDO testWhiteDO = testWhiteMapper.selectByPrimaryKey(id);
        if (testWhiteDO != null) {
            testWhiteDO.setIsDelete(1);
            testWhiteMapper.updateByPrimaryKeySelective(testWhiteDO);
        }
        return id;
    }

    @Override
    public Integer add(TestWhiteDTO testWhiteDTO) {
        TestWhiteDO modelDO = testWhiteMapper.selectByMobile(testWhiteDTO.getMobile());
        if (Objects.nonNull(modelDO)) {
            throw new BizException("手机号已存在");
        }
        TestWhiteDO addDO = buildDOByDTO(testWhiteDTO);
        addDO.setCreateTime(new Date());
        addDO.setCreator(SsoUserInfoUtil.getUserName());
        testWhiteMapper.insertBySelective(addDO);
        return 0;
    }

    @Override
    public Integer update(TestWhiteDTO testWhiteDTO) {
        TestWhiteDO modelDO = testWhiteMapper.selectByPrimaryKey(testWhiteDTO.getId());
        if (Objects.isNull(modelDO)) {
            throw new BizException("记录不存在");
        }
        testWhiteMapper.updateByPrimaryKeySelective(buildDOByDTO(testWhiteDTO));
        return testWhiteDTO.getId();
    }

    @Override
    public Map<String, Integer> countAllByBrand() {
        return testWhiteMapper.countAllByBrand();
    }

    private TestWhiteDO buildDOByDTO(TestWhiteDTO testWhiteDTO) {
        TestWhiteDO addDO = new TestWhiteDO();
        BeanUtil.copyProperties(testWhiteDTO, addDO);
        addDO.setIsDelete(0);
        addDO.setUpdater(SsoUserInfoUtil.getUserName());
        addDO.setUpdateTime(new Date());
        return addDO;
    }

    private TestWhiteVO buildVO(TestWhiteDO testWhiteDO) {
        TestWhiteVO testWhiteVO = new TestWhiteVO();
        BeanUtil.copyProperties(testWhiteDO, testWhiteVO);
        testWhiteVO.setMobile(CommonUtil.maskMobile(testWhiteDO.getMobile()));
        testWhiteVO.setEncryptMobile(CommonUtil.encryptMobile(testWhiteDO.getMobile()));
        return testWhiteVO;
    }
}
