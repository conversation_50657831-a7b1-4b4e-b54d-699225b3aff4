package com.xhqb.spectre.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhqb.spectre.common.dal.entity.AppDailyStatsDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppDailyStatsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 统计日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date statDate;

    /**
     * 账户ID
     */
    private Integer accountId;

    /**
     * 模版编码
     */
    private String tplCode;

    /**
     * 映射ID
     */
    private Integer tplMappingId;

    /**
     * 请求数
     */
    private Integer totalRequests;

    /**
     * 错误码
     */
    private String errCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 将AppDailyStatsDO对象转换为AppDailyStatsVO对象
     * @param appDailyStatsDO
     * @return
     */
    public static AppDailyStatsVO buildAppDailyStatsVO(AppDailyStatsDO appDailyStatsDO) {
        return AppDailyStatsVO.builder()
                .id(appDailyStatsDO.getId())
                .statDate(appDailyStatsDO.getStatDate())
                .accountId(appDailyStatsDO.getAccountId())
                .tplCode(appDailyStatsDO.getTplCode())
                .tplMappingId(appDailyStatsDO.getTplMappingId())
                .totalRequests(appDailyStatsDO.getTotalRequests())
                .errCode(appDailyStatsDO.getErrCode())
                .createTime(appDailyStatsDO.getCreateTime())
                .updateTime(appDailyStatsDO.getUpdateTime())
                .build();
    }
}
