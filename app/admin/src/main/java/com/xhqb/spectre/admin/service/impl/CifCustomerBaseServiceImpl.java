package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.batchtask.cid.CidStrategyFactory;
import com.xhqb.spectre.admin.cif.entity.CifCustomerBaseDO;
import com.xhqb.spectre.admin.cif.mapper.CifCustomerBaseMapper;
import com.xhqb.spectre.admin.model.vo.batchtask.CustomerVO;
import com.xhqb.spectre.admin.service.CifCustomerBaseService;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * cif 数据服务
 *
 * <AUTHOR>
 * @date 2021/9/26
 */
@Service
@Slf4j
public class CifCustomerBaseServiceImpl implements CifCustomerBaseService {

    /**
     * 查询状态列表
     */
    // public static final List<String> QUERY_STATUS_LIST = Lists.newArrayList("OPEN", "ACTIVE");

    @Resource
    private CifCustomerBaseMapper cifCustomerBaseMapper;
    @Resource
    private CidStrategyFactory cidStrategyFactory;

    /**
     * 根据cid获取用户手机号码
     *
     * @param cid
     * @return
     */
    @Override
    public CifCustomerBaseDO getByCid(String cid) {
        return cifCustomerBaseMapper.selectByPrimaryKey(cid);
    }

    /**
     * 根据cid列表查询用户信息
     *
     * @param cidList
     * @param smsTypeCode
     * @return
     */
    @Override
    public List<CifCustomerBaseDO> queryByCidList(List<String> cidList, String smsTypeCode) {
        List<String> userStatusStrategy = cidStrategyFactory.getUserStatusStrategy(smsTypeCode);
        return cifCustomerBaseMapper.selectByIdList(cidList, userStatusStrategy);
    }

    /**
     * 查询结果返回map
     *
     * @param cidList
     * @return
     */
    @Override
    public Map<String, CustomerVO> mapByCidList(List<String> cidList, String smsTypeCode) {
        List<String> userStatusStrategy = cidStrategyFactory.getUserStatusStrategy(smsTypeCode);
        // 根据cid查询用户数据信息
        List<CifCustomerBaseDO> cifCustomerBaseList = cifCustomerBaseMapper.selectByIdList(cidList, userStatusStrategy);
        if (CommonUtil.isEmpty(cifCustomerBaseList)) {
            log.warn("未查询到customerBase任何有效用户信息,userStatusStrategy = {},cidList = {}", userStatusStrategy, cidList);
            return null;
        }

        // key -> cid  value -> CustomerVO
        Map<String, CustomerVO> resultMap = new HashMap<>((int) (cifCustomerBaseList.size() / 0.75));
        String mobilePhone;
        CustomerVO customerVO;
        for (CifCustomerBaseDO item : cifCustomerBaseList) {
            mobilePhone = item.getMobilePhone();
            if (StringUtils.isBlank(mobilePhone)) {
                log.info("cid有效，但是手机号码为空,过滤掉该客户信息(t_customer_base) = {}", item);
                continue;
            }

            customerVO = CustomerVO.customerBase(item);
            resultMap.put(item.getId(), customerVO);
        }
        return resultMap;
    }

    /**
     * 根据手机号查询用户列表
     *
     * @param mobileList 手机号码列表
     * @return
     */
    @Override
    public List<CifCustomerBaseDO> selectByMobileList(Collection<String> mobileList) {
        return cifCustomerBaseMapper.selectByMobileList(mobileList);
    }
}
