package com.xhqb.spectre.admin.enums;

public enum OaStatusEnum {
    INIT(0, "初始化"),
    APPROVING(1, "审批中"),
    REJECTED(2, "拒绝"),
    APPROVED(3, "通过"),
    OTHER(-1, "其他");

    private final int code;
    private final String description;

    OaStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据 code 获取枚举对象
     */
    public static OaStatusEnum fromCode(int code) {
        for (OaStatusEnum status : OaStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return OTHER;
    }
}
