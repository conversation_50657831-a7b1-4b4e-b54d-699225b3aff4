package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.readonly.mapper.SmsOrderReadonlyMapper;
import com.xhqb.spectre.admin.service.FailResendRecordService;
import com.xhqb.spectre.admin.service.FailResendStrategyService;
import com.xhqb.spectre.common.dal.dto.mq.CmppDeliverResqDTO;
import com.xhqb.spectre.common.dal.entity.FailResendRecordDO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.mapper.FailResendRecordMapper;
import com.xhqb.spectre.common.dto.FailResendMatchResultDTO;
import com.xhqb.spectre.common.enums.FailResendStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;

/**
 * 补发记录
 */
@Service
@Slf4j
public class FailResendRecordServiceImpl implements FailResendRecordService {
    
    @Resource
    private FailResendStrategyService failResendStrategyService;
    
    @Resource
    private FailResendRecordMapper failResendRecordMapper;
    
    @Resource
    private SmsOrderReadonlyMapper smsOrderReadonlyMapper;

    /**
     * 回执成功状态码
     */
    private static final String SUCCESS_REPORT_STATUS = "DELIVRD";
    
    @Override
    public void processFailResend(CmppDeliverResqDTO cmppDeliverResqDTO) {
        try {
            log.debug("开始处理补发, orderId: {}, tplCode: {}, resend: {}, reportStatus: {}",
                    cmppDeliverResqDTO.getOrderId(), cmppDeliverResqDTO.getTplCode(), 
                    cmppDeliverResqDTO.getResend(), cmppDeliverResqDTO.getReportStatus());

            if (!needFailResend(cmppDeliverResqDTO)) {
                log.debug("无需补发, orderId: {}", cmppDeliverResqDTO.getOrderId());
                return;
            }


            SmsOrderDO smsOrder = getSmsOrderFromReadonly(cmppDeliverResqDTO);
            if (smsOrder == null) {
                log.warn("从readonly表未找到订单信息, orderId: {}, resend: {}", 
                        cmppDeliverResqDTO.getOrderId(), cmppDeliverResqDTO.getResend());
                return;
            }

            log.debug("从订单表获取信息: smsOrder={}", smsOrder);

            if (smsOrder.getReportStatus() == 0) {
                log.info("订单回执成功,无需发送, orderId: {}, resend: {}",
                        cmppDeliverResqDTO.getOrderId(), cmppDeliverResqDTO.getResend());
                return;
            }

            FailResendMatchResultDTO matchResult = failResendStrategyService.matchStrategy(smsOrder);
            
            if (!matchResult.isMatched()) {
                log.info("未匹配到补发策略, orderId:{}, resend:{}, tplCode:{}",
                        smsOrder.getOrderId(), smsOrder.getResend(), smsOrder.getTplCode());
                return;
            }
            
            log.info("匹配到补发策略, orderId:{}, strategyId:{}, ruleId:{}, tplCode:{}, resendTplCode: {}",
                    cmppDeliverResqDTO.getOrderId(),
                    matchResult.getStrategyId(),
                    matchResult.getRuleId(),
                    cmppDeliverResqDTO.getTplCode(),
                    matchResult.getTplCode());

            createFailResendRecord(cmppDeliverResqDTO, matchResult, smsOrder);
            
        } catch (Exception e) {
            log.error("处理补发逻辑异常, orderId: {}", cmppDeliverResqDTO.getOrderId(), e);
        }
    }
    
    /**
     * 创建补发记录
     */
    private void createFailResendRecord(CmppDeliverResqDTO cmppDeliverResqDTO, 
                                       FailResendMatchResultDTO matchResult, SmsOrderDO smsOrder) {
        try {
            
            FailResendRecordDO record = new FailResendRecordDO();
            record.setOriginalOrderId(String.valueOf(cmppDeliverResqDTO.getOrderId()));
            record.setOriginalTplCode(smsOrder.getTplCode());
            record.setOriginalParameter(smsOrder.getParameter());
            record.setOriginalReportCode(cmppDeliverResqDTO.getReportStatus());
            record.setOriginalSignName(smsOrder.getSignName());
            record.setIspCode(smsOrder.getIspCode());
            record.setStrategyId(matchResult.getStrategyId());
            record.setRuleId(matchResult.getRuleId());
            record.setTplCode(matchResult.getTplCode());
            record.setMobile(smsOrder.getMobile());
            record.setStartTime((int) (System.currentTimeMillis() / 1000));
            record.setRequestId("RS" + cmppDeliverResqDTO.getOrderId());
            record.setStatus(FailResendStatusEnum.PENDING.getCode());
            
            int result = failResendRecordMapper.insert(record);
            if (result > 0) {
                log.debug("补发记录创建成功, recordId: {}, orderId: {}, strategyId: {}, resendTplCode: {}",
                        record.getId(), cmppDeliverResqDTO.getOrderId(), 
                        matchResult.getStrategyId(), matchResult.getTplCode());
            } else {
                log.warn("补发记录创建失败, orderId: {}", cmppDeliverResqDTO.getOrderId());
            }
            
        } catch (Exception e) {
            log.error("创建补发记录异常, orderId: {}", cmppDeliverResqDTO.getOrderId(), e);
        }
    }

    private SmsOrderDO getSmsOrderFromReadonly(CmppDeliverResqDTO cmppDeliverResqDTO) {
        try {

            String tableNameSuffix = cmppDeliverResqDTO.getTableNameSuffix();
            if (tableNameSuffix == null || tableNameSuffix.length() != 6) {
                log.warn("tableNameSuffix 无效，无法解析时间范围: {}", tableNameSuffix);
                return null;
            }

            int year = Integer.parseInt(tableNameSuffix.substring(0, 4));
            int month = Integer.parseInt(tableNameSuffix.substring(4, 6));

            LocalDateTime startOfMonth = YearMonth.of(year, month).atDay(1).atStartOfDay();
            LocalDateTime endOfMonth = YearMonth.of(year, month).atEndOfMonth().atTime(23, 59, 59, 999_000_000);

            long startTimestamp = startOfMonth.atZone(ZoneId.of("Asia/Shanghai")).toEpochSecond();
            long endTimestamp = endOfMonth.atZone(ZoneId.of("Asia/Shanghai")).toEpochSecond();

            log.debug("解析 tableNameSuffix: {}, startTime: {}, endTime: {}", tableNameSuffix, startTimestamp, endTimestamp);
            SmsOrderDO smsOrder = smsOrderReadonlyMapper.selectByOrderIdResendAndSendTimeRange(
                    cmppDeliverResqDTO.getOrderId(), cmppDeliverResqDTO.getResend(), startTimestamp, endTimestamp);
            
            if (smsOrder == null) {
                log.debug("从readonly表未找到订单, orderId: {}, resend: {}, startTime: {}, endTime: {}",
                        cmppDeliverResqDTO.getOrderId(), cmppDeliverResqDTO.getResend(), startTimestamp, endTimestamp);
            }
            
            return smsOrder;
            
        } catch (Exception e) {
            log.error("从readonly表查询订单异常, orderId: {}, resend: {}", 
                    cmppDeliverResqDTO.getOrderId(), cmppDeliverResqDTO.getResend(), e);
            return null;
        }
    }

    /**
     * 判断是否需要补发
     */
    private boolean needFailResend(CmppDeliverResqDTO cmppDeliverResqDTO) {
        if (StringUtils.isBlank(cmppDeliverResqDTO.getTplCode())) {
            log.debug("模板编码为空, 不需要补发");
            return false;
        }

        //生产者过滤了
        String reportStatus = cmppDeliverResqDTO.getReportStatus();
        if (SUCCESS_REPORT_STATUS.equals(reportStatus)) {
            log.debug("回执状态为成功, 不需要补发, reportStatus: {}", reportStatus);
            return false;
        }

        //该模板没有配置策略， 不需要补发
         if (!failResendStrategyService.isTplInStrategy(cmppDeliverResqDTO.getTplCode())) {
             log.debug("模板编码不在策略中, 不需要补发, tplCode: {}", cmppDeliverResqDTO.getTplCode());
             return false;
         }

        return true;
    }
}
