package com.xhqb.spectre.admin.service.brand.impl;

import com.xhqb.spectre.admin.service.brand.BrandDetectionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * vivo品牌
 */
@Component
@Slf4j
public class VivoDetection implements BrandDetectionStrategy {
    @Override
    public boolean isMatch(String originBrand, String os) {
        return originBrand.toLowerCase().contains("vivo");
    }

    @Override
    public String getMappedBrand() {
        return "vivo";
    }
}
