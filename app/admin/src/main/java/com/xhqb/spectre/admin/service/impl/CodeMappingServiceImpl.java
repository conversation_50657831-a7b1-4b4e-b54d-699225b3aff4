package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.CodeMappingDTO;
import com.xhqb.spectre.admin.model.vo.CodeMappingVO;
import com.xhqb.spectre.admin.service.CodeMappingService;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.CodeMappingDO;
import com.xhqb.spectre.common.dal.mapper.CodeMappingMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.CodeMappingQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 映射码
 *
 * <AUTHOR>
 * @date 2021/10/20
 */
@Service
@Slf4j
public class CodeMappingServiceImpl implements CodeMappingService {

    @Resource
    private CodeMappingMapper codeMappingMapper;

    /**
     * 分页查询映射码列表
     *
     * @param codeMappingQuery
     * @return
     */
    @Override
    public CommonPager<CodeMappingVO> listByPage(CodeMappingQuery codeMappingQuery) {
        return PageResultUtils.result(
                () -> codeMappingMapper.countByQuery(codeMappingQuery),
                () -> codeMappingMapper.selectByQuery(codeMappingQuery).stream().map(CodeMappingVO::buildListQuery).collect(Collectors.toList())
        );
    }

    /**
     * 根据主键查询映射码信息
     *
     * @param channelCode
     * @param type
     * @param channelErrCode
     * @return
     */
    @Override
    public CodeMappingVO getByPrimaryKey(String channelCode, String type, String channelErrCode) {
        CodeMappingDO codeMappingDO = codeMappingMapper.selectByPrimaryKey(channelCode, type, channelErrCode);
        if (Objects.isNull(codeMappingDO)) {
            throw new BizException("未找到该映射码信息");
        }
        return CodeMappingVO.buildInfoQuery(codeMappingDO);
    }

    /**
     * 添加映射码信息
     *
     * @param codeMappingDTO
     */
    @Override
    public void create(CodeMappingDTO codeMappingDTO) {
        //参数格式校验
        ValidatorUtil.validate(codeMappingDTO);
        CodeMappingDO codeMappingDO = codeMappingMapper.selectByPrimaryKey(codeMappingDTO.getChannelCode(), codeMappingDTO.getType(), codeMappingDTO.getChannelErrCode());
        if (Objects.nonNull(codeMappingDO)) {
            log.warn("添加映射码失败,映射码已经存在 = {}", JSON.toJSONString(codeMappingDTO));
            throw new BizException("映射码已经存在");
        }
        codeMappingDO = buildCodeMappingDO(codeMappingDTO);
        codeMappingMapper.insertSelective(codeMappingDO);
    }

    /**
     * 更新映射码信息
     *
     * @param codeMappingDTO
     */
    @Override
    public void update(CodeMappingDTO codeMappingDTO) {
        //参数格式校验
        ValidatorUtil.validate(codeMappingDTO);
        CodeMappingDO codeMappingDO = codeMappingMapper.selectByPrimaryKey(codeMappingDTO.getChannelCode(), codeMappingDTO.getType(), codeMappingDTO.getChannelErrCode());
        if (Objects.isNull(codeMappingDO)) {
            log.warn("更新映射码失败,映射码不存在 = {}", JSON.toJSONString(codeMappingDTO));
            throw new BizException("映射码不存在");
        }
        codeMappingDO = buildCodeMappingDO(codeMappingDTO);
        codeMappingMapper.updateByPrimaryKeySelective(codeMappingDO);
    }

    /**
     * 构建映射码
     *
     * @param codeMappingDTO
     * @return
     */
    private CodeMappingDO buildCodeMappingDO(CodeMappingDTO codeMappingDTO) {
        CodeMappingDO codeMappingDO = new CodeMappingDO();
        codeMappingDO.setChannelCode(codeMappingDTO.getChannelCode());
        codeMappingDO.setType(codeMappingDTO.getType());
        codeMappingDO.setChannelErrCode(codeMappingDTO.getChannelErrCode());
        codeMappingDO.setXhErrCode(codeMappingDTO.getXhErrCode());
        return codeMappingDO;
    }
}
