package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.GatewayTplMappingDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/26 17:11
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GatewayTplMappingVO implements Serializable {

    private static final long serialVersionUID = -4964608310384773654L;

    private Integer id;

    private Integer tplId;

    private String tplCode;

    private String signName;

    private String tplContent;

    private String createTime;

    private String creator;

    private String updateTime;

    private String updater;

    public static GatewayTplMappingVO build(GatewayTplMappingDO item) {
        return GatewayTplMappingVO.builder()
                .id(item.getId())
                .tplId(item.getTplId())
                .tplCode(item.getTplCode())
                .signName(item.getSignName())
                .tplContent(item.getTplContent())
                .createTime(DateUtil.dateToString(item.getCreateTime()))
                .creator(item.getCreator())
                .updateTime(DateUtil.dateToString(item.getUpdateTime()))
                .updater(item.getUpdater())
                .build();
    }
}
