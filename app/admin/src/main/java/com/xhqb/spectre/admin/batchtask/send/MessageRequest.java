package com.xhqb.spectre.admin.batchtask.send;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;

/**
 * 消息发送请求
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
public class MessageRequest implements Serializable {

    /**
     * 业务应用ID
     */
    private String appCode;

    /**
     * 业用加签秘钥
     */
    private String appSecret;

    /**
     * tplCode 模版编号
     */
    private String tplCode;

    /**
     * SMS Sign 短信签名
     */
    private String signName;

    /**
     * 短信发送号码组，以逗号分开
     */
    private String phoneNumbers;

    /**
     * 渠道id，如果设置了该参数就不读取模版关联的渠道信息
     */
    private Integer channelAccountId;

    /**
     * 群发任务的ID
     */
    private Integer batchId;
    /**
     * 签名code，为空则使用小花钱包签名
     */
    private String signCode;

    /**
     * 请求ID (可以为空) 如果设置了 将会把该值设置到请求头中去
     */
    private String requestId;

    /**
     * 指定api
     */
    private String api;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
