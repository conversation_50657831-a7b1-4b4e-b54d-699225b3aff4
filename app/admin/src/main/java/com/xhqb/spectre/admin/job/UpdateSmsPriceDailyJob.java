package com.xhqb.spectre.admin.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.service.ChannelSmsTypePriceLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 每日更新短信统计价格任务
 */
@Component
@Job("updateSmsPriceDailyJob")
@Slf4j
public class UpdateSmsPriceDailyJob implements SimpleJob {


    @Autowired
    private ChannelSmsTypePriceLogService channelSmsTypePriceLogService;

    @Override
    public void execute(ShardingContext shardingContext) {
        LocalDate yesterday = LocalDate.now().minusDays(4);
        log.info("开始执行每日短信价格更新任务，更新日期：{}", yesterday);
        long start = System.currentTimeMillis();
        try {
            channelSmsTypePriceLogService.updateRecordsByDate(yesterday);
            log.info("完成短信价格更新任务，更新日期：{}，耗时 {} ms", yesterday, (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.error("短信价格更新任务执行失败，日期：{}", yesterday, e);
        }
    }
}