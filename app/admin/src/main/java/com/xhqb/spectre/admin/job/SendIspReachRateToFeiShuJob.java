package com.xhqb.spectre.admin.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.google.common.collect.Lists;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.bidata.mapper.BidataPlatformSendStatMapper;
import com.xhqb.spectre.admin.bidata.model.SendIspCodeDataDO;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.service.FeiShuAlert;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 运营商触达率飞书通知 （每日9.45 进行飞书通知）
 * 运营商 T-1发送量 T-2触达量 T-1触达率 T-2触达率 T-3触达率 T-4触达率
 */

@Component
@Job("sendIspReachRateToFeiShuJob")
@Slf4j
public class SendIspReachRateToFeiShuJob implements SimpleJob {

    @Resource
    private BidataPlatformSendStatMapper bidataPlatformSendStatMapper;

    @Resource
    private FeiShuAlert feiShuAlert;

    @Resource
    private VenusConfig venusConfig;

    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            doSendIspReachRateHandler();
        } catch (Exception e) {
            log.error("运营商触达率飞书通知异常", e);
        }
    }

    private void doSendIspReachRateHandler() {
        // 获取 T-1 到 T-4 的 运营商（电信、联通、移动、其他）数据 （发送量、触达量）并按照时间降序排序
        Date beginDate = DateUtil.beginOfDay(new Date());
        Date t1Date = DateUtil.offsetDay(beginDate, -1);
        Date t2Date = DateUtil.offsetDay(beginDate, -2);
        Date t3Date = DateUtil.offsetDay(beginDate, -3);
        Date t4Date = DateUtil.offsetDay(beginDate, -4);

        List<SendIspCodeDataDO> t1ToT4DataList = bidataPlatformSendStatMapper.selectDataByTimeAndIspCode(t4Date, t1Date);

        // 计算 触达率 并得到 T-1发送量 T-2触达量 T-1触达率 T-2触达率 T-3触达率 T-4触达率
        if (CollectionUtil.isEmpty(t1ToT4DataList)) {
            log.info("[运营商触达率飞书通知] 无数据，开始日期: {}, 结束日期: {}",
                    DateUtil.format(t4Date, "yyyy-MM-dd"), DateUtil.format(t1Date, "yyyy-MM-dd"));
            return;
        }

        List<String> ispCodeList = Lists.newArrayList("电信", "联通", "移动", "其他");
        Map<String, List<SendIspCodeDataDO>> ispStatDateMap = t1ToT4DataList.stream()
                .collect(Collectors.groupingBy(SendIspCodeDataDO::getIspCode));

        List<JSONObject> messageList = new ArrayList<>();

        for (String ispCode : ispCodeList) {
            List<SendIspCodeDataDO> ispDataList = ispStatDateMap.getOrDefault(ispCode, Collections.emptyList());

            Map<String, List<SendIspCodeDataDO>> statDateMap = ispDataList.stream()
                    .collect(Collectors.groupingBy(data -> {
                        try {
                            return DateUtil.format(DateUtil.parseDate(data.getStatDate()), "yyyy-MM-dd");
                        } catch (Exception e) {
                            // 添加异常处理，防止因个别数据格式错误导致整个任务失败
                            log.warn("解析统计日期失败，原始值: {}", data.getStatDate(), e);
                            return "invalid_date";
                        }
                    }));

            JSONObject message = new JSONObject();
            message.put("ispCode", ispCode);

            putReachRateInfo(statDateMap, message, "t1", t1Date);
            putReachRateInfo(statDateMap, message, "t2", t2Date);
            putReachRateInfo(statDateMap, message, "t3", t3Date);
            putReachRateInfo(statDateMap, message, "t4", t4Date);

            messageList.add(message);
        }

        log.info("发送飞书通知 内容size[{}]", messageList.size());
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("reports", messageList);
        // 发送飞书通知
        feiShuAlert.sendFeiShuAlert(dataMap, venusConfig.getSendIspReachRateToFeiShuJob());
    }

    /**
     * 提取公共逻辑，设置触达率信息
     */
    private void putReachRateInfo(Map<String, List<SendIspCodeDataDO>> statDateMap, JSONObject message,
                                  String prefix, Date date) {
        String dateStr = DateUtil.format(date, "yyyy-MM-dd");

        // 获取对应日期的数据
        List<SendIspCodeDataDO> dataList = statDateMap.getOrDefault(dateStr, Collections.emptyList());

        // 计算发送量和触达量
        long sendCount = dataList.stream().mapToLong(SendIspCodeDataDO::getSendCount).sum();
        long reachCount = dataList.stream().mapToLong(SendIspCodeDataDO::getReachCount).sum();

        // 计算触达率，处理除数为0的情况
        double reachRate = 0.0;
        if (sendCount > 0) {
            reachRate = CommonUtil.realDivision(reachCount, sendCount) * 100;
            // 限制小数位数，提高可读性
            reachRate = Math.round(reachRate * 100.0) / 100.0;
        }

        // 设置值到message中
        message.put(prefix + "SendCount", sendCount);
        if ("t1".equals(prefix)) {
            message.put("t1ReachRate", reachRate);
        } else {
            message.put(prefix + "ReachRate", reachRate);
            if ("t2".equals(prefix)) {
                message.put("t2ReachCount", sendCount);
            }
        }
    }


}

