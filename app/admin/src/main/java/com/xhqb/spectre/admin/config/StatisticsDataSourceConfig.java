package com.xhqb.spectre.admin.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.xhqb.kael.boot.autoconfigure.druid.DatasourceConfigUtils;
import com.xhqb.kael.boot.autoconfigure.druid.DefaultConnectionProperties;
import com.xhqb.kael.boot.autoconfigure.druid.DruidConnectionProperties;
import com.xhqb.spectre.admin.config.properties.StatisticsDruidProperties;
import com.xhqb.spectre.admin.util.CommonUtil;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/28 11:31
 * @Description:
 */
@Configuration
@MapperScan(basePackages = "com.xhqb.spectre.admin.statistics.mapper", sqlSessionTemplateRef = "statisticsSqlSessionTemplate")
@EnableConfigurationProperties(StatisticsDruidProperties.class)
public class StatisticsDataSourceConfig {

    /**
     * statistics数据源
     *
     * @param statisticsDruidProperties
     * @return
     */
    @Bean(name = "statisticsDataSource")
    public DataSource statisticsDataSource(StatisticsDruidProperties statisticsDruidProperties) {
        DruidConnectionProperties defaultProperties = DruidConnectionProperties.withDefault(new DefaultConnectionProperties());
        BeanUtils.copyProperties(statisticsDruidProperties, defaultProperties, CommonUtil.getNullPropertyNames(statisticsDruidProperties));
        DruidDataSource dataSource = DatasourceConfigUtils.createDataSource(defaultProperties);
        dataSource.setName("statistics");
        return dataSource;
    }

    /**
     * session factory
     *
     * @param dataSource
     * @return
     * @throws Exception
     */
    @Bean(name = "statisticsSqlSessionFactory")
    public SqlSessionFactory statisticsSqlSessionFactory(@Qualifier("statisticsDataSource") DataSource dataSource) throws Exception {
        final SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/statistics/*Mapper.xml"));
        return bean.getObject();
    }

    /**
     * transaction manager
     *
     * @param dataSource
     * @return
     */
    @Bean(name = "statisticsTransactionManager")
    public DataSourceTransactionManager statisticsTransactionManager(@Qualifier("statisticsDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * build sql session template
     *
     * @param sqlSessionFactory
     * @return
     */
    @Bean(name = "statisticsSqlSessionTemplate")
    public SqlSessionTemplate statisticsSqlSessionTemplate(@Qualifier("statisticsSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
