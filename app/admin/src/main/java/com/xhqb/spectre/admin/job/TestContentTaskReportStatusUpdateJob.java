package com.xhqb.spectre.admin.job;

import cn.hutool.core.date.DateUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.enums.SmsContentTestTaskStatuseEnum;
import com.xhqb.spectre.admin.service.test.tool.TestContentTaskService;
import com.xhqb.spectre.common.dal.entity.test.tool.TestContentTaskDO;
import com.xhqb.spectre.common.dal.mapper.TestContentTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
@Job("testContentTaskReportStatusUpdateJob")
@Slf4j
public class TestContentTaskReportStatusUpdateJob implements SimpleJob {

    @Resource
    private TestContentTaskMapper testContentTaskMapper;

    @Resource
    private TestContentTaskService testContentTaskService;


    @Override
    public void execute(ShardingContext shardingContext) {
        // 当天时间
        Date endTime = DateUtil.date();
        Date startTime = DateUtil.beginOfDay(endTime);
        List<TestContentTaskDO> taskList = testContentTaskMapper.selectByTaskStatus(SmsContentTestTaskStatuseEnum.STATUS_SUCCESS.getCode(), startTime, endTime);
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }

        for (TestContentTaskDO task : taskList) {
            log.info("开始更新状态|taskId:{}", task.getTaskId());
            testContentTaskService.updateTaskRecordReportStatus(task.getTaskId());
        }

    }
}
