package com.xhqb.spectre.admin.model.dto;

import lombok.Data;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class UserShortUrlDTO {

    /**
     * 长链URL
     */
    @NotBlank(message = "长链URL不能为空")
    @Size(max = 512, message = "长链URL最大为{max}个字符")
    @URL(message = "长链URL格式有误")
    private String longUrl;

    /**
     * 短链模版编码
     */
    @NotBlank(message = "短链模版编码不能为空")
    private String tplCode;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * cid
     */
    private String cid;

    /**
     * 主键id
     */
    private Integer id;
}
