package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.ParamsDTO;
import com.xhqb.spectre.admin.model.dto.ParamsDeleteDTO;
import com.xhqb.spectre.admin.model.dto.ParamsValueDTO;
import com.xhqb.spectre.admin.model.vo.ParamsInfoVO;
import com.xhqb.spectre.admin.model.vo.ParamsPageVO;
import com.xhqb.spectre.admin.model.vo.ParamsValuePageVO;
import com.xhqb.spectre.admin.service.ParamsService;
import com.xhqb.spectre.admin.util.RandomValueGeneratorUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.ParamsCodeDO;
import com.xhqb.spectre.common.dal.entity.ParamsDO;
import com.xhqb.spectre.common.dal.entity.ParamsValueDO;
import com.xhqb.spectre.common.dal.entity.test.TestMobileDO;
import com.xhqb.spectre.common.dal.mapper.ParamsCodeMapper;
import com.xhqb.spectre.common.dal.mapper.ParamsValueMapper;
import com.xhqb.spectre.common.dal.mapper.TestMobileMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ParamsQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ParamsServiceImpl implements ParamsService {


    @Resource
    private ParamsCodeMapper paramsCodeMapper;

    @Resource
    private ParamsValueMapper paramsValueMapper;

    @Resource
    private TestMobileMapper testMobileMapper;

    @Resource
    private VenusConfig venusConfig;

    @Override
    public CommonPager<ParamsPageVO> codePage(ParamsQuery paramsQuery) {
        return PageResultUtils.result(
                () -> paramsCodeMapper.countByQuery(paramsQuery),
                () -> paramsCodeMapper.selectByQuery(paramsQuery).stream().map(this::buildVO).collect(Collectors.toList())
        );
    }

    @Override
    public String codeAdd(ParamsDTO paramsDTO) {
        ParamsCodeDO modelParams = paramsCodeMapper.selectByCode(paramsDTO.getCode());
        if (Objects.nonNull(modelParams)) {
            throw new BizException("参数编码已存在");
        }
        ParamsCodeDO paramsCodeDO = buildDO(paramsDTO);
        paramsCodeMapper.insertSelective(paramsCodeDO);
        return "success";
    }


    @Override
    public String codeDelete(ParamsDeleteDTO paramsDTO) {
        List<String> codeList = paramsDTO.getCodeList();
        if (CollectionUtils.isEmpty(codeList)) {
            throw new BizException("请选择删除项");
        }
        // 删除 code
        paramsCodeMapper.updateDeleteTagByCodeList(codeList);
        // 删除 value
        paramsValueMapper.updateDeleteTagByCodeList(codeList);
        return "success";
    }


    @Override
    public CommonPager<ParamsValuePageVO> valuePage(ParamsQuery paramsQuery) {
        return PageResultUtils.result(
                () -> paramsValueMapper.countByQuery(paramsQuery),
                () -> paramsValueMapper.selectByQuery(paramsQuery).stream().map(this::buildVO).collect(Collectors.toList())
        );
    }

    @Override
    public String valueAdd(ParamsValueDTO paramsValueDTO) {
        ParamsCodeDO modelParams = paramsCodeMapper.selectByCode(paramsValueDTO.getCode());
        if (Objects.isNull(modelParams)) {
            throw new BizException("参数编码不存在");
        }
        List<String> valueList = paramsValueDTO.getValueList();
        opBasicParams(paramsValueDTO, modelParams);

        List<ParamsValueDO> paramsValueDOList = new ArrayList<>(valueList.size());
        Date date = new Date();
        String userName = SsoUserInfoUtil.getUserName();
        for (String value : valueList) {
            ParamsValueDO paramsValueDO = new ParamsValueDO();
            paramsValueDO.setCode(paramsValueDTO.getCode());
            paramsValueDO.setValue(value);
            paramsValueDO.setCreator(userName);
            paramsValueDO.setUpdater(userName);
            paramsValueDO.setUpdateTime(date);
            paramsValueDOList.add(paramsValueDO);
        }
        paramsValueDOList.forEach(paramsValueDO -> paramsValueMapper.insertSelective(paramsValueDO));
        return "success";
    }


    @Override
    public String valueDelete(ParamsDeleteDTO paramsDTO) {
        List<Integer> idList = paramsDTO.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            throw new BizException("请选择删除项");
        }
        paramsValueMapper.updateDeleteTagByIdList(idList);
        return "success";
    }


    private ParamsValuePageVO buildVO(ParamsValueDO paramsValueDO) {
        ParamsValuePageVO paramsValuePageVO = new ParamsValuePageVO();
        BeanUtils.copyProperties(paramsValueDO, paramsValuePageVO);
        return paramsValuePageVO;
    }

    private ParamsPageVO buildVO(ParamsCodeDO paramsCodeDO) {
        ParamsPageVO paramsPageVO = new ParamsPageVO();
        BeanUtils.copyProperties(paramsCodeDO, paramsPageVO);
        return paramsPageVO;
    }

    private ParamsCodeDO buildDO(ParamsDTO paramsDTO) {
        ParamsCodeDO paramsDO = new ParamsCodeDO();
        BeanUtils.copyProperties(paramsDTO, paramsDO);
        paramsDO.setCreator(SsoUserInfoUtil.getUserName());
        paramsDO.setUpdateTime(new Date());
        return paramsDO;
    }

    /**
     * 校验参数值数量 (数据库 加 请求参数)
     *
     * @param paramsValueDTO 请求参数
     */
    private void checkParamsValueSize(ParamsValueDTO paramsValueDTO) {
        List<String> valueList = paramsValueDTO.getValueList();
        List<ParamsValueDO> modelList = paramsValueMapper.selectByCode(paramsValueDTO.getCode());
        int modelListSize = modelList.size();
        int sum = modelListSize + valueList.size();
        int paramsInitValueSize = venusConfig.getParamsInitValueSize();
        log.info(String.format("modelListSize: %s, valueListSize: %s, paramsInitValueSize: %s", modelListSize, valueList.size(), paramsInitValueSize));
        if (sum < paramsInitValueSize) {
            throw new BizException(String.format("当前参数值数量太少,最少还需要添加 %s", paramsInitValueSize - sum));
        }
    }

    /**
     * 操作基本参数
     *
     * @param paramsValueDTO 参数值DTO
     * @param modelParams    模型参数DO
     */
    private void opBasicParams(ParamsValueDTO paramsValueDTO, ParamsCodeDO modelParams) {
        if (!isBasicType(modelParams)) {
            return;
        }

        // 校验参数值数量
        checkParamsValueSize(paramsValueDTO);
        // 校验参数和手机号变量值 处理基础变量
        handleMobileParams(paramsValueDTO);
    }


    /**
     * 校验参数和手机号变量值
     *
     * @param paramsValueDTO 参数值DTO
     */
    private void handleMobileParams(ParamsValueDTO paramsValueDTO) {
        TestMobileDO testMobileDO = testMobileMapper.selectByUpdateTimeLimit();
        if (Objects.isNull(testMobileDO)) {
            return;
        }
        String param = testMobileDO.getParam();
        JSONObject jsonObject = new JSONObject();
        if (StringUtils.isNotEmpty(param)) {
            jsonObject = JSON.parseObject(param);
        }
        if (jsonObject.containsKey(paramsValueDTO.getCode())) {
            return;
        }
        List<TestMobileDO> mobileDOList = testMobileMapper.selectAll();
        String newParamValue = null;
        for (TestMobileDO mobileDO : mobileDOList) {
            JSONObject tempJSONObject = new JSONObject();
            List<String> randomValues = RandomValueGeneratorUtil.randomValues(paramsValueDTO.getValueList(), 1);
            if (!randomValues.isEmpty()) {
                String randomValue = randomValues.get(0);
                tempJSONObject.put(paramsValueDTO.getCode(), randomValue);
                tempJSONObject.putAll(jsonObject);
                newParamValue = tempJSONObject.toJSONString();
                mobileDO.setParam(newParamValue);
                mobileDO.setUpdateTime(new Date());
                testMobileMapper.updateByPrimaryKeySelective(mobileDO);
            }
        }
    }


    /**
     * 判断是否为基本类型
     *
     * @param modelParams 模型参数DO
     * @return 是否为基本类型
     */
    private boolean isBasicType(ParamsCodeDO modelParams) {
        return Objects.equals(modelParams.getType(), "basic");
    }
}
