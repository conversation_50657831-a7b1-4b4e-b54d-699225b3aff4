package com.xhqb.spectre.admin.statistics.vo;

import com.xhqb.spectre.admin.statistics.entity.SmsStatisSumDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/2 14:19
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsStatisClassSumListVO implements Serializable {

    private static final long serialVersionUID = -5553061161558103627L;

    /**
     * 统计时间（3种时间维度：日期、时间、月份）
     */
    private String date;

    /**
     * 指标值
     */
    private String classValue;

    /**
     * 发送量
     */
    private Integer sendCount;

    /**
     * 计费量
     */
    private Integer billCount;

    public static SmsStatisClassSumListVO build(SmsStatisSumDO item) {
        return SmsStatisClassSumListVO.builder()
                .date(item.getDate())
                .classValue(item.getClassValue())
                .sendCount(item.getSendCount())
                .billCount(item.getBillCount())
                .build();
    }
}
