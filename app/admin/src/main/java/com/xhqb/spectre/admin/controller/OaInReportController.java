package com.xhqb.spectre.admin.controller;

import com.alibaba.excel.EasyExcel;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.OaReportDto;
import com.xhqb.spectre.admin.model.dto.OaUpdateReportDto;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.InExportVO;
import com.xhqb.spectre.admin.model.vo.ReportVO;
import com.xhqb.spectre.admin.service.oa.OaReportService;
import com.xhqb.spectre.admin.service.oa.vo.FlowDataAllInfo;
import com.xhqb.spectre.admin.util.DailyCounterUtil;
import com.xhqb.spectre.admin.util.FileResponseUtil;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.OaReportQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * OA 内部报备
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/oaInReport")
@Slf4j
public class OaInReportController {

    @Resource
    private OaReportService oaReportService;

    @Resource
    private DailyCounterUtil dailyCounterUtil;

    /**
     * 获取内部报备的分页信息
     *
     * @param oaReportQuery 内部报备查询条件
     * @param pageNum       页码
     * @param pageSize      每页显示条数
     * @return 分页结果，包含分页信息和内部报备列表
     */
    @GetMapping("")
    public CommonResult<CommonPager<ReportVO>> page(@ModelAttribute OaReportQuery oaReportQuery, Integer pageNum, Integer pageSize) {
        oaReportQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        oaReportQuery.setContentType("IN");
        log.info("查询内部报备请求信息 oaReportQuery:{}", JsonLogUtil.toJSONString(oaReportQuery));
        return CommonResult.success(oaReportService.page(oaReportQuery));
    }


    /**
     * 保存内部报备请求信息
     *
     * @param oaReportDto 内部报备请求的数据传输对象，包含需要保存的信息
     * @return 保存操作的结果，包含操作状态及可能返回的消息
     */
    @PostMapping("/save")
    public CommonResult<String> save(@RequestBody OaReportDto oaReportDto) {
        log.info("保存内部报备请求信息 oaReportDto:{}", JsonLogUtil.toJSONString(oaReportDto));
        return CommonResult.success(oaReportService.saveOaInReport(oaReportDto));
    }

    /**
     * 提交内部报备请求
     *
     * @param oaReportDto 内部报备请求的数据传输对象，包含请求的相关信息
     * @return 提交结果，封装在CommonResult<String>对象中
     */
    @PostMapping("submit")
    public CommonResult<String> submit(@RequestBody OaReportDto oaReportDto) {
        log.info("提交内部报备请求信息 oaReportDto:{}", JsonLogUtil.toJSONString(oaReportDto));
        return CommonResult.success(oaReportService.submitOaInReport(oaReportDto));
    }

    /**
     * 批量提交内部报备请求
     *
     * @param contentList 包含多个contentId的列表 contentId
     * @return 提交结果，封装在CommonResult<String>对象中
     */
    @PostMapping("batchReport")
    public CommonResult<String> batchReport(@RequestBody List<String> contentList) {
        log.info("批量提交内部报备请求信息 contentList:{}", JsonLogUtil.toJSONString(contentList));
        return CommonResult.success(oaReportService.batchReportOaInReport(contentList));
    }

    /**
     * 更新内部报备请求信息
     * <p>
     * 此方法用于更新内部报备请求的信息。
     *
     * @param oaUpdateReportDto 包含更新信息的内部报备请求数据传输对象
     * @return 更新操作的结果，封装在CommonResult<String>对象中
     */
    @PostMapping("update")
    public CommonResult<String> update(@RequestBody OaUpdateReportDto oaUpdateReportDto) {
        log.info("更新内部报备请求信息 oaReportDto:{}", JsonLogUtil.toJSONString(oaUpdateReportDto));
        return CommonResult.success(oaReportService.update(oaUpdateReportDto));
    }

    /**
     * 获取内部报备详情信息
     *
     * @param contentId 报备内容的ID
     * @return 包含报备详情信息的CommonResult对象
     */
    @GetMapping("detail")
    public CommonResult<ReportVO> detail(String contentId) {
        log.info("获取内部报备详情信息 contentId:{}", JsonLogUtil.toJSONString(
                contentId));
        return CommonResult.success(oaReportService.detail(contentId));
    }


    /**
     * 审批记录获取
     * <p>
     * 通过给定的contentId来审批对应的内部或外部报备请求。
     *
     * @param contentId 报备请求的唯一标识
     * @return 审批操作的结果，封装在CommonResult<List<FlowDataAllInfo>>对象中，其中包含了审批后的全部流程信息
     */
    @GetMapping("/approve")
    public CommonResult<List<FlowDataAllInfo>> approve(String contentId) {
        log.info("审批内部报备请求信息 contentId:{}", JsonLogUtil.toJSONString(contentId));
        return CommonResult.success(oaReportService.approve(contentId));
    }

    /**
     * 同步模板信息
     * <p>
     * 根据提供的contentId同步对应的模板信息。
     *
     * @param contentId 需要同步模板信息的唯一标识
     * @return 同步操作的结果，封装在CommonResult<String>对象中
     */
    @PostMapping("/syncTpl")
    public CommonResult<String> syncTpl(String contentId) {
        log.info("同步模板信息 contentId:{}", JsonLogUtil.toJSONString(contentId));
        return CommonResult.success(oaReportService.syncTpl(contentId));
    }

    /**
     * 删除内部报备请求信息
     * <p>
     * 根据提供的contentId删除对应的内部或外部报备请求。
     *
     * @param contentId 需要删除的报备请求的唯一标识
     * @return 删除操作的结果，封装在CommonResult<String>对象中
     */
    @PostMapping("/delete")
    public CommonResult<String> delete(String contentId) {
        log.info("删除内部报备请求信息 contentId:{}", JsonLogUtil.toJSONString(contentId));
        return CommonResult.success(oaReportService.delete(contentId));
    }


    /**
     * 导出内部报备数据
     * @param response
     * @param oaReportQuery
     */
    @GetMapping("/download")
    public void downloadReport(HttpServletResponse response, @ModelAttribute OaReportQuery oaReportQuery) {
        try {
            oaReportQuery.setContentType("IN");
            log.info("导出内部报备请求信息 oaReportQuery:{}", JsonLogUtil.toJSONString(oaReportQuery));
            List<InExportVO> exportList = oaReportService.inExport(oaReportQuery);

            String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String sequence = dailyCounterUtil.getDailySequenceNumber(2);
            String fileName = "内部短信文案" + dateStr + sequence;

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    FileResponseUtil.buildContentDisposition(fileName, FileResponseUtil.XLSX));

            EasyExcel.write(response.getOutputStream(), InExportVO.class)
                    .sheet("sheet1")
                    .doWrite(exportList);
        } catch (Exception e) {
            log.error("导出内部报备数据失败", e);
            throw new BizException("导出失败");
        }
    }
}
