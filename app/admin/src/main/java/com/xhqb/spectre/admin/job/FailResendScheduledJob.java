package com.xhqb.spectre.admin.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.spectre.admin.config.FailResendConfig;
import com.xhqb.spectre.admin.service.FailResendExecutionService;
import com.xhqb.spectre.admin.service.FailResendStatusService;
import com.xhqb.spectre.common.dal.entity.FailResendRecordDO;
import com.xhqb.spectre.common.dal.mapper.FailResendRecordMapper;
import com.xhqb.spectre.common.enums.FailResendStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 补发定时任务
 */
@Component
@Slf4j
public class FailResendScheduledJob implements SimpleJob {
    
    @Resource
    private FailResendExecutionService failResendExecutionService;
    
    @Resource
    private FailResendStatusService failResendStatusService;
    
    @Resource
    private FailResendRecordMapper failResendRecordMapper;

    @Resource
    private FailResendConfig failResendConfig;

    @Override
    public void execute(ShardingContext shardingContext) {
        if (!failResendConfig.getJob().isEnabled()) {
            log.debug("补发定时任务未启用");
            return;
        }

        long startTime = System.currentTimeMillis();
        log.info("开始执行补发定时任务, shardingItem: {}", shardingContext.getShardingItem());

        int batchSize = failResendConfig.getJob().getBatchSize();
        int totalProcessed = 0;
        int totalSuccess = 0;

        try {
            while (!Thread.currentThread().isInterrupted()) {
                long batchStartTime = System.currentTimeMillis();

                List<FailResendRecordDO> pendingRecords = getTodayAfterPendingRecords(batchSize);

                if (pendingRecords.isEmpty()) {
                    log.debug("没有更多待补发记录，任务结束");
                    break;
                }

                log.debug("获取到待补发记录数量: {}", pendingRecords.size());

                int successCount = batchExecuteFailResend(pendingRecords);
                totalProcessed += pendingRecords.size();
                totalSuccess += successCount;

                long batchEndTime = System.currentTimeMillis();
                long batchDuration = batchEndTime - batchStartTime;

                log.debug("本批次补发完成, 处理: {}, 成功: {}, 失败: {}, 耗时: {} ms",
                        pendingRecords.size(), successCount, pendingRecords.size() - successCount, batchDuration);

                if (pendingRecords.size() < batchSize) {
                    log.debug("最后一批数据已处理，任务结束");
                    break;
                }
            }
        } catch (Exception e) {
            log.error("补发定时任务执行异常", e);
        } finally {
            long totalTime = System.currentTimeMillis() - startTime;
            log.info("补发任务全部完成, 总处理: {}, 总成功: {}, 总失败: {}, 总耗时: {} ms",
                    totalProcessed, totalSuccess, totalProcessed - totalSuccess, totalTime);
        }
    }

    /**
     * 获取当天之后的待补发记录
     */
    private List<FailResendRecordDO> getTodayAfterPendingRecords(int limit) {
        try {
            LocalDate today = LocalDate.now();
            LocalDateTime todayStart = LocalDateTime.of(today, LocalTime.MIN);
            Date todayStartDate = Date.from(todayStart.atZone(ZoneId.of("Asia/Shanghai")).toInstant());

            List<FailResendRecordDO> records = failResendRecordMapper.selectPendingRecordsAfterDate(
                    FailResendStatusEnum.PENDING.getCode(), todayStartDate, limit);

            log.debug("获取到当天之后的待补发记录, 数量: {}, 查询日期: {}", records.size(), todayStartDate);

            return records;

        } catch (Exception e) {
            log.error("获取当天之后的待补发记录异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 批量执行补发
     * @param records 补发记录列表
     * @return 执行成功的数量
     */
    private int batchExecuteFailResend(List<FailResendRecordDO> records) {
        if (records == null || records.isEmpty()) {
            return 0;
        }

        int successCount = 0;

        log.debug("开始批量执行补发, 记录数量: {}", records.size());

        for (FailResendRecordDO record : records) {
            if (Thread.currentThread().isInterrupted()) {
                return 0;
            }
            try {
                if (executeFailResend(record)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.warn("补发执行异常, recordId: {}", record.getId(), e);
                failResendStatusService.markFailed(record.getId());
            }
        }
        log.debug("批量补发执行完成, 总数: {}, 成功: {}", records.size(), successCount);
        return successCount;
    }

    /**
     * 执行单个补发任务
     * @param record 补发记录
     * @return 是否执行成功
     */
    private boolean executeFailResend(FailResendRecordDO record) {
        if (record == null) {
            log.warn("补发记录为空");
            return false;
        }

        Long recordId = record.getId();

        log.debug("开始执行补发, recordId: {}, tplCode: {}, mobile: {}",
                recordId, record.getTplCode(), record.getMobile());

        return failResendExecutionService.executeFailResend(record);
    }
}
