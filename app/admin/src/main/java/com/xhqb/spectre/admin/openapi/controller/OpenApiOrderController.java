package com.xhqb.spectre.admin.openapi.controller;

import com.xhqb.spectre.admin.openapi.common.ActionResult;
import com.xhqb.spectre.admin.openapi.request.QueryOrderRequest;
import com.xhqb.spectre.admin.openapi.response.QueryOrderVO;
import com.xhqb.spectre.admin.service.SmsOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/open/api/order")
@Slf4j
public class OpenApiOrderController {
    @Resource
    private SmsOrderService smsOrderService;

    @PostMapping("")
    public ActionResult<List<QueryOrderVO>> queryOrder(@RequestBody QueryOrderRequest request) {
        List<QueryOrderVO> queryOrderVOList = smsOrderService.queryOrder(request.getRequestIds());
        return ActionResult.success(queryOrderVOList);
    }
}
