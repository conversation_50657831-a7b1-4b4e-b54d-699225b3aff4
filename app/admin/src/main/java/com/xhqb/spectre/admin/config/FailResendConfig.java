package com.xhqb.spectre.admin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 补发功能配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "spectre.admin.fail-resend")
public class FailResendConfig {

    private JobConfig job = new JobConfig();

    private ExecutionConfig execution = new ExecutionConfig();

    private MonitorConfig monitor = new MonitorConfig();
    
    @Data
    public static class JobConfig {
        // 是否启用
        private boolean enabled = true;
        // 每次处理数量
        private int batchSize = 50;

        // 处理超时时间
        private int processingTimeoutMinutes = 10;

        // 只处理当天之后的记录
        private boolean todayOnly = true;
    }
    
    @Data
    public static class ExecutionConfig {
        // 无重试
        private boolean noRetry = true;
    }
    
    @Data
    public static class MonitorConfig {
        private boolean statisticsEnabled = false;
        private int alertThreshold = 100;
    }
}
