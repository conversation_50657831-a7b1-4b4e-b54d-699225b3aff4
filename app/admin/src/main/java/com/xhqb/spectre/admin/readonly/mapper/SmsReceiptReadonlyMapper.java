package com.xhqb.spectre.admin.readonly.mapper;

import com.xhqb.spectre.common.dal.entity.SmsReceiptDO;
import com.xhqb.spectre.common.dal.query.SmsReceiptQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SmsReceiptReadonlyMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_send_receipt
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_send_receipt
     *
     * @mbggenerated
     */
    int insert(SmsReceiptDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_send_receipt
     *
     * @mbggenerated
     */
    int insertSelective(SmsReceiptDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_send_receipt
     *
     * @mbggenerated
     */
    SmsReceiptDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_send_receipt
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SmsReceiptDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_send_receipt
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SmsReceiptDO record);

    Integer countByQuery(SmsReceiptQuery smsReceiptQuery);

    List<SmsReceiptDO> selectByQuery(SmsReceiptQuery smsReceiptQuery);
}