package com.xhqb.spectre.admin.batchtask.result.impl;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.batchtask.cid.CidStrategyFactory;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.result.ApplyLoanResultCounter;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.enums.CidStrategyEnum;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.vo.batchtask.CheckResultVO;
import com.xhqb.spectre.admin.model.vo.batchtask.ParamItemVO;
import com.xhqb.spectre.admin.model.vo.batchtask.QueryTaskSegmentVO;
import com.xhqb.spectre.admin.model.vo.batchtask.UploadQueryVO;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.BatchTaskDO;
import com.xhqb.spectre.common.dal.entity.BatchTaskParamDO;
import com.xhqb.spectre.common.dal.mapper.BatchTaskMapper;
import com.xhqb.spectre.common.dal.mapper.BatchTaskParamMapper;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 更新批量任务参数 taskId 信息
 *
 * <AUTHOR>
 * @date 2021/10/6
 */
@Component
@Slf4j
public class UpdateTaskParamHandler {

    /**
     * 完件列表
     * <p>
     * ING-完件审批中
     * REFUSE-拒绝
     * PASS-通过
     * 都是完件，完件的不发营销
     */
    // private static final String[] APPLY_LOAN_RESULT_LIST = new String[]{"ING", "REFUSE", "PASS"};


    @Autowired
    private VenusConfig venusConfig;
    /**
     * CID类型
     */
    private static final int CID_TYPE = 1;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private BatchTaskParamMapper batchTaskParamMapper;
    @Resource
    private BatchTaskMapper batchTaskMapper;
    @Resource
    private CidStrategyFactory cidStrategyFactory;

    /**
     * 更新批量任务参数 taskId 信息
     *
     * @param batchTaskDO t_batch_task的主键
     * @param taskNoList  任务列表
     */
    public void handler(BatchTaskDO batchTaskDO, List<String> taskNoList) {
        if (Objects.isNull(batchTaskDO)) {
            throw new BizException("群发任务不能够为空");
        }
        Integer taskId = batchTaskDO.getId();
        if (Objects.isNull(taskId) || taskId <= 0) {
            throw new BizException("传入的taskId不能够小于0");
        }
        if (CommonUtil.isEmpty(taskNoList)) {
            // 任务编号为空
            throw new BizException("任务编号列表不能够为空");
        }
        long start = System.currentTimeMillis();
        // 完件过滤移除的数量
        // key -> fileMd5  value -> 数量
        Map<String, ApplyLoanResultCounter> remCounter = new HashMap<>(16);
        for (String taskNo : taskNoList) {
            doHandler(batchTaskDO, taskNo, remCounter);
        }
        log.info("更新批量任务参数taskId信息完成,耗时 = {},taskId = {},taskNoList = {}", (System.currentTimeMillis() - start), taskId, taskNoList);

        try {
            // 完件过滤更新群发任务数量
            this.doUpdateBatchTaskIfNecessary(remCounter, taskId);
        } catch (Exception e) {
            log.error("完件过滤更新群发任务数量失败, taskId = {}", taskId, e);
        }
    }

    /**
     * 根据任务参数更新
     *
     * @param batchTaskDO
     * @param taskNo
     * @param remCounter  完件过滤计数器
     */
    private void doHandler(BatchTaskDO batchTaskDO, String taskNo, Map<String, ApplyLoanResultCounter> remCounter) {
        Integer taskId = batchTaskDO.getId();
        // taskNo 不存在 表示当前已经处理完成
        String uploadQueryResult = stringRedisTemplate.opsForValue().get(RedisKeys.BatchTaskKeys.BATCH_TASK_UPLOAD_RESULT_STR_KEY + ":" + taskNo);
        if (StringUtils.isBlank(uploadQueryResult)) {
            // 没有内容表示文件上传处理失败
            throw new BizException("根据传入的taskNo未查询到文件分片结果,taskNo = " + taskNo);
        }
        UploadQueryVO uploadQueryVO = JSON.parseObject(uploadQueryResult, UploadQueryVO.class);
        QueryTaskSegmentVO taskParamItem = uploadQueryVO.getTaskParamItem();
        if (Objects.isNull(taskParamItem)) {
            throw new BizException("分片结果不存在,taskNo = " + taskNo);
        }
        List<Integer> segmentList = uploadQueryVO.getTaskParamItem().getSegmentList();
        if (CommonUtil.isEmpty(segmentList)) {
            throw new BizException("分片数据不存在,taskNo = " + taskNo);
        }
        long start = System.currentTimeMillis();
        log.info("开始更新分片数据, segmentList ={},taskNo ={},taskId =  {}", segmentList, taskNo, taskId);
        this.updateParamTaskId(segmentList, batchTaskDO, remCounter);
        log.info("更新数据分片结束，耗时 = {},taskNo ={},taskId = {}", (System.currentTimeMillis() - start), taskNo, taskId);
    }


    /**
     * 查询任务参数列表
     *
     * @param segmentList
     * @param batchTaskDO
     * @param remCounter  完件过滤计数器
     */
    private void updateParamTaskId(List<Integer> segmentList, BatchTaskDO batchTaskDO, Map<String, ApplyLoanResultCounter> remCounter) {
        // 获取到完件数据信息
        String[] applyLoanResultArray = cidStrategyFactory.toArrayByKey(CidStrategyEnum.APPLY_LOAN_RESULT.getCode());
        int BATCH_UPDATE_COUNT = venusConfig.getBatchUpdateTaskParamCount();
        // 大数量分批次批量保存
        int paramSize = segmentList.size();
        if (paramSize <= BATCH_UPDATE_COUNT) {
            doUpdate(segmentList, batchTaskDO, remCounter, applyLoanResultArray);
            return;
        }

        int segment = paramSize / BATCH_UPDATE_COUNT;
        List<Integer> subList;

        for (int i = 0; i < segment; i++) {
            subList = segmentList.subList(i * BATCH_UPDATE_COUNT, (i + 1) * BATCH_UPDATE_COUNT);
            doUpdate(subList, batchTaskDO, remCounter, applyLoanResultArray);
        }

        if (paramSize % BATCH_UPDATE_COUNT != 0) {
            subList = segmentList.subList((paramSize / BATCH_UPDATE_COUNT) * BATCH_UPDATE_COUNT, paramSize);
            doUpdate(subList, batchTaskDO, remCounter, applyLoanResultArray);
        }

    }

    /**
     * 更新批量任务参数taskId
     *
     * @param paramIdList
     * @param batchTaskDO
     * @param remCounter           完件过滤计数器
     * @param applyLoanResultArray 完件过滤列表
     */
    private void doUpdate(List<Integer> paramIdList, BatchTaskDO batchTaskDO, Map<String, ApplyLoanResultCounter> remCounter, String[] applyLoanResultArray) {
        if (CommonUtil.isEmpty(paramIdList)) {
            // 为空时 则不进行保存操作
            return;
        }

        Integer taskId = batchTaskDO.getId();
        //   1：cid；2：手机号'
        Integer userIdType = batchTaskDO.getUserIdType();
        // 只有市场营销时才进行完件过滤处理
        boolean isMarket = StringUtils.equalsIgnoreCase(batchTaskDO.getSmsTypeCode(), MessageTypeEnum.MARKET.getMessageType());
        // 添加一个完件过滤的开关
        // 预防完件处理有问题或不需要进行完件过滤时，关闭开关
        // 只有类型为cid并且短信类型是市场营销并且在venus打开完件过滤开关时 才进行完件过滤处理
        boolean isNeedWanJianHandler = Objects.equals(CID_TYPE, userIdType) && isMarket && Objects.equals(true, venusConfig.getBatchTaskWanJianSwitch());
        // 是否包含完件信息
        boolean hasWanJian = hasApplyLoanResult(applyLoanResultArray);

        if (isNeedWanJianHandler && !hasWanJian) {
            // 需要完件处理，但是没有完件信息，打印日志
            // 方便追溯定位问题
            log.info("需要进行完件处理逻辑,但是完件信息为空, taskId = {},applyLoanResultArray = {}", taskId, JSON.toJSONString(applyLoanResultArray));
        }

        // 需要进行完件处理时，完件信息同时存在时才进行完件过滤
        if (isNeedWanJianHandler && hasWanJian) {
            log.info("准备开始进行完件过滤操作,taskId = {},applyLoanResultArray = {}", taskId, JSON.toJSONString(applyLoanResultArray));
            long start = System.currentTimeMillis();
            // cid 时需要更新mapping参数
            this.doCidUpdate(paramIdList, batchTaskDO, remCounter, applyLoanResultArray);
            log.info("cid更新mapping参数耗时 = {},paramIdList = {}", (System.currentTimeMillis() - start), JSON.toJSONString(paramIdList));
        } else {
            // 批量更新
            batchTaskParamMapper.updateTaskIdByIdList(paramIdList, taskId);
        }
    }

    /**
     * 判断是否包含完件信息
     *
     * @param applyLoanResultArray
     * @return
     */
    private boolean hasApplyLoanResult(String[] applyLoanResultArray) {
        return Objects.nonNull(applyLoanResultArray) && applyLoanResultArray.length > 0;
    }

    /**
     * 做cid更新
     *
     * @param paramIdList
     * @param batchTaskDO
     * @param remCounter           完件过滤计数器
     * @param applyLoanResultArray 完件列表
     */
    private void doCidUpdate(List<Integer> paramIdList, BatchTaskDO batchTaskDO, Map<String, ApplyLoanResultCounter> remCounter, String[] applyLoanResultArray) {
        Integer taskId = batchTaskDO.getId();
        List<BatchTaskParamDO> batchTaskParamDOList = batchTaskParamMapper.selectByIdListAndMapping(paramIdList);
        if (CommonUtil.isEmpty(batchTaskParamDOList)) {
            // 以防万一 再做一次更新
            // 批量更新
            batchTaskParamMapper.updateTaskIdByIdList(paramIdList, taskId);
            return;
        }

        // cid需要移除多余的参数信息
        // 短信类型
        String smsTypeCode = batchTaskDO.getSmsTypeCode();
        // 市场营销需要删除手机号
        boolean isRemMobile = StringUtils.equalsIgnoreCase(smsTypeCode, MessageTypeEnum.MARKET.getMessageType());

        String paramJson;
        for (BatchTaskParamDO batchTaskParamDO : batchTaskParamDOList) {
            // 移除cid相关信息
            paramJson = removeCidInfo(batchTaskParamDO, isRemMobile, remCounter, applyLoanResultArray);
            batchTaskParamDO.setParamJsonArray(paramJson);
            batchTaskParamDO.setTaskId(taskId);
        }

        try {
            // 批量更新
            batchTaskParamMapper.batchUpdateTaskIdAndMapping(batchTaskParamDOList);
            // 批量更新完成之后 则需要判断是否有做过完件移除的情况
            // 若存在 则需要更新群发任务的数量 以及更新文件检测的数据信息
            return;
        } catch (Exception e) {
            log.error("批量更新群发参数信息失败,paramIdList = {},taskId ={}", paramIdList, taskId, e);
        }

        // 若批量更新参数失败，那么就直接更新taskId就可以了 这样就不会影响到业务处理逻辑
        // 双重保险
        batchTaskParamMapper.updateTaskIdByIdList(paramIdList, taskId);
    }

    /**
     * 移除cid和完件信息
     *
     * @param batchTaskParamDO
     * @param isRemMobile          是否需要移除手机号(是否进行完件检测)
     * @param remCounter           完件过滤移除的数量
     * @param applyLoanResultArray 完件过滤列表
     * @return
     */
    private String removeCidInfo(BatchTaskParamDO batchTaskParamDO, boolean isRemMobile, Map<String, ApplyLoanResultCounter> remCounter, String[] applyLoanResultArray) {
        String paramJson = batchTaskParamDO.getParamJsonArray();
        if (StringUtils.isBlank(paramJson)) {
            return paramJson;
        }

        List<ParamItemVO> paramItemList = JSON.parseArray(paramJson, ParamItemVO.class);
        if (Objects.isNull(paramItemList)) {
            return paramJson;
        }


        String fileMd5 = batchTaskParamDO.getFileMd5();
        ApplyLoanResultCounter counter = remCounter.get(fileMd5);
        ParamItemVO paramItemVO;
        Iterator<ParamItemVO> iterator = paramItemList.iterator();
        while (iterator.hasNext()) {
            paramItemVO = iterator.next();
            if (Objects.isNull(paramItemVO)) {
                continue;
            }

            if (!checkApplyLoanResult(paramItemVO, isRemMobile, applyLoanResultArray)) {
                // 不需要进行完件过滤
                // 不移除cid信息，防止当数据出现问题时，依旧还有手动处理的机会 2021-11-24
//                jsonObject.remove(BatchTaskConstants.DataType.CID);
//                jsonObject.remove(BatchTaskConstants.DataType.APPLY_LOAN_RESULT);
                continue;
            }

            // 需要进行完件过滤
            // 营销短信，完件手机号需要删除掉
            if (Objects.isNull(counter)) {
                counter = new ApplyLoanResultCounter();
            }
            // 进行计数操作
            counter.add(paramItemVO.get(BatchTaskConstants.DataType.CID));
            // 完件过滤不移除原始数据 防止数据有问题时 好进行手动处理 2021-11-24
            // iterator.remove();
            // 添加过滤标记 ， 打上该标记的数据 不进行发送处理 2021-11-24
            paramItemVO.put(BatchTaskConstants.DataType.SKIP_SEND, "true");
        }
        if (Objects.nonNull(counter)) {
            remCounter.putIfAbsent(fileMd5, counter);
        }
        return JSON.toJSONString(paramItemList);
    }

    /**
     * 做完件校验检测
     *
     * @param paramItemVO
     * @param isRemMobile
     * @param applyLoanResultArray 完件数据列表
     * @return 返回true表示做了完件检测 不需要再往下执行了
     */
    private boolean checkApplyLoanResult(ParamItemVO paramItemVO, boolean isRemMobile, String[] applyLoanResultArray) {
        if (!isRemMobile) {
            // 不需要检测完件
            return false;
        }
        // 获取到完件信息
        String applyLoanResult = paramItemVO.get(BatchTaskConstants.DataType.APPLY_LOAN_RESULT);
        if (StringUtils.isBlank(applyLoanResult)) {
            // 没有完件信息 不进行完件检测
            return false;
        }

        // ING-完件审批中
        // REFUSE-拒绝
        // PASS-通过
        // 都是完件，完件的不发营销
        return StringUtils.equalsAnyIgnoreCase(applyLoanResult, applyLoanResultArray);
    }

    /**
     * 如果有完件检测过滤数据 那么就需要更新群发任务的数量以及文件检测的结果
     *
     * @param remCounter
     * @param taskId
     */
    private void doUpdateBatchTaskIfNecessary(Map<String, ApplyLoanResultCounter> remCounter, Integer taskId) {
        if (remCounter.isEmpty()) {
            // 没有完件过滤信息
            return;
        }

        BatchTaskDO batchTaskDO = batchTaskMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(batchTaskDO)) {
            return;
        }

        Integer totalCount = batchTaskDO.getTotalCount();
        if (Objects.isNull(totalCount) || totalCount == 0) {
            // 总数都没有 就不需要再往下做数量计算了
            return;
        }

        String fileInfoList = batchTaskDO.getFileInfoList();
        if (StringUtils.isBlank(fileInfoList)) {
            // 没有文件信息 也不需要往下做计算了
            return;
        }

        List<CheckResultVO> checkResultList = JSON.parseArray(fileInfoList, CheckResultVO.class);
        if (CommonUtil.isEmpty(checkResultList)) {
            // 数据为空 也不需要往下做计算了
            return;
        }

        // 计数器
        int invalidCount = 0;
        Set<Map.Entry<String, ApplyLoanResultCounter>> entries = remCounter.entrySet();
        String fileMd5;
        ApplyLoanResultCounter counter;
        // 获取无效数据限制
        int limitLength;
        // 文件检测有效的数据量
        Integer validCount;
        for (Map.Entry<String, ApplyLoanResultCounter> entry : entries) {
            fileMd5 = entry.getKey();
            counter = entry.getValue();
            if (StringUtils.isBlank(fileMd5) || Objects.isNull(counter)) {
                continue;
            }
            // 无效数据总量
            invalidCount += counter.getCounter().get();
            CheckResultVO checkResultVO = this.getCheckResultVO(fileMd5, checkResultList);
            if (Objects.isNull(checkResultVO)) {
                continue;
            }

            List<String> badInfo = checkResultVO.getBadInfo();
            if (Objects.isNull(badInfo)) {
                badInfo = new ArrayList<>();
            }

            // 获取到要获取的无效数据量
            limitLength = Math.min(BatchTaskConstants.Commons.CHECK_RESULT_COUNT, counter.getCidList().size()) - badInfo.size();
            for (int i = 0; i < limitLength; i++) {
                badInfo.add(counter.getCid(i));
            }
            Integer badCount = checkResultVO.getBadCount();
            if (Objects.isNull(badCount)) {
                badCount = 0;
            }
            // 设置无效的数据数量
            checkResultVO.setBadCount(badCount + counter.getCidList().size());
            validCount = checkResultVO.getValidCount();
            if (Objects.nonNull(validCount)) {
                // 设置有效的数据量
                // 不进行小于0判断
                // 若小于0 则表示当前计算逻辑有问题
                checkResultVO.setValidCount(validCount - counter.getCounter().get());
            }

        }

        // 计算最终的数据总数
        totalCount = totalCount - invalidCount;
        BatchTaskDO updateResult = new BatchTaskDO();
        updateResult.setId(taskId);
        // 不进行小于0判断
        // 若小于0 则表示当前计算逻辑有问题
        updateResult.setTotalCount(totalCount);
        updateResult.setFileInfoList(JSON.toJSONString(checkResultList));

        String remark = batchTaskDO.getRemark();
        if (StringUtils.isBlank(remark)) {
            remark = "";
        }
        if (invalidCount > 0) {
            // 将完件过滤的数量设置到remark标记中
            remark = remark + "@完件过滤数量=" + invalidCount;
        }
        updateResult.setRemark(remark);
        long start = System.currentTimeMillis();
        batchTaskMapper.updateByPrimaryKeySelective(updateResult);
        log.info("过滤完件数据更新群发任务数量完成 = {}, cost = {}", JSON.toJSONString(updateResult), (System.currentTimeMillis() - start));
    }

    /**
     * 根据fileMd5获取文件检测结果
     *
     * @param fileMd5
     * @param checkResultList
     * @return
     */
    private CheckResultVO getCheckResultVO(String fileMd5, List<CheckResultVO> checkResultList) {
        return checkResultList.stream()
                .filter(s -> StringUtils.equals(fileMd5, s.getFileMd5()))
                .findFirst()
                .orElse(null);
    }

}
