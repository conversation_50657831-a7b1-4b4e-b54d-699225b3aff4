package com.xhqb.spectre.admin.mq.producer;

import com.xhqb.spectre.admin.mq.send.SenderContext;
import com.xhqb.spectre.admin.mq.send.SenderResult;

/**
 * 消息队列生产者
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
public interface MessageProducer<T> {

    /**
     *
     * @param senderContext
     * @return
     * @throws Exception
     */
    SenderResult send(SenderContext<T> senderContext) throws Exception;


}
