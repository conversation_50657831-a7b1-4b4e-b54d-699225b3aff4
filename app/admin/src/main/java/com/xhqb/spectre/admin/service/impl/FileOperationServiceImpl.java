package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.batchtask.io.UploadFileUtils;
import com.xhqb.spectre.admin.batchtask.upload.UploadContext;
import com.xhqb.spectre.admin.batchtask.upload.UploadHandler;
import com.xhqb.spectre.admin.batchtask.upload.cos.S3Helper;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.vo.DownLoadUrlVO;
import com.xhqb.spectre.admin.service.FileOperationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/30 15:28
 */
@Service
@Slf4j
public class FileOperationServiceImpl implements FileOperationService {

    @Resource
    private UploadHandler uploadHandler;

    @Resource
    private S3Helper s3Helper;


    @Override
    public String upload(MultipartFile file) throws IOException {
        File localFile = UploadFileUtils.saveFile(file);
        String fileName = UploadFileUtils.getOriginalFilename(localFile);
        // 文件上传的流
        InputStream uploadStream = Files.newInputStream(localFile.toPath());
        return uploadHandler.handler(new UploadContext(fileName, uploadStream));
    }

    @Override
    public DownLoadUrlVO downLoad(String fileName) {
        DownLoadUrlVO downLoadUrlVO = new DownLoadUrlVO();
        if (StringUtils.isBlank(fileName)) {
            throw new BizException("文件名称不能够为空");
        }
        downLoadUrlVO.setDownLoadUrl(s3Helper.preAdminUrl(fileName));
        return downLoadUrlVO;
    }


}
