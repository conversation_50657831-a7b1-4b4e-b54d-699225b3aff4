package com.xhqb.spectre.admin.batchtask.ab;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.xhqb.msgcenter.model.response.OminiSendResult;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.omini.OminiMessageSpectreHandler;
import com.xhqb.spectre.admin.readonly.mapper.SmsOrderReadonlyMapper;
import com.xhqb.spectre.admin.service.BatchTaskService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.BatchAbJobDO;
import com.xhqb.spectre.common.dal.entity.BatchTaskDO;
import com.xhqb.spectre.common.dal.mapper.BatchAbJobMapper;
import com.xhqb.spectre.common.dal.mapper.BatchTaskMapper;
import com.xhqb.spectre.common.enums.BatchTaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 群发任务灰度任务
 *
 * <AUTHOR>
 * @date 2021/12/29
 */
@Component
@Slf4j
public class BatchTaskAbJobHandler {

    public static final int AB_STATUS_ZERO = 0;
    public static final int AB_STATUS_ONE = 1;

    /**
     * 默认查询的数量
     */
    private static final int DEFAULT_QUERY_SIZE = 1000;

    @Resource
    private BatchAbJobMapper batchAbJobMapper;
    @Resource
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private VenusConfig venusConfig;
    @Autowired
    private BatchTaskService batchTaskService;
    @Resource
    private OminiMessageSpectreHandler ominiMessageSpectreHandler;
    @Resource
    private SmsOrderReadonlyMapper smsOrderReadonlyMapper;

    /**
     * 灰度任务处理
     * <p>
     * 1. 扫描正在处理的群发灰度任务
     * 2. 统计当前群发短信触达率
     * 3. 判断灰度任务是否过期
     * 过期则判断短信触达率是否超过70%,若超过自动提交群发任务
     * 不超过则需要发送钉钉触达率低的警告
     */
    public void jobHandler() {
        Integer lastId = 0;
        List<BatchAbJobDO> batchAbJobList;
        do {
            batchAbJobList = batchAbJobMapper.scanAbJobList(lastId, DEFAULT_QUERY_SIZE);
            if (CommonUtil.isEmpty(batchAbJobList)) {
                break;
            }
            lastId = this.processAdJobList(batchAbJobList);
        } while (true);
    }

    /**
     * 处理灰度任务列表数据
     *
     * @param batchAbJobList
     * @return
     */
    private Integer processAdJobList(List<BatchAbJobDO> batchAbJobList) {
        for (BatchAbJobDO batchAbJob : batchAbJobList) {
            this.processAdJob(batchAbJob);
        }
        return batchAbJobList.get(batchAbJobList.size() - 1).getId();
    }

    /**
     * 处理灰度任务数据
     *
     * @param batchAbJob
     */
    private void processAdJob(BatchAbJobDO batchAbJob) {
        try {
            this.doProcessAdJob(batchAbJob);
        } catch (Exception e) {
            log.warn("处理灰度任务数据失败,batchAbJob = {}", JSON.toJSONString(batchAbJob), e);
        }
    }

    /**
     * 处理灰度任务数据
     *
     * @param batchAbJob
     */
    private void doProcessAdJob(BatchAbJobDO batchAbJob) {
        // 1. 查询短信触达率
        BigDecimal smsRate = this.querySmsRate(batchAbJob);
        BigDecimal abRate = venusConfig.getAbRate();

        if (smsRate.compareTo(abRate) > -1) {
            // 触达率达标 则直接进行群发任务提交操作
            log.info("触达率达标 则直接进行群发任务提交操作,batchAbJob = {}", JSON.toJSONString(batchAbJob));
            this.updateAbJobStatus(batchAbJob.getId(), AB_STATUS_ONE, smsRate, "触达率已达标");
            // 更新群发任务状态为 结束灰度状态
            this.updateBatchTaskToAbWarning(batchAbJob.getTaskId());
            batchTaskService.submit(batchAbJob.getTaskId());
            log.info("群发短信提交成功, batchAbJob = {},smsRate ={}", JSON.toJSONString(batchAbJob), smsRate);
            return;
        }

        // 触达率暂时未达标
        // 检测灰度任务是否过期
        boolean abJobExpire = this.isAbJobExpire(batchAbJob);
        if (!abJobExpire) {
            // 触达率未达标 只更新触达率
            this.updateAbJobStatus(batchAbJob.getId(), null, smsRate, null);
            return;
        }

        // 更新群发任务状态为 结束灰度状态
        this.updateBatchTaskToAbWarning(batchAbJob.getTaskId());

        // 设置灰度任务已完成
        this.updateAbJobStatus(batchAbJob.getId(), AB_STATUS_ONE, smsRate, "触达率未达标,发送警告");

        // 灰度触达率未达标 并发送钉钉警告
        batchAbJob.setRate(smsRate);
        this.sendWarningMessage(batchAbJob);
    }

    /**
     * 查询短信触达率
     *
     * @param batchAbJob
     * @return
     */
    private BigDecimal querySmsRate(BatchAbJobDO batchAbJob) {
        Date createTime = batchAbJob.getCreateTime();
        long startTime = createTime.getTime() / 1000;
        long endTime = DateUtil.endOfDay(createTime).getTime() / 1000;
        Map<String, Long> resultMap = smsOrderReadonlyMapper.abJobRate(batchAbJob.getTaskId(), startTime, endTime);
        if (Objects.isNull(resultMap)) {
            log.warn("短信发送触达率返回结果集为空,batchAbJob = {}", JSON.toJSONString(batchAbJob));
            return BigDecimal.ZERO;
        }

        long total = this.toLong(resultMap.get("total"));
        long success = this.toLong(resultMap.get("success"));
        if (total == 0) {
            log.warn("未查询到短信发送触达率, resultMap = {},batchAbJob = {}", JSON.toJSONString(resultMap), JSON.toJSONString(batchAbJob));
            return BigDecimal.ZERO;
        }

        if (success == 0) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal((double) success / total).multiply(BigDecimal.TEN).multiply(BigDecimal.TEN).setScale(2, BigDecimal.ROUND_HALF_UP);
    }


    /**
     * 判断灰度任务是否过期
     *
     * @param batchAbJob
     * @return
     */
    private boolean isAbJobExpire(BatchAbJobDO batchAbJob) {
        Integer abJobMinute = venusConfig.getAbJobMinute();
        Date createTime = batchAbJob.getCreateTime();
        Date nowTime = new Date();
        long diff = nowTime.getTime() - createTime.getTime();
        long expireMinutes = abJobMinute * 60 * 1000;
        // 不考虑diff小于0的情况(服务器时间与数据库时间不一致问题)
        return diff - expireMinutes >= 0;
    }

    /**
     * 更新群发任务状态为 灰度结束
     *
     * @param taskId
     */
    private void updateBatchTaskToAbWarning(Integer taskId) {
        BatchTaskDO batchTask = new BatchTaskDO();
        batchTask.setId(taskId);
        batchTask.setStatus(BatchTaskStatusEnum.AB_WARNING.getCode());
        // 如果群发任务状态处于灰度中 则更新为灰度结束 否则不做任何操作
        int rows = batchTaskMapper.updateByStatusWithId(taskId, BatchTaskStatusEnum.AB_WARNING.getCode(), null, BatchTaskStatusEnum.AB_SUBMIT.getCode());
        log.info("更新群发任务状态为灰度结束(AB_WARNING)是否成功={},taskId = {}", (rows > 0), taskId);
    }

    /**
     * 更新灰度任务状态
     *
     * @param id
     * @param status
     * @param rate
     * @param description
     */
    private void updateAbJobStatus(Integer id, Integer status, BigDecimal rate, String description) {
        BatchAbJobDO batchAbJob = new BatchAbJobDO();
        batchAbJob.setId(id);
        if (Objects.nonNull(status)) {
            batchAbJob.setStatus(status);
        }
        batchAbJob.setStatus(status);
        if (Objects.nonNull(rate)) {
            batchAbJob.setRate(rate);
        }
        if (StringUtils.isNotBlank(description)) {
            batchAbJob.setDescription(description);
        }
        batchAbJobMapper.updateByPrimaryKeySelective(batchAbJob);
    }

    /**
     * 转换成long
     *
     * @param data
     * @return
     */
    private long toLong(Long data) {
        return Objects.isNull(data) ? 0L : data;
    }

    /**
     * 发送告警消息
     *
     * @param abJobDO
     */
    public void sendWarningMessage(BatchAbJobDO abJobDO) {
        long start = System.currentTimeMillis();
        try {
            BatchTaskAbWarningMessage warningMessage = this.buildWarningMessage(abJobDO.getTaskId(), abJobDO.getRate(), abJobDO.getMobile());
            log.info("灰度触达率未达标准备发送钉钉告警消息,warningMessage = {}, abJob = {}", JSON.toJSONString(warningMessage), JSON.toJSONString(abJobDO));
            OminiSendResult ominiSendResult = ominiMessageSpectreHandler.sendMessage(warningMessage);
            log.info("灰度触达率未达标发送钉钉警告是否成功 = {}, ominiSendResult = {}, cost = {}", ominiMessageSpectreHandler.isSuccess(ominiSendResult), JSON.toJSONString(ominiSendResult), (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.warn("灰度触达率未达标发送钉钉告警消息失败,abJob = {}", JSON.toJSONString(abJobDO), e);
        }
    }


    /**
     * 构建警告消息
     *
     * @param taskId
     * @param rate
     * @param mobile
     * @return
     */
    private BatchTaskAbWarningMessage buildWarningMessage(Integer taskId, BigDecimal rate, String mobile) {
        BatchTaskDO batchTaskDO = batchTaskMapper.selectByPrimaryKey(taskId);
        String name = "";
        if (Objects.nonNull(batchTaskDO)) {
            // 群发备注信息
            name = batchTaskDO.getRemark();
        }
        BatchTaskAbWarningMessage warningMessage = new BatchTaskAbWarningMessage(venusConfig.getAbWarningStrategyId(), venusConfig.getAbWarningDingDingAccount());
        warningMessage.setName(name);
        warningMessage.setId(taskId);
        warningMessage.setRate(rate);
        warningMessage.setSecret(venusConfig.getAbWarningDingDingSecret());
        List<String> mobileList = venusConfig.getAbWarningDingDingAtMobile();
        if (Objects.isNull(mobileList)) {
            mobileList = new ArrayList<>();
        }

        // 添加默认手机号码
        this.addMobile(mobileList, mobile, batchTaskDO);
        // 去重
        mobileList = mobileList.stream().distinct().collect(Collectors.toList());

        warningMessage.setAtMobile(mobileList);
        return warningMessage;
    }

    /**
     * 添加手机号
     *
     * @param mobileList
     * @param mobile
     */
    private void addMobile(List<String> mobileList, String mobile, BatchTaskDO batchTaskDO) {
        if (StringUtils.isNotBlank(mobile)) {
            mobileList.add(0, mobile);
            return;
        }

        List<String> missCreatorDefaultMobile = venusConfig.getMissCreatorDefaultMobile();
        if (CommonUtil.isEmpty(missCreatorDefaultMobile)) {
            return;
        }

        mobileList.addAll(missCreatorDefaultMobile);

        if (Objects.nonNull(batchTaskDO)) {
            log.warn("群发任务创建人手机号码为空，默认使用missCreatorDefaultMobile ={}进行通知,creator = {},taskId = {}", missCreatorDefaultMobile, batchTaskDO.getCreator(), batchTaskDO.getId());
        }
    }

}
