package com.xhqb.spectre.admin.service.impl;

import com.xhqb.kael.sequencegenerator.DistributedSequence;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.TestTaskDTO;
import com.xhqb.spectre.admin.model.vo.ChannelTestTaskVO;
import com.xhqb.spectre.admin.service.ChannelTestTaskService;
import com.xhqb.spectre.admin.service.test.task.TestTaskService;
import com.xhqb.spectre.common.dal.dto.mq.ApiChannelMessage;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.entity.test.TestTaskDO;
import com.xhqb.spectre.common.dal.entity.test.TestTaskLogDO;
import com.xhqb.spectre.common.dal.entity.test.TestTplDO;
import com.xhqb.spectre.common.dal.mapper.*;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ChannelTestTaskQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChannelTestTaskServiceImpl implements ChannelTestTaskService {

    @Resource
    private TestTaskLogMapper taskLogMapper;

    @Resource
    private TestTaskMapper testTaskMapper;

    @Resource
    private TestTaskService testTaskService;

    @Resource
    private TestTplMapper testTplMapper;

    @Resource
    private SignMapper signMapper;

    @Resource
    private TplMapper tplMapper;

    @Resource
    private DistributedSequence distributedSequence;

    @Override
    public CommonPager<ChannelTestTaskVO> listByPage(ChannelTestTaskQuery channelTestTaskQuery) {
        return PageResultUtils.result(
                () -> testTaskMapper.countByQuery(channelTestTaskQuery),
                () -> testTaskMapper.selectByQuery(channelTestTaskQuery).stream().map(this::buildVO).collect(Collectors.toList())
        );
    }

    @Override
    public String add(TestTaskDTO testTaskDTO) {
        TestTaskDO taskDO = new TestTaskDO();
        String taskId = distributedSequence.nextStr("channel:test:task");
        taskDO.setTaskId(taskId);
        taskDO.setTplId(testTaskDTO.getTplId());
        taskDO.setName(testTaskDTO.getName());
        taskDO.setIsDelete(0);
        taskDO.setStatus(0);
        taskDO.setRemark(testTaskDTO.getRemark());
        taskDO.setCreateTime(new Date());
        taskDO.setUpdateTime(new Date());
        // 创建任务发送记录
        List<TestTaskLogDO> taskLogDOList = testTaskService.createTaskLog(taskDO);
        if (CollectionUtils.isEmpty(taskLogDOList)) {
            taskDO.setStatus(3);
        }
        testTaskMapper.insertBySelective(taskDO);
        taskLogDOList.forEach(taskLogDO -> taskLogMapper.insertBySelective(taskLogDO));
        return taskId;
    }

    @Override
    public String cancel(String taskId) {
        TestTaskDO taskDO = testTaskMapper.selectByTaskId(taskId);
        if (Objects.isNull(taskDO)) {
            throw new BizException("记录不存在");
        }
        if (!Objects.equals(0, taskDO.getStatus())) {
            throw new BizException("任务“初始状态”：支持“取消”操作");
        }
        if (testTaskMapper.updateStatusByTaskId(taskId) <= 0) {
            throw new BizException("更新失败");
        }
        return "success";
    }

    @Override
    public String delete(String taskId) {
        TestTaskDO taskDO = testTaskMapper.selectByTaskId(taskId);
        if (Objects.isNull(taskDO)) {
            throw new BizException("记录不存在");
        }
        if (!Objects.equals(2, taskDO.getStatus())) {
            throw new BizException("任务“已取消”：支持 “删除”操作");
        }
        if (testTaskMapper.updateDeleteTagByTaskId(taskId) <= 0) {
            throw new BizException("删除失败");
        }
        return "success";
    }

    @Override
    public List<String> execute(String taskId) {
        TestTaskDO taskDO = testTaskMapper.selectByTaskId(taskId);
        if (Objects.isNull(taskDO)) {
            throw new BizException("记录不存在");
        }
        if (!Objects.equals(0, taskDO.getStatus())) {
            throw new BizException("任务“初始状态”：支持“执行”操作");
        }
        // 下发任务明细
        // 更新
        int updateStatus = 1;
        List<String> failList = new ArrayList<>();
        try {
            failList = testTaskService.execute(taskDO);
        } catch (Exception e) {
            log.info("下发失败", e);
            updateStatus = 3;
        }
        if (testTaskMapper.updateByTaskIdAndStatus(taskId, updateStatus) <= 0) {
            throw new BizException("更新失败");
        }
        return failList;
    }

    private ChannelTestTaskVO buildVO(TestTaskDO testTaskDO) {
        ChannelTestTaskVO channelTestTaskVO = new ChannelTestTaskVO();
        BeanUtils.copyProperties(testTaskDO, channelTestTaskVO);
        return channelTestTaskVO;
    }

    @Override
    public List<String> apiChannelTestTask(List<ApiChannelMessage> apiChannelMessageList) {
        if (CollectionUtils.isEmpty(apiChannelMessageList)) {
            return new ArrayList<>();
        }

        List<String> failList = new ArrayList<>();
        for (ApiChannelMessage apiChannelMessage : apiChannelMessageList) {
            SignDO signDO = signMapper.selectByCode(apiChannelMessage.getSignCode());
            if(Objects.isNull(signDO)){
                return failList;
            }
            TplDO tplDO = tplMapper.selectByCodeAndSign(apiChannelMessage.getTplCode(), signDO.getId());
            if(Objects.isNull(tplDO)){
                return failList;
            }
            TestTplDO testTplDO = testTplMapper.selectByTplId(tplDO.getId());
            if(Objects.isNull(testTplDO)){
                return failList;
            }
            Integer senderLevel = testTplDO.getSenderLevel();
            if (apiChannelMessage.getCount() >= senderLevel) {
                try {
                    testTaskService.check(testTplDO);
                    failList.addAll(trigTask(testTplDO));
                } catch (BizException e) {
                    log.info("已知异常|msg:{}", e.getMsg());
                }
            }
        }
        return failList;
    }

    private List<String> trigTask(TestTplDO testTplDO) {
        List<String> failList = new ArrayList<>();
        try{
            TestTaskDTO testTaskDTO = new TestTaskDTO();
            testTaskDTO.setTplId(testTplDO.getId());
            testTaskDTO.setName(testTplDO.getSmsTplId() + "_" + System.currentTimeMillis());
            String taskId = add(testTaskDTO);
            failList = execute(taskId);
        }catch (Exception e){
            log.warn("下发失败", e);
        }
        return failList;
    }
}
