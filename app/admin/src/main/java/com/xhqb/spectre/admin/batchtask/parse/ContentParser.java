package com.xhqb.spectre.admin.batchtask.parse;

import java.util.List;

/**
 * 内容解析器
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
public interface ContentParser {

    /**
     * 当前解析器是否支持解析操作
     *
     * @param context
     * @return
     */
    boolean supports(ParseContext context);

    /**
     * 具体内容解析操作
     *
     * @param context
     * @return
     * @throws Exception
     */
    List<ParseResult> parse(ParseContext context) throws Exception;
}
