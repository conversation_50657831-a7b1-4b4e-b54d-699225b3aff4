package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.vo.DownLoadUrlVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/30 15:27
 */

public interface FileOperationService {
    /**
     * 文件上传
     * @param file 上传文件
     * @return url
     */
    String upload(MultipartFile file) throws IOException;

    /**
     * 文件下载
     * @param fileName 文件名（文件url）
     * @return
     */
    DownLoadUrlVO downLoad(String fileName);
}
