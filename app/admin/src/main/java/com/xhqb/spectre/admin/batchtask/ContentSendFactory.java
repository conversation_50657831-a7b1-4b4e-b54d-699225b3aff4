package com.xhqb.spectre.admin.batchtask;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xhqb.spectre.admin.batchtask.send.CompositeMessageResult;
import com.xhqb.spectre.admin.batchtask.send.MessageSendResult;
import com.xhqb.spectre.admin.batchtask.send.content.ContentMessageRequest;
import com.xhqb.spectre.admin.batchtask.send.content.ContentMessageSender;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryContext;
import com.xhqb.spectre.admin.batchtask.send.record.MessageSendFailedRecord;
import com.xhqb.spectre.admin.batchtask.send.single.SingleMessageResponse;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于内容发送短信
 *
 * <AUTHOR>
 * @date 2022/1/13
 */
@Configuration
@Slf4j
public class ContentSendFactory {


    @Resource
    private MessageSendFactory messageSendFactory;
    @Resource
    private ContentMessageSender contentMessageSender;
    @Autowired
    private VenusConfig venusConfig;

    /**
     * 单个消息发送请求
     *
     * @param context
     * @return 返回失败的手机号码
     */
    public List<String> sendContent(SingleFactoryContext context) {
        List<String> failureMobile = Lists.newArrayList();
        ContentMessageRequest request = null;
        try {
            long start = System.currentTimeMillis();
            request = this.buildContentMessageRequest(context);
            MessageSendResult<CompositeMessageResult<SingleMessageResponse>> result = contentMessageSender.send(request);
            if (venusConfig.isLogEnable()) {
                log.info("短信基于内容发送请求结果 = {},请求参数 = {},cost = {}", JSON.toJSONString(result), request, (System.currentTimeMillis() - start));
            }
            CompositeMessageResult<SingleMessageResponse> data = result.getData();
            if (Objects.isNull(data)) {
                // 所有号码都发送失败
                return context.getMobileList();
            }
            List<MessageSendFailedRecord> failureSMSRecord = data.getSmsSendResult().getFailureSMSRecord();
            if (CommonUtil.isEmpty(failureSMSRecord)) {
                return failureMobile;
            }
            return failureSMSRecord.stream().map(s -> s.getPhoneNumber()).collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("基于内容短信发送http请求失败,request = {}", JSON.toJSONString(request), e);
            failureMobile = context.getMobileList();
        }
        return failureMobile;
    }


    /**
     * 构建基于内容发送短信
     *
     * @param context
     * @return
     */
    private ContentMessageRequest buildContentMessageRequest(SingleFactoryContext context) {
        List<String> mobileList = context.getMobileList();
        int mobileSize = mobileList.size();
        // 是否存在手机号与参数一一对应的情况
        String appSecret = messageSendFactory.getAppSecret(context.getAppCode());
        ContentMessageRequest request = new ContentMessageRequest();
        //业务应用编码
        request.setAppCode(context.getAppCode());
        // 加签秘钥
        request.setAppSecret(appSecret);
        // 短信类型
        request.setSmsType(context.getSmsCodeType());
        // 设置签名编码
        request.setSignCode(context.getSignCode());
        request.setContent(context.getContent());

        List<Map<String, String>> placeHolderList = context.getPlaceHolderList();
        if (!CommonUtil.isEmpty(placeHolderList)) {
            // 占位符模式
            request.setParamList(placeHolderList);
            return request;
        }

        // 纯文本模式
        List<Map<String, String>> paramMap = new ArrayList<>();
        Map<String, String> mapping;
        for (int i = 0; i < mobileSize; i++) {
            mapping = new HashMap<>(16);
            mapping.put("phone", mobileList.get(i));
            paramMap.add(mapping);
        }

        request.setParamList(paramMap);
        return request;
    }

}
