
package com.xhqb.spectre.admin.service.oa;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.http.HtmlUtil;
import cn.hutool.http.HttpRequest;

import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.enums.OaUrlEnum;
import com.xhqb.spectre.admin.exception.BizException;

import com.xhqb.spectre.admin.service.oa.dto.*;
import com.xhqb.spectre.admin.service.oa.vo.*;

import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.ucenter.sso.client.rpc.SsoUser;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 对接 OA 业务service
 * <p>
 * 对接注册和token https://note.youdao.com/ynoteshare/index.html?id=a9745f54e2a4e3a9a3680e824fcaa54d&type=note&_time=1700120434053?auto
 * rsa在线加密链接: https://www.toolscat.com/decode/rsa
 */

@Service
public class OaBizService {


    private static final Logger log = LoggerFactory.getLogger(OaBizService.class);

    @Resource
    private VenusConfig venusConfig;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private final static String OA_TOKEN = "spectre:admin:oa:token:";
    private final static String OA_REGISTER = "spectre:admin:oa:register:";


    /**
     * 根据分页条件获取人力资源用户信息
     *
     * @return 包含人力资源用户信息的分页视图对象，若未找到则可能返回null
     */
    public HrmUserInfoWithPageVO getHrmUserInfoWithPage() {

        HrmUserInfoWithPageDTO hrmUserInfoWithPageDTO = getHrmUserInfoWithPageDTO();

        Map<String, String> httpHeaders = getHttpHeaders(venusConfig.getUserInfoByUserId());

        log.info("获取人力资源用户信息，请求参数：{}", JSON.toJSONString(hrmUserInfoWithPageDTO));
        String resultJSon = HttpRequest.post(venusConfig.getOaServiceUrl() + OaUrlEnum.GET_HRM_USERINFO_WITH_PAGE.getUrl())
                .addHeaders(httpHeaders)
                .body(JSON.toJSONString(hrmUserInfoWithPageDTO))
                .execute().body();

        log.info("获取人力资源用户信息，响应结果：{}", JSON.toJSONString(resultJSon));
        PageVO pageVO = JSON.parseObject(resultJSon, PageVO.class);

        if (Objects.nonNull(pageVO) && Objects.equals("1", pageVO.getCode())) {
            return pageVO.getData().getDataList().stream().findFirst().orElse(null);
        }

        return null;
    }

    /**
     * 执行注册操作
     *
     * @return 返回注册结果，封装在RegisterVO对象中
     */
    public RegisterVO register() {
        Map<String, String> httpHeaders = new HashMap<>();
        httpHeaders.put("appid", venusConfig.getOaAppId());

        log.info("注册，请求参数：{}", JSON.toJSONString(httpHeaders));
        String resultJSon = HttpRequest.post(venusConfig.getOaServiceUrl() + OaUrlEnum.REGISTER.getUrl())
                .addHeaders(httpHeaders)
                .execute().body();

        String registerKey = OA_REGISTER + venusConfig.getOaAppId();
        log.info("注册，registerKey：{}", registerKey);
        stringRedisTemplate.opsForValue().set(registerKey, resultJSon);

        log.info("注册，响应结果：{}", JSON.toJSONString(resultJSon));
        return JSON.parseObject(resultJSon, RegisterVO.class);
    }

    /**
     * 申请令牌
     *
     * @param userId 用户ID
     * @return 返回包含令牌的ApplyTokenVO对象
     * <p>
     * 说明：
     * 该方法用于为用户申请一个令牌（Token）。
     * 首先，方法尝试从Redis缓存中获取指定用户的令牌。
     * 如果缓存中不存在该用户的令牌（即token为null），则调用applyToken()方法重新申请令牌。
     * 如果缓存中存在令牌，则将其封装在ApplyTokenVO对象中并返回。
     * 注意：
     * - Redis中的键名为"spectre:admin:oa:"加上用户ID，用于唯一标识用户的令牌。
     */
    public ApplyTokenVO applyToken(String userId) {
        log.info("申请令牌，userId:{}", userId);
        String key = OA_TOKEN + venusConfig.getOaAppId();
        String token = stringRedisTemplate.opsForValue().get(key);
        log.info("申请令牌，token:{}", token);
        if (Objects.isNull(token)) {
            return applyToken();
        }

        ApplyTokenVO applyTokenVO = new ApplyTokenVO();
        applyTokenVO.setToken(token);

        return applyTokenVO;
    }


    /**
     * 申请令牌
     *
     * @return 返回包含令牌的ApplyTokenVO对象
     */
    public ApplyTokenVO applyToken() {

        String registerKey = OA_REGISTER + venusConfig.getOaAppId();
        String registerJson = stringRedisTemplate.opsForValue().get(registerKey);
        log.info("申请令牌获取register:{}", registerJson);
        if (Objects.isNull(registerJson)) {
            registerJson = JSON.toJSONString(this.register());
        }

        RegisterVO registerVO = JSON.parseObject(registerJson, RegisterVO.class);

        //RSA加密
        RSA rsa = new RSA(null, registerVO.getSpk().trim());
        // 对密钥进行RSA加密
        String secret = rsa.encryptBase64(registerVO.getSecret().trim(), CharsetUtil.CHARSET_UTF_8, KeyType.PublicKey);

        log.info("申请令牌，secret:{}", secret.trim());
        String resultJSon = HttpRequest.post(venusConfig.getOaServiceUrl() + OaUrlEnum.APPLY_TOKEN.getUrl())
                .header("appid", venusConfig.getOaAppId())
                .header("secret", secret.trim())
                .execute().body();
        log.info("申请令牌，响应结果：{}", JSON.toJSONString(resultJSon));

        JSONObject jsonObject = JSON.parseObject(resultJSon);

        String token = jsonObject.getString("token");

        String tokenKey = OA_TOKEN + venusConfig.getOaAppId();
        stringRedisTemplate.opsForValue().set(tokenKey, token);
        stringRedisTemplate.expire(tokenKey, venusConfig.getOaExpireTime(), TimeUnit.MINUTES);

        return JSON.parseObject(resultJSon, ApplyTokenVO.class);
    }


    /**
     * 发送创建请求
     *
     * @param createRequestDTO 创建请求的数据传输对象，包含创建请求所需的参数
     * @return 返回创建请求的结果视图对象
     */
    public DoCreateRequestVO doCreateRequest(CreateRequestDTO createRequestDTO) {
        DoCreateRequestVO doCreateRequestVO = new DoCreateRequestVO();
        try {
            HrmUserInfoWithPageVO hrmUserInfoWithPage = getHrmUserInfoWithPage();
            if (Objects.isNull(hrmUserInfoWithPage)) {
                throw new BizException("未查询到用户信息");
            }
            Map<String, String> httpHeaders = getHttpHeaders(hrmUserInfoWithPage.getId());
            if (venusConfig.isLogEnable()) {
                log.info("发送创建请求，httpHeaders：{}", JSON.toJSONString(httpHeaders));
                log.info("发送创建请求，hrmUserInfoWithPage：{}", JSON.toJSONString(hrmUserInfoWithPage));
            }

            DoCreateRequestDTO request = buildRequestParams(createRequestDTO, hrmUserInfoWithPage);
            log.info("发送创建请求，请求参数：{}", JSON.toJSONString(request));
            String resultJSon = HttpRequest.post(venusConfig.getOaServiceUrl() + OaUrlEnum.DO_CREATE_REQUEST.getUrl())
                    .addHeaders(httpHeaders)
                    .form(convertObjectToMap(request))
                    .execute().body();
            log.info("发送创建请求，响应结果：{}", JSON.toJSONString(resultJSon));
            doCreateRequestVO = JSON.parseObject(resultJSon, DoCreateRequestVO.class);
            doCreateRequestVO.setUserId(hrmUserInfoWithPage.getId());
        } catch (BizException e) {
            log.error("未查询到用户信息", e);
        } catch (Exception e) {
            log.error("发送创建请求失败", e);
        }


        return doCreateRequestVO;
    }

    /**
     * 获取请求操作员信息(流程实例：获取流程流转数据（对外）)
     *
     * @param flowId 流程ID，用于标识需要查询操作员信息的请求
     * @return 返回包含请求操作员信息的RequestOperatorInfo对象
     * @throws BizException 如果未查询到用户信息，则抛出业务异常
     */
    public RequestOperatorInfo getRequestOperatorInfo(String flowId, String userId) {

        Map<String, String> httpHeaders = getHttpHeaders(userId);
        httpHeaders.remove("Content-Type");
        log.info("流程实例：获取流程流转数据（对外）,请求参数：{}", flowId);
        HttpResponse httpResponse = HttpRequest.get(venusConfig.getOaServiceUrl() + OaUrlEnum.GET_REQUEST_OPERATOR_INFO.getUrl())
                .addHeaders(httpHeaders)
                .form("requestId", flowId)
                .execute();

        if (httpResponse.getStatus() != 200) {
            log.warn("流程实例：获取流程流转数据（对外）失败,响应结果：{}", httpResponse.getStatus());
            return null;
        }
        String resultJSon = httpResponse.body();
        log.info("流程实例：获取流程流转数据（对外）:{}", JSON.toJSONString(resultJSon));
        return JSON.parseObject(resultJSon, RequestOperatorInfo.class);
    }


    /**
     * 获取工作流请求日志
     *
     * @param flowId 流程ID
     * @param userId 用户ID
     * @return 包含工作流请求日志的列表
     */
    public List<WorkflowRequestLogsVO> getWorkflowRequestLogs(String flowId, String userId) {

        Map<String, String> httpHeaders = getHttpHeaders(userId);

        WorkflowRequestLogsDTO workflowRequestLogsDTO = new WorkflowRequestLogsDTO();
        workflowRequestLogsDTO.setWorkflowId(String.valueOf(venusConfig.getOaWorkFlowId()));
        workflowRequestLogsDTO.setRequestId(flowId);
        workflowRequestLogsDTO.setPageSize(String.valueOf(50));
        workflowRequestLogsDTO.setUserId(userId);

        log.info("流程实例:获取流程意见（对外）,请求参数：{}", JSON.toJSONString(workflowRequestLogsDTO));
        HttpResponse httpResponse = HttpRequest.post(venusConfig.getOaServiceUrl() + OaUrlEnum.GET_WORKFLOW_REQUEST_LOGS.getUrl())
                .addHeaders(httpHeaders)
                .form(convertObjectToMap(workflowRequestLogsDTO))
                .execute();
        if (httpResponse.getStatus() != 200) {
            log.warn("流程实例:获取流程意见（对外）失败,响应结果：{}", httpResponse.getStatus());
            return null;
        }

        String resultJSon = httpResponse.body();
        log.info("流程实例:获取流程意见（对外）,响应结果：{}", JSON.toJSONString(resultJSon));
        return JSON.parseArray(resultJSon, WorkflowRequestLogsVO.class);
    }

    /**
     * 获取流程请求信息(流程实例：获取流程信息（对外）)
     *
     * @param flowId 流程ID，用于标识需要查询的流程请求
     * @return 返回包含流程请求信息的WorkflowRequestVO对象
     * @throws BizException 如果未查询到用户信息，则抛出业务异常
     *                      <p>
     *                      说明：
     *                      该方法用于根据提供的流程ID获取对应的流程请求信息。
     *                      首先，方法尝试获取当前用户的HRM用户信息。如果未查询到用户信息，则抛出BizException异常。
     *                      然后，根据用户ID生成HTTP请求头，并移除其中的Content-Type字段。
     *                      接着，通过HTTP GET请求向OA服务URL发送请求，请求中包含流程ID作为参数。
     *                      最后，将响应的JSON字符串解析为WorkflowRequestVO对象并返回。
     *                      在请求和响应过程中，均会记录日志信息以便追踪和调试。
     */
    public WorkflowRequestVO getWorkflowRequest(String flowId, String userId) {

        Map<String, String> httpHeaders = getHttpHeaders(userId);
        httpHeaders.remove("Content-Type");
        log.info("流程实例:获取流程信息（对外）,请求参数：{}", flowId);
        String resultJSon = HttpRequest.get(venusConfig.getOaServiceUrl() + OaUrlEnum.GET_WORK_FLOW_REQUEST.getUrl())
                .addHeaders(httpHeaders)
                .form("requestId", flowId)
                .execute().body();
        log.info("流程实例:获取流程信息（对外）,响应结果：{}", JSON.toJSONString(resultJSon));
        return JSON.parseObject(resultJSon, WorkflowRequestVO.class);
    }


    public List<FlowDataAllInfo> getFlowDataAllInfo(String flowId, String userId) {

        if (venusConfig.isLogEnable()) {
            log.info("获取流程流转数据,flowId:{},userId:{}", flowId, userId);
        }

        List<WorkflowRequestLogsVO> workflowRequestLogs = getWorkflowRequestLogs(flowId, userId);
        RequestOperatorInfo requestOperatorInfo = getRequestOperatorInfo(flowId, userId);

        if (CollectionUtils.isEmpty(workflowRequestLogs) || Objects.isNull(requestOperatorInfo) || CollectionUtils.isEmpty(requestOperatorInfo.getData())) {
            return new ArrayList<>();
        }
        Map<String, List<WorkflowRequestLogsVO>> nodeIdMap = workflowRequestLogs.stream()
                .collect(Collectors.groupingBy(WorkflowRequestLogsVO::getNodeId));
        List<FlowDataAllInfo> flowDataAllInfos = new ArrayList<>();
        List<FlowDataInfo> flowDataInfoList = requestOperatorInfo.getData();


        Map<String, List<FlowDataInfo>> nodeIdDataMap = flowDataInfoList.stream().collect(Collectors.groupingBy(f -> String.valueOf(f.getNodeid())));
        for (Map.Entry<String, List<FlowDataInfo>> entry : nodeIdDataMap.entrySet()) {
            List<FlowDataInfo> valueList = entry.getValue();
            String nodeId = entry.getKey();
            for (int i = 0; i < valueList.size(); i++) {
                FlowDataInfo tempData = valueList.get(i);
                FlowDataAllInfo flowDataAllInfo = new FlowDataAllInfo();
                flowDataAllInfo.setId(tempData.getId());
                flowDataAllInfo.setUserId(String.valueOf(tempData.getUserid()));
                flowDataAllInfo.setUserName(tempData.getUserName());
                flowDataAllInfo.setNodeName(tempData.getNodename());
                flowDataAllInfo.setNodeId(nodeId);
                flowDataAllInfo.setIsRemark(tempData.getIsremark());
                flowDataAllInfo.setViewType(tempData.getViewType());
                String tempTimeStr = tempData.getReceivedate() + " " + tempData.getOperatetime();
                flowDataAllInfo.setOperateTime(tempTimeStr);
                flowDataAllInfo.setOperateDate(tempData.getOperatedate());
                List<WorkflowRequestLogsVO> workflowRequestLogsVOList = nodeIdMap.get(nodeId);

                if (CollectionUtils.isNotEmpty(workflowRequestLogsVOList)) {
                    for (WorkflowRequestLogsVO workflowRequestLogsVO : workflowRequestLogsVOList) {
                        String timeStr = workflowRequestLogsVO.getOperateDate() + " " + workflowRequestLogsVO.getOperateTime();
                        if (Objects.equals(timeStr, tempTimeStr)) {
                            flowDataAllInfo.setRemark(workflowRequestLogsVO.getRemark());
                            flowDataAllInfo.setOperateType(workflowRequestLogsVO.getOperateType());
                        }

                    }
                }
                flowDataAllInfos.add(flowDataAllInfo);
            }
        }


        flowDataAllInfos.sort(Comparator.comparingLong(FlowDataAllInfo::getId));
        return flowDataAllInfos;
    }

    /**
     * 获取HTTP请求头
     *
     * @param userId 用户ID
     * @return 包含所需Map<String, String>对象
     */
    private Map<String, String> getHttpHeaders(String userId) {
        Map<String, String> httpHeaders = new HashMap<>();
        if (Strings.isBlank(userId)) {
            throw new BizException("userId不能为空");
        }
        String registerKey = OA_REGISTER + venusConfig.getOaAppId();
        String registerJson = stringRedisTemplate.opsForValue().get(registerKey);
        if (Objects.isNull(registerJson)) {
            registerJson = JSON.toJSONString(this.register());
        }
        RegisterVO registerVO = JSON.parseObject(registerJson, RegisterVO.class);

        if (venusConfig.isLogEnable()) {
            log.info("获取用户信息,userId:{}", userId);
        }

        //RSA加密
        RSA rsa = new RSA(null, registerVO.getSpk().trim());
        // 对密钥进行RSA加密
        String userid = rsa.encryptBase64(userId, CharsetUtil.CHARSET_UTF_8, KeyType.PublicKey);
        httpHeaders.put("appid", venusConfig.getOaAppId());
        httpHeaders.put("userid", userid);
        httpHeaders.put("Content-Type", "application/x-www-form-urlencoded");


        httpHeaders.put("token", applyToken(userId).getToken());
        return httpHeaders;
    }


    public Map<String, String> getFlowContentMap(String flowId, String userId) {
        WorkflowRequestVO workflowRequest = getWorkflowRequest(flowId, userId);

        log.info("获取流程请求信息,WorkflowRequestVO:{}", JSON.toJSONString(workflowRequest));

        if (Objects.isNull(workflowRequest) || Objects.isNull(workflowRequest.getData())) {
            return new HashMap<>();
        }
        // key:content_id, value:短信内容
        Map<String, String> flowContentMap = new HashMap<>();

        List<WorkflowDetailTableInfosVO> detailTableInfosList = workflowRequest.getData().getWorkflowDetailTableInfos();
        if (CollectionUtils.isEmpty(detailTableInfosList)) {
            log.info("获取流程请求信息失败,未查询到流程详情信息");
            return new HashMap<>();
        }

        Optional<WorkflowDetailTableInfosVO> detailTableInfosOptional = detailTableInfosList.stream()
                .filter(item -> Objects.equals(item.getTableDBName(), venusConfig.getOaDetailTableDbName()))
                .findFirst();

        if (!detailTableInfosOptional.isPresent()) {
            log.info("获取流程请求信息失败,未查询到流程详情信息 tableDbName:{}", venusConfig.getOaDetailTableDbName());
            return new HashMap<>();
        }

        WorkflowDetailTableInfosVO detailTableInfosVO = detailTableInfosOptional.get();
        Pattern pattern = Pattern.compile("(【.*?】)");
        List<DoCreateRequestDetailTableDTO> dtoList = detailTableInfosVO.getWorkflowRequestTableRecords();
        for (DoCreateRequestDetailTableDTO dto : dtoList) {
            String key = "";
            String value = "";

            for (DoCreateRequestParam param : dto.getWorkflowRequestTableFields()) {
                if (Objects.equals("mbid", param.getFieldName())) {
                    key = param.getFieldValue();
                } else if (Objects.equals("dxnr", param.getFieldName())) {
                    value = param.getFieldValue();
                    Matcher matcher = pattern.matcher(value);
                    if (matcher.find()) {
                        String group = matcher.group();
                        value = value.replace(group, "");
                        value = HtmlUtil.unescape(value);
                        value = HtmlUtil.cleanHtmlTag(value);

                    }
                }
            }
            flowContentMap.put(key, value);
        }
        return flowContentMap;

    }


    /**
     * 根据提供的CreateRequestDTO和HrmUserInfoWithPageVO对象，构建DoCreateRequestDTO请求参数
     *
     * @param createRequestDTO    请求的原始DTO对象，包含创建请求的基本信息
     * @param hrmUserInfoWithPage 用户信息页面视图对象，包含用户相关的详细信息
     * @return 构建好的DoCreateRequestDTO对象，包含用于后续处理的请求参数
     */
    public DoCreateRequestDTO buildRequestParams(CreateRequestDTO createRequestDTO, HrmUserInfoWithPageVO
            hrmUserInfoWithPage) {
        JSONObject jsonObject = JSON.parseObject(venusConfig.getOaCreateRequestInfo());

        List<DoCreateRequestParam> doCreateRequestParamList = new ArrayList<>();
        Map<String, Object> dtoMap = this.convertObjectToMap(createRequestDTO);
        Map<String, Object> pageMap = this.convertObjectToMap(hrmUserInfoWithPage);

        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            DoCreateRequestParam doCreateRequestParam = new DoCreateRequestParam();
            String key = entry.getKey();
            String fieldName = (String) entry.getValue();

            doCreateRequestParam.setFieldName(fieldName);

            // 尝试从CreateRequestDTO获取值
            Object dtoValue = dtoMap.get(key);
            if (dtoValue != null) {
                try {
                    doCreateRequestParam.setFieldValue(dtoValue.toString());
                } catch (Exception e) {
                    log.error("Error getting value from CreateRequestDTO for field: {}", key, e);
                }
            }

            // 尝试从HrmUserInfoWithPageVO获取值
            Object pageValue = pageMap.get(key);
            if (pageValue != null) {
                try {
                    doCreateRequestParam.setFieldValue(pageValue.toString());
                } catch (Exception e) {
                    log.error("Error getting value from HrmUserInfoWithPageVO for field: {}", key, e);
                }
            }

            doCreateRequestParamList.add(doCreateRequestParam);
        }

        // 明细表
        List<DoCreateRequestDetailDTO> doCreateRequestDetailDTOS = buildDoCreateRequestDetailDTOS(createRequestDTO, venusConfig.getOaDetailTableColumns());

        DoCreateRequestParam dateParam = new DoCreateRequestParam();
        dateParam.setFieldName("sqrq");
        dateParam.setFieldValue(DateUtil.format(new Date(), "yyyy-MM-dd"));
        doCreateRequestParamList.add(dateParam);
        DoCreateRequestDTO doCreateRequestDTO = new DoCreateRequestDTO();
        doCreateRequestDTO.setWorkflowId(createRequestDTO.getWorkflowId());
        doCreateRequestDTO.setRequestName(createRequestDTO.getTitle());
        doCreateRequestDTO.setMainData(JSON.toJSONString(doCreateRequestParamList));
        doCreateRequestDTO.setDetailData(JSON.toJSONString(doCreateRequestDetailDTOS));

        return doCreateRequestDTO;
    }

    private List<DoCreateRequestDetailDTO> buildDoCreateRequestDetailDTOS(CreateRequestDTO createRequestDTO, String oaDetailTableColumns) {
        List<DoCreateRequestDetailDTO> doCreateRequestDetailDTOS = new LinkedList<>();
        DoCreateRequestDetailDTO detailDTO = new DoCreateRequestDetailDTO();
        List<DoCreateRequestDetailTableDTO> detailTableDTOList = new LinkedList<>();
        createRequestDTO.getContentMap().forEach((k, v) -> {
            DoCreateRequestDetailTableDTO detailTableDTO = new DoCreateRequestDetailTableDTO();
            List<DoCreateRequestParam> workflowRequestTableFields = new LinkedList<>();
            for (String item : oaDetailTableColumns.split(",")) {
                DoCreateRequestParam doCreateRequestParam = new DoCreateRequestParam();
                doCreateRequestParam.setFieldName(item);
                doCreateRequestParam.setFieldValue(Objects.equals("mbid", item) ? k : v);
                workflowRequestTableFields.add(doCreateRequestParam);
            }
            detailTableDTO.setWorkflowRequestTableFields(workflowRequestTableFields);
            detailTableDTOList.add(detailTableDTO);
        });

        detailDTO.setTableDbName(venusConfig.getOaDetailTableDbName());
        detailDTO.setWorkflowRequestTableRecords(detailTableDTOList);
        doCreateRequestDetailDTOS.add(detailDTO);
        return doCreateRequestDetailDTOS;
    }

    private static HrmUserInfoWithPageDTO getHrmUserInfoWithPageDTO() {
        HrmUserInfoWithPageDTO hrmUserInfoWithPageDTO = new HrmUserInfoWithPageDTO();
        HrmUserInfoWithInnerPageDTO hrmUserInfoWithInnerPageDTO = new HrmUserInfoWithInnerPageDTO();
        SsoUser ssoUser = SsoUserInfoUtil.getSubject();
        if (Objects.isNull(ssoUser)) {
            throw new BizException("未登录");
        }
        hrmUserInfoWithInnerPageDTO.setWorkcode(ssoUser.getEmpId());
        hrmUserInfoWithPageDTO.setParams(hrmUserInfoWithInnerPageDTO);
        return hrmUserInfoWithPageDTO;
    }


    public Map<String, Object> convertObjectToMap(Object obj) {
        // 创建一个Map来存储转换后的键值对
        Map<String, Object> map = new HashMap<>();

        // 获取对象的类
        Class<?> clazz = obj.getClass();

        // 遍历对象的字段
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true); // 设置字段可访问

            try {
                // 获取字段名和值，并存储到Map中
                String fieldName = field.getName();
                Object fieldValue = field.get(obj);
                map.put(fieldName, fieldValue);
            } catch (Exception e) {
                // Ignore if Exception does not exist
            }
        }

        return map;
    }
}