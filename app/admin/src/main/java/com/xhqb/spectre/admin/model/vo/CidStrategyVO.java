package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.CidStrategyDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Cid校验策略表
 *
 * <AUTHOR>
 * @date 2021/11/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CidStrategyVO implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 策略值
     */
    private String strategyValue;

    /**
     * 类型 0->用户状态 1->用户完件
     */
    private String type;

    /**
     * 状态 0->未使用(未选中) 1->使用(已选中)
     */
    private Integer status;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 策略分组
     */
    private String strategyGroup;

    /**
     * 查询列表数据展现
     *
     * @param cidStrategyDO
     * @return
     */
    public static CidStrategyVO buildListQuery(CidStrategyDO cidStrategyDO) {
        return buildInfoQuery(cidStrategyDO);
    }

    /**
     * 查询数据详情展现
     *
     * @param cidStrategyDO
     * @return
     */
    public static CidStrategyVO buildInfoQuery(CidStrategyDO cidStrategyDO) {
        return CidStrategyVO.builder()
                // 主键
                .id(cidStrategyDO.getId())
                // 策略值
                .strategyValue(cidStrategyDO.getStrategyValue())
                // 类型 0->用户状态 1->用户完件
                .type(cidStrategyDO.getType())
                // 状态 0->未使用 1->使用
                .status(cidStrategyDO.getStatus())
                // 创建人
                .creator(cidStrategyDO.getCreator())
                // 创建时间
                .createTime(cidStrategyDO.getCreateTime())
                // 修改人
                .updater(cidStrategyDO.getUpdater())
                // 更新时间
                .updateTime(cidStrategyDO.getUpdateTime())
                // 描述信息
                .description(cidStrategyDO.getDescription())
                // 分组
                .strategyGroup(cidStrategyDO.getStrategyGroup())
                .build();
    }
}
