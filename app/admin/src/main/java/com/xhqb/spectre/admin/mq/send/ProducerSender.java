package com.xhqb.spectre.admin.mq.send;

import org.apache.pulsar.client.api.TypedMessageBuilder;

/**
 * 消息发送
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
public interface ProducerSender {

    /**
     * 做mq消息发送
     *
     * @param messageBuilder
     * @param senderContext
     * @param <T>
     * @return
     * @throws Exception
     */
    <T> SenderResult send(TypedMessageBuilder<String> messageBuilder, SenderContext<T> senderContext) throws Exception;

}
