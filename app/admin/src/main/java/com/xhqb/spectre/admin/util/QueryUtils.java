package com.xhqb.spectre.admin.util;

import com.xhqb.spectre.common.dal.query.MultipleCompareQuery;
import com.xhqb.spectre.common.dal.query.ReachRateQuery;
import com.xhqb.spectre.common.dal.query.SendQuery;
import org.apache.commons.lang3.StringUtils;

public class QueryUtils {

    private static final String DEFAULT_TABLE_NAME = "t_sms_send_stat";
    private static final String DEFAULT_CHANNEL_CODE = "t_sms_platform_send_stat";

    public  static void setQuery(SendQuery query) {
        if (StringUtils.isBlank(query.getChannelCode())) {
            return;
        }
        query.setTableName(defaultTableName());
    }

    public  static void setQuery(ReachRateQuery query) {
        if (StringUtils.isBlank(query.getChannelCode())) {
            return;
        }
        query.setTableName(defaultTableName());
    }

    public  static void setQuery(MultipleCompareQuery query) {
        if (StringUtils.isBlank(query.getChannelCode())) {
            return;
        }
        query.setTableName(defaultTableName());
    }

    public static String channelTableName() {
        return DEFAULT_CHANNEL_CODE;
    }

    public static String defaultTableName() {
        return DEFAULT_TABLE_NAME;
    }
}
