package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.ShortUrlDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/11 16:11
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShortUrlVO implements Serializable {

    private static final long serialVersionUID = 1141530547356360007L;

    private static final Date PERMANENT_DATE = DateUtil.smallToDate("2099-12-31");

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 短链编码
     */
    private String shortCode;

    /**
     * 短链地址
     */
    private String shortUrl;

    /**
     * 源地址
     */
    private String srcUrl;

    /**
     * 过期日期
     */
    private String expiredDate;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态，0：无效，1：有效
     */
    private Integer status;

    /**
     * 点击次数
     */
    private Integer clickCount;

    /**
     * 独立IP点击次数
     */
    private Integer ipClickCount;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新人
     */
    private String updater;

    public static ShortUrlVO buildShortUrlVO(ShortUrlDO item, String shortUrl) {
        String expiredDate = PERMANENT_DATE.equals(item.getExpiredDate()) ? "永久有效" : DateUtil.smallToStr(item.getExpiredDate());
        return ShortUrlVO.builder()
                .id(item.getId())
                .shortCode(item.getShortCode())
                .shortUrl(shortUrl)
                .srcUrl(item.getSrcUrl())
                .expiredDate(expiredDate)
                .description(item.getDescription())
                .status(item.getStatus())
                .clickCount(item.getClickCount())
                .ipClickCount(item.getIpClickCount())
                .createTime(DateUtil.dateToString(item.getCreateTime()))
                .creator(item.getCreator())
                .updateTime(DateUtil.dateToString(item.getUpdateTime()))
                .updater(item.getUpdater())
                .build();
    }
}
