package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.ChannelGroupDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2022/4/11 11:16
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelGroupVO implements Serializable {

    private static final long serialVersionUID = -6352588282041396195L;

    /**
     * 渠道组ID
     */
    private Integer id;

    /**
     * 渠道组名称
     */
    private String name;

    /**
     * 渠道组描述
     */
    private String description;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 签名ID
     */
    private Integer signId;

    /**
     * 渠道信息列表
     */
    private List<ChannelGroupItemVO> channelInfoList;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新人
     */
    private String updater;

    public static ChannelGroupVO build(ChannelGroupDO item) {
        return ChannelGroupVO.builder()
                .id(item.getId())
                .name(item.getName())
                .description(item.getDescription())
                .smsTypeCode(item.getSmsTypeCode())
                .signId(item.getSignId())
                .createTime(DateUtil.dateToString(item.getCreateTime()))
                .creator(item.getCreator())
                .updateTime(DateUtil.dateToString(item.getUpdateTime()))
                .updater(item.getUpdater())
                .build();
    }

    public static ChannelGroupVO build(ChannelGroupDO item, List<ChannelGroupItemVO> channelInfoList) {
        ChannelGroupVO channelGroupVO = build(item);
        channelGroupVO.setChannelInfoList(channelInfoList);
        return channelGroupVO;
    }
}
