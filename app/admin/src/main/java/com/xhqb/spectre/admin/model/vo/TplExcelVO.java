package com.xhqb.spectre.admin.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.TplDO;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/21 16:11
 **/
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Data
@Builder
@ContentRowHeight(18)
@HeadRowHeight(25)
@ColumnWidth(20)
public class TplExcelVO implements Serializable {
    private static final long serialVersionUID = -3943465469386315270L;

    @ExcelProperty(index = 0, value = "id")
    private Integer id;

    @ExcelProperty(index = 2, value = "模版编码")
    @ColumnWidth(36)
    private String code;

    @ExcelProperty(index = 1, value = "名称")
    @ColumnWidth(25)
    private String title;

    @ExcelProperty(index = 6, value = "完整模版内容")
    @ColumnWidth(124)
    private String content;

    @ExcelProperty(index = 7, value = "更新时间")
    private String updateTime;

    @ExcelProperty(index = 8, value = "更新人")
    private String updater;

    @ExcelProperty(index = 3, value = "签名")
    private String signName;

    @ExcelProperty(index = 4, value = "短信类型")
    private String smsTypeName;

    @ExcelProperty(index = 5, value = "状态")
    private String statusName;

    public static TplExcelVO build(TplDO tplDO, String singName, String smsTypeName, String statusName) {
        return TplExcelVO.builder()
                .id(tplDO.getId())
                .title(tplDO.getTitle())
                .code(tplDO.getCode())
                .signName(singName)
                .smsTypeName(smsTypeName)
                .statusName(statusName)
                .content(tplDO.getContent())
                .updateTime(DateUtil.dateToString(tplDO.getUpdateTime()))
                .updater(tplDO.getUpdater())
                .build();
    }
}
