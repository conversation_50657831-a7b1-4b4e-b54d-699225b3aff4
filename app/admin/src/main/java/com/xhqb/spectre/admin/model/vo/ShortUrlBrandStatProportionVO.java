package com.xhqb.spectre.admin.model.vo;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

@Data
public class ShortUrlBrandStatProportionVO {
    private List<String> brands;
    private List<Double> proportion;

    public static ShortUrlBrandStatProportionVO buildDefault() {
        ShortUrlBrandStatProportionVO shortUrlBrandStatProportionVO = new ShortUrlBrandStatProportionVO();
        shortUrlBrandStatProportionVO.setBrands(Lists.newArrayList("apple", "huawei", "xiaomi", "oppo", "vivo", "other", "honor"));
        shortUrlBrandStatProportionVO.setProportion(Lists.newArrayList(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0));
        return shortUrlBrandStatProportionVO;
    }
}
