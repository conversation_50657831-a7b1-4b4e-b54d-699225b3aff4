package com.xhqb.spectre.admin.model.vo;

import lombok.Data;

import java.util.Date;

@Data
public class ChannelTestTaskVO {

    /**
     * 测试模版
     */
    private Long tplId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 号码个数
     */
    private Integer mobileCount;

    /**
     * 任务状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

}
