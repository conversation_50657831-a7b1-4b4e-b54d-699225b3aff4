package com.xhqb.spectre.admin.config.properties;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/*
 * @Author: huangyanxiong
 * @Date: 2022/6/1 15:13
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ConfigurationProperties(prefix = "kael.datasource.druid.spectre-read")
public class SpectreReadDruidProperties extends DruidProperties {
}
