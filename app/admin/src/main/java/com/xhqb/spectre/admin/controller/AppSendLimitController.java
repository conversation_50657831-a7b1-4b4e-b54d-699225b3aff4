package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.AppSendLimitDTO;
import com.xhqb.spectre.admin.model.dto.SendLimitListDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.AppSendLimitVO;
import com.xhqb.spectre.admin.service.AppSendLimitService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.AppSendLimitQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 10:18
 * @Description:
 */
@RestController
@RequestMapping("/appSendLimit")
@Slf4j
public class AppSendLimitController {

    @Autowired
    private AppSendLimitService appSendLimitService;

    /**
     * 查询应用限流配置列表
     *
     * @param appSendLimitQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(@ModelAttribute AppSendLimitQuery appSendLimitQuery, Integer pageNum, Integer pageSize) {
        appSendLimitQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<AppSendLimitVO> commonPager = appSendLimitService.listByPage(appSendLimitQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询应用限流配置详情
     *
     * @param appCode
     * @return
     */
    @GetMapping("/{appCode}")
    public AdminResult queryInfo(@PathVariable("appCode") String appCode) {
        return AdminResult.success(appSendLimitService.getByAppCode(appCode));
    }

    /**
     * 编辑
     *
     * @param appCode
     * @param sendLimitListDTO
     * @return
     */
    @PostMapping("/{appCode}")
    @LogOpTime(OpLogConstant.MODULE_SEND_LIMIT)
    public AdminResult edit(@PathVariable("appCode") String appCode, @RequestBody SendLimitListDTO sendLimitListDTO) {
        AppSendLimitDTO appSendLimitDTO = AppSendLimitDTO.builder()
                .appCode(appCode)
                .limitList(sendLimitListDTO)
                .build();
        log.info("edit appSendLimit, appSendLimitDTO: {}", JSON.toJSONString(appSendLimitDTO));
        appSendLimitService.edit(appSendLimitDTO);
        return AdminResult.success();
    }

    /**
     * 启用
     *
     * @param appCode
     * @return
     */
    @PostMapping("/enable/{appCode}")
    @LogOpTime(OpLogConstant.MODULE_SEND_LIMIT)
    public AdminResult enable(@PathVariable("appCode") String appCode) {
        log.info("enable appSendLimit, appCode: {}", appCode);
        appSendLimitService.enable(appCode);
        return AdminResult.success();
    }

    /**
     * 停用
     *
     * @param appCode
     * @return
     */
    @PostMapping("/disable/{appCode}")
    @LogOpTime(OpLogConstant.MODULE_SEND_LIMIT)
    public AdminResult disable(@PathVariable("appCode") String appCode) {
        log.info("disable appSendLimit, appCode: {}", appCode);
        appSendLimitService.disable(appCode);
        return AdminResult.success();
    }

    /**
     * 删除
     *
     * @param appCode
     * @return
     */
    @DeleteMapping("/{appCode}")
    @LogOpTime(OpLogConstant.MODULE_SEND_LIMIT)
    public AdminResult delete(@PathVariable("appCode") String appCode) {
        log.info("delete appSendLimit, appCode: {}", appCode);
        appSendLimitService.delete(appCode);
        return AdminResult.success();
    }

    /**
     * 查询限流规则枚举
     *
     * @return
     */
    @GetMapping("/queryLimitRuleEnum")
    public AdminResult queryLimitRuleEnum() {
        return AdminResult.success(appSendLimitService.queryLimitRuleEnum());
    }
}
