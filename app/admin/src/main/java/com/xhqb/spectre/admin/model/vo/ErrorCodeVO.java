package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.ErrorCodeDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 错误码
 *
 * <AUTHOR>
 * @date 2021/10/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ErrorCodeVO implements Serializable {
    /**
     * 错误码类型 submit->短信发送 deliver->短信回执
     */
    private String type;
    /**
     * 错误码编码
     */
    private Integer xhErrCode;
    /**
     * 错误码描述
     */
    private String codeDesc;
    /**
     * 是否重发 0->否 1->是
     */
    private Integer retry;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;


    /**
     * 查询列表数据展现
     *
     * @param errorCodeDO
     * @return
     */
    public static ErrorCodeVO buildListQuery(ErrorCodeDO errorCodeDO) {
        return ErrorCodeVO.builder()
                // 错误码类型 submit->短信发送 deliver->短信回执
                .type(errorCodeDO.getType())
                // 错误码编码
                .xhErrCode(errorCodeDO.getXhErrCode())
                // 错误码描述
                .codeDesc(errorCodeDO.getCodeDesc())
                // 是否重发 0->否 1->是
                .retry(errorCodeDO.getRetry())
                // 创建时间
                .createTime(errorCodeDO.getCreateTime())
                // 修改时间
                .updateTime(errorCodeDO.getUpdateTime())
                .build();
    }

    /**
     * 查询数据详情展现
     *
     * @param errorCodeDO
     * @return
     */
    public static ErrorCodeVO buildInfoQuery(ErrorCodeDO errorCodeDO) {
        return buildListQuery(errorCodeDO);
    }
}
