package com.xhqb.spectre.admin.util.excel;

import java.lang.reflect.Field;
import java.util.*;

public class DataUtils {

    public static <T> List<List<Object>> buildDataRows(List<T> dataList, List<MultiLevelHeader> headers) throws Exception {
        List<List<Object>> result = new ArrayList<>();

        for (T data : dataList) {
            List<Object> row = new ArrayList<>();
            for (MultiLevelHeader header : headers) {
                Field field = data.getClass().getDeclaredField(header.getField());
                field.setAccessible(true);

                if (header.isMultiLevel()) {
                    List<?> list = (List<?>) field.get(data);
                    for (Object value : list) {
                        row.add(value);
                    }
                } else {
                    row.add(field.get(data));
                }
            }
            result.add(row);
        }

        return result;
    }
}