package com.xhqb.spectre.admin.batchtask.detect;

import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.model.vo.batchtask.CheckResultVO;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.mapper.BatchDetectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 更新群发检测结果数据
 *
 * <AUTHOR>
 * @date 2022/2/22
 */
@Component
@Slf4j
public class BatchDetectUpdateHandler {

    @Autowired
    private VenusConfig venusConfig;
    @Resource
    private BatchDetectMapper batchDetectMapper;

    /**
     * 更新群发任务检测结果taskId
     *
     * @param checkResultList
     * @param taskId
     */
    public void update(List<CheckResultVO> checkResultList, int taskId) {
        long start = System.currentTimeMillis();
        try {
            this.doUpdateHandler(checkResultList, taskId);
        } catch (Exception e) {
            // 更新群发检测结果失败时
            // 不影响群发保存主流程
            log.error("更新群发检测结果taskId失败,taskId = {}", taskId, e);
        }
        log.info("更新群发检测结果taskId耗时 = {}, taskId = {}", (System.currentTimeMillis() - start), taskId);
    }

    /**
     * 真正做更新群发任务检测结果taskId逻辑
     *
     * @param checkResultList
     * @param taskId
     */
    private void doUpdateHandler(List<CheckResultVO> checkResultList, int taskId) {
        if (CommonUtil.isEmpty(checkResultList)) {
            return;
        }

        List<Integer> detectIdList;
        for (CheckResultVO checkResultVO : checkResultList) {
            detectIdList = batchDetectMapper.selectIdByFileMd5(checkResultVO.getFileMd5());
            this.updateDetectTaskId(detectIdList, taskId);
        }
    }


    /**
     * 更新检测文件结果taskId信息
     *
     * @param detectIdList
     * @param taskId
     */
    private void updateDetectTaskId(List<Integer> detectIdList, int taskId) {
        if (CommonUtil.isEmpty(detectIdList)) {
            return;
        }
        // 获取到完件数据信息
        int BATCH_UPDATE_COUNT = venusConfig.getBatchUpdateTaskParamCount();
        // 大数量分批次批量保存
        int paramSize = detectIdList.size();
        if (paramSize <= BATCH_UPDATE_COUNT) {
            batchDetectMapper.updateTaskIdByIdList(detectIdList, taskId);
            return;
        }

        int segment = paramSize / BATCH_UPDATE_COUNT;
        List<Integer> subList;

        for (int i = 0; i < segment; i++) {
            subList = detectIdList.subList(i * BATCH_UPDATE_COUNT, (i + 1) * BATCH_UPDATE_COUNT);
            batchDetectMapper.updateTaskIdByIdList(subList, taskId);
        }

        if (paramSize % BATCH_UPDATE_COUNT != 0) {
            subList = detectIdList.subList((paramSize / BATCH_UPDATE_COUNT) * BATCH_UPDATE_COUNT, paramSize);
            batchDetectMapper.updateTaskIdByIdList(subList, taskId);
        }

    }

}
