package com.xhqb.spectre.admin.bidata.entity;

import lombok.Data;

/**
 * 供应商纵向触达统计
 */
@Data
public class SubReachStatDO {
    /**
     * 统计日期
     * 月份
     */
    private String statDate;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 签名
     */
    private String signName;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 触达量
     */
    private Integer reachCount;

    /**
     * 计费量
     */
    private Integer reachBillCount;

    /**
     * 触达率
     */
    private Double reachRate;

    /**
     * 费用
     */
    private Integer priceCount;

}
