package com.xhqb.spectre.admin.model.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2022/4/11 11:04
 * @Description:
 */
@Data
public class ChannelGroupItemDTO implements Serializable {

    private static final long serialVersionUID = -5542717889898663564L;

    @NotNull(message = "渠道账号ID不能为空")
    private Integer channelAccountId;

    @NotEmpty(message = "运营商不能为空")
    private List<String> ispList;

    @NotNull(message = "地域过滤类型不能为空")
    @Range(min = 1, max = 2, message = "地域过滤类型应为包含或不包含")
    private Integer areaFilterType;

    @NotEmpty(message = "地域不能为空")
    private List<AreaDTO> areaList;

    @NotNull(message = "权重不能为空")
    @Min(value = 0, message = "权重不能小于0")
    @Max(value = 100, message = "权重不能大于100")
    private Integer weight;

    @Size(max = 256, message = "备注最大为{max}个字符")
    private String remark;
}
