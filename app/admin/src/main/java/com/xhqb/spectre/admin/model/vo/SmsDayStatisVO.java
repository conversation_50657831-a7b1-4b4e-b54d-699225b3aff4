package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.SmsDayStatisDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 短信发送量日概况
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsDayStatisVO implements Serializable {

    /**
     * 统计日期 yyyy-MM-dd
     */
    private String date;
    /**
     * 类型编码
     */
    private String code;
    /**
     * 日发送量
     */
    private Long sendTotal;
    /**
     * 日计费量
     */
    private Long billingTotal;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 查询列表数据展现
     *
     * @param smsDayStatisDO
     * @return
     */
    public static SmsDayStatisVO buildListQuery(SmsDayStatisDO smsDayStatisDO) {
        return SmsDayStatisVO.builder()
                // 统计日期
                .date(DateUtil.smallToStr(smsDayStatisDO.getDate()))
                // 类型编码
                .code(smsDayStatisDO.getCode())
                // 日发送量
                .sendTotal(smsDayStatisDO.getSendTotal())
                // 日计费量
                .billingTotal(smsDayStatisDO.getBillingTotal())
                // 创建时间
                .createTime(smsDayStatisDO.getCreateTime())
                // 发送时间
                .updateTime(smsDayStatisDO.getUpdateTime())
                .build();
    }
}
