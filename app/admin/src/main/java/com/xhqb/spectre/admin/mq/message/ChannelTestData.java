package com.xhqb.spectre.admin.mq.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelTestData {

    /**
     * 测试模板 id
     */
    private Long id;

    /**
     * 短信模版id
     */
    private Integer smsTplId;

    /**
     * 短信模版编码
     */
    private String smsTplCode;

    /**
     * 签名ID
     */
    private Integer signId;

    /**
     * 签名code
     */
    private String signCode;

    /**
     * 签名名称
     */
    private String signName;

    /**
     * 最大测试次数
     */
    private Integer maxTimes;

    /**
     * 测试时间范围
     */
    private String timePeriod;

    /**
     * 发送量级
     */
    private Integer senderLevel;
}
