package com.xhqb.spectre.admin.batchtask.parse;

import com.google.common.collect.Lists;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.io.UploadFileUtils;
import com.xhqb.spectre.admin.exception.BizException;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 内容解析结果
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
@Data
public class ParseResult {

    /**
     * 有效的文件类型
     */
    private static final List<String> VALID_CONTENT_TYPE_LIST = Lists.newArrayList(BatchTaskConstants.DataType.MOBILE,
            BatchTaskConstants.DataType.CID);

    /**
     * 文件存储地址
     */
    private String saveUrl;
    /**
     * 文件Md5值
     */
    private String fileMd5;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件后缀名
     */
    private String fileType;

    /**
     * 列表头
     */
    private List<String> titleList;

    /**
     * ocid 、mobile
     */
    private String contentType;
    /**
     * 内容
     */
    private List<ContentItem> contentItemList;

    /**
     * 签名名称
     */
    private String signName;

    /**
     * 手机状态验证列表 (空号,停机)
     */
    private List<String> phoneStatusList;

    /**
     * 添加解析的内容信息
     *
     * @param contentItem
     * @return
     */
    public ParseResult addContentItem(ContentItem contentItem) {
        if (Objects.isNull(contentItemList)) {
            contentItemList = new ArrayList<>();
        }
        contentItemList.add(contentItem);
        return this;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
        if (!VALID_CONTENT_TYPE_LIST.contains(contentType)) {
            throw new BizException("文件内容格式不正确");
        }
    }

    /**
     * 复制数据
     * 1. setFileName
     * 2. setFileType
     * 3. setSignName
     * 4. setPhoneStatusList
     *
     * @return
     */
    public ParseResult copy() {
        ParseResult parseResult = new ParseResult();

        parseResult.setFileName(this.getFileName());
        parseResult.setFileType(UploadFileUtils.getFileSuffix(this.getFileName()));
        // 设置签名
        parseResult.setSignName(this.getSignName());
        // 设置手机状态校验列表标记
        parseResult.setPhoneStatusList(this.getPhoneStatusList());
        return parseResult;
    }
}
