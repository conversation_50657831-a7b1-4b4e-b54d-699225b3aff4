package com.xhqb.spectre.admin.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.service.AppDailyStatsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 请求统计入库
 */
@Component
@Job("appDailyStatsJob")
@Slf4j
public class AppDailyStatsJob implements SimpleJob {

    @Resource
    private AppDailyStatsService appDailyStatsService;

    @Override
    public void execute(ShardingContext shardingContext) {
        long start = System.currentTimeMillis();
        log.info("开始统计落库任务");
        appDailyStatsService.saveDailyStats();
        log.info("处理统计落库任务完成,耗时 ={}", (System.currentTimeMillis() - start));
    }
}
