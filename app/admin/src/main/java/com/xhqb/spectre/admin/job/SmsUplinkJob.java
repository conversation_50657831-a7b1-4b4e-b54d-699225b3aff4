package com.xhqb.spectre.admin.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.cif.entity.CifCustomerBaseDO;
import com.xhqb.spectre.admin.cif.entity.CifEnterpriseCustomerDO;
import com.xhqb.spectre.admin.service.CifCustomerBaseService;
import com.xhqb.spectre.admin.service.CifEnterpriseCustomerService;
import com.xhqb.spectre.admin.service.SmsUplinkService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.SmsUplinkDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 上行短信JOB
 * <p>
 * 主要是填充上行短信的CID信息
 *
 * <AUTHOR>
 * @date 2022/6/8
 */
@Component
@Job("smsUplinkJob")
@Slf4j
public class SmsUplinkJob implements SimpleJob {
    private static final int PAGE_SIZE = 200;
    @Resource
    private SmsUplinkService smsUplinkService;
    @Resource
    private CifCustomerBaseService cifCustomerBaseService;
    @Resource
    private CifEnterpriseCustomerService cifEnterpriseCustomerService;


    @Override
    public void execute(ShardingContext shardingContext) {
        long start = System.currentTimeMillis();
        log.info("开始刷新上行短信CID信息");
        try {
            this.scan();
        } catch (Exception e) {
            log.warn("刷新上行短信CID信息失败", e);
        }
        log.info("刷新上行短信CID信息结束,耗时 = {}", (System.currentTimeMillis() - start));
    }

    private void scan() {
        Long lastId = 0L;
        do {
            lastId = this.doScan(lastId);
        } while (lastId != null);
    }

    private Long doScan(Long lastId) {
        List<SmsUplinkDO> smsUplinkList = smsUplinkService.scanUplink(lastId, PAGE_SIZE);
        if (CommonUtil.isEmpty(smsUplinkList)) {
            return null;
        }

        // 最后一条记录的id
        long id = smsUplinkList.get(smsUplinkList.size() - 1).getId();

        Map<String, List<SmsUplinkDO>> mobileMapping = smsUplinkList.stream()
                .filter(s -> StringUtils.isNotBlank(s.getMobile()))
                .collect(Collectors.groupingBy(SmsUplinkDO::getMobile));

        // 原始手机列表数量
        int mobileSize = mobileMapping.keySet().size();
        // 手机列表
        Set<String> mobileList = new HashSet<>(mobileMapping.keySet());
        if (CommonUtil.isEmpty(mobileList)) {
            return id;
        }

        // key -> mobile, value -> cid
        Map<String, String> mobileAndCid = this.selectCidByCustomerBase(mobileList);
        if (mobileAndCid.size() < mobileSize) {
            // 有些cid信息未查询到 则继续使用t_enterprise_customer_base进行数据查询
            this.selectCidByEnterpriseCustomer(mobileList, mobileAndCid);
        }

        // 处理手机号码映射信息
        this.processMobileMapping(mobileMapping, mobileAndCid);

        return id;
    }

    /**
     * 处理手机号码映射信息
     *
     * @param mobileMapping
     * @param mobileAndCid
     */
    private void processMobileMapping(Map<String, List<SmsUplinkDO>> mobileMapping, Map<String, String> mobileAndCid) {
        Set<Map.Entry<String, List<SmsUplinkDO>>> entries = mobileMapping.entrySet();
        String mobilePhone;
        String cid;
        List<SmsUplinkDO> smsUplinkList;
        for (Map.Entry<String, List<SmsUplinkDO>> entry : entries) {
            mobilePhone = entry.getKey();
            cid = mobileAndCid.get(mobilePhone);
            if (StringUtils.isBlank(cid)) {
                // 未匹配到cid信息
                continue;
            }
            smsUplinkList = entry.getValue();
            this.doUpdateCid(smsUplinkList, cid);
        }
    }

    /**
     * 更新上行短信cid信息
     *
     * @param smsUplinkList
     * @param cid
     */
    private void doUpdateCid(List<SmsUplinkDO> smsUplinkList, String cid) {
        if (CommonUtil.isEmpty(smsUplinkList)) {
            return;
        }
        SmsUplinkDO dbDO;
        for (SmsUplinkDO smsUplink : smsUplinkList) {
            dbDO = this.buildDO(smsUplink.getId(), cid);
            smsUplinkService.updateByPrimaryKeySelective(dbDO);
        }
    }

    private SmsUplinkDO buildDO(Long id, String cid) {
        SmsUplinkDO smsUplinkDO = new SmsUplinkDO();
        smsUplinkDO.setId(id);
        smsUplinkDO.setCid(cid);
        return smsUplinkDO;
    }

    /**
     * 查询基础用户信息
     *
     * @param mobileList
     * @return 返回手机号码与cid映射关系
     */
    private Map<String, String> selectCidByCustomerBase(Set<String> mobileList) {
        List<CifCustomerBaseDO> cifCustomerBaseList = cifCustomerBaseService.selectByMobileList(mobileList);
        if (CommonUtil.isEmpty(cifCustomerBaseList)) {
            return new HashMap<>(16);
        }

        // key -> mobile, value -> cid
        Map<String, String> mobileAndCid = new HashMap<>(calcMapSize(mobileList.size()));
        String mobilePhone;
        for (CifCustomerBaseDO cifCustomerBase : cifCustomerBaseList) {
            mobilePhone = cifCustomerBase.getMobilePhone();
            mobileList.remove(mobilePhone);
            mobileAndCid.put(mobilePhone, cifCustomerBase.getId());
        }

        return mobileAndCid;
    }

    /**
     * 查询企业用户
     *
     * @param mobileList
     * @param mobileAndCid
     */
    private void selectCidByEnterpriseCustomer(Set<String> mobileList, Map<String, String> mobileAndCid) {
        List<CifEnterpriseCustomerDO> cifEnterpriseCustomerList = cifEnterpriseCustomerService.selectByMobileList(mobileList);
        if (CommonUtil.isEmpty(cifEnterpriseCustomerList)) {
            return;
        }

        String mobilePhone;
        for (CifEnterpriseCustomerDO cifEnterpriseCustomer : cifEnterpriseCustomerList) {
            mobilePhone = cifEnterpriseCustomer.getMobilePhone();
            mobileList.remove(mobilePhone);
            mobileAndCid.put(mobilePhone, cifEnterpriseCustomer.getCid());
        }
    }

    private static int calcMapSize(int len) {
        return (int) (len / 0.75);
    }


}
