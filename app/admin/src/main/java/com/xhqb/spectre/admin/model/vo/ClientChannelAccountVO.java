package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.ClientChannelAccountDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 网关分配账号
 *
 * <AUTHOR>
 * @date 2021/10/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClientChannelAccountVO implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 服务编码
     */
    private String serviceId;

    /**
     * 账号
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 连接状态 0->未连接 1->已连接
     */
    private Byte isChannelConnect;

    /**
     * 是否有效 0->无效 1->有效
     */
    private Byte isValid;

    /**
     * cmpp协议版本
     */
    private Integer version;

    /**
     * 最大连接数
     */
    private Integer maxConnect;

    /**
     * 写限制
     */
    private Integer writeLimit;

    /**
     * 读限制
     */
    private Integer readLimit;

    /**
     * 白名单 多个ip使用逗号分割
     */
    private String ipList;

    /**
     * 是否开启白名单检测 0->不检测 1->检测
     */
    private Byte isIpCheck;

    /**
     * 组名称
     */
    private String groupName;

    /**
     * 空闲时间(秒)
     */
    private Integer idleTimeSec;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 是否删除 删除标志 0->未删除 1->已删除
     */
    private Byte isDelete;


    /**
     * 查询列表数据展现
     *
     * @param clientChannelAccountDO
     * @return
     */
    public static ClientChannelAccountVO buildListQuery(ClientChannelAccountDO clientChannelAccountDO) {
        return ClientChannelAccountVO.builder()
                // 主键ID
                .id(clientChannelAccountDO.getId())
                // 服务编码
                .serviceId(clientChannelAccountDO.getServiceId())
                // 账号
                .username(clientChannelAccountDO.getUsername())
                // 密码
                .password(clientChannelAccountDO.getPassword())
                // 连接状态 0->未连接 1->已连接
                .isChannelConnect(clientChannelAccountDO.getIsChannelConnect())
                // 是否有效 0->无效 1->有效
                .isValid(clientChannelAccountDO.getIsValid())
                // cmpp协议版本
                .version(clientChannelAccountDO.getVersion())
                // 最大连接数
                .maxConnect(clientChannelAccountDO.getMaxConnect())
                // 写限制
                .writeLimit(clientChannelAccountDO.getWriteLimit())
                // 读限制
                .readLimit(clientChannelAccountDO.getReadLimit())
                // 白名单 多个ip使用逗号分割
                .ipList(clientChannelAccountDO.getIpList())
                // 是否开启白名单检测 0->不检测 1->检测
                .isIpCheck(clientChannelAccountDO.getIsIpCheck())
                // 组名称
                .groupName(clientChannelAccountDO.getGroupName())
                // 空闲时间(秒)
                .idleTimeSec(clientChannelAccountDO.getIdleTimeSec())
                // 创建时间
                .createTime(clientChannelAccountDO.getCreateTime())
                // 创建人
                .creator(clientChannelAccountDO.getCreator())
                // 更新时间
                .updateTime(clientChannelAccountDO.getUpdateTime())
                // 更新人
                .updater(clientChannelAccountDO.getUpdater())
                // 是否删除 删除标志 0->未删除 1->已删除
                .isDelete(clientChannelAccountDO.getIsDelete())
                .build();
    }

    /**
     * 查询数据详情展现
     *
     * @param clientChannelAccountVO
     * @return
     */
    public static ClientChannelAccountVO buildInfoQuery(ClientChannelAccountDO clientChannelAccountVO) {
        return buildListQuery(clientChannelAccountVO);
    }

}
