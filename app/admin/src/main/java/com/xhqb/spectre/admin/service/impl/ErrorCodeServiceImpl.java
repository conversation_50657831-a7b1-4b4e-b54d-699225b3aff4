package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.ErrorCodeDTO;
import com.xhqb.spectre.admin.model.vo.ErrorCodeVO;
import com.xhqb.spectre.admin.service.ErrorCodeService;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.ErrorCodeDO;
import com.xhqb.spectre.common.dal.mapper.ErrorCodeMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ErrorCodeQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 错误码
 *
 * <AUTHOR>
 * @date 2021/10/20
 */
@Service
@Slf4j
public class ErrorCodeServiceImpl implements ErrorCodeService {

    @Resource
    private ErrorCodeMapper errorCodeMapper;

    /**
     * 分页查询错误码列表
     *
     * @param errorCodeQuery
     * @return
     */
    @Override
    public CommonPager<ErrorCodeVO> listByPage(ErrorCodeQuery errorCodeQuery) {
        return PageResultUtils.result(
                () -> errorCodeMapper.countByQuery(errorCodeQuery),
                () -> errorCodeMapper.selectByQuery(errorCodeQuery).stream().map(ErrorCodeVO::buildListQuery).collect(Collectors.toList())
        );
    }

    /**
     * 根据主键查询错误码信息
     *
     * @param type      错误码类型 submit->短信发送 deliver->短信回执
     * @param xhErrCode 错误码编码
     * @return
     */
    @Override
    public ErrorCodeVO getByPrimaryKey(String type, Integer xhErrCode) {
        ErrorCodeDO errorCodeDO = errorCodeMapper.selectByPrimaryKey(type, xhErrCode);
        if (Objects.isNull(errorCodeDO)) {
            throw new BizException("未找到该错误码信息");
        }
        return ErrorCodeVO.buildInfoQuery(errorCodeDO);
    }

    /**
     * 添加错误码信息
     *
     * @param errorCodeDTO
     */
    @Override
    public void create(ErrorCodeDTO errorCodeDTO) {
        //参数格式校验
        ValidatorUtil.validate(errorCodeDTO);
        ErrorCodeDO errorCodeDO = errorCodeMapper.selectByPrimaryKey(errorCodeDTO.getType(), errorCodeDTO.getXhErrCode());
        if (Objects.nonNull(errorCodeDO)) {
            log.error("添加错误码失败,错误码已经存在 = {}", JSON.toJSONString(errorCodeDTO));
            throw new BizException("错误码已经存在");
        }

        // 获取当前类型的最大错误码
        Integer maxXhErrorCode = errorCodeMapper.selectMaxCodeByType(errorCodeDTO.getType());
        if (Objects.isNull(maxXhErrorCode)) {
            maxXhErrorCode = 0;
        }
        errorCodeDO = buildErrorCodeDO(errorCodeDTO);
        errorCodeDO.setXhErrCode(maxXhErrorCode + 1);
        errorCodeMapper.insertSelective(errorCodeDO);
    }

    /**
     * 更新错误码信息
     *
     * @param errorCodeDTO
     */
    @Override
    public void update(ErrorCodeDTO errorCodeDTO) {
        //参数格式校验
        ValidatorUtil.validate(errorCodeDTO);
        ErrorCodeDO errorCodeDO = errorCodeMapper.selectByPrimaryKey(errorCodeDTO.getType(), errorCodeDTO.getXhErrCode());
        if (Objects.isNull(errorCodeDO)) {
            log.error("更新错误码失败,错误码不存在 = {}", JSON.toJSONString(errorCodeDTO));
            throw new BizException("错误码不存在");
        }
        errorCodeDO = buildErrorCodeDO(errorCodeDTO);
        errorCodeMapper.updateByPrimaryKeySelective(errorCodeDO);
    }

    /**
     * 构建错误码
     *
     * @param errorCodeDTO
     * @return
     */
    private ErrorCodeDO buildErrorCodeDO(ErrorCodeDTO errorCodeDTO) {
        ErrorCodeDO errorCodeDO = new ErrorCodeDO();
        errorCodeDO.setType(errorCodeDTO.getType());
        errorCodeDO.setXhErrCode(errorCodeDTO.getXhErrCode());
        errorCodeDO.setRetry(errorCodeDTO.getRetry());
        errorCodeDO.setCodeDesc(errorCodeDTO.getCodeDesc());
        return errorCodeDO;
    }

}
