package com.xhqb.spectre.admin.batchtask.send.verify;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.batchtask.send.MessageRequest;
import com.xhqb.spectre.admin.batchtask.send.single.SingleMessageRequest;

/**
 * 验证码请求
 * <p>
 * {"tplCode":"KF_YZM_200076","phoneNumbers":"15026552734"}
 *
 * <AUTHOR>
 * @date 2021/10/14
 */
public class VerifyCodeRequest extends MessageRequest {


    public VerifyCodeRequest(SingleMessageRequest singleMessageRequest) {
        // 业务应用ID
        super.setAppCode(singleMessageRequest.getAppCode());
        //业用加签秘钥
        super.setAppSecret(singleMessageRequest.getAppSecret());
        // tplCode 模版编号
        super.setTplCode(singleMessageRequest.getTplCode());
        // SMS Sign 短信签名
        super.setSignName(singleMessageRequest.getSignName());
        // 短信发送号码组，以逗号分开
        super.setPhoneNumbers(singleMessageRequest.getPhoneNumbers());
        // 渠道id，如果设置了该参数就不读取模版关联的渠道信息
        super.setChannelAccountId(singleMessageRequest.getChannelAccountId());
        // 设置群发ID
        super.setBatchId(singleMessageRequest.getBatchId());
        // 设置签名编码
        super.setSignCode(singleMessageRequest.getSignCode());
        // 验证码设置requestId
        super.setRequestId(singleMessageRequest.getRequestId());
    }

    /**
     * 将对象转换成请求JSON
     *
     * @return
     */
    public String toJSONString() {
        return JSON.toJSONString(this);
    }

}
