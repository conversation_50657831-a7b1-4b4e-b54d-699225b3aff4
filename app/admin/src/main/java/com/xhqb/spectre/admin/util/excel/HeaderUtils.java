package com.xhqb.spectre.admin.util.excel;

import java.util.ArrayList;
import java.util.List;

public class HeaderUtils {

    public static List<List<String>> buildMultiLevelHeaders(List<MultiLevelHeader> headers) {
        List<List<String>> result = new ArrayList<>();

        for (MultiLevelHeader header : headers) {
            if (header.isMultiLevel()) {
                for (String subTitle : header.getSubTitles()) {
                    List<String> colHeader = new ArrayList<>();
                    colHeader.add(header.getMainTitle()); // 一级标题
                    colHeader.add(subTitle);              // 二级标题
                    result.add(colHeader);
                }
            } else {
                List<String> colHeader = new ArrayList<>();
                colHeader.add(header.getMainTitle()); // 一级标题
                colHeader.add("");                    // 没有二级标题
                result.add(colHeader);
            }
        }

        return result;
    }
}
