package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.CodeMappingDTO;
import com.xhqb.spectre.admin.model.vo.CodeMappingVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.CodeMappingQuery;

/**
 * 映射码
 *
 * <AUTHOR>
 * @date 2021/10/20
 */
public interface CodeMappingService {

    /**
     * 分页查询映射码列表
     *
     * @param codeMappingQuery
     * @return
     */
    CommonPager<CodeMappingVO> listByPage(CodeMappingQuery codeMappingQuery);

    /**
     * 根据主键查询映射码信息
     *
     * @param channelCode
     * @param type
     * @param channelErrCode
     * @return
     */
    CodeMappingVO getByPrimaryKey(String channelCode, String type, String channelErrCode);

    /**
     * 添加映射码信息
     *
     * @param codeMappingDTO
     */
    void create(CodeMappingDTO codeMappingDTO);

    /**
     * 更新映射码信息
     *
     * @param codeMappingDTO
     */
    void update(CodeMappingDTO codeMappingDTO);

}
