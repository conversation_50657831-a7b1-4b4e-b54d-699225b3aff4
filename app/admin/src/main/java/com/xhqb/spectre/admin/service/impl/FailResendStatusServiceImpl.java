package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.service.FailResendStatusService;
import com.xhqb.spectre.common.dal.entity.FailResendRecordDO;
import com.xhqb.spectre.common.dal.mapper.FailResendRecordMapper;
import com.xhqb.spectre.common.enums.FailResendStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 补发状态管理服务
 */
@Service
@Slf4j
public class FailResendStatusServiceImpl implements FailResendStatusService {
    
    @Resource
    private FailResendRecordMapper failResendRecordMapper;
    
    @Override
    @Transactional
    public boolean updateStatus(Long recordId, FailResendStatusEnum newStatus) {
        try {
            Date updateTime = new Date();
            int result = failResendRecordMapper.updateStatusById(recordId, newStatus.getCode(), updateTime);

            if (result > 0) {
                log.debug("补发记录状态更新成功, recordId: {}, newStatus: {}", recordId, newStatus);
                return true;
            } else {
                log.warn("补发记录状态更新失败, recordId: {}, newStatus: {}", recordId, newStatus);
                return false;
            }

        } catch (Exception e) {
            log.error("更新补发记录状态异常, recordId: {}, newStatus: {}", recordId, newStatus, e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean updateStatusWithCheck(Long recordId, FailResendStatusEnum expectedCurrentStatus, FailResendStatusEnum newStatus) {
        try {
            if (!FailResendStatusEnum.isValidTransition(expectedCurrentStatus, newStatus)) {
                log.warn("补发记录状态转换不合法, recordId: {}, from: {}, to: {}",
                        recordId, expectedCurrentStatus, newStatus);
                return false;
            }

            Date updateTime = new Date();
            int result = failResendRecordMapper.updateStatusByIdWithCondition(
                    recordId, expectedCurrentStatus.getCode(), newStatus.getCode(), updateTime);

            if (result > 0) {
                log.debug("带校验的状态更新成功, recordId: {}, from: {}, to: {}",
                        recordId, expectedCurrentStatus, newStatus);
                return true;
            } else {
                log.warn("带校验的状态更新失败, recordId: {}, expected: {}, newStatus: {} (记录不存在或状态不匹配)",
                        recordId, expectedCurrentStatus, newStatus);
                return false;
            }

        } catch (Exception e) {
            log.error("带校验的状态更新异常, recordId: {}, expected: {}, newStatus: {}",
                    recordId, expectedCurrentStatus, newStatus, e);
            return false;
        }
    }
    
    @Override
    public boolean startProcessing(Long recordId) {
        log.debug("开始补发处理, recordId: {}", recordId);
        return updateStatusWithCheck(recordId, FailResendStatusEnum.PENDING, FailResendStatusEnum.PROCESSING);
    }
    
    @Override
    public boolean markSuccess(Long recordId) {
        log.debug("标记补发成功, recordId: {}", recordId);
        return updateStatusWithCheck(recordId, FailResendStatusEnum.PROCESSING, FailResendStatusEnum.SUCCESS);
    }
    
    @Override
    public boolean markFailed(Long recordId) {
        try {
            Date updateTime = new Date();
            int result = failResendRecordMapper.updateStatusById(
                    recordId, FailResendStatusEnum.FAILED.getCode(), updateTime);

            if (result > 0) {
                log.debug("标记补发失败成功, recordId: {}", recordId);
                return true;
            } else {
                log.warn("标记补发失败失败, recordId: {}", recordId);
                return false;
            }

        } catch (Exception e) {
            log.error("标记补发失败异常, recordId: {}", recordId, e);
            return false;
        }
    }
    
    @Override
    public FailResendStatusEnum getStatus(Long recordId) {
        try {
            FailResendRecordDO record = failResendRecordMapper.selectByPrimaryKey(recordId);
            if (record == null) {
                log.warn("补发记录不存在, recordId: {}", recordId);
                return null;
            }

            return FailResendStatusEnum.getByCode(record.getStatus());

        } catch (Exception e) {
            log.error("获取补发记录状态异常, recordId: {}", recordId, e);
            return null;
        }
    }

}
