package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.BlackSourceDO;
import com.xhqb.spectre.common.dal.entity.SignDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlackSourceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String name;

    private String description;

    private String code;

    private Integer status;

    private String createTime;

    private String creator;

    private String updateTime;

    private String updater;

    public static BlackSourceVO buildBlackSourceVO(BlackSourceDO blackSourceDO) {
        return BlackSourceVO.builder()
                .id(blackSourceDO.getId())
                .name(blackSourceDO.getName())
                .description(blackSourceDO.getDescription())
                .code(blackSourceDO.getCode())
                .status(blackSourceDO.getStatus())
                .createTime(DateUtil.dateToString(blackSourceDO.getCreateTime()))
                .creator(blackSourceDO.getCreator())
                .updateTime(DateUtil.dateToString(blackSourceDO.getUpdateTime()))
                .updater(blackSourceDO.getUpdater())
                .build();
    }
}
