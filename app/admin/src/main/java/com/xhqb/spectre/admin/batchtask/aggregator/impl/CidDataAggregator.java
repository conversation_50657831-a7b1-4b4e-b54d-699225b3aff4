package com.xhqb.spectre.admin.batchtask.aggregator.impl;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xhqb.spectre.admin.batchtask.aggregator.*;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.parse.ContentItem;
import com.xhqb.spectre.admin.batchtask.validate.ValidateContext;
import com.xhqb.spectre.admin.batchtask.validate.ValidateResult;
import com.xhqb.spectre.admin.model.vo.batchtask.CustomerVO;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * CID数据聚合
 * <p>
 * 名称位置 ，cid文件上传时需要将名称信息替换成t_customer_base表中的用户名 为空则不包含name关键属性
 *
 * <AUTHOR>
 * @date 2021/9/28
 */
@Component
@Slf4j
public class CidDataAggregator extends AbstractDataAggregator implements AsyncTask.AsyncTaskHandler {

    /**
     * CID聚合处理
     */
    private static final ExecutorService CID_AGGREGATOR_POOL = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 2,
            100,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("cid-aggregator-pool-%d").build()
    );

    /**
     * 数据分割长度
     */
    private static final int SLICE_LENGTH = 900;

    @Resource
    private DataHandlerFactory dataHandlerFactory;


    /**
     * 返回聚合结果
     *
     * @param sourceDataList
     * @param validateContext
     * @param validateResult
     * @return
     */
    @Override
    protected AggregatorResult doAggregate(List<ContentItem> sourceDataList, ValidateContext validateContext, ValidateResult validateResult) {
        long start = System.currentTimeMillis();
        // 原始数据数量
        int sourceSize = sourceDataList.size();
        AggregatorResult aggregatorResult;
        if (sourceSize <= SLICE_LENGTH) {
            // 数据量小于切分数量 不需要异步处理
            aggregatorResult = new AggregatorResult();
            this.asyncTaskHandler(sourceDataList, validateContext, validateResult, aggregatorResult);
            log.info("CID数据聚合同步耗时 = {}, fileName = {}, fileMd5 = {}", (System.currentTimeMillis() - start), validateContext.getFileName(), validateContext.getFileMd5());
            return aggregatorResult;
        }

        // 切割片段
        int segment = sourceSize / SLICE_LENGTH;
        // 总共做任务的数量
        int latchCount = sourceSize % SLICE_LENGTH != 0 ? segment + 1 : segment;
        CountDownLatch latch = new CountDownLatch(latchCount);
        List<AggregatorResult> aggregatorResultList = new ArrayList<>(segment + 1);
        for (int i = 0; i < segment; i++) {
            List<ContentItem> contentItemList = sourceDataList.subList(i * SLICE_LENGTH, (i + 1) * SLICE_LENGTH);
            aggregatorResult = new AggregatorResult();
            aggregatorResultList.add(aggregatorResult);
            CID_AGGREGATOR_POOL.execute(new AsyncTask(latch, contentItemList, validateContext, validateResult, aggregatorResult, this));
        }

        if (sourceSize % SLICE_LENGTH != 0) {
            List<ContentItem> contentItemList = sourceDataList.subList((sourceSize / SLICE_LENGTH) * SLICE_LENGTH, sourceSize);
            aggregatorResult = new AggregatorResult();
            aggregatorResultList.add(aggregatorResult);
            CID_AGGREGATOR_POOL.execute(new AsyncTask(latch, contentItemList, validateContext, validateResult, aggregatorResult, this));
        }

        try {
            latch.await();
        } catch (Exception e) {
            throw new RuntimeException("CID数据聚合处理失败");
        }
        // 做聚合操作
        aggregatorResult = AggregatorResult.toAggregatorResult(aggregatorResultList, sourceSize);
        log.info("CID数据聚合异步耗时 = {}, fileName = {}, fileMd5 = {}", (System.currentTimeMillis() - start), validateContext.getFileName(), validateContext.getFileMd5());
        return aggregatorResult;
    }

    /**
     * 异步任务处理方法
     *
     * @param contentItemList
     * @param validateContext
     * @param validateResult
     * @param aggregatorResult
     */
    @Override
    public void asyncTaskHandler(List<ContentItem> contentItemList, ValidateContext validateContext, ValidateResult validateResult, AggregatorResult aggregatorResult) {
        // 名称位置 ，cid文件上传时需要将名称信息替换成t_customer_base表中的用户名 为空则不包含name关键属性
        Integer namePos = super.getTitlePos(BatchTaskConstants.Commons.NAME_TITLE, validateContext);
        // 转换cid查询条件列表
        List<String> cidList = contentItemList.stream().map(s -> s.getContent()).collect(Collectors.toList());
        // 当前签名名称
        String signName = validateContext.getSignName();
        CustomerDataHandler customerDataHandler = dataHandlerFactory.getCustomerDataHandler(signName);
        log.info("聚合器使用 customerDataHandler = {},signName = {}", customerDataHandler.getClass(), signName);
        final String smsTypeCode = validateContext.getSmsTypeCode();
        // cid查询结果
        Map<String, CustomerVO> customerVOMap = customerDataHandler.query(cidList, smsTypeCode);
        if (Objects.isNull(customerVOMap) || customerVOMap.isEmpty()) {
            // 所有数据无效
            aggregatorResult.setBadList(contentItemList);
            return;
        }

        // 对比查询出来的数据与原始数据一致 那么所有数据有效
//        if (contentItemList.size() == customerVOMap.size()) {
//            // 所有数据有效
//            aggregatorResult.setValidateList(contentItemList);
//            return;
//        }

        // 存在无效数据 则进行数据处理
        List<ContentItem> badList = new ArrayList<>(contentItemList.size());
        // 存在的有效数据
        List<ContentItem> validList = contentItemList.stream().filter(s -> {
            CustomerVO customerVO = customerVOMap.get(s.getContent());
            if (Objects.isNull(customerVO) || StringUtils.isBlank(customerVO.getMobile())) {
                // 无效数据
                badList.add(s);
                return false;
            }

            // 完件信息为空时 构造空完件空字符串
            String applyLoanResult = customerVO.getApplyLoanResult();
            if (StringUtils.isBlank(applyLoanResult)) {
                applyLoanResult = " ";
            }

            // 4. CID校验: 调整为仅对营销类短信有效，其他类型的短信不再校验CID状态。
            // 非营销类短信 也不再进行完件过滤 (2024-09-04)
            if (!MessageTypeEnum.isMarket(smsTypeCode)) {
                applyLoanResult = " ";
            }

            // 数据合法 则设置 cid、mobile+applyLoanResult映射关系
            validateResult.addMapping(s.getContent(), customerVO.getMobile() + BatchTaskConstants.Commons.SPLIT_FLAG + applyLoanResult);
            // 如果包含 name 关键字 则使用t_customer_base表中的用户名替换内容
            if (Objects.isNull(namePos)) {
                // namePos为空 没有包含name关键属性
                return true;
            }
            // 替换name内容
            s.getParamList().set(namePos, customerVO.getName());
            return true;
        }).collect(Collectors.toList());

        aggregatorResult.setBadList(badList);
        aggregatorResult.setValidateList(validList);
    }
}
