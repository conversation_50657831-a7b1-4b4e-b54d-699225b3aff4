package com.xhqb.spectre.admin.batchtask.result.impl;

import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.constant.RedisKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据已经读取完成时 删除缓存数据，释放redis内存
 * <p>
 * 删除分片索引和分片数据缓存信息
 *
 * <AUTHOR>
 * @date 2021/10/2
 */
@Component
@Slf4j
public class DeleteUploadResultHandler {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 删除分片索引和分片数据缓存信息
     *
     * @param taskNoList 批量任务编号集合
     * @return
     */
    @Async
    public void handler(List<String> taskNoList) {
        long start = System.currentTimeMillis();
        if (CommonUtil.isEmpty(taskNoList)) {
            return;
        }

        log.info("开始进行删除分片索引和分片数据缓存信息, taskNoList = {}", taskNoList);
        for (String taskNo : taskNoList) {
            try {
                stringRedisTemplate.delete(RedisKeys.BatchTaskKeys.BATCH_TASK_UPLOAD_RESULT_STR_KEY + ":" + taskNo);
            } catch (Exception e) {
                log.error("删除分片索引和分片数据缓存信息失败,taskNo = {}", taskNo, e);
            }
        }
        log.info("删除分片索引和分片数据缓存信息完成,耗时 = {}, taskNoList = {}", (System.currentTimeMillis() - start), taskNoList);
    }

}
