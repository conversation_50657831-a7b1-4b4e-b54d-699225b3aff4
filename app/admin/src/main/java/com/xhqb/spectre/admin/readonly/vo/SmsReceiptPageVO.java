package com.xhqb.spectre.admin.readonly.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/30 15:53
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsReceiptPageVO implements Serializable {

    private static final long serialVersionUID = 1621263776071111279L;

    /**
     * 发送记录列表
     */
    private List<SmsReceiptVO> dataList;

    /**
     * 总记录数
     */
    private Integer totalCount;

    /**
     * 是否返回总记录数
     */
    private boolean returnTotal;

    /**
     * 是否有下一页，returnTotal = false时使用
     */
    private boolean hasNextPage;
}
