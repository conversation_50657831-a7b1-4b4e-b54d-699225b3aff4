package com.xhqb.spectre.admin.batchtask.validate.impl;

import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.parse.ContentItem;
import com.xhqb.spectre.admin.batchtask.validate.ContentValidator;
import com.xhqb.spectre.admin.batchtask.validate.ValidateContext;
import com.xhqb.spectre.admin.batchtask.validate.ValidateResult;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 内容去重判断
 * <p>
 * 去重判断规则：
 * 根据第一个内容值进行去重过滤操作
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
@Component
@Slf4j
public class RepeatContentValidator implements ContentValidator {

    public static final String PHONE_STATUS = "重复过滤";

    /**
     * 是否需要进行重复校验开关,默认需要
     * true需要验证
     */
    @Value("${spectre.admin.repeatValidatorSwitch:true}")
    private boolean repeatValidatorSwitch;

    /**
     * 数据验证
     *
     * @param validateContext
     * @param validateResult
     */
    @Override
    public void validate(ValidateContext validateContext, ValidateResult validateResult) {

        List<String> list = validateContext.getPhoneStatusList();
        if (list == null || !list.contains(PHONE_STATUS)) {
            log.info("不检测重复数据, phoneStatus={}", list);
            return;
        }

        if (!repeatValidatorSwitch) {
            log.warn("校验重复数据开关已关闭");
            return;
        }
        // 重复的数据信息
        List<ContentItem> repeatList = new ArrayList<>();
        Set<String> repeatSets = new HashSet<>();
        final String smsTypeCode = validateContext.getSmsTypeCode();
        // 过滤之后有效的数据
        List<ContentItem> validateList = validateContext.getValidateList().stream().filter(s -> {
            if (!doRepeatValidate(s, repeatSets, smsTypeCode)) {
                // 重复数据
                repeatList.add(s);
                return false;
            }
            // 有效
            return true;
        }).collect(Collectors.toList());
        // 绑定结果数据信息
        // 设置重复参数数据
        validateResult.setRepeatList(repeatList);

        // 设置上下文待验证的数据
        validateContext.setValidateList(validateList);
    }

    /**
     * 根据CID或者Mobile做去重操作
     *
     * @param contentItem
     * @param repeatSets
     * @param smsTypeCode
     * @return
     */
    private boolean doRepeatValidate(ContentItem contentItem, Set<String> repeatSets, String smsTypeCode) {
        if (!repeatValidatorSwitch) {
            return true;
        }

        // 2. 号码重复判断规则(调整时间 2024-09-04)
        // 调整为两种重复判断规则; 规则1:原规则,只判断CID/手机号是否重复,规则2:CID/手机号+所有模板变量都相同,则认定为重复。
        // 营销类型规则不变, 使用规则1
        // 其他类型; 使用规则2
        // 简而言之,营销类短信验证contentItem.getContent()内容, 其他类型短信验证整个contentItem内容
        String content = contentItem.getContent();
        if (!MessageTypeEnum.isMarket(smsTypeCode)) {
            // 不是营销短信 使用 contentItem 作为整个content进行过滤判断
            content = contentItemToStr(contentItem);
        }
        if (repeatSets.contains(content)) {
            return false;
        }
        repeatSets.add(content);
        return true;
    }

    /**
     * contentItem 整个内容转换成string
     *
     * @param contentItem
     * @return
     */
    private String contentItemToStr(ContentItem contentItem) {
        String content = contentItem.getContent();
        List<String> paramList = contentItem.getParamList();
        if (!CollectionUtils.isEmpty(paramList)) {
            content += String.join(",", paramList);
        }
        return content;
    }

    @Override
    public int getOrder() {
        return BatchTaskConstants.ValidatorOrdered.REPEAT;
    }
}
