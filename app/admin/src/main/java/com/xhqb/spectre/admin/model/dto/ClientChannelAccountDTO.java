package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.ClientChannelAccountDO;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 网关账号
 *
 * <AUTHOR>
 * @date 2021/10/26
 */
@Data
public class ClientChannelAccountDTO implements Serializable {
    /**
     * 服务代码
     */
    @NotBlank(message = "服务代码不能为空")
    private String serviceId;
    /**
     * 账号
     */
    @NotBlank(message = "账号不能为空")
    private String username;
    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;
    /**
     * 连接状态 0->未连接 1->已连接
     */
    private Byte isChannelConnect;
    /**
     * 是否有效 0->无效 1->有效
     */
    private Byte isValid;
    /**
     * cmpp协议版本
     */
    private Integer version;
    /**
     * 最大连接数
     */
    private Integer maxConnect;
    /**
     * 写限制
     */
    private Integer writeLimit;
    /**
     * 读限制
     */
    private Integer readLimit;
    /**
     * 白名单 多个ip使用逗号分割
     */
    private String ipList;
    /**
     * 是否开启白名单检测 0->不检测 1->检测
     */
    private Byte isIpCheck;
    /**
     * 组名称
     */
    @NotBlank(message = "组名称不能为空")
    private String groupName;
    /**
     * 空闲时间(秒)
     */
    @NotBlank(message = "空闲时间不能为空")
    @Min(value = 1, message = "空闲时间不能够小于1秒")
    private Integer idleTimeSec;

    /**
     * 转换成DO
     *
     * @param clientChannelAccountDTO
     * @return
     */
    public static ClientChannelAccountDO toDO(ClientChannelAccountDTO clientChannelAccountDTO) {
        ClientChannelAccountDO clientChannelAccountDO = new ClientChannelAccountDO();
        // 服务代码
        clientChannelAccountDO.setServiceId(clientChannelAccountDTO.getServiceId());
        // 账号
        clientChannelAccountDO.setUsername(clientChannelAccountDTO.getUsername());
        // 密码
        clientChannelAccountDO.setPassword(clientChannelAccountDTO.getPassword());
        // 连接状态 0->未连接 1->已连接
        clientChannelAccountDO.setIsChannelConnect(clientChannelAccountDTO.getIsChannelConnect());
        // 是否有效 0->无效 1->有效
        clientChannelAccountDO.setIsValid(clientChannelAccountDTO.getIsValid());
        // cmpp协议版本
        clientChannelAccountDO.setVersion(clientChannelAccountDTO.getVersion());
        // 最大连接数
        clientChannelAccountDO.setMaxConnect(clientChannelAccountDTO.getMaxConnect());
        // 写限制
        clientChannelAccountDO.setWriteLimit(clientChannelAccountDTO.getWriteLimit());
        // 读限制
        clientChannelAccountDO.setReadLimit(clientChannelAccountDTO.getReadLimit());
        // 白名单 多个ip使用逗号分割
        clientChannelAccountDO.setIpList(clientChannelAccountDTO.getIpList());
        // 是否开启白名单检测 0->不检测 1->检测
        clientChannelAccountDO.setIsIpCheck(clientChannelAccountDTO.getIsIpCheck());
        // 组名称
        clientChannelAccountDO.setGroupName(clientChannelAccountDTO.getGroupName());
        // 空闲时间(秒)
        clientChannelAccountDO.setIdleTimeSec(clientChannelAccountDTO.getIdleTimeSec());
        // 设置更新人
        clientChannelAccountDO.setUpdater(SsoUserInfoUtil.getUserName());
        return clientChannelAccountDO;
    }

}
