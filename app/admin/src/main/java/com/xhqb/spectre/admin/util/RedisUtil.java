package com.xhqb.spectre.admin.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;

import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.Set;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/7 15:08
 * @Description:
 */
@Slf4j
public class RedisUtil {

    /**
     * Redis scan
     *
     * @param redisTemplate
     * @param pattern
     * @return
     */
    public static Set<String> scan(RedisTemplate<String, Object> redisTemplate, String pattern) {
        return redisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keySet = new HashSet<>();
            try (Cursor<byte[]> cursor = connection.scan(new ScanOptions.ScanOptionsBuilder()
                    .match(pattern)
                    .count(100000)
                    .build())) {
                while (cursor.hasNext()) {
                    keySet.add(new String(cursor.next(), StandardCharsets.UTF_8));
                }
            } catch (Exception e) {
                log.error("redis scan exception", e);
                throw new RuntimeException(e);
            }
            return keySet;
        });
    }
}
