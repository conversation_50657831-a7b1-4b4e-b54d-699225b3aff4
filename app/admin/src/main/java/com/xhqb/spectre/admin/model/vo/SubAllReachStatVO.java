package com.xhqb.spectre.admin.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Builder
public class SubAllReachStatVO implements Serializable {

    @ExcelProperty(index = 0, value = "周期")
    private String statDate;

    @ExcelIgnore
    private String channelCodeName;

    /**
     * 渠道编码
     */
    @ExcelIgnore
    private String channelCode;

    /**
     * 短信类型编码
     */
    @ExcelProperty(index = 1, value = "类型")
    private String smsTypeCodeName;

    @ExcelProperty(index = 2, value = "模板")
    private String tplCode;

    /**
     * 触达量
     */
    @ExcelProperty(index = 3, value = "触达量")
    private Integer reachCount;

    /**
     * 计费量
     */
    @ExcelProperty(index = 4, value = "计费量")
    private Integer reachBillCount;

    /**
     * 触达率
     */
    @ExcelProperty(index = 5, value = "触达率")
    private String reachRate;

    /**
     * 模板内容
     */
    @ExcelProperty(index = 6, value = "费用")
    private BigDecimal priceCount;
    
    /**
     * 签名名称
     */
    @ExcelProperty(index = 7, value = "签名")
    private String signName;

    @ExcelProperty(index = 8, value = "报备人")
    private String creator;

    @ExcelProperty(index = 9, value = "模板内容")
    private String tplContent;



    public static SubAllReachStatVO convert(SubReachStatVO vo) {
        if (vo == null) {
            return null;
        }
        return SubAllReachStatVO.builder()
                .statDate(vo.getStatDate())
                .channelCodeName(vo.getChannelCodeName())
                .channelCode(vo.getChannelCode())
                .smsTypeCodeName(vo.getSmsTypeCodeName())
                .tplCode(vo.getTplCode())
                .reachCount(vo.getReachCount())
                .reachBillCount(vo.getReachBillCount())
                .reachRate(vo.getReachRate())
                .priceCount(vo.getPriceCount())
                .signName(vo.getSignName())
                .creator(vo.getCreator())
                .tplContent(vo.getTplContent())
                .build();
    }
}
