package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReachStatDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer type;
    private String startDate;
    private String endDate;
    private String statDate;
    private String channelCode;
    private List<String> channelCodes;
    private String smsTypeCode;
    private List<String> smsTypeCodes;
    private List<String> signList;
    private PageParameter pageParameter;
    private Integer pageNum;
    private Integer pageSize;
    private String tplCode;

    public String getSmsTypeCode() {
        return CommonUtil.camelToSnake(smsTypeCode);
    }

    public List<String> getSmsTypeCodes() {
        if (smsTypeCodes == null) return null;
        return smsTypeCodes.stream().map(CommonUtil::camelToSnake).collect(Collectors.toList());
    }
}
