package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.SmsUplinkDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 上行短信
 *
 * <AUTHOR>
 * @date 2021/10/13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsUplinkVO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 订单号
     */
    private String orderId;

    /**
     * 内容
     */
    private String msgContent;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 接收时间
     */
    private Date recvUplinkTime;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 渠道账号id
     */
    private Integer channelAccountId;

    /**
     * 渠道code
     */
    private String channelCode;

    /**
     * 短信类型
     */
    private String smsTypeCode;

    /**
     * CID
     */
    private String cid;

    /**
     * 查询列表数据展现
     *
     * @param smsUplinkDO
     * @return
     */
    public static SmsUplinkVO buildListQuery(SmsUplinkDO smsUplinkDO) {
        return SmsUplinkVO.builder()
                // 主键
                .id(smsUplinkDO.getId())
                // 订单号
                .orderId(smsUplinkDO.getChannelMsgId())
                // 内容
                .msgContent(smsUplinkDO.getMsgContent())
                // 短信类型
                .mobile(CommonUtil.maskMobile(smsUplinkDO.getMobile()))
                // 接收时间
                .recvUplinkTime(DateUtil.intToDate(smsUplinkDO.getRecvUplinkTime()))
                // 创建时间
                .createTime(smsUplinkDO.getCreateTime())
                .channelAccountId(smsUplinkDO.getChannelAccountId())
                .channelCode(smsUplinkDO.getChannelCode())
                .smsTypeCode(smsUplinkDO.getSmsTypeCode())
                .cid(smsUplinkDO.getCid())
                .build();
    }


}
