package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.WxUrlLinkDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.time.DateUtils;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WxUrlLinkVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final Date PERMANENT_DATE = DateUtil.smallToDate("2099-12-31");

    /**
     * id
     */
    private Long id;

    /**
     * 使用场景描述
     */
    private String linkDesc;

    /**
     * 短链地址
     */
    private String shortUrl;

    /**
     * 小程序短链地址
     */
    private String urlLink;

    /**
     * 小程序
     */
    private String appid;

    /**
     * 环境变量
     */
    private String envVersion;

    /**
     * 进入小程序的路径
     */
    private String path;

    /**
     * 进入小程序的query参数
     */
    private String query;

    /**
     * 短链过期时间
     */
    private Date expiredDate;


    /**
     * 状态，0：无效，1：有效
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    public static WxUrlLinkVO buildUrlLinkVO(WxUrlLinkDO item) {
        Date expiredDate = null;
        if (item.getGenerateDate() != null) {
            expiredDate = DateUtil.addDaysToDate(item.getGenerateDate(), CommonConstant.EXPIRE_INTERVAL);
        }
        return WxUrlLinkVO.builder()
                .id(item.getId())
                .linkDesc(item.getLinkDesc())
                .shortUrl(item.getShortUrl())
                .urlLink(item.getUrlLink())
                .appid(item.getAppid())
                .envVersion(item.getEnvVersion())
                .path(item.getPath())
                .query(item.getQuery())
                .expiredDate(expiredDate)
                .status(item.getStatus())
                .createTime(item.getCreateTime())
                .creator(item.getCreator())
                .updateTime(item.getUpdateTime())
                .updater(item.getUpdater())
                .build();
    }

}
