package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.AutoTestTaskVO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskVO;
import com.xhqb.spectre.admin.service.AutoTestTaskService;
import com.xhqb.spectre.common.dal.entity.AutoTestTaskDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.AutoTestOverrideQuery;
import com.xhqb.spectre.common.dal.query.AutoTestTaskQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 自动测试任务
 *
 * <AUTHOR>
 * @date 2025-07-09 11:25:33
 */
@Slf4j
@RestController
@RequestMapping("/auto_test_task")
public class AutoTestTaskController {

    @Resource
    private AutoTestTaskService autoTestTaskService;

    @GetMapping("/list")
    public CommonResult<CommonPager<AutoTestTaskDO>> list(@ModelAttribute AutoTestTaskQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        return CommonResult.success(this.autoTestTaskService.list(query));
    }

    /**
     * @param id 任务id
     */
    @GetMapping("/detail/{id}")
    public CommonResult<AutoTestTaskDO> detail(@PathVariable Long id) {
        return CommonResult.success(this.autoTestTaskService.detail(id));
    }

    @PostMapping("/add")
    public CommonResult<Void> add(@RequestBody AutoTestTaskVO vo) {
        this.autoTestTaskService.add(vo);
        return CommonResult.success();
    }

    @PutMapping("/edit")
    public CommonResult<Void> edit(@RequestBody AutoTestTaskVO vo) {
        this.autoTestTaskService.edit(vo);
        return CommonResult.success();
    }

    /**
     * 覆盖
     *
     * @param query    查询茶树
     * @param pageNum  页码
     * @param pageSize 页长
     */
    @GetMapping("/override")
    public CommonResult<CommonPager<TestContentTaskVO>> override(@ModelAttribute AutoTestOverrideQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        return CommonResult.success(this.autoTestTaskService.override(query));
    }
}
