package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.AppDTO;
import com.xhqb.spectre.admin.model.vo.AppEnumVO;
import com.xhqb.spectre.admin.model.vo.AppVO;
import com.xhqb.spectre.admin.service.AppService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.entity.AppDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.mapper.*;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.AppQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 10:21
 * @Description:
 */
@Slf4j
@Service
public class AppServiceImpl implements AppService {

    @Autowired
    private AppMapper appMapper;

    @Autowired
    private TplMapper tplMapper;

    @Autowired
    private AppSendLimitMapper appSendLimitMapper;

    @Autowired
    private OpTimeMapper opTimeMapper;

    @Autowired
    private MobileWhiteMapper mobileWhiteMapper;

    /**
     * 列表查询
     *
     * @param appQuery
     * @return
     */
    @Override
    public CommonPager<AppVO> listByPage(AppQuery appQuery) {
        return PageResultUtils.result(
                () -> appMapper.countByQuery(appQuery),
                () -> appMapper.selectByQuery(appQuery).stream().map(AppVO::buildAppVO).collect(Collectors.toList())
        );
    }

    /**
     * 查询应用详情
     *
     * @param id
     * @return
     */
    @Override
    public AppVO getById(Integer id) {
        AppDO appDO = validateAndSelectById(id);
        return AppVO.buildAppVO(appDO);
    }

    /**
     * 查询应用枚举
     *
     * @return
     */
    @Override
    public List<AppEnumVO> queryEnum() {
        return appMapper.selectEnum().stream().map(AppEnumVO::buildAppEnumVO).collect(Collectors.toList());
    }

    /**
     * 创建应用
     *
     * @param appDTO
     */
    @Override
    public void create(AppDTO appDTO) {
        //校验
        checkAddParam(appDTO);

        //生成skey
        String key = CommonUtil.getAppKey();
        if (StringUtils.isBlank(key)) {
            throw new BizException("生成应用秘钥失败");
        }

        //写入应用信息
        AppDO appDO = buildAppDO(appDTO, key);
        appMapper.insertSelective(appDO);
    }

    /**
     * 更新应用
     *
     * @param appDTO
     */
    @Override
    public void update(AppDTO appDTO) {
        //校验
        checkUpdateParam(appDTO);

        //写入应用信息
        AppDO appDO = buildAppDO(appDTO);
        appMapper.updateByPrimaryKeySelective(appDO);
    }

    /**
     * 删除应用
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void delete(Integer id) {
        AppDO appDO = validateAndSelectById(id);
        //判断是否有模板引用该应用
        List<TplDO> tplDOList = tplMapper.selectByAppCode(appDO.getCode()).stream()
                .filter(item -> item.getStatus().equals(CommonConstant.STATUS_VALID)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tplDOList)) {
            throw new BizException("以下模板引用了该业务应用，请先停用模板。模板列表：" + tplDOList.stream().map(TplDO::getCode).collect(Collectors.joining("、")));
        }

        //删除应用
        String currentUser = SsoUserInfoUtil.getUserName();
        appMapper.delete(id, currentUser);

        //删除应用限流配置
        String appCode = appDO.getCode();
        Integer currentTime = DateUtil.getNow();
        appSendLimitMapper.delete(appCode, currentUser);
        opTimeMapper.updateOpTime(OpLogConstant.MODULE_SEND_LIMIT, currentTime);

        //删除应用限流白名单
        mobileWhiteMapper.deleteByAppCode(appCode, currentUser);
        opTimeMapper.updateOpTime(OpLogConstant.MODULE_MOBILE_WHITE, currentTime);
    }

    private void checkAddParam(AppDTO appDTO) {
        //参数格式校验
        ValidatorUtil.validate(appDTO);
        //应用编码校验
        String appCode = appDTO.getCode();
        if (StringUtils.isBlank(appCode)) {
            throw new BizException("应用编码不能为空");
        }
        if (appCode.length() > 32) {
            throw new BizException("应用编码最大为32个字符");
        }
        Pattern pattern = Pattern.compile(CommonConstant.PATTERN_CODE);
        if (!pattern.matcher(appCode).matches()) {
            throw new BizException("应用编码格式有误");
        }
        AppDO exist = appMapper.selectByCode(appCode);
        if (Objects.nonNull(exist)) {
            throw new BizException("应用编码已存在");
        }
    }

    private void checkUpdateParam(AppDTO appDTO) {
        //参数格式校验
        ValidatorUtil.validate(appDTO);
        //存在性校验
        validateAndSelectById(appDTO.getId());
    }

    private AppDO validateAndSelectById(Integer id) {
        AppDO appDO = appMapper.selectByPrimaryKey(id);
        if (Objects.isNull(appDO)) {
            throw new BizException("未找到该业务应用");
        }
        return appDO;
    }

    private AppDO buildAppDO(AppDTO appDTO, String skey) {
        String userName = SsoUserInfoUtil.getUserName();
        return AppDO.builder()
                .code(appDTO.getCode())
                .name(appDTO.getName())
                .description(appDTO.getDescription())
//                .cbUrl(appDTO.getCbUrl())
                .skey(skey)
                .contentApiType(appDTO.getContentApiType())
                .creator(userName)
                .updater(userName)
                .build();
    }

    private AppDO buildAppDO(AppDTO appDTO) {
        return AppDO.builder()
                .id(appDTO.getId())
                .name(appDTO.getName())
                .description(appDTO.getDescription())
//                .cbUrl(appDTO.getCbUrl())
                .contentApiType(appDTO.getContentApiType())
                .updater(SsoUserInfoUtil.getUserName())
                .build();
    }
}
