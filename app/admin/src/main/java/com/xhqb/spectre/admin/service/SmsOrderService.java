package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.readonly.vo.SmsOrderPageVO;
import com.xhqb.spectre.admin.openapi.response.QueryOrderVO;
import com.xhqb.spectre.common.dal.query.SmsOrderQuery;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/23 18:04
 * @Description:
 */
public interface SmsOrderService {

    Integer queryTotalCount(SmsOrderQuery smsOrderQuery);

    SmsOrderPageVO listByPage(SmsOrderQuery smsOrderQuery);

    String queryMobile(Long orderId);

    List<QueryOrderVO> queryOrder(List<String> requestIdList);
}
