package com.xhqb.spectre.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.Strings;
import com.xhqb.kael.sequencegenerator.DistributedSequence;
import com.xhqb.spectre.admin.batchtask.ContentSendFactory;
import com.xhqb.spectre.admin.batchtask.MessageSendFactory;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryContext;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryResult;
import com.xhqb.spectre.admin.batchtask.utils.BatchTaskUtils;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.constant.ChannelConstant;
import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.enums.RespCodeEnum;
import com.xhqb.spectre.admin.enums.SmsTypeCodeEnum;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.*;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.result.GeneratorResult;
import com.xhqb.spectre.admin.model.vo.*;
import com.xhqb.spectre.admin.service.*;
import com.xhqb.spectre.admin.service.oa.OaBizService;
import com.xhqb.spectre.admin.service.oa.dto.CreateRequestDTO;
import com.xhqb.spectre.admin.service.oa.vo.DoCreateRequestInnerVO;
import com.xhqb.spectre.admin.service.oa.vo.DoCreateRequestVO;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.entity.*;
import com.xhqb.spectre.common.dal.entity.oa.TplApproveContent;
import com.xhqb.spectre.common.dal.entity.oa.TplContent;
import com.xhqb.spectre.common.dal.entity.oa.TplOaApprove;
import com.xhqb.spectre.common.dal.entity.support.TplChannelDOSupport;
import com.xhqb.spectre.common.dal.mapper.*;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.AppQuery;
import com.xhqb.spectre.common.dal.query.GatewayUserQuery;
import com.xhqb.spectre.common.dal.query.TplChangeRecordsQuery;
import com.xhqb.spectre.common.dal.query.TplQuery;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xhqb.spectre.admin.model.vo.TplVO.buildTplVO;

/**
 * @Author: huangyanxiong
 * @Date: 2021/9/15 17:30
 * @Description:
 */
@Service
@Slf4j
public class TplServiceImpl implements TplService {

    @Autowired
    private TplMapper tplMapper;

    @Autowired
    private SignMapper signMapper;

    @Autowired
    private AppMapper appMapper;

    @Autowired
    private TplChannelMapper tplChannelMapper;

    @Autowired
    private ChannelAccountMapper channelAccountMapper;

    @Autowired
    private TplDisableMapper tplDisableMapper;

    @Autowired
    private MessageSendFactory messageSendFactory;

    @Autowired
    private GatewayUserMapper gatewayUserMapper;

    @Autowired
    private GatewayTplMappingMapper gatewayTplMappingMapper;

    @Autowired
    private ContentSendFactory contentSendFactory;

    @Resource
    private BusinessLineService businessLineService;

    @Resource
    private MarketSceneService marketSceneService;

    @Resource
    private OpTimeMapper opTimeMapper;

    @Resource
    private TplOaApproveMapper tplOaApproveMapper;

    @Resource
    private TplApproveContentMapper tplApproveContentMapper;

    @Resource
    private TplContentMapper tplContentMapper;

    @Resource
    private DistributedSequence distributedSequence;

    @Resource
    private OaBizService oaBizService;

    @Resource
    private VenusConfig venusConfig;

    @Resource
    private TplOaReportService tplOaReportService;

    @Resource
    private SerialNumberGenerator serialNumberGenerator;

    @Resource
    private TplOpRecordMapper tplOpRecordMapper;

    @Resource
    private SignService signService;

    @Resource
    private TplUsageService tplUsageService;

    @Resource
    private FeiShuAlert feiShuAlert;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    /**
     * 查询模板列表
     *
     * @param tplQuery
     * @return
     */
    @Override
    public CommonPager<TplVO> listByPage(TplQuery tplQuery) {

        //增加调用时间范围条件
        if (tplQuery.getCallStartTime() != null && tplQuery.getCallEndTime() != null) {
            long startTs = tplQuery.getCallStartTime().getTime() / 1000;
            long endTs = tplQuery.getCallEndTime().getTime() / 1000;

            Set<String> tplCodes = tplUsageService.findTplCodesBySendTime(startTs, endTs);
            if (tplCodes != null) {
                tplQuery.setTplCodeList(tplCodes);
            }
        }

        Set<String> gatewayCodeSet = gatewayUserMapper.selectAll().stream().map(GatewayUserDO::getTplCode).collect(Collectors.toSet());
        Map<String, AppDO> appMap = appMapper.selectByQuery(new AppQuery()).stream().collect(Collectors.toMap(AppDO::getCode, Function.identity()));
        log.info("模板列表查询, tplQuery:{}", tplQuery);
        return PageResultUtils.result(
                () -> tplMapper.countByQuery(tplQuery),
                () -> tplMapper.selectByQuery(tplQuery).stream()
                        .map(item -> buildTplVO(item, isSupportMultiContent(item, gatewayCodeSet, appMap)))
                        .collect(Collectors.toList())
        );
    }

    /**
     * 添加模板
     *
     * @param addTplDTO
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void create(AddTplDTO addTplDTO) {
        //参数校验
        checkAddParam(addTplDTO);

        //写入模板信息
        TplDO tplDO = buildTplDO(addTplDTO, true);
        tplMapper.insertSelective(tplDO);
        businessLineService.addBusinessLineTpl(tplDO.getId(), addTplDTO.getBusinessLineId());
        marketSceneService.addMarketSceneTpl(tplDO.getId(), addTplDTO.getMarketSceneId());
        try {
            TplVO tplVO = buildTplVO(tplDO, false);
            tplVO.setBusinessLineId(addTplDTO.getBusinessLineId());
            tplVO.setMarketSceneId(addTplDTO.getMarketSceneId());
            saveTplOpRecord(tplVO, "create");
        } catch (Exception e) {
            log.info("保存模板操作记录失败", e);
        }
    }

    /**
     * 批量更新渠道报备信息
     *
     * @param batchTplChannelDTO
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public AdminResult updateByTplIdList(BatchTplChannelDTO batchTplChannelDTO) {
        //校验参数
        ValidatorUtil.validate(batchTplChannelDTO);
        //获取tplIdList
        List<Integer> tplIdList = batchTplChannelDTO.getTplIdList();
        //获取传输数据
        List<TplChannelDTO> tplChannelDTOList = batchTplChannelDTO.getTplChannelDTOList();
        //用来接收最终批量修改数据的集合
        List<TplChannelDO> tplChannelDOArrayList = new ArrayList<>();
        //渠道账号 id 和 对应的记录
        Map<Integer, ChannelAccountDO> accountMap = getChannelAccountMap(tplChannelDTOList.stream().
                map(TplChannelDTO::getChannelAccountId).collect(Collectors.toList()));
        //模版id 和 对应短信类型
        Map<Integer, String> tplMap = tplMapper.selectByIdList(tplIdList).stream().collect(Collectors.toMap(TplDO::getId, TplDO::getSmsTypeCode));
        //校验模版短信类型和渠道类型是否一致
        for (Integer id : tplIdList) {
            for (TplChannelDTO tplChannelDTO : tplChannelDTOList) {
                ChannelAccountDO channelAccountDO = accountMap.get(tplChannelDTO.getChannelAccountId());
                if (Objects.isNull(channelAccountDO)) {
                    return AdminResult.error("未找到渠道账号，账号ID：" + tplChannelDTO.getChannelAccountId());
                }
                if (channelAccountDO.getStatus().equals(CommonConstant.STATUS_INVALID)) {
                    return AdminResult.error("渠道账号已停用，账号：" + channelAccountDO.getName());
                }
                if (!channelAccountDO.getSmsTypeCode().equals(tplMap.get(id))) {
                    return AdminResult.error("短信类型不匹配");
                }
                tplChannelDOArrayList.add(buildTplChannelDO(tplChannelDTO, id));
            }
        }
        log.info("最后批量的数据大小为：{}", tplChannelDOArrayList.size());
        List<TplVO> tplVOList = new ArrayList<>();
        for (TplChannelDO tplChannelDO : tplChannelDOArrayList) {
            TplVO tplVO = getById(tplChannelDO.getTplId());
            tplVOList.add(tplVO);
        }
        //开始修改渠道模版
        if (Boolean.TRUE.equals(batchTplChannelDTO.getCovered())) {
            tplChannelMapper.deleteByTplIdList(tplIdList);
        } else {
            List<Integer> channelAccountIdList = tplChannelDOArrayList.stream()
                    .map(TplChannelDO::getChannelAccountId)
                    .collect(Collectors.toList());
            tplChannelMapper.deleteByCover(tplIdList, channelAccountIdList);
        }

        tplChannelMapper.saveByTplChannelDOList(tplChannelDOArrayList);

        for (TplVO tplVO : tplVOList) {
            saveTplOpRecord(tplVO, "update");
        }
        return AdminResult.success();
    }

    /**
     * 批量上报短信内容以进行审批
     *
     * @param batchReportDTO 包含需要上报的短信内容及相关信息的批量上报数据传输对象
     * @return 返回审批流程的ID
     * @throws BizException 当没有需要审批的短信内容或报备调用OA审批流程失败时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "spectreTransactionManager")
    public String batchReport(BatchReportDTO batchReportDTO) {

        if (!Objects.equals(batchReportDTO.getSmsTypeCode(), "market")) {
            throw new BizException("非营销场景，不支持报备");
        }

        Set<String> contentSet = tplOaReportService.getApprovedAndApprovingContentSet();
        List<SignDO> signDOList = signMapper.selectEnum(1);
        Map<String, String> signMap = signDOList.stream().collect(Collectors.toMap(s -> s.getId() + "", SignDO::getName));
        List<TplContent> tplContentList = new ArrayList<>();
        List<String> contentList = new ArrayList<>();
        List<TplApproveContent> tplApproveContentList = new LinkedList<>();
        batchReportDTO.getCodeContentMap().forEach((key, content) -> {
            if (contentSet.contains(content)) {
                return;
            }
            String[] codeAndSignId = key.split("&-");

            if (ArrayUtils.isEmpty(codeAndSignId) || codeAndSignId.length != 2) {
                return;
            }

            String contentId = distributedSequence.nextStr("spectre::admin::tpl-content:");
            Date curDate = new Date();
            TplContent tplContent = new TplContent();
            tplContent.setContent(content);
            tplContent.setContentId(contentId);
            tplContent.setCreateTime(curDate);
            tplContent.setUpdateTime(curDate);
            tplContentList.add(tplContent);

            TplApproveContent tplApproveContent = new TplApproveContent();
            tplApproveContent.setContentId(contentId);
            tplApproveContent.setTplCode(codeAndSignId[0]);
            String signId = codeAndSignId[1];
            tplApproveContent.setSignId(signId);
            tplApproveContent.setCreateTime(curDate);
            tplApproveContent.setUpdateTime(curDate);
            tplApproveContentList.add(tplApproveContent);
            contentList.add(Optional.ofNullable(signMap.get(signId)).orElse("") + content);

        });

        if (CollectionUtils.isEmpty(tplContentList)) {
            throw new BizException("模板的短信内容已报备过，无需重复报备");
        }

        String content = Strings.join("\n", contentList);

        TplOaApprove tplOaApprove = new TplOaApprove();
        try {
            // 调用OA审批接口，获取审批流程ID
            CreateRequestDTO createRequestDTO = new CreateRequestDTO();
            createRequestDTO.setTitle(batchReportDTO.getTitle());
            createRequestDTO.setSceneCode(batchReportDTO.getSceneCode());
            createRequestDTO.setWorkflowId(venusConfig.getOaWorkFlowId());
            createRequestDTO.setSmsTypeCode(batchReportDTO.getSmsTypeCode());
            createRequestDTO.setContent(content);

            DoCreateRequestVO doCreateRequestVO = oaBizService.doCreateRequest(createRequestDTO);
            if (!(Objects.isNull(doCreateRequestVO) || Objects.isNull(doCreateRequestVO.getData()))) {
                DoCreateRequestInnerVO data = doCreateRequestVO.getData();
                tplOaApprove.setFlowId(data.getRequestid());
                tplOaApprove.setUserId(doCreateRequestVO.getUserId());
            }
        } catch (Exception e) {
            log.warn("报备调用OA审批流程失败", e);
        }

        String flowId = tplOaApprove.getFlowId();
        if (StringUtils.isBlank(flowId)) {
            throw new BizException("报备调用OA审批流程失败");
        }

        tplOaApprove.setFlowTypeId(String.valueOf(venusConfig.getOaWorkFlowId()));
        tplOaApprove.setOriginalContent(JSON.toJSONString(contentList));
        tplOaApprove.setStatus(1);
        tplOaApprove.setSceneCode(batchReportDTO.getSceneCode());
        tplOaApprove.setTitle(batchReportDTO.getTitle());
        tplOaApprove.setSmsTypeCode(batchReportDTO.getSmsTypeCode());
        tplOaApprove.setCreator(SsoUserInfoUtil.getUserName());
        tplOaApprove.setRemark(batchReportDTO.getRemark());
        tplOaApproveMapper.insertSelective(tplOaApprove);
        tplContentList.forEach(item -> tplContentMapper.insertSelective(item));
        for (TplApproveContent tplApproveContent : tplApproveContentList) {
            tplApproveContent.setFlowId(flowId);
        }
        tplApproveContentList.forEach(item -> tplApproveContentMapper.insertSelective(item));
        tplApproveContentList.forEach(item -> tplMapper.updateApproveStatusByCodeAndSignId(item.getTplCode(), item.getSignId(), 1, SsoUserInfoUtil.getUserName()));

        return flowId;
    }

    /**
     * 编辑模板
     *
     * @param updateTplDTO
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(UpdateTplDTO updateTplDTO) {
        //参数校验
        TplDO exist = checkUpdateParam(updateTplDTO);
        boolean isGatewayTpl = isSupportMultiContent(exist);
        Integer id = updateTplDTO.getId();
        List<String> gatewayTplContentList = updateTplDTO.getGatewayTplContentList();
        checkGatewayTplContent(isGatewayTpl, id, gatewayTplContentList);

        // 获取 detail
        TplVO tplVO = getById(id);

        //写入模板信息
        TplDO tplDO = buildTplDO(updateTplDTO, false);
        tplMapper.updateByPrimaryKeySelective(tplDO);

        //写入模板的渠道报备信息
        List<TplChannelDTO> channelList = updateTplDTO.getChannelInfoList();
        tplChannelMapper.deleteByTplId(id);
        channelList.forEach(item -> tplChannelMapper.insertSelective(buildTplChannelDO(item, id)));

        //写入模板的屏蔽信息
        List<TplDisableDTO> disableList = updateTplDTO.getDisableInfoList();
        tplDisableMapper.deleteByTplId(id);
        disableList.forEach(item -> tplDisableMapper.insertSelective(buildTplDisableDO(item, id)));

        //如果是网关账号的模板，写入模板文案映射信息
        if (isGatewayTpl && CollectionUtils.isNotEmpty(gatewayTplContentList)) {
            String userName = SsoUserInfoUtil.getUserName();
            gatewayTplContentList.forEach(content -> {
                if (StringUtils.isBlank(content)) {
                    //忽略空内容
                    return;
                }
                GatewayTplMappingDO mappingDO = GatewayTplMappingDO.builder()
                        .tplId(id)
                        .tplContent(content)
                        .creator(userName)
                        .updater(userName)
                        .build();
                gatewayTplMappingMapper.insertSelective(mappingDO);
            });
            opTimeMapper.updateOpTime(OpLogConstant.MODULE_GATEWAY_TPL_MAPPING, DateUtil.getNow());
        }

        businessLineService.addBusinessLineTpl(tplDO.getId(), updateTplDTO.getBusinessLineId());
        marketSceneService.addMarketSceneTpl(tplDO.getId(), updateTplDTO.getMarketSceneId());

        saveTplOpRecord(tplVO, "update");
    }

    /**
     * 查询模板详情
     *
     * @param id
     * @return
     */
    @Override
    public TplVO getById(Integer id) {
        TplDO tplDO = validateAndSelectById(id);
        List<TplChannelDOSupport> channelDOList = tplChannelMapper.selectSupportByTplId(id);
        List<TplDisableDO> disableDOList = tplDisableMapper.selectByTplId(id);
        TplVO tplVO = TplVO.buildTplVOSupport(tplDO, channelDOList, disableDOList, isSupportMultiContent(tplDO));
        Integer businessLineId = businessLineService.getBusinessLineIdByTpl(id);
        Integer marketSceneId = marketSceneService.getMarketSceneIdByTpl(id);
        tplVO.setBusinessLineId(businessLineId);
        tplVO.setMarketSceneId(marketSceneId);
        return tplVO;
    }

    /**
     * 启用模板
     *
     * @param id
     */
    @Override
    public void enable(Integer id) {
        TplDO tplDO = validateAndSelectById(id);
        if (!isDisabled(tplDO)) {
            throw new BizException("模板不处于停用状态，不能启用");
        }
        //判断签名是否可用
        SignDO signDO = signMapper.selectByPrimaryKey(tplDO.getSignId());
        if (Objects.isNull(signDO) || !signDO.getStatus().equals(CommonConstant.STATUS_VALID)) {
            throw new BizException("模板引用的签名已停用，不能启用模板");
        }
        //判断是否有可用渠道
        if (!hasAvailableChannel(id)) {
            throw new BizException("模板没有可用渠道，不能启用");
        }

        TplVO tplVO = getById(id);
        //修改状态为启用
        String currentUser = SsoUserInfoUtil.getUserName();
        tplMapper.enable(id, currentUser);
        saveTplOpRecord(tplVO, "turn");
    }

    /**
     * 停用模板
     *
     * @param id
     */
    @Override
    public void disable(Integer id) {
        TplDO tplDO = validateAndSelectById(id);
        if (!isEnabled(tplDO)) {
            throw new BizException("模板不处于启用状态，不能停用");
        }
        TplVO tplVO = getById(id);
        //修改状态为停用
        String currentUser = SsoUserInfoUtil.getUserName();
        tplMapper.disable(id, currentUser);
        saveTplOpRecord(tplVO, "turn");
        // 发送关停通知
        sendTplStopNotify(tplDO);
    }


    /**
     * 删除模板
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void delete(Integer id) {
        TplDO tplDO = validateAndSelectById(id);
        if (!isDisabled(tplDO)) {
            throw new BizException("模板不处于停用状态，不能删除");
        }

        //删除记录
        tplMapper.delete(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 查询模板枚举
     *
     * @param code
     * @param title
     * @param status
     * @param signId
     * @return
     */
    @Override
    public List<TplEnumVO> queryEnum(String code, String title, Integer status, Integer signId) {
        return tplMapper.selectEnum(code, title, status, signId).stream().map(TplEnumVO::buildTplEnumVO).collect(Collectors.toList());
    }

    /**
     * 模板测试
     *
     * @param id
     * @param tplTestDTO
     * @return
     */
    @Override
    public List<String> test(Integer id, TplTestDTO tplTestDTO) {
        //参数格式校验
        ValidatorUtil.validate(tplTestDTO);

        // 是否是基于内容发送短信
        boolean isContentSend = Objects.equals(tplTestDTO.getSendType(), 1);

        TplDO tplDO = validateAndSelectById(id);
        //手机号校验
        String[] mobileList = tplTestDTO.getMobile().split(",");
        List<String> mobileTmpList = Arrays.asList(mobileList);
        mobileTmpList.forEach(mobile -> {
            if (!CommonUtil.isLegalPhone(mobile)) {
                throw new BizException("手机号不合法：" + mobile);
            }
        });
        //模板参数个数校验
        Integer tplParamCount = CommonUtil.getTplParamCount(tplDO.getContent());
        List<LinkedHashMap<String, String>> reqParamList = tplTestDTO.getParamList();

        if (!isContentSend) {
            // 不是基于内容发送短信才校验参数个数逻辑
            if ((CollectionUtils.isEmpty(reqParamList) && tplParamCount > 0) || tplParamCount != reqParamList.size()) {
                throw new BizException("参数个数与模板中的参数个数不一致");
            }
        }

        SingleFactoryContext singleFactoryContext = new SingleFactoryContext();
        singleFactoryContext.setAppCode(tplDO.getAppCode());
        singleFactoryContext.setTplCode(tplDO.getCode());
        singleFactoryContext.setMobileList(Arrays.asList(mobileList));

        if (CommonUtil.isTplPlaceholderModel(tplDO.getContent())) {
            // 短信模板占位符模式
            List<Map<String, String>> placeHolderList = BatchTaskUtils.paramMappingToPlaceholderList(reqParamList, mobileList);
            singleFactoryContext.setPlaceHolderList(placeHolderList);

        } else {
            //构造短信参数
            List<String> paramItem = reqParamList.stream().map(item -> item.get("value")).collect(Collectors.toList());
            List<List<String>> paramList = new ArrayList<>();
            mobileTmpList.forEach(item -> paramList.add(paramItem));
            singleFactoryContext.setParamMapList(paramList);
        }


        // 营销设置设置smsTypeCode值
        singleFactoryContext.setSmsCodeType(tplDO.getSmsTypeCode());
        singleFactoryContext.setChannelAccountId(tplTestDTO.getChannelAccountId());

        Integer signId = tplDO.getSignId();
        SignDO signDO = signMapper.selectByPrimaryKey(signId);
        if (Objects.nonNull(signDO)) {
            // 设置签名编码
            singleFactoryContext.setSignCode(signDO.getCode());
        }


        if (!isContentSend) {
            // 不是基于内容发送 走原有流程
            // 发送短信
            List<SingleFactoryResult> factoryResultList = messageSendFactory.singleSendMessage(singleFactoryContext);
            // 返回发送失败的手机号码
            return SingleFactoryResult.getAllFailMobile(factoryResultList);
        }

        // 基于内容发送短信 则需要设置短信模板内容
        // 因为短信内容已经做了 占位符替换了 (该content已经属于纯短信内容)
        singleFactoryContext.setContent(tplTestDTO.getContent());
        return contentSendFactory.sendContent(singleFactoryContext);
    }

    private void checkCommonParam(TplDTO tplDTO) {
        if (!MessageTypeEnum.contains(tplDTO.getSmsTypeCode())) {
            throw new BizException("不支持该短信类型编码");
        }
        SignDO signDO = signMapper.selectByPrimaryKey(tplDTO.getSignId());
        if (Objects.isNull(signDO) || !signDO.getStatus().equals(CommonConstant.STATUS_VALID)) {
            throw new BizException("签名ID有误，该签名不存在或已停用");
        }
        AppDO appDO = appMapper.selectByCode(tplDTO.getAppCode());
        if (Objects.isNull(appDO)) {
            throw new BizException("授权应用编码有误，该应用不存在");
        }
    }

    @Override
    public CommonPager<TplOpRecordDO> queryChangeRecords(TplChangeRecordsQuery query) {
        List<TplOpRecordDO> recordDOList = tplOpRecordMapper.selectByQuery(query);

        for (TplOpRecordDO recordDO : recordDOList) {
            if (Objects.equals(recordDO.getType(), "turn")) {
                recordDO.setStatus(recordDO.getStatus() == 0 ? 1 : 0);
            }
        }

        return PageResultUtils.result(
                () -> tplOpRecordMapper.countByQuery(query),
                () -> recordDOList
        );
    }

    @Override
    public TplOpUpdateRecordVO queryUpdateRecords(TplChangeRecordsQuery query) {
        query.setPageParameter(null);
        TplOpRecordDO modelDO = tplOpRecordMapper.selectByQueryOrderById(query);
        TplOpUpdateRecordVO tplOpUpdateRecordVO = new TplOpUpdateRecordVO();
        TplOpRecordDO beforeDO = tplOpRecordMapper.selectByPrimaryKey(query.getId());
        tplOpUpdateRecordVO.setChangeBefore(beforeDO);
        if (Objects.nonNull(modelDO)) {
            tplOpUpdateRecordVO.setChangeAfter(modelDO);
            return tplOpUpdateRecordVO;
        }
        TplDO tplDO = tplMapper.selectByCodeAndSign(query.getCode(), query.getSignId());
        TplVO tplVO = getById(tplDO.getId());
        TplOpRecordDO tplOpRecordDO = buildTplOpRecordDOV2(tplVO);
        tplOpUpdateRecordVO.setChangeAfter(tplOpRecordDO);
        return tplOpUpdateRecordVO;
    }

    @Override
    public List<TplOperationVO> exportTpl(ExportTplDTO exportTplDTO) {
        List<Integer> ids = exportTplDTO.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            throw new BizException(RespCodeEnum.PARAM_ERROR);
        }
        List<TplOperationVO> voList = new ArrayList<>(ids.size());

        for (Integer id : ids) {
            try {
                TplOperationVO tplVO = doExportTplData(id);
                voList.add(tplVO);
            } catch (Exception e) {
                log.warn("导出模板失败，模板ID：{}", id, e);
            }
        }

        return voList;
    }

    @Override
    public Object importTplByJson(List<TplOperationVO> tplOperationVOList) {

        if (CollectionUtils.isEmpty(tplOperationVOList)) {
            throw new BizException(RespCodeEnum.PARAM_ERROR);
        }
        for (int index = 0; index < tplOperationVOList.size(); index++) {
            TplOperationVO tplOperationVO = tplOperationVOList.get(index);
            AddTplDTO addTplDTO = buildAddTplDTO(tplOperationVO);
            try {
                create(addTplDTO);
            } catch (BizException bizException) {
                throw new BizException(String.format("第{%s}条之后未导入成功｜模板编码:{%s} | 签名编码:{%s} 导入失败", index + 1, addTplDTO.getCode(), tplOperationVO.getSignCode()));
            } catch (Exception e) {
                throw new BizException(String.format("第{%s}条之后未导入成功｜模板编码:{%s} | 签名编码:{%s} 未知异常", index + 1, addTplDTO.getCode(), tplOperationVO.getSignCode()));
            }
        }
        return "success";
    }

    @Override
    public Object deleteTplAvailableChannel() {
        String reachRateLowNotifySmsType = venusConfig.getReachRateLowNotifySmsType();
        Set<String> smsTypeSet = Arrays.stream(reachRateLowNotifySmsType.split(","))
                .collect(Collectors.toSet());
        List<TplDO> tplDOS = tplMapper.selectAll().stream()
                .filter(tplDO -> smsTypeSet.contains(tplDO.getSmsTypeCode()))
                .collect(Collectors.toList());
        for(TplDO tplDO : tplDOS){
            redisTemplate.delete("sms:template:history:"+ tplDO.getCode());
        }

        return "success";
    }

    private AddTplDTO buildAddTplDTO(TplOperationVO tplOperationVO) {
        AddTplDTO addTplDTO = new AddTplDTO();
        addTplDTO.setAppCode(tplOperationVO.getAppCode());
        addTplDTO.setCode(tplOperationVO.getCode());
        addTplDTO.setTitle(tplOperationVO.getTitle());
        addTplDTO.setContent(tplOperationVO.getContent());
        addTplDTO.setSmsTypeCode(tplOperationVO.getSmsTypeCode());
        addTplDTO.setRemark(tplOperationVO.getRemark());
        // 查询签名
        int signId = signService.findOrCreateMarketScene(tplOperationVO.getSignCode(), tplOperationVO.getSignName());
        // 营销场景 (根据营销场景名称查询是否存在，不存在则创建一个营销场景对象)
        int marketSceneId = marketSceneService.findOrCreateMarketScene(tplOperationVO.getMarketSceneName());
        // 业务线 (根据业务线名称查询是否存在，不存在则创建一个业务线对象)
        int businessLineId = businessLineService.findOrCreateBusinessLine(tplOperationVO.getBusinessLineName());
        addTplDTO.setSignId(signId);
        addTplDTO.setMarketSceneId(marketSceneId);
        addTplDTO.setBusinessLineId(businessLineId);
        return addTplDTO;
    }

    private TplOperationVO doExportTplData(Integer id) {
        TplOperationVO tplOperationVO = new TplOperationVO();
        TplDO tplDO = tplMapper.selectByPrimaryKey(id);
        if (Objects.isNull(tplDO)) {
            throw new BizException("模板不存在");
        }
        SignDO signDO = signMapper.selectByPrimaryKey(tplDO.getSignId());
        if (Objects.isNull(signDO)) {
            throw new BizException("签名不存在");
        }
        String businessLineName = businessLineService.getBusinessLineByTpl(id);
        if (StringUtils.isBlank(businessLineName)) {
            throw new BizException("业务线不存在");
        }
        String marketSceneName = marketSceneService.getMarketSceneByTpl(id);
        if (StringUtils.isBlank(marketSceneName)) {
            throw new BizException("营销场景不存在");
        }
        tplOperationVO.setBusinessLineName(businessLineName);
        tplOperationVO.setMarketSceneName(marketSceneName);
        tplOperationVO.setCode(tplDO.getCode());
        tplOperationVO.setSmsTypeCode(tplDO.getSmsTypeCode());
        tplOperationVO.setTitle(tplDO.getTitle());
        tplOperationVO.setContent(tplDO.getContent());
        tplOperationVO.setAppCode(tplDO.getAppCode());
        tplOperationVO.setSignCode(signDO.getCode());
        tplOperationVO.setSignName(signDO.getName());
        return tplOperationVO;
    }

    private void checkAddParam(AddTplDTO addTplDTO) {
        ValidatorUtil.validate(addTplDTO);
        checkCommonParam(addTplDTO);
        //校验模板编码和签名唯一性
        TplDO exist = tplMapper.selectByCodeAndSign(addTplDTO.getCode(), addTplDTO.getSignId());
        if (Objects.nonNull(exist)) {
            throw new BizException("该模板编码和签名已存在");
        }
    }

    private TplDO checkUpdateParam(UpdateTplDTO item) {
        //参数格式校验
        ValidatorUtil.validate(item);
        //通用参数校验
        checkCommonParam(item);
        //校验模板是否存在
        TplDO tplDO = validateAndSelectById(item.getId());
        //签名有变动，校验模板编码和签名唯一性
        if (!item.getSignId().equals(tplDO.getSignId())) {
            TplDO exist = tplMapper.selectByCodeAndSign(tplDO.getCode(), item.getSignId());
            if (Objects.nonNull(exist)) {
                throw new BizException("该模板编码和签名已存在");
            }
        }
        //校验渠道信息
        List<TplChannelDTO> channelList = item.getChannelInfoList();
        Map<Integer, ChannelAccountDO> accountMap = getChannelAccountMap(channelList.stream().map(TplChannelDTO::getChannelAccountId).collect(Collectors.toList()));
        Set<Integer> channelKeySet = new HashSet<>();
        channelList.forEach(channelInfo -> {
            //校验字段格式
            ValidatorUtil.validate(channelInfo);
            //校验渠道账号
            Integer channelAccountId = channelInfo.getChannelAccountId();
            ChannelAccountDO accountDO = accountMap.get(channelAccountId);
            if (Objects.isNull(accountDO)) {
                throw new BizException("未找到渠道账号，账号ID：" + channelAccountId);
            }
            if (accountDO.getStatus().equals(CommonConstant.STATUS_INVALID)) {
                throw new BizException("渠道账号已停用，账号：" + accountDO.getName());
            }
            if (!item.getSmsTypeCode().equals(accountDO.getSmsTypeCode())) {
                throw new BizException("渠道账号的短信类型与模板的短信类型不一致，账号：" + accountDO.getName());
            }
            if (channelKeySet.contains(channelAccountId)) {
                throw new BizException("渠道账号配置重复，账号：" + accountDO.getName());
            }
            channelKeySet.add(channelAccountId);

            //校验渠道定制模板内容
            String tplContent = channelInfo.getTplContent();
            if (StringUtils.isNotBlank(tplContent) && !CommonUtil.getTplParamCount(item.getContent()).equals(CommonUtil.getTplParamCount(tplContent))) {
                throw new BizException("渠道定制模板内容的参数与模板内容的参数不一致");
            }
        });
        //校验模板屏蔽信息
        List<TplDisableDTO> disabledInfoList = item.getDisableInfoList();
        disabledInfoList.forEach(disabledInfo -> {
            ValidatorUtil.validate(disabledInfo);
            //校验生效时间
            Date start = DateUtil.stringToDate(disabledInfo.getStartTime());
            Date end = DateUtil.stringToDate(disabledInfo.getEndTime());
            assert end != null;
            if (end.before(start)) {
                throw new BizException("开始时间不能大于结束时间");
            }
        });
        //如果模板已启用，需判断是否有可用渠道
//        if (isEnabled(tplDO) && channelList.stream().noneMatch(e -> e.getStatus().equals(ChannelConstant.AUDIT_STATUS_ACCEPT))) {
//            throw new BizException("该模板已启用，请至少配置一个可用的渠道");
//        }

        return tplDO;
    }

    private Map<Integer, ChannelAccountDO> getChannelAccountMap(List<Integer> channelAccountIdList) {
        return channelAccountMapper.selectByIdList(channelAccountIdList).stream()
                .collect(Collectors.toMap(ChannelAccountDO::getId, Function.identity()));
    }

    private TplDO buildTplDO(TplDTO tplDTO, boolean isAdd) {
        String userName = SsoUserInfoUtil.getUserName();
        TplDO tplDO = TplDO.builder()
                .smsTypeCode(tplDTO.getSmsTypeCode())
                .title(tplDTO.getTitle())
                .signId(tplDTO.getSignId())
                .content(tplDTO.getContent())
                .appCode(tplDTO.getAppCode())
                .remark(tplDTO.getRemark())
                .updater(userName)
                .tag(Objects.equals(tplDTO.getTag(), 1)? 1 : 0)
                .build();
        if (isAdd) {
            tplDO.setCode(((AddTplDTO) tplDTO).getCode());
            tplDO.setCreator(userName);
            tplDO.setApproveStatus(0);
        } else {
            tplDO.setId(((UpdateTplDTO) tplDTO).getId());
        }
        return tplDO;
    }

    private TplChannelDO buildTplChannelDO(TplChannelDTO item, Integer tplId) {
        return TplChannelDO.builder()
                .tplId(tplId)
                .channelAccountId(item.getChannelAccountId())
//                .status(item.getStatus())
//                .channelTplId(item.getChannelTplId())
                .isps(String.join(",", item.getIspList()))
                .areaFilterType(item.getAreaFilterType())
                .areas(JSON.toJSONString(item.getAreaList()))
                .weight(item.getWeight())
                .remark(StringUtils.isNotEmpty(item.getRemark()) ? item.getRemark() : "")
                .tplContent(StringUtils.isNotEmpty(item.getTplContent()) ? item.getTplContent() : "")
                .build();
    }

    private TplDisableDO buildTplDisableDO(TplDisableDTO item, Integer tplId) {
        return TplDisableDO.builder()
                .tplId(tplId)
                .isps(String.join(",", item.getIspList()))
                .areas(JSON.toJSONString(item.getAreaList()))
                .startTime(DateUtil.dateToInt(DateUtil.stringToDate(item.getStartTime())))
                .endTime(DateUtil.dateToInt(DateUtil.stringToDate(item.getEndTime())))
                .build();
    }

    private TplDO validateAndSelectById(Integer id) {
        TplDO tplDO = tplMapper.selectByPrimaryKey(id);
        if (Objects.isNull(tplDO)) {
            throw new BizException("未找到该模板");
        }
        return tplDO;
    }

    /**
     * 是否有可用渠道
     *
     * @param tplId
     * @return
     */
    private boolean hasAvailableChannel(Integer tplId) {
        List<Integer> availableChannelList = tplChannelMapper.selectByTplId(tplId).stream()
                .filter(item -> item.getStatus().equals(ChannelConstant.AUDIT_STATUS_ACCEPT))
                .map(TplChannelDO::getChannelAccountId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(availableChannelList)) {
            return false;
        }
        //判断渠道账号是否可用
        Map<Integer, ChannelAccountDO> accountMap = getChannelAccountMap(availableChannelList);
        availableChannelList = availableChannelList.stream().filter(id -> {
            ChannelAccountDO item = accountMap.get(id);
            return Objects.nonNull(item) && item.getStatus().equals(CommonConstant.STATUS_VALID);
        }).collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(availableChannelList);
    }

    private boolean isEnabled(TplDO tplDO) {
        return tplDO.getStatus().equals(CommonConstant.STATUS_VALID);
    }

    private boolean isDisabled(TplDO tplDO) {
        return tplDO.getStatus().equals(CommonConstant.STATUS_INVALID);
    }

    /**
     * 是否支持多个模板内容
     *
     * @param tplDO
     * @return
     */
    private boolean isSupportMultiContent(TplDO tplDO) {
        return isGatewayTpl(tplDO.getCode()) || isHttpContentTpl(tplDO);
    }

    /**
     * 是否网关账号的模板
     *
     * @param tplCode
     * @return
     */
    private boolean isGatewayTpl(String tplCode) {
        GatewayUserQuery query = new GatewayUserQuery();
        query.setTplCode(tplCode);
        Integer count = gatewayUserMapper.countByQuery(query);
        return Objects.nonNull(count) && count > 0;
    }

    /**
     * 是否http内容模板
     *
     * @param tplDO
     * @return
     */
    private boolean isHttpContentTpl(TplDO tplDO) {
        AppDO appDO = appMapper.selectByCode(tplDO.getAppCode());
        return Objects.nonNull(appDO) && appDO.isSupportContentApi() && tplDO.getCode().equals(buildHttpContentTplCode(tplDO));
    }

    /**
     * 是否支持多个模板内容
     *
     * @param tplDO
     * @param gatewayCodeSet
     * @param appMap
     * @return
     */
    private boolean isSupportMultiContent(TplDO tplDO, Set<String> gatewayCodeSet, Map<String, AppDO> appMap) {
        String tplCode = tplDO.getCode();
        if (gatewayCodeSet.contains(tplCode)) {
            return true;
        }
        AppDO appDO = appMap.get(tplDO.getAppCode());
        return Objects.nonNull(appDO) && appDO.isSupportContentApi() && tplCode.equals(buildHttpContentTplCode(tplDO));
    }

    private String buildHttpContentTplCode(TplDO tplDO) {
        return "http_" + tplDO.getAppCode() + "_" + tplDO.getSmsTypeCode() + "_tpl";
    }

    /**
     * 网关账号模板文案校验
     *
     * @param isGatewayTpl
     * @param tplId
     * @param gatewayTplContentList
     */
    private void checkGatewayTplContent(boolean isGatewayTpl, Integer tplId, List<String> gatewayTplContentList) {
        if (!isGatewayTpl || CollectionUtils.isEmpty(gatewayTplContentList)) {
            return;
        }
        gatewayTplContentList.forEach(content -> {
            //忽略空内容
            if (StringUtils.isBlank(content)) {
                return;
            }
            //重复性校验
            GatewayTplMappingDO exist = gatewayTplMappingMapper.selectByTplIdAndContent(tplId, content);
            if (Objects.nonNull(exist)) {
                throw new BizException("网关账号模板的文案已存在，请检查");
            }
        });
    }

    @Override
    public TplVO queryTpl(String code, String signName) {
        SignDO signDO = signMapper.selectByName(signName);
        if (signDO == null) return null;
        TplDO tplDO = tplMapper.selectByCodeAndSign(code, signDO.getId());
        if (tplDO == null) return null;
        return buildTplVO(tplDO, false);
    }

    /**
     * 导出活跃模版
     *
     * @return
     */
    @Override
    public List<TplExcelVO> downLoadActive(TplQuery tplQuery) {
        return getExcelList(tplMapper.selectByActive(tplQuery));
    }

    @Override
    public List<TplInfoVO> queryByAppCode(String appCode) {
        List<TplDO> tplDOList = tplMapper.selectByAppCode(appCode);
        List<SignDO> signDOList = signMapper.selectEnum(1);
        Map<Integer, String> signMap = signDOList.stream().collect(Collectors.toMap(SignDO::getId, SignDO::getName));
        List<TplInfoVO> tplInfoVOList = new ArrayList<>();
        if (tplDOList != null) {
            for (TplDO tplDO : tplDOList) {
                tplInfoVOList.add(TplInfoVO.buildTplInfoVO(tplDO, signMap.get(tplDO.getSignId())));
            }
        }
        return tplInfoVOList;
    }

    /**
     * 转成前端需要的模版数据
     *
     * @param lists 模版数据
     * @return
     */
    private List<TplExcelVO> getExcelList(List<TplDO> lists) {
        if (CollectionUtils.isEmpty(lists)) {
            return Collections.emptyList();
        }
        Map<String, String> messageTypeMap = new HashMap<>();
        for (MessageTypeEnum value : MessageTypeEnum.values()) {
            messageTypeMap.put(value.getMessageType(), value.getDescription());
        }
        Map<Integer, String> statusMap = new HashMap<>();
        statusMap.put(0, "停用");
        statusMap.put(1, "启用");
        Map<Integer, String> singMap = signMapper.selectByQuery(null).stream()
                .collect(Collectors.toMap(SignDO::getId, SignDO::getName, (s, s2) -> s2));
        List<TplExcelVO> list = lists.stream().map(tplDO ->
                {
                    TplExcelVO voExcel = TplExcelVO
                            .build(tplDO, singMap.get(tplDO.getSignId()), messageTypeMap.get(tplDO.getSmsTypeCode()), statusMap.get(tplDO.getStatus()));
                    voExcel.setContent((voExcel.getSignName() == null ? "" : voExcel.getSignName()) + voExcel.getContent());
                    return voExcel;
                }
        ).collect(Collectors.toList());
        return list;
    }


    public String syncTpl(TplContent tplContent) {
        TplDO tplDO = tplMapper.selectByReportId(tplContent.getContentId());
        if (Objects.nonNull(tplDO)) {
            throw new BizException("该模版已报备");
        }

        Integer signId = StringUtils.isBlank(tplContent.getSignId()) ? 0 : Integer.parseInt(tplContent.getSignId());
        SignDO signDO = signMapper.selectByPrimaryKey(signId);
        if (Objects.isNull(signDO)) {
            throw new BizException("报备申请id:" + tplContent.getContentId() + "|签名id:" + tplContent.getSignId() + "签名不存在");
        }


        TplDO tpl = new TplDO();
        tpl.setApproveStatus(3);
        tpl.setCreator("SYS");
        tpl.setUpdater("SYS");
        tpl.setCreateTime(new Date());
        tpl.setUpdateTime(new Date());
        tpl.setSource("sync");
        tpl.setStatus(0);
        tpl.setSmsTypeCode(tplContent.getSmsTypeCode());
        tpl.setContent(tplContent.getApprovedContent());
        tpl.setRemark("由报备申请id:" + tplContent.getContentId() + "同步");
        tpl.setReportId(tplContent.getContentId());
        tpl.setNotifySendStatus(0);
        tpl.setTag(tplContent.getTag());

        // code 生成规则：MM-DD-短信类型-4位序号
        GeneratorResult result = serialNumberGenerator.getTplResult(tplContent, signDO, tpl);
        log.info("生成模版code:{}", result.getCode());

        tpl.setCode(result.getCode());
        String dayFormDate = DateUtils.format(new Date(), "MMdd");
        tpl.setTitle(dayFormDate + SmsTypeCodeEnum.getByCode(tplContent.getSmsTypeCode()).getName() + "-" + result.getSerialNumber());
        tpl.setSignId(signDO.getId());

        tplMapper.insertSelective(tpl);
        TplVO tplVO = buildTplVO(tpl, false);
        saveTplOpRecord(tplVO, "create");
        return tpl.getCode();
    }

    public void saveTplOpRecord(TplVO tplVO, String opType) {
        try {
            TplOpRecordDO tplOpRecord = buildTplOpRecordDO(tplVO, opType);
            tplOpRecordMapper.insertSelective(tplOpRecord);
        } catch (Exception e) {
            log.error("保存模版操作记录异常", e);
        }
    }

    private static TplOpRecordDO buildTplOpRecordDO(TplVO tplVO, String type) {
        TplOpRecordDO tplOpRecord = new TplOpRecordDO();
        tplOpRecord.setAppCode(tplVO.getAppCode());
        tplOpRecord.setSignId(tplVO.getSignId());
        tplOpRecord.setSmsTypeCode(tplVO.getSmsTypeCode());
        tplOpRecord.setTitle(tplVO.getTitle());
        tplOpRecord.setCode(tplVO.getCode());
        tplOpRecord.setContent(tplVO.getContent());
        tplOpRecord.setStatus(tplVO.getStatus());
        tplOpRecord.setCreateTime(new Date());
        tplOpRecord.setUpdateTime(new Date());
        tplOpRecord.setOperator(SsoUserInfoUtil.getUserName());
        tplOpRecord.setType(type);
        tplOpRecord.setBusinessLineId(tplVO.getBusinessLineId());
        tplOpRecord.setMarketSceneId(tplVO.getMarketSceneId());
        tplOpRecord.setChannelInfo(CollectionUtils.isEmpty(tplVO.getChannelInfoList()) ? "" : JSON.toJSONString(tplVO.getChannelInfoList()));
        tplOpRecord.setDisableInfo(CollectionUtils.isEmpty(tplVO.getDisableInfoList()) ? "" : JSON.toJSONString(tplVO.getDisableInfoList()));
        return tplOpRecord;
    }

    private TplOpRecordDO buildTplOpRecordDOV2(TplVO tplVO) {
        TplOpRecordDO tplOpRecordDO = buildTplOpRecordDO(tplVO, "update");
        tplOpRecordDO.setOperator(tplVO.getUpdater());
        tplOpRecordDO.setCreateTime(DateUtil.stringToDate(tplVO.getCreateTime()));
        tplOpRecordDO.setUpdateTime(DateUtil.stringToDate(tplVO.getUpdateTime()));
        return tplOpRecordDO;
    }

    private void sendTplStopNotify(TplDO tplDO) {
        try {
            setCreatorByReportId(tplDO);
            List<String> atUserList = new ArrayList<>();
            atUserList.add(tplDO.getCreator());

            List<SignDO> signDOList = signMapper.selectEnum(1);
            if (signDOList == null || signDOList.isEmpty()) {
                log.warn("SignDO 列表为空，无法发送模板停用通知");
                return;
            }

            Map<Integer, String> signMap = signDOList.stream()
                    .collect(Collectors.toMap(SignDO::getId, SignDO::getName));

            Map<String, Object> dataMap = new HashMap<>();
            JSONObject jsonObject = new JSONObject();

            Integer signId = tplDO.getSignId();
            String content = signMap.getOrDefault(signId != null ? signId : -1, "") + tplDO.getContent();

            jsonObject.put("content", content);
            jsonObject.put("code", tplDO.getCode());

            dataMap.put("reports", CollectionUtil.newArrayList(jsonObject));
            dataMap.put("atUserList", atUserList);

            feiShuAlert.sendFeiShuAlert(dataMap, venusConfig.getTplDisableNotifyStrategyId());
        } catch (IllegalArgumentException | NullPointerException e) {
            log.warn("发送模板停用通知失败, 模板编码: {}", tplDO.getCode(), e);
        }
    }


    @Override
    public void setCreatorByReportId(TplDO tplDO) {
        if (Objects.equals(tplDO.getCreator(), "SYS") && !StringUtils.isEmpty(tplDO.getReportId())) {
            TplContent tplContent = tplContentMapper.selectByContentId(tplDO.getReportId());
            if (tplContent != null) {
                tplDO.setCreator(tplContent.getCreator());
            }
        }
        if (Objects.equals(tplDO.getCreator(), "SYS")) {
            tplDO.setCreator(venusConfig.getCreatorTplNotifyUser());
        }
    }


    @Override
    public void batchUpdateTplChannelWeights(Integer tplId, List<TplChannelWeightDTO> tplChannelWeightDTOList) {
        List<TplChannelDO> tplChannelDOList = tplChannelMapper.selectByTplId(tplId);

        Set<Integer> configuredAccountIds = tplChannelDOList.stream()
                .map(TplChannelDO::getChannelAccountId)
                .collect(Collectors.toSet());

        Set<Integer> updatedAccountIds = tplChannelWeightDTOList.stream()
                .map(TplChannelWeightDTO::getChannelAccountId)
                .collect(Collectors.toSet());

        if (!configuredAccountIds.equals(updatedAccountIds)) {
            throw new BizException("channelAccountId不一致，存在差集");
        }

        Map<Integer, TplChannelDO> doMap = tplChannelDOList.stream()
                .collect(Collectors.toMap(
                        TplChannelDO::getChannelAccountId,
                        t -> t
                ));


        List<TplChannelDO> updatedList = new ArrayList<>();
        for (TplChannelWeightDTO dto : tplChannelWeightDTOList) {
            Integer accountId = dto.getChannelAccountId();
            Integer newWeight = dto.getWeight();

            if (doMap.containsKey(accountId)) {
                TplChannelDO existingDO = doMap.get(accountId);
                if (!Objects.equals(existingDO.getWeight(), newWeight)) {
                    existingDO.setWeight(newWeight);
                    updatedList.add(existingDO);
                }
            }
        }

        if (!updatedList.isEmpty()) {
            for (TplChannelDO channel : updatedList) {
                log.info("更新权重:{}", channel);
                tplChannelMapper.updateWeightByPrimaryKey(channel.getId(), channel.getWeight());
            }
        }
    }
}
