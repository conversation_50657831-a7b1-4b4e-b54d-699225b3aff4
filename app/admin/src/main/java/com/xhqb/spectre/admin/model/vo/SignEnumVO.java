package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.SignDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 11:23
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SignEnumVO implements Serializable {

    private static final long serialVersionUID = 2318656636774346288L;

    private Integer id;

    private String name;

    private Integer status;

    public static SignEnumVO buildSignEnumVO(SignDO signDO) {
        return SignEnumVO.builder()
                .id(signDO.getId())
                .name(signDO.getName())
                .status(signDO.getStatus())
                .build();
    }
}
