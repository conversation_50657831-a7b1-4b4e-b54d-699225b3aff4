package com.xhqb.spectre.admin.batchtask.result.impl;

import com.xhqb.spectre.admin.batchtask.result.UploadResultContext;
import com.xhqb.spectre.admin.batchtask.result.UploadResultHandler;
import com.xhqb.spectre.admin.model.vo.batchtask.QueryTaskSegmentVO;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.BatchTaskParamDO;
import com.xhqb.spectre.common.dal.mapper.BatchTaskParamMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 读取上传文件数据信息
 * <p>
 * 根据分片内容 组装成完整的数据
 *
 * <AUTHOR>
 * @date 2021/10/1
 */
@Component
@Slf4j
public class ReadResultDataHandler implements UploadResultHandler<QueryTaskSegmentVO, List<BatchTaskParamDO>> {

    /**
     * 一次查询的数量
     */
    private static final int BATCH_QUERY_COUNT = 200;

    @Resource
    private BatchTaskParamMapper batchTaskParamMapper;

    /**
     * 上传结果处理
     * <p>
     * 根据分片内容 组装成完整的数据
     *
     * @param context
     * @return
     */
    @Override
    public List<BatchTaskParamDO> handler(UploadResultContext<QueryTaskSegmentVO> context) {
        QueryTaskSegmentVO taskSegmentVO = context.getData();
        String taskNo = taskSegmentVO.getTaskNo();
        // task_param主键
        List<Integer> segmentList = taskSegmentVO.getSegmentList();
        log.info("上传文件处理结果分片数量 = {},taskNo = {}", segmentList, taskNo);
        long start = System.currentTimeMillis();
        List<BatchTaskParamDO> batchTaskParamDOList = this.queryTaskParamList(segmentList);
        log.info("上传文件分片内容读取耗时 = {}, taskNo = {},", (System.currentTimeMillis() - start), taskNo);
        return batchTaskParamDOList;
    }

    /**
     * 查询任务参数列表
     *
     * @param segmentList
     * @return
     */
    private List<BatchTaskParamDO> queryTaskParamList(List<Integer> segmentList) {
        // 大数量分批次批量保存
        int paramSize = segmentList.size();
        if (paramSize <= BATCH_QUERY_COUNT) {
            return batchTaskParamMapper.selectByIdList(segmentList);
        }

        List<BatchTaskParamDO> resultList = new ArrayList<>(segmentList.size() + 1);
        int segment = paramSize / BATCH_QUERY_COUNT;
        List<Integer> subList;
        List<BatchTaskParamDO> batchTaskParamDOList;

        for (int i = 0; i < segment; i++) {
            subList = segmentList.subList(i * BATCH_QUERY_COUNT, (i + 1) * BATCH_QUERY_COUNT);
            batchTaskParamDOList = batchTaskParamMapper.selectByIdList(subList);
            if (!CommonUtil.isEmpty(batchTaskParamDOList)) {
                resultList.addAll(batchTaskParamDOList);
            }
        }

        if (paramSize % BATCH_QUERY_COUNT != 0) {
            subList = segmentList.subList((paramSize / BATCH_QUERY_COUNT) * BATCH_QUERY_COUNT, paramSize);
            batchTaskParamDOList = batchTaskParamMapper.selectByIdList(subList);
            if (!CommonUtil.isEmpty(batchTaskParamDOList)) {
                resultList.addAll(batchTaskParamDOList);
            }
        }

        return resultList;
    }
}
