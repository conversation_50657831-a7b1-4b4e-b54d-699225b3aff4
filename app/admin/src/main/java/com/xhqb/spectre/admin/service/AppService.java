package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.AppDTO;
import com.xhqb.spectre.admin.model.vo.AppEnumVO;
import com.xhqb.spectre.admin.model.vo.AppVO;
import com.xhqb.spectre.common.dal.dto.AppData;
import com.xhqb.spectre.common.dal.entity.AppDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.AppQuery;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 10:21
 * @Description:
 */
public interface AppService {

    CommonPager<AppVO> listByPage(AppQuery appQuery);

    AppVO getById(Integer id);

    List<AppEnumVO> queryEnum();

    void create(AppDTO appDTO);

    void update(AppDTO appDTO);

    void delete(Integer id);
}
