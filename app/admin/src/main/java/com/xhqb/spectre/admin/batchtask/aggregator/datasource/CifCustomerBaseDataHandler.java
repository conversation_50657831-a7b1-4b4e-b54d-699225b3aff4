package com.xhqb.spectre.admin.batchtask.aggregator.datasource;

import com.xhqb.spectre.admin.batchtask.aggregator.CustomerDataHandler;
import com.xhqb.spectre.admin.model.vo.batchtask.CustomerVO;
import com.xhqb.spectre.admin.service.CifCustomerBaseService;
import com.xhqb.spectre.admin.util.CommonUtil;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * cifdb.t_customer_base数据查询处理
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Component
public class CifCustomerBaseDataHandler implements CustomerDataHandler {

    @Resource
    private CifCustomerBaseService cifCustomerBaseService;

    /**
     * 数据来源查询
     *
     * @param cidList     cid列表
     * @param smsTypeCode 短信类型
     * @return
     */
    @Override
    public Map<String, CustomerVO> query(List<String> cidList, String smsTypeCode) {
        if (CommonUtil.isEmpty(cidList)) {
            return null;
        }
        return cifCustomerBaseService.mapByCidList(cidList, smsTypeCode);
    }

    /**
     * 判断当前处理器是否支持指定的签名名称
     *
     * @param signName
     * @return
     */
    @Override
    public boolean supports(String signName) {
        // 该handler为兜底处理器
        // 之前的handler都没有匹配到 则使用该处理
        return true;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
