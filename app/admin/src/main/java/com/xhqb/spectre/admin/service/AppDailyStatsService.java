package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.vo.AppDailyStatsVO;
import com.xhqb.spectre.admin.model.vo.AppDailyStatsChartVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.AppDailyStatsQuery;


public interface AppDailyStatsService {

    AppDailyStatsChartVO overview(AppDailyStatsQuery appDailyStatsQuery);

    CommonPager<AppDailyStatsVO> listByPage(AppDailyStatsQuery appDailyStatsQuery);

    void saveDailyStats();


}
