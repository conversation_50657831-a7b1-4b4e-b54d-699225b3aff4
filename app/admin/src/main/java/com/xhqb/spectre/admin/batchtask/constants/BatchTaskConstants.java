package com.xhqb.spectre.admin.batchtask.constants;

/**
 * 群发任务常量
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
public interface BatchTaskConstants {

    /**
     * 普通常量
     */
    interface Commons {
        /**
         * 名称标题关键字
         */
        String NAME_TITLE = "name";
        /**
         * 上传结果切分长度
         */
        int UPLOAD_RESULT_SLICE_LENGTH = 100000;

        /**
         * 存储文件上传结果redis过期时间  30分钟
         */
        int UPLOAD_RESULT_CACHE_EXPIRE = 30;
        /**
         * 文件校验结果显示数据数量
         */
        int CHECK_RESULT_COUNT = 30;
        /**
         * 分隔符
         */
        String SPLIT_FLAG = "@f@";
        /**
         * 2个长度
         */
        int LENGTH_TWO = 2;

        /**
         * cos spectre admin目录
         */
        String COS_ADMIN_DIR = "/admin";
        /**
         * cos 大数据上传文件目录
         */
        String COS_BIGDATA_DIR = "/bigdata";
        /**
         * 大数据标识
         */
        String COS_BIGDATA_FLAG = "bigdata";
        /**
         * cos文件名称分隔符
         */
        String COS_FILE_NAME_SPLIT = "$_$";
        /**
         * http字符串
         */
        String HTTP_STR = "http";
        /**
         * 数值1
         */
        int NUMBER_ONE = 1;

        /**
         * 未定义
         */
        String UNDEFINED = "undefined";
    }

    /**
     * 验证类执行顺序，越小值优先级越高，越先执行
     *
     * <AUTHOR>
     * @date 2021/9/18
     */
    interface ValidatorOrdered {
        /**
         * 有效性判断
         */
        int VALID = 10;
        /**
         * 过滤漏填参数
         */
        int MISS = 20;
        /**
         * 内容去重判断
         */
        int REPEAT = 30;
        /**
         * 号码状态
         */
        int PHONE_STATUS = 40;

    }

    /**
     * 策略名称
     *
     * <AUTHOR>
     * @date 2021/9/18
     */
    interface StrategyNamed {
        /**
         * URL策略名称
         */
        String URL_STRATEGY_NAME = "url";
        /**
         * WEB策略名称
         */
        String WEB_STRATEGY_NAME = "web";
        /**
         * 文件策略名称
         */
        String FILE_STRATEGY_NAME = "file";
    }

    /**
     * 数据类型
     */
    interface DataType {
        /**
         * 手机号码
         */
        String MOBILE = "mobile";
        /**
         * CID
         */
        String CID = "cid";
        /**
         * 完件信息
         */
        String APPLY_LOAN_RESULT = "wanjian";
        /**
         * 跳过发送 完件过滤时写入该标记，有该标记的数据不进行发送
         */
        String SKIP_SEND = "skipSend";
    }

    /**
     * http请求头
     */
    interface HttpHeader {
        /**
         * 签名请求头
         */
        String SIGN_KEY = "sign";
        /**
         * appKey
         */
        String APP_KEY = "appKey";
        /**
         * 请求ID
         */
        String REQUEST_ID = "requestId";
        /**
         * 请求头
         */
        String CONTENT_TYPE = "Content-Type";
        /**
         * JSON请求头
         */
        String JSON_TYPE = "application/json;charset=UTF-8";
    }

    /**
     * 文件上传查询状态
     */
    interface UploadQueryStatus {
        /**
         * 处理中
         */
        int PROCESSING = 0;
        /**
         * 处理完成
         */
        int COMPLETE = 1;
        /**
         * 处理失败
         */
        int FAIL = 2;
    }

    /**
     * 数据来源处理器
     */
    interface DataHandlerOrdered {
        /**
         * t_enterprise_customer_base处理器编号
         */
        int CIF_ENTERPRISE_CUSTOMER = 10;
    }

    /**
     * 群发日志
     */
    interface TaskLog {
        /**
         * 失败状态
         */
        int STATUS_ERROR = 0;
        /**
         * 成功状态
         */
        int STATUS_SUCCESS = 1;
    }


    /**
     * 模板类型
     */
    interface TplType {
        /**
         * 正常的[*]
         */
        int NORMAL = 0;
        /**
         * 占位符 ${xx}
         */
        int PLACEHOLDER = 1;
    }

    /**
     * 群发文件检测结果类型
     */
    interface DetectType {
        /**
         * 无效CID
         */
        int BAD_TYPE = 0;
        /**
         * 空号类型
         */
        int PHONE_EMPTY_TYPE = 1;
        /**
         * 停机类型
         */
        int PHONE_HALT_TYPE = 2;
        /**
         * 参数缺失
         */
        int MISS_TYPE = 3;
        /**
         * 数据重复
         */
        int REPEAT_TYPE = 4;
    }

}
