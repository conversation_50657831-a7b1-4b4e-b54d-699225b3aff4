package com.xhqb.spectre.admin.model.vo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class TplStatVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Map<String, String> headers;
    private List<TplChannelRateVO> tplChannelRates;
    private Integer totalCount;
}
