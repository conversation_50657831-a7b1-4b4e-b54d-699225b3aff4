package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class FailResendDTO {

    /**
     * 适用模板编号(原始模板编码) 多个使用英文逗号隔开
     */
    private String originalTplCodes;

    /**
     * 策略编号 新增 ''｜更新 策略编号
     */
    private String strategyId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 时间段 09:12:21-19:21:10
     */
    private String timePeriod;

    /**
     * 状态：0=启用，1=停用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 执行项 规则类型只能存在一种（也就是当前版本只会存在一个规则） ruleDTOList.size = 1
     */
    private List<FailResendRuleDTO> ruleDTOList;

}
