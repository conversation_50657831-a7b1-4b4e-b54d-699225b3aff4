package com.xhqb.spectre.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RespCodeEnum {

    /**
     * 请求成功：0
     */
    SUCCESS(0, "请求处理成功"),

    /**
     * 前端异常：1000-9999
     */
    PARAM_ERROR(1000, "请求参数异常"),

    /**
     * 业务场景异常：10000-19999
     */
    FAIL(10001, "请求处理失败"),
    NO_FIND_DATA(10002, "未查到数据"),

    /**
     * 后端服务异常：20000-29999
     */
    SYSTEM_ERROR(20001, "系统异常"),
    UNKNOWE(20002, "未知错误"),
    BUSINESS_ERROR(20003, "系统处理异常"),
    RPC_ERROR(20004, "系统调用dubbo服务异常"),
    MQ_SEND_ERROR(20005, "消息推送队列异常"),
    DUBBO_SYS_ERROR(20006, "dubbo服务调用异常"),
    UPLOAD_ERROR(20007, "文件上传异常"),
    DB_ERROR(21001, "数据库异常"),
    DB_UNIQUE_CONFLICT_ERROR(21002, "数据库唯一键冲突"),
    NULL_POINTER_ERROR(21003, "空指针异常");

    private Integer code;
    private String msg;
}
