package com.xhqb.spectre.admin.batchtask.limit;

import com.google.common.util.concurrent.RateLimiter;
import com.xhqb.spectre.admin.config.VenusConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/7/4
 */
@Configuration
@EnableScheduling
@Slf4j
public class BatchSubmitRateLimitService {

    /**
     * 提交限流缓存
     * key:群发任务ID，value：RateLimiter
     */
    private Map<Integer, RateLimiter> submitRateLimiterCache = new ConcurrentHashMap<>(64);

    @Resource
    private VenusConfig venusConfig;

    /**
     * 加入速率限制
     *
     * @param taskId    群发任务ID
     * @param limitRate 分发速率，0表示不限速，正数表示每秒处理的号码数
     */
    public void join(Integer taskId, Integer limitRate) {
        if (Objects.isNull(limitRate) || Objects.equals(limitRate, 0)) {
            return;
        }

        if (submitRateLimiterCache.containsKey(taskId)) {
            return;
        }

        int taskParamCount = venusConfig.getBatchUpdateTaskParamCount();
        int permitsPerSecond = limitRate / taskParamCount;
        if (permitsPerSecond <= 0) {
            log.info("taskParamCount = 0, 不做限流处理, taskId = {}, limitRate ={}", taskId, limitRate);
            return;
        }

        submitRateLimiterCache.put(taskId, RateLimiter.create(permitsPerSecond));
        log.info("群发限流器创建成功, taskId ={}, permitsPerSecond ={}", taskId, permitsPerSecond);
    }

    public void acquire(Integer taskId) {
        if (!this.venusConfig.isBatchSubmitRateEnable()) {
            log.info("群发提交速率限制开关未开启");
        }

        RateLimiter submitRateLimiter = submitRateLimiterCache.get(taskId);
        if (Objects.nonNull(submitRateLimiter)) {
            submitRateLimiter.acquire();
        }
    }


    public void leave(Integer taskId) {
        submitRateLimiterCache.remove(taskId);
        log.info("群发限流器移除成功, taskId ={}", taskId);
    }

    /**
     * 凌晨定时清理限流缓存
     */
    @Scheduled(cron = "0 0 1 * * ?")
    void scheduled() {
        submitRateLimiterCache.clear();
    }


}
