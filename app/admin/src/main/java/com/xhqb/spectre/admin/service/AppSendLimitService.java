package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.AppSendLimitDTO;
import com.xhqb.spectre.admin.model.vo.AppSendLimitVO;
import com.xhqb.spectre.admin.model.vo.LimitRuleEnumVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.AppSendLimitQuery;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 10:19
 * @Description:
 */
public interface AppSendLimitService {

    CommonPager<AppSendLimitVO> listByPage(AppSendLimitQuery appSendLimitQuery);

    AppSendLimitVO getByAppCode(String appCode);

    void edit(AppSendLimitDTO appSendLimitDTO);

    void enable(String appCode);

    void disable(String appCode);

    void delete(String appCode);

    List<LimitRuleEnumVO> queryLimitRuleEnum();
}
