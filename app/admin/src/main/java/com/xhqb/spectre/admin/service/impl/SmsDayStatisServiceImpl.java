package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.model.vo.SmsDayStatisVO;
import com.xhqb.spectre.admin.service.SmsDayStatisService;
import com.xhqb.spectre.common.dal.mapper.SmsDayStatisMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.SmsDayStatisQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * 短信发送量日概况
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
@Service
@Slf4j
public class SmsDayStatisServiceImpl implements SmsDayStatisService {

    @Resource
    private SmsDayStatisMapper smsDayStatisMapper;

    /**
     * 分页查询短信发送量日概况列表
     *
     * @param smsDayStatisQuery
     * @return
     */
    @Override
    public CommonPager<SmsDayStatisVO> listByPage(SmsDayStatisQuery smsDayStatisQuery) {
        return PageResultUtils.result(
                () -> smsDayStatisMapper.countByQuery(smsDayStatisQuery),
                () -> smsDayStatisMapper.selectByQuery(smsDayStatisQuery).stream().map(SmsDayStatisVO::buildListQuery).collect(Collectors.toList())
        );
    }
}
