package com.xhqb.spectre.admin.constant;

/**
 * api接口定义
 *
 * <AUTHOR>
 * @date 2021/10/11
 */
public interface Apis {

    /**
     * http协议
     */
    String HTTP = "http://";
    /**
     * https协议
     */
    String HTTPS = "https://";

    /**
     * cmmp相关接口
     */
    interface ReceiptCmpp {
        /**
         * cmpp 应用前缀
         */
        String CMPP_CONTEXT = "/spectre-receiptcmpp";

        /**
         * 查询渠道账户状态接口
         */
        String CHANNEL_DETAIL_API = CMPP_CONTEXT + "/channel/detail";
        /**
         * 渠道账号上线接口
         */
        String CHANNEL_REFRESH_API = CMPP_CONTEXT + "/channel/refresh/{channelAccountId}";
        /**
         * 渠道 账号下线接口
         */
        String CHANNEL_OFFLINE_API = CMPP_CONTEXT + "/channel/offline/{channelAccountId}";
    }

    /**
     * spectre api应用
     */
    interface SpectreApi {
        /**
         * 单个消息发送的API名称
         */
        String SINGLE_API_NAME = "/api/spectre/v3/sendSMS";
        /**
         * 验证码发送的API名称
         */
        String VERIFY_CODE_API_NAME = "/api/spectre/v3/sendVerify";

        /**
         * 基于内容发送短信 请求
         */
        String SEND_CONTENT_API_NAME = "/api/spectre/v3/sendContentAuto";

        /**
         * 查询手机号码状态的接口
         */
        String CHECK_PHONE_STATUS = "/api/spectre/v3/checkPhone";

        /**
         * 测试任务发送的API名称
         */
        String TEST_TASK_API_NAME = "/api/spectre/v3/testTask";
    }

    /**
     * cmpp server 应用
     */
    interface CmppServerApi {
        /**
         * cmpp server 账号上线接口
         */
        String ONLINE_API = "/channel/online";
        /**
         * cmpp server 账号下线接口
         */
        String OFFLINE_API = "/channel/offline";
    }

    /**
     * 短链 应用
     */
    interface ShortUrlApi {
        /**
         * 短链生成接口
         */
        String SHORT_CODE_URL ="/hold";
    }
}
