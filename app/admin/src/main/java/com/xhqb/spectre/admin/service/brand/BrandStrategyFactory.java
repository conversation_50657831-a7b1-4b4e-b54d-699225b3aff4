package com.xhqb.spectre.admin.service.brand;

import com.xhqb.spectre.common.dal.dto.ShortUrlLogData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Optional;

/**
 * 短链品牌工厂
 */

@Configuration
@Slf4j
public class BrandStrategyFactory {
    private final List<BrandDetectionStrategy> strategies;

    public BrandStrategyFactory(List<BrandDetectionStrategy> strategies) {
        this.strategies = strategies;
    }

    public String detectBrand(ShortUrlLogData shortUrlLogData) {
        String originBrand = Optional.ofNullable(shortUrlLogData.getBrand()).orElse("");
        String os = Optional.ofNullable(shortUrlLogData.getOs()).orElse("");

        return strategies.stream()
                .filter(s -> s.isMatch(originBrand, os))
                .findFirst()
                .map(BrandDetectionStrategy::getMappedBrand)
                .orElse("other");
    }
}
