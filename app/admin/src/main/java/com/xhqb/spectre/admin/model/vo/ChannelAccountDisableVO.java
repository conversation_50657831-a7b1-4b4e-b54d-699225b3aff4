package com.xhqb.spectre.admin.model.vo;

import cn.hutool.core.date.DateTime;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.ChannelAccountDO;
import com.xhqb.spectre.common.dal.entity.ChannelAccountDisableDO;
import com.xhqb.spectre.common.dal.entity.ChannelDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/22 15:56
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelAccountDisableVO implements Serializable {

    private static final long serialVersionUID = -843666624284234171L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 渠道账号ID
     */
    private Integer channelAccountId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道账号名称
     */
    private String channelAccountName;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 渠道账号状态，0：无效，1：有效
     */
    private Integer channelAccountStatus;

    /**
     * 运营商列表
     */
    private List<String> ispList;

    /**
     * 地域列表
     */
    private List<AreaVO> areaList;

    /**
     * 屏蔽开始时间(失效)
     */
    private String startTime;

    /**
     * 屏蔽结束时间(失效)
     */
    private String endTime;

    /**
     * 屏蔽类型：1-整个时间段屏蔽（原功能），2-固定时间段屏蔽（新功能）
     */
    private Integer disableType;

    /**
     * 时间段开始时间（格式：HH:mm，如 10:30）
     */
    private String periodStartTime;

    /**
     * 时间段结束时间（格式：HH:mm，如 12:00）
     */
    private String periodEndTime;

    /**
     * 备注信息（屏蔽原因等）
     */
    private String remark;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 屏蔽开始时间
     */
    private String maskStartTime;

    /**
     * 屏蔽开始时间
     */
    private String maskEndTime;

    public static ChannelAccountDisableVO buildAccountDisableVO(ChannelAccountDisableDO item) {
        ChannelAccountDisableVO accountDisableVO = ChannelAccountDisableVO.builder()
                .id(item.getId())
                .channelAccountId(item.getChannelAccountId())
                .ispList(CommonUtil.ispToList(item.getIsps()))
                .areaList(CommonUtil.areaToList(item.getAreas()))
                .createTime(DateUtil.dateToString(item.getCreateTime()))
                .creator(item.getCreator())
                .updateTime(DateUtil.dateToString(item.getUpdateTime()))
                .updater(item.getUpdater())
                .startTime(DateTime.of(item.getMaskStartTime()).toString())
                .endTime(DateTime.of(item.getMaskEndTime()).toString())
                .maskStartTime(DateTime.of(item.getMaskStartTime()).toString())
                .maskEndTime(DateTime.of(item.getMaskEndTime()).toString())
                .disableType(item.getDisableType())
                .periodStartTime(item.getPeriodStartTime())
                .periodEndTime(item.getPeriodEndTime())
                .remark(item.getRemark())
                .build();
        ChannelAccountDO channelAccountDO = item.getChannelAccountDO();
        if (Objects.nonNull(channelAccountDO)) {
            accountDisableVO.setSmsTypeCode(channelAccountDO.getSmsTypeCode());
            accountDisableVO.setChannelAccountName(channelAccountDO.getName());
            accountDisableVO.setChannelAccountStatus(channelAccountDO.getStatus());
            ChannelDO channelDO = channelAccountDO.getChannelDO();
            if (Objects.nonNull(channelDO)) {
                accountDisableVO.setChannelName(channelDO.getName());
            } else {
                accountDisableVO.setChannelName("");
            }

        }
        return accountDisableVO;
    }
}
