package com.xhqb.spectre.admin.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xhqb.spectre.admin.bidata.entity.ChannelReachStatDO;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonoReachStatVO {
    /**
     * 统计日期
     * 月份
     */
    @ExcelProperty({"", "日期"})
    private String statDate;
    /**
     * 渠道编码
     */
    @ExcelIgnore
    private String channelCode;
    @ExcelProperty({"", "供应商"})
    private String channelCodeName;

    @ExcelProperty({"触达率", "验证码"})
    private String verifyReachRate;
    @ExcelProperty({"触达率", "通知"})
    private String notifyReachRate;
    @ExcelProperty({"触达率", "营销"})
    private String marketReachRate;
    @ExcelProperty({"触达率", "催收"})
    private String collectorReachRate;
    @ExcelProperty({"触达率", "轻催"})
    private String lightCollectorReachRate;
    @ExcelProperty({"触达率", "重催"})
    private String severeCollectorReachRate;
    @ExcelProperty({"触达率", "债转"})
    private String debtSwapReachRate;
    @ExcelProperty({"触达率", "平均率"})
    private String avgReachRate;


    //验证码计费数
    @ExcelProperty({"计费量(条)", "验证码"})
    private Integer verifyReachBillCount;
    //通知计费数
    @ExcelProperty({"计费量(条)", "通知"})
    private Integer notifyReachBillCount;
    //营销计费数
    @ExcelProperty({"计费量(条)", "营销"})
    private Integer marketReachBillCount;
    //行业通知计费数
    @ExcelProperty({"计费量(条)", "催收"})
    private Integer collectorReachBillCount;
    //轻催计费数
    @ExcelProperty({"计费量(条)", "轻催"})
    private Integer lightCollectorReachBillCount;
    //重催计费数
    @ExcelProperty({"计费量(条)", "重催"})
    private Integer severeCollectorReachBillCount;
    //债转计费数
    @ExcelProperty({"计费量(条)", "债转"})
    private Integer debtSwapReachBillCount;
    //合计计费数
    @ExcelProperty({"计费量(条)", "总计费量"})
    private Integer totalReachBillCount;

    @ExcelIgnore
    private Integer verifyReachCount;
    @ExcelIgnore
    private Integer notifyReachCount;
    @ExcelIgnore
    private Integer marketReachCount;
    @ExcelIgnore
    private Integer collectorReachCount;
    @ExcelIgnore
    private Integer lightCollectorReachCount;
    @ExcelIgnore
    private Integer severeCollectorReachCount;
    @ExcelIgnore
    private Integer debtSwapReachCount;
    @ExcelIgnore
    private Integer totalReachCount;

    public static MonoReachStatVO convert(ChannelReachStatDO channelReachStatDO, String channelCodeName, List<String> selectedTypes) {
        MonoReachStatVO vo = MonoReachStatVO.builder()
                .statDate(channelReachStatDO.getStatDate())
                .channelCode(channelReachStatDO.getChannelCode())
                .channelCodeName(channelCodeName).build();

        double totalReachRateSum = 0.0;
        int reachRateCount = 0;
        int totalReachBillCountSum = 0;

        if (selectedTypes == null || selectedTypes.contains("verify")) {
            vo.setVerifyReachRate(CommonUtil.double2String(channelReachStatDO.getVerifyReachRate()));
            vo.setVerifyReachBillCount(channelReachStatDO.getVerifyReachBillCount());


            if (channelReachStatDO.getVerifyReachRate() != null) {
                totalReachRateSum += channelReachStatDO.getVerifyReachRate();
                reachRateCount++;
            }
            if (channelReachStatDO.getVerifyReachBillCount() != null) {
                totalReachBillCountSum += channelReachStatDO.getVerifyReachBillCount();
            }
        }

        if (selectedTypes == null || selectedTypes.contains("notify")) {
            vo.setNotifyReachRate(CommonUtil.double2String(channelReachStatDO.getNotifyReachRate()));

            vo.setNotifyReachBillCount(channelReachStatDO.getNotifyReachBillCount());

            if (channelReachStatDO.getNotifyReachRate() != null) {
                totalReachRateSum += channelReachStatDO.getNotifyReachRate();
                reachRateCount++;
            }
            if (channelReachStatDO.getNotifyReachBillCount() != null) {
                totalReachBillCountSum += channelReachStatDO.getNotifyReachBillCount();
            }
        }

        if (selectedTypes == null || selectedTypes.contains("market")) {
            vo.setMarketReachRate(CommonUtil.double2String(channelReachStatDO.getMarketReachRate()));

            vo.setMarketReachBillCount(channelReachStatDO.getMarketReachBillCount());

            if (channelReachStatDO.getMarketReachRate() != null) {
                totalReachRateSum += channelReachStatDO.getMarketReachRate();
                reachRateCount++;
            }
            if (channelReachStatDO.getMarketReachBillCount() != null) {
                totalReachBillCountSum += channelReachStatDO.getMarketReachBillCount();
            }
        }

        if (selectedTypes == null || selectedTypes.contains("collector")) {
            vo.setCollectorReachRate(CommonUtil.double2String(channelReachStatDO.getCollectorReachRate()));

            vo.setCollectorReachBillCount(channelReachStatDO.getCollectorReachBillCount());

            if (channelReachStatDO.getCollectorReachRate() != null) {
                totalReachRateSum += channelReachStatDO.getCollectorReachRate();
                reachRateCount++;
            }
            if (channelReachStatDO.getCollectorReachBillCount() != null) {
                totalReachBillCountSum += channelReachStatDO.getCollectorReachBillCount();
            }
        }

        if (selectedTypes == null || selectedTypes.contains("light_collector")) {
            vo.setLightCollectorReachRate(CommonUtil.double2String(channelReachStatDO.getLightCollectorReachRate()));

            vo.setLightCollectorReachBillCount(channelReachStatDO.getLightCollectorReachBillCount());

            if (channelReachStatDO.getLightCollectorReachRate() != null) {
                totalReachRateSum += channelReachStatDO.getLightCollectorReachRate();
                reachRateCount++;
            }
            if (channelReachStatDO.getLightCollectorReachBillCount() != null) {
                totalReachBillCountSum += channelReachStatDO.getLightCollectorReachBillCount();
            }
        }

        if (selectedTypes == null || selectedTypes.contains("severe_collector")) {
            vo.setSevereCollectorReachRate(CommonUtil.double2String(channelReachStatDO.getSevereCollectorReachRate()));

            vo.setSevereCollectorReachBillCount(channelReachStatDO.getSevereCollectorReachBillCount());



            if (channelReachStatDO.getSevereCollectorReachRate() != null) {
                totalReachRateSum += channelReachStatDO.getSevereCollectorReachRate();
                reachRateCount++;
            }

            if (channelReachStatDO.getSevereCollectorReachBillCount() != null) {
                totalReachBillCountSum += channelReachStatDO.getSevereCollectorReachBillCount();
            }
        }

        if (selectedTypes == null || selectedTypes.contains("debt_swap")) {
            vo.setDebtSwapReachRate(CommonUtil.double2String(channelReachStatDO.getDebtSwapReachRate()));
            vo.setDebtSwapReachBillCount(channelReachStatDO.getDebtSwapReachBillCount());

            if (channelReachStatDO.getDebtSwapReachRate() != null) {
                totalReachRateSum += channelReachStatDO.getDebtSwapReachRate();
                reachRateCount++;
            }
            if (channelReachStatDO.getDebtSwapReachBillCount() != null) {
                vo.setDebtSwapReachRate(CommonUtil.double2String(channelReachStatDO.getDebtSwapReachRate()));
            }
        }

        vo.setTotalReachBillCount(totalReachBillCountSum);
        if (reachRateCount > 0) {
            vo.setAvgReachRate(CommonUtil.double2String(totalReachRateSum / reachRateCount));
        }

        return vo;
    }
}
