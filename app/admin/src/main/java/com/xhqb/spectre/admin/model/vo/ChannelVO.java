package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.ChannelDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/24 16:43
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelVO implements Serializable {

    private static final long serialVersionUID = 8686466501135182243L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 渠道编码
     */
    private String code;

    /**
     * 渠道名称
     */
    private String name;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新人
     */
    private String updater;

    public static ChannelVO buildChannelVO(ChannelDO channelDO) {
        return ChannelVO.builder()
                .id(channelDO.getId())
                .code(channelDO.getCode())
                .name(channelDO.getName())
                .createTime(DateUtil.dateToString(channelDO.getCreateTime()))
                .creator(channelDO.getCreator())
                .updateTime(DateUtil.dateToString(channelDO.getUpdateTime()))
                .updater(channelDO.getUpdater())
                .build();
    }
}
