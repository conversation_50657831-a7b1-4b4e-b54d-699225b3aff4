package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.ClientChannelAccountDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ClientChannelAccountVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ClientChannelAccountQuery;

/**
 * 网关分配账号
 *
 * <AUTHOR>
 * @date 2021/10/26
 */
public interface ClientChannelAccountService {


    /**
     * 分页查询网关分配账号列表
     *
     * @param clientChannelAccountQuery
     * @return
     */
    CommonPager<ClientChannelAccountVO> listByPage(ClientChannelAccountQuery clientChannelAccountQuery);


    /**
     * 根据ID查询网关账号详情
     *
     * @param id
     * @return
     */
    ClientChannelAccountVO getById(Integer id);

    /**
     * 添加网关账号信息
     *
     * @param clientChannelAccountDTO
     */
    void create(ClientChannelAccountDTO clientChannelAccountDTO);


    /**
     * 更新网关账号信息
     *
     * @param id
     * @param clientChannelAccountDTO
     */
    void update(Integer id, ClientChannelAccountDTO clientChannelAccountDTO);

    /**
     * 删除网关账号信息
     *
     * @param id
     * @return
     */
    AdminResult delete(Integer id);

    /**
     * cmppserver 上线操作
     *
     * @param id
     * @return
     */
    AdminResult online(Integer id);

    /**
     * cmppserver 下线操作
     *
     * @param id
     * @return
     */
    AdminResult offline(Integer id);
}
