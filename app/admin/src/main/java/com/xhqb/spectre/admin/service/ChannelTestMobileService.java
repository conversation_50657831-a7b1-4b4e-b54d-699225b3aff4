package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.ChannelMobileBatchEditStatusDTO;
import com.xhqb.spectre.admin.model.dto.ChannelMobileSyncDTO;
import com.xhqb.spectre.admin.model.dto.ChannelTestDeleteDTO;
import com.xhqb.spectre.admin.model.dto.ChannelTestMobileDTO;
import com.xhqb.spectre.admin.model.vo.AppVO;
import com.xhqb.spectre.admin.model.vo.ChannelTestMobileVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ChannelTestMobileQuery;


public interface ChannelTestMobileService {
    CommonPager<ChannelTestMobileVO>  listByPage(ChannelTestMobileQuery channelTestMobileQuery);

    Integer add(ChannelTestMobileDTO channelTestMobileDTO);

    Integer delete(ChannelTestDeleteDTO channelTestDeleteDTO);

    Integer sync(ChannelMobileSyncDTO channelMobileSyncDTO);

    String batchEditStatus(ChannelMobileBatchEditStatusDTO batchEditStatusDTO);

}
