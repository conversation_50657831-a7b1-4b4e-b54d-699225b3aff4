package com.xhqb.spectre.admin.batchtask.send.content;

import com.xhqb.spectre.admin.batchtask.send.MessageRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 基于内容发送短信请求
 *
 * <AUTHOR>
 * @date 2022/1/13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContentMessageRequest extends MessageRequest {

    /**
     * 短信类型
     */
    private String smsType;

    /**
     * 短信模板内容
     */
    private String content;

    /**
     * 发送时间
     * 实时/定时，为空则实时发送
     */
    private String sendTime;

    /**
     * 模板参数
     * map中必须包含phone的key参数，最多支持100个手机号
     */
    private List<? extends Map<String, String>> paramList;
}
