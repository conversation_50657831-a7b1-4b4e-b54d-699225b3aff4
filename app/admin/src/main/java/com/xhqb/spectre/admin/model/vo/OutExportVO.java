package com.xhqb.spectre.admin.model.vo;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xhqb.spectre.admin.enums.OaStatusEnum;
import com.xhqb.spectre.common.dal.entity.oa.TplContent;
import lombok.Data;

@Data
public class OutExportVO {

    /**
     * 主键
     */
    @ExcelProperty(value = "id", index = 0)
    private Long id;

    /**
     * 渠道简称
     */
    @ExcelProperty(value = "渠道简称", index = 1)
    private String channelCodeName;


    /**
     * 原始内容
     */
    @ExcelProperty(value = "申请文案", index = 2)
    private String originalContent;

    /**
     * 报备通过内容
     */
    @ExcelProperty(value = "报备文案", index = 3)
    private String approvedContent;


    /**
     * tag
     */
    @ExcelProperty(value = "审批状态", index = 4)
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", index = 5)
    private String createTime;


    /**
     * 创建者
     */
    @ExcelProperty(value = "创建人", index = 6)
    private String creator;

    public static OutExportVO build(TplContent tplContent, String channelCodeName) {
        OaStatusEnum oaStatusEnum = OaStatusEnum.fromCode(tplContent.getStatus());
        OutExportVO exportVO = new OutExportVO();
        exportVO.setId(tplContent.getId());
        exportVO.setChannelCodeName(channelCodeName);
        exportVO.setOriginalContent(tplContent.getOriginalContent());
        exportVO.setApprovedContent(tplContent.getApprovedContent());
        exportVO.setStatus(oaStatusEnum.getDescription());
        exportVO.setCreator(tplContent.getCreator());
        exportVO.setCreateTime(DateUtil.format(tplContent.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        return exportVO;
    }
}
