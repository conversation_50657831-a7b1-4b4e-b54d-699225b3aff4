package com.xhqb.spectre.admin.batchtask.validate.impl;

import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.parse.ContentItem;
import com.xhqb.spectre.admin.batchtask.validate.ContentValidator;
import com.xhqb.spectre.admin.batchtask.validate.ValidateContext;
import com.xhqb.spectre.admin.batchtask.validate.ValidateResult;
import com.xhqb.spectre.admin.util.TplParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 过滤漏填参数条目
 * 过滤规则：
 * 根据上传文件的头信息，判断是否有空值存在，若存在则认为数据漏填
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
@Component
@Slf4j
public class MissContentValidator implements ContentValidator {
    /**
     * 数据验证
     *
     * @param validateContext
     * @param validateResult
     */
    @Override
    public void validate(ValidateContext validateContext, ValidateResult validateResult) {

        String tplContent = validateContext.getTplDO().getContent();
        int countSequentialPlaceholders = TplParamUtil.countSequentialPlaceholders(tplContent);
        int countKeyedPlaceholders = TplParamUtil.countKeyedPlaceholders(tplContent);
        int calculatedParamLength = 0;
        if (countSequentialPlaceholders > 0) {
            calculatedParamLength = countSequentialPlaceholders;
        } else if (countKeyedPlaceholders > 0) {
            calculatedParamLength = countKeyedPlaceholders;
        }

        // 缺少参数的数据
        List<ContentItem> missList = new ArrayList<>();
        // 参数的长度
        final int paramLength = calculatedParamLength;
        // 过滤之后有效的数据
        List<ContentItem> validateList = validateContext.getValidateList().stream().filter(s -> {
            if (!doMissValidate(s, paramLength)) {
                // 缺少参数
                missList.add(s);
                return false;
            }
            // 有效
            return true;
        }).collect(Collectors.toList());
        // 绑定结果数据信息
        // 设置缺少参数数据
        validateResult.setMissList(missList);

        // 设置上下文待验证的数据
        validateContext.setValidateList(validateList);
    }

    /**
     * 做缺少参数的验证
     *
     * @param contentItem
     * @param paramLength
     * @return
     */
    private boolean doMissValidate(ContentItem contentItem, int paramLength) {
        if (paramLength == 0) {
            //模板参数为空, 无需校验参数
            return true;
        }
        String content = contentItem.getContent();
        if (StringUtils.isBlank(content)) {
            // 内容为空 数据有问题
            log.warn("手机号码为空,内容为空 = {}", contentItem);
            return false;
        }
        List<String> paramList = contentItem.getParamList();
        // paramList 包含cid或mobile 需+1
        if (Objects.isNull(paramList) || paramList.size() != paramLength + 1) {
            // 参数数据为空 或者参值不对
            log.warn("手机号码为空,参值不对 = {}", contentItem);
            return false;
        }

        for (String param : paramList) {
            if (StringUtils.isBlank(param)) {
                // 参数数据为空
                log.warn("手机号码为空,参值为空 = {}", contentItem);
                return false;
            }
        }

        // 数据正常
        return true;
    }

    @Override
    public int getOrder() {
        return BatchTaskConstants.ValidatorOrdered.MISS;
    }
}
