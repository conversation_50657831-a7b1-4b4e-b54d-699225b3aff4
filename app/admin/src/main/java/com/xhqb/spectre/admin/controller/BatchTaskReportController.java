package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.service.BatchTaskReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 群发报告
 *
 * <AUTHOR>
 * @date 2021/10/22
 */
@RestController
@RequestMapping("/batchTaskReport")
@Slf4j
public class BatchTaskReportController {

    @Resource
    private BatchTaskReportService batchTaskReportService;

    /**
     * 查询报表信息
     *
     * @param taskId
     * @param taskParamId
     * @return
     */
    @GetMapping("/queryReport")
    public AdminResult queryReport(Integer taskId, Integer taskParamId) {
        log.info("查询群发报告,taskId = {},taskParamId = {}", taskId, taskParamId);
        return AdminResult.success(batchTaskReportService.queryReport(taskId, taskParamId));
    }


    /**
     * 快速统计
     * <p>
     * 主要快速统计出失败类型 失败数量 成功数等
     *
     * @param taskId
     * @return
     */
    @GetMapping("/quickStats")
    public AdminResult quickStats(Integer taskId) {
        return batchTaskReportService.quickStats(taskId);
    }
}
