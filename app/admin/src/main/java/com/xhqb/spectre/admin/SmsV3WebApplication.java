package com.xhqb.spectre.admin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
@EnableRedisHttpSession
@EnableTransactionManagement
@ComponentScan(basePackages={"com.xhqb.spectre.admin","com.xhqb.msgcenter.sdk.*"})
public class SmsV3WebApplication {

    public static void main(String[] args) {
        SpringApplication.run(SmsV3WebApplication.class, args);
    }
}
