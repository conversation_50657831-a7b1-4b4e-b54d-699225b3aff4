package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.ShortUrlTplDTO;
import com.xhqb.spectre.admin.model.vo.ShortUrlTplVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ShortUrlTplQuery;

public interface ShortUrlTplService {
    Integer add(ShortUrlTplDTO shortUrlTplDTO);

    Integer update(ShortUrlTplDTO shortUrlTplDTO);

    Integer delete(Integer id);

    CommonPager<ShortUrlTplVO> page(ShortUrlTplQuery shortUrlTplQuery);

    ShortUrlTplVO detail(Integer id);
}
