package com.xhqb.spectre.admin.service.test.tool;

import com.xhqb.spectre.admin.model.dto.TestContentTaskDTO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskDetailTypeVO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskDetailVO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskOverviewVO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.TestContentTaskQuery;

import java.util.List;
import java.util.Map;

public interface TestContentTaskService {
    CommonPager<TestContentTaskVO> listByPage(TestContentTaskQuery testContentTaskQuery);

    String add(TestContentTaskDTO testContentTaskDTO);

    String update(TestContentTaskDTO testContentTaskDTO);

    TestContentTaskVO detail(String taskId);

    TestContentTaskOverviewVO overview(String taskId);

    List<TestContentTaskDetailVO> detailBrand(String taskId);

    String executeTask(String taskId);

    String executeTask(String taskId, Map<String, String> map);

    void updateTaskRecordReportStatus(String taskId);

    void cancel(String taskId);

    TestContentTaskDetailTypeVO detailType(String taskId);
}
