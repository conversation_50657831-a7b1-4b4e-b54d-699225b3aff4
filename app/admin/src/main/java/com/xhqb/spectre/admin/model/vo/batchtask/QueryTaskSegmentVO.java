package com.xhqb.spectre.admin.model.vo.batchtask;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 上传结果查询显示任务分片信息
 *
 * <AUTHOR>
 * @date 2021/10/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryTaskSegmentVO implements Serializable {
    /**
     * 任务编号
     */
    private String taskNo;
    /**
     * 分片列表
     */
    private List<Integer> segmentList;
    /**
     * 文件的md5值
     */
    private String fileMd5;

    /**
     * 任务参数信息(保存好的参数数据将会有该值) 否则为空即可
     */
    private Integer taskParamId;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
