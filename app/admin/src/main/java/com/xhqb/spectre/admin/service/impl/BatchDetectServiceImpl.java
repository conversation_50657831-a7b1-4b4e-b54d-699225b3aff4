package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.service.BatchDetectService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.BatchDetectDO;
import com.xhqb.spectre.common.dal.mapper.BatchDetectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 群发检测结果服务
 *
 * <AUTHOR>
 * @date 2022/2/22
 */
@Service
public class BatchDetectServiceImpl implements BatchDetectService {

    /**
     * 批量参数插入数量
     */
    @Value("${spectre.admin.batchInsertDetectCount:300}")
    private Integer batchInsertDetectCount;

    @Resource
    private BatchDetectMapper batchDetectMapper;

    /**
     * 批量保存检测结果
     *
     * @param batchDetectList
     * @return
     */
    @Override
    public List<Integer> batchInsert(List<BatchDetectDO> batchDetectList) {
        if (CommonUtil.isEmpty(batchDetectList)) {
            // 为空时 则不进行保存操作
            return null;
        }
        List<Integer> detectIdList = new ArrayList<>(batchDetectList.size());

        // 大数量分批次批量保存
        int paramSize = batchDetectList.size();
        if (paramSize <= batchInsertDetectCount) {
            batchDetectMapper.batchInsert(batchDetectList);
            bindDetectId(detectIdList, batchDetectList);
            return detectIdList;
        }

        int segment = paramSize / batchInsertDetectCount;
        List<BatchDetectDO> subList;
        for (int i = 0; i < segment; i++) {
            subList = batchDetectList.subList(i * batchInsertDetectCount, (i + 1) * batchInsertDetectCount);
            batchDetectMapper.batchInsert(subList);
            bindDetectId(detectIdList, subList);
        }

        if (paramSize % batchInsertDetectCount != 0) {
            subList = batchDetectList.subList((paramSize / batchInsertDetectCount) * batchInsertDetectCount, paramSize);
            batchDetectMapper.batchInsert(subList);
            bindDetectId(detectIdList, subList);
        }

        return detectIdList;
    }

    /**
     * 绑定检测结果的id
     *
     * @param detectIdList
     * @param batchDetectList
     */
    private void bindDetectId(List<Integer> detectIdList, List<BatchDetectDO> batchDetectList) {
        batchDetectList.forEach(s -> detectIdList.add(s.getId()));
    }
}
