package com.xhqb.spectre.admin.model.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 渠道账号
 *
 * <AUTHOR>
 * @date 2021/9/21
 */
@Data
public class ChannelAccountDTO implements Serializable {

    /**
     * 渠道编码
     */
    @NotBlank(message = "渠道编码不能为空")
    private String channelCode;
    /**
     * 短信类型编码
     */
    @NotBlank(message = "短信类型编码不能为空")
    private String smsTypeCode;
    /**
     * 账号名称
     */
    @NotBlank(message = "账号名称不能为空")
    private String name;
    /**
     * 渠道账号
     */
    @NotBlank(message = "渠道账号不能为空")
    private String key;
    /**
     * 渠道账号json配置参数
     */
    @NotBlank(message = "渠道账号配置参数不能够为空")
    private String jsonMapping;
    /**
     * 费率，千分位存储
     */
    @NotNull(message = "费率不能够为空")
    @Min(value = 0, message = "费率值不能够小于零")
    private Integer price;
    /**
     * 协议，1：http；2：cmpp
     */
    @NotNull(message = "协议类型不能够为空")
    private Integer protocol;
    /**
     * 运营商
     */
    @NotEmpty(message = "运营商不能为空")
    private List<String> ispList;
    /**
     * 地域过滤类型，1：包含；2：不包含
     */
    @NotNull(message = "地域过滤类型不能为空")
    @Range(min = 1, max = 2, message = "地域过滤类型应为包含或不包含")
    private Integer areaFilterType;
    /**
     * 地域列表
     */
    @NotEmpty(message = "地域不能为空")
    private List<AreaDTO> areaList;
    /**
     * 权重
     */
    private Integer weight;
    /**
     * 是否免模板审核，1：是；0：否
     */
    private Integer isTplFreeAudit;
    /**
     * 支持的签名ID列表
     */
    @NotEmpty(message = "支持的签名ID列表不能为空")
    private List<Integer> signIdList;
    /**
     * 备注
     */
    private String remark;

}
