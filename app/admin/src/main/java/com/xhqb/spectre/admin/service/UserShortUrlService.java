package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.UserShortUrlDTO;
import com.xhqb.spectre.admin.model.result.FileCheckResult;
import com.xhqb.spectre.admin.model.vo.UserShortUrlVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.UserShortUrlQuery;

public interface UserShortUrlService {
    String add(UserShortUrlDTO userShortUrlDTO);
    CommonPager<UserShortUrlVO> page(UserShortUrlQuery userShortUrlQuery);

    UserShortUrlVO detail(Long id);

    FileCheckResult fileCheckResult(String fileName);

    String fileUploadAdd(String fileName);

    String downLoad(UserShortUrlQuery query);

    String queryMobile(Long id);
}
