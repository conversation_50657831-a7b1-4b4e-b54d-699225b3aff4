package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/*
 * @Author: huangyan<PERSON>ong
 * @Date: 2021/9/27 17:53
 * @Description:
 */
@RestController
@RequestMapping("/preLogin")
public class PreLoginController {

    /**
     * 预登陆接口
     *
     * @return
     */
    @GetMapping("")
    public AdminResult preLogin() {
        return AdminResult.success();
    }

}
