package com.xhqb.spectre.admin.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.msgcenter.model.MsgSendRequest;
import com.xhqb.msgcenter.model.iteam.MsgSendEntry;
import com.xhqb.msgcenter.model.response.OminiSendResult;
import com.xhqb.msgcenter.sdk.OminiSendMessage;
import com.xhqb.spectre.admin.cache.impl.TestMobileMemoryCache;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.common.dal.entity.ChannelAccountDO;
import com.xhqb.spectre.common.dal.entity.test.TestTaskDO;
import com.xhqb.spectre.common.dal.entity.test.TestTaskLogDO;
import com.xhqb.spectre.common.dal.entity.test.TestTplDO;
import com.xhqb.spectre.common.dal.mapper.ChannelAccountMapper;
import com.xhqb.spectre.common.dal.mapper.TestTaskLogMapper;
import com.xhqb.spectre.common.dal.mapper.TestTaskMapper;
import com.xhqb.spectre.common.dal.mapper.TestTplMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@Job("testChannelAlarmJob")
@Slf4j
public class TestChannelAlarmJob implements SimpleJob {

    @Resource
    private VenusConfig venusConfig;
    @Resource
    private OminiSendMessage ominiSendMessage;
    @Resource
    private TestTaskMapper taskMapper;
    @Resource
    private TestTaskLogMapper taskLogMapper;
    @Resource
    private TestMobileMemoryCache testMobileMemoryCache;
    @Resource
    private TestTplMapper testTplMapper;
    @Resource
    private ChannelAccountMapper channelAccountMapper;

    private static final String BLACK_SUCCESS_NUM = "blackSuccessNum";
    private static final String EMPTY_NUMBER_NUM = "emptyNumberNum";
    private static final String BLACK_ALARM_MOBILES = "blackAlarmMobiles";
    private static final String EMPTY_ALARM_MOBILES = "emptyAlarmMobiles";
    private static final String BLACK_ALARM_NUM = "blackAlarmNum";
    private static final String EMPTY_ALARM_NUM = "emptyAlarmNum";
    private static final String TASK_ID = "taskId";
    private static final String CHANNEL_ACCOUNT = "channelAccount";
    private static final String TPL_CODE = "tplCode";
    private static final String AT_USER_LIST = "atUserList";

    @Override
    public void execute(ShardingContext shardingContext) {
        long startTime = System.currentTimeMillis();
        log.info("开始执行测试渠道告警任务 start:{}", startTime);
        try {
            Date currentDate = new Date();
            DateTime offsetDay = DateUtil.offsetDay(currentDate, venusConfig.getAlarmDay());
            Date startDate = DateUtil.beginOfDay(offsetDay);
            Date endDate = DateUtil.endOfDay(offsetDay);
            log.info("开始执行测试渠道告警任务,开始时间:{}, 结束时间:{}", startDate, endDate);

            List<TestTaskDO> testTaskDOList = filterActiveTasks(taskMapper.selectByTime(startDate, endDate));
            if (CollectionUtil.isEmpty(testTaskDOList)) {
                log.info("没有需要告警的测试任务");
                return;
            }

            List<String> taskIdList = obtainBySource(testTaskDOList, 0);
            if (CollectionUtil.isEmpty(taskIdList)) {
                log.info("没有需要告警的测试任务taskIdList");
                return;
            }
            log.info("需要告警的测试任务taskIdList:{}", JsonLogUtil.toJSONString(taskIdList));
            List<TestTaskLogDO> taskLogDOList = filterTaskLogs(taskLogMapper.selectByTaskIdList(taskIdList));
            if (CollectionUtil.isEmpty(taskLogDOList)) {
                log.info("没有需要告警的测试任务对应的mobile");
                return;
            }
            List<Result> tempList = processTaskLogs(taskLogDOList);
            if (CollectionUtil.isEmpty(tempList)) {
                log.info("没有需要告警渠道异常数据");
                return;
            }
            Map<String, BaseSuccessInfo> successInfoByTaskIdMap = getStringBaseSuccessInfoMap(taskLogDOList);
            log.info("告警渠道异常数据成功信息:{}", JsonLogUtil.toJSONString(successInfoByTaskIdMap));
            sendFeiShuAlert(buildAlertMessages(tempList, successInfoByTaskIdMap));
        } catch (Exception e) {
            log.error("执行告警任务时发生异常:", e);
        }
        log.info("执行测试渠道告警任务结束,耗时:{}", System.currentTimeMillis() - startTime);
    }

    private List<String> obtainBySource(List<TestTaskDO> testTaskDOList, int source) {
        List<String> taskIdList = new ArrayList<>();
        Map<Long, List<TestTaskDO>> tplIdMap = testTaskDOList.stream()
                .collect(Collectors.groupingBy(TestTaskDO::getTplId));
        List<Long> tplIdList = testTaskDOList.stream().map(TestTaskDO::getTplId).collect(Collectors.toList());
        Set<Long> modelIdSet = testTplMapper.selectByTplIdList(tplIdList, source).stream().map(TestTplDO::getId).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(modelIdSet)) {
            return taskIdList;
        }
        for (Map.Entry<Long, List<TestTaskDO>> entry : tplIdMap.entrySet()) {
            if (modelIdSet.contains(entry.getKey())) {
                List<TestTaskDO> tempTestTaskDOList = entry.getValue();
                List<String> tempTaskIdList = tempTestTaskDOList.stream().map(TestTaskDO::getTaskId).collect(Collectors.toList());
                taskIdList.addAll(tempTaskIdList);
            }
        }
        return taskIdList;
    }

    public void sendFeiShuAlert(List<JSONObject> list) {
        MsgSendRequest msgSendRequest = new MsgSendRequest();

        // 飞书通知
        msgSendRequest.setMsgClient("1");
        msgSendRequest.setSystem(this.venusConfig.getServerName());

        MsgSendEntry msgSendEntry = new MsgSendEntry();
        msgSendEntry.setStrategyId(this.venusConfig.getTestChannelAlertStrategyId());

        HashMap<String, Object> params = new HashMap<>();
        params.put("msgType", "json");
        params.put("isDynamic", "1");
        params.put("alarmInfoList", list);
        msgSendEntry.setMsgVariableJson(params);

        msgSendRequest.setMsgSendEntries(CollectionUtil.toList(msgSendEntry));

        try {
            OminiSendResult ominiSendResult = this.ominiSendMessage.sendMessage(msgSendRequest);
            if (ominiSendResult.isSuccess()) {
                log.info("推送告警信息成功");
                return;
            }
            log.info("推送告警信息失败，失败编码：{}", ominiSendResult.getCode());
        } catch (Exception e) {
            log.warn("推送告警信息失败", e);
        }
    }

    private List<TestTaskDO> filterActiveTasks(List<TestTaskDO> tasks) {
        return tasks.stream().filter(taskDO -> Objects.equals(taskDO.getStatus(), 1)).collect(Collectors.toList());
    }

    private List<TestTaskLogDO> filterTaskLogs(List<TestTaskLogDO> logs) {
        return logs.stream()
                .filter(logDO -> Objects.equals(logDO.getSendStatus(), 0))
                .filter(logDO -> !Objects.equals(logDO.getCallStatus(), -1))
                .collect(Collectors.toList());
    }

    private List<Result> processTaskLogs(List<TestTaskLogDO> taskLogs) {
        List<Result> resultList = new ArrayList<>();
        for (TestTaskLogDO taskLogDO : taskLogs) {
            String mobile = taskLogDO.getMobile();
            Integer type = testMobileMemoryCache.getType(mobile);
            boolean abnormalDataTag = (Objects.equals(0, type) || Objects.equals(2, type)) && Objects.equals(taskLogDO.getCallStatus(), 0);
            if (abnormalDataTag) {
                Result result = new Result();
                result.setTaskId(taskLogDO.getTaskId());
                result.setMobile(mobile);
                result.setTplId(taskLogDO.getTplId());
                result.setChannelAccountId(taskLogDO.getChannelAccountId());
                result.setType(type);
                resultList.add(result);
            }
        }
        return resultList;
    }

    private List<JSONObject> buildAlertMessages(List<Result> results, Map<String, BaseSuccessInfo> successInfoByTaskIdMap) {
        List<JSONObject> jsonObjectList = new ArrayList<>();
        Map<Long, String> tplNameMap = new HashMap<>();
        Map<Long, String> notifyAccountMap = new HashMap<>();
        Map<Integer, String> channelAccountNameMap = new HashMap<>();
        Map<String, List<Result>> taskResultsMap = results.stream().collect(Collectors.groupingBy(Result::getTaskId));

        // 对任务结果进行处理
        taskResultsMap.forEach((taskId, resultList) -> {
            JSONObject jsonObject = new JSONObject();
            Result result = resultList.get(0);
            Long tplId = result.getTplId();
            Integer channelAccountId = result.getChannelAccountId();
            String tplName = tplNameMap.computeIfAbsent(tplId, this::getTplName);
            String channelAccountName = channelAccountNameMap.computeIfAbsent(channelAccountId, this::getChannelAccountName);
            String notifyAccount = notifyAccountMap.computeIfAbsent(tplId, this::getNotifyAccount);
            jsonObject.put(TASK_ID, result.getTaskId());
            jsonObject.put(CHANNEL_ACCOUNT, channelAccountName);
            jsonObject.put(TPL_CODE, String.format("%s (%s)", tplName, tplId));
            jsonObject.put(AT_USER_LIST, Arrays.stream(notifyAccount.split(",")).collect(Collectors.toList()));
            AtomicInteger blackAlarmNum = new AtomicInteger();
            AtomicInteger emptyAlarmNum = new AtomicInteger();
            resultList.forEach(resulItem -> {
                if (Objects.equals(resulItem.getType(), 2)) {
                    blackAlarmNum.getAndIncrement();
                    jsonObject.put(BLACK_ALARM_MOBILES, resulItem.getMobile());
                } else if (Objects.equals(resulItem.getType(), 0)) {
                    emptyAlarmNum.getAndIncrement();
                    jsonObject.put(EMPTY_ALARM_MOBILES, resulItem.getMobile());
                }
            });
            BaseSuccessInfo baseSuccessInfo = successInfoByTaskIdMap.get(taskId);
            if (Objects.nonNull(baseSuccessInfo)) {
                jsonObject.put(BLACK_SUCCESS_NUM, baseSuccessInfo.getBlackSuccessNum());
                jsonObject.put(EMPTY_NUMBER_NUM, baseSuccessInfo.getEmptyNumberNum());
            }
            jsonObject.put(BLACK_ALARM_NUM, blackAlarmNum);
            jsonObject.put(EMPTY_ALARM_NUM, emptyAlarmNum);
            if (!(blackAlarmNum.get() <= 0 && emptyAlarmNum.get() <= 0)) {
                jsonObjectList.add(jsonObject);
            }
        });
        if (jsonObjectList.isEmpty()) {
            throw new BizException("告警信息为空");
        }
        log.info("告警信息：{}", JsonLogUtil.toJSONString(jsonObjectList));
        return jsonObjectList;
    }


    private Map<String, BaseSuccessInfo> getStringBaseSuccessInfoMap(List<TestTaskLogDO> taskLogDOList) {
        Map<String, List<TestTaskLogDO>> taskIdMap = taskLogDOList.stream().collect(Collectors.groupingBy(TestTaskLogDO::getTaskId));
        Map<String, BaseSuccessInfo> successInfoByTaskIdMap = new HashMap<>();
        taskIdMap.forEach((taskId, tempLogList) -> {
            BaseSuccessInfo baseSuccessInfo = new BaseSuccessInfo();
            AtomicInteger blackSuccessNum = new AtomicInteger();
            AtomicInteger emptyNumberNum = new AtomicInteger();
            tempLogList.forEach(testTaskLogDO -> {
                Integer type = testMobileMemoryCache.getType(testTaskLogDO.getMobile());
                if (Objects.equals(type, 2)) {
                    blackSuccessNum.incrementAndGet();
                } else if (Objects.equals(type, 0)) {
                    emptyNumberNum.incrementAndGet();
                }
            });
            baseSuccessInfo.setBlackSuccessNum(blackSuccessNum);
            baseSuccessInfo.setEmptyNumberNum(emptyNumberNum);
            successInfoByTaskIdMap.put(taskId, baseSuccessInfo);
        });
        return successInfoByTaskIdMap;
    }


    private String getNotifyAccount(Long tplId) {
        return testTplMapper.selectByTplIdList(Collections.singletonList(tplId), 0)
                .stream()
                .findFirst()
                .map(TestTplDO::getNotifyAccount)
                .orElse("");
    }

    private String getTplName(Long tplId) {
        return testTplMapper.selectByTplIdList(Collections.singletonList(tplId), 0)
                .stream()
                .findFirst()
                .map(TestTplDO::getName)
                .orElse("");
    }

    private String getChannelAccountName(Integer channelId) {
        return channelAccountMapper.selectByIdList(Collections.singletonList(channelId))
                .stream()
                .findFirst()
                .map(ChannelAccountDO::getName)
                .orElse("");
    }

    @Data
    static class Result {

        /**
         * 任务id
         */
        private String taskId;

        /**
         * 渠道账号id
         */
        private Integer channelAccountId;

        /**
         * 模板id
         */
        private Long tplId;

        /**
         * 手机号
         */
        private String mobile;

        /**
         * 手机类型
         */
        private Integer type;

        /**
         * 异常通知账号
         */
        private String notifyAccount;

    }

    @Data
    static class BaseSuccessInfo {
        private AtomicInteger blackSuccessNum;
        private AtomicInteger emptyNumberNum;
    }

}
