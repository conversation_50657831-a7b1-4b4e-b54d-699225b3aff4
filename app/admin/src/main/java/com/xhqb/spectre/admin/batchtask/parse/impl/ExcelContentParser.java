package com.xhqb.spectre.admin.batchtask.parse.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.xhqb.spectre.admin.batchtask.parse.*;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * excel文件内容解析器
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
@Component
public class ExcelContentParser extends AbstractContentParser {

    /**
     * excel 2003
     */
    private static final String EXCEL_2003 = ".xls";
    /**
     * excel 2007
     */
    private static final String EXCEL_2007 = ".xlsx";

    /**
     * csv文件后缀名
     */
    private static final String[] EXCEL_SUFFIX = {EXCEL_2003, EXCEL_2007};

    /**
     * 获取到支持解析的文件后缀名
     *
     * @return
     */
    @Override
    protected String[] getSupportSuffix() {
        return EXCEL_SUFFIX;
    }

    /**
     * 真正做内容解析操作，并将处理结果写入到ParseResult对象中
     *
     * @param context
     * @param result
     * @return
     * @throws Exception
     */
    @Override
    protected List<ParseResult> doParse(ParseContext context, ParseResult result) throws Exception {
        List<ParseResult> parseResultList = null;
        try (InputStream inputStream = context.getInputStream()) {
            ExcelParserEvent parserEvent = new ExcelParserEvent(result);
            EasyExcel.read(inputStream, parserEvent).sheet(0).doRead();
            parseResultList = parserEvent.getParseResultList();
        }
        return parseResultList;
    }


    /**
     * excel文件解析事件监听
     *
     * <AUTHOR>
     * @date 2022/7/27
     */
    private static class ExcelParserEvent extends AnalysisEventListener<LinkedHashMap<Integer, String>> {

        private ParseResult result;
        private ResultGroupFactory resultGroupFactory;

        public ExcelParserEvent(ParseResult result) {
            this.result = result;
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            List<String> header = headMap.entrySet().stream().map(s -> s.getValue()).collect(Collectors.toList());
            resultGroupFactory = new ResultGroupFactory(result, header);
        }

        @Override
        public void invoke(LinkedHashMap<Integer, String> data, AnalysisContext context) {
            if (Objects.isNull(data) || data.isEmpty()) {
                return;
            }

            List<String> paramList = new ArrayList<>();
            int emptySize = 0;
            Set<Map.Entry<Integer, String>> entries = data.entrySet();
            for (Map.Entry<Integer, String> entry : entries) {
                String value = entry.getValue();
                paramList.add(value);
                if (Objects.isNull(value)) {
                    // 空值进行进行叠加
                    emptySize++;
                }
            }

            if (emptySize == data.size()) {
                // 所有数据都为空 则不需要保存
                return;
            }

            resultGroupFactory.parseContent(paramList);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {

        }

        public List<ParseResult> getParseResultList() {
            return resultGroupFactory.getParseResultList();
        }
    }

}
