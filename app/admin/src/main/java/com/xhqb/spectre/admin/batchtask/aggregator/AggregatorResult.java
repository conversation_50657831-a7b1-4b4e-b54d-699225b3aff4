package com.xhqb.spectre.admin.batchtask.aggregator;

import com.xhqb.spectre.admin.batchtask.parse.ContentItem;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/9/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AggregatorResult {
    /**
     * 无效数据
     */
    private List<ContentItem> badList;
    /**
     * 有效等待下一个流程验证的数据
     */
    private List<ContentItem> validateList;

    /**
     * 空号数据
     */
    private List<ContentItem> phoneEmptyList;
    /**
     * 停机数据
     */
    private List<ContentItem> phoneHaltList;

    /**
     * 聚合结果
     *
     * @param aggregatorResultList
     * @param initSize
     * @return
     */
    public static AggregatorResult toAggregatorResult(List<AggregatorResult> aggregatorResultList, int initSize) {
        List<ContentItem> badList = new ArrayList<>(initSize);
        List<ContentItem> validList = new ArrayList<>(initSize);

        // 空号
        List<ContentItem> phoneEmptyList = new ArrayList<>(initSize);
        // 停机
        List<ContentItem> phoneHaltList = new ArrayList<>(initSize);


        List<ContentItem> resultList;

        for (AggregatorResult aggregatorResult : aggregatorResultList) {
            resultList = aggregatorResult.getBadList();
            if (Objects.nonNull(resultList) && !resultList.isEmpty()) {
                badList.addAll(resultList);
            }

            resultList = aggregatorResult.getValidateList();
            if (Objects.nonNull(resultList) && !resultList.isEmpty()) {
                validList.addAll(resultList);
            }

            resultList = aggregatorResult.getPhoneEmptyList();
            if (!CommonUtil.isEmpty(resultList)) {
                phoneEmptyList.addAll(resultList);
            }

            resultList = aggregatorResult.getPhoneHaltList();
            if (!CommonUtil.isEmpty(resultList)) {
                phoneHaltList.addAll(resultList);
            }

        }
        return AggregatorResult.builder()
                .badList(badList)
                .validateList(validList)
                .phoneEmptyList(phoneEmptyList)
                .phoneHaltList(phoneHaltList)
                .build();
    }
}
