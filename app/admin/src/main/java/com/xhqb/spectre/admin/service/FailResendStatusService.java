package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.common.enums.FailResendStatusEnum;

/**
 * 补发状态
 */
public interface FailResendStatusService {
    
    /**
     * 更新补发记录状态
     * @param recordId 记录ID
     * @param newStatus 新状态
     * @return 是否更新成功
     */
    boolean updateStatus(Long recordId, FailResendStatusEnum newStatus);
    
    /**
     * 更新补发记录状态
     * @param recordId 记录ID
     * @param expectedCurrentStatus 期望的当前状态
     * @param newStatus 新状态
     * @return 是否更新成功
     */
    boolean updateStatusWithCheck(Long recordId, FailResendStatusEnum expectedCurrentStatus, FailResendStatusEnum newStatus);
    
    /**
     * 开始补发处理
     * @param recordId 记录ID
     * @return 是否更新成功
     */
    boolean startProcessing(Long recordId);
    
    /**
     * 标记补发成功
     * @param recordId 记录ID
     * @return 是否更新成功
     */
    boolean markSuccess(Long recordId);
    
    /**
     * 标记补发失败
     */
    boolean markFailed(Long recordId);
    
    /**
     * 获取补发记录状态
     * @param recordId 记录ID
     * @return 状态枚举
     */
    FailResendStatusEnum getStatus(Long recordId);
}
