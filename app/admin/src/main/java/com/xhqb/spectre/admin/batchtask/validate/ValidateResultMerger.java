package com.xhqb.spectre.admin.batchtask.validate;

import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.parse.ContentItem;
import com.xhqb.spectre.admin.batchtask.parse.ParseResult;
import com.xhqb.spectre.admin.model.dto.BatchTaskParamDTO;
import com.xhqb.spectre.admin.model.vo.batchtask.ParamItemVO;
import com.xhqb.spectre.admin.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 验证结果合并器
 *
 * <AUTHOR>
 * @date 2022/7/27
 */
public class ValidateResultMerger {

    /**
     * 总数据
     */
    private Integer total;
    /**
     * 有效数量列表
     */
    private List<ContentItem> validList;
    /**
     * 无效数量列表
     */
    private List<ContentItem> badList;
    /**
     * 重复数量列表
     */
    private List<ContentItem> repeatList;
    /**
     * 缺少参数数量列表
     */
    private List<ContentItem> missList;

    /**
     * cid数据与手机号映射关系
     */
    private Map<String, String> cidMobileMapping;

    /**
     * 空号数据
     */
    private List<ContentItem> phoneEmptyList;
    /**
     * 停机数据
     */
    private List<ContentItem> phoneHaltList;

    /**
     * 短信参数列表(保存至数据库的短信参数信息)
     */
    private BatchTaskParamDTO batchTaskParamDTO;

    /**
     * 验证结果数据合并
     *
     * @param validateResultList
     * @param parseResultList
     * @return
     */
    public ValidateResult merge(List<ValidateResult> validateResultList, List<ParseResult> parseResultList) {
        if (validateResultList.size() == BatchTaskConstants.Commons.NUMBER_ONE) {
            return validateResultList.get(0);
        }
        ValidateResult validateResult1 = validateResultList.get(0);
        ValidateResult validateResult2 = validateResultList.get(1);
        this.beforeMerge(validateResult1, validateResult2);
        ValidateResult validateResult = this.doMerge(validateResult1, validateResult2);
        this.postMerge(validateResultList, parseResultList);
        return validateResult;
    }

    /**
     * 合并初始化
     *
     * @param validateResult1
     * @param validateResult2
     */
    private void beforeMerge(ValidateResult validateResult1, ValidateResult validateResult2) {
        // 总数据
        this.total = computeValidateTotalSize(validateResult1.getTotal(), validateResult2.getTotal());
        // 有效数量列表
        this.validList = new ArrayList<>(computeValidateListSize(validateResult1.getValidList(), validateResult2.getValidList()));
        // 无效数量列表
        this.badList = new ArrayList<>(computeValidateListSize(validateResult1.getBadList(), validateResult2.getBadList()));
        // 重复数量列表
        this.repeatList = new ArrayList<>(computeValidateListSize(validateResult1.getRepeatList(), validateResult2.getRepeatList()));
        // 缺少参数数量列表
        this.missList = new ArrayList<>(computeValidateListSize(validateResult1.getMissList(), validateResult2.getMissList()));

        // cid数据与手机号映射关系
        this.cidMobileMapping = new ConcurrentHashMap<>(computeCidMobileMappingSize(validateResult1.getCidMobileMapping(), validateResult2.getCidMobileMapping()));

        // 空号数据
        this.phoneEmptyList = new ArrayList<>(computeValidateListSize(validateResult1.getPhoneEmptyList(), validateResult2.getPhoneEmptyList()));
        // 停机数据
        this.phoneHaltList = new ArrayList<>(computeValidateListSize(validateResult1.getPhoneHaltList(), validateResult2.getPhoneHaltList()));

    }

    /**
     * 做数据合并
     *
     * @param validateResult1
     * @param validateResult2
     * @retun
     */
    private ValidateResult doMerge(ValidateResult validateResult1, ValidateResult validateResult2) {
        ValidateResult mergeResult = new ValidateResult();
        // 总数据
        mergeResult.setTotal(this.total);
        // 有效数量列表
        this.mergeList(this.validList, validateResult1.getValidList(), validateResult2.getValidList());
        mergeResult.setValidList(this.validList);
        // 无效数量列表
        this.mergeList(this.badList, validateResult1.getBadList(), validateResult2.getBadList());
        mergeResult.setBadList(this.badList);
        // 重复数量列表
        this.mergeList(this.repeatList, validateResult1.getRepeatList(), validateResult2.getRepeatList());
        mergeResult.setRepeatList(this.repeatList);
        // 缺少参数数量列表
        this.mergeList(this.missList, validateResult1.getMissList(), validateResult2.getMissList());
        mergeResult.setMissList(this.missList);
        // cid数据与手机号映射关系
        this.mergeMap(this.cidMobileMapping, validateResult1.getCidMobileMapping(), validateResult2.getCidMobileMapping());
        mergeResult.setCidMobileMapping(this.cidMobileMapping);
        // 空号数据
        this.mergeList(this.phoneEmptyList, validateResult1.getPhoneEmptyList(), validateResult2.getPhoneEmptyList());
        mergeResult.setPhoneEmptyList(this.phoneEmptyList);
        // 停机数据
        this.mergeList(this.phoneHaltList, validateResult1.getPhoneHaltList(), validateResult2.getPhoneHaltList());
        mergeResult.setPhoneHaltList(this.phoneHaltList);
        return mergeResult;
    }

    /**
     * 合并之后处理
     * <p>
     * 将数据组合成真实的短信内容数据列表(ParamItemVO)
     *
     * @param validateResultList
     * @param parseResultList
     */
    private void postMerge(List<ValidateResult> validateResultList, List<ParseResult> parseResultList) {
        // 短信参数列表(保存至数据库的短信参数信息)
        List<ParamItemVO> paramItemList = new ArrayList<>(this.validList.size());
        ValidateResult validateResult;
        ParseResult parseResult;
        List<ParamItemVO> voList;
        for (int i = 0; i < validateResultList.size(); i++) {
            validateResult = validateResultList.get(i);
            parseResult = parseResultList.get(i);
            voList = this.toParamItemList(validateResult, parseResult);
            if (!CommonUtil.isEmpty(voList)) {
                paramItemList.addAll(voList);
            }
        }

        this.batchTaskParamDTO = new BatchTaskParamDTO();
        batchTaskParamDTO.setFileMd5(parseResultList.get(0).getFileMd5());
        batchTaskParamDTO.setParamList(paramItemList);
    }

    /**
     * 转换成短信发送参数列表
     *
     * @param validateResult
     * @param parseResult
     * @return
     */
    private List<ParamItemVO> toParamItemList(ValidateResult validateResult, ParseResult parseResult) {
        List<ContentItem> validList = validateResult.getValidList();
        if (CommonUtil.isEmpty(validList)) {
            return null;
        }

        List<String> titleList = parseResult.getTitleList();
        boolean isCidType = StringUtils.equalsIgnoreCase(parseResult.getContentType(), BatchTaskConstants.DataType.CID);
        // 转换成参数列表
        return validList.stream().map(s -> {
            ParamItemVO paramItemVO = ParamItemVO.newParamItem();
            paramItemVO.mapping(titleList, s.getParamList());
            if (isCidType) {
                // 如果属于cid类型 则添加手机号码
                paramItemVO.put(BatchTaskConstants.DataType.MOBILE, validateResult.getMobile(s.getContent()));
                // 如果属于cid类型 也需要添加一下完件信息
                paramItemVO.put(BatchTaskConstants.DataType.APPLY_LOAN_RESULT, validateResult.getApplyLoanResult(s.getContent()));
            }
            return paramItemVO;
        }).collect(Collectors.toList());
    }


    /**
     * 计算两个验证结果列表数量
     *
     * @param dataList1
     * @param dataList2
     * @return
     */
    private int computeValidateListSize(List<ContentItem> dataList1, List<ContentItem> dataList2) {
        return CommonUtil.getCollectionSize(dataList1) + CommonUtil.getCollectionSize(dataList2);
    }

    /**
     * 计算cid 映射的数量
     *
     * @param cidMobileMapping1
     * @param cidMobileMapping2
     * @return
     */
    private int computeCidMobileMappingSize(Map<String, String> cidMobileMapping1, Map<String, String> cidMobileMapping2) {
        int count = 0;
        if (Objects.nonNull(cidMobileMapping1)) {
            count += cidMobileMapping1.size();
        }

        if (Objects.nonNull(cidMobileMapping2)) {
            count += cidMobileMapping2.size();
        }
        return count;
    }

    /**
     * 计算验证结果总记录数
     *
     * @param total1
     * @param total2
     * @return
     */
    private int computeValidateTotalSize(Integer total1, Integer total2) {
        int count = 0;
        if (Objects.nonNull(total1)) {
            count += total1;
        }
        if (Objects.nonNull(total2)) {
            count += total2;
        }
        return count;
    }

    /**
     * 合并列表
     *
     * @param source
     * @param dataList1
     * @param dataList2
     */
    private void mergeList(List<ContentItem> source, List<ContentItem> dataList1, List<ContentItem> dataList2) {
        if (!CommonUtil.isEmpty(dataList1)) {
            source.addAll(dataList1);
        }

        if (!CommonUtil.isEmpty(dataList2)) {
            source.addAll(dataList2);
        }
    }

    /**
     * 合并map
     *
     * @param source
     * @param map1
     * @param map2
     */
    private void mergeMap(Map<String, String> source, Map<String, String> map1, Map<String, String> map2) {
        if (Objects.nonNull(map1) && !map1.isEmpty()) {
            source.putAll(map1);
        }

        if (Objects.nonNull(map2) && !map2.isEmpty()) {
            source.putAll(map2);
        }
    }

    public BatchTaskParamDTO getBatchTaskParamDTO() {
        return batchTaskParamDTO;
    }
}
