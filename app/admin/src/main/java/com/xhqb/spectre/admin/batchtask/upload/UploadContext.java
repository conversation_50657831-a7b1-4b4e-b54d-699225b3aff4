package com.xhqb.spectre.admin.batchtask.upload;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.InputStream;

/**
 * 上传文件上下文信息
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadContext {

    /**
     * 文件名称
     */
    String fileName;
    /**
     * 上传的文件流
     */
    InputStream inputStream;
}
