package com.xhqb.spectre.admin.batchtask.send;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;

/**
 * 该消息结果会包含单个发送或者批量发送响应结果
 * 参考 com.xhqb.spectre.api.model.smsresp.BaseSMSResultVO
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
public class CompositeMessageResult<T> implements Serializable {

    /**
     * 返回短信发送请求的请求Id
     */
    private String requestId;

    /**
     * 外部流水线号 OutBizId
     */
    private String outOrderId;

    /**
     * 发送状态（0：接口调用失败；1：接口调用成功； 2：接口下发部分成功； 3：接口下发成功）
     */
    private String sendStatus;

    /**
     * 发送结果描述
     */
    private T smsSendResult;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
