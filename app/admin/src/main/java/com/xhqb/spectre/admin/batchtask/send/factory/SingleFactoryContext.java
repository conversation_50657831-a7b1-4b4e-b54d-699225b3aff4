package com.xhqb.spectre.admin.batchtask.send.factory;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SingleFactoryContext implements Serializable {

    /**
     * 业务应用编码
     */
    private String appCode;
    /**
     * 模板编码
     */
    private String tplCode;
    /**
     * 签名名称
     */
    private String signName;
    /**
     * 手机列表
     */
    private List<String> mobileList;
    /**
     * 参数列表
     */
    private List<List<String>> paramMapList;

    /**
     * 短信类型[通过查看api实现逻辑，短信类型为市场营销时必须填写，其他类型不需要设置值]
     * <p>
     * 参考 SMSMessageManagerServiceImpl.checkTplData
     */
    private String smsCodeType;

    /**
     * 渠道id，如果设置了该参数就不读取模版关联的渠道信息
     */
    private Integer channelAccountId;
    /**
     * 群发任务的ID
     */
    private Integer batchId;
    /**
     * 签名code，为空则使用小花钱包签名
     */
    private String signCode;

    /**
     * 请求ID (可以为空) 如果设置了 将会把该值设置到请求头中去
     */
    private String requestId;

    /**
     * 基于内容发送短信时的模板内容
     */
    private String content;


    /**
     *
     * 用来兼容模版内容中带${}的，如果paramMap有值就认为模版中带[]，paramList有值就认为是${}模版
     *
     * 模板参数
     * map中必须包含phone的key参数，最多支持100个手机号
     */
    private List<Map<String, String>> placeHolderList;

    /**
     * 指定调用那个api接口
     */
    private String api;
}
