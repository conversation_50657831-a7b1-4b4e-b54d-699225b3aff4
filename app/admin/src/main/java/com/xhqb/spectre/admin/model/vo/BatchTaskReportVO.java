package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.BatchTaskReportDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 群发报告
 *
 * <AUTHOR>
 * @date 2021/10/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BatchTaskReportVO implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 批次号
     */
    private Integer taskId;

    /**
     * 参数ID
     */
    private Integer taskParamId;

    /**
     * 耗时
     */
    private Integer cost;

    /**
     * 发送总数
     */
    private Integer total;

    /**
     * 成功数
     */
    private Integer success;

    /**
     * 失败数
     */
    private Integer fail;

    /**
     * 未知数量
     */
    private Integer unknown;

    /**
     * 时间统计
     */
    private String timeStat;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 错误信息
     */
    private String errorStat;


    /**
     * 查询数据详情展现
     *
     * @param batchTaskReportDO
     * @return
     */
    public static BatchTaskReportVO buildInfoQuery(BatchTaskReportDO batchTaskReportDO) {
        return BatchTaskReportVO.builder()
                // 主键
                .id(batchTaskReportDO.getId())
                // 批次号
                .taskId(batchTaskReportDO.getTaskId())
                // 参数ID
                .taskParamId(batchTaskReportDO.getTaskParamId())
                // 耗时
                .cost(batchTaskReportDO.getCost())
                // 发送总数
                .total(batchTaskReportDO.getTotal())
                // 成功数
                .success(batchTaskReportDO.getSuccess())
                // 失败数
                .fail(batchTaskReportDO.getFail())
                // 未知数量
                .unknown(batchTaskReportDO.getUnknown())
                // 时间统计
                .timeStat(batchTaskReportDO.getTimeStat())
                // 创建时间
                .createTime(batchTaskReportDO.getCreateTime())
                // 更新时间
                .updateTime(batchTaskReportDO.getUpdateTime())
                // 错误信息
                .errorStat(batchTaskReportDO.getErrorStat())
                .build();
    }
}
