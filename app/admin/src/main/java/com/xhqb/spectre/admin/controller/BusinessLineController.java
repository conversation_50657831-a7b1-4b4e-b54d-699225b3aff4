package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.dto.BusinessLineDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.BusinessLineVO;
import com.xhqb.spectre.admin.service.BusinessLineService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.BusinessLineQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 业务线管理
 *
 * <AUTHOR>
 * @date 2022/9/23
 */
@RestController
@RequestMapping("/businessLine")
@Slf4j
public class BusinessLineController {

    @Resource
    private BusinessLineService businessLineService;

    /**
     * 业务线列表展示
     *
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(BusinessLineQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<BusinessLineVO> commonPager = businessLineService.listByPage(query);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询业务线详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult detail(@PathVariable("id") Integer id) {
        return AdminResult.success(businessLineService.getById(id));
    }

    /**
     * 新增业务线
     *
     * @param businessLineDTO
     * @return
     */
    @PostMapping("")
    public AdminResult create(@RequestBody BusinessLineDTO businessLineDTO) {
        log.info("新增业务线请求 = {}", businessLineDTO);
        businessLineService.create(businessLineDTO);
        return AdminResult.success();
    }

    /**
     * 更新业务线
     *
     * @param id
     * @param businessLineDTO
     * @return
     */
    @PutMapping("/{id}")
    public AdminResult updateApp(@PathVariable("id") Integer id, @RequestBody BusinessLineDTO businessLineDTO) {
        log.info("更新业务线请求 = {}, id = {}", businessLineDTO, id);
        businessLineService.update(id, businessLineDTO);
        return AdminResult.success();
    }

    /**
     * 启用业务线
     *
     * @param id
     * @return
     */
    @PostMapping("/enable/{id}")
    public AdminResult enableProjectDesc(@PathVariable("id") Integer id) {
        log.info("启用业务线请求 = {}", id);
        businessLineService.status(id, 1);
        return AdminResult.success();
    }

    /**
     * 停用业务线
     *
     * @param id
     * @return
     */
    @PostMapping("/disable/{id}")
    public AdminResult disableProjectDesc(@PathVariable("id") Integer id) {
        log.info("停用业务线请求 = {}", id);
        businessLineService.status(id, 0);
        return AdminResult.success();
    }

    /**
     * 删除业务线
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public AdminResult deleteProjectDesc(@PathVariable("id") Integer id) {
        log.info("删除业务线请求 = {}", id);
        businessLineService.deleteById(id);
        return AdminResult.success();
    }

    /**
     * 下拉列表展示业务线
     *
     * @return
     */
    @GetMapping("/enum")
    public AdminResult queryEnum() {
        return AdminResult.success(businessLineService.listAll(1));
    }
}
