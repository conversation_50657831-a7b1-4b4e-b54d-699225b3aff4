package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.constant.Apis;
import com.xhqb.spectre.admin.model.dto.UserShortUrlRequest;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.result.ShortCodeResult;
import com.xhqb.spectre.admin.model.vo.UserShortUrlResponse;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class ObtainShortCodeService {

    @Resource
    private VenusConfig venusConfig;

    @Resource
    private RestTemplate restTemplate;

    public List<UserShortUrlResponse> handler(UserShortUrlRequest request) {
        log.info("obtainShortCode request:{}", JsonLogUtil.toJSONString(request));
        ShortCodeResult shortCodeResult = obtainShortCode(request);
        log.info("obtainShortCode response:{}", JsonLogUtil.toJSONString(shortCodeResult));
        return JSON.parseObject(JSON.toJSONString(shortCodeResult.getData()), new TypeReference<List<UserShortUrlResponse>>() {
        });
    }


    @SneakyThrows
    public ShortCodeResult obtainShortCode(UserShortUrlRequest request) {
        HttpEntity<UserShortUrlRequest> requestEntity = new HttpEntity<>(request);
        ResponseEntity<ShortCodeResult> response = restTemplate.exchange(this.getShortCodeUrlApi(), HttpMethod.POST, requestEntity, ShortCodeResult.class);
        log.info(" restTemplate response:{}", JsonLogUtil.toJSONString(response));
        if (!Objects.equals(response.getStatusCode(), HttpStatus.OK)) {
            return response.getBody();
        }

        String s = JSON.toJSONString(response.getBody());
        log.info("restTemplate response body :{}", s);
        return JSON.parseObject(s, new TypeReference<ShortCodeResult>() {
        });
    }

    private String getShortCodeUrlApi() {
        return venusConfig.getSpectreUrlShortenerHost() + Apis.ShortUrlApi.SHORT_CODE_URL;
    }
}
