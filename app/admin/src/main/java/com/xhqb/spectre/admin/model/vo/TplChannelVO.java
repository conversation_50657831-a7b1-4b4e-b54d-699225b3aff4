package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.TplChannelDO;
import com.xhqb.spectre.common.dal.entity.support.TplChannelDOSupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/15 17:50
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplChannelVO implements Serializable {

    private static final long serialVersionUID = -982853368542973783L;

    /**
     * 渠道账号ID
     */
    private Integer channelAccountId;
    /**
     * 渠道名称
     */
    private String channelAccountName;

    /**
     * 状态，1：通过；2：驳回
     */
    private Integer status;

    /**
     * 渠道模板ID
     */
    private String channelTplId;

    /**
     * 运营商
     */
    private List<String> ispList;

    /**
     * 地域过滤类型，1：包含；2：不包含
     */
    private Integer areaFilterType;

    /**
     * 地域列表
     */
    private List<AreaVO> areaList;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 备注
     */
    private String remark;

    /**
     * 渠道定制模板内容
     */
    private String tplContent;

    public static TplChannelVO buildTplChannelVO(TplChannelDO item) {
        return TplChannelVO.builder()
                .channelAccountId(item.getChannelAccountId())
                .status(item.getStatus())
                .channelTplId(item.getChannelTplId())
                .ispList(CommonUtil.ispToList(item.getIsps()))
                .areaFilterType(item.getAreaFilterType())
                .areaList(CommonUtil.areaToList(item.getAreas()))
                .weight(item.getWeight())
                .remark(item.getRemark())
                .tplContent(item.getTplContent())
                .build();
    }

    public static TplChannelVO buildTplChannelVOSupport(TplChannelDOSupport item) {
        return TplChannelVO.builder()
                .channelAccountId(item.getChannelAccountId())
                .channelAccountName(item.getChannelAccountName())
                .status(item.getStatus())
                .channelTplId(item.getChannelTplId())
                .ispList(CommonUtil.ispToList(item.getIsps()))
                .areaFilterType(item.getAreaFilterType())
                .areaList(CommonUtil.areaToList(item.getAreas()))
                .weight(item.getWeight())
                .remark(item.getRemark())
                .tplContent(item.getTplContent())
                .build();
    }
}
