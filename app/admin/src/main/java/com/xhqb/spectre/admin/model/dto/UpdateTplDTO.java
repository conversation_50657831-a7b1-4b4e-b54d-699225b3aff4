package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 17:57
 * @Description:
 */
@Data
public class UpdateTplDTO extends TplDTO {

    private static final long serialVersionUID = 8219230645609177126L;

    private Integer id;

    @NotEmpty(message = "渠道信息列表不能为空")
    private List<TplChannelDTO> channelInfoList;

    @NotNull(message = "模板屏蔽信息列表不能为null")
    private List<TplDisableDTO> disableInfoList;

    private List<String> gatewayTplContentList;
}
