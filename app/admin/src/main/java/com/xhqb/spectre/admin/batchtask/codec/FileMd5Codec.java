package com.xhqb.spectre.admin.batchtask.codec;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;

import java.io.InputStream;
import java.security.MessageDigest;

/**
 * 文件md5值获取
 *
 * <AUTHOR>
 * @date 2021/9/21
 */
@Slf4j
public class FileMd5Codec {
    /**
     * 加密算法名称
     */
    private static final String ALGORITHM = "MD5";

    /**
     * 文件md5编码操作
     *
     * @param inputStream 待编码的文件输入流
     * @param fileName    文件名称
     * @return 返回文件md5值
     * @throws Exception
     */
    public static String encode(InputStream inputStream, String fileName) throws Exception {
        long start = System.currentTimeMillis();
        try {
            MessageDigest messageDigest = MessageDigest.getInstance(ALGORITHM);
            byte[] buffer = new byte[8192];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                messageDigest.update(buffer, 0, length);
            }
            inputStream.close();
            return new String(Hex.encodeHex(messageDigest.digest()));
        } finally {
            log.info("文件md5编码操作耗时 = {}, fileName = {}", (System.currentTimeMillis() - start), fileName);
        }
    }
}
