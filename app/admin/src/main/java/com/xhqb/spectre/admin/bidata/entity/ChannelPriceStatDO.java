package com.xhqb.spectre.admin.bidata.entity;

import lombok.Data;

/**
 * 供应商纵向触达统计
 */
@Data
public class ChannelPriceStatDO {
    /**
     * 统计日期
     * 月份
     */
    private String statDate;
    /**
     * 渠道编码
     */
    private String channelCode;

    private Integer verifyReachBillCount;
    private Integer notifyReachBillCount;
    private Integer marketReachBillCount;
    private Integer collectorReachBillCount;
    private Integer lightCollectorReachBillCount;
    private Integer severeCollectorReachBillCount;
    private Integer debtSwapReachBillCount;

    private Double verifyReachRate;
    private Double notifyReachRate;
    private Double marketReachRate;
    private Double collectorReachRate;
    private Double lightCollectorReachRate;
    private Double severeCollectorReachRate;
    private Double debtSwapReachRate;

    private Integer verifyPriceCount;
    private Integer notifyPriceCount;
    private Integer marketPriceCount;
    private Integer collectorPriceCount;
    private Integer lightCollectorPriceCount;
    private Integer severeCollectorPriceCount;
    private Integer debtSwapPriceCount;

    /**
     * 总费用
     */
    private Long totalPriceCount;

    /**
     *  平均触达率
     */
    private Double avgReachRate;

    /**
     * 总计费条数量
     */
    private Integer totalReachBillCount;
}
