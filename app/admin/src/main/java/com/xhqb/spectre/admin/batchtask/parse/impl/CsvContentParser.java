package com.xhqb.spectre.admin.batchtask.parse.impl;

import com.xhqb.spectre.admin.batchtask.parse.*;
import com.xhqb.spectre.admin.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.List;

/**
 * CSV文件内容解析器
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
@Component
public class CsvContentParser extends AbstractContentParser {
    /**
     * 字符编码
     */
    private static final String CHARSET = "UTF-8";
    /**
     * 分隔符
     */
    private static final String SEPARATOR = ",";
    /**
     * csv文件后缀名
     */
    private static final String[] CSV_SUFFIX = {".csv"};

    /**
     * 获取到支持解析的文件后缀名
     *
     * @return
     */
    @Override
    protected String[] getSupportSuffix() {
        return CSV_SUFFIX;
    }

    /**
     * 真正做内容解析操作，并将处理结果写入到ParseResult对象中
     *
     * @param context
     * @param result
     * @return
     * @throws Exception
     */
    @Override
    protected List<ParseResult> doParse(ParseContext context, ParseResult result) throws Exception {
        List<ParseResult> parseResultList = null;
        try (InputStream inputStream = context.getInputStream()) {
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, CHARSET));
            String s = bufferedReader.readLine();
            if (StringUtils.isBlank(s)) {
                throw new BizException("csv文件没有设置头部信息");
            }
            // 解析第一行作为头部信息
            String[] titleList = s.split(SEPARATOR);
            ResultGroupFactory resultGroupFactory = new ResultGroupFactory(result, Arrays.asList(titleList));
            // 进行下一行读取
            s = bufferedReader.readLine();
            // 列表内容
            List<String> columnList;
            while (s != null) {
                // 内容解析
                columnList = Arrays.asList(s.split(SEPARATOR));
                resultGroupFactory.parseContent(columnList);
                // 进行下一行读取
                s = bufferedReader.readLine();
            }
            parseResultList = resultGroupFactory.getParseResultList();
        }
        return parseResultList;
    }
}
