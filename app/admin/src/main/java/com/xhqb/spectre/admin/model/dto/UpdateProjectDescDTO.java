package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class UpdateProjectDescDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Integer id;

    @NotBlank(message = "项目用途不能为空")
    @Size(max = 32, message = "项目用途最大为{max}个字符")
    private String description;
}
