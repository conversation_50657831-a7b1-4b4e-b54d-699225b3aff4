package com.xhqb.spectre.admin.batchtask.send.verify;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 验证码响应
 * <p>
 * {
 * "code": 200,
 * "msg": "success",
 * "date": "2021-10-14 18:17:12",
 * "data": {
 * "identificationCode": "20211014000000014000",
 * "requestId": "1014181712010633500102",
 * "tplCode": "KF_YZM_200076",
 * "sendCode": "200",
 * "sendMsg": "success"
 * }
 * }
 *
 * <AUTHOR>
 * @date 2021/10/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VerifyCodeResponse implements Serializable {

    private String identificationCode;

    /**
     * 返回短信发送请求的请求Id
     */
    private String requestId;

    private String tplCode;

    private String sendCode;

    private String sendMsg;
}
