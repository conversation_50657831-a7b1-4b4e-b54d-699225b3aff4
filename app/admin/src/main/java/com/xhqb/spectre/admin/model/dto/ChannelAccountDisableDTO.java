package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.admin.constant.CommonConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/22 17:43
 * @Description:
 */
@Data
public class ChannelAccountDisableDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private Integer channelAccountId;

    @NotBlank(message = "开始时间不能为空")
    @Pattern(regexp = CommonConstant.PATTERN_DATE_TIME, message = "开始时间格式为" + CommonConstant.DATE_TIME_FORMAT)
    private String startTime;

    @NotBlank(message = "结束时间不能为空")
    @Pattern(regexp = CommonConstant.PATTERN_DATE_TIME, message = "结束时间格式为" + CommonConstant.DATE_TIME_FORMAT)
    private String endTime;

    @NotEmpty(message = "运营商不能为空")
    private List<String> ispList;

    @NotEmpty(message = "地域不能为空")
    private List<AreaDTO> areaList;

    /**
     * 屏蔽类型：1-整个时间段屏蔽（原功能），2-固定时间段屏蔽（新功能）
     */
    private Integer disableType;

    /**
     * 时间段开始时间（格式：HH:mm，如 10:30）
     */
    private String periodStartTime;

    /**
     * 时间段结束时间（格式：HH:mm，如 12:00）
     */
    private String periodEndTime;

    /**
     * 备注信息（屏蔽原因等）
     */
    private String remark;
}
