package com.xhqb.spectre.admin.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.*;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.TplExcelVO;
import com.xhqb.spectre.admin.model.vo.TplOpUpdateRecordVO;
import com.xhqb.spectre.admin.model.vo.TplOperationVO;
import com.xhqb.spectre.admin.model.vo.TplVO;
import com.xhqb.spectre.admin.service.TplService;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.entity.TplOpRecordDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.TplChangeRecordsQuery;
import com.xhqb.spectre.common.dal.query.TplQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/15 17:27
 * @Description:
 */
@RestController
@RequestMapping("/tpl")
@Slf4j
public class TplController {

    @Autowired
    private TplService tplService;

    /**
     * 模板列表查询
     *
     * @param tplQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryTplList(@ModelAttribute TplQuery tplQuery, Integer pageNum, Integer pageSize) {
        tplQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<TplVO> commonPager = tplService.listByPage(tplQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询模板详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryTplInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(tplService.getById(id));
    }

    /**
     * 添加模板
     *
     * @param addTplDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_TPL)
    public AdminResult createTpl(@RequestBody AddTplDTO addTplDTO) {
        log.info("addTplDTO: {}", JSON.toJSONString(addTplDTO));
        tplService.create(addTplDTO);
        return AdminResult.success();
    }

    /**
     * 编辑模板
     *
     * @param id
     * @param updateTplDTO
     * @return
     */
    @PutMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_TPL)
    public AdminResult updateTpl(@PathVariable("id") Integer id, @RequestBody UpdateTplDTO updateTplDTO) {
        updateTplDTO.setId(id);
        log.info("updateTplDTO: {}", JSON.toJSONString(updateTplDTO));
        tplService.update(updateTplDTO);
        return AdminResult.success();
    }

    /**
     * 启用模板
     *
     * @param id
     * @return
     */
    @PostMapping("/enable/{id}")
    @LogOpTime(OpLogConstant.MODULE_TPL)
    public AdminResult enableTpl(@PathVariable("id") Integer id) {
        log.info("enableTpl, id: {}", id);
        tplService.enable(id);
        return AdminResult.success();
    }

    /**
     * 停用模板
     *
     * @param id
     * @return
     */
    @PostMapping("/disable/{id}")
    @LogOpTime(OpLogConstant.MODULE_TPL)
    public AdminResult disableTpl(@PathVariable("id") Integer id) {
        log.info("disableTpl, id: {}", id);
        tplService.disable(id);
        return AdminResult.success();
    }

    /**
     * 删除模板
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_TPL)
    public AdminResult deleteTpl(@PathVariable("id") Integer id) {
        log.info("deleteTpl, id: {}", id);
        tplService.delete(id);
        return AdminResult.success();
    }

    /**
     * 查询模板枚举
     *
     * @param code
     * @param title
     * @param status
     * @param signId
     * @return
     */
    @GetMapping("/enum")
    public AdminResult queryTplEnum(String code, String title, Integer status, Integer signId) {
        return AdminResult.success(tplService.queryEnum(code, title, status, signId));
    }

    /**
     * 模板测试
     *
     * @param id
     * @param tplTestDTO
     * @return
     */
    @PostMapping("/test/{id}")
    public AdminResult testTpl(@PathVariable("id") Integer id, @RequestBody TplTestDTO tplTestDTO) {
        log.info("testTpl, id: {}, testTplDTO: {}", id, JSON.toJSONString(tplTestDTO));
        List<String> failMobileList = tplService.test(id, tplTestDTO);
        Map<String, Object> sendFailMap = Maps.newHashMap();
        sendFailMap.put("sendFail", failMobileList);
        return AdminResult.success(sendFailMap);
    }

    /**
     * 导出活跃模版数据
     *
     * @param response
     * @return
     * @throws IOException
     */
    @GetMapping("/downloadActive")
    public void downloadActive(HttpServletResponse response, TplQuery tplQuery) throws IOException {
        List<TplExcelVO> tplExcelVOS = tplService.downLoadActive(tplQuery);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "activeTpl";
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), TplExcelVO.class).sheet("活跃模版数据").doWrite(tplExcelVOS);
    }

    /**
     * 批量修改渠道信息
     *
     * @param batchTplChannelDTO 请求体  携带批量id  和 请求参数
     * @return
     */
    @PostMapping("/batchUpdateChannel")
    @LogOpTime(OpLogConstant.MODULE_TPL)
    public AdminResult updateByTplIdList(@RequestBody BatchTplChannelDTO batchTplChannelDTO) {
        log.info("updateTplDTO: {}", batchTplChannelDTO);
        return tplService.updateByTplIdList(batchTplChannelDTO);
    }


    /**
     * 批量上报模板使用情况
     *
     * @param batchReportDTO 批量上报的数据传输对象，包含需要上报的模板信息
     * @return CommonResult<String> 包含操作结果的通用结果对象，返回值为上报操作的结果信息
     */
    @PostMapping("/batchReport")
    public CommonResult<String> batchReport(@RequestBody BatchReportDTO batchReportDTO) {
        log.info("batchReportDTO: {}", batchReportDTO);
        ValidatorUtil.validate(batchReportDTO);
        return CommonResult.success(tplService.batchReport(batchReportDTO));
    }

    /**
     * 查询变更记录分页列表
     *
     * @param query    查询条件，使用@ModelAttribute注解绑定请求参数到TplChangeRecordsQuery对象
     * @param pageNum  当前页码，用于分页查询
     * @param pageSize 每页显示记录数，用于分页查询
     * @return CommonResult<CommonPager < TplOpRecordDO>> 包含查询结果的CommonResult对象，
     * 结果类型为CommonPager<TplOpRecordDO>，其中CommonPager封装了分页信息和记录列表
     */
    @GetMapping("/queryChangeRecords")
    public CommonResult<CommonPager<TplOpRecordDO>> queryChangeRecords(@ModelAttribute TplChangeRecordsQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        ValidatorUtil.validate(query);
        log.info("变更记录分页列表: {}", JSON.toJSONString(query));
        return CommonResult.success(tplService.queryChangeRecords(query));
    }

    /**
     * 更新操作类型 --- 查看
     *
     * @param query 查询条件，使用@ModelAttribute注解绑定请求参数到TplChangeRecordsQuery对象
     * @return CommonResult<TplOpUpdateRecordVO> 包含查询结果的CommonResult对象，结果类型为TplOpUpdateRecordVO的列表
     */
    @GetMapping("/queryUpdateRecords")
    public CommonResult<TplOpUpdateRecordVO> queryUpdateRecords(@ModelAttribute TplChangeRecordsQuery query) {
        log.info("更新记录查看请求参数: {}", JSON.toJSONString(query));
        ValidatorUtil.validate(query);
        return CommonResult.success(tplService.queryUpdateRecords(query));
    }

    /**
     * 导出模板数据
     *
     * @param exportTplDTO 导出模板数据传输对象
     * @return 导chu模板数据的操作记录
     */
    @PostMapping("/exportTpl")
    public CommonResult<List<TplOperationVO>> exportTpl(@RequestBody ExportTplDTO exportTplDTO) {
        log.info("exportTpl:exportTplDTO: {}", JSON.toJSONString(exportTplDTO));
        return CommonResult.success(tplService.exportTpl(exportTplDTO));
    }

    @PostMapping("/importTplByJson")
    public CommonResult<Object> importTplByJson(@RequestBody List<TplOperationVO> tplOperationVOList) {
        return CommonResult.success(tplService.importTplByJson(tplOperationVOList));

    }

    @GetMapping("/deleteTplAvailableChannel")
    public CommonResult<Object> deleteTplAvailableChannel() {
        return CommonResult.success(tplService.deleteTplAvailableChannel());
    }



    /**
     * 根据模板ID批量更新模板下的渠道权重
     * @param channelWeightsDTO
     * @return
     */
    @PostMapping("/update-channel-weights")
    @LogOpTime(OpLogConstant.MODULE_TPL)
    public CommonResult<Void> batchUpdateTplChannelWeights(@RequestBody ChannelWeightsDTO channelWeightsDTO) {
        log.info("更新模板权重, channelWeightsDTO={}", channelWeightsDTO);
        tplService.batchUpdateTplChannelWeights(channelWeightsDTO.getTplId(), channelWeightsDTO.getTplChannelWeights());
        return CommonResult.success();
    }

}
