package com.xhqb.spectre.admin.batchtask.detect;

import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.parse.ContentItem;
import com.xhqb.spectre.admin.batchtask.parse.ParseResult;
import com.xhqb.spectre.admin.batchtask.validate.ValidateResult;
import com.xhqb.spectre.admin.service.BatchDetectService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.BatchDetectDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 群发任务结构检测保存处理
 *
 * <AUTHOR>
 * @date 2022/2/22
 */
@Component
@Slf4j
public class BatchDetectSaveHandler {


    @Resource
    private BatchDetectService batchDetectService;

    /**
     * 保存探测结果处理
     * <p>
     * 主要是将检测到不合法的数据保存到t_batch_detect表中，
     * 为文件检测列表下载提供数据
     *
     * @param validateResult
     * @param parseResult
     */
    public void save(ValidateResult validateResult, ParseResult parseResult) {
        String fileMd5 = parseResult.getFileMd5();
        String fileName = parseResult.getFileName();
        long start = System.currentTimeMillis();
        try {
            // 具体保存处理方法
            this.doSave(validateResult, parseResult);
        } catch (Exception e) {
            // 保存失败不影响群发的正常处理流程
            log.error("保存探测结果失败,fileMd5 ={},fileName = {}", fileMd5, fileName, e);
        }
        log.info("保存探测结果耗时={},fileMd5={}, fileName ={}", (System.currentTimeMillis() - start), fileMd5, fileName);
    }

    /**
     * 具体保存处理方法
     *
     * @param validateResult
     * @param parseResult
     */
    private void doSave(ValidateResult validateResult, ParseResult parseResult) {
        // 文件md5
        String fileMd5 = parseResult.getFileMd5();
        List<BatchDetectDO> batchDetectList = this.buildDetectList(validateResult, fileMd5);
        // 数据保存
        batchDetectService.batchInsert(batchDetectList);
    }

    /**
     * 构建检测结果列表
     *
     * @param validateResult
     * @param fileMd5
     * @return
     */
    private List<BatchDetectDO> buildDetectList(ValidateResult validateResult, String fileMd5) {
        // 无效cid
        List<ContentItem> badList = validateResult.getBadList();
        // 空号数
        List<ContentItem> phoneEmptyList = validateResult.getPhoneEmptyList();
        // 停机数
        List<ContentItem> phoneHaltList = validateResult.getPhoneHaltList();
        // 参数漏填
        List<ContentItem> missList = validateResult.getMissList();
        // 参数重复
        List<ContentItem> repeatList = validateResult.getRepeatList();

        // 检测结果列表
        List<BatchDetectDO> batchDetectList = this.newDetectList(badList, phoneEmptyList, phoneHaltList, missList, repeatList);

        // 无效cid
        this.doGenDetect(badList, BatchTaskConstants.DetectType.BAD_TYPE, "状态不合法", fileMd5, batchDetectList);
        // 空号数
        this.doGenDetect(phoneEmptyList, BatchTaskConstants.DetectType.PHONE_EMPTY_TYPE, "空号", fileMd5, batchDetectList);
        // 停机数
        this.doGenDetect(phoneHaltList, BatchTaskConstants.DetectType.PHONE_HALT_TYPE, "停机", fileMd5, batchDetectList);
        // 参数漏填
        this.doGenDetect(missList, BatchTaskConstants.DetectType.MISS_TYPE, "参数缺失", fileMd5, batchDetectList);
        // 数据重复
        this.doGenDetect(repeatList, BatchTaskConstants.DetectType.REPEAT_TYPE, "数据重复", fileMd5, batchDetectList);

        return batchDetectList;
    }


    /**
     * 生成detect结果
     *
     * @param itemList
     * @param detectType
     * @param remark
     * @param fileMd5
     * @param batchDetectList
     */
    private void doGenDetect(List<ContentItem> itemList, int detectType, String remark, String fileMd5, List<BatchDetectDO> batchDetectList) {
        if (CommonUtil.isEmpty(itemList)) {
            return;
        }

        BatchDetectDO detectDO;
        for (ContentItem item : itemList) {
            detectDO = this.build(item.getContent(), fileMd5, detectType, remark);
            batchDetectList.add(detectDO);
        }

    }

    /**
     * 创建detect容器
     *
     * @param lists
     * @return
     */
    private List<BatchDetectDO> newDetectList(List<ContentItem>... lists) {
        int size = 0;
        for (List<ContentItem> list : lists) {
            if (!CommonUtil.isEmpty(list)) {
                size += list.size();
            }
        }
        return new ArrayList<>(size);
    }

    /**
     * 构建detect数据对象
     *
     * @param content
     * @param fileMd5
     * @param detectType
     * @param remark
     * @return
     */
    private BatchDetectDO build(String content, String fileMd5, Integer detectType, String remark) {
        BatchDetectDO detectDO = new BatchDetectDO();
        detectDO.setContent(content);
        detectDO.setFileMd5(fileMd5);
        detectDO.setType(detectType);
        detectDO.setRemark(remark);
        return detectDO;
    }
}
