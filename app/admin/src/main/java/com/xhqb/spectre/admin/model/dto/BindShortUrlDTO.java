package com.xhqb.spectre.admin.model.dto;

import lombok.Data;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.Size;
import java.io.Serializable;


@Data
public class BindShortUrlDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * appid
     */
    private String id;

    /**
     * 小花短地址, 新创建则不传此参数
     */
    @Size(max = 512, message = "短地址最大为{max}个字符")
    @URL(message = "短地址格式有误")
    private String shortUrl;
}
