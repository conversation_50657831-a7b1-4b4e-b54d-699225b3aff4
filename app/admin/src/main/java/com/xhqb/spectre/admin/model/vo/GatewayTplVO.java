package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.support.TplDOSupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/26 18:52
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GatewayTplVO implements Serializable {

    private static final long serialVersionUID = 2005643555449055928L;

    /**
     * 模板ID
     */
    private Integer id;

    /**
     * 模板编码
     */
    private String code;

    /**
     * 模板名称
     */
    private String title;

    /**
     * 签名名称
     */
    private String signName;

    public static GatewayTplVO build(TplDOSupport item) {
        return GatewayTplVO.builder()
                .id(item.getId())
                .code(item.getCode())
                .title(item.getTitle())
                .signName(item.getSignName())
                .build();
    }
}
