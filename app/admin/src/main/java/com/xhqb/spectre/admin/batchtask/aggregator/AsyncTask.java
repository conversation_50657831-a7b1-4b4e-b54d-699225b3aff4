package com.xhqb.spectre.admin.batchtask.aggregator;

import com.xhqb.spectre.admin.batchtask.parse.ContentItem;
import com.xhqb.spectre.admin.batchtask.validate.ValidateContext;
import com.xhqb.spectre.admin.batchtask.validate.ValidateResult;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 聚合的异步任务
 *
 * <AUTHOR>
 * @date 2021/9/28
 */
@Slf4j
public class AsyncTask implements Runnable {

    /**
     * 锁标记
     */
    private CountDownLatch latch;
    /**
     * 待聚合的数据
     */
    private List<ContentItem> contentItemList;

    /**
     * 验证数据上下文信息
     */
    private ValidateContext validateContext;
    /**
     * 验证结果
     */
    private ValidateResult validateResult;

    /**
     * 聚合结果
     */
    private AggregatorResult aggregatorResult;
    /**
     * 异步处理方法
     */
    private AsyncTaskHandler asyncTaskHandler;

    /**
     * 异步任务
     * <p>
     * 有参数必填,不能够设置为null
     *
     * @param latch
     * @param contentItemList
     * @param validateContext
     * @param validateResult
     * @param aggregatorResult
     * @param asyncTaskHandler
     */
    public AsyncTask(CountDownLatch latch, List<ContentItem> contentItemList, ValidateContext validateContext, ValidateResult validateResult, AggregatorResult aggregatorResult, AsyncTaskHandler asyncTaskHandler) {
        this.latch = latch;
        this.contentItemList = contentItemList;
        this.validateContext = validateContext;
        this.validateResult = validateResult;
        this.aggregatorResult = aggregatorResult;
        this.asyncTaskHandler = asyncTaskHandler;
    }

    @Override
    public void run() {
        try {
            this.asyncTaskHandler.asyncTaskHandler(contentItemList, validateContext, validateResult, aggregatorResult);
        } finally {
            latch.countDown();
        }
    }

    /**
     * 异步任务处理
     */
    public interface AsyncTaskHandler {

        /**
         * 异步任务处理方法
         *
         * @param contentItemList
         * @param validateContext
         * @param validateResult
         * @param aggregatorResult
         */
        void asyncTaskHandler(List<ContentItem> contentItemList, ValidateContext validateContext, ValidateResult validateResult, AggregatorResult aggregatorResult);
    }

}
