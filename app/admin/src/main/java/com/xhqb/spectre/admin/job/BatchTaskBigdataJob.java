package com.xhqb.spectre.admin.job;

import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.google.common.collect.Lists;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.batchtask.BatchTaskFactory;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.io.UploadFileUtils;
import com.xhqb.spectre.admin.batchtask.result.impl.QueryUploadResultHandler;
import com.xhqb.spectre.admin.batchtask.strategy.FileLoadContext;
import com.xhqb.spectre.admin.batchtask.upload.cos.S3Helper;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.model.dto.BatchTaskDTO;
import com.xhqb.spectre.admin.model.vo.batchtask.CheckResultVO;
import com.xhqb.spectre.admin.model.vo.batchtask.QueryTaskSegmentVO;
import com.xhqb.spectre.admin.model.vo.batchtask.UploadQueryVO;
import com.xhqb.spectre.admin.service.BatchTaskService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.BatchTaskBigdataDO;
import com.xhqb.spectre.common.dal.entity.BatchTaskDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.mapper.BatchTaskBigdataMapper;
import com.xhqb.spectre.common.dal.mapper.BatchTaskMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import com.xhqb.spectre.common.enums.BatchTaskBigdataStatusEnum;
import com.xhqb.spectre.common.enums.BatchTaskStatusEnum;
import com.xhqb.spectre.common.enums.SendTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 大数据批量任务处理
 * <p>
 * 1. job定时(暂定60s)扫描t_batch_task_bigdata
 * 2. 优先处理状态为正在处理的数据
 * 3. 处理状态为未处理的数据
 * 4. 保存短信群发任务
 * 5. 更新t_batch_task_bigdata状态
 *
 * <AUTHOR>
 * @date 2021/10/28
 */
@Component
@Job("batchTaskBigdataJob")
@Slf4j
public class BatchTaskBigdataJob implements SimpleJob {

    /**
     * 备注
     */
    private static final String REMARK = "大数据批量任务";
    /**
     * 一次扫描10个
     */
    private static final int BATCH_SCAN_COUNT = 10;
    /**
     * 请求ID
     */
    private static final String REQUEST_ID_KEY = "requestId";
    /**
     * 24小时为判定大数据任务处理超时,需要更新大数据任务状态为超时状态
     */
    private static final int TIMEOUT_MILLS = 86400000;
    /**
     * 重试查询的次数，如果超过指定的查询次数 则等待下一次job进行执行
     */
    private static final int RETRY_QUERY_COUNT = 120;
    /**
     * 最大的消息长度
     */
    private static final int MAX_MESSAGE_LENGTH = 240;

    @Resource
    private BatchTaskBigdataMapper batchTaskBigdataMapper;
    @Resource
    private TplMapper tplMapper;
    @Resource
    private QueryUploadResultHandler queryUploadResultHandler;
    @Autowired
    private BatchTaskService batchTaskService;
    @Autowired
    private BatchTaskFactory batchTaskFactory;
    @Autowired
    private VenusConfig venusConfig;
    @Autowired
    private S3Helper s3Helper;
    @Resource
    private BatchTaskMapper batchTaskMapper;

    @Override
    public void execute(ShardingContext shardingContext) {
        long start = System.currentTimeMillis();
        log.info("开始准备处理大数据批量任务");
        AtomicInteger counter = new AtomicInteger();
        try {
            doExecute(counter);
        } catch (Exception e) {
            log.error("处理大数据批量任务失败", e);
        }
        log.info("处理大数据批量任务完成,耗时 ={},处理成功的任务数量 = {}", (System.currentTimeMillis() - start), counter.get());
    }

    /**
     * 做job 任务
     *
     * @param counter
     */
    private void doExecute(AtomicInteger counter) {
        // 正在处理的任务
        handlerProcessingTask(counter);
        // 未处理的任务状态
        handlerUnProcessTask(counter);
    }

    /**
     * 处理任务状态为正在处理中的任务数据
     *
     * @param counter
     */
    private void handlerProcessingTask(AtomicInteger counter) {
        // 扫描正在处理的任务
        Integer lastId = 0;
        // 1. 查看最早的正在处理的任务
        BatchTaskBigdataDO batchTaskBigdataDO;
        do {
            // 查询到正在处理的任务
            batchTaskBigdataDO = batchTaskBigdataMapper.selectEarliestOneByStatus(lastId, BatchTaskBigdataStatusEnum.PROCESSING.getCode());
            if (Objects.nonNull(batchTaskBigdataDO)) {
                lastId = batchTaskBigdataDO.getId();
                doTaskQuery(batchTaskBigdataDO, counter);
            }
        } while (Objects.nonNull(batchTaskBigdataDO));
    }

    /**
     * 处理任务状态为未处理的任务数据
     *
     * @param counter
     */
    private void handlerUnProcessTask(AtomicInteger counter) {
        // 扫描未处理的任务
        Integer lastId = 0;
        List<BatchTaskBigdataDO> batchTaskBigdataList;
        do {
            // 处理未处理的大数据任务
            batchTaskBigdataList = batchTaskBigdataMapper.jobScan(lastId, BATCH_SCAN_COUNT, BatchTaskBigdataStatusEnum.UN_PROCESS.getCode());
            if (!CommonUtil.isEmpty(batchTaskBigdataList)) {
                for (BatchTaskBigdataDO taskItem : batchTaskBigdataList) {
                    doTaskHandler(taskItem, counter);
                    lastId = taskItem.getId();
                }
            }
        } while (!CommonUtil.isEmpty(batchTaskBigdataList));
    }


    /**
     * 做文件任务出来结果查询
     * <p>
     * 最长 阻塞时长 (RETRY_QUERY_COUNT * 2)/60   分钟
     *
     * @param batchTaskBigdataDO
     * @param counter
     */
    private void doTaskQuery(BatchTaskBigdataDO batchTaskBigdataDO, AtomicInteger counter) {
        if (Objects.isNull(batchTaskBigdataDO)) {
            return;
        }
        Integer id = batchTaskBigdataDO.getId();
        String taskNo = batchTaskBigdataDO.getTaskNo();
        if (StringUtils.isBlank(taskNo)) {
            log.info("taskNo为空,更新大数据批量任务为处理失败,batchTaskBigdataDO = {}", JSON.toJSONString(batchTaskBigdataDO));
            updateTaskBigdataStatus(id, BatchTaskBigdataStatusEnum.FAIL.getCode(), "taskNo为空");
            return;
        }

        Integer tplId = batchTaskBigdataDO.getTplId();
        TplDO tplDO = tplMapper.selectByPrimaryKey(tplId);
        if (Objects.isNull(tplDO)) {
            log.info("大数据批量任务未查询到模板信息 = {}", JSON.toJSONString(batchTaskBigdataDO));
            updateTaskBigdataStatus(id, BatchTaskBigdataStatusEnum.FAIL.getCode(), "未查询到模板信息");
            return;
        }

        // 判断当前任务是否超时
        // 防止由于群发任务处理过程中应用强制退出,从而导致当前查询任务一直都处于正在处理的状态(redis cache)
        // 导致大数据任务一直无法完成的情况
        if (isTaskExpire(batchTaskBigdataDO)) {
            log.info("大数据群发任务已超时,batchTaskBigdataDO = {}", JSON.toJSONString(batchTaskBigdataDO));
            updateTaskBigdataStatus(id, BatchTaskBigdataStatusEnum.TIMEOUT.getCode(), "任务处理超时");
            return;
        }

        UploadQueryVO uploadQueryVO;
        // 查询计数器
        // 为了防止缓存一直存在 导致job一直无法结束
        int count = 0;
        while (count <= RETRY_QUERY_COUNT) {
            count++;
            uploadQueryVO = queryUploadResultHandler.handler(taskNo);
            Integer status = uploadQueryVO.getStatus();
            if (Objects.equals(BatchTaskConstants.UploadQueryStatus.PROCESSING, status)) {
                // 正在处理中 就停一会儿
                log.info("大数据群发任务，当前任务正在处理中 id = {},taskNo = {}", id, taskNo);
                try {
                    TimeUnit.SECONDS.sleep(2);
                } catch (Exception e) {
                    log.warn("doUploadQuery 休眠被打断...", e);
                }
                continue;
            }

            if (Objects.equals(BatchTaskConstants.UploadQueryStatus.FAIL, status)) {
                log.info("查询文件处理结果失败,batchTaskBigdataDO = {}", JSON.toJSONString(batchTaskBigdataDO));
                // 处理失败则结束当前查询
                updateTaskBigdataStatus(id, BatchTaskBigdataStatusEnum.FAIL.getCode(), "查询文件处理结果失败");
                return;
            }

            // 处理成功啦
            // 进行数据保存操作并提交群发任务
            handlerBatchTask(batchTaskBigdataDO, uploadQueryVO, tplDO, counter);
            // 退出while循环
            return;
        }

    }

    /**
     * 做任务处理
     *
     * @param batchTaskBigdataDO
     * @param counter
     */
    private void doTaskHandler(BatchTaskBigdataDO batchTaskBigdataDO, AtomicInteger counter) {
        Integer id = batchTaskBigdataDO.getId();
        if (StringUtils.isBlank(batchTaskBigdataDO.getAppCode())) {
            log.info("大数据任务appCode为空,batchTaskBigdataDO = {}", JSON.toJSONString(batchTaskBigdataDO));
            updateTaskBigdataStatus(id, BatchTaskBigdataStatusEnum.FAIL.getCode(), "appCode为空");
            return;
        }

        if (Objects.isNull(batchTaskBigdataDO.getTplId())) {
            log.info("大数据任务tplId为空,batchTaskBigdataDO = {}", JSON.toJSONString(batchTaskBigdataDO));
            updateTaskBigdataStatus(id, BatchTaskBigdataStatusEnum.FAIL.getCode(), "tplId为空");
            return;
        }

        String fileUrl = batchTaskBigdataDO.getFileUrl();
        if (StringUtils.isBlank(fileUrl)) {
            log.info("大数据任务文件地址为空,batchTaskBigdataDO = {}", JSON.toJSONString(batchTaskBigdataDO));
            updateTaskBigdataStatus(id, BatchTaskBigdataStatusEnum.FAIL.getCode(), "文件地址为空");
            return;
        }

        // 通过cos查询文件地址
        String cosUrl = null;
        try {
            if (StringUtils.startsWithIgnoreCase(fileUrl, BatchTaskConstants.Commons.HTTP_STR)) {
                // 如果文件地址是以http开头 表示当前文件不需要使用cos进行查询
                cosUrl = fileUrl;
            } else {
                // 查询bigdata表的文件名称属于原始地址
                cosUrl = s3Helper.preBigdataUrl(fileUrl, true);
            }
        } catch (Exception e) {
            log.error("查询cos文件地址失败,batchTaskBigdataDO = {}", JSON.toJSONString(batchTaskBigdataDO), e);
        }

        if (StringUtils.isBlank(cosUrl)) {
            updateTaskBigdataStatus(batchTaskBigdataDO.getId(), BatchTaskBigdataStatusEnum.FAIL.getCode(), "查询cos文件地址为空");
            return;
        }

        // 文件的原始名称
        String originalFilename = fileUrl;
        int index = fileUrl.lastIndexOf('/');
        if (index != -1) {
            originalFilename = fileUrl.substring(index + 1);
        }

        File localFile = null;
        try {
            // 使用cosUrl地址进行文件下载处理
            localFile = UploadFileUtils.saveFile(cosUrl, originalFilename);
        } catch (Exception e) {
            log.error("大数据任务文件下载失败,batchTaskBigdataDO = {},cosUrl = {}", JSON.toJSONString(batchTaskBigdataDO), cosUrl, e);
        }
        if (Objects.isNull(localFile)) {
            updateTaskBigdataStatus(id, BatchTaskBigdataStatusEnum.FAIL.getCode(), "文件下载失败");
            return;
        }

        Integer signId = this.getSignId(batchTaskBigdataDO);

        FileLoadContext context = FileLoadContext.builder()
                .file(localFile)
                // 过滤cos文件上传
                .filterCos(true)
                // 设置文件上传bigdata文件名称
                .namePrefix(BatchTaskConstants.Commons.COS_BIGDATA_FLAG)
                .build();
        log.info("大数据文件上传fileName = {},signId = {}", originalFilename, signId);

        TplDO tplDO = tplMapper.selectByPrimaryKey(batchTaskBigdataDO.getTplId());
        Map<String, String> uploadResult = batchTaskFactory.asyncHandler(context, originalFilename, signId, "重复过滤", tplDO.getSmsTypeCode(), batchTaskBigdataDO.getTplId());
        // 任务编号
        String taskNo = uploadResult.get(REQUEST_ID_KEY);
        batchTaskBigdataDO.setTaskNo(taskNo);
        // 更新任务编号
        updateTaskBigdataTaskNo(id, taskNo);
        // 做任务查询
        doTaskQuery(batchTaskBigdataDO, counter);
    }

    /**
     * 更新大数据任务状态
     *
     * @param id
     * @param status
     * @param description
     */
    private void updateTaskBigdataStatus(Integer id, Integer status, String description) {
        BatchTaskBigdataDO result = new BatchTaskBigdataDO();
        result.setId(id);
        result.setStatus(status);
        result.setDescription(description);
        batchTaskBigdataMapper.updateByPrimaryKeySelective(result);
    }

    /**
     * 更新大数据任务 taskNo
     *
     * @param id
     * @param taskNo
     */
    private void updateTaskBigdataTaskNo(Integer id, String taskNo) {
        BatchTaskBigdataDO result = new BatchTaskBigdataDO();
        result.setId(id);
        result.setTaskNo(taskNo);
        result.setStatus(BatchTaskBigdataStatusEnum.PROCESSING.getCode());
        batchTaskBigdataMapper.updateByPrimaryKeySelective(result);
    }

    /**
     * 更新大数据任务 taskId
     *
     * @param id
     * @param taskId
     */
    private void updateTaskBigdataTaskId(Integer id, Integer taskId) {
        BatchTaskBigdataDO result = new BatchTaskBigdataDO();
        result.setId(id);
        result.setTaskId(taskId);
        result.setStatus(BatchTaskBigdataStatusEnum.PROCESSED.getCode());
        result.setDescription("success");
        batchTaskBigdataMapper.updateByPrimaryKeySelective(result);
    }

    /**
     * 判断当前任务是否超时
     *
     * @param batchTaskBigdataDO
     * @return
     */
    private boolean isTaskExpire(BatchTaskBigdataDO batchTaskBigdataDO) {
        Date updateTime = batchTaskBigdataDO.getUpdateTime();
        if (Objects.isNull(updateTime)) {
            return true;
        }

        // 超过24小时 当前任务还处于 处理中 则该任务已超时
        return System.currentTimeMillis() - updateTime.getTime() > TIMEOUT_MILLS;
    }

    /**
     * 处理群发任务
     *
     * @param batchTaskBigdataDO
     * @param uploadQueryVO
     * @param tplDO
     * @param counter
     */
    private void handlerBatchTask(BatchTaskBigdataDO batchTaskBigdataDO, UploadQueryVO uploadQueryVO, TplDO tplDO, AtomicInteger counter) {
        Integer id = batchTaskBigdataDO.getId();
        Integer taskId = null;
        String message = null;
        try {
            // 保存群发任务
            taskId = saveBatchTask(batchTaskBigdataDO, uploadQueryVO, tplDO);
        } catch (Exception e) {
            log.error("大数据任务保存批量群发任务失败,batchTaskBigdataDO = {}", JSON.toJSONString(batchTaskBigdataDO), e);
            message = e.getMessage();
        }

        if (Objects.isNull(taskId)) {
            // 如果任务ID为空 那么群发任务保存失败
            updateTaskBigdataStatus(id, BatchTaskBigdataStatusEnum.FAIL.getCode(), trimMessage(message, "保存群发任务失败"));
            return;
        }

        // 将大数据任务与群发任务绑定起来
        updateTaskBigdataTaskId(id, taskId);

        // 做群发任务提交
        try {
            message = null;
            submitBatchTask(batchTaskBigdataDO, taskId);
            // 处理成功了
            counter.incrementAndGet();
        } catch (Exception e) {
            log.error("大数据任务提交群发任务失败,batchTaskBigdataDO = {}", JSON.toJSONString(batchTaskBigdataDO), e);
            message = e.getMessage();
        }

        if (StringUtils.isNotBlank(message)) {
            updateTaskBigdataStatus(id, BatchTaskBigdataStatusEnum.FAIL.getCode(), trimMessage(message, "提交群发任务失败"));
        }

    }

    /**
     * 保存群发任务
     *
     * @param batchTaskBigdataDO
     * @param uploadQueryVO
     * @param tplDO
     * @return
     */
    private Integer saveBatchTask(BatchTaskBigdataDO batchTaskBigdataDO, UploadQueryVO uploadQueryVO, TplDO tplDO) {
        // 处理成功啦
        // 进行数据保存操作
        BatchTaskDTO batchTaskDTO = new BatchTaskDTO();
        // 应用编码
        batchTaskDTO.setAppCode(batchTaskBigdataDO.getAppCode());
        // 模板ID
        batchTaskDTO.setTplId(batchTaskBigdataDO.getTplId());
        // 签名ID
        batchTaskDTO.setSignId(tplDO.getSignId());
        // 短信类型编码
        batchTaskDTO.setSmsTypeCode(tplDO.getSmsTypeCode());

        // 项目用途设置 2022-05-16
        String projectDesc = batchTaskBigdataDO.getProjectDesc();
        if (StringUtils.isNotBlank(projectDesc)) {
            batchTaskDTO.setProjectDesc(projectDesc);
        }

        // 内容
        batchTaskDTO.setContent(tplDO.getContent());
        // 发送类型，1：立即发送；2：定时发送
        batchTaskDTO.setSendType(SendTypeEnum.IMMEDIATE.getType());
        String remark = batchTaskBigdataDO.getRemark();
        if (StringUtils.isBlank(remark)) {
            remark = REMARK;
        }
        // 备注，发送原因
        batchTaskDTO.setRemark(remark + "@" + batchTaskBigdataDO.getId());
        // 设置大数据任务任务id
        batchTaskDTO.setBigdataId(batchTaskBigdataDO.getId());

        CheckResultVO checkResult = uploadQueryVO.getCheckResult();
        // 名单中的用户标识类型，1：cid；2：手机号
        String dataType = checkResult.getDataType();
        if (StringUtils.equalsAnyIgnoreCase(BatchTaskConstants.DataType.CID, dataType)) {
            // cid
            batchTaskDTO.setUserIdType(1);
        } else if (StringUtils.equalsIgnoreCase(BatchTaskConstants.DataType.MOBILE, dataType)) {
            // 手机
            batchTaskDTO.setUserIdType(2);
        }

        // 文件检测结果列表
        List<CheckResultVO> checkResultList = Lists.newArrayList(checkResult);
        batchTaskDTO.setCheckResultList(checkResultList);
        // 短信群发任务的参数分片
        // 参考 BatchTaskParamDTO
        List<QueryTaskSegmentVO> taskParamList = Lists.newArrayList(uploadQueryVO.getTaskParamItem());
        batchTaskDTO.setTaskParamList(taskParamList);

        // 判断任务是否是定时发送任务
        if (isDelaySend(batchTaskBigdataDO, batchTaskDTO)) {
            Integer sendTime = batchTaskBigdataDO.getSendTime();
            batchTaskDTO.setSendTime(DateUtil.intToDate(sendTime));
            batchTaskDTO.setSendType(SendTypeEnum.IMMEDIATE.getType());
        }

        // 保存群发任务
        return batchTaskService.create(batchTaskDTO, batchTaskBigdataDO.getCreator());
    }

    /**
     * 提交群发任务
     *
     * @param batchTaskBigdataDO
     * @param taskId
     */
    private void submitBatchTask(BatchTaskBigdataDO batchTaskBigdataDO, Integer taskId) {
        if (Objects.equals(true, venusConfig.getTaskBigdataSubmitEnable())) {
            // 自动提交群发任务
            log.info("大数据任务处理完成,自动提交群发任务, batchTaskBigdataDO = {}, taskId = {}", JSON.toJSONString(batchTaskBigdataDO), taskId);
            // 任务提交之前将群发任务状态设置为灰度结束(只有灰度结束时 才不会校验测试日志校验逻辑)
            this.updateBatchTaskToAbWarning(taskId);
            batchTaskService.submit(taskId);
        } else {
            log.info("大数据任务处理完成,未启用自动提交群发任务, batchTaskBigdataDO = {}, taskId = {}", JSON.toJSONString(batchTaskBigdataDO), taskId);
        }
    }

    /**
     * 判断当前任务是否是延迟发送任务
     * <p>
     * 若设置的sendTime比当前时间小 那么则不用延期发送了
     *
     * @param batchTaskBigdataDO
     * @param batchTaskDTO
     * @return
     */
    private boolean isDelaySend(BatchTaskBigdataDO batchTaskBigdataDO, BatchTaskDTO batchTaskDTO) {
        Integer sendTime = batchTaskBigdataDO.getSendTime();
        if (Objects.isNull(sendTime) || sendTime == 0) {
            // 没有设置延期时间
            return false;
        }

        Date sendDate = DateUtil.intToDate(sendTime);
        if (Objects.isNull(sendDate)) {
            return false;
        }

        long interval = sendDate.getTime() - System.currentTimeMillis();
        if (interval <= 0) {
            log.error("大数据任务设置了消息延迟发送,但设置的时间小于等于当前时间,消息改为立即发送,batchTaskBigdataDO = {}", JSON.toJSONString(batchTaskBigdataDO));
            // 将过期描述写入群发任务描述信息中去
            String remark = batchTaskDTO.getRemark();
            batchTaskDTO.setRemark(remark + "@设置的延期时间已过期=" + sendTime);
            return false;
        }
        return true;
    }

    /**
     * 截取字符串
     *
     * @param message
     * @param message
     * @return
     */
    private String trimMessage(String message, String defaultMessage) {
        if (StringUtils.isBlank(message)) {
            return defaultMessage;
        }
        int length = message.length();
        if (length < MAX_MESSAGE_LENGTH) {
            return message;
        }
        return message.substring(0, MAX_MESSAGE_LENGTH);
    }

    /**
     * 获取到签名ID
     *
     * @param batchTaskBigdataDO
     * @return
     */
    private Integer getSignId(BatchTaskBigdataDO batchTaskBigdataDO) {
        Integer tplId = batchTaskBigdataDO.getTplId();
        if (Objects.isNull(tplId)) {
            return null;
        }

        TplDO tplDO = tplMapper.selectByPrimaryKey(tplId);
        return Objects.nonNull(tplDO) ? tplDO.getSignId() : null;
    }


    /**
     * 更新群发任务状态为 灰度结束
     *
     * @param taskId
     */
    private void updateBatchTaskToAbWarning(Integer taskId) {
        BatchTaskDO batchTask = new BatchTaskDO();
        batchTask.setId(taskId);
        batchTask.setStatus(BatchTaskStatusEnum.AB_WARNING.getCode());
        // 如果群发任务状态处于灰度中 则更新为灰度结束 否则不做任何操作
        int rows = batchTaskMapper.updateByStatusWithId(taskId, BatchTaskStatusEnum.AB_WARNING.getCode(), null, BatchTaskStatusEnum.UN_COMMIT.getCode());
        log.info("大数据任务更新群发任务状态为灰度结束(AB_WARNING)是否成功={},taskId = {}", (rows > 0), taskId);
    }

}
