package com.xhqb.spectre.admin.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xhqb.spectre.admin.bidata.entity.ChannelPriceStatDO;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonoBillStatVO {
    /**
     * 统计日期
     * 月份
     */
    @ExcelProperty({"", "日期"})
    private String statDate;
    /**
     * 渠道编码
     */
    @ExcelIgnore
    private String channelCode;
    @ExcelProperty({"", "供应商"})
    private String channelCodeName;

    @ExcelProperty({"触达率", "验证码"})
    private String verifyReachRate;
    @ExcelProperty({"触达率", "通知"})
    private String notifyReachRate;
    @ExcelProperty({"触达率", "营销"})
    private String marketReachRate;
    @ExcelProperty({"触达率", "催收"})
    private String collectorReachRate;
    @ExcelProperty({"触达率", "轻催"})
    private String lightCollectorReachRate;
    @ExcelProperty({"触达率", "重催"})
    private String severeCollectorReachRate;
    @ExcelProperty({"触达率", "债转"})
    private String debtSwapReachRate;
    @ExcelProperty({"触达率", "平均率"})
    private String avgReachRate;

    @ExcelIgnore
    private BigDecimal verifyPriceCount;
    @ExcelIgnore
    private BigDecimal notifyPriceCount;
    @ExcelIgnore
    private BigDecimal marketPriceCount;
    @ExcelIgnore
    private BigDecimal collectorPriceCount;
    @ExcelIgnore
    private BigDecimal lightCollectorPriceCount;
    @ExcelIgnore
    private BigDecimal severeCollectorPriceCount;
    @ExcelIgnore
    private BigDecimal debtSwapPriceCount;
    @ExcelIgnore
    private BigDecimal totalPriceCount;

    //验证码计费数
    @ExcelProperty({"计费量(条)", "验证码"})
    private Integer verifyReachBillCount;
    //通知计费数
    @ExcelProperty({"计费量(条)", "通知"})
    private Integer notifyReachBillCount;
    //营销计费数
    @ExcelProperty({"计费量(条)", "营销"})
    private Integer marketReachBillCount;
    //行业通知计费数
    @ExcelProperty({"计费量(条)", "催收"})
    private Integer collectorReachBillCount;
    //轻催计费数
    @ExcelProperty({"计费量(条)", "轻催"})
    private Integer lightCollectorReachBillCount;
    //重催计费数
    @ExcelProperty({"计费量(条)", "重催"})
    private Integer severeCollectorReachBillCount;
    //债转计费数
    @ExcelProperty({"计费量(条)", "债转"})
    private Integer debtSwapReachBillCount;
    //合计计费数
    @ExcelProperty({"计费量(条)", "总计费量"})
    private Integer totalReachBillCount;

    public static MonoBillStatVO convert(ChannelPriceStatDO channelPriceStatDO,
                                         String channelCodeName,
                                         List<String> selectedTypes) {
        MonoBillStatVO vo = MonoBillStatVO.builder()
                .statDate(channelPriceStatDO.getStatDate())
                .channelCode(channelPriceStatDO.getChannelCode())
                .channelCodeName(channelCodeName).build();

                BigDecimal totalPriceCountSum = BigDecimal.ZERO;
                double totalReachRateSum = 0.0;
                int reachRateCount = 0;
                int totalReachBillCountSum = 0;

                if (selectedTypes == null || selectedTypes.contains("verify")) {
                    vo.setVerifyReachRate(CommonUtil.double2String(channelPriceStatDO.getVerifyReachRate()));
                    vo.setVerifyPriceCount(convertFenToYuan(channelPriceStatDO.getVerifyPriceCount()));
                    vo.setVerifyReachBillCount(channelPriceStatDO.getVerifyReachBillCount());

                    if (channelPriceStatDO.getVerifyReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getVerifyReachRate();
                        reachRateCount++;
                    }
                    if (channelPriceStatDO.getVerifyReachBillCount() != null) {
                        totalReachBillCountSum += channelPriceStatDO.getVerifyReachBillCount();
                    }
                }

                if (selectedTypes == null || selectedTypes.contains("notify")) {
                    vo.setNotifyReachRate(CommonUtil.double2String(channelPriceStatDO.getNotifyReachRate()));
                    vo.setNotifyPriceCount(convertFenToYuan(channelPriceStatDO.getNotifyPriceCount()));
                    vo.setNotifyReachBillCount(channelPriceStatDO.getNotifyReachBillCount());

                    if (channelPriceStatDO.getNotifyReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getNotifyReachRate();
                        reachRateCount++;
                    }
                    if (channelPriceStatDO.getNotifyReachBillCount() != null) {
                        totalReachBillCountSum += channelPriceStatDO.getNotifyReachBillCount();
                    }
                }

                if (selectedTypes == null || selectedTypes.contains("market")) {
                    vo.setMarketReachRate(CommonUtil.double2String(channelPriceStatDO.getMarketReachRate()));
                    vo.setMarketPriceCount(convertFenToYuan(channelPriceStatDO.getMarketPriceCount()));
                    vo.setMarketReachBillCount(channelPriceStatDO.getMarketReachBillCount());

                    if (channelPriceStatDO.getMarketReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getMarketReachRate();
                        reachRateCount++;
                    }
                    if (channelPriceStatDO.getMarketReachBillCount() != null) {
                        totalReachBillCountSum += channelPriceStatDO.getMarketReachBillCount();
                    }
                }

                if (selectedTypes == null || selectedTypes.contains("collector")) {
                    vo.setCollectorReachRate(CommonUtil.double2String(channelPriceStatDO.getCollectorReachRate()));
                    vo.setCollectorPriceCount(convertFenToYuan(channelPriceStatDO.getCollectorPriceCount()));
                    vo.setCollectorReachBillCount(channelPriceStatDO.getCollectorReachBillCount());
                    if (channelPriceStatDO.getCollectorReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getCollectorReachRate();
                        reachRateCount++;
                    }
                    if (channelPriceStatDO.getCollectorReachBillCount() != null) {
                        totalReachBillCountSum += channelPriceStatDO.getCollectorReachBillCount();
                    }
                }

                if (selectedTypes == null || selectedTypes.contains("light_collector")) {
                    vo.setLightCollectorReachRate(CommonUtil.double2String(channelPriceStatDO.getLightCollectorReachRate()));
                    vo.setLightCollectorPriceCount(convertFenToYuan(channelPriceStatDO.getLightCollectorPriceCount()));
                    vo.setLightCollectorReachBillCount(channelPriceStatDO.getLightCollectorReachBillCount());

                    if (channelPriceStatDO.getLightCollectorReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getLightCollectorReachRate();
                        reachRateCount++;
                    }
                    if (channelPriceStatDO.getLightCollectorReachBillCount() != null) {
                        totalReachBillCountSum += channelPriceStatDO.getLightCollectorReachBillCount();
                    }
                }

                if (selectedTypes == null || selectedTypes.contains("severe_collector")) {
                    vo.setSevereCollectorReachRate(CommonUtil.double2String(channelPriceStatDO.getSevereCollectorReachRate()));
                    vo.setSevereCollectorPriceCount(convertFenToYuan(channelPriceStatDO.getSevereCollectorPriceCount()));
                    vo.setSevereCollectorReachBillCount(channelPriceStatDO.getSevereCollectorReachBillCount());

                    if (channelPriceStatDO.getSevereCollectorReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getSevereCollectorReachRate();
                        reachRateCount++;
                    }

                    if (channelPriceStatDO.getSevereCollectorReachBillCount() != null) {
                        totalReachBillCountSum += channelPriceStatDO.getSevereCollectorReachBillCount();
                    }
                }

                if (selectedTypes == null || selectedTypes.contains("debt_swap")) {
                    vo.setDebtSwapReachRate(CommonUtil.double2String(channelPriceStatDO.getDebtSwapReachRate()));
                    vo.setDebtSwapPriceCount(convertFenToYuan(channelPriceStatDO.getDebtSwapPriceCount()));
                    vo.setDebtSwapReachBillCount(channelPriceStatDO.getDebtSwapReachBillCount());

                    if (channelPriceStatDO.getDebtSwapReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getDebtSwapReachRate();
                        reachRateCount++;
                    }
                    if (channelPriceStatDO.getDebtSwapReachBillCount() != null) {
                        vo.setDebtSwapReachRate(CommonUtil.double2String(channelPriceStatDO.getDebtSwapReachRate()));
                    }
                }

                vo.setTotalPriceCount(totalPriceCountSum);
                vo.setTotalReachBillCount(totalReachBillCountSum);
                if (reachRateCount > 0) {
                    vo.setAvgReachRate(CommonUtil.double2String(totalReachRateSum / reachRateCount));
                }

                return vo;
    }

    private static BigDecimal convertFenToYuan(Integer fen) {
        return BigDecimal.ZERO;
    }
}
