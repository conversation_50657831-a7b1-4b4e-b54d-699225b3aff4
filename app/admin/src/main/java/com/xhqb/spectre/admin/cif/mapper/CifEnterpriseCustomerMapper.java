package com.xhqb.spectre.admin.cif.mapper;

import com.xhqb.spectre.admin.cif.entity.CifEnterpriseCustomerDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * cifdb.t_enterprise_customer_base 处理
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
public interface CifEnterpriseCustomerMapper {

    /**
     * 根据cid获取用户手机号码
     *
     * @param id
     * @return
     */
    CifEnterpriseCustomerDO selectByPrimaryKey(String id);

    /**
     * 根据cid列表查询用户信息
     *
     * @param cidList    cid列表
     * @param statusList 状态列表
     * @return
     */
    List<CifEnterpriseCustomerDO> selectByCidList(@Param("cidList") List<String> cidList, @Param("statusList") List<String> statusList);

    /**
     * 根据cid列表查询用户信息
     *
     * @param mobileList 手机号码列表
     * @return
     */
    List<CifEnterpriseCustomerDO> selectByMobileList(@Param("mobileList") Collection<String> mobileList);
}
