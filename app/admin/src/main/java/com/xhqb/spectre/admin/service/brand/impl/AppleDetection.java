package com.xhqb.spectre.admin.service.brand.impl;

import com.xhqb.spectre.admin.service.brand.BrandDetectionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 苹果品牌
 */
@Component
@Slf4j
public class AppleDetection implements BrandDetectionStrategy {
    @Override
    public boolean isMatch(String brand, String os) {
        return os.toLowerCase().contains("ios")
                || brand.toLowerCase().contains("iphone");
    }

    @Override
    public String getMappedBrand() {
        return "apple";
    }
}

