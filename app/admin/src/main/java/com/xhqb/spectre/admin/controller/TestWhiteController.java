package com.xhqb.spectre.admin.controller;

import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.model.dto.TestWhiteDTO;
import com.xhqb.spectre.admin.model.vo.TestWhiteVO;
import com.xhqb.spectre.admin.service.test.tool.TestWhiteService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.TestWhiteQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;


/**
 * 测试白名单
 */

@RestController
@RequestMapping("/testWhite")
@Slf4j
public class TestWhiteController {

    @Resource
    private TestWhiteService testWhiteService;

    /**
     * 分页查询测试白名单列表
     *
     * @param testWhiteQuery 包含查询条件的对象，用于过滤测试白名单数据
     * @param pageNum        当前页码，表示要查询的页数
     * @param pageSize       每页显示的记录数，表示每页返回的数据条数
     * @return 返回分页后的测试白名单数据，包含分页信息和数据列表
     */
    @GetMapping("/listByPage")
    public CommonResult<CommonPager<TestWhiteVO>> listByPage(@ModelAttribute TestWhiteQuery testWhiteQuery, Integer pageNum, Integer pageSize) {
        testWhiteQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        log.info("测试白名单分页查询参数 listByPage:{}", JsonLogUtil.toJSONString(testWhiteQuery));
        return CommonResult.success(testWhiteService.listByPage(testWhiteQuery));
    }

    /**
     * 新增
     *
     * @param testWhiteDTO 新增参数
     * @return 主键 id
     */
    @PostMapping("/add")
    public CommonResult<Integer> add(@RequestBody TestWhiteDTO testWhiteDTO) {
        log.info("测试白名单新增 add:{}", JsonLogUtil.toJSONString(testWhiteDTO));
        return CommonResult.success(testWhiteService.add(testWhiteDTO));
    }

    /**
     * 修改
     *
     * @param testWhiteDTO 修改参数
     * @return 主键 id
     */
    @PostMapping("/update")
    public CommonResult<Integer> update(@RequestBody TestWhiteDTO testWhiteDTO) {
        log.info("测试白名单修改 update:{}", JsonLogUtil.toJSONString(testWhiteDTO));
        return CommonResult.success(testWhiteService.update(testWhiteDTO));
    }


    /**
     * 详情
     *
     * @param id 主键 id
     * @return 详情
     */
    @GetMapping("/detail/{id}")
    public CommonResult<TestWhiteVO> detail(@PathVariable("id") Integer id) {
        log.info("测试白名单查看 detail:{}", id);
        return CommonResult.success(testWhiteService.detail(id));
    }

    /**
     * 删除
     *
     * @param id 主键 id
     * @return 删除
     */
    @PostMapping("/delete/{id}")
    public CommonResult<Integer> delete(@PathVariable("id") Integer id) {
        log.info("测试白名单删除 delete:{}", id);
        return CommonResult.success(testWhiteService.delete(id));
    }

    /**
     * 查看各个品牌数量
     *
     * @return 品牌数量
     */
    @GetMapping("/countAllByBrand")
    public CommonResult<Map<String, Integer>> countAllByBrand() {
        log.info("测试白名单查看品牌数量");
        return CommonResult.success(testWhiteService.countAllByBrand());
    }
}
