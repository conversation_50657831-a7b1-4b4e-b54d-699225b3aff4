package com.xhqb.spectre.admin.statistics.vo;

import com.xhqb.spectre.admin.statistics.entity.SmsStatisSumDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/28 15:31
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsStatisSumVO implements Serializable {

    private static final long serialVersionUID = 7466488798225735511L;

    /**
     * 统计时间（3种时间维度：日期、时间、月份）
     */
    private String date;

    /**
     * 发送量
     */
    private Integer sendCount;

    /**
     * 计费量
     */
    private Integer billCount;

    public static SmsStatisSumVO build(SmsStatisSumDO item) {
        return SmsStatisSumVO.builder()
                .date(item.getDate())
                .sendCount(item.getSendCount())
                .billCount(item.getBillCount())
                .build();
    }
}
