package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.cif.entity.CifCustomerBaseDO;
import com.xhqb.spectre.admin.model.vo.batchtask.CustomerVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * cif 数据服务
 *
 * <AUTHOR>
 * @date 2021/9/26
 */
public interface CifCustomerBaseService {

    /**
     * 根据cid获取用户信息
     *
     * @param cid
     * @return
     */
    CifCustomerBaseDO getByCid(String cid);

    /**
     * 根据cid列表查询用户信息
     *
     * @param cidList
     * @param smsTypeCode
     * @return
     */
    List<CifCustomerBaseDO> queryByCidList(List<String> cidList, String smsTypeCode);

    /**
     * 查询结果返回map
     *
     * @param cidList
     * @param smsTypeCode
     * @return
     */
    Map<String, CustomerVO> mapByCidList(List<String> cidList, String smsTypeCode);

    /**
     * 根据手机号查询用户列表
     *
     * @param mobileList 手机号码列表
     * @return
     */
    List<CifCustomerBaseDO> selectByMobileList(Collection<String> mobileList);
}
