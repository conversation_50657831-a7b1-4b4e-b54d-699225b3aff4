package com.xhqb.spectre.admin.cif.entity;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * cifdb.t_enterprise_customer_base 表对象
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CifEnterpriseCustomerDO implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 用户cid
     */
    private String cid;
    /**
     * 手机号码
     */
    private String mobilePhone;
    /**
     * 用户名称
     */
    private String customerName;
    /**
     * 状态
     */
    private String status;
    /**
     * 完件
     * PASS-通过
     * PRE-未完件
     * IN-完件操作中
     * ING-完件审批中
     * REFUSE-拒绝
     *
     * <p>
     * 需求：
     * ING-完件审批中
     * REFUSE-拒绝
     * PASS-通过
     * 都是完件，完件的不发营销
     */
    private String applyLoanResult;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
