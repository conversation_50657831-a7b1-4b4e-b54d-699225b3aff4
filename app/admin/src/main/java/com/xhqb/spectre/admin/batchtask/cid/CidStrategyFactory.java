package com.xhqb.spectre.admin.batchtask.cid;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.enums.CidStrategyEnum;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.CidStrategyDO;
import com.xhqb.spectre.common.dal.mapper.CidStrategyMapper;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * cid策略处理
 *
 * <AUTHOR>
 * @date 2021/11/26
 */
@Component
@Slf4j
public class CidStrategyFactory {
    /**
     * 查询用户状态列表
     */
    public static final List<String> DEFAULT_USER_STATUS_LIST = Lists.newArrayList("OPEN", "ACTIVE");

    /**
     * 有效状态
     */
    private static final int VALID_STATUS = 1;
    /**
     * 缓存30分钟失效
     */
    private static final int CACHE_TIMEOUT_MINUTE = 30;
    /**
     * cid策略key
     */
    private static final String CACHE_KEY = RedisKeys.BatchTaskKeys.CID_STRATEGY_STR_KEY;

    @Resource
    private CidStrategyMapper cidStrategyMapper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private VenusConfig venusConfig;

    /**
     * 写入缓存
     * <p>
     * 则是重新查询所有的策略数据 并重新写入缓存
     */
    public synchronized void putCache() {
        List<CidStrategyDO> cidStrategyList = cidStrategyMapper.selectAll();
        stringRedisTemplate.opsForValue().set(CACHE_KEY, JSON.toJSONString(cidStrategyList), CACHE_TIMEOUT_MINUTE, TimeUnit.MINUTES);
        log.info("刷新cid策略缓存成功...");
    }

    /**
     * 获取缓存数据
     * <p>
     * 返回的数据中status包含有效和无效的数据
     *
     * @param key
     * @return
     */
    public List<CidStrategyDO> getCache(String key) {
        List<CidStrategyDO> cidStrategyList = null;
        try {
            cidStrategyList = loadingCache(key);
        } catch (Exception e) {
            log.warn("获取cid策略缓存数据失败,key = {}", key, e);
        }

        if (!CommonUtil.isEmpty(cidStrategyList)) {
            return cidStrategyList;
        }

        log.info("未获取到cid策略缓存数据,直接加载数据库数据,type = {}", key);
        cidStrategyList = cidStrategyMapper.selectByType(key);
        // 更新缓存
        try {
            putCache();
        } catch (Exception e) {
            log.error("重新写入缓存失败", e);
        }
        if (CommonUtil.isEmpty(cidStrategyList)) {
            log.info("获取cid策略数据为空,key = {}", key);
        }
        return cidStrategyList;
    }

    /**
     * 从缓存中获取到值
     *
     * @param key
     * @return
     */
    private List<CidStrategyDO> loadingCache(String key) {
        String result = stringRedisTemplate.opsForValue().get(CACHE_KEY);
        if (StringUtils.isBlank(result)) {
            return null;
        }

        List<CidStrategyDO> cidStrategyList = JSON.parseArray(result, CidStrategyDO.class);
        if (CommonUtil.isEmpty(cidStrategyList)) {
            return null;
        }
        return cidStrategyList.stream().filter(s -> StringUtils.equals(key, s.getType())).collect(Collectors.toList());
    }


    /**
     * 过滤有效的cid策略信息
     *
     * @param cidStrategyList
     * @return
     */
    public List<CidStrategyDO> filter(List<CidStrategyDO> cidStrategyList) {
        if (CommonUtil.isEmpty(cidStrategyList)) {
            return null;
        }
        return cidStrategyList.stream().filter(s -> Objects.equals(s.getStatus(), VALID_STATUS)).collect(Collectors.toList());
    }

    /**
     * 是否有效
     *
     * @param cidStrategyDO
     * @return
     */
    public boolean isValid(CidStrategyDO cidStrategyDO) {
        return Objects.equals(VALID_STATUS, cidStrategyDO.getStatus());
    }

    /**
     * cid策略转换成list
     *
     * @param key
     * @return
     */
    public List<String> toListByKey(String key) {
        // 以防万一 添加 try...catch
        try {
            List<CidStrategyDO> cidStrategyList = getCache(key);
            if (CommonUtil.isEmpty(cidStrategyList)) {
                log.warn("未获取到cid策略信息, key = {}", key);
                return null;
            }
            // 只拿有效的数据
            List<String> list = cidStrategyList.stream().filter(s -> isValid(s)).map(s -> s.getStrategyValue()).collect(Collectors.toList());
            if (CommonUtil.isEmpty(list)) {
                return null;
            }
            return list;
        } catch (Exception e) {
            log.error("获取cid策略信息失败[list],key = {}", key, e);
        }
        return null;
    }


    /**
     * cid策略转换成array
     *
     * @param key
     * @return
     */
    public String[] toArrayByKey(String key) {
        // 以防万一 添加 try...catch
        try {
            List<String> cidStrategyList = toListByKey(key);
            if (CommonUtil.isEmpty(cidStrategyList)) {
                return null;
            }
            return cidStrategyList.toArray(new String[cidStrategyList.size()]);
        } catch (Exception e) {
            log.error("获取cid策略信息失败[array],key = {}", key, e);
        }
        return null;
    }

    /**
     * 获取用户状态策略
     *
     * @param smsTypeCode
     * @return
     */
    public List<String> getUserStatusStrategy(String smsTypeCode) {
        if (!MessageTypeEnum.isMarket(smsTypeCode)) {
            // 4. CID校验: 调整为仅对营销类短信有效，其他类型的短信不再校验CID状态。 (2024-09-04)
            // 非营销类 不进行用户状态策略的限制
            return null;
        }

        List<String> userStatusList = toListByKey(CidStrategyEnum.USER_STATUS.getCode());
        if (!CommonUtil.isEmpty(userStatusList)) {
            return userStatusList;
        }

        // 未配置有cid策略 用户状态 数据
        // 判断是否开启使用默认用户状态开关
        // 若开启 则使用默认用户状态 ，否则返回null
        if (Objects.equals(venusConfig.getIsUseDefaultUserStatusSwitch(), true)) {
            // 默认开关是关闭的，防止在cid策略有问题时
            // 可以打开该开关 返回默认的用户状态 以防影响到业务处理
            log.warn("未配置有cid策略用户状态,使用默认的用户数据 = {}", DEFAULT_USER_STATUS_LIST);
            return DEFAULT_USER_STATUS_LIST;
        }
        return null;
    }
}
