package com.xhqb.spectre.admin.model.vo;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xhqb.spectre.admin.enums.OaStatusEnum;
import com.xhqb.spectre.common.dal.entity.oa.TplContent;
import lombok.Data;

@Data
public class InExportVO {

    /**
     * 主键
     */
    @ExcelProperty(value = "id", index = 0)
    private Long id;

    /**
     * 模板编码
     */
    @ExcelProperty(value = "模板编号", index = 1)
    private String tplCode;

    /**
     * 签名 id
     */
    @ExcelProperty(value = "签名", index = 2)
    private String signName;

    /**
     * 短信类型(market)
     */
    @ExcelProperty(value = "短信类型", index = 3)
    private String smsTypeCodeName;

    /**
     * 原始内容
     */
    @ExcelProperty(value = "申请文案", index = 4)
    private String originalContent;

    /**
     * 报备通过内容
     */
    @ExcelProperty(value = "报备文案", index = 5)
    private String approvedContent;

    /**
     * 场景编码
     */
    @ExcelProperty(value = "使用场景", index = 6)
    private String sceneCode;

    /**
     * tag
     */
    @ExcelProperty(value = "审批状态", index = 7)
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", index = 8)
    private String createTime;


    /**
     * 创建者
     */
    @ExcelProperty(value = "创建人", index = 9)
    private String creator;


    public static InExportVO build(TplContent tplContent, String signName, String smsTypeCodeName) {
        OaStatusEnum oaStatusEnum = OaStatusEnum.fromCode(tplContent.getStatus());
        InExportVO inExportVO = new InExportVO();
        inExportVO.setId(tplContent.getId());
        inExportVO.setTplCode(tplContent.getTplCode());
        inExportVO.setSignName(signName);
        inExportVO.setSmsTypeCodeName(smsTypeCodeName);
        inExportVO.setOriginalContent(tplContent.getOriginalContent());
        inExportVO.setApprovedContent(tplContent.getApprovedContent());
        inExportVO.setSceneCode(tplContent.getSceneCode());
        inExportVO.setStatus(oaStatusEnum.getDescription());
        inExportVO.setCreator(tplContent.getCreator());
        inExportVO.setCreateTime(DateUtil.format(tplContent.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        return inExportVO;
    }
}
