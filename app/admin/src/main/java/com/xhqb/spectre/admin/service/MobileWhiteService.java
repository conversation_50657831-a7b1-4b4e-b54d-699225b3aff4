package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.MobileWhiteDTO;
import com.xhqb.spectre.admin.model.vo.MobileWhiteVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.MobileWhiteQuery;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 10:22
 * @Description:
 */
public interface MobileWhiteService {

    CommonPager<MobileWhiteVO> listByPage(MobileWhiteQuery mobileWhiteQuery);

    MobileWhiteVO getById(Integer id);

    void create(MobileWhiteDTO mobileWhiteDTO);

    void delete(Integer id);

    void batchDelete(List<Integer> idList);

}
