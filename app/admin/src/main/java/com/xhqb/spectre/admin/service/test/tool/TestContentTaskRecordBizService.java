package com.xhqb.spectre.admin.service.test.tool;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.enums.SmsContentTestCheckStatuseEnum;
import com.xhqb.spectre.admin.model.dto.TestContentTaskDetailMobileDataVO;
import com.xhqb.spectre.admin.model.dto.TestContentTaskRecordDTO;
import com.xhqb.spectre.admin.model.vo.*;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.test.tool.TestContentTaskDO;
import com.xhqb.spectre.common.dal.entity.test.tool.TestContentTaskRecordDO;
import com.xhqb.spectre.common.dal.mapper.TestContentTaskRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TestContentTaskRecordBizService {

    @Resource
    private TestContentTaskRecordMapper testContentTaskRecordMapper;
    @Resource
    private VenusConfig venusConfig;

    public TestContentTaskOverviewVO overview(String taskId) {

        List<TestContentTaskRecordDO> modelList = testContentTaskRecordMapper.selectByTaskId(taskId);
        TestContentTaskOverviewVO testContentTaskOverviewVO = new TestContentTaskOverviewVO();
        long sendCount = modelList.isEmpty() ? 0L : modelList.size();
        long receiptSuccessCount = modelList.stream().filter(m -> m.getReportStatus() == 0).count();
        long appReportCount = modelList.stream().filter(m -> m.getAppReportStatus() == 1).count();
        testContentTaskOverviewVO.setSendCount(sendCount);
        testContentTaskOverviewVO.setReceiptSuccessCount(receiptSuccessCount);
        testContentTaskOverviewVO.setAppReportCount(appReportCount);
        // app上报成功比较快，渠道回执慢的场景
        if (appReportCount > receiptSuccessCount) {
            receiptSuccessCount = appReportCount;
        }
        testContentTaskOverviewVO.setAppReportRate(CommonUtil.division(appReportCount, receiptSuccessCount));
        return testContentTaskOverviewVO;
    }

    public List<TestContentTaskDetailVO> detailBrand(String taskId) {
        List<TestContentTaskRecordDO> modelList = testContentTaskRecordMapper.selectByTaskId(taskId);
        if (CollectionUtil.isEmpty(modelList)) {
            return CollectionUtil.newArrayList();
        }

        Map<String, List<TestContentTaskRecordDO>> contentMap = modelList.stream().collect(Collectors.groupingBy(TestContentTaskRecordDO::getContent));
        List<TestContentTaskDetailVO> voList = new ArrayList<>();
        for (Map.Entry<String, List<TestContentTaskRecordDO>> entry : contentMap.entrySet()) {
            TestContentTaskDetailVO testContentTaskDetailVO = new TestContentTaskDetailVO();
            List<TestContentTaskRecordDO> contentList = entry.getValue();
            Map<String, List<TestContentTaskRecordDO>> brandMap = contentList.stream().collect(Collectors.groupingBy(TestContentTaskRecordDO::getBrand));
            Map<String, TestContentTaskDetailBrandVO> detailBrandVOMap = new HashMap<>();
            for (Map.Entry<String, List<TestContentTaskRecordDO>> brandEntry : brandMap.entrySet()) {
                TestContentTaskDetailBrandVO testContentTaskDetailBrandVO = new TestContentTaskDetailBrandVO();
                List<TestContentTaskRecordDO> brandList = brandEntry.getValue();
                Long successCount = brandList.stream().filter(m -> m.getSendStatus() == 0).count();
                Long receiptSuccessCount = brandList.stream().filter(m -> m.getReportStatus() == 0).count();
                Long appReportCount = brandList.stream().filter(m -> m.getAppReportStatus() == 1).count();
                testContentTaskDetailBrandVO.setSuccessCount(successCount);
                testContentTaskDetailBrandVO.setReceiptSuccessCount(receiptSuccessCount);
                testContentTaskDetailBrandVO.setAppReportCount(appReportCount);
                testContentTaskDetailBrandVO.setMobileDataVOList(brandList.stream().map(m -> {
                    TestContentTaskDetailMobileDataVO testContentTaskDetailMobileDataVO = new TestContentTaskDetailMobileDataVO();
                    BeanUtils.copyProperties(m, testContentTaskDetailMobileDataVO);
                    return testContentTaskDetailMobileDataVO;
                }).collect(Collectors.toList()));
                detailBrandVOMap.put(brandEntry.getKey(), testContentTaskDetailBrandVO);
            }
            testContentTaskDetailVO.setContent(entry.getKey());
            testContentTaskDetailVO.setDetailBrandVOMap(detailBrandVOMap);
            voList.add(testContentTaskDetailVO);
        }

        return voList;
    }

    public void add(TestContentTaskRecordDTO testContentTaskRecordDTO) {
        TestContentTaskRecordDO testContentTaskRecordDO = new TestContentTaskRecordDO();
        BeanUtils.copyProperties(testContentTaskRecordDTO, testContentTaskRecordDO);
        Date curTime = new Date();
        testContentTaskRecordDO.setCreateTime(curTime);
        testContentTaskRecordDO.setUpdateTime(curTime);
        testContentTaskRecordDO.setReportStatus(-1);
        testContentTaskRecordDO.setAppReportStatus(0);
        testContentTaskRecordMapper.insertBySelective(testContentTaskRecordDO);
    }

    public TestContentTaskDetailTypeVO getTestContentTaskDetail(String taskIdentifier, TestContentTaskDO testContentTaskDO) {
        Integer checkStatus = getCheckStatus(testContentTaskDO);

        TestContentTaskDetailTypeVO typeVO = new TestContentTaskDetailTypeVO();
        List<TestContentTaskRecordDO> recordDOList = testContentTaskRecordMapper.selectByTaskId(taskIdentifier);
        if (CollectionUtil.isEmpty(recordDOList)) {
            return new TestContentTaskDetailTypeVO();
        }

        recordDOList.sort(Comparator.comparing(
                TestContentTaskRecordDO::getBrand,
                Comparator.nullsLast(String::compareTo)
        ));

        List<ContentBrandVO> contentBrandData = new ArrayList<>();
        List<ContentAllVO> contentAllData = new ArrayList<>();


        Map<String, List<TestContentTaskRecordDO>> brandAndContentMap = new HashMap<>();
        for (TestContentTaskRecordDO recordDO : recordDOList) {
            // 构建 contentAllData
            contentAllData.add(buildContentAllVO(recordDO));

            // 构建 brandAndContentMap 并同时处理 contentBrandData
            String key = generateBrandContentKey(recordDO);
            brandAndContentMap.computeIfAbsent(key, k -> new ArrayList<>()).add(recordDO);
        }

        // 处理 brandAndContentMap 生成 contentBrandData
        for (Map.Entry<String, List<TestContentTaskRecordDO>> entry : brandAndContentMap.entrySet()) {
            contentBrandData.add(buildContentBrandVO(entry, checkStatus));
        }

        contentBrandData.sort(Comparator.comparing(
                ContentBrandVO::getBrand,
                Comparator.nullsLast(String::compareTo)
        ));

        typeVO.setContentAllData(contentAllData);
        typeVO.setContentBrandData(contentBrandData);
        return typeVO;
    }

    private ContentAllVO buildContentAllVO(TestContentTaskRecordDO recordDO) {
        ContentAllVO contentAllVO = new ContentAllVO();
        contentAllVO.setContent(recordDO.getContent());
        contentAllVO.setMobile(recordDO.getMobile());
        contentAllVO.setSendTime(DateUtil.formatDateTime(recordDO.getCreateTime()));
        contentAllVO.setSendStatus(recordDO.getSendStatus());
        contentAllVO.setReportStatus(recordDO.getReportStatus());
        contentAllVO.setAppReportStatus(recordDO.getAppReportStatus());
        contentAllVO.setBrand(recordDO.getBrand());
        return contentAllVO;
    }

    private ContentBrandVO buildContentBrandVO(Map.Entry<String, List<TestContentTaskRecordDO>> entry, Integer checkStatus) {
        List<TestContentTaskRecordDO> contentList = entry.getValue();
        long reportCount = contentList.stream().filter(r -> r.getReportStatus() == 0).count();
        long appReportCount = contentList.stream().filter(r -> r.getAppReportStatus() == 1).count();
        long sendCount = contentList.stream().filter(r -> r.getSendStatus() == 0).count();
        Double reachRate = CommonUtil.division(reportCount, sendCount);
        Double appReportRate = CommonUtil.division(appReportCount, reportCount);
        String[] keyInfo = entry.getKey().split("&");
        String brand = keyInfo[0];
        String content = keyInfo[1];
        ContentBrandVO contentBrandVO = new ContentBrandVO();
        contentBrandVO.setBrand(brand);
        contentBrandVO.setContent(content);
        if (sendCount == appReportCount && appReportCount >= reportCount) {
            contentBrandVO.setCheckStatus(SmsContentTestCheckStatuseEnum.COMPLETED.getCode());
        } else {
            contentBrandVO.setCheckStatus(checkStatus);
        }
        contentBrandVO.setReachRate(reachRate);
        contentBrandVO.setAppReportRate(appReportRate);
        return contentBrandVO;
    }

    private Integer getCheckStatus(TestContentTaskDO testContentTaskDO) {
        int checkStatus = SmsContentTestCheckStatuseEnum.INIT.getCode();
        long elapsedSeconds = (System.currentTimeMillis() - testContentTaskDO.getSubmitTime() * 1000L) / 1000;
        double progress = CommonUtil.division(elapsedSeconds, venusConfig.getTestContentTaskCountdown());
        double checkResult = Math.min(progress, 100.0);
        if (Objects.equals(100.0, checkResult)) {
            checkStatus = SmsContentTestCheckStatuseEnum.COMPLETED.getCode();
        } else if (checkResult > 0.0 && checkResult < 100.0) {
            checkStatus = SmsContentTestCheckStatuseEnum.ING.getCode();
        }

        return checkStatus;
    }

    private String generateBrandContentKey(TestContentTaskRecordDO record) {
        return record.getBrand() + "&" + record.getContent();
    }

}
