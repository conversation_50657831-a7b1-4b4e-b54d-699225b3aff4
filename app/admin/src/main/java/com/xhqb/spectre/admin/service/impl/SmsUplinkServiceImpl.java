package com.xhqb.spectre.admin.service.impl;

import cn.hutool.core.date.DateUtil;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.enums.RespCodeEnum;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.vo.SmsUplinkVO;
import com.xhqb.spectre.admin.model.vo.UplinkRelationOrderVO;
import com.xhqb.spectre.admin.readonly.mapper.SmsOrderReadonlyMapper;
import com.xhqb.spectre.admin.service.SmsUplinkService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.BatchTaskDO;
import com.xhqb.spectre.common.dal.entity.SmsUplinkDO;
import com.xhqb.spectre.common.dal.mapper.BatchTaskMapper;
import com.xhqb.spectre.common.dal.mapper.SmsUplinkMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.SmsUplinkQuery;
import com.xhqb.spectre.common.dal.query.SmsUplinkRelationOrderQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 上行短信
 *
 * <AUTHOR>
 * @date 2021/10/13
 */
@Service
@Slf4j
public class SmsUplinkServiceImpl implements SmsUplinkService {

    @Resource
    private SmsUplinkMapper smsUplinkMapper;
    @Resource
    private SmsOrderReadonlyMapper smsOrderReadonlyMapper;
    @Resource
    private VenusConfig venusConfig;
    @Resource
    private BatchTaskMapper batchTaskMapper;

    /**
     * 分页查询上行短信列表
     *
     * @param smsUplinkQuery
     * @return
     */
    @Override
    public CommonPager<SmsUplinkVO> listByPage(SmsUplinkQuery smsUplinkQuery) {
        return PageResultUtils.result(
                () -> smsUplinkMapper.countByQuery(smsUplinkQuery),
                () -> smsUplinkMapper.selectByQuery(smsUplinkQuery).stream().map(SmsUplinkVO::buildListQuery).collect(Collectors.toList())
        );
    }

    /**
     * 关联发送记录列表
     *
     * @param id
     * @return
     */
    @Override
    public CommonPager<UplinkRelationOrderVO> relationOrder(Long id) {
        SmsUplinkDO smsUplinkDO = smsUplinkMapper.selectByPrimaryKey(id);
        if (Objects.isNull(smsUplinkDO)) {
            throw new BizException(RespCodeEnum.NO_FIND_DATA);
        }

        String mobile = smsUplinkDO.getMobile();
        Date endTime = smsUplinkDO.getCreateTime();
        Date startTime = DateUtils.addDays(smsUplinkDO.getCreateTime(), venusConfig.getUplinkRelationOrderDays());
        Integer pageSize = venusConfig.getUplinkRelationOrderPageSize();
        SmsUplinkRelationOrderQuery query = new SmsUplinkRelationOrderQuery();
        query.setMobile(mobile);
        query.setPageSize(pageSize);
        query.setStartTimestamp(startTime.getTime() / 1000);
        query.setEndTimestamp(endTime.getTime() / 1000);

        List<UplinkRelationOrderVO> smsOrderList = smsOrderReadonlyMapper.uplinkRelationOrderQuery(query)
                .stream().map(UplinkRelationOrderVO::buildSmsOrderVO).collect(Collectors.toList());

        List<Integer> batchIdList = smsOrderList.stream().filter(s -> isValidBatchId(s.getBatchId()))
                .map(UplinkRelationOrderVO::getBatchId).collect(Collectors.toList());
        // key->batchId , value->batchCreator
        Map<Integer, String> batchCreatorMapping = null;
        if (!CommonUtil.isEmpty(batchIdList)) {
            // 群发任务
            List<BatchTaskDO> batchTaskList = batchTaskMapper.selectByIdList(batchIdList);
            batchCreatorMapping = this.batchTaskMapping(batchTaskList);
        }

        if (Objects.isNull(batchCreatorMapping)) {
            return new CommonPager<>(smsOrderList.size(), smsOrderList);
        }

        // 填充batchCreator
        Integer batchId;
        String batchCreator;
        for (UplinkRelationOrderVO smsOrder : smsOrderList) {
            batchId = smsOrder.getBatchId();
            if (!isValidBatchId(batchId)) {
                continue;
            }
            batchCreator = batchCreatorMapping.get(batchId);
            smsOrder.setBatchCreator(batchCreator);
        }
        return new CommonPager<>(smsOrderList.size(), smsOrderList);
    }

    /**
     * 上行短信扫描
     *
     * @param lastId
     * @param pageSize
     * @return
     */
    @Override
    public List<SmsUplinkDO> scanUplink(Long lastId, Integer pageSize) {
        return smsUplinkMapper.scanUplink(lastId, pageSize);
    }


    /**
     * @param lastSmsUplinkId 上一条记录id
     * @param pageSize        查询数量大小
     * @param collectList     收集上行信息集合
     * @return 返回本次查询id
     */
    @Override
    public Long selectIdByMarket(Long lastSmsUplinkId, int pageSize, List<SmsUplinkDO> collectList) {
        List<SmsUplinkDO> existMarketList = smsUplinkMapper.selectByMarket(lastSmsUplinkId, pageSize);
        if (CollectionUtils.isEmpty(existMarketList)) {
            return null;
        }
        collectList.addAll(existMarketList);
        return existMarketList.get(existMarketList.size() - 1).getId();
    }

    @Override
    public int updateByPrimaryKeySelective(SmsUplinkDO record) {
        return smsUplinkMapper.updateByPrimaryKeySelective(record);
    }

    private Map<Integer, String> batchTaskMapping(List<BatchTaskDO> batchTaskList) {
        if (CommonUtil.isEmpty(batchTaskList)) {
            return null;
        }
        return batchTaskList.stream().collect(Collectors.toMap(BatchTaskDO::getId, BatchTaskDO::getCreator));
    }

    private boolean isValidBatchId(Integer batchId) {
        return Objects.nonNull(batchId) && batchId > 0;
    }
}
