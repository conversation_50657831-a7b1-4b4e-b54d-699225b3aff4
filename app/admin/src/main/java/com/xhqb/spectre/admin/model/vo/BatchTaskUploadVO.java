package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.model.dto.BatchTaskParamDTO;
import com.xhqb.spectre.admin.model.vo.batchtask.CheckResultVO;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 短信群发文件上传显示信息
 *
 * <AUTHOR>
 * @date 2021/9/21
 */
@Data
public class BatchTaskUploadVO implements Serializable {


    /**
     * 文件参数列表
     */
    private BatchTaskParamDTO taskParamItem;

    /**
     * 文件检测结果
     */
    private CheckResultVO checkResult;

}
