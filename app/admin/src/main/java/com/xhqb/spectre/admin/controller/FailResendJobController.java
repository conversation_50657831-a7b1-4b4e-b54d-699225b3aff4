package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.admin.config.FailResendConfig;
import com.xhqb.spectre.admin.readonly.mapper.SmsOrderReadonlyMapper;
import com.xhqb.spectre.common.dal.dto.mq.CmppDeliverResqDTO;
import com.xhqb.spectre.common.dal.entity.FailResendRecordDO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.mapper.FailResendRecordMapper;
import com.xhqb.spectre.common.enums.FailResendStatusEnum;
import com.xhqb.spectre.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.MessageId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 补发任务管理
 */
@RestController
@RequestMapping("/fail-resend")
@Slf4j
public class FailResendJobController {
    
    @Resource
    private FailResendRecordMapper failResendRecordMapper;
    
    @Resource
    private FailResendConfig failResendConfig;

    @Resource
    private SmsOrderReadonlyMapper smsOrderReadonlyMapper;

    @Resource
    private MQTemplate<String> mqTemplate;

    /**
     * 补发MQ Topic
     */
    @Value("${tdmq.producers.failResend}")
    private String failResendTopic;
    
    /**
     * 获取补发统计信息
     */
    @GetMapping("/statistics")
    public Map<String, Object> getStatistics() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Long> statusCount = new HashMap<>();
            for (FailResendStatusEnum status : FailResendStatusEnum.values()) {
                List<FailResendRecordDO> records = 
                        failResendRecordMapper.selectByStatus(status.getCode());
                statusCount.put(status.getDescription(), (long) records.size());
            }
            
            // 计算总数和成功率
            long total = statusCount.values().stream().mapToLong(Long::longValue).sum();
            long successCount = statusCount.getOrDefault("补发成功", 0L);
            double successRate = total > 0 ? (successCount * 100.0 / total) : 0;
            
            result.put("statusCount", statusCount);
            result.put("total", total);
            result.put("successRate", String.format("%.2f%%", successRate));
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("获取补发统计信息异常", e);
            result.put("success", false);
            result.put("message", "获取统计信息失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取配置信息
     */
    @GetMapping("/config")
    public Map<String, Object> getConfig() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("job", failResendConfig.getJob());
            result.put("execution", failResendConfig.getExecution());
            result.put("monitor", failResendConfig.getMonitor());
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("获取配置信息异常", e);
            result.put("success", false);
            result.put("message", "获取配置失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 根据手机号发送测试补发MQ消息
     */
    @PostMapping("/test-fail-resend-by-mobile")
    public Map<String, Object> testFailResendByMobile(@RequestParam String mobile) {
        Map<String, Object> result = new HashMap<>();

        try {
            if (mobile == null || mobile.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "手机号不能为空");
                return result;
            }

            long endTime = DateUtil.getNow();
            long startTime = endTime - 24 * 3600;

            log.info("开始查询手机号 {} 的最后一条短信记录, 时间范围: {} - {}", mobile, startTime, endTime);

            // 查询该手机号的最后一条记录
            SmsOrderDO lastOrder = smsOrderReadonlyMapper.selectLastByMobile(mobile, startTime, endTime);

            if (lastOrder == null) {
                result.put("success", false);
                result.put("message", "未找到该手机号在最近7天的短信记录");
                return result;
            }

            log.info("找到手机号 {} 的最后一条短信记录: orderId={}, tplCode={}, sendTime={}",
                    mobile, lastOrder.getOrderId(), lastOrder.getTplCode(), lastOrder.getSendTime());

            // 构建CmppDeliverResqDTO消息
            CmppDeliverResqDTO cmppDeliverResqDTO = buildCmppDeliverResqDTO(lastOrder);

            // 发送MQ消息
            String message = JSONObject.toJSONString(cmppDeliverResqDTO);
            MessageId messageId = mqTemplate.send(failResendTopic, message);

            log.info("成功发送测试补发MQ消息, messageId: {}, orderId: {}, mobile: {}",
                    messageId, lastOrder.getOrderId(), mobile);

            result.put("success", true);
            result.put("message", "测试补发MQ消息发送成功");
            result.put("messageId", messageId.toString());
            result.put("orderId", lastOrder.getOrderId());
            result.put("tplCode", lastOrder.getTplCode());
            result.put("mobile", mobile);
            result.put("sendTime", lastOrder.getSendTime());
            result.put("mqMessage", cmppDeliverResqDTO);

        } catch (Exception e) {
            log.error("发送测试补发MQ消息异常, mobile: {}", mobile, e);
            result.put("success", false);
            result.put("message", "发送测试补发MQ消息失败: " + e.getMessage());
        }

        return result;
    }

    private CmppDeliverResqDTO buildCmppDeliverResqDTO(SmsOrderDO smsOrder) {
        String tableNameSuffix = null;
        if (smsOrder.getCreateTime() != null) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
            tableNameSuffix = formatter.format(smsOrder.getCreateTime());
        } else {
            tableNameSuffix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        }
        return CmppDeliverResqDTO.builder()
                .orderId(smsOrder.getOrderId())
                .channelAccountId(smsOrder.getChannelAccountId())
                .channelCode(smsOrder.getChannelCode())
                .mobile(smsOrder.getMobile())
                .channelMsgId(smsOrder.getChannelMsgId())
                .tplCode(smsOrder.getTplCode())
                .destTerminalId(smsOrder.getMobile())
                .submitTime(formatTime(smsOrder.getSendTime()))
                .recvReportTime((long) DateUtil.getNow())
                .doneTime(formatTime(DateUtil.getNow()))
                .reportStatus("2")
                .addition(null)
                .resend(smsOrder.getResend() != null ? smsOrder.getResend() : 0)
                .smsTypeCode(smsOrder.getSmsTypeCode())
                .recvSendTime(smsOrder.getSendTime())
                .reqSrc(smsOrder.getReqSrc())
                .gatewayUserName(null)
                .requestId(smsOrder.getRequestId())
                .destId(smsOrder.getMobile())
                .tableNameSuffix(tableNameSuffix)
                .build();
    }

    private String formatTime(long timestamp) {
        if (timestamp <= 0) {
            return null;
        }

        try {
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyMMddHHmm");
            return sdf.format(new java.util.Date(timestamp * 1000L));
        } catch (Exception e) {
            log.warn("格式化时间异常, timestamp: {}", timestamp, e);
            return null;
        }
    }
}
