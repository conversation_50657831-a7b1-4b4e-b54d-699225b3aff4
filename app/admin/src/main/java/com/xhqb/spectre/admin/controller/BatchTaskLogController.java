package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.BatchTaskLogVO;
import com.xhqb.spectre.admin.service.BatchTaskLogService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.BatchTaskLogQuery;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 群发任务日志
 *
 * <AUTHOR>
 * @date 2021/12/1
 */
@RestController
@RequestMapping("/batchTaskLog")
public class BatchTaskLogController {

    @Resource
    private BatchTaskLogService batchTaskLogService;

    /**
     * 查询群发日志列表
     *
     * @param batchTaskLogQuery 群发短信任务查询条件
     * @param pageNum           当前页码
     * @param pageSize          一页显示的记录数
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(BatchTaskLogQuery batchTaskLogQuery, Integer pageNum, Integer pageSize) {
        batchTaskLogQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<BatchTaskLogVO> commonPager = batchTaskLogService.listByPage(batchTaskLogQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 根据群发任务ID查询群发日志流水信息
     *
     * @param taskId
     * @return
     */
    @GetMapping("/getByTaskId")
    public AdminResult getByTaskId(Integer taskId) {
        List<BatchTaskLogVO> taskLogList = batchTaskLogService.getByTaskId(taskId);
        return AdminResult.success(taskLogList);
    }
}
