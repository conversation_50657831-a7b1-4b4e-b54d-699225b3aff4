package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/26 17:18
 * @Description:
 */
@Data
public class GatewayTplMappingDTO implements Serializable {

    private static final long serialVersionUID = -6945900687326847934L;

    @NotNull(message = "模板ID不能为空")
    private Integer tplId;

    @NotBlank(message = "模板内容不能为空")
    @Size(max = 1024, message = "模板内容最大为{max}个字符")
    private String tplContent;
}
