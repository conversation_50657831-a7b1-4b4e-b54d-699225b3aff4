package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.ChannelAccountDisableDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ChannelAccountDisableVO;
import com.xhqb.spectre.admin.service.ChannelAccountDisableService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ChannelAccountDisableQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/22 15:19
 * @Description:
 */
@RestController
@RequestMapping("/channelAccountDisable")
@Slf4j
public class ChannelAccountDisableController {

    @Autowired
    private ChannelAccountDisableService accountDisableService;

    /**
     * 列表查询
     * @param accountDisableQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(@ModelAttribute ChannelAccountDisableQuery accountDisableQuery, Integer pageNum, Integer pageSize) {
        accountDisableQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<ChannelAccountDisableVO> commonPager = accountDisableService.listByPage(accountDisableQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(accountDisableService.getById(id));
    }

    /**
     * 添加屏蔽信息
     *
     * @param accountDisableDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_CHANNEL_ACCOUNT_DISABLE)
    public AdminResult create(@RequestBody ChannelAccountDisableDTO accountDisableDTO) {
        log.info("create disableInfo, ChannelAccountDisableDTO: {}", JSON.toJSONString(accountDisableDTO));
        accountDisableService.create(accountDisableDTO);
        return AdminResult.success();
    }

    /**
     * 更新屏蔽信息
     *
     * @param id
     * @param accountDisableDTO
     * @return
     */
    @PutMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_CHANNEL_ACCOUNT_DISABLE)
    public AdminResult update(@PathVariable("id") Integer id, @RequestBody ChannelAccountDisableDTO accountDisableDTO) {
        accountDisableDTO.setId(id);
        log.info("update disableInfo, ChannelAccountDisableDTO: {}", JSON.toJSONString(accountDisableDTO));
        accountDisableService.update(accountDisableDTO);
        return AdminResult.success();
    }

    /**
     * 删除屏蔽信息
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_CHANNEL_ACCOUNT_DISABLE)
    public AdminResult delete(@PathVariable("id") Integer id) {
        log.info("delete disableInfo, id: {}", id);
        accountDisableService.delete(id);
        return AdminResult.success();
    }
}
