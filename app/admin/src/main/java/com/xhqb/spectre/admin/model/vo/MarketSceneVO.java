package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.MarketSceneDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 营销场景
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class MarketSceneVO implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 营销场景名称
     */
    private String name;

    /**
     * 营销场景状态，0：无效，1：有效
     */
    private Integer status;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    public static MarketSceneVO buildMarketSceneVO(MarketSceneDO marketSceneDO) {
        if (Objects.isNull(marketSceneDO)) {
            return null;
        }
        return MarketSceneVO.builder()
                .id(marketSceneDO.getId())
                .name(marketSceneDO.getName())
                .status(marketSceneDO.getStatus())
                .createTime(marketSceneDO.getCreateTime())
                .creator(marketSceneDO.getCreator())
                .updateTime(marketSceneDO.getUpdateTime())
                .updater(marketSceneDO.getUpdater())
                .build();
    }
}
