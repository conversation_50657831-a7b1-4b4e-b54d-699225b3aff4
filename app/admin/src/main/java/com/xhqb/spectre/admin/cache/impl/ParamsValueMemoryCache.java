package com.xhqb.spectre.admin.cache.impl;

import com.xhqb.spectre.admin.cache.AbstractMemoryCache;
import com.xhqb.spectre.common.dal.entity.ParamsValueDO;
import com.xhqb.spectre.common.dal.mapper.ParamsValueMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ParamsValueMemoryCache extends AbstractMemoryCache<ParamsValueDO> {

    // key:code value: values
    private static final Map<String, List<String>> valueMap = new HashMap<>();

    @Resource
    private ParamsValueMapper paramsValueMapper;

    @Override
    protected List<ParamsValueDO> loadCache() {
        log.info("  ParamsValueDO  load cache");
        List<ParamsValueDO> paramsDOList = paramsValueMapper.selectAll();
        if (CollectionUtils.isNotEmpty(paramsDOList)) {
            Map<String, List<String>> listMap = paramsDOList.stream()
                    .collect(Collectors.groupingBy(ParamsValueDO::getCode,
                            Collectors.mapping(ParamsValueDO::getValue, Collectors.toList())));
            valueMap.putAll(listMap);
        }
        return paramsDOList;
    }

    @Override
    protected String tableName() {
        return "t_test_param_value";
    }

    public List<String> getValues(String code) {
        return valueMap.get(code);
    }

    public Map<String, List<String>> getMap() {
        return new HashMap<>(valueMap);
    }
}
