package com.xhqb.spectre.admin.omini;

import com.google.common.collect.Lists;
import com.xhqb.msgcenter.model.MsgSendRequest;
import com.xhqb.msgcenter.model.iteam.MsgSendEntry;
import com.xhqb.msgcenter.model.response.OminiSendResult;
import com.xhqb.msgcenter.sdk.OminiSendMessage;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.util.CommonUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * spectre 消息中心处理
 *
 * <AUTHOR>
 * @date 2021/12/30
 */
@Component
public class OminiMessageSpectreHandler {

    @Resource
    private OminiSendMessage ominiSendMessage;

    /**
     * 消息发送
     *
     * @param context
     * @return
     */
    public OminiSendResult sendMessage(OminiMessageContext context) {
        if (Objects.isNull(context)) {
            throw new IllegalArgumentException("OminiContext参数不能够为空");
        }
        return this.sendMessage(Lists.newArrayList(context));
    }


    /**
     * 消息发送
     *
     * @param contextList
     * @return
     */
    public OminiSendResult sendMessage(List<OminiMessageContext> contextList) {
        if (CommonUtil.isEmpty(contextList)) {
            throw new IllegalArgumentException("contextList参数不能够为空");
        }
        MsgSendRequest msgSendRequest = this.buildRequestMessage(contextList);
        return this.sendMessage(msgSendRequest);
    }

    /**
     * 消息发送
     *
     * @param msgSendRequest
     * @return
     */
    public OminiSendResult sendMessage(MsgSendRequest msgSendRequest) {
        try {
            return ominiSendMessage.sendMessage(msgSendRequest);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 构建请求消息对象
     *
     * @param contextList
     * @return
     */
    private MsgSendRequest buildRequestMessage(List<OminiMessageContext> contextList) {
        ArrayList<MsgSendEntry> entries = new ArrayList<>(contextList.size());
        MsgSendEntry msgSendEntry;
        for (OminiMessageContext ominiContext : contextList) {
            msgSendEntry = ominiContext.buildEntry();
            if (Objects.isNull(msgSendEntry)) {
                throw new BizException("Omini消息内容不能够为空");
            }
            entries.add(msgSendEntry);
        }
        MsgSendRequest msgSendRequest = new MsgSendRequest();
        msgSendRequest.setMsgSendEntries(entries);
        return msgSendRequest;
    }

    /**
     * 判断消息发送是否成功
     *
     * @param result
     * @return
     */
    public boolean isSuccess(OminiSendResult result) {
        return Objects.nonNull(result) && result.isSuccess();
    }
}
