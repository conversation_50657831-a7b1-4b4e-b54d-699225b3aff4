package com.xhqb.spectre.admin.readonly.mapper;

import com.xhqb.spectre.admin.model.dto.ChannelMobileSyncDTO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.entity.SmsOrderKey;
import com.xhqb.spectre.common.dal.query.SmsOrderQuery;
import com.xhqb.spectre.common.dal.query.SmsUplinkRelationOrderQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface SmsOrderReadonlyMapper {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(SmsOrder<PERSON>ey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int insert(SmsOrderDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int insertSelective(SmsOrderDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    SmsOrderDO selectByPrimaryKey(SmsOrderKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SmsOrderDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SmsOrderDO record);

    SmsOrderDO selectByOrderId(Long orderId);

    SmsOrderDO selectByRequestId(@Param("requestId") String requestId, @Param("startTimestamp") long startTimestamp, @Param("endTimestamp") long endTimestamp);

    List<SmsOrderDO> selectByRequestIds(@Param("requestIdList") List<String> requestIdList, @Param("startTimestamp") long startTimestamp, @Param("endTimestamp") long endTimestamp);

    Integer countByQuery(SmsOrderQuery smsOrderQuery);

    List<SmsOrderDO> selectByQuery(SmsOrderQuery smsOrderQuery);

    /**
     * 上行短信关联发送记录查询
     *
     * @param mobile
     * @param createTime
     * @param pageSize
     * @return
     */
    List<SmsOrderDO> uplinkRelationOrderQuery(@Param("query") SmsUplinkRelationOrderQuery query);

    List<SmsOrderDO> selectByRequestIdAndMobileList(@Param("requestId") String requestId, @Param("mobileList") List<String> mobileList, @Param("startTimestamp") long startTimestamp, @Param("endTimestamp") long endTimestamp);

    int countBySyncDTO(ChannelMobileSyncDTO channelMobileSyncDTO);

    List<SmsOrderDO> selectBySyncDTO(ChannelMobileSyncDTO channelMobileSyncDTO);

    SmsOrderDO batchTestQuery(@Param("batchId") Integer batchId, @Param("startTimestamp") long startTimestamp, @Param("endTimestamp") long endTimestamp);

    Map<String, Long> abJobRate(@Param("taskId") Integer taskId, @Param("startTime") long startTime, @Param("endTime") long endTime);


    List<SmsOrderDO> selectByRequestIdAndMobile(@Param("requestId") String requestId, @Param("mobile") String mobile, @Param("beginTime") long beginTime, @Param("endTime") long endTime);

    SmsOrderDO selectLastByTplCode(@Param("tplCode") String tplCode, @Param("startTime") long startTime, @Param("endTime") long endTime);

    SmsOrderDO selectLastByMobile(@Param("mobile") String mobile, @Param("startTime") long startTime, @Param("endTime") long endTime);

    SmsOrderDO selectByOrderIdResendAndSendTimeRange(
            @Param("orderId") Long orderId,
            @Param("resend") Integer resend,
            @Param("startTimestamp") Long startTimestamp,
            @Param("endTimestamp") Long endTimestamp
    );
}