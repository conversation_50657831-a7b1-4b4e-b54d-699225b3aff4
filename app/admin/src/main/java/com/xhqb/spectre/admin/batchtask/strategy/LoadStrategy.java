package com.xhqb.spectre.admin.batchtask.strategy;

import com.xhqb.spectre.admin.batchtask.parse.ParseContext;

import java.io.IOException;

/**
 * 加载文件的策略
 * 例如：WEB文件上传、URL解析等策略
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
public interface LoadStrategy<T> {

    /**
     * 策略名称 用于策略加载
     *
     * @return
     */
    String named();

    /**
     * 加载文件策略
     *
     * @param request
     * @return
     * @throws IOException
     */
    ParseContext load(T request) throws IOException;

}
