package com.xhqb.spectre.admin.service.oa.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.enums.ContentTypeEnum;
import com.xhqb.spectre.admin.enums.RespCodeEnum;
import com.xhqb.spectre.admin.enums.SceneCodeEnum;
import com.xhqb.spectre.admin.enums.SmsTypeCodeEnum;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.OaReportDto;
import com.xhqb.spectre.admin.model.dto.OaUpdateReportDto;
import com.xhqb.spectre.admin.model.dto.ReportContentDto;
import com.xhqb.spectre.admin.model.vo.InExportVO;
import com.xhqb.spectre.admin.model.vo.OutExportVO;
import com.xhqb.spectre.admin.model.vo.ReportVO;
import com.xhqb.spectre.admin.service.SerialNumberGenerator;
import com.xhqb.spectre.admin.service.TplUsageService;
import com.xhqb.spectre.admin.service.impl.TplServiceImpl;
import com.xhqb.spectre.admin.service.oa.OaBizService;
import com.xhqb.spectre.admin.service.oa.OaReportService;
import com.xhqb.spectre.admin.service.oa.dto.CreateRequestDTO;
import com.xhqb.spectre.admin.service.oa.vo.DoCreateRequestVO;
import com.xhqb.spectre.admin.service.oa.vo.FlowDataAllInfo;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.oa.TplApproveContent;
import com.xhqb.spectre.common.dal.entity.oa.TplContent;
import com.xhqb.spectre.common.dal.entity.oa.TplOaApprove;
import com.xhqb.spectre.common.dal.mapper.SignMapper;
import com.xhqb.spectre.common.dal.mapper.TplApproveContentMapper;
import com.xhqb.spectre.common.dal.mapper.TplContentMapper;
import com.xhqb.spectre.common.dal.mapper.TplOaApproveMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.OaReportQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OaReportServiceImpl implements OaReportService {

    @Resource
    private TplContentMapper tplContentMapper;

    @Resource
    private TplOaApproveMapper tplOaApproveMapper;

    @Resource
    private TplApproveContentMapper tplApproveContentMapper;

    @Resource
    private OaReportServiceImpl thisProxy;

    @Resource
    private OaBizService oaBizService;

    @Resource
    private SignMapper signMapper;

    @Resource
    private VenusConfig venusConfig;

    @Resource
    private TplServiceImpl tplService;

    @Resource
    private SerialNumberGenerator serialNumberGenerator;

    @Resource
    private TplUsageService tplUsageService;


    @Override
    public String saveOaInReport(OaReportDto oaReportDto) {
        String result = "success";
        try {
            saveContent(oaReportDto, ContentTypeEnum.IN.getCode());
        } catch (BizException e) {
            throw new BizException("内部-保存短信内容失败-" + e.getMessage());
        } catch (Exception e) {
            log.warn("内部-保存短信内容失败; {}", e.getMessage());
            throw new BizException(RespCodeEnum.UNKNOWE, "内部-保存短信内容失败-" + RespCodeEnum.UNKNOWE.getMsg());
        }
        return result;
    }

    @Override
    public String saveOaOutReport(OaReportDto oaReportDto) {
        String result = "success";
        try {
            // 外部报备默认设置签名为小花钱包
            Pattern pattern = Pattern.compile("(【.*?】)");
            for (ReportContentDto item : oaReportDto.getContentList()) {
                item.setSignId(venusConfig.getXhqbSignId() + "");
                item.setSignName(venusConfig.getXhqbSignName());
                String content = item.getSmsContent();
                Matcher matcher = pattern.matcher(content);
                if (matcher.find()) {
                    String group = matcher.group();
                    content = content.replace(group, "");
                    item.setSmsContent(content);
                }
            }
            saveContent(oaReportDto, ContentTypeEnum.OUT.getCode());
        } catch (BizException e) {
            throw new BizException("外部-保存短信内容失败-" + e.getMessage());
        } catch (Exception e) {
            throw new BizException(RespCodeEnum.UNKNOWE, "外部-保存短信内容失败-" + RespCodeEnum.UNKNOWE.getMsg());
        }
        return result;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public String submitOaInReport(OaReportDto oaReportDto) {
        // 保存短信内容
        List<TplContent> saveContentList;
        String result = "success";
        try {
            saveContentList = saveContent(oaReportDto, ContentTypeEnum.IN.getCode());
        } catch (BizException e) {
            throw new BizException("提交内部报备请求失败-" + e.getMessage());
        } catch (Exception e) {
            throw new BizException(RespCodeEnum.UNKNOWE, "提交内部报备请求失败-" + RespCodeEnum.UNKNOWE.getMsg());
        }

        handleOaReportAndStore(oaReportDto, saveContentList, ContentTypeEnum.IN.getCode());

        return result;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public String submitOaOutReport(OaReportDto oaReportDto) {
        // 保存短信内容
        List<TplContent> saveContentList;
        String result = "success";
        try {
            if (CollectionUtils.isNotEmpty(oaReportDto.getChannelCodeList())) {
                // channelCode 渠道列表 ----> channelNameList
                oaReportDto.setChannelNameList(buildChannelNameList(oaReportDto));
            }

            saveContentList = saveContent(oaReportDto, ContentTypeEnum.OUT.getCode());
        } catch (BizException e) {
            throw new BizException("提交外部报备请求失败-" + e.getMessage());
        } catch (Exception e) {
            throw new BizException(RespCodeEnum.UNKNOWE, "提交外部报备请求失败-" + RespCodeEnum.UNKNOWE.getMsg());
        }

        handleOaReportAndStore(oaReportDto, saveContentList, ContentTypeEnum.OUT.getCode());

        return result;
    }


    @Override
    public String batchReportOaOutReport(List<String> contentList) {

        List<TplContent> modelList = tplContentMapper.selectByContentIdList(contentList);
        if (CollectionUtils.isEmpty(modelList)) {
            throw new BizException("批量提交外部报备请求失败-内容不存在");
        }

        checkOutParams(modelList);

        OaReportDto oaReportDto = new OaReportDto();
        Map<String, String> signMap = signMapper.selectEnum(1).stream()
                .collect(Collectors.toMap(s -> s.getId() + "", SignDO::getName));
        List<ReportContentDto> reportDtoList = modelList.stream().map(content -> {
            ReportContentDto contentDto = new ReportContentDto();
            contentDto.setSignId(content.getSignId());
            contentDto.setSmsContent(content.getOriginalContent());
            contentDto.setSignName(signMap.getOrDefault(content.getSignId(), ""));
            return contentDto;
        }).collect(Collectors.toList());

        oaReportDto.setContentList(reportDtoList);
        TplContent tplContent = modelList.get(0);
        oaReportDto.setChannelCodeList(Arrays.asList(tplContent.getChannelCode().split(",")));
        // channelCode 渠道列表 ----> channelNameList
        List<String> channelNameList = buildChannelNameList(oaReportDto);
        oaReportDto.setSmsType(tplContent.getSmsTypeCode());
        oaReportDto.setChannelNameList(channelNameList);

        thisProxy.handleOaReportAndStore(oaReportDto, modelList, ContentTypeEnum.OUT.getCode());
        return "success";
    }


    @Override
    public String batchReportOaInReport(List<String> contentList) {
        List<TplContent> modelList = tplContentMapper.selectByContentIdList(contentList);
        if (CollectionUtils.isEmpty(modelList)) {
            throw new BizException("批量提交内部报备请求失败-内容不存在");
        }
        checkInParams(modelList);

        TplContent tplContent = modelList.get(0);
        OaReportDto oaReportDto = new OaReportDto();
        oaReportDto.setSmsType(tplContent.getSmsTypeCode());
        oaReportDto.setSceneCode(tplContent.getSceneCode());

        Map<String, String> signMap = signMapper.selectEnum(1).stream()
                .collect(Collectors.toMap(s -> s.getId() + "", SignDO::getName));
        List<ReportContentDto> reportDtoList = modelList.stream().map(content -> {
            ReportContentDto contentDto = new ReportContentDto();
            contentDto.setSignId(content.getSignId());
            contentDto.setSmsContent(content.getOriginalContent());
            contentDto.setSignName(signMap.getOrDefault(content.getSignId(), ""));
            return contentDto;
        }).collect(Collectors.toList());

        oaReportDto.setContentList(reportDtoList);
        thisProxy.handleOaReportAndStore(oaReportDto, modelList, ContentTypeEnum.IN.getCode());
        return "success";
    }

    @Override
    public CommonPager<ReportVO> page(OaReportQuery oaReportQuery) {

        //增加调用时间范围条件
        if (oaReportQuery.getCallStartTime() != null && oaReportQuery.getCallEndTime() != null) {
            long startTs = oaReportQuery.getCallStartTime().getTime() / 1000;
            long endTs = oaReportQuery.getCallEndTime().getTime() / 1000;

            Set<String> tplCodes = tplUsageService.findTplCodesBySendTime(startTs, endTs);
            if (tplCodes != null) {
                oaReportQuery.setTplCodeList(tplCodes);
            }
        }
        log.info("查询报备记录开始, oaReportQuery={}", oaReportQuery);
        List<ReportVO> reportVOList = tplContentMapper.selectByQuery(oaReportQuery).stream()
                .map(this::convertToReportVO)
                .collect(Collectors.toList());

        return PageResultUtils.result(
                () -> tplContentMapper.countByQuery(oaReportQuery),
                () -> reportVOList);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public String update(OaUpdateReportDto oaUpdateReportDto) {

        List<TplContent> contentList = tplContentMapper.selectByContentIdList(Collections.singletonList(oaUpdateReportDto.getContentId()));
        if (CollectionUtils.isEmpty(contentList)) {
            throw new BizException("记录不存在");
        }
        TplContent tplContent = contentList.get(0);

        if (!Objects.equals(tplContent.getStatus(), 0)) {
            throw new BizException("该记录不是初始状态，不可修改");
        }

        tplContent.setUpdater(SsoUserInfoUtil.getUserName());
        tplContent.setSignId(oaUpdateReportDto.getSignId());
        tplContent.setUpdateTime(new Date());
        tplContent.setOriginalContent(oaUpdateReportDto.getSmsContent());
        tplContent.setSmsTypeCode(oaUpdateReportDto.getSmsTypeCode());
        tplContent.setSceneCode(oaUpdateReportDto.getSceneCode());
        tplContent.setTag(oaUpdateReportDto.getTag());
        tplContent.setParamType(oaUpdateReportDto.getParamType());
        tplContent.setParamLen(oaUpdateReportDto.getParamLen());
        tplContentMapper.updateByPrimaryKeySelective(tplContent);
        return "success";
    }

    @Override
    public List<FlowDataAllInfo> approve(String contentId) {

        List<TplContent> contentList = tplContentMapper.selectByContentIdList(Collections.singletonList(contentId))
                .stream()
                .filter(tplContent -> !Objects.equals(tplContent.getStatus(), 0))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(contentList)) {
            throw new BizException("不存该审批，不可查看审批详情");
        }
        TplApproveContent tplApproveContent = tplApproveContentMapper.selectByContentId(contentId);
        TplOaApprove tplOaApprove = tplOaApproveMapper.selectByFlowId(tplApproveContent.getFlowId());

        return oaBizService.getFlowDataAllInfo(tplOaApprove.getFlowId(), tplOaApprove.getUserId());

    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public String syncTpl(String contentId) {
        List<TplContent> contentList = tplContentMapper.selectByContentIdList(Collections.singletonList(contentId));
        if (CollectionUtils.isEmpty(contentList)) {
            throw new BizException("记录不存在");
        }
        TplContent tplContent = contentList.get(0);

        if (!Objects.equals(tplContent.getStatus(), 3)) {
            throw new BizException("记录不是审批通过状态，不可同步");
        }

        String tplCode = tplService.syncTpl(tplContent);
        tplContent.setTplCode(tplCode);
        tplContentMapper.updateByPrimaryKeySelective(tplContent);
        return tplCode;
    }

    @Override
    public String delete(String contentId) {
        List<TplContent> contentList = tplContentMapper.selectByContentIdList(Collections.singletonList(contentId));
        if (CollectionUtils.isEmpty(contentList)) {
            throw new BizException("记录不存在");
        }
        TplContent tplContent = contentList.get(0);
        if (!Objects.equals(tplContent.getStatus(), 0)) {
            throw new BizException("记录不是初始状态，不可删除");
        }

        tplContentMapper.updateDeleteTagByContentId(contentId, 1);
        return "success";
    }

    @Override
    public ReportVO detail(String contentId) {
        List<TplContent> contentList = tplContentMapper.selectByContentIdList(Collections.singletonList(contentId));
        if (CollectionUtils.isEmpty(contentList)) {
            throw new BizException("记录不存在");
        }
        return convertToReportVO(contentList.get(0));
    }

    private ReportVO convertToReportVO(TplContent tplContent) {
        ReportVO reportVO = new ReportVO();
        BeanUtils.copyProperties(tplContent, reportVO);
        return reportVO;
    }


    /**
     * 处理并存储OA报备信息
     *
     * @param oaReportDto     OA报备信息实体
     * @param saveContentList 短信内容列表
     * @param type            报备类型，内部或外部
     * @throws BizException 抛出业务异常
     */
    public void handleOaReportAndStore(OaReportDto oaReportDto, List<TplContent> saveContentList, String type) {

        String titlePrefix;
        SmsTypeCodeEnum typeCodeEnum = SmsTypeCodeEnum.fromCode(oaReportDto.getSmsType());
        String smsTypeName = Objects.isNull(typeCodeEnum) ? "" : typeCodeEnum.getName();
        String sceneCodeDesc = SceneCodeEnum.UNREGISTERED.getDesc();
        if (Objects.equals(type, ContentTypeEnum.IN.getCode())) {
            titlePrefix = "内部短信内容报备" + "_" + smsTypeName + "_" + SceneCodeEnum.getByCode(oaReportDto.getSceneCode()).getDesc();
            sceneCodeDesc = SceneCodeEnum.getByCode(oaReportDto.getSceneCode()).getDesc();
            smsTypeName = SmsTypeCodeEnum.getByCode(oaReportDto.getSmsType()).getName();
        } else {
            titlePrefix = "外部短信内容报备_" + SceneCodeEnum.UNREGISTERED.getDesc();
        }

        // 提交 OA 审批请求准备
        Map<String, String> signMap = signMapper.selectEnum(1).stream()
                .collect(Collectors.toMap(s -> s.getId() + "", SignDO::getName));
        Map<String, String> contentMap = saveContentList.stream()
                .collect(Collectors.toMap(TplContent::getContentId,
                        content -> signMap.getOrDefault(content.getSignId(), "") + content.getOriginalContent()));
        String title = titlePrefix + serialNumberGenerator.getCommonResult().getSerialNumber();
        CreateRequestDTO createRequestDTO = new CreateRequestDTO();
        // 设置createRequestDTO属性
        createRequestDTO.setWorkflowId(venusConfig.getOaWorkFlowId());
        createRequestDTO.setSmsTypeCode(Optional.ofNullable(smsTypeName).orElse(""));
        createRequestDTO.setSceneCode(sceneCodeDesc);
        createRequestDTO.setTitle(title);
        createRequestDTO.setContentType(ContentTypeEnum.getOaCodeByCode(type) + "");
        createRequestDTO.setChannelName(CollectionUtils.isEmpty(oaReportDto.getChannelNameList()) ? "" : String.join(",", oaReportDto.getChannelNameList()));
        createRequestDTO.setContentMap(contentMap);
        // 调用服务创建OA报备请求
        DoCreateRequestVO doCreateRequestVO;
        try {
            doCreateRequestVO = oaBizService.doCreateRequest(createRequestDTO);
            if (Objects.isNull(doCreateRequestVO) || ObjectUtils.isEmpty(doCreateRequestVO.getData())) {
                throw new BizException("创建OA报备请求失败，返回结果为空");
            }
        } catch (Exception e) {
            log.error("创建OA报备请求失败，参数：{}", createRequestDTO, e);
            throw new BizException("创建OA报备请求失败");
        }

        // 创建审批单
        TplOaApprove tplOaApprove = getTplOaApprove(oaReportDto, doCreateRequestVO, title);
        tplOaApproveMapper.insertSelective(tplOaApprove);

        // 关联审批单和短信内容
        List<TplApproveContent> tplApproveContentList = saveContentList.stream()
                .map(content -> {
                    TplApproveContent tplApproveContent = new TplApproveContent();
                    tplApproveContent.setFlowId(doCreateRequestVO.getData().getRequestid());
                    tplApproveContent.setContentId(content.getContentId());
                    tplApproveContent.setSignId(content.getSignId());
                    return tplApproveContent;
                })
                .collect(Collectors.toList());

        tplApproveContentList.forEach(tplApproveContentMapper::insertSelective);
        saveContentList.forEach(content -> {
            content.setStatus(1);
            content.setUpdateTime(null);
            tplContentMapper.updateByPrimaryKeySelective(content);
        });
    }


    private TplOaApprove getTplOaApprove(OaReportDto oaReportDto, DoCreateRequestVO doCreateRequestVO, String title) {
        String userName = SsoUserInfoUtil.getUserName();
        Date curTime = new Date();
        TplOaApprove tplOaApprove = new TplOaApprove();
        String flowId = doCreateRequestVO.getData().getRequestid();
        tplOaApprove.setFlowId(flowId);
        tplOaApprove.setFlowTypeId(venusConfig.getOaWorkFlowId() + "");
        tplOaApprove.setSmsTypeCode(oaReportDto.getSmsType());
        tplOaApprove.setSceneCode(oaReportDto.getSceneCode());
        tplOaApprove.setTitle(title);
        tplOaApprove.setUserId(doCreateRequestVO.getUserId());
        tplOaApprove.setStatus(1);
        tplOaApprove.setCreator(userName);
        tplOaApprove.setCreateTime(curTime);
        tplOaApprove.setUpdater(userName);
        tplOaApprove.setUpdateTime(curTime);
        if (CollectionUtils.isNotEmpty(oaReportDto.getContentList())) {
            List<String> contentList = oaReportDto.getContentList().stream()
                    .map(reportContentDto -> {
                        String content = reportContentDto.getSmsContent();
                        if (ObjectUtils.isNotEmpty(reportContentDto.getSignName())) {
                            content = reportContentDto.getSignName() + content;
                        }
                        return content;
                    })
                    .collect(Collectors.toList());
            tplOaApprove.setOriginalContent(JSON.toJSONString(contentList));
        }
        return tplOaApprove;
    }


    /**
     * 保存短信内容
     *
     * @param oaReportDto 报表数据对象
     * @param prefix      内容类型 IN: 内部 OUT: 外部
     */
    public List<TplContent> saveContent(OaReportDto oaReportDto, String prefix) {
        List<ReportContentDto> dtoList = oaReportDto.getContentList();
        checkDTO(dtoList);
        Date curTime = new Date();
        String userName = SsoUserInfoUtil.getUserName();
        List<TplContent> resultList = new LinkedList<>();
        for (ReportContentDto dto : dtoList) {
            TplContent content = new TplContent();
            content.setContentId(serialNumberGenerator.getReportResult(prefix).getCode());
            content.setOriginalContent(dto.getSmsContent());
            content.setSceneCode(Optional.ofNullable(oaReportDto.getSceneCode()).orElse(""));
            content.setSignId(dto.getSignId());
            content.setStatus(0);
            content.setType(prefix);
            content.setCreator(userName);
            content.setUpdater(userName);
            content.setCreateTime(curTime);
            content.setUpdateTime(curTime);
            content.setSmsTypeCode(Optional.ofNullable(oaReportDto.getSmsType()).orElse(""));
            content.setChannelCode(CollectionUtils.isEmpty(oaReportDto.getChannelCodeList()) ? "" : String.join(",", oaReportDto.getChannelCodeList()));
            content.setTag(Objects.equals(oaReportDto.getTag(), 1) ? 1 : 0);
            content.setParamType(dto.getParamType());
            content.setParamLen(dto.getParamLen());
            resultList.add(content);
        }

        if (CollectionUtils.isNotEmpty(resultList)) {
            resultList.forEach(tplContentMapper::insertSelective);
        }

        log.info("保存短信内容成功 size:{}", resultList.size());
        return resultList;

    }

    private void checkDTO(List<ReportContentDto> dtoList) {

        if (CollectionUtils.isEmpty(dtoList)) {
            throw new BizException("短信内容不能为空");
        }

        if (dtoList.size() > 10) {
            throw new BizException("短信内容数量不能超过10条");
        }

        for (ReportContentDto dto : dtoList) {
            if (StringUtils.isEmpty(dto.getSmsContent())) {
                throw new BizException("短信内容不能为空");
            }
            if (dto.getSmsContent().length() > 1023) {
                throw new BizException("短信内容长度不能超过1023个字符");
            }
        }
    }


    private void checkInParams(List<TplContent> modelList) {

        if (CollectionUtils.isEmpty(modelList)) {
            throw new BizException("批量提交短信内容不能为空");
        }

        if (modelList.size() > 10) {
            throw new BizException("批量提交短信数量不能超过10条");
        }
//        // 当前版本短信类型只能是营销
//        for (TplContent model : modelList) {
//            if (!Objects.equals(model.getSmsTypeCode(), SmsTypeCodeEnum.MARKET.getCode())) {
//                throw new BizException("当前版本短信类型只能是营销");
//            }
//        }
        // 相同的短信类型和场景码
        if (modelList.stream().map(TplContent::getSmsTypeCode).distinct().count() != 1
                || modelList.stream().map(TplContent::getSceneCode).distinct().count() != 1) {
            throw new BizException("批量提交短信类型或场景码不一致");
        }
    }

    private void checkOutParams(List<TplContent> modelList) {
        if (modelList.size() > 10) {
            throw new BizException("批量提交短信数量不能超过10条");
        }
        // 相同渠道 code检验
        if (modelList.stream().map(TplContent::getChannelCode).distinct().count() != 1) {
            throw new BizException("批量提交渠道简称不一致");
        }
    }

    private List<String> buildChannelNameList(OaReportDto oaReportDto) {
        JSONObject jsonObject = JSON.parseObject(venusConfig.getOutChannel());
        List<String> channelNameList = new LinkedList<>();
        for (String channel : oaReportDto.getChannelCodeList()) {
            String channelCode = jsonObject.getString(channel);
            if (StringUtils.isNotEmpty(channelCode)) {
                channelNameList.add(channelCode);
            }
        }
        return channelNameList;
    }

    /**
     * 导出报表
     *
     * @param oaReportQuery
     * @return
     */
    private List<TplContent> export(OaReportQuery oaReportQuery) {

        List<TplContent> tplContentList;
        if (StringUtils.isNotEmpty(oaReportQuery.getContentId())) {
            List<String> contentIdList = Arrays.stream(oaReportQuery.getContentId().split(","))
                    .map(String::trim)
                    .filter(id -> !id.isEmpty()).collect(Collectors.toList());
            tplContentList = tplContentMapper.selectByContentIdList(contentIdList).stream()
                    .map(this::convertToReportVO)
                    .collect(Collectors.toList());
        } else {
            if (oaReportQuery.getCallStartTime() != null
                    && oaReportQuery.getCallEndTime() != null) {
                long startTs = oaReportQuery.getCallStartTime().getTime() / 1000;
                long endTs = oaReportQuery.getCallEndTime().getTime() / 1000;
                Set<String> tplCodes = tplUsageService.findTplCodesBySendTime(startTs, endTs);
                if (CollectionUtils.isNotEmpty(tplCodes)) {
                    oaReportQuery.setTplCodeList(tplCodes);
                }
            }

            tplContentList = tplContentMapper.selectByQuery(oaReportQuery);
        }

        return tplContentList;
    }

    @Override
    public List<InExportVO> inExport(OaReportQuery oaReportQuery) {

        Map<Integer, String> signMap = signMapper.selectAll().stream().collect(Collectors.toMap(SignDO::getId, SignDO::getName));
        return export(oaReportQuery).stream()
                .map(this::convertToReportVO)
                .map(report -> {
                    String signName = "";
                    String signId = report.getSignId();
                    if (Objects.nonNull(signId)) {
                        signName = signMap.getOrDefault(Integer.valueOf(signId), "");
                    }
                    String smsTypeCodeName = "";
                    String smsTypeCode = report.getSmsTypeCode();
                    if (Objects.nonNull(smsTypeCode)) {
                        SmsTypeCodeEnum typeCodeEnum = SmsTypeCodeEnum.fromCode(smsTypeCode);
                        if (typeCodeEnum != null) {
                            smsTypeCodeName = typeCodeEnum.getName();
                        }
                    }

                    return InExportVO.build(report, signName, smsTypeCodeName);
                }).collect(Collectors.toList());
    }

    @Override
    public List<OutExportVO> outExport(OaReportQuery oaReportQuery) {
        return export(oaReportQuery).stream()
                .map(this::convertToReportVO)
                .map(report -> {
                    return OutExportVO.build(report, report.getChannelCode());
                }).collect(Collectors.toList());

    }
}
