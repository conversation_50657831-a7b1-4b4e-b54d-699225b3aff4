package com.xhqb.spectre.admin.mq.consumer.handler;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.batchtask.MessageSendFactory;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.limit.BatchSubmitRateLimitService;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryContext;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryResult;
import com.xhqb.spectre.admin.batchtask.send.record.MessageSendFailedRecord;
import com.xhqb.spectre.admin.model.vo.batchtask.ParamItemVO;
import com.xhqb.spectre.admin.mq.consumer.ConsumerHandler;
import com.xhqb.spectre.admin.mq.event.BatchTaskParamEvent;
import com.xhqb.spectre.admin.mq.event.TaskParamResult;
import com.xhqb.spectre.admin.mq.event.listener.BatchTaskParamEventListener;
import com.xhqb.spectre.admin.mq.message.BatchTaskParamMessage;
import com.xhqb.spectre.admin.service.BatchTaskLogService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.BatchTaskLogDO;
import com.xhqb.spectre.common.dal.entity.BatchTaskParamDO;
import com.xhqb.spectre.common.dal.entity.BatchTaskReportDO;
import com.xhqb.spectre.common.dal.mapper.BatchTaskParamMapper;
import com.xhqb.spectre.common.dal.mapper.BatchTaskReportMapper;
import com.xhqb.spectre.common.enums.BatchTaskParamStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 群发任务参数消费处理器
 * 1. 验证taskId是否处于发送中状态,其他状态则不进行处理
 * 2. 验证模板是否正常
 * 3. 验证签名是否正常
 * 4. 验证t_batch_task_param状态是否为未发送,其他状态不进行处理
 * 5. 更新t_batch_task_param状态为发送中
 * 6. 更新t_batch_task 真实发送时间
 * 7. 发起短信推送接口
 * 8. 生成短信报告
 * 9. 更新t_batch_task_param状态为已发送
 * 10. 通知发送结果
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Component
@Slf4j
public class BatchTaskParamConsumerHandler implements ConsumerHandler {

    @Resource
    private BatchTaskParamMapper batchTaskParamMapper;
    @Resource
    private BatchTaskReportMapper batchTaskReportMapper;
    @Resource
    private MessageSendFactory messageSendFactory;
    @Resource
    private BatchTaskParamEventListener batchTaskParamEventListener;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private BatchTaskLogService batchTaskLogService;
    @Resource
    private BatchSubmitRateLimitService batchSubmitRateLimitService;

    /**
     * 群发任务参数消费处理
     *
     * @param message
     */
    @Override
    public void handler(String message) {
        long start = System.currentTimeMillis();
        log.info("群发任务参数消费处理器收到消息 = {}", message);
        BatchTaskParamMessage batchTaskParamMessage = JSON.parseObject(message, BatchTaskParamMessage.class);
        TaskParamResult taskParamResult = null;
        try {
            if (!this.isTaskCanceled(batchTaskParamMessage)) {
                batchSubmitRateLimitService.acquire(batchTaskParamMessage.getTaskId());
                taskParamResult = this.doHandler(batchTaskParamMessage, start);
            }
        } catch (Exception e) {
            log.error("群发任务参数消费处理失败,message ={}", message, e);
        }

        if (Objects.nonNull(taskParamResult)) {
            // 通知发送结果
            batchTaskParamEventListener.publish(new BatchTaskParamEvent(batchTaskParamMessage, taskParamResult));
        }

        log.info("群发任务参数消费处理器处理完成，batchTaskParamMessage = {}, 耗时 = {}", message, (System.currentTimeMillis() - start));
    }

    /**
     * 判断任务是否已取消
     *
     * @param batchTaskParamMessage
     * @return 返回true表示当前任务已取消
     */
    private boolean isTaskCanceled(BatchTaskParamMessage batchTaskParamMessage) {
        return stringRedisTemplate.opsForHash().hasKey(RedisKeys.BatchTaskKeys.BATCH_TASK_CANCELED_FLAG_HASH_KEY, String.valueOf(batchTaskParamMessage.getTaskId()));
    }

    /**
     * 做消息处理
     * <p>
     * 若抛出异常，则需要做task总数递减，若返回为null，task总数不做递减(重复消费消息)
     *
     * @param batchTaskParamMessage
     * @param start                 消费消息开始时间
     * @return 返回已经发送的数量量  如果返回为null ，则表示记录总数不需要递减(subTaskCountFromCache)
     */
    public TaskParamResult doHandler(BatchTaskParamMessage batchTaskParamMessage, long start) {
        BatchTaskParamDO batchTaskParamDO = this.checkTaskParamStatus(batchTaskParamMessage);
        if (Objects.isNull(batchTaskParamDO)) {
            // 群发参数状态检测失败 忽略该消息
            return null;
        }

        // 更新群发参数状态为发送中
        boolean hasAffectRows = this.updateTaskParamStatusToSending(batchTaskParamMessage);
        if (!hasAffectRows) {
            // 更新群发参数参数状态为发送中 失败，不往下进行处理
            // 该判断主要是为了防止群发任务参数并发下被重复发送(消息被重复消费时会出现该问题)
            log.warn("更新群发任务参数状态为发送中失败, batchTaskParamMessage = {}", batchTaskParamMessage);
            return null;
        }

        // 发送短信消息
        List<SingleFactoryResult> factoryResultList = this.sendMessage(batchTaskParamMessage, batchTaskParamDO);
        if (Objects.isNull(factoryResultList)) {
            // 返回空表示当前没有参数
            // 没有参数时 也需要记录一下报表信息 2021-11-24
            factoryResultList = new ArrayList<>();
            log.warn("群发参数数据为空为空,导致factoryResultList为空,发送短信成功数为0, taskId = {},taskParamId = {}", batchTaskParamMessage.getTaskId(), batchTaskParamMessage.getTaskParamId());
        }

        // 记录发送报表
        TaskParamResult taskParamResult = this.recordTaskReport(batchTaskParamMessage, batchTaskParamDO, (System.currentTimeMillis() - start), factoryResultList);

        // 更新群发参数状态为已发送
        this.updateTaskParamStatus(batchTaskParamMessage, BatchTaskParamStatusEnum.SENT.getCode());

        return taskParamResult;
    }


    /**
     * 验证t_batch_task_param状态是否为未发送,其他状态不进行处理
     *
     * @param batchTaskParamMessage
     * @return 返回验证通过的群发参数对象, 验证不通过返回null
     */
    private BatchTaskParamDO checkTaskParamStatus(BatchTaskParamMessage batchTaskParamMessage) {
        BatchTaskParamDO batchTaskParamDO = batchTaskParamMapper.selectByPrimaryKey(batchTaskParamMessage.getTaskParamId());
        if (Objects.isNull(batchTaskParamDO)) {
            log.warn("当前群发参数不存在 = {}", batchTaskParamMessage);
            return null;
        }

        if (!Objects.equals(batchTaskParamDO.getSendStatus(), BatchTaskParamStatusEnum.UN_SEND.getCode())) {
            log.warn("当前群发任务参数状态不是未发送状态,status = {},batchTaskParamMessage = {}", batchTaskParamDO.getSendStatus(), batchTaskParamMessage);
            return null;
        }
        // 状态正常
        return batchTaskParamDO;
    }

    /**
     * 更新群发任务状态为发送中
     * <p>
     * 若更新失败 则不进行之后的短信发送逻辑
     * <p>
     * 为了防止消息重复消费时造成的短信重复发送
     *
     * @param batchTaskParamMessage
     * @return 返回true表示群发任务状态修改成功，false修改失败
     */
    private boolean updateTaskParamStatusToSending(BatchTaskParamMessage batchTaskParamMessage) {
        Integer destStatus = BatchTaskParamStatusEnum.SENDING.getCode();
        Integer sourceStatus = BatchTaskParamStatusEnum.UN_SEND.getCode();
        int affectRows = batchTaskParamMapper.updateSendStatusById(batchTaskParamMessage.getTaskParamId(), destStatus, sourceStatus);
        return affectRows > 0;
    }

    /**
     * 更新t_batch_task_param状态为发送中
     *
     * @param batchTaskParamMessage
     * @param sendStatus
     */
    private void updateTaskParamStatus(BatchTaskParamMessage batchTaskParamMessage, Integer sendStatus) {
        BatchTaskParamDO batchTaskParamDO = new BatchTaskParamDO();
        batchTaskParamDO.setId(batchTaskParamMessage.getTaskParamId());
        batchTaskParamDO.setSendStatus(sendStatus);
        batchTaskParamMapper.updateByPrimaryKeySelective(batchTaskParamDO);
    }

    /**
     * 发送短信消息
     *
     * @param batchTaskParamMessage
     * @param batchTaskParamDO
     * @return
     */
    private List<SingleFactoryResult> sendMessage(BatchTaskParamMessage batchTaskParamMessage, BatchTaskParamDO batchTaskParamDO) {
        String paramJsonArray = batchTaskParamDO.getParamJsonArray();
        List<ParamItemVO> paramItemList = JSON.parseArray(paramJsonArray, ParamItemVO.class);
        // 过滤掉包含sendSkip标记的数据 2021-11-24
        paramItemList = paramItemList.stream().filter(s -> !hasSkipSend(s)).collect(Collectors.toList());
        if (CommonUtil.isEmpty(paramItemList)) {
            // 没有数据 则不需要进行发送处理
            log.warn("当前群发参数数据为空 = {},batchTaskParamMessage ={}", paramJsonArray, JSON.toJSONString(batchTaskParamMessage));
            return null;
        }

        //重复号码拆分为多个context, [1,2,3,1,3,1] => [1,2,3] [1,3] [1]
        List<Map<String, ParamItemVO>> unique = new ArrayList<>();
        paramItemList.forEach(p -> {
            String mobile = p.get(BatchTaskConstants.DataType.MOBILE);
            boolean s = false;
            for (Map<String, ParamItemVO> v : unique) {
                if (!v.containsKey(mobile)) {
                    v.put(mobile, p);
                    s = true;
                    break;
                }
            }
            if (!s) {
                Map<String, ParamItemVO> n = new HashMap<>();
                n.put(mobile, p);
                unique.add(n);
            }
        });

        List<SingleFactoryResult> results = new ArrayList<>();
        unique.forEach(u -> {
            List<ParamItemVO> list = new ArrayList<>(u.values());
            // 构建单个消息发送上下文信息
            SingleFactoryContext singleFactoryContext = SingleFactoryContext.builder()
                    .appCode(batchTaskParamMessage.getAppCode())
                    .tplCode(batchTaskParamMessage.getTplCode())
                    .signName(batchTaskParamMessage.getSignName())
                    .smsCodeType(batchTaskParamMessage.getSmsCodeType())
                    .batchId(batchTaskParamMessage.getTaskId())
                    .signCode(batchTaskParamMessage.getSignCode())
                    .build();

            Integer tplType = batchTaskParamMessage.getTplType();
            if (Objects.equals(tplType, BatchTaskConstants.TplType.PLACEHOLDER)) {
                // 占位符处理
                this.placeholderMessageHandler(singleFactoryContext, list);
            } else {
                // [*] 处理
                this.normalMessageHandler(singleFactoryContext, list);
            }

            // 发送短信
            List<SingleFactoryResult> result = messageSendFactory.singleSendMessage(singleFactoryContext);
            if (!CollectionUtils.isEmpty(result)) {
                results.addAll(result);
            }
        });

        return CollectionUtils.isEmpty(results) ? null : results;
    }

    /**
     * 正常消息处理
     *
     * @param context
     * @param paramItemList
     */
    private void normalMessageHandler(SingleFactoryContext context, List<ParamItemVO> paramItemList) {
        List<String> mobileList = new ArrayList<>(paramItemList.size());
        List<List<String>> paramMapList = new ArrayList<>(paramItemList.size());
        String mobile;
        List<String> paramList;
        for (ParamItemVO paramItem : paramItemList) {
            // 获取到手机号
            mobile = paramItem.get(BatchTaskConstants.DataType.MOBILE);
            mobileList.add(mobile);
            // 获取到参数信息
            paramList = paramItem.toParamList();
            paramMapList.add(paramList);
        }

        context.setMobileList(mobileList);
        context.setParamMapList(paramMapList);
    }

    /**
     * 占位符消息处理 ${xx}
     *
     * @param context
     * @param paramItemList
     */
    private void placeholderMessageHandler(SingleFactoryContext context, List<ParamItemVO> paramItemList) {
        List<Map<String, String>> placeholderList = new ArrayList<>(paramItemList.size());
        List<String> mobileList = new ArrayList<>(paramItemList.size());
        String mobile;
        Map<String, String> paramList;
        for (ParamItemVO paramItem : paramItemList) {
            // 获取到手机号
            mobile = paramItem.get(BatchTaskConstants.DataType.MOBILE);
            mobileList.add(mobile);
            // 获取到参数信息
            paramList = paramItem.toParamMap();
            placeholderList.add(paramList);
        }

        context.setMobileList(mobileList);
        context.setPlaceHolderList(placeholderList);
    }

    /**
     * 记录群发任务报表
     *
     * @param batchTaskParamMessage
     * @param batchTaskParamDO
     * @param cost                  发送耗时 单位毫秒
     * @param factoryResultList
     * @return 返回发送的总数量
     */
    private TaskParamResult recordTaskReport(BatchTaskParamMessage batchTaskParamMessage, BatchTaskParamDO batchTaskParamDO, long cost, List<SingleFactoryResult> factoryResultList) {
        int total = 0;
        List<MessageSendFailedRecord> allFailedRecord = null;
        int successCount = 0;
        try {
            total = SingleFactoryResult.getAllTotalCount(factoryResultList);
            BatchTaskReportDO batchTaskReportDO = BatchTaskReportDO.builder()
                    .taskId(batchTaskParamMessage.getTaskId())
                    .taskParamId(batchTaskParamMessage.getTaskParamId())
                    .cost((int) cost)
                    .total(total)
                    .build();
            // 失败的数量
            Integer allFailCount = SingleFactoryResult.getAllFailCount(factoryResultList);
            batchTaskReportDO.setFail(allFailCount);
            if (allFailCount > 0) {
                // 设置失败的手机号
                allFailedRecord = SingleFactoryResult.getAllFailedRecord(factoryResultList);
                batchTaskReportDO.setErrorStat(JSON.toJSONString(allFailedRecord));
            }
            batchTaskReportDO.setSuccess(total - batchTaskReportDO.getFail());
            successCount = batchTaskReportDO.getSuccess();
            batchTaskReportMapper.insertSelective(batchTaskReportDO);
        } catch (Exception e) {
            log.error("记录群发任务报表失败,batchTaskParamMessage = {}, factoryResultList = {}", batchTaskParamMessage, JSON.toJSONString(factoryResultList), e);
        }

        try {
            // 保存群发日志
            saveBatchTaskLog(batchTaskParamMessage, batchTaskParamDO, successCount, allFailedRecord);
        } catch (Exception e) {
            log.error("保存群发日志失败,batchTaskParamMessage = {}", JSON.toJSONString(batchTaskParamMessage), e);
        }

        return new TaskParamResult(total, successCount);
    }

    /**
     * 保存群发日志
     *
     * @param batchTaskParamMessage
     * @param batchTaskParamDO
     * @param successCount
     * @param allFailedRecord
     */
    private void saveBatchTaskLog(BatchTaskParamMessage batchTaskParamMessage, BatchTaskParamDO batchTaskParamDO, int successCount, List<MessageSendFailedRecord> allFailedRecord) {
        long start = System.currentTimeMillis();
        String paramJsonArray = batchTaskParamDO.getParamJsonArray();
        List<ParamItemVO> paramItemList = JSON.parseArray(paramJsonArray, ParamItemVO.class);
        // 过滤掉包含sendSkip标记的数据 2021-11-24
        paramItemList = paramItemList.stream().filter(s -> !hasSkipSend(s)).collect(Collectors.toList());
        if (CommonUtil.isEmpty(paramItemList)) {
            // 没有数据 则不需要进行日志保存操作
            log.warn("当前群发参数数据为空,不保存群发日志信息 = {},batchTaskParamMessage ={}", paramJsonArray, JSON.toJSONString(batchTaskParamMessage));
            return;
        }

        if (successCount <= 0) {
            log.warn("当前群发任务成功数为0,所有记录都会被标记为失败,batchTaskParamMessage = {},successCount = {}", batchTaskParamMessage, successCount);
        }

        // 从失败数据中获取失败 若存在 则失败 否则认为其为成功
        Map<String, MessageSendFailedRecord> mobileFailMapping = failedRecordToMap(allFailedRecord);
        // 群发日志列表
        List<BatchTaskLogDO> taskLogList = new ArrayList<>(paramItemList.size());

        BatchTaskLogDO batchTaskLogDO = null;
        String cid = null;
        String mobile = null;
        MessageSendFailedRecord sendFailedRecord = null;
        for (ParamItemVO paramItemVO : paramItemList) {
            cid = paramItemVO.get(BatchTaskConstants.DataType.CID);
            if (StringUtils.isBlank(cid)) {
                cid = "";
            }
            mobile = paramItemVO.get(BatchTaskConstants.DataType.MOBILE);
            if (StringUtils.isBlank(mobile)) {
                mobile = "";
            }
            batchTaskLogDO = BatchTaskLogDO.builder()
                    .taskId(batchTaskParamMessage.getTaskId())
                    .taskParamId(batchTaskParamMessage.getTaskParamId())
                    .cid(cid)
                    .mobile(mobile)
                    .param(JSON.toJSONString(paramItemVO))
                    .build();

            if (StringUtils.isBlank(mobile)) {
                // 手机号
                batchTaskLogDO.setStatus(BatchTaskConstants.TaskLog.STATUS_ERROR);
                batchTaskLogDO.setDescription("手机号码为空");
                batchTaskLogDO.setErrCode("e0000");
                taskLogList.add(batchTaskLogDO);
                continue;
            }

            // 根据手机号从错误信息中查找，若找到错误信息 那么则认为发送失败
            // 否则认为当前任务处理成功
            sendFailedRecord = mobileFailMapping.get(mobile);
            if (Objects.nonNull(sendFailedRecord)) {
                batchTaskLogDO.setStatus(BatchTaskConstants.TaskLog.STATUS_ERROR);
                batchTaskLogDO.setDescription(sendFailedRecord.getFileMsg());
                // 防止接口未返回errCode
                if (StringUtils.isNotBlank(sendFailedRecord.getFileCode())) {
                    batchTaskLogDO.setErrCode(sendFailedRecord.getFileCode());
                } else {
                    batchTaskLogDO.setErrCode("e9999");
                }
                taskLogList.add(batchTaskLogDO);
                continue;
            }

            // 如果successCount小于等于0 那么没有在映射错误信息也认为当前任务发送失败
            batchTaskLogDO.setStatus(successCount <= 0 ? BatchTaskConstants.TaskLog.STATUS_ERROR : BatchTaskConstants.TaskLog.STATUS_SUCCESS);
            batchTaskLogDO.setDescription("");
            batchTaskLogDO.setErrCode("");
            taskLogList.add(batchTaskLogDO);
        }
        batchTaskLogService.batchInsert(taskLogList);
        log.info("保存群发日志记录数据库耗时 = {},batchTaskParamMessage = {}", (System.currentTimeMillis() - start), JSON.toJSONString(batchTaskParamMessage));
    }

    /**
     * 错误信息转换map
     * <p>
     * key=手机号码
     *
     * @param allFailedRecord
     * @return
     */
    private Map<String, MessageSendFailedRecord> failedRecordToMap(List<MessageSendFailedRecord> allFailedRecord) {
        if (CommonUtil.isEmpty(allFailedRecord)) {
            return new HashMap<>();
        }
        // key -> mobile
        Map<String, MessageSendFailedRecord> mapping = new HashMap<>(64);
        for (MessageSendFailedRecord record : allFailedRecord) {
            if (Objects.nonNull(record)) {
                mapping.put(record.getPhoneNumber(), record);
            }
        }
        return mapping;
    }

    /**
     * 添加任务取消标记
     *
     * @param taskId
     */
    public void addTaskCanceled(Integer taskId) {
        String keyAndValue = String.valueOf(taskId);
        log.info("添加群发任务取消标记至缓存, taskId = {}", taskId);
        stringRedisTemplate.opsForHash().put(RedisKeys.BatchTaskKeys.BATCH_TASK_CANCELED_FLAG_HASH_KEY, keyAndValue, keyAndValue);
    }

    /**
     * 删除任务取消标记
     *
     * @param taskId
     */
    public void removeTaskCanceled(Integer taskId) {
        log.info("从缓存中移除群发任务取消标记,taskId = {}", taskId);
        stringRedisTemplate.opsForHash().delete(RedisKeys.BatchTaskKeys.BATCH_TASK_CANCELED_FLAG_HASH_KEY, String.valueOf(taskId));
    }

    /**
     * 判断是否包含skipSend属性
     *
     * @param paramItemVO
     * @return true 包含skip属性
     */
    private boolean hasSkipSend(ParamItemVO paramItemVO) {
        try {
            String skipSend = paramItemVO.get(BatchTaskConstants.DataType.SKIP_SEND);
            return StringUtils.equalsAnyIgnoreCase(skipSend, "true");
        } catch (Exception e) {
            log.warn("判断是否包含skipSend属性失败,paramItemVO ={}", JSON.toJSONString(paramItemVO), e);
        }
        return false;
    }

}
