package com.xhqb.spectre.admin.model.vo.batchtask;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 文件检测结果
 *
 * <AUTHOR>
 * @date 2021/9/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CheckResultVO implements Serializable {

    /**
     * 文件存储地址
     */
    private String url;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件后缀名
     */
    private String fileType;
    /**
     * 文件md5值
     */
    private String fileMd5;
    /**
     * 数据类型 cid/mobile
     */
    private String dataType;
    /**
     * 有效数据量
     */
    private Integer validCount;
    /**
     * 无效数量数量
     */
    private Integer badCount;

    /**
     * 无效数量信息
     */
    private List<String> badInfo;
    /**
     * 缺失参数数量
     */
    private Integer missCount;
    /**
     * 参数缺失信息
     */
    private List<String> missInfo;
    /**
     * 重复数据数量
     */
    private Integer repeatCount;

    /**
     * 重复数据信息
     */
    private List<String> repeatInfo;

    /**
     * 空号数量
     */
    private Integer phoneEmptyCount;
    /**
     * 空号数据
     */
    private List<String> phoneEmptyInfo;

    /**
     * 停机数量
     */
    private Integer phoneHaltCount;

    /**
     * 停机数量
     */
    private List<String> phoneHaltInfo;

}
