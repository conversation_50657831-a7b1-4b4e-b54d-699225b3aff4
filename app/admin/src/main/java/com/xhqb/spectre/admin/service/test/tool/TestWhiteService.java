package com.xhqb.spectre.admin.service.test.tool;

import com.xhqb.spectre.admin.model.dto.TestWhiteDTO;
import com.xhqb.spectre.admin.model.vo.TestWhiteVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.TestWhiteQuery;

import java.util.Map;

public interface TestWhiteService {
    CommonPager<TestWhiteVO> listByPage(TestWhiteQuery testWhiteQuery);

    TestWhiteVO detail(Integer id);

    Integer delete(Integer id);

    Integer add(TestWhiteDTO testWhiteDTO);

    Integer update(TestWhiteDTO testWhiteDTO);

    Map<String, Integer> countAllByBrand();

}
