package com.xhqb.spectre.admin.statistics.vo;

import com.xhqb.spectre.admin.statistics.entity.SmsHisStatisDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/1 15:41
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsHisStatisVO implements Serializable {

    private static final long serialVersionUID = -6099424463302799583L;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 累积发送总量
     */
    private Long hisSendTotal;

    /**
     * 累积计费总量
     */
    private Long hisBillingTotal;

    /**
     * 上月发送量
     */
    private Long lastMonthSendTotal;

    /**
     * 上月计费量
     */
    private Long lastMonthBillingTotal;

    /**
     * 本月发送量
     */
    private Long curMonthSendTotal;

    /**
     * 本月计费量
     */
    private Long curMonthBillingTotal;

    /**
     * 昨日发送量
     */
    private Long lastDaySendTotal;

    /**
     * 昨日计费量
     */
    private Long lastDayBillingTotal;

    /**
     * 今日发送量
     */
    private Long todaySendTotal;

    /**
     * 今日计费总量
     */
    private Long todayBillingTotal;

    public static SmsHisStatisVO buildSmsHisStatisVO(SmsHisStatisDO item) {
        return SmsHisStatisVO.builder()
                .smsTypeCode(item.getCode())
                .hisSendTotal(item.getHisSendTotal())
                .hisBillingTotal(item.getHisBillingTotal())
                .lastMonthSendTotal(item.getLastmonthSendTotal())
                .lastMonthBillingTotal(item.getLastmonthBillingTotal())
                .curMonthSendTotal(item.getCurmonthSendTotal())
                .curMonthBillingTotal(item.getCurmonthBillingTotal())
                .lastDaySendTotal(item.getLastdaySendTotal())
                .lastDayBillingTotal(item.getLastdayBillingTotal())
                .todaySendTotal(item.getTodaySendTotal())
                .todayBillingTotal(item.getTodayBillingTotal())
                .build();
    }
}
