package com.xhqb.spectre.admin.controller;

import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.UserShortUrlDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.service.UserShortUrlService;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.UserShortUrlQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
/**
 * 用户短链
 * @Author: yang<PERSON><PERSON><PERSON>
 * @Date: 2024/04/08
 * @Description:
 */
@RestController
@RequestMapping("/userShortUrl")
@Slf4j
public class UserShortUrlController {

    @Resource
    private UserShortUrlService userShortUrlService;

    /**
     * 用户短链分页列表
     * @param userShortUrlQuery 请求参数
     * @param pageNum 页吗
     * @param pageSize 页面大小
     * @return
     */
    @GetMapping("")
    public AdminResult page(@ModelAttribute UserShortUrlQuery userShortUrlQuery, Integer pageNum, Integer pageSize){
        userShortUrlQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        log.info("短链模版分页请求参数｜shortUrlTplQuery:{}", JsonLogUtil.toJSONString(userShortUrlQuery));
        return AdminResult.success(userShortUrlService.page(userShortUrlQuery));
    }

    /**
     * 新增
     * @param userShortUrlDTO 新增参数
     * @return 主键id
     */
    @PostMapping("/add")
    @LogOpTime(OpLogConstant.MODULE_SHORT_URL_TPL)
    public AdminResult add(@RequestBody UserShortUrlDTO userShortUrlDTO) {
        log.info("add shortUrl, userShortUrlDTO: {}", JsonLogUtil.toJSONString(userShortUrlDTO));
        ValidatorUtil.validate(userShortUrlDTO);
        return AdminResult.success(userShortUrlService.add(userShortUrlDTO));
    }

    /**
     * 详情
     * @param id 主键id
     * @return 主键id
     */
    @GetMapping("/detail/{id}")
    public AdminResult detail(@PathVariable("id") Long id) {
        log.info("info shortUrl, id: {}", id);
        return AdminResult.success(userShortUrlService.detail(id));
    }

    /**
     * 文件检测结果
     *
     * @param fileName 文件url
     * @return 检测结果
     */
    @GetMapping("/fileCheckResult/{fileName}")
    public AdminResult fileCheckResult(@PathVariable("fileName") String fileName) {
        log.info("文件检测结果|fileName:{}", fileName);
        return AdminResult.success(userShortUrlService.fileCheckResult(fileName));
    }


    /**
     * 文件上传名单
     * @param fileName
     * @return
     */
    @PostMapping("/fileUploadAdd/{fileName}")
    public AdminResult fileUploadAdd(@PathVariable("fileName") String fileName){
        log.info("文件上传名单|fileName:{}", fileName);
        return AdminResult.success(userShortUrlService.fileUploadAdd(fileName));
    }

    /**
     * 导出用户短链
     *
     * @param query 查询条件
     * @return 下载url
     */
    @GetMapping("/download")
    public AdminResult downLoadPageDetail(@ModelAttribute UserShortUrlQuery query){
        log.info("导出用户短链|query:{}", JsonLogUtil.toJSONString(query));
        return AdminResult.success(userShortUrlService.downLoad(query));
    }


    /**
     * 查询明文手机号
     *
     * @param id
     * @return
     */
    @GetMapping("/queryMobile")
    public AdminResult queryMobile(Long id) {
        return AdminResult.success(userShortUrlService.queryMobile(id));
    }

}
