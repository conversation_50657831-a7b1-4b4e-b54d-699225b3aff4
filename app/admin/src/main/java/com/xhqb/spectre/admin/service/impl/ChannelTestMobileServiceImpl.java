package com.xhqb.spectre.admin.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.admin.cache.impl.ParamsValueMemoryCache;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.*;
import com.xhqb.spectre.admin.model.vo.ChannelTestMobileVO;
import com.xhqb.spectre.admin.readonly.mapper.SmsOrderReadonlyMapper;
import com.xhqb.spectre.admin.service.ChannelTestMobileService;
import com.xhqb.spectre.admin.service.detection.EmptyNumberDetection;
import com.xhqb.spectre.admin.util.AesUtil;
import com.xhqb.spectre.admin.util.RandomValueGeneratorUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.ParamsCodeDO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.entity.test.TestMobileDO;
import com.xhqb.spectre.common.dal.mapper.ParamsCodeMapper;
import com.xhqb.spectre.common.dal.mapper.TestMobileMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ChannelTestMobileQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChannelTestMobileServiceImpl implements ChannelTestMobileService {

    @Resource
    private TestMobileMapper testMobileMapper;

    @Resource
    private EmptyNumberDetection emptyNumberDetection;

    @Resource
    private SmsOrderReadonlyMapper smsOrderReadonlyMapper;

    @Resource
    private ParamsCodeMapper paramsCodeMapper;
    @Resource
    private ParamsValueMemoryCache paramsValueMemoryCache;

    @Override
    public CommonPager<ChannelTestMobileVO> listByPage(ChannelTestMobileQuery channelTestMobileQuery) {
        return PageResultUtils.result(
                () -> testMobileMapper.countByQuery(channelTestMobileQuery),
                () -> testMobileMapper.listByQuery(channelTestMobileQuery).stream().map(this::buildVO).collect(Collectors.toList())
        );
    }

    @Override
    public Integer add(ChannelTestMobileDTO channelTestMobileDTO) {
        // mobiles
        List<String> mobiles = Arrays.stream(channelTestMobileDTO.getMobiles().split(","))
                .collect(Collectors.toList());
        // 判断唯一性
        // todo：yjq 加解密插件
        List<String> existMobiles = testMobileMapper.selectByMobiles(mobiles).stream()
                .map(TestMobileDO::getMobile).collect(Collectors.toList());
        if (!existMobiles.isEmpty()) {
            // 移除存在号码集合
            mobiles.removeAll(existMobiles);
        }

        if (CollectionUtils.isEmpty(mobiles)) {
            return 0;
        }

        Date detectTime = null;
        if (Objects.equals(channelTestMobileDTO.getType(), 0) && Objects.equals(channelTestMobileDTO.getDetectTag(), 1)) {
            // 空号检测
            mobiles = emptyNumberDetection.batchDetect(mobiles);
            detectTime = DateUtils.addDays(new Date(), 7);
        }

        if (CollectionUtils.isEmpty(mobiles)) {
            return 0;
        }

        // 构建插入DO
        List<String> codeList = paramsCodeMapper.selectByType("basic")
                .stream().map(ParamsCodeDO::getCode)
                .collect(Collectors.toList());
        Map<String, List<String>> codeValueMap = new HashMap<>();
        int size = mobiles.size();
        for (String code : codeList) {
            List<String> values = paramsValueMemoryCache.getValues(code);
            List<String> valueResultList = RandomValueGeneratorUtil.randomValues(values, size);
            codeValueMap.put(code, valueResultList);
        }

        List<String> paramValueList = new ArrayList<>();
        List<TestMobileDO> mobileDOList = new ArrayList<>();
        List<String> keys = new ArrayList<>(codeValueMap.keySet());
        for (int i = 0; i < size; i++) {
            JSONObject json = new JSONObject();
            for (String key : keys) {
                List<String> values = codeValueMap.get(key);
                json.put(key, values.get(i));
            }
            TestMobileDO testMobileDO = buildDO(channelTestMobileDTO, mobiles.get(i));
            testMobileDO.setParam(json.toJSONString());
            testMobileDO.setDetectTime(detectTime);
            mobileDOList.add(testMobileDO);
        }
        mobileDOList.forEach(testMobileDO -> {
            testMobileMapper.insertBySelective(testMobileDO);
        });
        return mobiles.size();
    }

    private TestMobileDO buildDO(ChannelTestMobileDTO channelTestMobileDTO, String mobile) {
        TestMobileDO testMobileDO = new TestMobileDO();
        testMobileDO.setMobile(mobile);
        testMobileDO.setType(channelTestMobileDTO.getType());
        testMobileDO.setRemark(channelTestMobileDTO.getRemark());
        testMobileDO.setIsDelete(0);
        testMobileDO.setStatus(1);
        String userName = SsoUserInfoUtil.getUserName();
        testMobileDO.setCreator(userName);
        testMobileDO.setUpdater(userName);
        Date date = new Date();
        testMobileDO.setCreateTime(date);
        testMobileDO.setUpdateTime(date);
        testMobileDO.setExpiredTime(DateUtils.addDays(date, 7));
        return testMobileDO;
    }


    @Override
    public Integer delete(ChannelTestDeleteDTO channelTestDeleteDTO) {
        if (Objects.isNull(channelTestDeleteDTO)) {
            throw new BizException("请求参数不能为空");
        }
        List<Integer> idList = channelTestDeleteDTO.getDeleteDetailList().stream()
                .filter(channelTestDeleteDetailDTO -> Objects.equals(channelTestDeleteDetailDTO.getStatus(), 0))
                .map(ChannelTestDeleteDetailDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)) {
            throw new BizException("idList不能为空");
        }
        testMobileMapper.updateDeleteByIdList(idList);
        return idList.size();
    }

    @Override
    public Integer sync(ChannelMobileSyncDTO channelMobileSyncDTO) {

        channelMobileSyncDTO.setStartTimestamp(Objects.requireNonNull(com.xhqb.spectre.admin.util.DateUtil.stringToDate(channelMobileSyncDTO.getStartTime())).getTime() / 1000);
        channelMobileSyncDTO.setEndTimestamp(Objects.requireNonNull(com.xhqb.spectre.admin.util.DateUtil.stringToDate(channelMobileSyncDTO.getEndTime())).getTime() / 1000);

        int count = smsOrderReadonlyMapper.countBySyncDTO(channelMobileSyncDTO);
        if (Objects.equals(count, 0)) {
            return 0;
        }
        int randomIndex = (int) (Math.random() * count);
        channelMobileSyncDTO.setPageParameter(new PageParameter(randomIndex, count));
        List<SmsOrderDO> smsOrderDOList = smsOrderReadonlyMapper.selectBySyncDTO(channelMobileSyncDTO);
        if (CollectionUtils.isEmpty(smsOrderDOList)) {
            return 0;
        }
        List<String> mobiles = smsOrderDOList.stream().map(SmsOrderDO::getMobile).collect(Collectors.toList());
        Date detectTime = null;
        if (Objects.equals(channelMobileSyncDTO.getDetectTag(), 1)) {
            // 空号检测
            mobiles = emptyNumberDetection.batchDetect(mobiles);
            detectTime = DateUtils.addDays(new Date(), 7);

        }

        Date finalDetectTime = detectTime;
        mobiles.forEach(mobile -> {
            TestMobileDO testMobileDO = new TestMobileDO();
            testMobileDO.setMobile(mobile);
            testMobileDO.setType(0);
            testMobileDO.setRemark("");
            testMobileDO.setIsDelete(0);
            testMobileDO.setStatus(1);
            testMobileDO.setCreator(SsoUserInfoUtil.getUserName());
            testMobileDO.setUpdater(SsoUserInfoUtil.getUserName());
            testMobileDO.setCreateTime(new Date());
            testMobileDO.setUpdateTime(new Date());
            testMobileDO.setDetectTime(new Date());
            testMobileDO.setDetectTime(finalDetectTime);
            if (Objects.nonNull(channelMobileSyncDTO.getExpiredTime())) {
                testMobileDO.setExpiredTime(DateUtil.parseDateTime(channelMobileSyncDTO.getExpiredTime()));
            }
            testMobileMapper.insertBySelective(testMobileDO);
        });
        return mobiles.size();
    }

    @Override
    public String batchEditStatus(ChannelMobileBatchEditStatusDTO batchEditStatusDTO) {
        // 请求参数检验
        List<String> mobileList = batchEditStatusDTO.getMobileList();
        if (CollectionUtils.isEmpty(mobileList)) {
            return "DTO mobileList is empty";
        }

        // 命中数据库
        List<TestMobileDO> mobileDOList = testMobileMapper.selectByMobiles(mobileList);
        if (CollectionUtils.isEmpty(mobileDOList)) {
            return "DO mobileDOList is empty";
        }

        // 设置更新字段
        mobileDOList.forEach(testMobileDO -> {
            testMobileDO.setStatus(batchEditStatusDTO.getStatus());
            testMobileDO.setUpdater("batchEditStatus");
            testMobileDO.setUpdateTime(new Date());
        });

        // 更新
        mobileDOList.forEach(testMobileMapper::updateByPrimaryKeySelective);

        return "success";
    }

    ChannelTestMobileVO buildVO(TestMobileDO testMobileDO) {
        ChannelTestMobileVO channelTestMobileVO = new ChannelTestMobileVO();
        BeanUtils.copyProperties(testMobileDO, channelTestMobileVO);
        String mobile = testMobileDO.getMobile();
        channelTestMobileVO.setEncryptMobile(AesUtil.encryptFromString(mobile, Mode.CBC, Padding.ZeroPadding));
        channelTestMobileVO.setMobile(DesensitizedUtil.mobilePhone(mobile));
        return channelTestMobileVO;
    }
}
