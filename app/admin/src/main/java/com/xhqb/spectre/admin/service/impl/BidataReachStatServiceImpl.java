package com.xhqb.spectre.admin.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.xhqb.spectre.admin.bidata.entity.*;
import com.xhqb.spectre.admin.bidata.mapper.BidataSendStatMapper;
import com.xhqb.spectre.admin.enums.SmsTypeCodeEnum;
import com.xhqb.spectre.admin.model.dto.ReachStatDTO;
import com.xhqb.spectre.admin.model.vo.*;
import com.xhqb.spectre.admin.service.BidataReachStatService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.MonthWeekUtils;
import com.xhqb.spectre.admin.util.QueryUtils;
import com.xhqb.spectre.common.dal.dto.Pair;
import com.xhqb.spectre.common.dal.entity.ChannelDO;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TopTplChannelDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.entity.oa.TplContent;
import com.xhqb.spectre.common.dal.mapper.*;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.OaReportQuery;
import com.xhqb.spectre.common.dal.query.SendQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BidataReachStatServiceImpl implements BidataReachStatService {

    private static final String CACHE_KEY_CHANNEL_MAP = "channel_map";
    private static final String CACHE_KEY_SIGN_MAP = "sign_map";
    private StatChannelVO statChannelCache;
    private List<Map<String, Object>> statSignCache;
    private StatSmsTypeVO statSmsTypeCache;
    @Autowired
    private BidataSendStatMapper bidataSendStatMapper;
    @Autowired
    private ChannelMapper channelMapper;
    @Autowired
    private TplMapper tplMapper;
    @Autowired
    private TplContentMapper tplContentMapper;
    @Autowired
    private TplChannelMapper tplChannelMapper;
    @Autowired
    private SignMapper signMapper;
    private Cache<String, Map<?, ?>> cache;

    // 加载渠道列表并缓存
    public Map<String, ChannelDO> getChannelMap() {
        return (Map<String, ChannelDO>) cache.get(CACHE_KEY_CHANNEL_MAP, key -> {
            List<ChannelDO> channelList = channelMapper.selectAll();
            return channelList.stream().collect(Collectors.toMap(ChannelDO::getCode, Function.identity(), (existing, replacement) -> existing));
        });
    }


    public Map<Integer, SignDO> getSignMap() {
        Object cached = cache.get(CACHE_KEY_SIGN_MAP, key -> {
            List<SignDO> signList = signMapper.selectAll();
            if (signList == null || signList.isEmpty()) {
                return Collections.emptyMap();
            }
            return signList.stream().collect(Collectors.toMap(SignDO::getId, Function.identity()));
        });

        if (cached != null) {
            return (Map<Integer, SignDO>) cached;
        } else {
            return Collections.emptyMap();
        }
    }

    public void refreshChannelList() {
        List<ChannelDO> channelList = channelMapper.selectAll();
        Map<String, ChannelDO> channelMap = channelList.stream().collect(Collectors.toMap(ChannelDO::getCode, Function.identity(), (existing, replacement) -> existing));
        cache.put(CACHE_KEY_CHANNEL_MAP, channelMap);
    }

    public void refreshSignList() {
        List<SignDO> signList = signMapper.selectAll();
        Map<Integer, SignDO> signDOMap = signList.stream().collect(Collectors.toMap(SignDO::getId, Function.identity()));
        cache.put(CACHE_KEY_SIGN_MAP, signDOMap);
    }

    @PostConstruct
    public void init() {
        cache = Caffeine.newBuilder().expireAfterWrite(120, TimeUnit.MINUTES).build();
        log.info("应用启动初始化触达统计缓存");
        doRefreshCache();
    }

    @Scheduled(fixedRate = 60 * 60 * 1000)
    public void scheduledRefresh() {
        log.info("定时刷新触达统计缓存");
        doRefreshCache();
    }

    public void doRefreshCache() {
        StopWatch stopWatch = new StopWatch("触达统计缓存刷新任务");
        log.info("刷新触达统计缓存");
        try {
            stopWatch.start("刷新渠道列表");
            log.info("刷新渠道列表");
            refreshChannelList();
            stopWatch.stop();

            stopWatch.start("刷新签名列表");
            log.info("刷新签名列表");
            refreshSignList();
            stopWatch.stop();

            stopWatch.start("刷新供应商纵向条件列表");
            log.info("刷新供应商纵向条件列表");
            statChannelCache = getStatChannelListFromDB();
            stopWatch.stop();

            stopWatch.start("刷新供应商横向条件列表");
            log.info("刷新供应商横向条件列表");
            statSignCache = getStatSignListFromDB();
            stopWatch.stop();

            stopWatch.start("刷新供应商横纵列表");
            log.info("刷新供应商横纵列表");
            statSmsTypeCache = getStatSmsTypeListFromDB();
            stopWatch.stop();
        } catch (Exception e) {
            log.warn("刷新渠道列表和签名列表异常", e);
        } finally {
            log.info("触达统计缓存刷新耗时：\n{}", stopWatch.prettyPrint());
        }
    }

    /**
     * 获取最近13个月或13个周调用过的渠道以及渠道包含的签名
     */
    @Override
    public StatChannelVO getStatChannelList() {
        return statChannelCache;
    }


    public StatChannelVO getStatChannelListFromDB() {
        String month = MonthWeekUtils.getStartDateMonthsAgo(12);
        String week = MonthWeekUtils.getStartDateOfWeeksAgo(12);
        Map<String, StatChannelVO.SubChannel> monthMap = new HashMap<>();

        List<StatChannelDO> monthListDO = bidataSendStatMapper.getStatChannelList(month);
        monthListDO.forEach(statChannelDO -> {
            StatChannelVO.SubChannel subChannel = monthMap.get(statChannelDO.getChannelCode());
            if (subChannel == null) {
                String channelName = Optional.ofNullable(statChannelDO.getChannelCode()).map(getChannelMap()::get).map(ChannelDO::getName).orElse("unknown");

                subChannel = StatChannelVO.SubChannel.builder().channelCode(statChannelDO.getChannelCode()).channelCodeName(channelName).signList(new HashSet<>()).build();
                monthMap.put(statChannelDO.getChannelCode(), subChannel);
            }
            subChannel.addSign(statChannelDO.getSignName().replaceAll("\\s+", "").trim());
        });

        Map<String, StatChannelVO.SubChannel> weekMap = new HashMap<>();
        List<StatChannelDO> weekListDO = bidataSendStatMapper.getStatChannelList(week);
        log.info("获取条件渠道列表, month={}, week={}", month, week);
        weekListDO.forEach(statChannelDO -> {
            StatChannelVO.SubChannel subChannel = weekMap.get(statChannelDO.getChannelCode());
            if (subChannel == null) {
                String channelName = Optional.ofNullable(statChannelDO.getChannelCode()).map(getChannelMap()::get).map(ChannelDO::getName).orElse("unknown");
                subChannel = StatChannelVO.SubChannel.builder().channelCode(statChannelDO.getChannelCode()).channelCodeName(channelName).signList(new HashSet<>()).build();
                weekMap.put(statChannelDO.getChannelCode(), subChannel);
            }
            subChannel.addSign(statChannelDO.getSignName().replaceAll("\\s+", "").trim());
        });

        return StatChannelVO.builder().months(new ArrayList<>(monthMap.values())).weeks(new ArrayList<>(weekMap.values())).build();
    }

    @Override
    public List<Map<String, Object>> getStatSignList() {
        return statSignCache;
    }

    public List<Map<String, Object>> getStatSignListFromDB() {
        String startDate = MonthWeekUtils.getStartDateMonthsAgo(12);
        List<StatSignDO> statSignDOList = bidataSendStatMapper.getStatSignList(startDate);
        Map<String, List<String>> signMap = statSignDOList.stream().collect(Collectors.groupingBy(StatSignDO::getStatDate, Collectors.mapping(StatSignDO::getSignName, Collectors.toList())));

        List<Map<String, Object>> datas = MonthWeekUtils.getRecent13MonthsWithWeeks();

        datas.forEach(data -> {
            String month = (String) data.get("month");
            List<String> signList = signMap.get(month);
            data.put("signList", signList);
        });

        return datas;
    }


    @Override
    public StatSmsTypeVO getStatSmsTypeList() {
        return statSmsTypeCache;
    }

    public StatSmsTypeVO getStatSmsTypeListFromDB() {
        String month = MonthWeekUtils.getStartDateMonthsAgo(12);
        String week = MonthWeekUtils.getStartDateOfWeeksAgo(12);
        Map<String, StatSmsTypeVO.SubSmsType> monthMap = new HashMap<>();
        List<StatSmsTypeDO> monthListDO = bidataSendStatMapper.getStatSmsTypeList(month);
        monthListDO.forEach(statSmsTypeDO -> {
            StatSmsTypeVO.SubSmsType subSmsType = monthMap.get(statSmsTypeDO.getSmsTypeCode());
            if (subSmsType == null) {
                SmsTypeCodeEnum smsTypeCodeEnum = SmsTypeCodeEnum.getByCode(statSmsTypeDO.getSmsTypeCode());


                subSmsType = StatSmsTypeVO.SubSmsType.builder().smsTypeCode(statSmsTypeDO.getSmsTypeCode()).smsTypeName(smsTypeCodeEnum.getName()).signList(new HashSet<>()).build();

                monthMap.put(statSmsTypeDO.getSmsTypeCode(), subSmsType);
            }
            subSmsType.addSign(statSmsTypeDO.getSignName().replaceAll("\\s+", "").trim());

        });

        Map<String, StatSmsTypeVO.SubSmsType> weekMap = new HashMap<>();
        List<StatSmsTypeDO> weekListDO = bidataSendStatMapper.getStatSmsTypeList(week);
        log.debug("获取条件短信类型列表, month={}, week={}", month, week);
        weekListDO.forEach(statSmsTypeDO -> {
            StatSmsTypeVO.SubSmsType subSmsType = weekMap.get(statSmsTypeDO.getSmsTypeCode());
            if (subSmsType == null) {
                SmsTypeCodeEnum smsTypeCodeEnum = SmsTypeCodeEnum.getByCode(statSmsTypeDO.getSmsTypeCode());
                subSmsType = StatSmsTypeVO.SubSmsType.builder().smsTypeCode(statSmsTypeDO.getSmsTypeCode()).smsTypeName(smsTypeCodeEnum.getName()).signList(new HashSet<>()).build();

                weekMap.put(statSmsTypeDO.getSmsTypeCode(), subSmsType);
            }
            subSmsType.addSign(statSmsTypeDO.getSignName().replaceAll("\\s+", "").trim());
        });

        return StatSmsTypeVO.builder().months(new ArrayList<>(monthMap.values())).weeks(new ArrayList<>(weekMap.values())).build();

    }

    /**
     * 按月根据渠道统计最近13个月的调用数据
     *
     * @param reachStatDTO
     * @return
     */
    @Override
    public List<MonoReachStatVO> getMonthStatsByChannel(ReachStatDTO reachStatDTO) {
        reachStatDTO.setStartDate(MonthWeekUtils.getStartDateMonthsAgo(12));
        reachStatDTO.setEndDate(MonthWeekUtils.getCurrentDate());
        log.info("获取月渠道列表, reachStatDTO={},", reachStatDTO);
        List<ChannelReachStatDO> channelReachStatDOList = bidataSendStatMapper.getMonthStatsByChannelCode(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getChannelCode(), reachStatDTO.getSignList());

        Map<String, ChannelDO> channelDOMap = getChannelMap();
        return channelReachStatDOList.stream().map(row -> MonoReachStatVO.convert(row, "", reachStatDTO.getSmsTypeCodes())).peek(vo -> {
            ChannelDO channelDO = channelDOMap.get(vo.getChannelCode());
            vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
        }).collect(Collectors.toList());
    }

    /**
     * 按周根据渠道统计最近13个周的调用数据
     *
     * @param reachStatDTO
     * @return
     */
    @Override
    public List<MonoReachStatVO> getWeekStatsByChannel(ReachStatDTO reachStatDTO) {
        reachStatDTO.setStartDate(MonthWeekUtils.getStartDateOfWeeksAgo(12));
        reachStatDTO.setEndDate(MonthWeekUtils.getCurrentDate());
        log.info("获取周渠道列表, reachStatDTO={},", reachStatDTO);
        List<ChannelReachStatDO> channelReachStatDOList = bidataSendStatMapper.getWeekStatsByChannelCode(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getChannelCode(), reachStatDTO.getSignList());
        Map<String, ChannelDO> channelDOMap = getChannelMap();
        return channelReachStatDOList.stream().map(row -> MonoReachStatVO.convert(row, "", reachStatDTO.getSmsTypeCodes())).peek(vo -> {
            ChannelDO channelDO = channelDOMap.get(vo.getChannelCode());
            vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
        }).collect(Collectors.toList());
    }

    @Override
    public List<MonoReachStatVO> getMonthStatsByDate(ReachStatDTO reachStatDTO) {
        MonthWeekUtils.DateRange dateRange = MonthWeekUtils.getMonthStartAndEnd(reachStatDTO.getStatDate());
        reachStatDTO.setStartDate(dateRange.getStartDate());
        reachStatDTO.setEndDate(dateRange.getEndDate());
        log.info("获取月日期列表, reachStatDTO={},", reachStatDTO);
        List<ChannelReachStatDO> channelReachStatDOList = bidataSendStatMapper.getMonthStatsByDate(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getSignList());

        Map<String, ChannelDO> channelDOMap = getChannelMap();
        return channelReachStatDOList.stream().map(row -> MonoReachStatVO.convert(row, "", reachStatDTO.getSmsTypeCodes())).peek(vo -> {
            ChannelDO channelDO = channelDOMap.get(vo.getChannelCode());
            vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
        }).collect(Collectors.toList());
    }

    @Override
    public List<MonoReachStatVO> getWeekStatsByDate(ReachStatDTO reachStatDTO) {
        MonthWeekUtils.DateRange dateRange = MonthWeekUtils.getWeekStartAndEnd(reachStatDTO.getStatDate());
        reachStatDTO.setStartDate(dateRange.getStartDate());
        reachStatDTO.setEndDate(dateRange.getEndDate());
        log.info("获取周日期列表, reachStatDTO={},", reachStatDTO);
        List<ChannelReachStatDO> channelReachStatDOList = bidataSendStatMapper.getWeekStatsByDate(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getSignList());
        Map<String, ChannelDO> channelDOMap = getChannelMap();
        return channelReachStatDOList.stream().map(row -> MonoReachStatVO.convert(row, "", reachStatDTO.getSmsTypeCodes())).peek(vo -> {
            ChannelDO channelDO = channelDOMap.get(vo.getChannelCode());
            vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
        }).collect(Collectors.toList());
    }

    @Override
    public List<PolyReachStatVO> getMonthStatsByType(ReachStatDTO reachStatDTO) {
        reachStatDTO.setStartDate(MonthWeekUtils.getStartDateMonthsAgo(5));
        reachStatDTO.setEndDate(MonthWeekUtils.getCurrentDate());

        log.info("获取月类型列表, reachStatDTO={},", reachStatDTO);
        List<TypeReachStatDO> typeReachStatDOList = bidataSendStatMapper.getMonthStatsByType(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getSmsTypeCode(), reachStatDTO.getSignList());

        Map<String, PolyReachStatVO> polyReachStatVOMap = new HashMap<>();
        Map<String, ChannelDO> channelDOMap = getChannelMap();
        typeReachStatDOList.forEach(row -> {
            PolyReachStatVO polyReachStatVO = polyReachStatVOMap.get(row.getChannelCode());
            if (polyReachStatVO == null) {

                String channelCodeName = Optional.ofNullable(channelDOMap.get(row.getChannelCode())).map(ChannelDO::getName).orElse("");
                polyReachStatVO = PolyReachStatVO.builder().statDate(new ArrayList<>()).channelCode(row.getChannelCode()).channelCodeName(channelCodeName).reachCount(new ArrayList<>()).reachBillCount(new ArrayList<>()).reachRate(new ArrayList<>()).build();
                polyReachStatVO.add(row);
                polyReachStatVOMap.put(row.getChannelCode(), polyReachStatVO);
            } else {
                polyReachStatVO.add(row);
            }
        });
        List<PolyReachStatVO> result = new ArrayList<>(polyReachStatVOMap.values());

        List<String> allMonths = MonthWeekUtils.getMonthsBetween(reachStatDTO.getStartDate().substring(0, 7), reachStatDTO.getEndDate().substring(0, 7));

        return result.stream().map(vo -> {
            Map<String, Integer> reachCountMap = new HashMap<>();
            Map<String, Integer> reachBillCountMap = new HashMap<>();
            Map<String, String> reachRateMap = new HashMap<>();

            for (int i = 0; i < vo.getStatDate().size(); i++) {
                String date = vo.getStatDate().get(i);
                reachCountMap.put(date, vo.getReachCount().get(i));
                reachBillCountMap.put(date, vo.getReachBillCount().get(i));
                reachRateMap.put(date, vo.getReachRate().get(i));
            }


            List<Integer> filledReachCount = new ArrayList<>();
            List<Integer> filledReachBillCount = new ArrayList<>();
            List<String> filledReachRate = new ArrayList<>();

            for (String month : allMonths) {
                filledReachCount.add(reachCountMap.getOrDefault(month, 0));
                filledReachBillCount.add(reachBillCountMap.getOrDefault(month, 0));
                filledReachRate.add(reachRateMap.getOrDefault(month, "0%"));
            }

            return PolyReachStatVO.builder().statDate(allMonths).channelCode(vo.getChannelCode()).channelCodeName(vo.getChannelCodeName()).reachCount(filledReachCount).reachBillCount(filledReachBillCount).reachRate(filledReachRate).build();
        }).collect(Collectors.toList());
    }

    @Override
    public List<PolyReachStatVO> getWeekStatsByType(ReachStatDTO reachStatDTO) {
        reachStatDTO.setStartDate(MonthWeekUtils.getStartDateOfWeeksAgo(5));
        reachStatDTO.setEndDate(MonthWeekUtils.getCurrentDate());
        log.info("获取周类型列表, reachStatDTO={},", reachStatDTO);
        List<TypeReachStatDO> typeReachStatDOList = bidataSendStatMapper.getWeekStatsByType(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getSmsTypeCode(), reachStatDTO.getSignList());

        Map<String, PolyReachStatVO> polyReachStatVOMap = new HashMap<>();
        Map<String, ChannelDO> channelDOMap = getChannelMap();
        typeReachStatDOList.forEach(row -> {
            PolyReachStatVO polyReachStatVO = polyReachStatVOMap.get(row.getChannelCode());
            if (polyReachStatVO == null) {
                String channelCodeName = Optional.ofNullable(channelDOMap.get(row.getChannelCode())).map(ChannelDO::getName).orElse("");
                polyReachStatVO = PolyReachStatVO.builder().statDate(new ArrayList<>()).channelCode(row.getChannelCode()).channelCodeName(channelCodeName).reachCount(new ArrayList<>()).reachBillCount(new ArrayList<>()).reachRate(new ArrayList<>()).build();
                polyReachStatVO.add(row);
                polyReachStatVOMap.put(row.getChannelCode(), polyReachStatVO);
            } else {
                polyReachStatVO.add(row);
            }
        });
        List<PolyReachStatVO> result = new ArrayList<>(polyReachStatVOMap.values());

        List<String> allWeeks = MonthWeekUtils.getWeeksBetween(reachStatDTO.getStartDate(), reachStatDTO.getEndDate());

        return result.stream().map(vo -> {
            Map<String, Integer> reachCountMap = new HashMap<>();
            Map<String, Integer> reachBillCountMap = new HashMap<>();
            Map<String, String> reachRateMap = new HashMap<>();

            for (int i = 0; i < vo.getStatDate().size(); i++) {
                String week = vo.getStatDate().get(i);
                reachCountMap.put(week, vo.getReachCount().get(i));
                reachBillCountMap.put(week, vo.getReachBillCount().get(i));
                reachRateMap.put(week, vo.getReachRate().get(i));
            }

            List<Integer> filledReachCount = new ArrayList<>();
            List<Integer> filledReachBillCount = new ArrayList<>();
            List<String> filledReachRate = new ArrayList<>();

            for (String week : allWeeks) {
                filledReachCount.add(reachCountMap.getOrDefault(week, 0));
                filledReachBillCount.add(reachBillCountMap.getOrDefault(week, 0));
                filledReachRate.add(reachRateMap.getOrDefault(week, "0%"));
            }

            return PolyReachStatVO.builder().statDate(allWeeks).channelCode(vo.getChannelCode()).channelCodeName(vo.getChannelCodeName()).reachCount(filledReachCount).reachBillCount(filledReachBillCount).reachRate(filledReachRate).build();
        }).collect(Collectors.toList());
    }


    @Override
    public List<SubReachStatVO> getMonthSubReachStats(ReachStatDTO reachStatDTO) {
        MonthWeekUtils.DateRange dateRange = MonthWeekUtils.getMonthStartAndEnd(reachStatDTO.getStatDate());
        reachStatDTO.setStartDate(dateRange.getStartDate());
        reachStatDTO.setEndDate(dateRange.getEndDate());
        log.info("查询子报表, reachStatDTO={}", reachStatDTO);
        List<SubReachStatDO> subReachStatDOList = bidataSendStatMapper.getMonthSubReachStats(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getChannelCode(), reachStatDTO.getChannelCodes(), reachStatDTO.getSmsTypeCode(), reachStatDTO.getSmsTypeCodes(), reachStatDTO.getSignList(), reachStatDTO.getTplCode(), reachStatDTO.getPageParameter());

        if (subReachStatDOList.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> tplCodeList = subReachStatDOList.stream().map(SubReachStatDO::getTplCode).distinct().collect(Collectors.toList());
        List<TplDO> tplDOList = tplMapper.selectByCodeList(tplCodeList);

        List<TplContent> tplContentList = tplContentMapper.selectByTplCodeList(tplCodeList);
        Map<String, TplContent> tplContentMap = tplContentList.stream().collect(Collectors.toMap(TplContent::getTplCode, tpl -> tpl));

        return subReachStatDOList.stream().map(subReachStatDO -> {
            String channelCodeName = Optional.ofNullable(subReachStatDO.getChannelCode()).map(getChannelMap()::get).map(ChannelDO::getName).orElse("-");
            String smsTypeCodeName = Optional.ofNullable(subReachStatDO.getSmsTypeCode()).map(SmsTypeCodeEnum::getByCode).map(SmsTypeCodeEnum::getName).orElse("");
            String tplContent = tplDOList.stream().filter(tplDO -> tplDO.getCode().equals(subReachStatDO.getTplCode())).map(TplDO::getContent).findFirst().orElse("unknown");
            String creator = Optional.ofNullable(tplContentMap.get(subReachStatDO.getTplCode())).map(TplContent::getCreator).orElse("");
            return SubReachStatVO.convert(subReachStatDO, channelCodeName, smsTypeCodeName, tplContent, creator);
        }).sorted(Comparator.comparing(SubReachStatVO::getReachBillCount, Comparator.nullsLast(Comparator.reverseOrder()))).collect(Collectors.toList());

    }

    @Override
    public List<SubReachStatVO> getWeekSubReachStats(ReachStatDTO reachStatDTO) {

        MonthWeekUtils.DateRange dateRange = MonthWeekUtils.getWeekStartAndEnd(reachStatDTO.getStatDate());
        reachStatDTO.setStartDate(dateRange.getStartDate());
        reachStatDTO.setEndDate(dateRange.getEndDate());
        log.info("查询子报表, reachStatDTO={}", reachStatDTO);
        List<SubReachStatDO> subReachStatDOList = bidataSendStatMapper.getWeekSubReachStats(dateRange.getStartDate(), dateRange.getEndDate(), reachStatDTO.getChannelCode(), reachStatDTO.getChannelCodes(), reachStatDTO.getSmsTypeCode(), reachStatDTO.getSmsTypeCodes(), reachStatDTO.getSignList(), reachStatDTO.getTplCode(), reachStatDTO.getPageParameter());

        if (subReachStatDOList.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> tplCodeList = subReachStatDOList.stream().map(SubReachStatDO::getTplCode).distinct().collect(Collectors.toList());
        List<TplDO> tplDOList = tplMapper.selectByCodeList(tplCodeList);

        List<TplContent> tplContentList = tplContentMapper.selectByTplCodeList(tplCodeList);
        Map<String, TplContent> tplContentMap = tplContentList.stream().collect(Collectors.toMap(TplContent::getTplCode, tpl -> tpl));


        return subReachStatDOList.stream().map(subReachStatDO -> {
            String channelCodeName = Optional.ofNullable(subReachStatDO.getChannelCode()).map(getChannelMap()::get).map(ChannelDO::getName).orElse("");
            String smsTypeCodeName = Optional.ofNullable(subReachStatDO.getSmsTypeCode()).map(SmsTypeCodeEnum::fromCode).map(SmsTypeCodeEnum::getName).orElse("unknown");
            String tplContent = tplDOList.stream().filter(tplDO -> tplDO.getCode().equals(subReachStatDO.getTplCode())).map(TplDO::getContent).findFirst().orElse("unknown");
            String creator = Optional.ofNullable(tplContentMap.get(subReachStatDO.getTplCode())).map(TplContent::getCreator).orElse("");
            return SubReachStatVO.convert(subReachStatDO, channelCodeName, smsTypeCodeName, tplContent, creator);
        }).filter(subReachStatVO -> Objects.equals(subReachStatVO.getStatDate(), reachStatDTO.getStatDate())).sorted(Comparator.comparing(SubReachStatVO::getReachBillCount, Comparator.nullsLast(Comparator.reverseOrder()))).collect(Collectors.toList());
    }


    public TplReachRateVO getAbnormalTplReachRates(PageParameter pageParameter) {
        List<String> weeks = MonthWeekUtils.getRecent4Weeks().stream().map(week -> {
            MonthWeekUtils.DateRange range = MonthWeekUtils.getWeekStartAndEnd(week, MonthWeekUtils.MONTH_DAY_FORMAT);
            return week + " (" + range.getStartDate() + "-" + range.getEndDate() + ")";
        }).collect(Collectors.toList());
        TplReachRateVO tplReachRateVO = new TplReachRateVO(weeks);
        List<TplReachRateDO> tplReachRateDOList = bidataSendStatMapper.getAbnormalTplReachRates(pageParameter);
        List<TplReachRateVO.RowData> rowDataList = tplReachRateDOList.stream().map(TplReachRateVO::convert).collect(Collectors.toList());
        tplReachRateVO.setRows(rowDataList);
        tplReachRateVO.setTotalCount(Optional.of(rowDataList.size()).orElse(0));
        return tplReachRateVO;
    }

    @Override
    public TplStatVO getAbnormalTplChannelReachRate(String statDate, double rateGap, int minSendCount) {
        MonthWeekUtils.DateRange dateRange = MonthWeekUtils.getWeekStartAndEnd(statDate);
        List<TplChannelRateDO> rawList = bidataSendStatMapper.getAbnormalTplChannelReachRate(dateRange.getStartDate(), dateRange.getEndDate(), rateGap, minSendCount);

        Set<String> channelCodeSet = rawList.stream().map(TplChannelRateDO::getChannelCode).collect(Collectors.toSet());
        Map<String, String> channelMap = getChannelMap().values().stream().filter(channelDO -> channelCodeSet.contains(channelDO.getCode())).collect(Collectors.toMap(ChannelDO::getCode, ChannelDO::getName));
        // 按模板分组
        Map<String, List<TplChannelRateDO>> grouped = rawList.stream().collect(Collectors.groupingBy(TplChannelRateDO::getTplCode));

        // 转为 VO 结构
        List<TplChannelRateVO> tplChannelRates = new ArrayList<>();
        for (Map.Entry<String, List<TplChannelRateDO>> entry : grouped.entrySet()) {
            TplChannelRateVO vo = new TplChannelRateVO();
            vo.setTplCode(entry.getKey());
            vo.setReachTotal(entry.getValue().stream().mapToInt(TplChannelRateDO::getReachCount).sum());

            List<TplChannelRateVO.ChannelStat> channels = entry.getValue().stream().map(dto -> {
                TplChannelRateVO.ChannelStat stat = new TplChannelRateVO.ChannelStat();
                stat.setChannelCode(dto.getChannelCode());
                stat.setChannelCodeName(channelMap.get(dto.getChannelCode()));
                stat.setSendCount(dto.getSendCount());
                stat.setReachCount(dto.getReachCount());
                stat.setReachRate(CommonUtil.double2String(dto.getReachRate()));
                return stat;
            }).collect(Collectors.toList());


            List<Double> validReachRates = channels.stream().filter(Objects::nonNull).map(TplChannelRateVO.ChannelStat::getReachRate).map(s -> s.replace("%", "")).map(Double::parseDouble).map(d -> d / 100.0)               // 转换为小数形式（0.87）
                    .filter(d -> d > 0).sorted().collect(Collectors.toList());

            if (validReachRates.size() > 1) {
                Double min = validReachRates.get(0);
                Double max = validReachRates.get(validReachRates.size() - 1);
                if ((max - min) >= rateGap) {
                    vo.setChannels(channels);
                    tplChannelRates.add(vo);
                }
            }

        }

        // 模板异常触达量大到小排序
        List<TplChannelRateVO> sortedTplChannelRates = tplChannelRates.stream().sorted(Comparator.comparing(TplChannelRateVO::getReachTotal).reversed()).collect(Collectors.toList());

        return TplStatVO.builder().headers(channelMap).tplChannelRates(sortedTplChannelRates).totalCount(Optional.of(tplChannelRates.size()).orElse(0)).build();
    }

    @Override
    public TplStatVO queryTplChannelStats(ReachStatDTO reachStatDTO) {
        MonthWeekUtils.DateRange dateRange = MonthWeekUtils.getWeekStartAndEnd(reachStatDTO.getStatDate());
        reachStatDTO.setStartDate(dateRange.getStartDate());
        reachStatDTO.setEndDate(dateRange.getEndDate());
        List<TopReachStatDO> flatList = bidataSendStatMapper.queryTplChannelStats(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getSmsTypeCode());

        Set<String> channelCodeSet = flatList.stream().map(TopReachStatDO::getChannelCode).collect(Collectors.toSet());
        Map<String, String> channelMap = getChannelMap().values().stream().filter(channelDO -> channelCodeSet.contains(channelDO.getCode())).collect(Collectors.toMap(ChannelDO::getCode, ChannelDO::getName));

        Map<String, List<TopReachStatDO>> grouped = flatList.stream().collect(Collectors.groupingBy(TopReachStatDO::getTplCode));

        List<TplChannelRateVO> tplChannelRates = new ArrayList<>();
        for (Map.Entry<String, List<TopReachStatDO>> entry : grouped.entrySet()) {
            TplChannelRateVO vo = new TplChannelRateVO();
            vo.setTplCode(entry.getKey());
            vo.setReachTotal(entry.getValue().stream().mapToInt(TopReachStatDO::getReachCount).sum());

            List<TplChannelRateVO.ChannelStat> channels = entry.getValue().stream().map(dto -> {
                TplChannelRateVO.ChannelStat stat = new TplChannelRateVO.ChannelStat();
                stat.setChannelCode(dto.getChannelCode());
                stat.setChannelCodeName(channelMap.get(dto.getChannelCode()));
                stat.setSendCount(dto.getSendCount());
                stat.setReachCount(dto.getReachCount());
                stat.setReachRate(CommonUtil.double2String(dto.getReachRate()));
                return stat;
            }).collect(Collectors.toList());

            vo.setChannels(channels);
            tplChannelRates.add(vo);
        }
        // top按触达量大到小排序
        List<TplChannelRateVO> sortedTplChannelRates = tplChannelRates.stream().sorted(Comparator.comparing(TplChannelRateVO::getReachTotal).reversed()).collect(Collectors.toList());

        return TplStatVO.builder().headers(channelMap).tplChannelRates(sortedTplChannelRates).totalCount(Optional.of(tplChannelRates.size()).orElse(0)).build();
    }


    public List<TplHeatStatVO> getOverallByTplHeat(String startDate, String endDate, String smsTypeCode, String tplCode, String creator) {
        List<String> tplCodes = new ArrayList<>();
        if (StringUtils.isNotEmpty(creator) && StringUtils.isEmpty(tplCode)) {
            OaReportQuery oaReportQuery = OaReportQuery.builder().creator(creator).build();
            tplCodes = tplContentMapper.selectByQuery(oaReportQuery).stream().map(TplContent::getTplCode).collect(Collectors.toList());
        }

        SendQuery query = new SendQuery();
        query.setStartTime(startDate);
        query.setEndTime(endDate);
        query.setSmsTypeCode(smsTypeCode);
        query.setTplCode(tplCode);
        query.setTplCodes(tplCodes);
        query.setTableName(QueryUtils.channelTableName());
        return bidataSendStatMapper.queryOverallByTplHeat(query).stream().map(TplHeatStatVO::convert).collect(Collectors.toList());
    }

    public CommonPager<TplHeatStatVO> getStatByTplHeat(String startDate, String endDate, String smsTypeCode, String tplCode, String creator, PageParameter pageParameter) {

        List<String> tplCodes = new ArrayList<>();
        if (StringUtils.isNotEmpty(creator) && StringUtils.isEmpty(tplCode)) {
            OaReportQuery oaReportQuery = OaReportQuery.builder().creator(creator).build();
            tplCodes = tplContentMapper.selectByQuery(oaReportQuery).stream().map(TplContent::getTplCode).collect(Collectors.toList());
        }

        SendQuery query = new SendQuery();
        query.setStartTime(startDate);
        query.setEndTime(endDate);
        query.setSmsTypeCode(smsTypeCode);
        query.setTplCode(tplCode);
        query.setTplCodes(tplCodes);
        query.setPageParameter(pageParameter);
        QueryUtils.setQuery(query);
        List<TplHeatStatDO> tplHeatStatDOList = bidataSendStatMapper.queryStatByTplHeat(query);
        if (tplHeatStatDOList == null || tplHeatStatDOList.isEmpty()) {
            return new CommonPager<>(0, Collections.emptyList());
        }

        Integer count = bidataSendStatMapper.queryStatByTplHeatCount(query);


        List<String> tplCodeList = tplHeatStatDOList.stream().map(TplHeatStatDO::getTplCode).collect(Collectors.toList());
        List<TplContent> tplContentList = tplContentMapper.selectByTplCodeList(tplCodeList);
        Map<String, TplContent> tplContentMap = tplContentList.stream().collect(Collectors.toMap(TplContent::getTplCode, tpl -> tpl));


        List<TplDO> tplDOList = tplMapper.selectByCodeList(tplCodeList);

        List<TplHeatStatVO> result = tplHeatStatDOList.stream().map(tplHeatStatDO -> {
            TplHeatStatVO tplHeatStatVO = TplHeatStatVO.convert(tplHeatStatDO);
            TplContent tplContent = tplContentMap.get(tplHeatStatDO.getTplCode());
            if (tplContent != null) {
                tplHeatStatVO.setCreator(tplContent.getCreator());
            }
            String content = tplDOList.stream().filter(tplDO -> tplDO.getCode().equals(tplHeatStatDO.getTplCode())).map(TplDO::getContent).findFirst().orElse("unknown");
            Integer signId = tplDOList.stream().filter(tplDO -> tplDO.getCode().equals(tplHeatStatDO.getTplCode())).map(TplDO::getSignId).findFirst().orElse(0);
            if (signId != 0) {
                SignDO signDO = getSignMap().get(signId);
                if (signDO != null) {
                    tplHeatStatVO.setSignName(signDO.getName());
                }
            }
            tplHeatStatVO.setContent(content);

            return tplHeatStatVO;
        }).collect(Collectors.toList());
        return PageResultUtils.result(() -> count, () -> result);
    }

    public List<TplHeatStatVO> getStatByTplHeat(String startDate, String endDate, String smsTypeCode, String tplCode, String creator) {
        List<String> tplCodes = new ArrayList<>();
        if (StringUtils.isNotEmpty(creator) && StringUtils.isEmpty(tplCode)) {
            OaReportQuery oaReportQuery = OaReportQuery.builder().creator(creator).build();
            tplCodes = tplContentMapper.selectByQuery(oaReportQuery).stream().map(TplContent::getTplCode).collect(Collectors.toList());
        }

        SendQuery query = new SendQuery();
        query.setStartTime(startDate);
        query.setEndTime(endDate);
        query.setSmsTypeCode(smsTypeCode);
        query.setTplCode(tplCode);
        query.setTplCodes(tplCodes);
        QueryUtils.setQuery(query);
        List<TplHeatStatDO> tplHeatStatDOList = bidataSendStatMapper.queryStatByTplHeat(query);

        List<String> tplCodeList = tplHeatStatDOList.stream().map(TplHeatStatDO::getTplCode).collect(Collectors.toList());
        List<TplContent> tplContentList = tplContentMapper.selectByTplCodeList(tplCodeList);
        Map<String, TplContent> tplContentMap = tplContentList.stream().collect(Collectors.toMap(TplContent::getTplCode, tpl -> tpl));


        List<TplDO> tplDOList = tplMapper.selectByCodeList(tplCodeList);

        return tplHeatStatDOList.stream().map(tplHeatStatDO -> {
            TplHeatStatVO tplHeatStatVO = TplHeatStatVO.convert(tplHeatStatDO);
            TplContent tplContent = tplContentMap.get(tplHeatStatDO.getTplCode());
            if (tplContent != null) {
                tplHeatStatVO.setCreator(tplContent.getCreator());
            }
            String content = tplDOList.stream().filter(tplDO -> tplDO.getCode().equals(tplHeatStatDO.getTplCode())).map(TplDO::getContent).findFirst().orElse("unknown");
            Integer signId = tplDOList.stream().filter(tplDO -> tplDO.getCode().equals(tplHeatStatDO.getTplCode())).map(TplDO::getSignId).findFirst().orElse(0);
            if (signId != 0) {
                SignDO signDO = getSignMap().get(signId);
                if (signDO != null) {
                    tplHeatStatVO.setSignName(signDO.getName());
                }
            }
            tplHeatStatVO.setContent(content);

            return tplHeatStatVO;
        }).collect(Collectors.toList());
    }


    /**
     * 渠道横向统计费用(月维度)
     *
     * @param reachStatDTO
     * @return
     */
    @Override
    public List<MonoPriceStatVO> getMonthPriceStatsByChannel(ReachStatDTO reachStatDTO) {
        reachStatDTO.setStartDate(MonthWeekUtils.getStartDateMonthsAgo(12));
        reachStatDTO.setEndDate(MonthWeekUtils.getCurrentDate());
        log.info("获取月渠道费用列表, reachStatDTO={},", reachStatDTO);
        List<ChannelPriceStatDO> channelPriceStatDOList = bidataSendStatMapper.getMonthPriceStatsByChannelCode(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getChannelCode(), reachStatDTO.getSignList());

        Map<String, ChannelDO> channelDOMap = getChannelMap();
        return channelPriceStatDOList.stream().map(row -> MonoPriceStatVO.convert(row, "", reachStatDTO.getSmsTypeCodes())).peek(vo -> {
            ChannelDO channelDO = channelDOMap.get(vo.getChannelCode());
            vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
        }).collect(Collectors.toList());
    }

    /**
     * 渠道横向统计费用(周维度)
     *
     * @param reachStatDTO
     * @return
     */
    @Override
    public List<MonoPriceStatVO> getWeekPriceStatsByChannel(ReachStatDTO reachStatDTO) {
        reachStatDTO.setStartDate(MonthWeekUtils.getStartDateOfWeeksAgo(12));
        reachStatDTO.setEndDate(MonthWeekUtils.getCurrentDate());
        log.info("获取周渠道费用列表, reachStatDTO={},", reachStatDTO);
        List<ChannelPriceStatDO> channelReachStatDOList = bidataSendStatMapper.getWeekPriceStatsByChannelCode(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getChannelCode(), reachStatDTO.getSignList());
        Map<String, ChannelDO> channelDOMap = getChannelMap();
        return channelReachStatDOList.stream().map(row -> MonoPriceStatVO.convert(row, "", reachStatDTO.getSmsTypeCodes())).peek(vo -> {
            ChannelDO channelDO = channelDOMap.get(vo.getChannelCode());
            vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
        }).collect(Collectors.toList());
    }

    /**
     * 费用横向统计(月维度)
     *
     * @param reachStatDTO
     * @return
     */
    @Override
    public List<MonoPriceStatVO> getMonthPriceStats(ReachStatDTO reachStatDTO) {
        reachStatDTO.setStartDate(MonthWeekUtils.getStartDateMonthsAgo(12));
        reachStatDTO.setEndDate(MonthWeekUtils.getCurrentDate());
        log.info("获取月渠道费用列表, reachStatDTO={},", reachStatDTO);
        List<ChannelPriceStatDO> channelPriceStatDOList = bidataSendStatMapper.getMonthPriceStats(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getChannelCodes(), reachStatDTO.getSignList());

        Map<String, ChannelDO> channelDOMap = getChannelMap();
        return channelPriceStatDOList.stream().map(row -> MonoPriceStatVO.convert(row, "", reachStatDTO.getSmsTypeCodes())).peek(vo -> {
            ChannelDO channelDO = channelDOMap.get(vo.getChannelCode());
            vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
        }).collect(Collectors.toList());
    }

    /**
     * 横向统计费用(周维度)
     *
     * @param reachStatDTO
     * @return
     */
    @Override
    public List<MonoPriceStatVO> getWeekPriceStats(ReachStatDTO reachStatDTO) {
        reachStatDTO.setStartDate(MonthWeekUtils.getStartDateOfWeeksAgo(12));
        reachStatDTO.setEndDate(MonthWeekUtils.getCurrentDate());
        log.info("获取周渠道费用列表, reachStatDTO={},", reachStatDTO);
        List<ChannelPriceStatDO> channelReachStatDOList = bidataSendStatMapper.getWeekPriceStats(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getChannelCodes(), reachStatDTO.getSignList());
        Map<String, ChannelDO> channelDOMap = getChannelMap();
        return channelReachStatDOList.stream().map(row -> MonoPriceStatVO.convert(row, "", reachStatDTO.getSmsTypeCodes())).peek(vo -> {
            ChannelDO channelDO = channelDOMap.get(vo.getChannelCode());
            vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
        }).collect(Collectors.toList());
    }

    /**
     * 渠道每月对账列表
     *
     * @param reachStatDTO
     * @return
     */
    @Override
    public List<ChannelMonthlyStatementVO> getMonthlyStatementStats(ReachStatDTO reachStatDTO) {
        MonthWeekUtils.DateRange dateRange = MonthWeekUtils.getMonthStartAndEnd(reachStatDTO.getStatDate());
        reachStatDTO.setStartDate(dateRange.getStartDate());
        reachStatDTO.setEndDate(dateRange.getEndDate());
        List<ChannelMonthlyStatementDO> channelMonthlyStatementDOList = bidataSendStatMapper.getMonthlyStatementStats(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getChannelCode(), reachStatDTO.getSignList());

        return channelMonthlyStatementDOList.stream().map(ChannelMonthlyStatementVO::convert).filter(vo -> vo.getTotalBillCount() != 0) // 添加的过滤条件
                .peek(vo -> {
                    ChannelDO channelDO = getChannelMap().get(vo.getChannelCode());
                    vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
                    if (vo.getSmsTypeCode() != null) {
                        SmsTypeCodeEnum smsTypeCodeEnum = SmsTypeCodeEnum.fromCode(vo.getSmsTypeCode());
                        vo.setSmsTypeCodeName(smsTypeCodeEnum != null ? smsTypeCodeEnum.getName() : "");
                    }
                }).collect(Collectors.toList());
    }

    @Override
    public List<ChannelMonthlyStatementVO> getMonthlyStatementRecords(ReachStatDTO reachStatDTO) {
        MonthWeekUtils.DateRange dateRange = MonthWeekUtils.getMonthStartAndEnd(reachStatDTO.getStatDate());
        reachStatDTO.setStartDate(dateRange.getStartDate());
        reachStatDTO.setEndDate(dateRange.getEndDate());
        List<ChannelMonthlyStatementDO> channelMonthlyStatementDOList = bidataSendStatMapper.getMonthlyStatementRecords(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getChannelCode(), reachStatDTO.getSignList());

        return channelMonthlyStatementDOList.stream().map(ChannelMonthlyStatementVO::convert).filter(vo -> vo.getTotalBillCount() != 0) // 过滤掉 totalBillCount = 0 的对象
                .peek(vo -> {
                    ChannelDO channelDO = getChannelMap().get(vo.getChannelCode());
                    vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
                    if (vo.getSmsTypeCode() != null) {
                        SmsTypeCodeEnum smsTypeCodeEnum = SmsTypeCodeEnum.fromCode(vo.getSmsTypeCode());
                        vo.setSmsTypeCodeName(smsTypeCodeEnum != null ? smsTypeCodeEnum.getName() : "");
                    }
                }).collect(Collectors.toList());
    }

    @Override
    public TopTplStatVO queryTopTplChannelReachRate(ReachStatDTO reachStatDTO) {
        List<TopReachStatDO> flatList = bidataSendStatMapper.queryTopTplChannelStats(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getSmsTypeCodes());

        if (flatList == null || flatList.isEmpty()) {
            return TopTplStatVO.builder().headers(null).tplChannelRates(null).totalCount(0).build();
        }


        Set<String> channelCodeSet = flatList.stream().map(TopReachStatDO::getChannelCode).collect(Collectors.toSet());
        Map<String, String> channelMap = getChannelMap().values().stream().filter(channelDO -> channelCodeSet.contains(channelDO.getCode())).collect(Collectors.toMap(ChannelDO::getCode, cn -> StringUtils.strip(cn.getName())));

        //pair<tplCode, signName>做key
        Map<Pair<String, String>, List<TopReachStatDO>> grouped = flatList.stream().collect(Collectors.groupingBy(item -> new Pair<>(item.getTplCode(), item.getSignName())));

        List<TopTplChannelRateVO> tplChannelRates = new ArrayList<>();
        for (Map.Entry<Pair<String, String>, List<TopReachStatDO>> entry : grouped.entrySet()) {
            TopTplChannelRateVO vo = new TopTplChannelRateVO();
            vo.setTplCode(entry.getKey().getKey());
            vo.setSignName(StringUtils.strip(entry.getKey().getValue()));
            vo.setReachTotal(entry.getValue().stream().mapToInt(TopReachStatDO::getReachCount).sum());


            List<TopReachStatDO> statList = entry.getValue();
            if (!statList.isEmpty()) {
                TopReachStatDO firstItem = statList.get(0);
                vo.setSmsTypeCode(firstItem.getSmsTypeCode());

                if (vo.getSmsTypeCode() != null) {
                    SmsTypeCodeEnum smsTypeCodeEnum = SmsTypeCodeEnum.fromCode(vo.getSmsTypeCode());
                    vo.setSmsTypeCodeName(smsTypeCodeEnum != null ? smsTypeCodeEnum.getName() : "");
                }
            }

            List<TopTplChannelRateVO.ChannelStat> channels = entry.getValue().stream().map(dto -> {
                TopTplChannelRateVO.ChannelStat stat = new TopTplChannelRateVO.ChannelStat();
                stat.setChannelCode(dto.getChannelCode());
                stat.setChannelCodeName(channelMap.get(dto.getChannelCode()));
                stat.setSendCount(dto.getSendCount());
                stat.setReachCount(dto.getReachCount());
                stat.setReachRate(CommonUtil.double2String(dto.getReachRate()));
                return stat;
            }).collect(Collectors.toList());

            vo.setChannels(channels);
            tplChannelRates.add(vo);
        }
        List<String> tplCodes = flatList.stream().map(TopReachStatDO::getTplCode).distinct().collect(Collectors.toList());


        List<TplDO> tplDOList = tplMapper.selectByCodeList(tplCodes);

        Map<Pair<String, String>, Integer> codeToIdMap = tplDOList.stream().collect(Collectors.toMap(tplDO -> {

            String signName = Optional.ofNullable(tplDO).map(TplDO::getSignId).flatMap(id -> Optional.ofNullable(getSignMap()).map(map -> map.get(id))).map(SignDO::getName).orElse("");
            return new Pair<>(tplDO.getCode(), signName);
        }, TplDO::getId, (existing, replacement) -> existing));
        List<Integer> tplIdList = tplDOList.stream().map(TplDO::getId).collect(Collectors.toList());


        List<TopTplChannelRateVO> sortedTplChannelRates = tplChannelRates.stream().sorted(Comparator.comparing(TopTplChannelRateVO::getReachTotal).reversed()).peek(vo -> {
            String tplCode = vo.getTplCode();
            String signName = vo.getSignName();
            Pair<String, String> key = new Pair<>(tplCode, signName);
            if (codeToIdMap.containsKey(key)) {
                vo.setTplId(codeToIdMap.get(key));
            }
        }).collect(Collectors.toList());

        // 模板+渠道名作为id 映射权重
        List<TopTplChannelDO> topTplChannelDOList = tplChannelMapper.selectByTplIdList(tplIdList);
        Map<Pair<Integer, String>, TopTplChannelDO> tplChannelMap = new HashMap<>();
        for (TopTplChannelDO channel : topTplChannelDOList) {
            Pair<Integer, String> key = new Pair<>(channel.getTplId(), channel.getChannelCode());
            tplChannelMap.put(key, channel);
        }

        for (TopTplChannelRateVO vo : sortedTplChannelRates) {
            Integer currentTplId = vo.getTplId() == null ? 0 : vo.getTplId();

            // 获取该模板下的所有 ChannelStat
            List<TopTplChannelRateVO.ChannelStat> channels = vo.getChannels();
            if (channels == null || channels.isEmpty()) continue;


            int totalWeight = channels.stream().mapToInt(ch -> {
                TopTplChannelDO match = tplChannelMap.get(new Pair<>(currentTplId, ch.getChannelCode()));
                return match == null ? 0 : match.getWeight() == null ? 0 : match.getWeight();
            }).sum();

            for (TopTplChannelRateVO.ChannelStat stat : channels) {
                String channelCode = stat.getChannelCode();
                TopTplChannelDO match = tplChannelMap.get(new Pair<>(currentTplId, channelCode));

                if (match != null) {
                    Integer weight = match.getWeight() == null ? 0 : match.getWeight();
                    stat.setWeight(weight);
                    stat.setTplChannelId(String.valueOf(match.getId()));
                    stat.setChannelAccountId(String.valueOf(match.getChannelAccountId()));

                    if (totalWeight > 0) {
                        double rate = ((double) weight / totalWeight) * 100;
                        stat.setWeightRate(String.format("%.0f%%", rate));
                    } else {
                        stat.setWeightRate("0%");
                    }
                }
            }
        }

        return TopTplStatVO.builder().headers(channelMap).tplChannelRates(sortedTplChannelRates).totalCount(Optional.of(tplChannelRates.size()).orElse(0)).build();
    }


    /**
     * 计费量统计(月维度)
     *
     * @param reachStatDTO
     * @return
     */
    @Override
    public List<MonoBillStatVO> getMonthBillStats(ReachStatDTO reachStatDTO) {
        reachStatDTO.setStartDate(MonthWeekUtils.getStartDateMonthsAgo(12));
        reachStatDTO.setEndDate(MonthWeekUtils.getCurrentDate());
        log.info("获取月渠道费用列表, reachStatDTO={},", reachStatDTO);
        List<ChannelPriceStatDO> channelPriceStatDOList = bidataSendStatMapper.getMonthPriceStats(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getChannelCodes(), reachStatDTO.getSignList());

        Map<String, ChannelDO> channelDOMap = getChannelMap();
        return channelPriceStatDOList.stream().map(row -> MonoBillStatVO.convert(row, "", reachStatDTO.getSmsTypeCodes())).peek(vo -> {
            ChannelDO channelDO = channelDOMap.get(vo.getChannelCode());
            vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
        }).collect(Collectors.toList());
    }

    /**
     * 计费量费用(周维度)
     *
     * @param reachStatDTO
     * @return
     */
    @Override
    public List<MonoBillStatVO> getWeekBillStats(ReachStatDTO reachStatDTO) {
        reachStatDTO.setStartDate(MonthWeekUtils.getStartDateOfWeeksAgo(12));
        reachStatDTO.setEndDate(MonthWeekUtils.getCurrentDate());
        log.info("获取周渠道费用列表, reachStatDTO={},", reachStatDTO);
        List<ChannelPriceStatDO> channelReachStatDOList = bidataSendStatMapper.getWeekPriceStats(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getChannelCodes(), reachStatDTO.getSignList());
        Map<String, ChannelDO> channelDOMap = getChannelMap();
        return channelReachStatDOList.stream().map(row -> MonoBillStatVO.convert(row, "", reachStatDTO.getSmsTypeCodes())).peek(vo -> {
            ChannelDO channelDO = channelDOMap.get(vo.getChannelCode());
            vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
        }).collect(Collectors.toList());
    }


    @Override
    public List<MonoPriceStatVO> getMonthPriceStatsByDate(ReachStatDTO reachStatDTO) {
        MonthWeekUtils.DateRange dateRange = MonthWeekUtils.getMonthStartAndEnd(reachStatDTO.getStatDate());
        reachStatDTO.setStartDate(dateRange.getStartDate());
        reachStatDTO.setEndDate(dateRange.getEndDate());
        log.info("获取月日期列表, reachStatDTO={},", reachStatDTO);
        List<ChannelPriceStatDO> channelReachStatDOList = bidataSendStatMapper.getMonthPriceStatsByDate(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getSmsTypeCodes(), reachStatDTO.getSignList());

        Map<String, ChannelDO> channelDOMap = getChannelMap();
        return channelReachStatDOList.stream().map(row -> MonoPriceStatVO.convert(row, "", reachStatDTO.getSmsTypeCodes())).peek(vo -> {
            ChannelDO channelDO = channelDOMap.get(vo.getChannelCode());
            vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
        }).collect(Collectors.toList());
    }

    @Override
    public List<MonoPriceStatVO> getWeekPriceStatsByDate(ReachStatDTO reachStatDTO) {
        MonthWeekUtils.DateRange dateRange = MonthWeekUtils.getWeekStartAndEnd(reachStatDTO.getStatDate());
        reachStatDTO.setStartDate(dateRange.getStartDate());
        reachStatDTO.setEndDate(dateRange.getEndDate());
        log.info("获取周日期列表, reachStatDTO={},", reachStatDTO);
        List<ChannelPriceStatDO> channelReachStatDOList = bidataSendStatMapper.getWeekPriceStatsByDate(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getSmsTypeCodes(), reachStatDTO.getSignList());
        Map<String, ChannelDO> channelDOMap = getChannelMap();
        return channelReachStatDOList.stream().map(row -> MonoPriceStatVO.convert(row, "", reachStatDTO.getSmsTypeCodes())).peek(vo -> {
            ChannelDO channelDO = channelDOMap.get(vo.getChannelCode());
            vo.setChannelCodeName(channelDO != null ? channelDO.getName() : "");
        }).collect(Collectors.toList());
    }

    @Override
    public List<PolyPriceStatVO> getMonthPriceStatsByType(ReachStatDTO reachStatDTO) {
        reachStatDTO.setStartDate(MonthWeekUtils.getStartDateMonthsAgo(5));
        reachStatDTO.setEndDate(MonthWeekUtils.getCurrentDate());

        log.info("获取月类型列表, reachStatDTO={},", reachStatDTO);
        List<TypePriceStatDO> typeReachStatDOList = bidataSendStatMapper.getMonthPriceStatsByType(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getChannelCode(), reachStatDTO.getSmsTypeCode(), reachStatDTO.getSignList());

        Map<String, PolyPriceStatVO> polyReachStatVOMap = new HashMap<>();
        Map<String, ChannelDO> channelDOMap = getChannelMap();
        typeReachStatDOList.forEach(row -> {
            PolyPriceStatVO polyReachStatVO = polyReachStatVOMap.get(row.getChannelCode());
            if (polyReachStatVO == null) {

                String channelCodeName = Optional.ofNullable(channelDOMap.get(row.getChannelCode())).map(ChannelDO::getName).orElse("");
                polyReachStatVO = PolyPriceStatVO.builder().statDate(new ArrayList<>()).channelCode(row.getChannelCode()).channelCodeName(channelCodeName).priceCount(new ArrayList<>()).reachBillCount(new ArrayList<>()).reachRate(new ArrayList<>()).build();
                polyReachStatVO.add(row);
                polyReachStatVOMap.put(row.getChannelCode(), polyReachStatVO);
            } else {
                polyReachStatVO.add(row);
            }
        });
        List<PolyPriceStatVO> result = new ArrayList<>(polyReachStatVOMap.values());

        List<String> allMonths = MonthWeekUtils.getMonthsBetween(reachStatDTO.getStartDate().substring(0, 7), reachStatDTO.getEndDate().substring(0, 7));

        return result.stream().map(vo -> {
            Map<String, BigDecimal> priceCountMap = new HashMap<>();
            Map<String, Integer> reachBillCountMap = new HashMap<>();
            Map<String, String> reachRateMap = new HashMap<>();

            // 构建各字段的月度映射
            for (int i = 0; i < vo.getStatDate().size(); i++) {
                String date = vo.getStatDate().get(i);
                priceCountMap.put(date, vo.getPriceCount().get(i));
                reachBillCountMap.put(date, vo.getReachBillCount().get(i));
                reachRateMap.put(date, vo.getReachRate().get(i));
            }

            // 补全缺失月份
            List<BigDecimal> filledPriceCount = new ArrayList<>();
            List<Integer> filledReachBillCount = new ArrayList<>();
            List<String> filledReachRate = new ArrayList<>();

            for (String month : allMonths) {
                filledPriceCount.add(priceCountMap.getOrDefault(month, BigDecimal.ZERO));
                filledReachBillCount.add(reachBillCountMap.getOrDefault(month, 0));
                filledReachRate.add(reachRateMap.getOrDefault(month, "0%"));
            }

            // 构建并返回新的 VO
            return PolyPriceStatVO.builder()
                    .statDate(allMonths)
                    .channelCode(vo.getChannelCode())
                    .channelCodeName(vo.getChannelCodeName())
                    .priceCount(filledPriceCount)
                    .reachBillCount(filledReachBillCount)
                    .reachRate(filledReachRate)
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    public List<PolyPriceStatVO> getWeekPriceStatsByType(ReachStatDTO reachStatDTO) {
        reachStatDTO.setStartDate(MonthWeekUtils.getStartDateOfWeeksAgo(5));
        reachStatDTO.setEndDate(MonthWeekUtils.getCurrentDate());
        log.info("获取周类型列表, reachStatDTO={},", reachStatDTO);
        List<TypePriceStatDO> typeReachStatDOList = bidataSendStatMapper.getWeekPriceStatsByType(reachStatDTO.getStartDate(), reachStatDTO.getEndDate(), reachStatDTO.getChannelCode(), reachStatDTO.getSmsTypeCode(), reachStatDTO.getSignList());

        Map<String, PolyPriceStatVO> polyReachStatVOMap = new HashMap<>();
        Map<String, ChannelDO> channelDOMap = getChannelMap();
        typeReachStatDOList.forEach(row -> {
            PolyPriceStatVO polyReachStatVO = polyReachStatVOMap.get(row.getChannelCode());
            if (polyReachStatVO == null) {
                String channelCodeName = Optional.ofNullable(channelDOMap.get(row.getChannelCode())).map(ChannelDO::getName).orElse("");
                polyReachStatVO = PolyPriceStatVO.builder().statDate(new ArrayList<>()).channelCode(row.getChannelCode()).channelCodeName(channelCodeName).priceCount(new ArrayList<>()).reachBillCount(new ArrayList<>()).reachRate(new ArrayList<>()).build();
                polyReachStatVO.add(row);
                polyReachStatVOMap.put(row.getChannelCode(), polyReachStatVO);
            } else {
                polyReachStatVO.add(row);
            }
        });
        List<PolyPriceStatVO> result = new ArrayList<>(polyReachStatVOMap.values());

        List<String> allWeeks = MonthWeekUtils.getWeeksBetween(reachStatDTO.getStartDate(), reachStatDTO.getEndDate());

        return result.stream().map(vo -> {

            Map<String, BigDecimal> reachCountMap = new HashMap<>();
            Map<String, Integer> reachBillCountMap = new HashMap<>();
            Map<String, String> reachRateMap = new HashMap<>();

            for (int i = 0; i < vo.getStatDate().size(); i++) {
                String week = vo.getStatDate().get(i);
                reachCountMap.put(week, vo.getPriceCount().get(i));
                reachBillCountMap.put(week, vo.getReachBillCount().get(i));
                reachRateMap.put(week, vo.getReachRate().get(i));
            }

            List<BigDecimal> filledReachCount = new ArrayList<>();
            List<Integer> filledReachBillCount = new ArrayList<>();
            List<String> filledReachRate = new ArrayList<>();

            for (String week : allWeeks) {
                filledReachCount.add(reachCountMap.getOrDefault(week, BigDecimal.ZERO));
                filledReachBillCount.add(reachBillCountMap.getOrDefault(week, 0));
                filledReachRate.add(reachRateMap.getOrDefault(week, "0%"));
            }

            return PolyPriceStatVO.builder()
                    .statDate(allWeeks)
                    .channelCode(vo.getChannelCode())
                    .channelCodeName(vo.getChannelCodeName())
                    .priceCount(filledReachCount)
                    .reachBillCount(filledReachBillCount)
                    .reachRate(filledReachRate)
                    .build();
        }).collect(Collectors.toList());
    }
}
