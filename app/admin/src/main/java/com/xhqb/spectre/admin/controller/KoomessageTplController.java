package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.service.KoomessageTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/koomessage/tpl")
@Slf4j
public class KoomessageTplController {
    @Resource
    private KoomessageTemplateService koomessageTemplateService;
    @PostMapping("/loadReport")
    public AdminResult queryList(@RequestParam(value = "exactDate", required = false) String exactDate,
                                 @RequestParam(value = "recentDays", required = false) int recentDays) {
        koomessageTemplateService.loadReports(exactDate, recentDays);
        return AdminResult.success();
    }
}
