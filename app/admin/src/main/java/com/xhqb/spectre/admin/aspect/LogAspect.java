package com.xhqb.spectre.admin.aspect;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.xhqb.spectre.admin.service.OpLogService;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.OpLogDO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 日志操作
 * 具体获取操作类型操作
 * 1.get,query方法名称开头的为查询
 * 2.add,create方法名称开头的为新增
 * 3.update,modify,edit方法名称开头的为编辑
 * 4.delete,batchDelete方法名称开头的为删除
 * 5.enable 方法名称开头的为启用
 * 6.disable方法名称开头的为禁用
 * 7.cancel开头的为取消
 *
 * <AUTHOR>
 * @date 2021/9/29
 */
@Aspect
@Component
@Slf4j
public class LogAspect {

    /**
     * 请求参数最大长度
     */
    private static final int REQ_PARAM_MAX_LENGTH = 2040;

    /**
     * 默认的系统名称
     */
    private static final String DEFAULT_SYSTEM = "spectre-admin";
    /**
     * 系统请求头key
     */
    private static final String SYSTEM_HEADER_KEY = "x-system";
    /**
     * 真实IP请求头
     */
    private static final String X_REAL_IP_KEY = "x-real-ip";
    /**
     * 空String数组
     */
    private static final String[] EMPTY_STR_ARRAY = {};
    /**
     * 排除的包名 不需要进行序列化
     */
    private static final String[] SKIP_PACKAGES = {"org.springframework"};

    /**
     * 参数获取器
     */
    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();
    /**
     * 参数名称映射
     * key -> 请求的方法(Controller方法)  value -> 方法的参数名称
     */
    private final ConcurrentHashMap<Method, String[]> paramNameMapping = new ConcurrentHashMap<>(512);
    /**
     * 操作类型映射
     * key -> 请求的方法(Controller方法)  value -> 操作类型 (1->查询 2->新增 3->修改 4->删除)
     */
    private final ConcurrentHashMap<Method, Integer> opTypeMapping = new ConcurrentHashMap<>(512);

    /**
     * 是否使用nginx转发的ip地址
     */
    @Value("${spectre.admin.useNginxIp:false}")
    private Boolean useNginxIp;

    @Autowired
    private OpLogService opLogService;

    /**
     * 做日志记录
     *
     * @param joinPoint
     */
    @Before("@within(org.springframework.stereotype.Controller) || @within(org.springframework.web.bind.annotation.RestController)")
    public void logAspect(JoinPoint joinPoint) {
        try {
            doLogAspect(joinPoint);
        } catch (Throwable e) {
            Signature signature = joinPoint.getSignature();
            log.warn("日志记录失败,method = {}", signature.getDeclaringTypeName() + "." + signature.getName(), e);
        }
    }

    /**
     * 真正做日志记录操作
     *
     * @param joinPoint
     */
    private void doLogAspect(JoinPoint joinPoint) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String system = request.getHeader(SYSTEM_HEADER_KEY);
        if (StringUtils.isBlank(system)) {
            system = DEFAULT_SYSTEM;
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String actionName = signature.getDeclaringTypeName() + "." + signature.getName();
        Method method = signature.getMethod();

        Integer opType = this.getOpType(method);
        // 具体参数信息
        Map<String, Object> parameters = getParameters(method, joinPoint.getArgs());
        String reqParam;
        try {
            reqParam = JSON.toJSONString(parameters);
        } catch (Exception e) {
            log.warn("序列化请求参数异常", e);
            reqParam = parameters.toString();
        }

        if (StringUtils.isNotBlank(reqParam) && reqParam.length() > REQ_PARAM_MAX_LENGTH) {
            // 不能够超过最大长度
            reqParam = reqParam.substring(0, REQ_PARAM_MAX_LENGTH);
        }

        OpLogDO opLogDO = OpLogDO.builder()
                .sysName(system)
                .opType(opType)
                .reqMethod(request.getMethod())
                .reqUri(request.getRequestURI())
                .actionName(actionName)
                .reqParam(reqParam)
                .reqIp(getClientIp(request))
                .operator(SsoUserInfoUtil.getUserName())
                .opTime(DateUtil.getNow())
                .build();

        // 写入日志
        opLogService.writeLog(opLogDO);
    }

    /**
     * 具体获取操作类型操作
     * <p>
     * 操作类型 1->查询 2->新增 3->修改 4->删除 5->启用 6->禁用 7->取消 20->其他
     * <p>
     * 1.get,query方法名称开头的为查询
     * 2.add,create方法名称开头的为新增
     * 3.update,modify,edit方法名称开头的为编辑
     * 4.delete,batchDelete方法名称开头的为删除
     * 5.enable 方法名称开头的为启用
     * 6.disable方法名称开头的为禁用
     * 7.cancel开头的为取消
     *
     * @param method
     * @return
     */
    private Integer getOpType(Method method) {
        Integer opType = opTypeMapping.putIfAbsent(method, OpType.getOpType(method.getName()));
        if (Objects.isNull(opType)) {
            opType = opTypeMapping.get(method);
        }
        return opType;
    }

    /**
     * 获取到请求参数
     *
     * @param method
     * @param paramValues
     * @return
     */
    private Map<String, Object> getParameters(Method method, Object[] paramValues) {
        Map<String, Object> parameters = Maps.newHashMap();
        if (Objects.isNull(paramValues)) {
            return parameters;
        }
        String[] paramNames = getParamNames(method);
        if (paramNames.length == paramValues.length) {
            // 参数一样
            for (int i = 0; i < paramValues.length; i++) {
                Class<?> valueClazz = null;
                if (Objects.nonNull(paramValues[i])) {
                    valueClazz = paramValues[i].getClass();
                }
                if (Objects.nonNull(valueClazz) && StringUtils.containsAny(valueClazz.getName(), SKIP_PACKAGES)) {
                    parameters.put(paramNames[i], valueClazz.getSimpleName());
                } else {
                    parameters.put(paramNames[i], paramValues[i]);
                }
            }
        } else {
            // 参数不一样时 直接写参数索引为key
            for (int i = 0; i < paramValues.length; i++) {
                Class<?> valueClazz = null;
                if (Objects.nonNull(paramValues[i])) {
                    valueClazz = paramValues[i].getClass();
                }
                if (Objects.nonNull(valueClazz) && StringUtils.containsAny(valueClazz.getName(), SKIP_PACKAGES)) {
                    parameters.put(String.valueOf(i), valueClazz.getSimpleName());
                } else {
                    parameters.put(String.valueOf(i), paramValues[i]);
                }
            }
        }

        return parameters;
    }

    /**
     * 获取到参数名称
     *
     * @param method
     * @return
     */
    private String[] getParamNames(Method method) {
        try {
            // 参数名称
            String[] paramNames = paramNameMapping.putIfAbsent(method, parameterNameDiscoverer.getParameterNames(method));
            if (Objects.isNull(paramNames)) {
                paramNames = paramNameMapping.get(method);
            }
            if (Objects.isNull(paramNames)) {
                paramNames = EMPTY_STR_ARRAY;
            }
            return paramNames;
        } catch (Exception e) {
            log.warn("获取方法参数名称失败,method = {}", method, e);
        }
        return EMPTY_STR_ARRAY;
    }

    /**
     * 获取到客户端ip地址
     *
     * @param request
     * @return
     */
    private String getClientIp(HttpServletRequest request) {
        if (!Objects.equals(useNginxIp, true)) {
            // 不使用nginx转发的ip
            return request.getRemoteAddr();
        }
        String clientIp = request.getHeader(X_REAL_IP_KEY);
        if (StringUtils.isBlank(clientIp)) {
            clientIp = request.getRemoteAddr();
        }
        return clientIp;
    }

    /**
     * 操作类型 1->查询 2->新增 3->修改 4->删除 5->启用 6->禁用 7->取消 20->其他
     */
    @Getter
    @AllArgsConstructor
    enum OpType {
        QUERY(1, "查询"),
        GET(1, "查询"),
        ADD(2, "新增"),
        CREATE(2, "新增"),
        UPDATE(3, "修改"),
        MODIFY(3, "修改"),
        EDIT(3, "修改"),
        DELETE(4, "删除"),
        BATCH_DELETE(4, "删除"),
        ENABLE(5, "启用"),
        DISABLE(6, "禁用"),
        CANCEL(7, "取消"),
        OTHER(20, "其他");

        private static final String BATCH_DELETE_NAME = "batchDelete";
        private final Integer type;
        private final String desc;

        /**
         * 获取到操作类型
         *
         * @param methodName
         * @return
         */
        public static Integer getOpType(String methodName) {
            String name;
            for (OpType opType : values()) {
                name = opType.name();
                if (Objects.equals(opType, BATCH_DELETE)) {
                    name = BATCH_DELETE_NAME;
                }
                if (StringUtils.startsWithIgnoreCase(methodName, name)) {
                    return opType.type;
                }
            }
            return OTHER.type;
        }
    }

}
