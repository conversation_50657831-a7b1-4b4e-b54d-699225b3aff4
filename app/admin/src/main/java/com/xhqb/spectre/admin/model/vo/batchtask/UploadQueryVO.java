package com.xhqb.spectre.admin.model.vo.batchtask;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/9/27
 */
@Data
public class UploadQueryVO implements Serializable {

    /**
     * 文件参数列表分片ID
     * (分片ID获取到数据就是BatchTaskParamDTO 这个列表)
     * 参考 BatchTaskUploadVO
     */
    private QueryTaskSegmentVO taskParamItem;

    /**
     * 文件检测结果
     */
    private CheckResultVO checkResult;

    /**
     * 查询状态 0->正在处理 1->上传处理完成 2->上传处理失败
     */
    private Integer status;

}
