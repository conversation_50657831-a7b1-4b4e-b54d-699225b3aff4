package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.MobileWhiteDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.MobileWhiteVO;
import com.xhqb.spectre.admin.service.MobileWhiteService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.MobileWhiteQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 10:20
 * @Description:
 */
@RestController
@RequestMapping("/mobileWhite")
@Slf4j
public class MobileWhiteController {

    @Autowired
    private MobileWhiteService mobileWhiteService;

    /**
     * 查询白名单列表
     *
     * @param mobileWhiteQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(@ModelAttribute MobileWhiteQuery mobileWhiteQuery, Integer pageNum, Integer pageSize) {
        mobileWhiteQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<MobileWhiteVO> commonPager = mobileWhiteService.listByPage(mobileWhiteQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询白名单详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(mobileWhiteService.getById(id));
    }

    /**
     * 添加白名单
     *
     * @param mobileWhiteDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_MOBILE_WHITE)
    public AdminResult create(@RequestBody MobileWhiteDTO mobileWhiteDTO) {
        log.info("create mobileWhite, mobileWhiteDTO: {}", JSON.toJSONString(mobileWhiteDTO));
        mobileWhiteService.create(mobileWhiteDTO);
        return AdminResult.success();
    }

    /**
     * 删除白名单
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_MOBILE_WHITE)
    public AdminResult delete(@PathVariable("id") Integer id) {
        log.info("delete mobileWhite, id: {}", id);
        mobileWhiteService.delete(id);
        return AdminResult.success();
    }

    /**
     * 批量删除白名单
     *
     * @param idList
     * @return
     */
    @DeleteMapping("/batchDelete")
    @LogOpTime(OpLogConstant.MODULE_MOBILE_WHITE)
    public AdminResult batchDelete(@RequestBody List<Integer> idList) {
        log.info("batchDelete mobileWhite, idList: {}", JSON.toJSONString(idList));
        mobileWhiteService.batchDelete(idList);
        return AdminResult.success();
    }
}
