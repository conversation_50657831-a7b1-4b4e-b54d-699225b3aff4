package com.xhqb.spectre.admin.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.sequencegenerator.DistributedSequence;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.TestContentTaskDTO;
import com.xhqb.spectre.admin.model.dto.TestContentTaskRecordDTO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskDetailTypeVO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskDetailVO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskOverviewVO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskVO;
import com.xhqb.spectre.admin.readonly.mapper.SmsOrderReadonlyMapper;
import com.xhqb.spectre.admin.service.FeiShuAlert;
import com.xhqb.spectre.admin.service.test.tool.TestContentTaskRecordBizService;
import com.xhqb.spectre.admin.service.test.tool.TestContentTaskService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.entity.test.tool.TestContentTaskDO;
import com.xhqb.spectre.common.dal.entity.test.tool.TestContentTaskRecordDO;
import com.xhqb.spectre.common.dal.entity.test.tool.TestWhiteDO;
import com.xhqb.spectre.common.dal.mapper.SignMapper;
import com.xhqb.spectre.common.dal.mapper.TestContentTaskMapper;
import com.xhqb.spectre.common.dal.mapper.TestContentTaskRecordMapper;
import com.xhqb.spectre.common.dal.mapper.TestWhiteMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.TestContentTaskQuery;
import com.xhqb.spectre.common.utils.SmsContentUtil;
import com.xhqb.spectre.sdk.sms.contants.SmsType;
import com.xhqb.spectre.sdk.sms.dto.SdkContentItemDTO;
import com.xhqb.spectre.sdk.sms.dto.SdkContentRequestDTO;
import com.xhqb.spectre.sdk.sms.send.CompositeMessageResult;
import com.xhqb.spectre.sdk.sms.send.SendResult;
import com.xhqb.spectre.sdk.sms.send.content.ContentResponse;
import com.xhqb.spectre.sdk.sms.service.SdkSmsSenderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TestContentTaskServiceImpl implements TestContentTaskService {

    private static final int STATUS_INIT = 0;
    private static final int STATUS_PROCESSING = 1;
    private static final int STATUS_SUCCESS = 2;
    private static final int STATUS_FAILED = 3;
    private static final int STATUS_CANCEL = 4;

    private static final int CHECK_STATUS_INIT = 0;
    private static final int CHECK_STATUS_PROCESSING = 1;
    private static final int CHECK_STATUS_SUCCESS = 2;

    private static final double COMPLETE_PERCENT = 100.0;
    private static final double INIT_PERCENT = 0.0;
    @Resource
    protected DistributedSequence distributedSequence;
    @Resource
    private TestContentTaskMapper testContentTaskMapper;
    @Resource
    private TestContentTaskRecordBizService testContentTaskRecordBizService;
    @Resource
    private TestWhiteMapper testWhiteMapper;
    @Resource
    private SdkSmsSenderService sdkSmsSenderService;
    @Resource
    private SignMapper signMapper;
    @Resource
    private VenusConfig venusConfig;
    @Resource
    private SmsOrderReadonlyMapper smsOrderReadonlyMapper;
    @Resource
    private TestContentTaskRecordMapper testContentTaskRecordMapper;
    @Resource
    private FeiShuAlert feiShuAlert;

    @Override
    public CommonPager<TestContentTaskVO> listByPage(TestContentTaskQuery testContentTaskQuery) {
        return PageResultUtils.result(
                () -> {
                    if (StringUtils.isNotBlank(testContentTaskQuery.getLikeTplCode())) {
                        return this.testContentTaskMapper.countUnionDataByQuery(testContentTaskQuery);
                    } else {
                        return testContentTaskMapper.countByQuery(testContentTaskQuery);
                    }
                },
                () -> composeTestTaskStats(testContentTaskQuery)
        );
    }

    @Override
    public String add(TestContentTaskDTO testContentTaskDTO) {

        this.checkSignName(testContentTaskDTO.getContent());
        TestContentTaskDO testContentTaskDO = new TestContentTaskDO();
        BeanUtils.copyProperties(testContentTaskDTO, testContentTaskDO);
        testContentTaskDO.setTaskStatus(STATUS_INIT);
        testContentTaskDO.setCheckStatus(CHECK_STATUS_INIT);
        testContentTaskDO.setTaskId(distributedSequence.nextStr("test_content_task"));
        testContentTaskDO.setCreateTime(new Date());
        testContentTaskDO.setUpdateTime(new Date());
        testContentTaskDO.setCreator(SsoUserInfoUtil.getUserName());
        testContentTaskDO.setUpdater(SsoUserInfoUtil.getUserName());
        testContentTaskMapper.insertBySelective(testContentTaskDO);
        return testContentTaskDO.getTaskId();
    }

    @Override
    public String update(TestContentTaskDTO testContentTaskDTO) {
        TestContentTaskDO modelDO = testContentTaskMapper.selectByTaskId(testContentTaskDTO.getTaskId());
        if (Objects.isNull(modelDO)) {
            throw new BizException("记录不存在");
        }
        this.checkSignName(testContentTaskDTO.getContent());
        TestContentTaskDO record = buildDOByDTO(testContentTaskDTO);
        record.setId(modelDO.getId());
        testContentTaskMapper.updateByPrimaryKeySelective(record);
        return testContentTaskDTO.getTaskId();
    }

    @Override
    public TestContentTaskVO detail(String taskId) {
        TestContentTaskDO testContentTaskDO = testContentTaskMapper.selectByTaskId(taskId);
        if (testContentTaskDO != null) {
            return buildTestContentTaskVO(testContentTaskDO);
        }
        return null;
    }

    @Override
    public TestContentTaskOverviewVO overview(String taskId) {
        return testContentTaskRecordBizService.overview(taskId);
    }

    @Override
    public List<TestContentTaskDetailVO> detailBrand(String taskId) {
        return testContentTaskRecordBizService.detailBrand(taskId);
    }

    public String executeTask(String taskId) {
        return this.executeTask(taskId, null);
    }

    @Override
    public String executeTask(String taskId, Map<String, String> map) {
        TestContentTaskDO modelDO = testContentTaskMapper.selectByTaskId(taskId);
        if (Objects.isNull(modelDO)) {
            throw new BizException("记录不存在");
        }
        // 判断状态 初始化
        if (!Objects.equals(STATUS_INIT, modelDO.getTaskStatus())) {
            throw new BizException("任务“初始状态”：支持“执行”操作");
        }

        // 短信内容
        String[] contents = modelDO.getContent().split("\n");

        // 手机号
        // {"huawei":2,"rongyao":1,"xiaomi":2,"oppo":1}
        Map<String, List<String>> brandMap = fetchRandomPhoneNumbersByBrandConfig(modelDO);
        log.info("brandMap:{}", JsonLogUtil.toJSONString(brandMap));
        try {
            // 更新为处理中状态
            updateTaskStatus(modelDO, STATUS_PROCESSING);

            processBatchSmsDelivery(taskId, contents, brandMap, map);
            // 更新为成功状态
            updateTaskStatus(modelDO, STATUS_SUCCESS);

        } catch (Exception e) {
            log.error("执行任务[{}]失败:", taskId, e);
            updateTaskStatus(modelDO, STATUS_FAILED);
        } finally {
            executeTaskAfterNotify(modelDO, brandMap);
        }
        return "success";
    }

    @Override
    public void updateTaskRecordReportStatus(String taskId) {
        Date endTime = DateUtil.date();
        log.info("开始更新任务记录回执状态：{}", taskId);

        TestContentTaskDO taskDO = testContentTaskMapper.selectByTaskId(taskId);
        if (Objects.isNull(taskDO) || taskDO.getTaskStatus() != STATUS_SUCCESS) {
            log.info("任务记录回执状态：{}|任务不存在或任务状态不是成功", taskId);
            return;
        }

        // 判断当前时间 和 taskDO 创建时间是否在三天之内 三天之内就不用刷新了
        if (DateUtil.between(taskDO.getCreateTime(), new Date(), DateUnit.DAY) > 3) {
            return;
        }

        long taskBeginTime = DateUtil.beginOfDay(taskDO.getCreateTime()).getTime() / 1000;

        List<TestContentTaskRecordDO> taskRecordDOList = testContentTaskRecordMapper.selectByTaskId(taskId);
        if (CollectionUtils.isEmpty(taskRecordDOList)) {
            log.info("任务记录回执状态：{}|taskRecordDOList is empty", taskId);
            return;
        }
        for (TestContentTaskRecordDO taskRecordDO : taskRecordDOList) {
            log.info("查询 order requestId:{} | mobile:{} | startTime:{} | endTime:{}", taskRecordDO.getRequestId(), taskRecordDO.getMobile(), Math.toIntExact(taskDO.getCreateTime().getTime() / 1000), Math.toIntExact(endTime.getTime() / 1000));
            List<SmsOrderDO> smsOrderDOList = smsOrderReadonlyMapper.selectByRequestIdAndMobile(taskRecordDO.getRequestId(), taskRecordDO.getMobile(),
                    taskBeginTime, endTime.getTime() / 1000);
            if (CollectionUtils.isEmpty(smsOrderDOList)) {
                log.info("任务记录回执状态：{}|smsOrderDOList is empty", taskId);
                continue;
            }

            log.info("任务查询order：{}|smsOrderDOList:{}", taskId, JsonLogUtil.toJSONString(smsOrderDOList));

            // 降序排序（新时间在前）
            smsOrderDOList = smsOrderDOList.stream()
                    .sorted(Comparator.comparing(SmsOrderDO::getSendTime).reversed())
                    .collect(Collectors.toList());

            SmsOrderDO smsOrderDO = smsOrderDOList.get(0);
            log.info("任务ID：{}|smsOrderDO:{}", taskId, JsonLogUtil.toJSONString(smsOrderDO));

            Integer reportStatus = smsOrderDO.getReportStatus() == null ? -1 : smsOrderDO.getReportStatus();
            log.info("任务ID：{}|reportStatus:{}", taskId, reportStatus);
            taskRecordDO.setReportStatus(reportStatus);
            testContentTaskRecordMapper.updateByPrimaryKeySelective(taskRecordDO);
        }

        // 判断 发送成功短信数 、 渠道回执成功短信数 、app 上报条数
        updateTaskCheckStatusBasedOnRecords(taskId, taskRecordDOList, taskDO);

    }

    private void updateTaskCheckStatusBasedOnRecords(String taskId, List<TestContentTaskRecordDO> taskRecordDOList, TestContentTaskDO taskDO) {
        try {

            if (!Objects.equals(taskDO.getTaskStatus(), STATUS_SUCCESS) || Objects.equals(taskDO.getCheckStatus(), CHECK_STATUS_SUCCESS)) {
                // 非任务完成 不更新检测状态
                return;
            }

            if (taskRecordDOList.stream().filter(m -> m.getSendStatus() == 0 && m.getReportStatus() == 0 && m.getAppReportStatus() == 1).count() == taskRecordDOList.size()) {
                log.info("任务ID：{}|任务完成", taskId);
                updateCheckStatus(taskDO, CHECK_STATUS_SUCCESS);
                return;
            }
            long time = new Date().getTime() / 1000L - taskDO.getSubmitTime();
            int checkStatus = CHECK_STATUS_PROCESSING;
            double checkResult = CommonUtil.division(time, venusConfig.getTestContentTaskCountdown());
            log.info("costRate:{}", CommonUtil.division(time, venusConfig.getTestContentTaskCountdown()));
            if (checkResult >= 100) {
                checkStatus = CHECK_STATUS_SUCCESS;
            }
            log.info("任务ID：{}|checkResult:{}", taskId, checkResult);
            updateCheckStatus(taskDO, checkStatus);
            if (checkStatus == CHECK_STATUS_SUCCESS) {
                testContentTaskRecordMapper.updateAppReportStatusByTaskId(taskDO.getTaskId());
            }
        } catch (Exception e) {
            log.error("任务ID：{}|更新任务检测状态异常", taskId, e);
        }
    }

    private void updateCheckStatus(TestContentTaskDO taskDO, int checkStatus) {
        if (checkStatus == CHECK_STATUS_SUCCESS) {
            taskDO.setCompleteTime((int) (System.currentTimeMillis() / 1000));
        }
        taskDO.setCheckStatus(checkStatus);
        taskDO.setUpdateTime(new Date());
        taskDO.setUpdater(StringUtils.isEmpty(SsoUserInfoUtil.getUserName()) ? "system" : SsoUserInfoUtil.getUserName());
        testContentTaskMapper.updateByPrimaryKeySelective(taskDO);
    }

    @Override
    public void cancel(String taskId) {
        TestContentTaskDO taskDO = testContentTaskMapper.selectByTaskId(taskId);
        if (Objects.isNull(taskDO)) {
            log.info("任务不存在");
            return;
        }
        if (!Objects.equals(0, taskDO.getTaskStatus())) {
            log.info("任务“初始状态”：支持“取消”操作");
            return;
        }

        updateTaskStatus(taskDO, STATUS_CANCEL);
    }

    @Override
    public TestContentTaskDetailTypeVO detailType(String taskId) {
        TestContentTaskDO testContentTaskDO = testContentTaskMapper.selectByTaskId(taskId);
        return testContentTaskRecordBizService.getTestContentTaskDetail(taskId, testContentTaskDO);
    }

    private void updateTaskStatus(TestContentTaskDO task, int status) {
        task.setTaskStatus(status);
        task.setUpdateTime(new Date());
        if (status != STATUS_CANCEL) {
            task.setSubmitTime((int) (System.currentTimeMillis() / 1000));
        }

        task.setUpdater(SsoUserInfoUtil.getUserName());
        if (testContentTaskMapper.updateByPrimaryKeySelective(task) != 1) {
            throw new BizException("更新任务状态失败");
        }
    }

    private void processBatchSmsDelivery(String taskId, String[] contents, Map<String, List<String>> brandMap, Map<String, String> map) {
        for (String content : contents) {
            String signName = SmsContentUtil.split(content).getSignName();
            if (StringUtils.isEmpty(signName)) {
                throw new BizException("短信内容格式不正确");
            }
            SignDO signDO = signMapper.selectByName(signName);
            if (Objects.isNull(signDO)) {
                throw new BizException("签名不存在");
            }
            String mainContent = SmsContentUtil.split(content).getContent();
            String tplCode = "";
            if (Objects.nonNull(map) && !map.isEmpty()) {
                tplCode = map.get(content);
            }
            for (Map.Entry<String, List<String>> entry : brandMap.entrySet()) {
                List<String> phoneList = entry.getValue();
                for (String phone : phoneList) {
                    sendAndRecordSmsTask(taskId, content, phone, signDO, entry.getKey(), mainContent, tplCode);
                }
            }
        }
    }

    private void sendAndRecordSmsTask(String taskId, String content,
                                      String phone, SignDO signDO, String brand, String mainContent, String tplCode) {

        SdkContentRequestDTO sdkContentRequest = new SdkContentRequestDTO();
        sdkContentRequest.setAppCode(venusConfig.getTestContentAppCode());
        sdkContentRequest.setAppSecret(venusConfig.getTestContentAppSecret());
        sdkContentRequest.setContent(mainContent);
        sdkContentRequest.setSmsType(SmsType.MARKET);
        sdkContentRequest.setSignCode(signDO.getCode());

        // 生成请求ID
        String requestId = distributedSequence.nextStr("spectre::admin::sdk-content-request:");
        sdkContentRequest.setRequestId(requestId);
        sdkContentRequest.add(new SdkContentItemDTO(phone));

        // 发送短信
        SendResult<CompositeMessageResult<ContentResponse>> result =
                sdkSmsSenderService.sendContent(sdkContentRequest);

        // 记录结果
        TestContentTaskRecordDTO record = new TestContentTaskRecordDTO();
        record.setTaskId(taskId);
        record.setContent(content);
        record.setMobile(phone);
        record.setSmsSignId(signDO.getId());
        record.setSmsTplCode(venusConfig.getTestContentTplCode());
        record.setSendStatus(result.isSuccess() ? 0 : 1);
        record.setRequestId(requestId);
        record.setBrand(brand);
        record.setTplCode(tplCode);

        testContentTaskRecordBizService.add(record);
    }

    private Map<String, List<String>> fetchRandomPhoneNumbersByBrandConfig(TestContentTaskDO modelDO) {
        Map<String, List<String>> brandMap = new HashMap<>();
        String brandConfig = modelDO.getBrandConfig();
        JSONObject jsonObject = JSONObject.parseObject(brandConfig);
        jsonObject.forEach((key, value) -> {
            List<String> phoneNumList = testWhiteMapper.selectByBrand(key).stream()
                    .map(TestWhiteDO::getMobile).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(phoneNumList)) {
                List<String> randomList = getRandomByStream(phoneNumList, (Integer) value);
                brandMap.put(key, randomList);
            }
        });
        return brandMap;
    }

    private TestContentTaskDO buildDOByDTO(TestContentTaskDTO testContentTaskDTO) {
        TestContentTaskDO testContentTaskDO = new TestContentTaskDO();
        BeanUtils.copyProperties(testContentTaskDTO, testContentTaskDO);
        testContentTaskDO.setUpdateTime(new Date());
        testContentTaskDO.setUpdater(SsoUserInfoUtil.getUserName());
        return testContentTaskDO;
    }

    private TestContentTaskVO buildTestContentTaskVO(TestContentTaskDO testContentTaskDO) {
        TestContentTaskVO vo = new TestContentTaskVO();
        BeanUtils.copyProperties(testContentTaskDO, vo);

        Integer checkStatus = testContentTaskDO.getCheckStatus();
        int submitTime = testContentTaskDO.getSubmitTime();
        String completeTime = safeFormat(testContentTaskDO.getCompleteTime() * 1000L);
        String finalCompleteTime = safeFormat((testContentTaskDO.getSubmitTime() + venusConfig.getTestContentTaskCountdown()) * 1000L);
        switch (checkStatus) {
            case CHECK_STATUS_INIT:
                vo.setCheckResult(INIT_PERCENT);
                break;

            case CHECK_STATUS_SUCCESS:
                vo.setCheckResult(COMPLETE_PERCENT);
                vo.setCompleteTimeStr(StringUtils.isEmpty(completeTime) ? finalCompleteTime : completeTime);
                break;

            case CHECK_STATUS_PROCESSING:
                long elapsedSeconds = (System.currentTimeMillis() - submitTime * 1000L) / 1000;
                double progress = CommonUtil.division(elapsedSeconds, venusConfig.getTestContentTaskCountdown());
                vo.setCheckResult(Math.min(progress, COMPLETE_PERCENT));
                if (Objects.equals(COMPLETE_PERCENT, vo.getCheckResult())) {
                    vo.setCheckStatus(CHECK_STATUS_SUCCESS);
                    vo.setCompleteTimeStr(StringUtils.isEmpty(completeTime) ? finalCompleteTime : completeTime);
                }
                break;
        }
        String submitTimeStr = safeFormat(submitTime * 1000L);
        vo.setSubmitTimeStr(StringUtils.isEmpty(submitTimeStr) ? DateUtil.formatDateTime(testContentTaskDO.getCreateTime()) : submitTimeStr);
        Integer taskStatus = testContentTaskDO.getTaskStatus();
        if (taskStatus == STATUS_INIT) {
            vo.setSubmitTime(0);
            vo.setSubmitTimeStr("");
        }

        return vo;
    }

    public List<String> getRandomByStream(List<String> list, int n) {
        Collections.shuffle(list);
        return list.stream().limit(n).collect(Collectors.toList());
    }

    private void checkSignName(String content) {
        String[] contents = content.split("/n");
        Set<String> signNames = Arrays.stream(venusConfig.getTestContentSignName().split(","))
                .collect(Collectors.toSet());
        for (String contentItem : contents) {
            Pattern pattern = Pattern.compile("【(.*?)】");
            Matcher matcher = pattern.matcher(contentItem);
            if (matcher.find()) {
                if (!signNames.contains(matcher.group(1))) {
                    throw new BizException("短信签名不存在");
                }
            }
        }
    }

    private void executeTaskAfterNotify(TestContentTaskDO modelDO, Map<String, List<String>> brandMap) {
        try {
            List<String> modelData = fetchPhoneAndOwnerByBrandConfig(brandMap);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("taskName", modelDO.getName());
            dataMap.put("creator", modelDO.getCreator());
            dataMap.put("executeTaskTime", DateUtil.formatDateTime(modelDO.getUpdateTime()));
            dataMap.put("modelData", String.join("<br>", modelData));
            dataMap.put("status", modelDO.getTaskStatus() == STATUS_SUCCESS ? "成功" : "失败");
            dataMap.put("atUserList", venusConfig.getTestContentTaskNotifier().split(","));
            feiShuAlert.sendFeiShuAlert(dataMap, venusConfig.getTestContentTaskNotifyStrategyId());
        } catch (Exception e) {
            log.error("执行任务后通知异常", e);
        }
    }

    private List<String> fetchPhoneAndOwnerByBrandConfig(Map<String, List<String>> brandMap) {
        List<String> phoneAndOwnerResult = new ArrayList<>();
        brandMap.forEach((key, value) -> {
            for (String phone : value) {
                TestWhiteDO testWhiteDO = testWhiteMapper.selectByMobile(phone);
                if (Objects.nonNull(testWhiteDO)) {
                    phoneAndOwnerResult.add(key + "(" + testWhiteDO.getOwner() + " " + phone + ")");
                }
            }
        });
        return phoneAndOwnerResult;
    }

    public String safeFormat(long timestamp) {
        return timestamp <= 0L ? "" : DateUtil.formatDateTime(new Date(timestamp));
    }

    private List<TestContentTaskVO> composeTestTaskStats(TestContentTaskQuery query) {
        List<TestContentTaskDO> taskList;
        if (StringUtils.isNotBlank(query.getLikeTplCode())) {
            taskList = testContentTaskMapper.selectUnionDataByQuery(query);
        } else {
            taskList = testContentTaskMapper.selectByQuery(query);
        }
        if (CollectionUtils.isEmpty(taskList)) {
            return Collections.emptyList();
        }

        Set<String> taskIds = taskList.stream()
                .map(TestContentTaskDO::getTaskId)
                .collect(Collectors.toSet());

        Map<String, List<TestContentTaskRecordDO>> recordMap = testContentTaskRecordMapper
                .selectByTaskIds(taskIds)
                .stream()
                .collect(Collectors.groupingBy(TestContentTaskRecordDO::getTaskId));

        return taskList.stream()
                .map(taskDO -> {
                    return enrichTaskVOWithStats(taskDO, recordMap);
                })
                .collect(Collectors.toList());
    }

    private TestContentTaskVO enrichTaskVOWithStats(TestContentTaskDO taskDO, Map<String, List<TestContentTaskRecordDO>> recordMap) {

        // 因为运营有时在列表查询，不会到任务详情中
        long nowSecond = System.currentTimeMillis() / 1000L;
        if (taskDO.getSubmitTime() > 0 && (nowSecond - taskDO.getSubmitTime()) < venusConfig.getTestContentTaskCountdown()) {
            this.updateTaskRecordReportStatus(taskDO.getTaskId());
        }

        List<TestContentTaskRecordDO> records = recordMap.getOrDefault(taskDO.getTaskId(), Collections.emptyList());
        TestContentTaskVO vo = buildTestContentTaskVO(taskDO);
        if (!records.isEmpty()) {

            long sendCount = 0, reportCount = 0, appReportCount = 0;
            for (TestContentTaskRecordDO r : records) {
                if (r.getSendStatus() == 0) sendCount++;
                if (r.getReportStatus() == 0) reportCount++;
                if (r.getAppReportStatus() == 1) appReportCount++;
            }

            // 避免回执更新慢
            if (appReportCount > 0 && sendCount == appReportCount) {
                reportCount = appReportCount;
                String finalCompleteTime = safeFormat((taskDO.getSubmitTime() + venusConfig.getTestContentTaskCountdown()) * 1000L);
                vo.setCheckResult(COMPLETE_PERCENT);
                vo.setCompleteTimeStr(finalCompleteTime);
            }

            vo.setReachRate(sendCount > 0 ? CommonUtil.division(reportCount, sendCount) : 0);
            vo.setAppReportRate(reportCount > 0 ? CommonUtil.division(appReportCount, reportCount) : 0);
        }
        return vo;
    }
}
