package com.xhqb.spectre.admin.batchtask.ab;

import com.xhqb.spectre.admin.omini.dingding.DingDingMessageContext;
import com.xhqb.spectre.admin.util.CommonUtil;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 群发任务灰度告警消息
 *
 * <AUTHOR>
 * @date 2021/12/30
 */
public class BatchTaskAbWarningMessage extends DingDingMessageContext {

    /**
     * 群发任务名称
     */
    private String name;
    /**
     * 群发任务批次号
     */
    private Integer id;
    /**
     * 触达率
     */
    private BigDecimal rate;
    /**
     * webHook 默认为1
     */
    private String webHook = "1";
    /**
     * 机器人通知秘钥
     */
    private String secret;

    /**
     * 需要被@的用户手机号数组
     */
    private List<String> atMobile;

    public BatchTaskAbWarningMessage(String strategyId, String dingDingAccount) {
        super(strategyId, dingDingAccount);
    }

    /**
     * 钉钉请求参数
     *
     * @return
     */
    @Override
    protected Map<String, Object> parameters() {
        Map<String, Object> params = new HashMap<>(16);
        params.put("name", this.name);
        params.put("id", this.id + "");
        params.put("webHook", this.webHook);
        params.put("secret", this.secret);

        if (Objects.nonNull(this.rate)) {
            params.put("rate", this.rate.intValue() + "%");
        } else {
            params.put("rate", "0%");
        }

        if (!CommonUtil.isEmpty(atMobile)) {
            params.put("atMobile", atMobile);
        }

        return params;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public String getWebHook() {
        return webHook;
    }

    public void setWebHook(String webHook) {
        this.webHook = webHook;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public List<String> getAtMobile() {
        return atMobile;
    }

    public void setAtMobile(List<String> atMobile) {
        this.atMobile = atMobile;
    }
}
