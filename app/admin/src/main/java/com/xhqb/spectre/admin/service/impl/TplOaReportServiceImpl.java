package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.model.vo.TplOaReportVO;
import com.xhqb.spectre.admin.service.TplOaReportService;

import com.xhqb.spectre.admin.service.oa.OaBizService;
import com.xhqb.spectre.admin.service.oa.vo.FlowDataAllInfo;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.oa.TplApproveContent;
import com.xhqb.spectre.common.dal.entity.oa.TplContent;
import com.xhqb.spectre.common.dal.entity.oa.TplOaApprove;
import com.xhqb.spectre.common.dal.mapper.SignMapper;
import com.xhqb.spectre.common.dal.mapper.TplApproveContentMapper;
import com.xhqb.spectre.common.dal.mapper.TplContentMapper;
import com.xhqb.spectre.common.dal.mapper.TplOaApproveMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.TplOaReportQuery;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TplOaReportServiceImpl implements TplOaReportService {

    private static final Logger log = LoggerFactory.getLogger(TplOaReportServiceImpl.class);
    @Resource
    private TplOaApproveMapper tplOaApproveMapper;

    @Resource
    private TplApproveContentMapper tplApproveContentMapper;

    @Resource
    private TplContentMapper tplContentMapper;

    @Resource
    private OaBizService oaBizService;

    @Resource
    private SignMapper signMapper;


    @Override
    public CommonPager<TplOaReportVO> page(TplOaReportQuery query) {
        List<TplOaApprove> modelList = tplOaApproveMapper.selectByQuery(query);
        int count = tplOaApproveMapper.countByQuery(query);

        List<TplOaReportVO> voList = new ArrayList<>();

        for (TplOaApprove model : modelList) {
            TplOaReportVO vo = new TplOaReportVO();
            BeanUtils.copyProperties(model, vo);
            vo.setApproveStatus(model.getStatus());
            vo.setOriginalContent(JSON.parseArray(model.getOriginalContent(), String.class));
            if (Objects.equals(model.getStatus(), 3)) {
                Set<String> contentSet = getContentSetByFlowIds(Collections.singleton(model.getFlowId()), true);
                vo.setContentSet(contentSet);
            }
            voList.add(vo);
        }

        return PageResultUtils.result(() -> count, () -> voList);
    }

    @Override
    public List<FlowDataAllInfo> approve(String flowId, String userId) {
        return oaBizService.getFlowDataAllInfo(flowId, userId);
    }

    public Set<String> getApprovedAndApprovingContentSet() {
        return getContentSetByFlowIds(getApprovedFlowIdSetByStatus(Arrays.asList(1, 3)), false);
    }


    public Set<String> getApprovedFlowIdSetByStatus(List<Integer> statusList) {
        return tplOaApproveMapper.selectByStatusList(statusList).stream()
                .map(TplOaApprove::getFlowId)
                .collect(Collectors.toSet());
    }

    public Set<String> getContentSetByFlowIds(Set<String> uniqueFlowIds, boolean isSignEnable) {

        // 如果没有已批准的FlowId，则直接返回空集合
        if (uniqueFlowIds.isEmpty()) {
            return Collections.emptySet();
        }

        // 查询对应FlowId的ContentId集合
        Map<String, String> contentIdMap = tplApproveContentMapper.selectByFlowIdList(new ArrayList<>(uniqueFlowIds))
                .stream().collect(Collectors.toMap(TplApproveContent::getContentId, TplApproveContent::getSignId));


        // 如果没有对应的ContentId，则直接返回空集合
        if (contentIdMap.isEmpty()) {
            return Collections.emptySet();
        }

        Set<String> contendIdSet = contentIdMap.keySet();
        // 根据ContentId集合查询内容并返回
        List<TplContent> tplContents = tplContentMapper.selectByContentIdList(new ArrayList<>(contendIdSet));

        if (isSignEnable) {
            List<SignDO> signDOList = signMapper.selectEnum(1);
            Map<String, String> signMap = signDOList.stream().collect(Collectors.toMap(s -> s.getId() + "", SignDO::getName));
            for (TplContent tplContent : tplContents) {
                String signId = contentIdMap.get(tplContent.getContentId());
                String signName = signMap.get(signId);
                String content = tplContent.getApprovedContent();
                if (ObjectUtils.isNotEmpty(signName)) {
                    content = signName + content;
                }
                tplContent.setContent(content);
            }
        }
        return tplContents.stream()
                .map(TplContent::getContent)
                .collect(Collectors.toSet());

    }
}
