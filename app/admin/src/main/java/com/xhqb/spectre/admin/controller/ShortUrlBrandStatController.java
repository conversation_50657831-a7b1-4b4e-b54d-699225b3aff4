package com.xhqb.spectre.admin.controller;

import cn.hutool.core.date.DateUtil;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.ShortUrlBrandStatPageVO;
import com.xhqb.spectre.admin.model.vo.ShortUrlBrandStatProportionVO;
import com.xhqb.spectre.admin.model.vo.ShortUrlBrandStatTrendChartVO;
import com.xhqb.spectre.admin.service.ShortUrlBrandStatService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ShortUrlBrandStatQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/shortUrlBrandStat")
@Slf4j
public class ShortUrlBrandStatController {

    @Resource
    private ShortUrlBrandStatService shortUrlBrandStatService;

    /**
     * 分页查询短链接品牌统计信息
     * 该函数接收查询条件、页码和每页大小作为参数，返回分页后的短链接品牌统计结果。
     *
     * @param shortUrlBrandStatQuery 短链接品牌统计查询条件对象，包含查询条件
     * @param pageNum                当前页码，表示要查询的页数
     * @param pageSize               每页大小，表示每页显示的记录数
     * @return CommonResult<CommonPager < ShortUrlBrandStatPageVO>> 返回分页查询结果，包含分页信息和短链接品牌统计数据的列表
     */
    @GetMapping("/page")
    public CommonResult<CommonPager<ShortUrlBrandStatPageVO>> page(@ModelAttribute ShortUrlBrandStatQuery shortUrlBrandStatQuery, Integer pageNum, Integer pageSize) {
        shortUrlBrandStatQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        log.info("查询短链接品牌统计请求参数 shortUrlBrandStatQuery:{}", JsonLogUtil.toJSONString(shortUrlBrandStatQuery));
        return CommonResult.success(shortUrlBrandStatService.page(shortUrlBrandStatQuery));
    }

    /**
     * 根据给定的查询条件，获取短链接的品牌占比信息。
     *
     * @param shortUrlBrandStatQuery 包含查询条件的对象，用于过滤和获取品牌占比数据。
     * @return CommonResult<ShortUrlBrandStatProportionVO> 包含品牌占比信息
     */
    @Deprecated
    @GetMapping("/brandProportion")
    public CommonResult<ShortUrlBrandStatProportionVO> brandProportion(@ModelAttribute ShortUrlBrandStatQuery shortUrlBrandStatQuery) {
        log.info("查询短链接品牌占比请求参数 shortUrlBrandStatQuery:{}", JsonLogUtil.toJSONString(shortUrlBrandStatQuery));
        return CommonResult.success(shortUrlBrandStatService.brandProportion(shortUrlBrandStatQuery));
    }


    /**
     * 根据给定的查询条件，获取短链接品牌趋势图信息。
     *
     * @param shortUrlBrandStatQuery 包含查询条件的对象，用于过滤和获取品牌趋势图数据。
     * @return CommonResult<ShortUrlBrandStatTrendChartVO> 包含品牌趋势图信息
     */
    @Deprecated
    @GetMapping("/trendChart")
    public CommonResult<ShortUrlBrandStatTrendChartVO> trendChart(@ModelAttribute ShortUrlBrandStatQuery shortUrlBrandStatQuery) {
        log.info("查询短链接品牌双轴趋势图请求参数 shortUrlBrandStatQuery:{}", JsonLogUtil.toJSONString(shortUrlBrandStatQuery));
        return CommonResult.success(shortUrlBrandStatService.trendChart(shortUrlBrandStatQuery));
    }


    @GetMapping("/runJob")
    public CommonResult<String> runJob(String curDateStr) {
        log.info("执行短链接品牌统计任务");
        if (StringUtils.isBlank(curDateStr)) {
            return CommonResult.make(500, "执行短链接品牌统计任务失败，请检查参数");
        }
        return CommonResult.success(shortUrlBrandStatService.runJob(DateUtil.parseDateTime(curDateStr)));
    }

    /**
     * 查询短链pv占比
     * @param shortUrlBrandStatQuery 查询条件
     * @return pv占比
     */
    @GetMapping("/pvProportion")
    public CommonResult<ShortUrlBrandStatProportionVO> pvProportion(@ModelAttribute ShortUrlBrandStatQuery shortUrlBrandStatQuery) {
        log.info("查询短链pv占比请求参数 shortUrlBrandStatQuery:{}", JsonLogUtil.toJSONString(shortUrlBrandStatQuery));
        return CommonResult.success(shortUrlBrandStatService.pvProportion(shortUrlBrandStatQuery));
    }


    /**
     * 查询短链uv占比
     * @param shortUrlBrandStatQuery 查询条件
     * @return uv占比
     */
    @GetMapping("/uvProportion")
    public CommonResult<ShortUrlBrandStatProportionVO> uvProportion(@ModelAttribute ShortUrlBrandStatQuery shortUrlBrandStatQuery) {
        log.info("查询短链uv占比请求参数 shortUrlBrandStatQuery:{}", JsonLogUtil.toJSONString(shortUrlBrandStatQuery));
        return CommonResult.success(shortUrlBrandStatService.uvProportion(shortUrlBrandStatQuery));
    }

}
