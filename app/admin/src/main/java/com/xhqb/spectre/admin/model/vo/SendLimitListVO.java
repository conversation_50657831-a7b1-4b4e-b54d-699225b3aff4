package com.xhqb.spectre.admin.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 10:46
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SendLimitListVO implements Serializable {

    private static final long serialVersionUID = 169832073889838854L;

    /**
     * 同一手机号30秒内最大接收短信条数
     */
    private Integer mobileMaxCountHalfMinute;

    /**
     * 同一手机号1小时内最大接收短信条数
     */
    private Integer mobileMaxCountHour;

    /**
     * 同一手机号1天内最大接收短信条数
     */
    private Integer mobileMaxCountDay;

    /**
     * 同一手机号1天内最大接收相同短信条数
     */
    private Integer mobileSameContentMaxCycle;

    /**
     * 同一手机号1天内最大接收验证码短信条数
     */
    private Integer mobileMaxCountVerify;
}
