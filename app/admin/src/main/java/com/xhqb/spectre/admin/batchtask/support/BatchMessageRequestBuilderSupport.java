package com.xhqb.spectre.admin.batchtask.support;

import com.xhqb.spectre.admin.batchtask.MessageSendFactory;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryContext;
import com.xhqb.spectre.admin.batchtask.send.single.SingleMessageRequest;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 批量发送消息构建扩展至支持
 *
 * <AUTHOR>
 * @date 2022/2/21
 */
@Component
public class BatchMessageRequestBuilderSupport {


    /**
     * 占位符构建构建批量参数请求
     *
     * @param context
     * @param sendFactory
     * @return
     */
    public List<SingleMessageRequest> placeholderBuilder(SingleFactoryContext context, MessageSendFactory sendFactory) {
        // 占位符映射列表
        List<? extends Map<String, String>> placeHolderList = context.getPlaceHolderList();
        List<String> mobileList = context.getMobileList();
        List<SingleMessageRequest> requestList = new ArrayList<>();
        String appSecret = sendFactory.getAppSecret(context.getAppCode());

        SingleMessageRequest request = new SingleMessageRequest();
        //业务应用编码
        request.setAppCode(context.getAppCode());
        // 加签秘钥
        request.setAppSecret(appSecret);
        // tplCode 模板编号
        request.setTplCode(context.getTplCode());
        // 签名名称
        request.setSignName(context.getSignName());
        // 手机号码
        request.setPhoneNumbers(String.join(",", mobileList));
        // 短信类型
        request.setSmsCodeType(context.getSmsCodeType());
        // 渠道关联账号的ID(用于测试短信时会传入该ID)
        request.setChannelAccountId(context.getChannelAccountId());
        // 设置批次号
        request.setBatchId(context.getBatchId());
        // 设置签名编码
        request.setSignCode(context.getSignCode());
        // 设置requestId 2021-12-14
        request.setRequestId(context.getRequestId());

        // 占位符支持
        request.setParamList(placeHolderList);

        // 设置参数对象为空
        request.setParamMap(null);

        requestList.add(request);

        return requestList;
    }
}
