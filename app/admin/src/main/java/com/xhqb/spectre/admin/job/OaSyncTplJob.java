package com.xhqb.spectre.admin.job;

import cn.hutool.core.date.DateUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.service.oa.OaReportService;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.entity.oa.TplContent;
import com.xhqb.spectre.common.dal.mapper.TplContentMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 模板同步 job
 * elastic-job.jobs.oaSyncTplJob.cron=0 0/15 * * * ?
 * elastic-job.jobs.oaSyncTplJob.sharding-total-count=1
 * elastic-job.jobs.oaSyncTplJob.sharding-item-parameters=0=A
 * elastic-job.jobs.oaSyncTplJob.disabled=true
 * elastic-job.jobs.oaSyncTplJob.description=模板同步 job
 *
 * <AUTHOR>
 */

@Component
@Job("oaSyncTplJob")
@Slf4j
public class OaSyncTplJob implements SimpleJob {


    @Resource
    private OaReportService oaReportService;
    @Resource
    private TplContentMapper tplContentMapper;
    @Resource
    private TplMapper tplMapper;

    @Resource
    private VenusConfig venusConfig;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("开始同步模板");
        long startTime = System.currentTimeMillis();
        // 查询时间
        Date curDate = new Date();
        Date queryStartDate = DateUtil.offsetDay(curDate, -venusConfig.getQueryDay());
        Date startDate = DateUtil.beginOfDay(queryStartDate);
        Date endDate = DateUtil.endOfDay(curDate);
        log.info("查询时间:{}~{}", startDate, endDate);
        List<TplContent> tplContentList = tplContentMapper.selectByStatus(3, startDate, endDate);
        if (CollectionUtils.isEmpty(tplContentList)) {
            return;
        }


        for (TplContent tplContent : tplContentList) {
            try {
                TplDO tplDO = tplMapper.selectByReportId(tplContent.getContentId());
                if (Objects.nonNull(tplDO)) {
                    log.info("该模版已报备,contentId:{}", tplContent.getContentId());
                    continue;
                }
                oaReportService.syncTpl(tplContent.getContentId());
            } catch (Exception e) {
                log.error("同步模板失败,contentId:{}", tplContent.getContentId());
            }
        }
        log.info("结束同步模板,耗时:{}", System.currentTimeMillis() - startTime);
    }
}
