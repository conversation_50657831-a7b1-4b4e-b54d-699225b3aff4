package com.xhqb.spectre.admin.job;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.google.common.collect.Lists;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.readonly.mapper.SmsOrderReadonlyMapper;
import com.xhqb.spectre.admin.readonly.mapper.SmsReceiptReadonlyMapper;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.entity.test.TestTaskDO;
import com.xhqb.spectre.common.dal.entity.test.TestTaskLogDO;
import com.xhqb.spectre.common.dal.mapper.TestTaskLogMapper;
import com.xhqb.spectre.common.dal.mapper.TestTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 测试任务记录回执更新
 */
@Component
@Job("testTaskRecordReceiptUpdateJob")
@Slf4j
public class TestTaskRecordReceiptUpdateJob implements SimpleJob {


    @Resource
    private VenusConfig venusConfig;

    @Resource
    private TestTaskMapper testTaskMapper;

    @Resource
    private TestTaskLogMapper testTaskLogMapper;

    @Resource
    private SmsOrderReadonlyMapper smsOrderReadonlyMapper;

    @Override
    public void execute(ShardingContext shardingContext) {
        long start = System.currentTimeMillis();
        try {
            log.info("testTaskRecordReceiptUpdateJob start:{}", start);
            // 设计数据格式 key ： taskId value ： List<String> mobile
            Date currentDate = new Date();
            Date offsetDate = DateUtil.offsetDay(currentDate, -venusConfig.getChannelSmsUpdateRecordTimeDay());
            Date startDate = DateUtil.beginOfDay(offsetDate);
            // 获取需要更新回执的任务 startDate - currentDate
            List<TestTaskDO> taskDOList = testTaskMapper.selectByTime(startDate, currentDate);
            if (CollectionUtils.isEmpty(taskDOList)) {
                log.info("taskDOList is empty 没有需要更新回执的任务");
                return;
            }
            List<String> taskIdList = taskDOList.stream().map(TestTaskDO::getTaskId).collect(Collectors.toList());
            List<TestTaskLogDO> taskLogDOList = testTaskLogMapper.selectByTaskIdListAndCallStatus(taskIdList, -1);
            if (CollectionUtils.isEmpty(taskLogDOList)) {
                log.info("taskLogDOList is empty 没有需要更新回执的任务");
                return;
            }
            Map<String, List<TestTaskLogDO>> initialDataMap = taskLogDOList.stream()
                    .collect(Collectors.groupingBy(TestTaskLogDO::getTaskId));

            List<TestTaskLogDO> updateList = new ArrayList<>();
            for (Map.Entry<String, List<TestTaskLogDO>> entry : initialDataMap.entrySet()) {
                String taskId = entry.getKey();
                List<String> mobileList = entry.getValue().stream()
                        .map(TestTaskLogDO::getMobile)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(mobileList)) {
                    log.info("当前taskId:{} |没有需要更新回执", taskId);
                    continue;
                }
                long startTimestamp = startDate.getTime() / 1000;
                long endTimestamp = currentDate.getTime() / 1000;
                List<SmsOrderDO> smsOrderDOList = smsOrderReadonlyMapper.selectByRequestIdAndMobileList(taskId, mobileList, startTimestamp, endTimestamp);
                if (CollectionUtils.isEmpty(smsOrderDOList)) {
                    log.info("当前taskId:{} |smsOrderDOList is empty", taskId);
                    continue;
                }
                for (SmsOrderDO smsOrderDO : smsOrderDOList) {
                    TestTaskLogDO testTaskLogDO = new TestTaskLogDO();
                    testTaskLogDO.setTaskId(smsOrderDO.getRequestId());
                    testTaskLogDO.setMobile(smsOrderDO.getMobile());
                    testTaskLogDO.setCallStatus(formatStatus(smsOrderDO.getReportStatus()));
                    testTaskLogDO.setCallReason(smsOrderDO.getReportDesc());
                    updateList.add(testTaskLogDO);
                }
            }
            updateList.forEach(testTaskLogMapper::updateByTaskIdAndMobile);
            log.info("testTaskRecordReceiptUpdateJob end:{}", System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.info("testTaskRecordReceiptUpdateJob error", e);
        }
    }


    /**
     * 格式化状态值，将失败状态统一为1，传给前端页面
     *
     * @param status
     * @return
     */
    private static int formatStatus(Integer status) {
        return status > 0 ? 1 : status;
    }

}
