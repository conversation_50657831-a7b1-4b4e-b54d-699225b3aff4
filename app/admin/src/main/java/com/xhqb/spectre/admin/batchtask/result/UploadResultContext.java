package com.xhqb.spectre.admin.batchtask.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 上传结果处理上下文信息
 *
 * <AUTHOR>
 * @date 2021/10/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UploadResultContext<T> implements Serializable {

    /**
     * 查询的任务编号
     */
    private String taskNo;

    /**
     * 数据类型
     */
    private T data;

}
