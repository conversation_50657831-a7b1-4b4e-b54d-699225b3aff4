package com.xhqb.spectre.admin.openapi.auth;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.openapi.constants.OpenApiConstants;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

/**
 * 请求头对象
 *
 * <AUTHOR>
 * @date 2021/12/16
 */
public class RequestHeader implements Serializable {
    /**
     * appKey
     */
    private String appKey;
    /**
     * 时间搓 (单位秒)
     */
    private String timestamp;
    /**
     * 随机数
     */
    private String nonce;
    /**
     * 签名
     */
    private String sign;
    /**
     * 请求头
     */
    private Map<String, String> headers;

    public RequestHeader(Map<String, String> headers) {
        if (Objects.isNull(headers)) {
            throw new IllegalArgumentException("headers不能够为空");
        }
        this.headers = headers;
        this.appKey = headers.get(OpenApiConstants.Header.APP_KEY);
        this.timestamp = headers.get(OpenApiConstants.Header.TIMESTAMP);
        this.nonce = headers.get(OpenApiConstants.Header.NONCE);
        this.sign = headers.get(OpenApiConstants.Header.SIGN);
    }

    public String getAppKey() {
        return appKey;
    }


    public String getTimestamp() {
        return timestamp;
    }

    public String getNonce() {
        return nonce;
    }

    public String getSign() {
        return sign;
    }

    /**
     * 通过key获取到header值
     *
     * @param key
     * @return
     */
    public String getHeader(String key) {
        return this.headers.get(key);
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
