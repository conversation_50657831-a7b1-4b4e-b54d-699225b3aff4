package com.xhqb.spectre.admin.statistics.mapper;

import com.xhqb.spectre.admin.statistics.entity.SmsStatisByHourDO;
import com.xhqb.spectre.admin.statistics.entity.SmsStatisByHourKey;
import com.xhqb.spectre.admin.statistics.entity.SmsStatisSumDO;
import com.xhqb.spectre.admin.statistics.query.SmsStatisQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SmsStatisByHourMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_class_statis_byhour
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(SmsStatisByHourKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_class_statis_byhour
     *
     * @mbggenerated
     */
    int insert(SmsStatisByHourDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_class_statis_byhour
     *
     * @mbggenerated
     */
    int insertSelective(SmsStatisByHourDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_class_statis_byhour
     *
     * @mbggenerated
     */
    SmsStatisByHourDO selectByPrimaryKey(SmsStatisByHourKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_class_statis_byhour
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SmsStatisByHourDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_class_statis_byhour
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SmsStatisByHourDO record);

    List<SmsStatisSumDO> selectSumList(SmsStatisQuery query);

    List<SmsStatisSumDO> selectSumListByPage(SmsStatisQuery query);

    Integer selectSumListCount(SmsStatisQuery query);

    List<SmsStatisSumDO> selectClassSum(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("columnName") String columnName);

    List<SmsStatisSumDO> selectClassSumList(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("columnName") String columnName);

}