package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.batchtask.cid.CidStrategyFactory;
import com.xhqb.spectre.admin.cif.entity.CifEnterpriseCustomerDO;
import com.xhqb.spectre.admin.cif.mapper.CifEnterpriseCustomerMapper;
import com.xhqb.spectre.admin.model.vo.batchtask.CustomerVO;
import com.xhqb.spectre.admin.service.CifEnterpriseCustomerService;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * cifdb.t_enterprise_customer_base 处理
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Component
@Slf4j
public class CifEnterpriseCustomerServiceImpl implements CifEnterpriseCustomerService {

    @Resource
    private CifEnterpriseCustomerMapper cifEnterpriseCustomerMapper;
    @Resource
    private CidStrategyFactory cidStrategyFactory;

    /**
     * 根据cid获取用户信息
     *
     * @param id
     * @return
     */
    @Override
    public CifEnterpriseCustomerDO getById(String id) {
        return cifEnterpriseCustomerMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据cid列表查询用户信息
     *
     * @param cidList
     * @param smsTypeCode
     * @return
     */
    @Override
    public List<CifEnterpriseCustomerDO> queryByCidList(List<String> cidList, String smsTypeCode) {
        List<String> userStatusStrategy = cidStrategyFactory.getUserStatusStrategy(smsTypeCode);
        return cifEnterpriseCustomerMapper.selectByCidList(cidList, userStatusStrategy);
    }

    /**
     * 查询结果返回map
     *
     * @param cidList
     * @param smsTypeCode
     * @return
     */
    @Override
    public Map<String, CustomerVO> mapByCidList(List<String> cidList, String smsTypeCode) {
        List<String> userStatusStrategy = cidStrategyFactory.getUserStatusStrategy(smsTypeCode);
        // 根据cid查询用户数据信息
        List<CifEnterpriseCustomerDO> cifEnterpriseCustomerList = cifEnterpriseCustomerMapper.selectByCidList(cidList, userStatusStrategy);
        if (CommonUtil.isEmpty(cifEnterpriseCustomerList)) {
            log.warn("未查询到enterpriseCustomer任何有效用户信息,userStatusStrategy = {},cidList = {}", userStatusStrategy, cidList);
            return null;
        }

        // key -> cid  value -> CustomerVO
        Map<String, CustomerVO> resultMap = new HashMap<>((int) (cifEnterpriseCustomerList.size() / 0.75));
        String mobilePhone;
        CustomerVO customerVO;
        for (CifEnterpriseCustomerDO item : cifEnterpriseCustomerList) {
            mobilePhone = item.getMobilePhone();
            if (StringUtils.isBlank(mobilePhone)) {
                log.info("cid有效，但是手机号码为空,过滤掉该客户信息(t_enterprise_customer_base) = {}", item);
                continue;
            }

            customerVO = CustomerVO.enterpriseCustomer(item);
            resultMap.put(item.getId(), customerVO);
        }
        return resultMap;
    }

    /**
     * 根据手机号查询用户列表
     *
     * @param mobileList 手机号码列表
     * @return
     */
    @Override
    public List<CifEnterpriseCustomerDO> selectByMobileList(Collection<String> mobileList) {
        return cifEnterpriseCustomerMapper.selectByMobileList(mobileList);
    }
}
