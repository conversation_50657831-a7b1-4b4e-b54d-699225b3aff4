package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.AppSendLimitDO;
import com.xhqb.spectre.common.enums.AppSendLimitEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 10:47
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class AppSendLimitVO implements Serializable {

    private static final long serialVersionUID = 4620265672436587257L;

    /**
     * 应用编码
     */
    private  String appCode;

    /**
     * 限流配置列表
     */
    private SendLimitListVO limitList;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新人
     */
    private String updater;

    public static AppSendLimitVO buildAppSendLimitVO(List<AppSendLimitDO> appSendLimitDOList) {
        if (CollectionUtils.isEmpty(appSendLimitDOList)) {
            return null;
        }
        //构造限流配置数据
        SendLimitListVO limitList = new SendLimitListVO();
        Class<? extends SendLimitListVO> clazz = limitList.getClass();
        appSendLimitDOList.forEach(item -> {
            String limitKey = item.getSendLimitKey();
            Integer value = Integer.valueOf(item.getSendLimitValue());
            if (AppSendLimitEnum.contains(limitKey)) {
                try {
                    Field field = clazz.getDeclaredField(limitKey);
                    field.setAccessible(true);
                    field.set(limitList, value);
                } catch (Exception e) {
                    log.warn("buildAppSendLimitVO Exception", e);
                }
            }
        });

        AppSendLimitDO firstItem = appSendLimitDOList.get(0);
        return AppSendLimitVO.builder()
                .appCode(firstItem.getAppCode())
                .limitList(limitList)
                .status(firstItem.getStatus())
                .createTime(DateUtil.dateToString(firstItem.getCreateTime()))
                .creator(firstItem.getCreator())
                .updateTime(DateUtil.dateToString(firstItem.getUpdateTime()))
                .updater(firstItem.getUpdater())
                .build();
    }
}
