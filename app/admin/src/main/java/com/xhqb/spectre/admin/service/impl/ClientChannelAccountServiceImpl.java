package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.constant.Apis;
import com.xhqb.spectre.admin.enums.RespCodeEnum;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.ClientChannelAccountDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ClientChannelAccountVO;
import com.xhqb.spectre.admin.service.ClientChannelAccountService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.ClientChannelAccountDO;
import com.xhqb.spectre.common.dal.mapper.ClientChannelAccountMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ClientChannelAccountQuery;
import com.xhqb.spectre.common.enums.DeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 网关分配账号
 *
 * <AUTHOR>
 * @date 2021/10/26
 */
@Service
@Slf4j
public class ClientChannelAccountServiceImpl implements ClientChannelAccountService {

    @Resource
    private ClientChannelAccountMapper clientChannelAccountMapper;
    @Resource
    private RestTemplate restTemplate;
    @Autowired
    private VenusConfig venusConfig;

    /**
     * 分页查询网关分配账号列表
     *
     * @param clientChannelAccountQuery
     * @return
     */
    @Override
    public CommonPager<ClientChannelAccountVO> listByPage(ClientChannelAccountQuery clientChannelAccountQuery) {
        return PageResultUtils.result(
                () -> clientChannelAccountMapper.countByQuery(clientChannelAccountQuery),
                () -> clientChannelAccountMapper.selectByQuery(clientChannelAccountQuery).stream().map(ClientChannelAccountVO::buildListQuery).collect(Collectors.toList())
        );
    }

    /**
     * 根据ID查询网关账号详情
     *
     * @param id
     * @return
     */
    @Override
    public ClientChannelAccountVO getById(Integer id) {
        ClientChannelAccountDO clientChannelAccountDO = selectById(id);
        return ClientChannelAccountVO.buildInfoQuery(clientChannelAccountDO);
    }

    /**
     * 添加网关账号信息
     *
     * @param clientChannelAccountDTO
     */
    @Override
    public void create(ClientChannelAccountDTO clientChannelAccountDTO) {
        ValidatorUtil.validate(clientChannelAccountDTO);
        ClientChannelAccountDO clientChannelAccountDO = ClientChannelAccountDTO.toDO(clientChannelAccountDTO);
        String updater = clientChannelAccountDO.getUpdater();
        clientChannelAccountDO.setCreator(updater);
        clientChannelAccountMapper.insertSelective(clientChannelAccountDO);
    }

    /**
     * 更新网关账号信息
     *
     * @param id
     * @param clientChannelAccountDTO
     */
    @Override
    public void update(Integer id, ClientChannelAccountDTO clientChannelAccountDTO) {
        selectById(id);
        ClientChannelAccountDO clientChannelAccountDO = ClientChannelAccountDTO.toDO(clientChannelAccountDTO);
        clientChannelAccountDO.setId(id);
        clientChannelAccountMapper.updateByPrimaryKeySelective(clientChannelAccountDO);
    }

    /**
     * 删除网关账号信息
     *
     * @param id
     * @return
     */
    @Override
    public AdminResult delete(Integer id) {
        ClientChannelAccountDO clientChannelAccountDO = selectById(id);

        long start = System.currentTimeMillis();
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        // 请勿轻易改变此提交方式，大部分的情况下，提交方式都是表单提交
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        HttpEntity<String> requestEntity = new HttpEntity<>("{}", headers);
        String deleteApi = venusConfig.getCmppServerHost() + Apis.CmppServerApi.OFFLINE_API + "?username=" + clientChannelAccountDO.getUsername();
        AdminResult adminResult;
        try {
            ResponseEntity<AdminResult> response = restTemplate.exchange(deleteApi, HttpMethod.DELETE, requestEntity, AdminResult.class);
            log.info("cmppserver账号删除响应耗时 = {}, response = {}, clientChannelAccountDO = {},deleteApi = {}", (System.currentTimeMillis() - start), response, JSON.toJSONString(clientChannelAccountDO), deleteApi);
            adminResult = response.getBody();

            if (Objects.equals(RespCodeEnum.SUCCESS.getCode(), adminResult.getCode())) {
                // 调用cmppserver 接口成功之后
                // 再进行数据删除操作
                ClientChannelAccountDO record = new ClientChannelAccountDO();
                record.setId(id);
                int deletedCode = DeleteEnum.DELETED.getCode();
                record.setIsDelete((byte) deletedCode);
                record.setUpdater(SsoUserInfoUtil.getUserName());
                clientChannelAccountMapper.updateByPrimaryKeySelective(record);
            } else {
                log.info("调用cmppserver 接口失败,不进行数据删除操作, id = {}", id);
            }
        } catch (Exception e) {
            log.error("cmppserver账号删除失败, clientChannelAccountDO = {}, deleteApi = {}", JSON.toJSONString(clientChannelAccountDO), deleteApi, e);
            adminResult = AdminResult.error("账号删除失败");
        }
        return adminResult;
    }

    /**
     * cmppserver 上线操作
     *
     * @param id
     * @return
     */
    @Override
    public AdminResult online(Integer id) {
        ClientChannelAccountDO clientChannelAccountDO = selectById(id);
        Byte isValid = clientChannelAccountDO.getIsValid();
        if (!Objects.equals(isValid, 1)) {
            throw new BizException("账号无效,不能够进行上线操作");
        }
        Map<Object, Object> body = toCmppServerRequest(clientChannelAccountDO);
        long start = System.currentTimeMillis();
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        // 请勿轻易改变此提交方式，大部分的情况下，提交方式都是表单提交
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(body), headers);
        String onlineApi = venusConfig.getCmppServerHost() + Apis.CmppServerApi.ONLINE_API;
        AdminResult adminResult;
        try {
            ResponseEntity<AdminResult> response = restTemplate.exchange(onlineApi, HttpMethod.POST, requestEntity, AdminResult.class);
            log.info("cmppserver账号上线响应耗时 = {}, response = {}, clientChannelAccountDO = {},onlineApi = {}", (System.currentTimeMillis() - start), response, JSON.toJSONString(clientChannelAccountDO), onlineApi);
            adminResult = response.getBody();
        } catch (Exception e) {
            log.error("cmppserver账号上线失败, clientChannelAccountDO = {}, onlineApi = {}", JSON.toJSONString(clientChannelAccountDO), onlineApi, e);
            adminResult = AdminResult.error("账号上线失败");
        }
        return adminResult;
    }

    /**
     * cmppserver 下线操作
     *
     * @param id
     * @return
     */
    @Override
    public AdminResult offline(Integer id) {
        ClientChannelAccountDO clientChannelAccountDO = selectById(id);
        Map<Object, Object> body = toCmppServerRequest(clientChannelAccountDO);
        long start = System.currentTimeMillis();
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        // 请勿轻易改变此提交方式，大部分的情况下，提交方式都是表单提交
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(body), headers);
        String offlineApi = venusConfig.getCmppServerHost() + Apis.CmppServerApi.OFFLINE_API;
        AdminResult adminResult;
        try {
            ResponseEntity<AdminResult> response = restTemplate.exchange(offlineApi, HttpMethod.POST, requestEntity, AdminResult.class);
            log.info("cmppserver账号下线响应耗时 = {}, response = {}, clientChannelAccountDO = {},onlineApi = {}", (System.currentTimeMillis() - start), response, JSON.toJSONString(clientChannelAccountDO), offlineApi);
            adminResult = response.getBody();
        } catch (Exception e) {
            log.error("cmppserver账号下线失败, clientChannelAccountDO = {}, offlineApi = {}", JSON.toJSONString(clientChannelAccountDO), offlineApi, e);
            adminResult = AdminResult.error("账号下线失败");
        }
        return adminResult;
    }


    /**
     * 根据ID查询网关账号信息
     *
     * @param id
     * @return
     */
    private ClientChannelAccountDO selectById(Integer id) {
        ClientChannelAccountDO clientChannelAccountDO = clientChannelAccountMapper.selectByPrimaryKey(id);
        if (Objects.isNull(clientChannelAccountDO)) {
            throw new BizException("未找到网关账号信息");
        }
        return clientChannelAccountDO;
    }

    /**
     * 转换成cmppserver请求对象
     *
     * @param clientChannelAccountDO
     * @return
     */
    private Map<Object, Object> toCmppServerRequest(ClientChannelAccountDO clientChannelAccountDO) {
        // 请求参数
        // 不确定该接口是否会调整
        // 所以直接定义map吧
        // {"groupName":"test10","isValid":true,"maxConnect":5,"password":"ICP002","readLimit":0,"serviceId":"child2","userName":"901722","version":32,"writeLimit":0}
        return ImmutableMap.builder()
                .put("groupName", clientChannelAccountDO.getGroupName())
                .put("isValid", clientChannelAccountDO.getIsValid())
                .put("maxConnect", clientChannelAccountDO.getMaxConnect())
                .put("password", clientChannelAccountDO.getPassword())
                .put("readLimit", clientChannelAccountDO.getReadLimit())
                .put("serviceId", clientChannelAccountDO.getServiceId())
                .put("userName", clientChannelAccountDO.getUsername())
                .put("version", clientChannelAccountDO.getVersion())
                .put("writeLimit", clientChannelAccountDO.getWriteLimit())
                .build();
    }
}
