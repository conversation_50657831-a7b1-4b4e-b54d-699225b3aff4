package com.xhqb.spectre.admin.batchtask.ab;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.service.BatchTaskService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.BatchTaskDO;
import com.xhqb.spectre.common.dal.mapper.BatchTaskMapper;
import com.xhqb.spectre.common.enums.BatchTaskStatusEnum;
import com.xhqb.spectre.common.enums.DeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 群发短信强制提交
 *
 * <AUTHOR>
 * @date 2021/12/29
 */
@Component
@Slf4j
public class BatchTaskForceSubmitHandler {

    @Resource
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private BatchTaskService batchTaskService;

    /**
     * 群发短信强制提交
     * <p>
     * 检测群发任务是否处于灰度中，若处于灰度中 则不能够进行强制提交
     *
     * @param taskId
     * @return
     */
    public AdminResult abForceSubmit(Integer taskId) {
        log.info("群发短信强制提交,taskId ={},user ={}", taskId, SsoUserInfoUtil.getUserName());
        BatchTaskDO batchTaskDO = batchTaskMapper.selectByPrimaryKey(taskId);
        // 群发任务状态检测
        AdminResult checkResult = this.checkTaskStatus(batchTaskDO, taskId);
        if (Objects.nonNull(checkResult)) {
            // 检测不通过
            return checkResult;
        }
        batchTaskService.submit(taskId);
        log.info("群发短信强制提交成功, taskId = {}", taskId);
        return AdminResult.success();
    }

    /**
     * 检测群发任务状态是否合法(群发任务状态只能为灰度结束才能够进行强制提交)
     *
     * @param batchTaskDO
     * @param taskId
     * @return 返回空表示验证通过
     */
    private AdminResult checkTaskStatus(BatchTaskDO batchTaskDO, Integer taskId) {
        if (Objects.isNull(batchTaskDO)) {
            log.warn("群发短信强制提交处理,群发任务不存在,taskId = {}", taskId);
            return AdminResult.error("群发任务不存在");
        }

        Integer deleteStatus = batchTaskDO.getIsDelete();
        if (Objects.equals(DeleteEnum.DELETED.getCode(), deleteStatus)) {
            log.warn("群发短信强制提交处理,群发任务已被删除,batchTaskDO = {}", JSON.toJSONString(batchTaskDO));
            return AdminResult.error("群发任务已被删除");
        }

        // 状态 0：待提交；1：已提交；2：发送中；3：已发送；4：灰度中；5：灰度结束；9：已取消
        Integer status = batchTaskDO.getStatus();
        // 不是灰度结束状态 不能够进行强制提交
        if (!Objects.equals(status, BatchTaskStatusEnum.AB_WARNING.getCode())) {
            log.warn("群发短信强制提交处理,群发任务状态不合法,status = {},taskId = {}", status, taskId);
            return AdminResult.error("群发任务状态不合法");
        }

        // 检测通过
        return null;
    }
}
