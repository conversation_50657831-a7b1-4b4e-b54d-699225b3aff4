package com.xhqb.spectre.admin.batchtask.send.single;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xhqb.spectre.admin.batchtask.send.MessageRequest;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 单条消息发送请求对象
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SingleMessageRequest extends MessageRequest {
    /**
     * 模板参数
     * key为手机号 value为参数，多个参数使用逗号分割
     */
    private Map<String, String> paramMap = Maps.newHashMap();

    /**
     * 短信类型[通过查看api实现逻辑，短信类型为市场营销时必须填写，其他类型不需要设置值]
     * <p>
     * 参考 SMSMessageManagerServiceImpl.checkTplData
     */
    private String smsCodeType;



    /**
     *
     * 用来兼容模版内容中带${}的，如果paramMap有值就认为模版中带[]，paramList有值就认为是${}模版
     *
     * 模板参数
     * map中必须包含phone的key参数，最多支持100个手机号
     */
    private List<? extends Map<String, String>> paramList;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    /**
     * 没有参数
     *
     * @param mobile
     */
    public void put(String mobile) {
        this.paramMap.put(mobileTrim(mobile), "");
    }

    /**
     * 放入参数
     *
     * @param mobile
     * @param paramList
     */
    public void put(String mobile, List<String> paramList) {
        this.paramMap.put(mobileTrim(mobile), String.join(",", paramList));
    }

    /**
     * 放入参数
     *
     * @param mobileGroup
     * @param paramList
     */
    public void put(List<String> mobileGroup, List<String> paramList) {
        if (CommonUtil.isEmpty(mobileGroup)) {
            return;
        }
        String param = String.join(",", paramList);
        mobileGroup.forEach(mobile -> paramMap.put(mobileTrim(mobile), param));
    }

    /**
     * 分组放入参数
     *
     * @param mobileGroup
     * @param paramGroup
     */
    public void putGroup(List<String> mobileGroup, List<List<String>> paramGroup) {
        if (CommonUtil.isEmpty(mobileGroup)) {
            return;
        }

        if (CommonUtil.isEmpty(paramGroup)) {
            put(mobileGroup, Lists.newArrayList());
            return;
        }

        for (int i = 0; i < mobileGroup.size(); i++) {
            paramMap.put(mobileTrim(mobileGroup.get(i)), String.join(",", paramGroup.get(i)));
        }

    }

    /**
     * 如果字符串为空 则返回""
     *
     * @param mobile
     * @return
     */
    private static String mobileTrim(String mobile) {
        return StringUtils.isNotBlank(mobile) ? mobile : "";
    }
}
