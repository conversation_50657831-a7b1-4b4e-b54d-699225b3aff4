package com.xhqb.spectre.admin.service.oa.vo;

import lombok.Data;

@Data
public class FlowDataInfo {

    private Integer agentorbyagentid;

    private Integer agenttype;

    private String groupid;

    private long id;

    /**
     * 0 :节点操作人未提交，1：转发接收人未提交，2：已提交，4：已归档，8/9：抄送人 11：传阅
     */
    private Integer isremark;

    /**
     * 节点名称：直接上级、法务、归档6、运营人员负责人、短信系统管理人员
     */
    private String nodename;

    /**
     * 节点id
     */
    private long nodeid;

    private String operatedate;

    private String operatetime;

    private Integer preisremark;

    private String receivedate;

    private String receivetime;

    /**
     * 当前节点名称
     */
    private String userName;

    private Integer userType;

    private long userid;

    /**
     * 0:未查看 -1：已查看有反馈 -2：已查看
     */
    private Integer viewType;
}
