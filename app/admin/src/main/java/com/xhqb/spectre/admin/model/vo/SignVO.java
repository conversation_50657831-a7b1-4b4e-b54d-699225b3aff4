package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.SignDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/18 10:49
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SignVO implements Serializable {

    private static final long serialVersionUID = 1143631880322405221L;

    private Integer id;

    private String name;

    private String description;

    private String code;

    private Integer status;

    private String createTime;

    private String creator;

    private String updateTime;

    private String updater;

    public static SignVO buildSignVO(SignDO signDO) {
        return SignVO.builder()
                .id(signDO.getId())
                .name(signDO.getName())
                .description(signDO.getDescription())
                .code(signDO.getCode())
                .status(signDO.getStatus())
                .createTime(DateUtil.dateToString(signDO.getCreateTime()))
                .creator(signDO.getCreator())
                .updateTime(DateUtil.dateToString(signDO.getUpdateTime()))
                .updater(signDO.getUpdater())
                .build();
    }
}
