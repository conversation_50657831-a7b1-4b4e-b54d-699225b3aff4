package com.xhqb.spectre.admin.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.xhqb.kael.boot.autoconfigure.druid.DatasourceConfigUtils;
import com.xhqb.kael.boot.autoconfigure.druid.DefaultConnectionProperties;
import com.xhqb.kael.boot.autoconfigure.druid.DruidConnectionProperties;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.config.properties.BiDataDruidProperties;
import javax.sql.DataSource;


@Configuration
@MapperScan(basePackages = "com.xhqb.spectre.admin.bidata.mapper", sqlSessionTemplateRef = "biDataSqlSessionTemplate")
@EnableConfigurationProperties(BiDataDruidProperties.class)
public class BiDataSourceConfig {

    /**
     * bi_data数据源
     */
    @Bean(name = "biDataSource")
    public DataSource biDataSource(BiDataDruidProperties biDataDruidProperties) {
        DruidConnectionProperties defaultProperties = DruidConnectionProperties.withDefault(new DefaultConnectionProperties());
        BeanUtils.copyProperties(biDataDruidProperties, defaultProperties, CommonUtil.getNullPropertyNames(biDataDruidProperties));
        DruidDataSource dataSource = DatasourceConfigUtils.createDataSource(defaultProperties);
        dataSource.setName("bi_data");
        return dataSource;
    }

    /**
     * session factory
     */
    @Bean(name = "biDataSqlSessionFactory")
    public SqlSessionFactory biDataSqlSessionFactory(@Qualifier("biDataSource") DataSource dataSource) throws Exception {
        final SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/bidata/*Mapper.xml"));
        return bean.getObject();
    }

    /**
     * transaction manager
     */
    @Bean(name = "biDataTransactionManager")
    public DataSourceTransactionManager biDataTransactionManager(@Qualifier("biDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * build sql session template
     */
    @Bean(name = "biDataSqlSessionTemplate")
    public SqlSessionTemplate biDataSqlSessionTemplate(@Qualifier("biDataSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
