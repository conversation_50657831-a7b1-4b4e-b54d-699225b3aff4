package com.xhqb.spectre.admin.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.bidata.model.SendCountTopDO;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.model.dto.DateContext;
import com.xhqb.spectre.admin.service.FeiShuAlert;
import com.xhqb.spectre.admin.service.TopSendCountNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 每日9:50执行，发送发送量TOP10数据到飞书
 * 支持top数量配置
 */
@Component
@Job("alertTopSendCountToFeiShuJob")
@Slf4j
public class AlertTopSendCountToFeiShuJob implements SimpleJob {

    @Resource
    private TopSendCountNotificationService notificationService;

    @Resource
    private FeiShuAlert feiShuAlert;

    @Resource
    private VenusConfig venusConfig;


    /**
     * 每日9:50执行，发送发送量TOP10数据到飞书
     * 支持top数量配置
     */
    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            log.info("[发送量TOP飞书通知] 开始执行任务");

            // 初始化日期和配置
            DateContext dateContext = notificationService.initializeDateContext();
            int topN = notificationService.getSendCountTopN();
            Set<String> ispCodeSet = notificationService.parseIspCodes();

            // 获取模板代码列表
            List<String> tplCodeList = notificationService.getGlobalTopTplCodes(dateContext, topN);
            if (CollectionUtil.isEmpty(tplCodeList)) {
                log.info("[发送量TOP{}飞书通知] 无数据，日期: {}", topN, dateContext.getT1DateStr());
                return;
            }

            // 批量获取数据
            List<SendCountTopDO> allDataList = notificationService.getStatisticsData(tplCodeList, dateContext);
            Map<String, String> tplCodeAndCreatorMap = notificationService.createTplInfoMap(tplCodeList);

            // 处理数据并构建消息
            List<JSONObject> messageList = notificationService.processGlobalTopData(
                    tplCodeList, allDataList, tplCodeAndCreatorMap, dateContext, ispCodeSet);

            // 发送飞书通知
            sendFeiShuNotification(messageList, topN);

            log.info("[发送量TOP{}飞书通知] 任务执行完成", topN);
        } catch (Exception e) {
            log.error("[发送量TOP飞书通知] 任务执行失败", e);
            throw e;
        }
    }

    /**
     * 发送飞书通知
     */
    private void sendFeiShuNotification(List<JSONObject> messageList, int topN) {
        log.info("[发送量TOP{}] 发送飞书通知 内容size[{}]", topN, messageList.size());

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("reports", messageList);
        dataMap.put("topN", topN);

        feiShuAlert.sendFeiShuAlert(dataMap, venusConfig.getAlertTopSendCountToFeiShuJob());
    }
}
