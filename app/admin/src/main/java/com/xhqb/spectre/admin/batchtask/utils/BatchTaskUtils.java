package com.xhqb.spectre.admin.batchtask.utils;

import com.xhqb.spectre.admin.batchtask.parse.ParseResult;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.apache.poi.util.IOUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 群发任务工具类
 *
 * <AUTHOR>
 * @date 2022/2/22
 */
@Slf4j
public class BatchTaskUtils {

    private static final int NUMBER_ONE = 1;

    private BatchTaskUtils() {
    }


    /**
     * 请求参数转换成占位符列表模式
     *
     * @param paramList
     * @param mobileList
     * @return
     */
    public static List<Map<String, String>> paramMappingToPlaceholderList(List<LinkedHashMap<String, String>> paramList, String[] mobileList) {
        // 短信模板占位符模式
        List<Map<String, String>> placeHolderList = new ArrayList<>();
        Map<String, String> placeHolderMapping;
        for (String phone : mobileList) {
            placeHolderMapping = new HashMap<>(16);
            placeHolderMapping.put("phone", phone);
            for (Map<String, String> reqParam : paramList) {
                placeHolderMapping.put(reqParam.get("key"), reqParam.get("value"));
            }
            placeHolderList.add(placeHolderMapping);
        }

        return placeHolderList;
    }

    /**
     * 是否是excel文件
     *
     * @param inputStream
     * @param fileName
     * @return
     */
    public static boolean isExcel(InputStream inputStream, String fileName) {
        try {
            InputStream fileMagics = FileMagic.prepareToCheckMagic(inputStream);
            FileMagic fileMagic = FileMagic.valueOf(fileMagics);
            if (Objects.equals(fileMagic, FileMagic.OLE2) || Objects.equals(fileMagic, FileMagic.OOXML)) {
                return true;
            }
        } catch (IOException e) {
            log.warn("FileMagic valueOf异常,fileName = {}", fileName, e);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
        return false;

    }

    /**
     * 获取到文件上传内容总数量
     *
     * @param parseResultList
     * @return
     */
    public static int getUploadContentCount(List<ParseResult> parseResultList) {
        if (CommonUtil.isEmpty(parseResultList)) {
            return 0;
        }

        int count = 0;
        for (ParseResult parseResult : parseResultList) {
            count += CommonUtil.getCollectionSize(parseResult.getContentItemList());
        }
        return count;
    }

}
