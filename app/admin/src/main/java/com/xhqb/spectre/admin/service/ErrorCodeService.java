package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.ErrorCodeDTO;
import com.xhqb.spectre.admin.model.vo.ErrorCodeVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ErrorCodeQuery;

/**
 * 错误码
 *
 * <AUTHOR>
 * @date 2021/10/20
 */
public interface ErrorCodeService {


    /**
     * 分页查询错误码列表
     *
     * @param errorCodeQuery
     * @return
     */
    CommonPager<ErrorCodeVO> listByPage(ErrorCodeQuery errorCodeQuery);

    /**
     * 根据主键查询错误码信息
     *
     * @param type      错误码类型 submit->短信发送 deliver->短信回执
     * @param xhErrCode 错误码编码
     * @return
     */
    ErrorCodeVO getByPrimaryKey(String type, Integer xhErrCode);

    /**
     * 添加错误码信息
     *
     * @param errorCodeDTO
     */
    void create(ErrorCodeDTO errorCodeDTO);

    /**
     * 更新错误码信息
     *
     * @param errorCodeDTO
     */
    void update(ErrorCodeDTO errorCodeDTO);
}
