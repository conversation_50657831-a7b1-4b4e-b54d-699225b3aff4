package com.xhqb.spectre.admin.config;

import com.xhqb.spectre.admin.openapi.auth.OpenApiAccessInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * web配置
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Bean
    public OpenApiAccessInterceptor openApiAccessInterceptor() {
        return new OpenApiAccessInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(openApiAccessInterceptor()).addPathPatterns("/open/api/**");
    }
}
