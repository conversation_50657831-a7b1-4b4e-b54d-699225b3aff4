package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.AddBlackSourceDTO;
import com.xhqb.spectre.admin.model.dto.AddSignDTO;
import com.xhqb.spectre.admin.model.dto.UpdateBlackSourceDTO;
import com.xhqb.spectre.admin.model.dto.UpdateSignDTO;
import com.xhqb.spectre.admin.model.vo.BlackSourceEnumVO;
import com.xhqb.spectre.admin.model.vo.BlackSourceVO;
import com.xhqb.spectre.admin.model.vo.SignEnumVO;
import com.xhqb.spectre.admin.service.BlackSourceService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.BlackSourceDO;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.mapper.BlackSourceMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.BlackSourceQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@Service
public class BlackSourceServiceImpl implements BlackSourceService {

    @Autowired
    private BlackSourceMapper blackSourceMapper;

    /**
     * 查询黑名单来源列表
     *
     * @param blackSourceQuery
     * @return
     */
    @Override
    public CommonPager<BlackSourceVO> listByPage(BlackSourceQuery blackSourceQuery) {
        return PageResultUtils.result(
                () -> blackSourceMapper.countByQuery(blackSourceQuery),
                () -> blackSourceMapper.selectByQuery(blackSourceQuery).stream()
                        .map(BlackSourceVO::buildBlackSourceVO)
                        .collect(Collectors.toList())
        );
    }

    /**
     * 查询黑名单来源详情
     *
     * @param id
     * @return
     */
    @Override
    public BlackSourceVO getById(Integer id) {
        BlackSourceDO blackSourceDO = validateAndSelectById(id);
        return BlackSourceVO.buildBlackSourceVO(blackSourceDO);
    }

    /**
     * 添加黑名单来源
     *
     * @param addBlackSourceDTO
     */
    @Override
    public void create(AddBlackSourceDTO addBlackSourceDTO) {
        //校验
        checkAddParam(addBlackSourceDTO);

        //写入来源信息
        BlackSourceDO blackSourceDO = buildBlackSourceDO(addBlackSourceDTO);
        blackSourceMapper.insertSelective(blackSourceDO);
    }

    /**
     * 编辑黑名单来源
     *
     * @param updateBlackSourceDTO
     */
    @Override
    public void update(UpdateBlackSourceDTO updateBlackSourceDTO) {
        //校验
        checkUpdateParam(updateBlackSourceDTO);

        //写入来源信息
        BlackSourceDO blackSourceDO = buildBlackSourceDO(updateBlackSourceDTO);
        blackSourceMapper.updateByPrimaryKeySelective(blackSourceDO);
    }

    /**
     * 启用黑名单来源
     *
     * @param id
     */
    @Override
    public void enable(Integer id) {
        BlackSourceDO blackSourceDO = validateAndSelectById(id);
        if (!isDisabled(blackSourceDO)) {
            throw new BizException("来源不处于停用状态，不能启用");
        }

        //修改状态为启用
        blackSourceMapper.enable(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 停用来源
     *
     * @param id
     */
    @Override
    public void disable(Integer id) {
        BlackSourceDO blackSourceDO = validateAndSelectById(id);
        if (!isEnabled(blackSourceDO)) {
            throw new BizException("来源不处于启用状态，不能停用");
        }

        //修改状态为停用
        blackSourceMapper.disable(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 删除来源
     *
     * @param id
     */
    @Override
    public void delete(Integer id) {
        BlackSourceDO blackSourceDO = validateAndSelectById(id);
        if (!isDisabled(blackSourceDO)) {
            throw new BizException("来源不处于停用状态，不能删除");
        }
        //删除记录
        blackSourceMapper.delete(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 查询来源枚举
     *
     * @param status
     * @return
     */
    @Override
    public List<BlackSourceEnumVO> queryEnum(Integer status) {
        return blackSourceMapper.selectEnum(status).stream()
                .map(BlackSourceEnumVO::buildBlackSourceEnumVO)
                .collect(Collectors.toList());
    }

    private BlackSourceDO validateAndSelectById(Integer id) {
        BlackSourceDO blackSourceDO = blackSourceMapper.selectByPrimaryKey(id);
        if (Objects.isNull(blackSourceDO)) {
            throw new BizException("未找到黑名单来源");
        }
        return blackSourceDO;
    }

    private void checkAddParam(AddBlackSourceDTO addBlackSourceDTO) {
        //参数格式校验
        ValidatorUtil.validate(addBlackSourceDTO);

        //名称校验
        BlackSourceDO exist = blackSourceMapper.selectByName(addBlackSourceDTO.getName());
        if (Objects.nonNull(exist)) {
            throw new BizException("来源名称已存在");
        }
        //编码校验
        exist = blackSourceMapper.selectByCode(addBlackSourceDTO.getCode());
        if(Objects.nonNull(exist)){
            throw new BizException("来源编码已存在");
        }
    }

    private void checkUpdateParam(UpdateBlackSourceDTO updateBlackSourceDTO) {
        //参数格式校验
        ValidatorUtil.validate(updateBlackSourceDTO);

        //校验来源是否存在
        validateAndSelectById(updateBlackSourceDTO.getId());
    }

    private BlackSourceDO buildBlackSourceDO(AddBlackSourceDTO addBlackSourceDTO) {
        String userName = SsoUserInfoUtil.getUserName();
        return BlackSourceDO.builder()
                .name(addBlackSourceDTO.getName())
                .description(addBlackSourceDTO.getDescription())
                .code(addBlackSourceDTO.getCode())
                .creator(userName)
                .updater(userName)
                .build();
    }

    private BlackSourceDO buildBlackSourceDO(UpdateBlackSourceDTO updateBlackSourceDTO) {
        return BlackSourceDO.builder()
                .id(updateBlackSourceDTO.getId())
                .name(updateBlackSourceDTO.getName())
                .description(updateBlackSourceDTO.getDescription())
                .updater(SsoUserInfoUtil.getUserName())
                .build();
    }

    private boolean isEnabled(BlackSourceDO blackSourceDO) {
        return blackSourceDO.getStatus().equals(CommonConstant.STATUS_VALID);
    }

    private boolean isDisabled(BlackSourceDO blackSourceDO) {
        return blackSourceDO.getStatus().equals(CommonConstant.STATUS_INVALID);
    }
}
