package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.ChannelGroupItemDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2022/4/11 11:13
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelGroupItemVO implements Serializable {

    private static final long serialVersionUID = 493099727665157771L;

    /**
     * 渠道账号ID
     */
    private Integer channelAccountId;

    /**
     * 渠道账号名称
     */
    private String channelAccountName;

    /**
     * 运营商
     */
    private List<String> ispList;

    /**
     * 地域过滤类型，1：包含；2：不包含
     */
    private Integer areaFilterType;

    /**
     * 地域列表
     */
    private List<AreaVO> areaList;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 备注
     */
    private String remark;

    public static ChannelGroupItemVO build(ChannelGroupItemDO item, String channelAccountName) {
        return ChannelGroupItemVO.builder()
                .channelAccountId(item.getChannelAccountId())
                .channelAccountName(channelAccountName)
                .ispList(CommonUtil.ispToList(item.getIsps()))
                .areaFilterType(item.getAreaFilterType())
                .areaList(CommonUtil.areaToList(item.getAreas()))
                .weight(item.getWeight())
                .remark(item.getRemark())
                .build();
    }
}
