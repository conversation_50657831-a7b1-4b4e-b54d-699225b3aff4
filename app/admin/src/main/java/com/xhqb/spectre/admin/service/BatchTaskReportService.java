package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.BatchTaskReportVO;

import java.util.List;

/**
 * 群发参数报表
 *
 * <AUTHOR>
 * @date 2021/10/22
 */
public interface BatchTaskReportService {

    /**
     * 查看群发报表信息
     *
     * @param taskId
     * @param taskParamId
     * @return
     */
    List<BatchTaskReportVO> queryReport(Integer taskId, Integer taskParamId);

    /**
     * 快速统计
     * <p>
     * 主要快速统计出失败类型 失败数量 成功数等
     *
     * @param taskId
     * @return
     */
    AdminResult quickStats(Integer taskId);
}
