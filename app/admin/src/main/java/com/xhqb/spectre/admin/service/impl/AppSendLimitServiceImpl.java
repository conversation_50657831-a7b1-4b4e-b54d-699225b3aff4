package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.enums.RespCodeEnum;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.AppSendLimitDTO;
import com.xhqb.spectre.admin.model.dto.SendLimitListDTO;
import com.xhqb.spectre.admin.model.vo.AppSendLimitVO;
import com.xhqb.spectre.admin.model.vo.LimitRuleEnumVO;
import com.xhqb.spectre.admin.service.AppSendLimitService;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.entity.AppDO;
import com.xhqb.spectre.common.dal.entity.AppSendLimitDO;
import com.xhqb.spectre.common.dal.mapper.AppMapper;
import com.xhqb.spectre.common.dal.mapper.AppSendLimitMapper;
import com.xhqb.spectre.common.dal.mapper.MobileWhiteMapper;
import com.xhqb.spectre.common.dal.mapper.OpTimeMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.AppSendLimitQuery;
import com.xhqb.spectre.common.enums.AppSendLimitEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 10:19
 * @Description:
 */
@Service
@Slf4j
public class AppSendLimitServiceImpl implements AppSendLimitService {

    @Autowired
    private AppSendLimitMapper appSendLimitMapper;

    @Autowired
    private AppMapper appMapper;

    @Autowired
    private OpTimeMapper opTimeMapper;

    @Autowired
    private MobileWhiteMapper mobileWhiteMapper;

    /**
     * 查询应用限流列表
     *
     * @param appSendLimitQuery
     * @return
     */
    @Override
    public CommonPager<AppSendLimitVO> listByPage(AppSendLimitQuery appSendLimitQuery) {
        Integer totalCount = appSendLimitMapper.countByQuery(appSendLimitQuery);
        if (Objects.isNull(totalCount) || totalCount == 0) {
            return new CommonPager<>(0, Collections.emptyList());
        }
        List<AppSendLimitDO> list = appSendLimitMapper.selectByQuery(appSendLimitQuery);
        List<String> appCodeList = list.stream().map(AppSendLimitDO::getAppCode).collect(Collectors.toList());
        Map<String, List<AppSendLimitDO>> map = getAppCodeMap(appCodeList);

        List<AppSendLimitVO> sendLimitVOList = list.stream()
                .map(item -> AppSendLimitVO.buildAppSendLimitVO(map.get(item.getAppCode()))).collect(Collectors.toList());
        return new CommonPager<>(totalCount, sendLimitVOList);
    }

    /**
     * 查询应用限流详情
     *
     * @param appCode
     * @return
     */
    @Override
    public AppSendLimitVO getByAppCode(String appCode) {
        List<AppSendLimitDO> appSendLimitDOList = validateAndSelect(appCode);
        return AppSendLimitVO.buildAppSendLimitVO(appSendLimitDOList);
    }

    /**
     * 编辑应用限流详情
     *
     * @param sendLimitDTO
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void edit(AppSendLimitDTO sendLimitDTO) {
        //校验
        checkEditParam(sendLimitDTO);

        //查询已有配置
        List<AppSendLimitDO> existList = appSendLimitMapper.selectByAppCode(sendLimitDTO.getAppCode());
        Map<String, Integer> existMap = existList.stream().collect(Collectors.toMap(AppSendLimitDO::getSendLimitKey, AppSendLimitDO::getId));
        //遍历限流配置列表，写入配置信息
        String currentUser = SsoUserInfoUtil.getUserName();
        SendLimitListDTO limitList = sendLimitDTO.getLimitList();
        Class<? extends SendLimitListDTO> clazz = limitList.getClass();
        Field[] fields = clazz.getDeclaredFields();
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                String value = String.valueOf(field.get(limitList));
                if (fieldName.equals("serialVersionUID")) {
                    continue;
                }
                if (existMap.containsKey(fieldName)) {
                    //更新
                    AppSendLimitDO appSendLimitDO = AppSendLimitDO.builder()
                            .id(existMap.get(fieldName))
                            .appCode(sendLimitDTO.getAppCode())
                            .sendLimitKey(fieldName)
                            .sendLimitValue(value)
                            .updater(currentUser)
                            .build();
                    appSendLimitMapper.updateByPrimaryKeySelective(appSendLimitDO);
                } else {
                    //新增
                    AppSendLimitDO appSendLimitDO = AppSendLimitDO.builder()
                            .appCode(sendLimitDTO.getAppCode())
                            .sendLimitKey(fieldName)
                            .sendLimitValue(value)
                            .creator(currentUser)
                            .updater(currentUser)
                            .build();
                    appSendLimitMapper.insertSelective(appSendLimitDO);
                }
            }
        } catch (Exception e) {
            log.error("edit appSendLimit exception", e);
            throw new BizException(RespCodeEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 启用
     *
     * @param appCode
     */
    @Override
    public void enable(String appCode) {
        List<AppSendLimitDO> sendLimitDOList = validateAndSelect(appCode);
        AppSendLimitDO firstItem = sendLimitDOList.get(0);
        if (!isDisabled(firstItem)) {
            throw new BizException("限流配置不处于停用状态，不能启用");
        }

        //启用
        appSendLimitMapper.enable(appCode, SsoUserInfoUtil.getUserName());
    }

    /**
     * 停用
     *
     * @param appCode
     */
    @Override
    public void disable(String appCode) {
        List<AppSendLimitDO> sendLimitDOList = validateAndSelect(appCode);
        AppSendLimitDO firstItem = sendLimitDOList.get(0);
        if (!isEnabled(firstItem)) {
            throw new BizException("限流配置不处于启用状态，不能停用");
        }

        //停用
        appSendLimitMapper.disable(appCode, SsoUserInfoUtil.getUserName());
    }

    /**
     * 删除
     *
     * @param appCode
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void delete(String appCode) {
        List<AppSendLimitDO> sendLimitDOList = validateAndSelect(appCode);
        AppSendLimitDO firstItem = sendLimitDOList.get(0);
        if (!isDisabled(firstItem)) {
            throw new BizException("限流配置不处于停用状态，不能删除");
        }

        //删除
        String currentUser = SsoUserInfoUtil.getUserName();
        appSendLimitMapper.delete(appCode, currentUser);

        //删除应用限流白名单
        mobileWhiteMapper.deleteByAppCode(appCode, currentUser);
        opTimeMapper.updateOpTime(OpLogConstant.MODULE_MOBILE_WHITE, DateUtil.getNow());
    }

    /**
     * 查询限流规则枚举
     *
     * @return
     */
    @Override
    public List<LimitRuleEnumVO> queryLimitRuleEnum() {
        return Arrays.stream(AppSendLimitEnum.values()).map(item -> LimitRuleEnumVO.buildLimitRuleEnumVO(item.getCode(), item.getDesc()))
                .collect(Collectors.toList());
    }

    private void checkEditParam(AppSendLimitDTO sendLimitDTO) {
        //参数格式校验
        ValidatorUtil.validate(sendLimitDTO);
        ValidatorUtil.validate(sendLimitDTO.getLimitList());

        //应用编码校验
        String appCode = sendLimitDTO.getAppCode();
        AppDO appDO = appMapper.selectByCode(appCode);
        if (Objects.isNull(appDO)) {
            throw new BizException("应用编码有误，该业务应用不存在");
        }
    }

    private List<AppSendLimitDO> validateAndSelect(String appCode) {
        List<AppSendLimitDO> appSendLimitDOList = appSendLimitMapper.selectByAppCode(appCode);
        if (CollectionUtils.isEmpty(appSendLimitDOList)) {
            throw new BizException("未找到该应用的限流配置");
        }
        return appSendLimitDOList;
    }

    private boolean isEnabled(AppSendLimitDO appSendLimitDO) {
        return appSendLimitDO.getStatus().equals(CommonConstant.STATUS_VALID);
    }

    private boolean isDisabled(AppSendLimitDO appSendLimitDO) {
        return appSendLimitDO.getStatus().equals(CommonConstant.STATUS_INVALID);
    }

    private Map<String, List<AppSendLimitDO>> getAppCodeMap(List<String> appCodeList) {
        List<AppSendLimitDO> sendLimitDOList = appSendLimitMapper.selectByAppCodeList(appCodeList);
        return sendLimitDOList.stream().collect(Collectors.groupingBy(AppSendLimitDO::getAppCode));
    }
}
