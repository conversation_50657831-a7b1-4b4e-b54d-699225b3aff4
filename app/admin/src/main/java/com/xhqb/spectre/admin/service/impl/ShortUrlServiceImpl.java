package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.constant.ShortUrlConstant;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.ShortUrlDTO;
import com.xhqb.spectre.admin.model.vo.ShortUrlVO;
import com.xhqb.spectre.admin.service.ShortUrlService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.ShortUrlDO;
import com.xhqb.spectre.common.dal.mapper.ShortUrlMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ShortUrlQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;


/*
 * @Author: huangyanxiong
 * @Date: 2021/11/11 11:50
 * @Description:
 */
@Service
@Slf4j
public class ShortUrlServiceImpl implements ShortUrlService {

    private static final Date PERMANENT_DATE = DateUtil.smallToDate("2099-12-31");


    @Autowired
    private ShortUrlMapper shortUrlMapper;

    @Autowired
    private VenusConfig venusConfig;

    /**
     * 查询短链列表
     *
     * @param shortUrlQuery
     * @return
     */
    @Override
    public CommonPager<ShortUrlVO> listByPage(ShortUrlQuery shortUrlQuery) {
        try {
            if (StringUtils.isNotBlank(shortUrlQuery.getId())) {
                Integer.valueOf(shortUrlQuery.getId());
            }
        } catch (NumberFormatException e) {
            return new CommonPager<>(0, Collections.emptyList());
        }
        String shortUrl = shortUrlQuery.getShortUrl();
        if (StringUtils.isNotBlank(shortUrl)) {
            int index = shortUrl.lastIndexOf("/");
            if (index > -1 && index < shortUrl.length() - 1) {
                shortUrl = shortUrl.substring(index + 1);
                shortUrlQuery.setShortUrl(shortUrl);
            }
        }
        return PageResultUtils.result(
                () -> shortUrlMapper.countByQuery(shortUrlQuery),
                () -> shortUrlMapper.selectByQuery(shortUrlQuery).stream()
                        .map(item -> ShortUrlVO.buildShortUrlVO(item, buildShortUrl(item.getShortCode()))).collect(Collectors.toList())
        );
    }

    /**
     * 查询短链详情
     *
     * @param id
     * @return
     */
    @Override
    public ShortUrlVO queryInfo(Integer id) {
        ShortUrlDO shortUrlDO = validateAndSelectById(id);
        return ShortUrlVO.buildShortUrlVO(shortUrlDO, buildShortUrl(shortUrlDO.getShortCode()));
    }

    /**
     * 根据短链编码查询详情
     * @param shortCode
     * @return
     */
    @Override
    public ShortUrlVO queryInfo(String shortCode) {
        ShortUrlDO shortUrlDO = shortUrlMapper.selectByCode(shortCode);
        if (Objects.isNull(shortUrlDO)) {
            throw new BizException("未找到该短链接配置");
        }
        return ShortUrlVO.buildShortUrlVO(shortUrlDO, buildShortUrl(shortUrlDO.getShortCode()));
    }

    /**
     * 生成短链
     *
     * @param shortUrlDTO
     * @return
     */
    @Override
    public String create(ShortUrlDTO shortUrlDTO) {
        //校验
        ValidatorUtil.validate(shortUrlDTO);

        //计算过期日期
        Date expiredDate = calExpiredDate(shortUrlDTO.getValidPeriod());

        //生成短链编码
        int shortCodeLength = venusConfig.getShortCodeLength();
        String shortCode = CommonUtil.getRandStr(shortCodeLength);

        //保存
        String currentUser = SsoUserInfoUtil.getUserName();
        ShortUrlDO shortUrlDO = ShortUrlDO.builder()
                .shortCode(shortCode)
                .srcUrl(shortUrlDTO.getSrcUrl())
                .description(shortUrlDTO.getDescription())
                .expiredDate(expiredDate)
                .status(CommonConstant.STATUS_VALID) //默认生效
                .creator(currentUser)
                .updater(currentUser)
                .build();
        try {
            shortUrlMapper.insertSelective(shortUrlDO);
        } catch (DuplicateKeyException e) {
            log.warn("短链编码重复，编码值：{}", shortCode);
            //重试一次
            shortUrlDO.setShortCode(CommonUtil.getRandStr(shortCodeLength));
            shortUrlMapper.insertSelective(shortUrlDO);
        }

        return buildShortUrl(shortUrlDO.getShortCode());
    }

    @Override
    public String innerCreate(ShortUrlDTO shortUrlDTO) {
        //校验
        ValidatorUtil.validate(shortUrlDTO);

        String shortCode;
        //计算过期日期
        Date expiredDate = calExpiredDate(shortUrlDTO.getValidPeriod());

        //生成短链编码
        int shortCodeLength = venusConfig.getShortCodeLength();
        shortCode = CommonUtil.getRandStr(shortCodeLength);

        //保存
        String currentUser = SsoUserInfoUtil.getUserName();
        ShortUrlDO shortUrlDO = ShortUrlDO.builder()
                .shortCode(shortCode)
                .srcUrl(shortUrlDTO.getSrcUrl())
                .description(shortUrlDTO.getDescription())
                .expiredDate(expiredDate)
                .status(CommonConstant.STATUS_VALID)
                .creator(currentUser)
                .updater(currentUser)
                .build();
        try {
            shortUrlMapper.insertSelective(shortUrlDO);
        } catch (DuplicateKeyException e) {
            log.warn("短链编码重复，编码值：{}", shortCode);
            //重试一次
            shortUrlDO.setShortCode(CommonUtil.getRandStr(shortCodeLength));
            shortUrlMapper.insertSelective(shortUrlDO);
        }

        return shortCode;
    }


    /**
     * 更新短链
     *
     * @param id
     * @param shortUrlDTO
     * @return
     */
    @Override
    public String update(Integer id, ShortUrlDTO shortUrlDTO) {
        ShortUrlDO exist = validateAndSelectById(id);

        //校验
        ValidatorUtil.validate(shortUrlDTO);

        //计算过期日期
        Date expiredDate = calExpiredDate(shortUrlDTO.getValidPeriod());

        //更新
        ShortUrlDO updateItem = ShortUrlDO.builder()
                .id(id)
                .srcUrl(shortUrlDTO.getSrcUrl())
                .description(shortUrlDTO.getDescription())
                .expiredDate(expiredDate)
                .updater(SsoUserInfoUtil.getUserName())
                .build();
        log.info("更新短链信息，id：{}, exist={}, updateItem={}", id, exist, updateItem);
        shortUrlMapper.updateByPrimaryKeySelective(updateItem);

        return buildShortUrl(exist.getShortCode());
    }

    /**
     * 更新短链
     *
     * @param shortCode
     * @param shortUrlDTO
     * @return
     */
    @Override
    public String update(String shortCode, ShortUrlDTO shortUrlDTO) {
        ShortUrlDO exist = shortUrlMapper.selectByCode(shortCode);
        if (Objects.isNull(exist)) {
            throw new BizException("未找到该短链接配置");
        }

        //校验
        ValidatorUtil.validate(shortUrlDTO);

        //更新
        ShortUrlDO updateItem = ShortUrlDO.builder()
                .id(exist.getId())
                .srcUrl(shortUrlDTO.getSrcUrl())
                .updater(SsoUserInfoUtil.getUserName())
                .build();
        shortUrlMapper.updateByPrimaryKeySelective(updateItem);

        return buildShortUrl(exist.getShortCode());
    }

    /**
     * 启用短链
     *
     * @param id
     */
    @Override
    public void enable(Integer id) {
        ShortUrlDO shortUrlDO = validateAndSelectById(id);
        if (!shortUrlDO.isDisabled()) {
            throw new BizException("该短链接不处于停用状态，不能启用");
        }
        //修改状态为启用
        shortUrlMapper.enable(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 停用短链
     *
     * @param id
     */
    @Override
    public void disable(Integer id) {
        ShortUrlDO shortUrlDO = validateAndSelectById(id);
        if (!shortUrlDO.isEnabled()) {
            throw new BizException("该短链接不处于启用状态，不能停用");
        }
        //修改状态为停用
        shortUrlMapper.disable(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 删除短链
     *
     * @param id
     */
    @Override
    public void delete(Integer id) {
        ShortUrlDO shortUrlDO = validateAndSelectById(id);
        if (!shortUrlDO.isDisabled()) {
            throw new BizException("该短链接不处于停用状态，不能删除");
        }
        //删除短链
        shortUrlMapper.delete(id, SsoUserInfoUtil.getUserName());
    }

    /**
     * 计算过期日期
     *
     * @param validPeriod 有效期。1：90天；2：180天；3：365天；4：永久有效
     * @return
     */
    private Date calExpiredDate(Integer validPeriod) {
        if (Objects.isNull(validPeriod)) {
            return PERMANENT_DATE;
        }
        Calendar c = Calendar.getInstance();
        switch (validPeriod) {
            case ShortUrlConstant.EXPIRED_TYPE_90:
                c.add(Calendar.DAY_OF_MONTH, 90);
                break;
            case ShortUrlConstant.EXPIRED_TYPE_180:
                c.add(Calendar.DAY_OF_MONTH, 180);
                break;
            case ShortUrlConstant.EXPIRED_TYPE_365:
                c.add(Calendar.DAY_OF_MONTH, 365);
                break;
            default:
                return PERMANENT_DATE;
        }
        return c.getTime();
    }

    private String buildShortUrl(String shortCode) {
        return venusConfig.getShortUrlDomain() + "/" + shortCode;
    }

    private ShortUrlDO validateAndSelectById(Integer id) {
        ShortUrlDO shortUrlDO = shortUrlMapper.selectByPrimaryKey(id);
        if (Objects.isNull(shortUrlDO)) {
            throw new BizException("未找到该短链接配置");
        }
        return shortUrlDO;
    }
}
