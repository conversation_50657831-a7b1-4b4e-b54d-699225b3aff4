package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.TaskParamDTO;
import com.xhqb.spectre.admin.model.vo.BatchTaskParamVO;
import com.xhqb.spectre.common.dal.entity.BatchTaskParamDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.BatchTaskParamQuery;

import java.util.List;

/**
 * 群发参数服务
 *
 * <AUTHOR>
 * @date 2021/10/21
 */
public interface BatchTaskParamService {

    /**
     * 分页查询群发参数任务列表
     *
     * @param batchTaskParamQuery
     * @return
     */
    CommonPager<BatchTaskParamVO> listByPage(BatchTaskParamQuery batchTaskParamQuery);

    /**
     * 根据群发任务ID查询批量参数信息
     *
     * @param taskId
     * @param needParamJson 是否需要查询mapping数据信息
     * @return
     */
    List<BatchTaskParamVO> getByTaskId(Integer taskId, Boolean needParamJson);

    /**
     * 更新群发短信任务参数信息
     *
     * @param id
     * @param taskParamDTO
     */
    void update(Integer id, TaskParamDTO taskParamDTO);

    /**
     * 删除群发任务参数
     * <p>
     * 将is_delete打上删除标记
     *
     * @param id
     */
    void delete(Integer id);

    /**
     * 重置群发参数状态
     * <p>
     * 会将deleted状态设置为0
     * 并将任务状态设置为0
     *
     * @param id
     */
    void reset(Integer id);

    /**
     * 群发参数重新发送处理
     *
     * @param id
     */
    void replay(Integer id);

    /**
     * 保存或更新
     *
     * @param batchTaskParamDO
     */
    void saveOrUpdate(BatchTaskParamDO batchTaskParamDO);
}
