package com.xhqb.spectre.admin.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/8/1 15:00
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class BatchTplChannelDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 批量请求id
     */
    @NotEmpty(message = "传入模版id不能为空")
    private List<Integer> tplIdList;

    /**
     * 请求渠道信息列表
     */
    @NotEmpty(message = "渠道信息列表不能为空")
    @Valid
    private List<TplChannelDTO> tplChannelDTOList;


    /**
     * 是否覆盖
     */
    private Boolean covered;
}
