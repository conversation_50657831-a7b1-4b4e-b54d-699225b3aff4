package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.AppDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.AppVO;
import com.xhqb.spectre.admin.service.AppService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.AppQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.bind.BindResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 10:17
 * @Description:
 */
@RestController
@RequestMapping("/app")
@Slf4j
public class AppController {

    @Autowired
    private AppService appService;

    /**
     * 查询应用列表
     *
     * @param appQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryAppList(@ModelAttribute AppQuery appQuery, Integer pageNum, Integer pageSize) {
        appQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<AppVO> commonPager = appService.listByPage(appQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询应用详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryAppInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(appService.getById(id));
    }

    /**
     * 创建应用
     *
     * @param appDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_APP)
    public AdminResult createApp(@RequestBody AppDTO appDTO) {
        log.info("appDTO: {}", appDTO);
        appService.create(appDTO);
        return AdminResult.success();
    }

    /**
     * 更新应用
     *
     * @param id
     * @param appDTO
     * @return
     */
    @PutMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_APP)
    public AdminResult updateApp(@PathVariable("id") Integer id, @RequestBody AppDTO appDTO) {
        appDTO.setId(id);
        log.info("appDTO: {}", appDTO);
        appService.update(appDTO);
        return AdminResult.success();
    }

    /**
     * 删除应用
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_APP)
    public AdminResult deleteApp(@PathVariable("id") Integer id) {
        log.info("deleteApp, id: {}", id);
        appService.delete(id);
        return AdminResult.success();
    }

    /**
     * 查询应用枚举
     *
     * @return
     */
    @GetMapping("/enum")
    public AdminResult queryEnum() {
        return AdminResult.success(appService.queryEnum());
    }

}
