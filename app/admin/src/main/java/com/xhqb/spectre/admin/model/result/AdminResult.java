package com.xhqb.spectre.admin.model.result;

import com.xhqb.spectre.admin.enums.RespCodeEnum;
import com.xhqb.spectre.admin.exception.BizException;
import lombok.Data;
import org.springframework.http.HttpStatus;

import java.io.Serializable;

/**
 * ShenyuAdminResult.
 */
@Data
public class AdminResult implements Serializable {

    private static final long serialVersionUID = -2792556188993845048L;

    private Integer code;

    private String message;

    private Object data;

    /**
     * Instantiates a new shenyu result.
     */
    public AdminResult() {

    }

    /**
     * Instantiates a new shenyu result.
     *
     * @param code the code
     * @param msg  the msg
     * @param data the data
     */
    public AdminResult(final Integer code, final String msg, final Object data) {
        this.code = code;
        this.message = msg;
        this.data = data;
    }

    /**
     * return success.
     *
     * @return {@linkplain AdminResult}
     */
    public static AdminResult success() {
        return get(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMsg(), null);
    }

    /**
     * return success.
     *
     * @param data this is result data.
     * @return {@linkplain AdminResult}
     */
    public static AdminResult success(final Object data) {
        return get(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMsg(), data);
    }

    /**
     * return success.
     *
     * @param msg  this ext msg.
     * @param data this is result data.
     * @return {@linkplain AdminResult}
     */
    public static AdminResult success(final String msg, final Object data) {
        return get(RespCodeEnum.SUCCESS.getCode(), msg, data);
    }

    /**
     * return error .
     *
     * @param msg error msg
     * @return {@linkplain AdminResult}
     */
    public static AdminResult error(final String msg) {
        return error(RespCodeEnum.SYSTEM_ERROR.getCode(), msg);
    }

    /**
     * return error .
     *
     * @param code error code
     * @param msg  error msg
     * @return {@linkplain AdminResult}
     */
    public static AdminResult error(final int code, final String msg) {
        return get(code, msg, null);
    }

    public static AdminResult error(RespCodeEnum codeEnum) {
        return error(codeEnum.getCode(), codeEnum.getMsg());
    }

    public static AdminResult error(BizException e) {
        return error(e.getCode(), e.getMsg());
    }

    public static AdminResult error() {
        return error(RespCodeEnum.SYSTEM_ERROR);
    }

    /**
     * return timeout .
     *
     * @param msg error msg
     * @return {@linkplain AdminResult}
     */
    public static AdminResult timeout(final String msg) {
        return error(HttpStatus.REQUEST_TIMEOUT.value(), msg);
    }

    private static AdminResult get(final int code, final String msg, final Object data) {
        return new AdminResult(code, msg, data);
    }

}
