package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.vo.SmsHisStatisVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.SmsHisStatisQuery;

/**
 * 短信历史发送量统计表
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
public interface SmsHisStatisService {

    /**
     * 分页查询短信历史发送量统计列表
     *
     * @param smsHisStatisQuery
     * @return
     */
    CommonPager<SmsHisStatisVO> listByPage(SmsHisStatisQuery smsHisStatisQuery);
}
