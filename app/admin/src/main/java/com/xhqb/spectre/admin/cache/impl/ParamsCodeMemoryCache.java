package com.xhqb.spectre.admin.cache.impl;

import com.xhqb.spectre.admin.cache.AbstractMemoryCache;
import com.xhqb.spectre.common.dal.entity.ParamsCodeDO;
import com.xhqb.spectre.common.dal.entity.ParamsValueDO;
import com.xhqb.spectre.common.dal.mapper.ParamsCodeMapper;
import com.xhqb.spectre.common.dal.mapper.ParamsValueMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ParamsCodeMemoryCache extends AbstractMemoryCache<ParamsCodeDO> {

    @Resource
    private ParamsCodeMapper paramsCodeMapper;

    @Override
    protected List<ParamsCodeDO> loadCache() {
        log.info(" ParamsCodeDO load cache");
        List<ParamsCodeDO> paramsCodeDOList = paramsCodeMapper.selectAll();
        if (CollectionUtils.isEmpty(paramsCodeDOList)) {
            return new ArrayList<>();
        }
        return paramsCodeDOList;
    }

    @Override
    protected String tableName() {
        return "t_test_param_value";
    }
}
