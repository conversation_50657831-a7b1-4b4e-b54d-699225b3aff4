package com.xhqb.spectre.admin.batchtask.upload;


import com.xhqb.spectre.admin.batchtask.upload.impl.CosUploadHandler;
import com.xhqb.spectre.admin.batchtask.upload.impl.LocalUploadHandler;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

/**
 * 上传配置
 * 用于不同环境使用不同的处理对象
 *
 * <AUTHOR>
 * @date 2021/9/22
 */
@Configuration
public class UploadConfiguration {

    /**
     * 本地调试使用这个文件上传处理器
     *
     * @return
     */
    @Bean
    @Profile({"local"})
    @Primary
    public UploadHandler localUploadHandler() {
        return new LocalUploadHandler();
    }

    /**
     * 不是local或者dev环境时启用cos 文件上传
     *
     * @return
     */
    @Bean
    @ConditionalOnMissingBean(UploadHandler.class)
    public UploadHandler cosUploadHandler() {
        return new CosUploadHandler();
    }

}
