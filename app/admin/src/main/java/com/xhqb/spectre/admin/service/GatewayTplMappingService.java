package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.GatewayTplMappingDTO;
import com.xhqb.spectre.admin.model.vo.GatewayTplMappingVO;
import com.xhqb.spectre.admin.model.vo.GatewayTplVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.GatewayTplMappingQuery;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/26 17:00
 * @Description:
 */
public interface GatewayTplMappingService {

    CommonPager<GatewayTplMappingVO> listByPage(GatewayTplMappingQuery query);

    GatewayTplMappingVO getById(Integer id);

    void create(GatewayTplMappingDTO gatewayTplMappingDTO);

    void update(Integer id, GatewayTplMappingDTO gatewayTplMappingDTO);

    void delete(Integer id);

    List<GatewayTplVO> queryTplList();



}
