package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.admin.constant.CommonConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/27 15:57
 * @Description:
 */
@Data
public class MobileBlackDTO implements Serializable {

    private static final long serialVersionUID = -753858863733002156L;

    /**
     * 黑名单来源
     */
    @NotBlank(message = "黑名单来源不能为空")
    private String source;

    /**
     * 用户CID
     */
    @Size(max = 32, message = "CID最大为{max}个字符")
    private String cid;

    /**
     * 手机号列表
     */
    @NotEmpty(message = "手机号不能为空")
    @Size(max = 50, message = "最大支持{max}个手机号")
    private List<String> mobileList;

    /**
     * 短信类型编码列表
     */
    @NotEmpty(message = "短信类型不能为空")
    private List<String> smsTypeCodeList;

    /**
     * 拉黑原因
     */
    @NotBlank(message = "拉黑原因不能为空")
    @Size(max = 256, message = "拉黑原因最大为{max}个字符")
    private String description;

    @NotBlank(message = "过期时间不能为空")
    @Pattern(regexp = CommonConstant.PATTERN_DATE_TIME, message = "过期时间格式为" + CommonConstant.DATE_TIME_FORMAT)
    private String expiredTime;
}
