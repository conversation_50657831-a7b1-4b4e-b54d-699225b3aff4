package com.xhqb.spectre.admin.mq.send;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.pulsar.client.api.MessageId;

import java.io.Serializable;
import java.util.concurrent.CompletableFuture;

/**
 * 发送返回结果
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SenderResult implements Serializable {

    /**
     * 异步返回
     */
    private CompletableFuture<MessageId> messageIdFuture;
    /**
     * 同步返回
     */
    private MessageId messageId;
}
