package com.xhqb.spectre.admin.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xhqb.spectre.admin.bidata.entity.SubReachStatDO;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@Builder
public class SubReachStatVO implements Serializable {

    /**
     * 统计日期
     * 月份
     */
    @ExcelProperty(index = 0, value = "周期")
    private String statDate;

    @ExcelProperty(index = 1, value = "供应商")
    private String channelCodeName;

    /**
     * 渠道编码
     */
    @ExcelIgnore
    private String channelCode;

    /**
     * 短信类型编码
     */
    @ExcelProperty(index = 2, value = "类型")
    private String smsTypeCodeName;

    @ExcelProperty(index = 3, value = "模板")
    private String tplCode;

    /**
     * 触达量
     */
    @ExcelProperty(index = 4, value = "触达量")
    private Integer reachCount;

    /**
     * 计费量
     */
    @ExcelProperty(index = 5, value = "计费量")
    private Integer reachBillCount;

    /**
     * 触达率
     */
    @ExcelProperty(index = 6, value = "触达率")
    private String reachRate;

    /**
     * 模板内容
     */
    @ExcelProperty(index = 7, value = "费用")
    private BigDecimal priceCount;
    
    /**
     * 签名名称
     */
    @ExcelProperty(index = 8, value = "签名")
    private String signName;

    @ExcelProperty(index = 9, value = "报备人")
    private String creator;

    @ExcelProperty(index = 10, value = "模板内容")
    private String tplContent;



    public static SubReachStatVO convert(SubReachStatDO subReachStatDO,
                                       String channelCodeName,
                                       String smsTypeCodeName, String tplContent, String creator) {
        BigDecimal priceInYuan = new BigDecimal(String.valueOf(subReachStatDO.getPriceCount()))
                .divide(new BigDecimal("1000"), 3, RoundingMode.HALF_UP);
        return SubReachStatVO.builder().statDate(subReachStatDO.getStatDate())
                .channelCode(subReachStatDO.getChannelCode())
                .channelCodeName(channelCodeName)
                .tplCode(subReachStatDO.getTplCode())
                .smsTypeCodeName(smsTypeCodeName)
                .signName(subReachStatDO.getSignName())
                .reachCount(subReachStatDO.getReachCount())
                .reachBillCount(subReachStatDO.getReachBillCount())
                .reachRate(CommonUtil.double2String(subReachStatDO.getReachRate()))
                .tplContent(tplContent)
                .priceCount(priceInYuan)
                .creator(creator)
                .build();
    }
}
