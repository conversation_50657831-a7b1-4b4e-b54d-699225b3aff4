package com.xhqb.spectre.admin.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.google.common.collect.Lists;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.constant.MobileBlackConstant;
import com.xhqb.spectre.admin.model.vo.UplinkRelationOrderVO;
import com.xhqb.spectre.admin.readonly.mapper.SmsOrderReadonlyMapper;
import com.xhqb.spectre.admin.service.SmsUplinkIndexService;
import com.xhqb.spectre.admin.service.SmsUplinkService;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.entity.MobileBlackDO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.entity.SmsUplinkDO;
import com.xhqb.spectre.common.dal.entity.SmsUplinkTdDO;
import com.xhqb.spectre.common.dal.mapper.MobileBlackMapper;
import com.xhqb.spectre.common.dal.mapper.OpTimeMapper;
import com.xhqb.spectre.common.dal.mapper.SmsUplinkTdMapper;
import com.xhqb.spectre.common.dal.query.SmsUplinkRelationOrderQuery;
import com.xhqb.spectre.common.enums.BlacklistActionEnum;
import com.xhqb.spectre.common.enums.BlacklistTypeEnum;
import com.xhqb.spectre.common.service.SmsEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/8/30 10:22
 **/
@Slf4j
@Component
@Job("addBlackBySmsUplinkJob")
public class AddBlackBySmsUplinkJob implements SimpleJob {
    @Autowired
    private SmsUplinkIndexService smsUplinkIndexService;
    @Autowired
    private SmsUplinkService smsUplinkService;
    @Autowired
    private VenusConfig venusConfig;
    @Resource
    private MobileBlackMapper mobileBlackMapper;
    @Resource
    private OpTimeMapper opTimeMapper;

    @Resource
    private SmsUplinkTdMapper smsUplinkTdMapper;

    @Resource
    private SmsOrderReadonlyMapper smsOrderReadonlyMapper;

    @Autowired
    @Qualifier("adminSmsEventService")
    private SmsEventService smsEventService;


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void execute(ShardingContext shardingContext) {
        log.info("开始准备刷上行短信到黑名单");
        addMobileBlack();
        log.info("刷上行短信到黑名单结束");
    }

    /**
     * 筛选需要加入黑名单的上行短信并加入黑名单
     */
    private void addMobileBlack() {
        //获取上次记录id，取出数据
        Long lastSmsUplinkId = smsUplinkIndexService.selectLastSmsUplinkId();
        List<SmsUplinkDO> collectList = new ArrayList<>();
        do {
            lastSmsUplinkId = smsUplinkService.selectIdByMarket(lastSmsUplinkId, MobileBlackConstant.ADD_MOBILE_PAGE_SIZE, collectList);
        } while (lastSmsUplinkId != null);
        //获取黑名单限制条件，过滤黑名单得到黑名单数据
        String[] keyword = venusConfig.getSmsUplinkKeyword().split(",");
        if (ArrayUtils.isEmpty(keyword) || CollectionUtils.isEmpty(collectList)) {
            return;
        }
        Long lastId = collectList.get(collectList.size() - 1).getId();
        log.info("本次记录id到第{}行", lastId);
        smsUplinkIndexService.updateUplinkLastId(lastId);
        //准备插入黑名单数据
        List<SmsUplinkDO> initAddData = collectList.stream().distinct().filter(smsUplinkDO -> {
            for (String s : keyword) {
                if (s.equalsIgnoreCase(smsUplinkDO.getMsgContent().trim())) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(initAddData)) {
            return;
        }
        // 保存上行td
        saveBatchTd(initAddData);

        List<String> mobileList = initAddData.stream().map(SmsUplinkDO::getMobile).collect(Collectors.toList());
        Set<String> existSet = new HashSet<>(mobileBlackMapper.selectByMarketAndPhone(mobileList));
        doAddMobileBlack(initAddData, existSet);
    }

    /**
     * 将符合条件的上行短信加入黑名单
     *
     * @param initAddData 上行短信集合
     */
    private void doAddMobileBlack(List<SmsUplinkDO> initAddData, Set<String> existSet) {
        List<MobileBlackDO> mobileBlackDOList = initAddData.stream().filter(smsUplinkDO -> !existSet.contains(smsUplinkDO.getMobile()))
                .map(this::buildMobileBlackDo).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(mobileBlackDOList)) {
            saveBatch(mobileBlackDOList);
            opTimeMapper.updateOpTime(OpLogConstant.MODULE_MOBILE_BLACK, DateUtil.getNow());

            sendBlacklistEvents(mobileBlackDOList);
        }
    }

    private void saveBatchTd(List<SmsUplinkDO> smsUplinkDOList) {

        List<SmsUplinkTdDO> smsUplinkTdDOList = smsUplinkDOList.stream().map(smsUplinkDO -> {

            int before = getDaysBefore(smsUplinkDO.getRecvUplinkTime());

            SmsUplinkRelationOrderQuery query = new SmsUplinkRelationOrderQuery();
            query.setMobile(smsUplinkDO.getMobile());
            query.setPageSize(1);
            query.setStartTimestamp(before);
            query.setEndTimestamp(smsUplinkDO.getRecvUplinkTime());

            long orderId = 0L;
            Integer resend = 0;
            int sendTime = 0;

            List<SmsOrderDO> smsOrderList = smsOrderReadonlyMapper.uplinkRelationOrderQuery(query);
            if (!CollectionUtils.isEmpty(smsOrderList)) {
                SmsOrderDO smsOrderDO = smsOrderList.get(0);
                orderId = smsOrderDO.getOrderId();
                resend = smsOrderDO.getResend();
                sendTime = smsOrderDO.getSendTime();
            }

            return SmsUplinkTdDO.builder()
                    .orderId(orderId)
                    .resend(resend)
                    .mobile(smsUplinkDO.getMobile())
                    .replyContent(smsUplinkDO.getMsgContent())
                    .sendTime(sendTime)
                    .replyTime(smsUplinkDO.getRecvUplinkTime())
                    .replyType(0)
                    .channelAccountId(smsUplinkDO.getChannelAccountId())
                    .channelCode(smsUplinkDO.getChannelCode())
                    .smsTypeCode(smsUplinkDO.getSmsTypeCode())
                    .build();
        }).collect(Collectors.toList());


        if (!CollectionUtils.isEmpty(smsUplinkDOList)) {
            log.info("批量插入退订记录: size={}", smsUplinkTdDOList.size());
            smsUplinkTdMapper.insertBatch(smsUplinkTdDOList);

        }
    }

    public int getDaysBefore(int timestamp) {
        return (int) Instant.ofEpochSecond(timestamp)
                .minus(Math.abs(venusConfig.getUplinkRelationOrderDays()), ChronoUnit.DAYS)
                .getEpochSecond();
    }

    /**
     * 转成黑名单需要的数据
     *
     * @param smsUplinkDO 上行短信do
     * @return
     */
    private MobileBlackDO buildMobileBlackDo(SmsUplinkDO smsUplinkDO) {
        return MobileBlackDO.builder()
                .cid(smsUplinkDO.getCid())
                .mobile(smsUplinkDO.getMobile())
                .source(MobileBlackConstant.ADD_MOBILE_SOURCE)
                .smsTypeCode(smsUplinkDO.getSmsTypeCode())
                .description(MobileBlackConstant.ADD_MOBILE_DESCRIPTION)
                .addType(MobileBlackConstant.ADD_TYPE_SYSTEM)
                .updater(MobileBlackConstant.ADD_MOBILE_OPERATOR)
                .creator(MobileBlackConstant.ADD_MOBILE_OPERATOR)
                .build();
    }

    /**
     * 批量分批次新增数据
     *
     * @param mobileBlackDOList
     */
    private void saveBatch(List<MobileBlackDO> mobileBlackDOList) {
        if (mobileBlackDOList.size() < MobileBlackConstant.ADD_MOBILE_BATCH_MAX_SIZE) {
            mobileBlackMapper.insertBatch(mobileBlackDOList);
            return;
        }
        //分批插入
        List<List<MobileBlackDO>> totalList = Lists.partition(mobileBlackDOList, MobileBlackConstant.ADD_MOBILE_BATCH_MAX_SIZE);
        totalList.forEach(singleList -> mobileBlackMapper.insertBatch(singleList));
    }

    private void sendBlacklistEvents(List<MobileBlackDO> mobileBlackDOList) {
        try {
            for (MobileBlackDO mobileBlackDO : mobileBlackDOList) {
                smsEventService.sendBlacklistEvent(
                    mobileBlackDO.getMobile(),
                    BlacklistActionEnum.ADD,
                    BlacklistTypeEnum.TD_UPLINK,
                    mobileBlackDO.getSmsTypeCode(),
                    mobileBlackDO.getDescription(),
                    mobileBlackDO.getCreator(),
                    "uplink-job"
                );
            }
            log.info("成功发送{}条上行退订黑名单事件", mobileBlackDOList.size());
        } catch (Exception e) {
            log.error("发送上行退订黑名单事件失败: error={}", e.getMessage(), e);
        }
    }
}
