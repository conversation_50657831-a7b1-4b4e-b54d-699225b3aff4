package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 批量任务发送DTO
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
public class BatchTaskSendDTO implements Serializable {

    /**
     * 批次号
     */
    @NotNull(message = "批次号不能够为空")
    private Integer id;

    /**
     * 渠道id，如果设置了该参数就不读取模版关联的渠道信息
     */
    private Integer channelAccountId;

    /**
     * 短信内容
     */
    @NotEmpty(message = "短信内容不能够为空")
    private String content;
    /**
     * 参数列表
     */
    private List<LinkedHashMap<String, String>> paramList;
    /**
     * 手机号码,多个手机号码使用逗号分割
     */
    @NotEmpty(message = "手机号码不能够为空")
    private String mobile;


}
