package com.xhqb.spectre.admin.service.impl;

import com.google.common.collect.Lists;
import com.xhqb.spectre.admin.batchtask.cid.CidStrategyFactory;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.CidStrategyVO;
import com.xhqb.spectre.admin.service.CidStrategyService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.dto.CidStrategyDTO;
import com.xhqb.spectre.common.dal.entity.CidStrategyDO;
import com.xhqb.spectre.common.dal.mapper.CidStrategyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Cid校验策略表
 *
 * <AUTHOR>
 * @date 2021/11/26
 */
@Service
@Slf4j
public class CidStrategyServiceImpl implements CidStrategyService {

    /**
     * 查询最大数量
     */
    private static final int QUERY_MAX_SIZE = 900;

    @Resource
    private CidStrategyMapper cidStrategyMapper;
    @Resource
    private CidStrategyFactory cidStrategyFactory;

    /**
     * cid策略分组
     *
     * <pre>
     *     {
     *     "0":[
     *         {"strategy_value":"open","status":1,"id":1},
     *         {"strategy_value":"close","status":1,"id":2}
     *     ],
     *     "1":[
     *         {"strategy_value":"PRE","status":1,"id":3},
     *         {"strategy_value":"完件状态2","status":1,"id":4}
     *     ]
     * }
     * </pre>
     *
     * @return
     */
    @Override
    public AdminResult cidStrategyGroup(String group) {
        List<CidStrategyDO> cidStrategyList = cidStrategyMapper.selectByGroup(group);
        if (CommonUtil.isEmpty(cidStrategyList)) {
            log.info("未查询到cid策略信息,group = {}", group);
            return AdminResult.success();
        }
        Map<String, List<CidStrategyVO>> resultMap = cidStrategyList.stream()
                .map(CidStrategyVO::buildInfoQuery)
                .collect(Collectors.groupingBy(CidStrategyVO::getType));
        return AdminResult.success(resultMap);
    }

    /**
     * 更新cid策略信息
     * <p>
     * 该update方法只更新status
     *
     * @param group
     * @param cidStrategyList
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void update(String group, List<CidStrategyDTO> cidStrategyList) {
        if (CommonUtil.isEmpty(cidStrategyList)) {
            // 若客户端传入null 则表示需要将所有状态设置为0
            cidStrategyList = new ArrayList<>();
        }

        List<CidStrategyDO> dbList = cidStrategyMapper.selectByGroup(group);
        if (CommonUtil.isEmpty(dbList)) {
            throw new BizException("未查询到cid策略信息");
        }

        // 首先将所有数据都状态都重置
        List<List<CidStrategyDO>> partition = Lists.partition(dbList, QUERY_MAX_SIZE);
        List<Integer> idList;
        for (List<CidStrategyDO> itemList : partition) {
            idList = itemList.stream().map(s -> s.getId()).collect(Collectors.toList());
            // 重置状态
            cidStrategyMapper.resetStatusByGroup(group, idList);
        }

        final String username = SsoUserInfoUtil.getUserName();
        // 先暂时一条一条更新吧
        List<CidStrategyDO> updateList = cidStrategyList.stream()
                .map(s -> CidStrategyDO.builder()
                        .id(s.getId())
                        .status(s.getStatus())
                        .updater(username)
                        .build())
                .collect(Collectors.toList());

        for (CidStrategyDO cidStrategyDO : updateList) {
            cidStrategyMapper.updateByPrimaryKeySelective(cidStrategyDO);
        }

        cidStrategyFactory.putCache();
    }

    /**
     * 根据Id查询详情信息
     *
     * @param id
     * @return
     */
    @Override
    public CidStrategyVO getById(Integer id) {
        CidStrategyDO cidStrategyDO = selectById(id);
        return CidStrategyVO.buildInfoQuery(cidStrategyDO);
    }

    /**
     * 新增cid策略
     *
     * @param cidStrategyDTO
     */
    @Override
    public void create(CidStrategyDTO cidStrategyDTO) {
        CidStrategyDO cidStrategyDO = cidStrategyDTO.toDO();
        String username = SsoUserInfoUtil.getUserName();
        cidStrategyDO.setCreator(username);
        cidStrategyDO.setUpdater(username);
        cidStrategyMapper.insertSelective(cidStrategyDO);
        cidStrategyFactory.putCache();
    }

    /**
     * 根据cid信息
     *
     * @param cidStrategyDTO
     */
    @Override
    public void update(CidStrategyDTO cidStrategyDTO) {
        CidStrategyDO cidStrategyDO = cidStrategyDTO.toDO();
        String username = SsoUserInfoUtil.getUserName();
        cidStrategyDO.setCreator(username);
        cidStrategyDO.setUpdater(username);
        cidStrategyMapper.updateByPrimaryKeySelective(cidStrategyDO);
        cidStrategyFactory.putCache();
    }

    /**
     * 初始化策略分组
     */
    @Override
    public void initStrategyGroup() {
        cidStrategyMapper.initStrategyGroup();
        cidStrategyFactory.putCache();
    }


    /**
     * 查询cid策略信息
     *
     * @param id
     * @return
     */
    private CidStrategyDO selectById(Integer id) {
        CidStrategyDO cidStrategyDO = cidStrategyMapper.selectByPrimaryKey(id);
        if (Objects.isNull(cidStrategyDO)) {
            throw new BizException("未查询到cid策略信息");
        }
        return cidStrategyDO;
    }
}
