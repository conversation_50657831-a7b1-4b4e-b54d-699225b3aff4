package com.xhqb.spectre.admin.batchtask.send.factory;

import com.google.common.collect.Lists;
import com.xhqb.spectre.admin.batchtask.send.record.MessageSendFailedRecord;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 发送消息处理结果
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SingleFactoryResult implements Serializable {

    /**
     * 当前响应结果是否成功  所有成功则为成功
     */
    private Boolean success;

    /**
     * 响应消息描述
     */
    private String message;

    /**
     * 成功个数
     */
    private Integer successCount;
    /**
     * 失败个数
     */
    private Integer failCount;
    /**
     * 总数
     */
    private Integer totalCount;

    /**
     * 短信发送状态失败记录
     */
    private List<MessageSendFailedRecord> failureSMSRecord;

    /**
     * 获取到总记录数
     *
     * @param singleFactoryResultList
     * @return
     */
    public static Integer getAllTotalCount(List<SingleFactoryResult> singleFactoryResultList) {
        if (CommonUtil.isEmpty(singleFactoryResultList)) {
            return 0;
        }

        int count = 0;
        for (SingleFactoryResult singleFactoryResult : singleFactoryResultList) {
            Integer totalCount = singleFactoryResult.getTotalCount();
            if (Objects.nonNull(totalCount) && totalCount > 0) {
                count += totalCount;
            }
        }

        return count;
    }

    /**
     * 获取到所有失败的数量
     *
     * @param singleFactoryResultList
     * @return
     */
    public static Integer getAllFailCount(List<SingleFactoryResult> singleFactoryResultList) {
        if (CommonUtil.isEmpty(singleFactoryResultList)) {
            return 0;
        }

        int count = 0;
        for (SingleFactoryResult singleFactoryResult : singleFactoryResultList) {
            Integer failCount = singleFactoryResult.getFailCount();
            if (Objects.nonNull(failCount) && failCount > 0) {
                count += failCount;
            }
        }

        return count;
    }

    /**
     * 获取到所有失败手机号码
     *
     * @param singleFactoryResultList
     * @return
     */
    public static List<String> getAllFailMobile(List<SingleFactoryResult> singleFactoryResultList) {
        if (CommonUtil.isEmpty(singleFactoryResultList)) {
            return Lists.newArrayList();
        }

        List<String> allFailMobileList = Lists.newArrayList();
        for (SingleFactoryResult singleFactoryResult : singleFactoryResultList) {
            List<MessageSendFailedRecord> failureSMSRecord = singleFactoryResult.getFailureSMSRecord();
            if (CommonUtil.isEmpty(failureSMSRecord)) {
                continue;
            }
            for (MessageSendFailedRecord record : failureSMSRecord) {
                allFailMobileList.add(record.getPhoneNumber());
            }
        }

        return allFailMobileList;
    }

    /**
     * 获取到所有错误信息
     *
     * @param singleFactoryResultList
     * @return
     */
    public static List<MessageSendFailedRecord> getAllFailedRecord(List<SingleFactoryResult> singleFactoryResultList) {
        if (CommonUtil.isEmpty(singleFactoryResultList)) {
            return null;
        }
        List<MessageSendFailedRecord> allFailedRecord = Lists.newArrayList();
        for (SingleFactoryResult singleFactoryResult : singleFactoryResultList) {
            List<MessageSendFailedRecord> failureSMSRecord = singleFactoryResult.getFailureSMSRecord();
            if (!CommonUtil.isEmpty(failureSMSRecord)) {
                allFailedRecord.addAll(failureSMSRecord);
            }

        }

        return allFailedRecord;
    }
}
