package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.batchtask.cid.CidStrategyFactory;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.service.CidStrategyService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.dto.CidStrategyDTO;
import com.xhqb.spectre.common.dal.entity.CidStrategyDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Cid校验策略表
 *
 * <AUTHOR>
 * @date 2021/11/26
 */
@RestController
@RequestMapping("/cidStrategy")
@Slf4j
public class CidStrategyController {

    @Resource
    private CidStrategyService cidStrategyService;
    @Resource
    private CidStrategyFactory cidStrategyFactory;

    /**
     * 查询cid校验策略表信息
     *
     * @param group
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(@RequestParam(name = "group") String group) {
        return cidStrategyService.cidStrategyGroup(group);
    }

    /**
     * 更新cid策略
     *
     * @param group
     * @param cidStrategyList
     * @return
     */
    @PostMapping("/update")
    public AdminResult update(@RequestParam(name = "group") String group, @RequestBody List<CidStrategyDTO> cidStrategyList) {
        log.info("收到更新cid策略请求 = {}", JSON.toJSONString(cidStrategyList));
        cidStrategyService.update(group, cidStrategyList);
        return AdminResult.success();
    }


    /**
     * 查询详情信息
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Integer id) {
        log.info("收到cid策略查询请求 = {}", id);
        return AdminResult.success(cidStrategyService.getById(id));
    }

    /**
     * 添加cid策略
     *
     * @param cidStrategyDTO
     * @return
     */
    @PostMapping("")
    public AdminResult createInfo(@RequestBody CidStrategyDTO cidStrategyDTO) {
        log.info("create cidStrategyDTO = {}", JSON.toJSONString(cidStrategyDTO));
        cidStrategyService.create(cidStrategyDTO);
        return AdminResult.success();
    }

    /**
     * 更新cid策略
     *
     * @param id
     * @param cidStrategyDTO
     * @return
     */
    @PutMapping("/{id}")
    public AdminResult updateInfo(@PathVariable("id") Integer id, @RequestBody CidStrategyDTO cidStrategyDTO) {
        log.info("update cidStrategyDTO = {}, id = {}", JSON.toJSONString(cidStrategyDTO), id);
        cidStrategyDTO.setId(id);
        cidStrategyService.update(cidStrategyDTO);
        return AdminResult.success();
    }


    /**
     * 检测cid策略缓存
     *
     * @param strategyType
     * @return
     */
    @GetMapping("/checkCache")
    public AdminResult checkCache(String strategyType) {
        if (StringUtils.isBlank(strategyType)) {
            return AdminResult.error("请输入strategyType");
        }

        List<CidStrategyDO> cacheResult = cidStrategyFactory.getCache(strategyType);
        return AdminResult.success(cacheResult);
    }

    /**
     * 刷新cid策略缓存
     *
     * @return
     */
    @GetMapping("/refreshCache")
    public AdminResult refreshCache() {
        log.info("收到刷新cid策略缓存请求,userName = {}", SsoUserInfoUtil.getUserName());
        cidStrategyFactory.putCache();
        return AdminResult.success();
    }

    /**
     * 初始化策略分组
     *
     * @return
     */
    @GetMapping("/initStrategyGroup")
    public AdminResult initStrategyGroup() {
        log.info("初始化cid策略请求,userName = {}", SsoUserInfoUtil.getUserName());
        cidStrategyService.initStrategyGroup();
        return AdminResult.success();
    }

}
