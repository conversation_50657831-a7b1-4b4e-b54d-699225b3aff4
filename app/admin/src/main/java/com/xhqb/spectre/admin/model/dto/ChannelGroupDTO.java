package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2022/4/11 10:53
 * @Description:
 */
@Data
public class ChannelGroupDTO implements Serializable {

    private static final long serialVersionUID = -2041843948865763978L;

    private Integer id;

    @NotBlank(message = "短信类型编码不能为空")
    private String smsTypeCode;

    @NotNull(message = "签名ID不能为空")
    private Integer signId;

    @NotBlank(message = "渠道组名称不能为空")
    @Size(max = 32, message = "渠道组名称最大为{max}个字符")
    private String name;

    @Size(max = 255, message = "渠道组描述最大为{max}个字符")
    private String description;

    @NotEmpty(message = "渠道信息列表不能为空")
    private List<ChannelGroupItemDTO> channelInfoList;
}
