package com.xhqb.spectre.admin.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 12:00
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppSendLimitDTO implements Serializable {

    private static final long serialVersionUID = 8650759308486495651L;

    @NotBlank(message = "应用编码不能为空")
    private String appCode;

    @NotNull(message = "应用限流配置不能为空")
    private SendLimitListDTO limitList;
}
