package com.xhqb.spectre.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.model.vo.ShortUrlBrandStatPageVO;
import com.xhqb.spectre.admin.model.vo.ShortUrlBrandStatProportionVO;
import com.xhqb.spectre.admin.model.vo.ShortUrlBrandStatTrendChartItemVO;
import com.xhqb.spectre.admin.model.vo.ShortUrlBrandStatTrendChartVO;
import com.xhqb.spectre.admin.service.ShortUrlBrandStatService;
import com.xhqb.spectre.admin.service.brand.BrandStrategyFactory;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.dto.ShortUrlLogData;
import com.xhqb.spectre.common.dal.entity.ShortUrlBrandStatDO;
import com.xhqb.spectre.common.dal.mapper.ShortUrlBrandStatMapper;
import com.xhqb.spectre.common.dal.mapper.ShortUrlLogMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ShortUrlBrandStatQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ShortUrlBrandStatServiceImpl implements ShortUrlBrandStatService {

    @Resource
    private ShortUrlBrandStatMapper shortUrlBrandStatMapper;
    @Resource
    private ShortUrlLogMapper shortUrlLogMapper;
    @Resource
    private BrandStrategyFactory brandStrategyFactory;
    @Resource
    private VenusConfig venusConfig;

    @Override
    public CommonPager<ShortUrlBrandStatPageVO> page(ShortUrlBrandStatQuery shortUrlBrandStatQuery) {
        this.check(shortUrlBrandStatQuery);
        return PageResultUtils.result(
                () -> shortUrlBrandStatMapper.countByQuery(shortUrlBrandStatQuery),
                () -> shortUrlBrandStatMapper.selectByQuery(shortUrlBrandStatQuery)
                        .stream().map(this::buildShortUrlBrandStatVO).collect(Collectors.toList())
        );
    }

    @Deprecated
    @Override
    public ShortUrlBrandStatProportionVO brandProportion(ShortUrlBrandStatQuery shortUrlBrandStatQuery) {
        this.check(shortUrlBrandStatQuery);
        List<ShortUrlBrandStatDO> modelDOList = shortUrlBrandStatMapper.selectByQuery(shortUrlBrandStatQuery);
        if (CollectionUtil.isEmpty(modelDOList)) {
            return ShortUrlBrandStatProportionVO.buildDefault();
        }
        List<String> brands = getBrandsList(modelDOList, shortUrlBrandStatQuery);
        List<Double> proportionList = new ArrayList<>();
        Map<String, List<ShortUrlBrandStatDO>> brandMap = modelDOList.stream().collect(Collectors.groupingBy(ShortUrlBrandStatDO::getBrand));
        for (String brand : brands) {
            List<ShortUrlBrandStatDO> value = brandMap.getOrDefault(brand, Collections.emptyList());
            long pv = value.stream().mapToLong(ShortUrlBrandStatDO::getPv).sum();
            long uv = value.stream().mapToLong(ShortUrlBrandStatDO::getUv).sum();
            double proportion = CommonUtil.realDivision(pv, uv);
            proportionList.add(proportion);
        }
        ShortUrlBrandStatProportionVO shortUrlBrandStatProportionVO = new ShortUrlBrandStatProportionVO();
        shortUrlBrandStatProportionVO.setBrands(brands);
        shortUrlBrandStatProportionVO.setProportion(proportionList);
        return shortUrlBrandStatProportionVO;
    }

    @Deprecated
    @Override
    public ShortUrlBrandStatTrendChartVO trendChart(ShortUrlBrandStatQuery shortUrlBrandStatQuery) {
        this.check(shortUrlBrandStatQuery);
        List<ShortUrlBrandStatDO> modelDOList = shortUrlBrandStatMapper.selectByQuery(shortUrlBrandStatQuery);
        if (CollectionUtil.isEmpty(modelDOList)) {
            return this.buildDefaultTrendChartVO(shortUrlBrandStatQuery);
        }
        List<String> statDates = getDateRangeAsStrings(shortUrlBrandStatQuery);
        Map<String, List<ShortUrlBrandStatDO>> statDateMap = modelDOList.stream()
                .collect(Collectors.groupingBy(statDO -> DateUtil.format(statDO.getStatDate(), "yyyy-MM-dd")));
        List<String> brands = getBrandsList(modelDOList, shortUrlBrandStatQuery);
        Map<String, ShortUrlBrandStatTrendChartItemVO> initBrandItemMap = new HashMap<>();
        for (String brand : brands) {
            ShortUrlBrandStatTrendChartItemVO itemVO = new ShortUrlBrandStatTrendChartItemVO();
            itemVO.setBrand(brand);
            itemVO.setPvs(Lists.newArrayList());
            itemVO.setUvs(Lists.newArrayList());
            itemVO.setPvuRatios(Lists.newArrayList());
            initBrandItemMap.put(brand, itemVO);
        }

        ShortUrlBrandStatTrendChartVO trendChartVO = new ShortUrlBrandStatTrendChartVO();
        List<Double> allPvuRatios = new ArrayList<>();
        for (String statDate : statDates) {
            List<ShortUrlBrandStatDO> brandList = statDateMap.getOrDefault(statDate, Collections.emptyList());
            Map<String, List<ShortUrlBrandStatDO>> brandMap = brandList.stream().collect(Collectors.groupingBy(ShortUrlBrandStatDO::getBrand));
            long allPv = brandList.stream().mapToLong(ShortUrlBrandStatDO::getPv).sum();
            long allUv = brandList.stream().mapToLong(ShortUrlBrandStatDO::getUv).sum();
            allPvuRatios.add(CommonUtil.realDivision(allPv, allUv));
            for (String brand : brands) {
                List<ShortUrlBrandStatDO> brandDOList = brandMap.getOrDefault(brand, Collections.emptyList());
                ShortUrlBrandStatTrendChartItemVO shortUrlBrandStatTrendChartItemVO = initBrandItemMap.get(brand);
                long pv = brandDOList.stream().mapToLong(ShortUrlBrandStatDO::getPv).sum();
                long uv = brandDOList.stream().mapToLong(ShortUrlBrandStatDO::getUv).sum();
                Double pvu = CommonUtil.realDivision(pv, uv);
                shortUrlBrandStatTrendChartItemVO.getPvs().add(pv);
                shortUrlBrandStatTrendChartItemVO.getUvs().add(uv);
                shortUrlBrandStatTrendChartItemVO.getPvuRatios().add(pvu);
                initBrandItemMap.put(brand, shortUrlBrandStatTrendChartItemVO);
            }
        }

        List<ShortUrlBrandStatTrendChartItemVO> itemVOList = new ArrayList<>(initBrandItemMap.values());
        trendChartVO.setStatDates(statDates);
        trendChartVO.setItems(itemVOList);
        trendChartVO.setAllPvuRatios(allPvuRatios);
        return trendChartVO;
    }

    @Override
    public String runJob(Date curDate) {
        String result = "success";
        try {
            doRunJob(curDate);
        } catch (Exception e) {
            log.error("短链访问品牌统计异常", e);
            result = "failed";
        }
        return result;
    }

    @Override
    public ShortUrlBrandStatProportionVO pvProportion(ShortUrlBrandStatQuery shortUrlBrandStatQuery) {
        this.check(shortUrlBrandStatQuery);
        List<ShortUrlBrandStatDO> modelDOList = shortUrlBrandStatMapper.selectByQuery(shortUrlBrandStatQuery);
        List<String> brands = getBrandsList(modelDOList, shortUrlBrandStatQuery);
        List<Double> proportionList = new ArrayList<>();
        Map<String, List<ShortUrlBrandStatDO>> brandMap = modelDOList.stream().collect(Collectors.groupingBy(ShortUrlBrandStatDO::getBrand));
        for (String brand : brands) {
            List<ShortUrlBrandStatDO> value = brandMap.getOrDefault(brand, Collections.emptyList());
            long pv = value.stream().mapToLong(ShortUrlBrandStatDO::getPv).sum();
            proportionList.add((double) pv);
        }
        ShortUrlBrandStatProportionVO shortUrlBrandStatProportionVO = new ShortUrlBrandStatProportionVO();
        shortUrlBrandStatProportionVO.setBrands(brands);
        shortUrlBrandStatProportionVO.setProportion(proportionList);
        return shortUrlBrandStatProportionVO;
    }

    @Override
    public ShortUrlBrandStatProportionVO uvProportion(ShortUrlBrandStatQuery shortUrlBrandStatQuery) {
        this.check(shortUrlBrandStatQuery);
        List<ShortUrlBrandStatDO> modelDOList = shortUrlBrandStatMapper.selectByQuery(shortUrlBrandStatQuery);
        List<String> brands = getBrandsList(modelDOList, shortUrlBrandStatQuery);
        List<Double> proportionList = new ArrayList<>();
        Map<String, List<ShortUrlBrandStatDO>> brandMap = modelDOList.stream().collect(Collectors.groupingBy(ShortUrlBrandStatDO::getBrand));
        for (String brand : brands) {
            List<ShortUrlBrandStatDO> value = brandMap.getOrDefault(brand, Collections.emptyList());
            long uv = value.stream().mapToLong(ShortUrlBrandStatDO::getUv).sum();
            proportionList.add((double) uv);
        }
        ShortUrlBrandStatProportionVO shortUrlBrandStatProportionVO = new ShortUrlBrandStatProportionVO();
        shortUrlBrandStatProportionVO.setBrands(brands);
        shortUrlBrandStatProportionVO.setProportion(proportionList);
        return shortUrlBrandStatProportionVO;
    }

    public void doRunJob(Date curDate) {
        // 捞出昨天点击记录
        List<ShortUrlLogData> originalList = fetchShortUrlLogsForCurrentDay(curDate);
        if (CollectionUtil.isEmpty(originalList)) {
            log.info("没有需要统计的短链访问数据");
            return;
        }

        // 查看是否有昨天统计数据 存在就更新删除标志
        checkAndMarkExistingStatsForDeletion(curDate);

        Date statDate = DateUtil.parse(DateUtil.format(curDate, "yyyy-MM-dd"));
        // 根据 brand，os 得到对应品牌
        List<ShortUrlLogData> resultList = mapToBrandDetectedLogData(originalList);
        // 计算 pv,uv
        List<ShortUrlBrandStatDO> statResults = calculateAndMapPvUvStats(resultList, statDate);
        // model 数据进行落库
        batchInsertShortUrlBrandStats(statResults);
    }

    private void checkAndMarkExistingStatsForDeletion(Date curDate) {
        List<ShortUrlBrandStatDO> existList = shortUrlBrandStatMapper.selectByTime(DateUtil.beginOfDay(curDate));
        if (CollectionUtil.isNotEmpty(existList)) {
            shortUrlBrandStatMapper.updateDeleteTagByTime(DateUtil.beginOfDay(curDate));
        }
    }

    public void batchInsertShortUrlBrandStats(List<ShortUrlBrandStatDO> statResults) {
        List<List<ShortUrlBrandStatDO>> partitionList = Lists.partition(statResults, 200);
        partitionList.forEach(list -> shortUrlBrandStatMapper.insertBatch(list));
    }


    public List<ShortUrlBrandStatDO> calculateAndMapPvUvStats(List<ShortUrlLogData> resultList, Date statDate) {
        Map<String, Long> pvMap = resultList.stream()
                .collect(Collectors.groupingBy(
                        data -> data.getUserTplCode() + ":" + data.getBrand(),
                        Collectors.counting()
                ));
        Map<String, Long> uvMap = resultList.stream()
                .collect(Collectors.groupingBy(
                        data -> data.getUserTplCode() + ":" + data.getBrand(),
                        Collectors.mapping(ShortUrlLogData::getShortCode, Collectors.toSet())
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> (long) entry.getValue().size()
                ));
        // 合并结果为 List<StatResult>
        Date date = new Date();
        return pvMap.entrySet().stream()
                .map(entry -> {
                    String[] keys = entry.getKey().split(":");
                    ShortUrlBrandStatDO result = new ShortUrlBrandStatDO();
                    result.setUserTplCode(keys[0]);
                    result.setBrand(keys[1]);
                    result.setPv(entry.getValue());
                    result.setUv(uvMap.getOrDefault(entry.getKey(), 0L));
                    result.setStatDate(statDate);
                    result.setCreateTime(date);
                    result.setUpdateTime(date);
                    return result;
                })
                .collect(Collectors.toList());
    }

    public List<ShortUrlLogData> mapToBrandDetectedLogData(List<ShortUrlLogData> originalList) {
        return originalList.stream()
                .map(shortUrlLogData -> new ShortUrlLogData(
                        brandStrategyFactory.detectBrand(shortUrlLogData),
                        shortUrlLogData.getOs(),
                        shortUrlLogData.getUserTplCode(),
                        shortUrlLogData.getShortCode()
                )).collect(Collectors.toList());
    }

    public List<ShortUrlLogData> fetchShortUrlLogsForCurrentDay(Date curDate) {
        long startOfDay = DateUtil.beginOfDay(curDate).getTime() / 1000;
        long endOfDay = DateUtil.endOfDay(curDate).getTime() / 1000;
        return shortUrlLogMapper.selectByTime(startOfDay, endOfDay);
    }

    public List<String> getBrandsList(List<ShortUrlBrandStatDO> modelDOList, ShortUrlBrandStatQuery shortUrlBrandStatQuery) {
        if (StringUtils.isNotBlank(shortUrlBrandStatQuery.getBrands())) {
            return Arrays.asList(shortUrlBrandStatQuery.getBrands().split(","));
        }
        return modelDOList.stream().map(ShortUrlBrandStatDO::getBrand).distinct().collect(Collectors.toList());
    }

    public ShortUrlBrandStatTrendChartVO buildDefaultTrendChartVO(ShortUrlBrandStatQuery shortUrlBrandStatQuery) {

        List<String> brands = Lists.newArrayList("apple", "huawei", "xiaomi", "oppo", "vivo", "other", "honor");
        if (StringUtils.isNotBlank(shortUrlBrandStatQuery.getBrands())) {
            brands.clear();
            brands = Arrays.asList(shortUrlBrandStatQuery.getBrands().split(","));
        }
        ShortUrlBrandStatTrendChartVO defaultTrendChartVO = new ShortUrlBrandStatTrendChartVO();

        List<String> statDates = getDateRangeAsStrings(shortUrlBrandStatQuery);
        List<ShortUrlBrandStatTrendChartItemVO> items = new ArrayList<>();
        for (String brand : brands) {
            ShortUrlBrandStatTrendChartItemVO itemVO = new ShortUrlBrandStatTrendChartItemVO();
            itemVO.setBrand(brand);
            int size = statDates.size();
            itemVO.setPvs(Collections.nCopies(size, 0L));
            itemVO.setUvs(Collections.nCopies(size, 0L));
            itemVO.setPvuRatios(Collections.nCopies(size, 0.0));
            items.add(itemVO);
        }
        defaultTrendChartVO.setStatDates(statDates);
        defaultTrendChartVO.setItems(items);
        return defaultTrendChartVO;
    }


    public List<String> getDateRangeAsStrings(ShortUrlBrandStatQuery shortUrlBrandStatQuery) {
        List<String> statDates = Lists.newArrayList();
        // shortUrlBrandStatQuery startTime (yyyy-mm-dd) 到 endTime 时间
        DateTime startDate = DateUtil.parse(shortUrlBrandStatQuery.getStartTime());
        DateTime endDate = DateUtil.parse(shortUrlBrandStatQuery.getEndTime());

        // 获取从 startDate 到 endDate 的每一天时间
        List<DateTime> dateRange = DateUtil.rangeToList(startDate, endDate, DateField.DAY_OF_MONTH);
        for (DateTime date : dateRange) {
            statDates.add(date.toString("yyyy-MM-dd"));
        }
        return statDates;
    }

    private ShortUrlBrandStatPageVO buildShortUrlBrandStatVO(ShortUrlBrandStatDO shortUrlBrandStatDO) {
        ShortUrlBrandStatPageVO shortUrlBrandStatVO = new ShortUrlBrandStatPageVO();
        BeanUtils.copyProperties(shortUrlBrandStatDO, shortUrlBrandStatVO);
        shortUrlBrandStatVO.setStatDateStr(DateUtil.format(shortUrlBrandStatDO.getStatDate(), "yyyy-MM-dd"));
        return shortUrlBrandStatVO;
    }

    private void check(ShortUrlBrandStatQuery query) {
        if (!this.venusConfig.getShortUrlCurlStatEnable()) {
            query.setUnContainsBrands("curl");
        }
    }
}
