package com.xhqb.spectre.admin.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xhqb.spectre.admin.bidata.entity.TplHeatStatDO;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 模板热度统计
 */
@Data
@Builder
public class TplHeatStatVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "日期", index = 0)
    private String statDate;

    @ExcelProperty(value = "模板编码", index = 1)
    private String tplCode;

    @ExcelProperty(value = "申请人", index = 2)
    private String creator;

    @ExcelProperty(value = "签名", index = 3)
    private String signName;

    @ExcelProperty(value = "短信内容", index = 4)
    private String content;

    @ExcelProperty(value = "发送量", index = 5)
    private Integer totalSendCount;

    @ExcelProperty(value = "触达量", index = 6)
    private Integer totalReachCount;

    @ExcelProperty(value = "计费量", index = 7)
    private Integer totalBillSendCount;

    public static TplHeatStatVO convert(TplHeatStatDO tplHeatStatDO) {
        return TplHeatStatVO.builder()
                .statDate(tplHeatStatDO.getStatDate())
                .tplCode(tplHeatStatDO.getTplCode())
                .totalSendCount(tplHeatStatDO.getSendCount())
                .totalReachCount(tplHeatStatDO.getReachCount())
                .totalBillSendCount(tplHeatStatDO.getReachBillCount())
                .build();
    }
}
