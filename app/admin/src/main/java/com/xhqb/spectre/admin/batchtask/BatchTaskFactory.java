package com.xhqb.spectre.admin.batchtask;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.detect.BatchDetectSaveHandler;
import com.xhqb.spectre.admin.batchtask.io.UploadFileUtils;
import com.xhqb.spectre.admin.batchtask.parse.ContentItem;
import com.xhqb.spectre.admin.batchtask.parse.ContentParser;
import com.xhqb.spectre.admin.batchtask.parse.ParseContext;
import com.xhqb.spectre.admin.batchtask.parse.ParseResult;
import com.xhqb.spectre.admin.batchtask.result.UploadResultContext;
import com.xhqb.spectre.admin.batchtask.result.impl.WriteUploadResultHandler;
import com.xhqb.spectre.admin.batchtask.strategy.FileLoadContext;
import com.xhqb.spectre.admin.batchtask.strategy.LoadStrategy;
import com.xhqb.spectre.admin.batchtask.utils.BatchTaskUtils;
import com.xhqb.spectre.admin.batchtask.validate.ContentValidator;
import com.xhqb.spectre.admin.batchtask.validate.ValidateContext;
import com.xhqb.spectre.admin.batchtask.validate.ValidateResult;
import com.xhqb.spectre.admin.batchtask.validate.ValidateResultMerger;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.BatchTaskParamDTO;
import com.xhqb.spectre.admin.model.vo.BatchTaskUploadVO;
import com.xhqb.spectre.admin.model.vo.batchtask.CheckResultVO;
import com.xhqb.spectre.admin.model.vo.batchtask.ParamItemVO;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.mapper.SignMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.OrderComparator;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 批量任务工厂
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
@Configuration
@Slf4j
public class BatchTaskFactory {

    /**
     * 文件上传异步处理线程池
     */
    private static final ExecutorService BATCH_UPLOAD_HANDLER_POOL = new ThreadPoolExecutor(
            1,
            40,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1024),
            new ThreadFactoryBuilder().setNameFormat("batch-upload-pool-%d").build(),
            new ThreadPoolExecutor.AbortPolicy()
    );
    /**
     * 文件加载策略列表
     */
    private final List<LoadStrategy> loadStrategyList;
    /**
     * 文件解析列表
     */
    private final List<ContentParser> contentParserList;
    /**
     * 内容验证过滤器
     */
    private final List<ContentValidator> contentValidatorList;
    /**
     * 检测结果截取数据的数量
     */
    private final int checkResultCount = BatchTaskConstants.Commons.CHECK_RESULT_COUNT;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private WriteUploadResultHandler writeUploadResultHandler;
    @Autowired
    private SignMapper signMapper;
    @Resource
    private BatchDetectSaveHandler batchDetectSaveHandler;
    @Autowired
    private TplMapper tplMapper;

    public BatchTaskFactory(ObjectProvider<List<LoadStrategy>> loadStrategyProvider,
                            ObjectProvider<List<ContentParser>> contentParserProvider,
                            ObjectProvider<List<ContentValidator>> contentValidatorProvider) {
        this.loadStrategyList = loadStrategyProvider.getIfAvailable();
        this.contentParserList = contentParserProvider.getIfAvailable();
        this.contentValidatorList = contentValidatorProvider.getIfAvailable();
        if (Objects.nonNull(this.contentValidatorList)) {
            // 排序，保证验证器按照系统设计的优先级执行
            OrderComparator.sort(this.contentValidatorList);
        }
    }

    /**
     * 异步处理
     * <p>
     * 异步处理完成之后，会根据处理数据结果长度进行判断，若需要进行分片，则分片存入redis缓存，
     * 分片的原因是，当上传数据量特别大，并且无效数量超级多时，数据写redis会超时
     *
     * @param fileLoadContext
     * @param originalFilename
     * @param signId
     * @param phoneStatus      手机状态检测  多值使用逗号分割  (空号,停机)
     * @param smsTypeCode      短信类型 主要用于一些规则校验判断
     * @return 返回异步任务ID
     */
    public Map<String, String> asyncHandler(FileLoadContext fileLoadContext, String originalFilename, Integer signId, String phoneStatus, String smsTypeCode, Integer tplId) {
        // 获取到签名名称
        String signName = this.getSignName(signId);
        TplDO tplDO = this.getTpl(tplId);
        String fileName = originalFilename;
        String taskNo = DigestUtils.md5Hex(UUID.randomUUID() + fileName);
        // 记录异步处理任务
        stringRedisTemplate.opsForHash().put(RedisKeys.BatchTaskKeys.BATCH_TASK_UPLOAD_FLAG_HASH_KEY, taskNo, taskNo);
        BATCH_UPLOAD_HANDLER_POOL.execute(() -> {
            List<String> phoneStatusList = null;
            if (StringUtils.isNotBlank(phoneStatus)) {
                // 根据逗号进行分割处理
                phoneStatusList = Arrays.stream(StringUtils.split(phoneStatus, ",")).collect(Collectors.toList());
            }
            try {
                long start = System.currentTimeMillis();
                BatchTaskUploadVO uploadVO = this.handler(BatchTaskConstants.StrategyNamed.FILE_STRATEGY_NAME, fileLoadContext, signName, phoneStatusList, smsTypeCode, tplDO);
                UploadResultContext context = UploadResultContext.builder()
                        .taskNo(taskNo)
                        .data(uploadVO)
                        .build();
                // 上传结果写入缓存 用于前端页面查询上传结果
                writeUploadResultHandler.handler(context);
                log.info("群发任务文件异步上传处理总耗时 = {},taskNo = {},fileName = {}", (System.currentTimeMillis() - start), taskNo, fileName);
            } catch (BizException e) {
                log.warn("文件上传处理失败, taskNo={}, fileName = {}, errCode = {}, errMsg = {}",
                        taskNo, fileName, e.getCode(), e.getMsg());
            } catch (Exception e) {
                log.error("文件上传处理失败,taskNo = {}, fileName = {}", taskNo, fileName, e);
            } finally {
                // 移除标记
                stringRedisTemplate.opsForHash().delete(RedisKeys.BatchTaskKeys.BATCH_TASK_UPLOAD_FLAG_HASH_KEY, taskNo);
                UploadFileUtils.deleteFile(fileLoadContext.getFile());
            }
        });
        HashMap<String, String> mapResult = Maps.newHashMap();
        mapResult.put("requestId", taskNo);
        return mapResult;
    }


    /**
     * 1.通过策略名称获取具体的策略对象
     * 2.通过策略可以获取到解析的上下文信息  ParseContext
     * 3.解析文件内容
     * 4.做数据校验操作
     * 5.已经获取到解析之后的所有数据了
     * 6.构建暴露给前端定义好的格式
     *
     * @param strategyNamed   策略名称
     * @param request         上传的内容信息
     * @param <T>             具体上传的文件信息泛型
     * @param signName        签名名称
     * @param phoneStatusList 手机状态验证列表
     * @param smsTypeCode     短信类型
     * @return 返回上传结果
     */
    public <T> BatchTaskUploadVO handler(String strategyNamed, T request, String signName, List<String> phoneStatusList, String smsTypeCode, TplDO tplDO) {
        long begin = System.currentTimeMillis();
        long start = System.currentTimeMillis();
        String fileName = null;
        try {
            // 获取到加载策略
            LoadStrategy loadStrategy = this.getLoadStrategy(strategyNamed);
            // 通过加载策略获取到解析内容的上下文对象
            ParseContext parseContext = loadStrategy.load(request);
            // 设置签名
            parseContext.setSignName(signName);
            // 设置手机状态校验列表标记
            parseContext.setPhoneStatusList(phoneStatusList);
            fileName = parseContext.getFileName();
            log.info("加载策略获取到解析内容的上下文对象耗时 = {},fileName = {}", (System.currentTimeMillis() - start), fileName);
            start = System.currentTimeMillis();
            // 获取到具体的解析器
            ContentParser contentParser = this.getContentParser(parseContext);
            // 获取到解析到的文件内容
            List<ParseResult> parseResultList = contentParser.parse(parseContext);
            log.info("获取到解析文件内容耗时 = {},内容总数 = {},fileName = {}", (System.currentTimeMillis() - start), BatchTaskUtils.getUploadContentCount(parseResultList), fileName);

            List<ValidateResult> validateResultList = new ArrayList<>(parseResultList.size());
            for (ParseResult parseResult : parseResultList) {
                start = System.currentTimeMillis();
                // 验证结果
                ValidateResult validateResult = this.validate(parseResult, smsTypeCode, tplDO);
                validateResultList.add(validateResult);
                log.info("文件内容验证结果耗时 = {}, contentType = {},fileName={}", (System.currentTimeMillis() - start), parseResult.getContentType(), fileName);
                // 保存文件检测结果 2022-02-22
                batchDetectSaveHandler.save(validateResult, parseResult);
            }

            start = System.currentTimeMillis();
            ValidateResultMerger resultMerger = new ValidateResultMerger();
            ValidateResult validateResult = resultMerger.merge(validateResultList, parseResultList);
            // 如果该值不为null时，表示当前为cid和mobile共存模式
            BatchTaskParamDTO mergeTaskParamDTO = resultMerger.getBatchTaskParamDTO();
            // 构建暴露给前端定义好的格式
            BatchTaskUploadVO batchTaskUploadVO = this.buildBatchTaskUploadResult(validateResult, parseResultList, mergeTaskParamDTO);
            log.info("构建响应结果对象耗时 = {},fileName = {}", (System.currentTimeMillis() - start), fileName);
            return batchTaskUploadVO;
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            log.info("批量任务文件上传处理总耗时 = {},fileName = {}", (System.currentTimeMillis() - begin), fileName);
        }
    }

    /**
     * 根据策略名称获取策略对象
     *
     * @param strategyNamed
     * @return
     */
    private LoadStrategy getLoadStrategy(String strategyNamed) {
        for (LoadStrategy loadStrategy : this.loadStrategyList) {
            if (StringUtils.equalsIgnoreCase(strategyNamed, loadStrategy.named())) {
                return loadStrategy;
            }
        }
        throw new BizException("不支持的策略名称 = " + strategyNamed);
    }

    /**
     * 获取到文件内容解析器
     *
     * @param parseContext
     * @return
     */
    private ContentParser getContentParser(ParseContext parseContext) {
        for (ContentParser contentParser : this.contentParserList) {
            if (contentParser.supports(parseContext)) {
                return contentParser;
            }
        }
        throw new BizException("不支持的文件类型 = " + parseContext.getFileName());
    }

    /**
     * 做数据验证处理
     *
     * @param parseResult
     * @param smsTypeCode
     * @return
     */
    private ValidateResult validate(ParseResult parseResult, String smsTypeCode, TplDO tplDO) {
        // 验证结果
        ValidateResult validateResult = new ValidateResult();
        //  验证上下文信息
        ValidateContext validateContext = new ValidateContext(parseResult, tplDO);
        for (ContentValidator contentValidator : this.contentValidatorList) {
            contentValidator.validate(validateContext, validateResult);
        }
        List<ContentItem> validateList = validateContext.getValidateList();
        if (CommonUtil.isEmpty(validateList)) {
            validateList = Lists.newArrayList();
        }
        validateResult.setValidList(validateList);
        return validateResult;
    }

    /**
     * 构建上传文件响应结果
     *
     * @param validateResult
     * @param parseResultList
     * @param mergeTaskParamDTO
     * @return
     */
    private BatchTaskUploadVO buildBatchTaskUploadResult(ValidateResult validateResult, List<ParseResult> parseResultList, BatchTaskParamDTO mergeTaskParamDTO) {
        // 进入到该方法里的数据信息 都属于有效数据信息 不会存在为null的情况
        // 构建文件检测结果
        CheckResultVO checkResultVO = this.buildCheckResultVO(validateResult, parseResultList.get(0));
        // 构建上传响应信息
        BatchTaskUploadVO batchTaskUploadVO = new BatchTaskUploadVO();
        batchTaskUploadVO.setCheckResult(checkResultVO);

        // 如果该值不为null时，表示当前为cid和mobile共存处理
        if (Objects.nonNull(mergeTaskParamDTO)) {
            batchTaskUploadVO.setTaskParamItem(mergeTaskParamDTO);
            return batchTaskUploadVO;
        }

        // ==============如下逻辑为原有处理方式==========
        // 单关键字处理模式
        ParseResult parseResult = parseResultList.get(0);
        // 当前数据是否是cid类型
        // 如果是cid类型 那么则需要为数据添加mobile参数
        boolean isCidType = StringUtils.equalsIgnoreCase(parseResult.getContentType(), BatchTaskConstants.DataType.CID);
        List<String> titleList = parseResult.getTitleList();

        List<ContentItem> validList = validateResult.getValidList();
        if (Objects.nonNull(validList)) {
            // 转换成参数列表
            List<ParamItemVO> paramList = validList.stream().map(s -> {
                ParamItemVO paramItemVO = buildParamItem(titleList, s);
                if (isCidType) {
                    // 如果属于cid类型 则添加手机号码
                    paramItemVO.put(BatchTaskConstants.DataType.MOBILE, validateResult.getMobile(s.getContent()));
                    // 如果属于cid类型 也需要添加一下完件信息
                    paramItemVO.put(BatchTaskConstants.DataType.APPLY_LOAN_RESULT, validateResult.getApplyLoanResult(s.getContent()));
                }
                return paramItemVO;
            }).collect(Collectors.toList());
            BatchTaskParamDTO batchTaskParamDTO = new BatchTaskParamDTO();
            batchTaskParamDTO.setFileMd5(parseResult.getFileMd5());
            batchTaskParamDTO.setParamList(paramList);
            batchTaskUploadVO.setTaskParamItem(batchTaskParamDTO);
        }
        return batchTaskUploadVO;
    }

    /**
     * 构建文件响应结果
     *
     * @param validateResult
     * @param parseResult
     * @return
     */
    private CheckResultVO buildCheckResultVO(ValidateResult validateResult, ParseResult parseResult) {
        return CheckResultVO.builder()
                // 文件存储地址
                .url(parseResult.getSaveUrl())
                // 文件名称
                .fileName(parseResult.getFileName())
                // 文件类型
                .fileType(parseResult.getFileType())
                // 文件MD5值
                .fileMd5(parseResult.getFileMd5())
                // 数据类型mobile或者cid
                .dataType(parseResult.getContentType())
                // 有效数量
                .validCount(CommonUtil.getCollectionSize(validateResult.getValidList()))
                // 无效数据量
                .badCount(CommonUtil.getCollectionSize(validateResult.getBadList()))
                // 无效数据信息
                .badInfo(contentItemToList(validateResult.getBadList(), checkResultCount))
                // 缺少参数数据数量
                .missCount(CommonUtil.getCollectionSize(validateResult.getMissList()))
                // 缺少参数数据信息
                .missInfo(contentItemToList(validateResult.getMissList(), checkResultCount))
                // 重复数据数量
                .repeatCount(CommonUtil.getCollectionSize(validateResult.getRepeatList()))
                // 重复数据信息
                .repeatInfo(contentItemToList(validateResult.getRepeatList(), checkResultCount))
                // 空号
                .phoneEmptyCount(CommonUtil.getCollectionSize(validateResult.getPhoneEmptyList()))
                // 空号数据
                .phoneEmptyInfo(contentItemToList(validateResult.getPhoneEmptyList(), checkResultCount))
                // 停机
                .phoneHaltCount(CommonUtil.getCollectionSize(validateResult.getPhoneHaltList()))
                // 停机数据
                .phoneHaltInfo(contentItemToList(validateResult.getPhoneHaltList(), checkResultCount))
                .build();
    }

    /**
     * 构建参数对象
     *
     * @param titleList
     * @param contentItem
     * @return
     */
    private ParamItemVO buildParamItem(List<String> titleList, ContentItem contentItem) {
        ParamItemVO paramItemVO = ParamItemVO.newParamItem();
        paramItemVO.mapping(titleList, contentItem.getParamList());
        return paramItemVO;
    }

    /**
     * 验证内容Item信息转换成List
     *
     * @param contentItemList
     * @param count           只获取内容的数量
     * @return
     */
    private List<String> contentItemToList(List<ContentItem> contentItemList, Integer count) {
        if (Objects.isNull(contentItemList) || contentItemList.isEmpty()) {
            return new ArrayList<>();
        }

        if (Objects.isNull(count) || count >= contentItemList.size()) {
            return contentItemList.stream().map(ContentItem::getContent).collect(Collectors.toList());
        }
        List<String> resultList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            resultList.add(contentItemList.get(i).getContent());
        }
        return resultList;
    }

    /**
     * 获取到签名名称
     *
     * @param signId
     * @return
     */
    private String getSignName(Integer signId) {
        if (Objects.isNull(signId)) {
            return null;
        }

        try {
            SignDO signDO = signMapper.selectByPrimaryKey(signId);
            if (Objects.nonNull(signDO)) {
                return signDO.getName();
            }
            log.info("未查询到签名信息,signId = {}", signId);
        } catch (Exception e) {
            // ignore ex
        }
        return null;
    }

    private TplDO getTpl(Integer tplId) {
        if (Objects.isNull(tplId)) {
            return null;
        }
        return tplMapper.selectByPrimaryKey(tplId);
    }
}
