package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.AddSignDTO;
import com.xhqb.spectre.admin.model.dto.UpdateSignDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.SignVO;
import com.xhqb.spectre.admin.service.SignService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.SignQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 11:19
 * @Description:
 */
@RestController
@RequestMapping("/sign")
@Slf4j
public class SignController {

    @Autowired
    private SignService signService;

    /**
     * 查询签名列表
     *
     * @param signQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult querySignList(@ModelAttribute SignQuery signQuery, Integer pageNum, Integer pageSize) {
        signQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<SignVO> commonPager = signService.listByPage(signQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询签名详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult querySignInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(signService.getById(id));
    }

    /**
     * 添加签名
     *
     * @param addSignDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_SIGN)
    public AdminResult createSign(@RequestBody AddSignDTO addSignDTO) {
        log.info("addSignDTO: {}", JSON.toJSONString(addSignDTO));
        signService.create(addSignDTO);
        return AdminResult.success();
    }

    /**
     * 修改签名
     *
     * @param id
     * @param updateSignDTO
     * @return
     */
    @PutMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_SIGN)
    public AdminResult updateSign(@PathVariable("id") Integer id, @RequestBody UpdateSignDTO updateSignDTO) {
        updateSignDTO.setId(id);
        log.info("updateSignDTO: {}", JSON.toJSONString(updateSignDTO));
        signService.update(updateSignDTO);
        return AdminResult.success();
    }

    /**
     * 启用签名
     *
     * @param id
     * @return
     */
    @PostMapping("/enable/{id}")
    @LogOpTime(OpLogConstant.MODULE_SIGN)
    public AdminResult enableSign(@PathVariable("id") Integer id) {
        log.info("enableSign, id: {}", id);
        signService.enable(id);
        return AdminResult.success();
    }

    /**
     * 停用签名
     *
     * @param id
     * @return
     */
    @PostMapping("/disable/{id}")
    @LogOpTime(OpLogConstant.MODULE_SIGN)
    public AdminResult disableSign(@PathVariable("id") Integer id) {
        log.info("disableSign, id: {}", id);
        signService.disable(id);
        return AdminResult.success();
    }

    /**
     * 删除签名
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_SIGN)
    public AdminResult deleteSign(@PathVariable("id") Integer id) {
        log.info("deleteSign, id: {}", id);
        signService.delete(id);
        return AdminResult.success();
    }

    /**
     * 查询签名枚举
     *
     * @param status
     * @return
     */
    @GetMapping("/enum")
    public AdminResult queryEnum(Integer status) {
        return AdminResult.success(signService.queryEnum(status));
    }
}
