package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.model.dto.ClientChannelAccountDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ClientChannelAccountVO;
import com.xhqb.spectre.admin.service.ClientChannelAccountService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ClientChannelAccountQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 网关分配账号
 *
 * <AUTHOR>
 * @date 2021/10/26
 */
@RestController
@RequestMapping("/clientChannelAccount")
@Slf4j
public class ClientChannelAccountController {


    @Resource
    private ClientChannelAccountService clientChannelAccountService;

    /**
     * 查询网关账号列表
     *
     * @param clientChannelAccountQuery 网关账号查询条件
     * @param pageNum                   当前页码
     * @param pageSize                  一页显示的记录数
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(ClientChannelAccountQuery clientChannelAccountQuery, Integer pageNum, Integer pageSize) {
        clientChannelAccountQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<ClientChannelAccountVO> commonPager = clientChannelAccountService.listByPage(clientChannelAccountQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(clientChannelAccountService.getById(id));
    }


    /**
     * 添加网关账号
     *
     * @param clientChannelAccountDTO 新增的网关账号信息
     * @return
     */
    @PostMapping("")
    public AdminResult createInfo(@RequestBody ClientChannelAccountDTO clientChannelAccountDTO) {
        log.info("create clientChannelAccountDTO = {}", JSON.toJSONString(clientChannelAccountDTO));
        clientChannelAccountService.create(clientChannelAccountDTO);
        return AdminResult.success();
    }

    /**
     * 更新网关账号
     *
     * @param id                      网关账号(主键)
     * @param clientChannelAccountDTO 修改的网关账号内容
     * @return
     */
    @PutMapping("/{id}")
    public AdminResult updateInfo(@PathVariable("id") Integer id, @RequestBody ClientChannelAccountDTO clientChannelAccountDTO) {
        log.info("update clientChannelAccountDTO = {}, id = {}", JSON.toJSONString(clientChannelAccountDTO), id);
        clientChannelAccountService.update(id, clientChannelAccountDTO);
        return AdminResult.success();
    }

    /**
     * 删除网关账号
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public AdminResult deleteInfo(@PathVariable("id") Integer id) {
        log.info("delete clientChannelAccount id = {}", id);
        return clientChannelAccountService.delete(id);
    }

    /**
     * cmppserver账号上线
     *
     * @param id
     * @return
     */
    @PostMapping("/online/{id}")
    public AdminResult online(@PathVariable("id") Integer id) {
        log.info("online clientChannelAccount id = {}", id);
        return clientChannelAccountService.online(id);
    }

    /**
     * cmppserver账号下线
     *
     * @param id
     * @return
     */
    @PostMapping("/offline/{id}")
    public AdminResult offline(@PathVariable("id") Integer id) {
        log.info("offline clientChannelAccount id = {}", id);
        return clientChannelAccountService.offline(id);
    }
}
