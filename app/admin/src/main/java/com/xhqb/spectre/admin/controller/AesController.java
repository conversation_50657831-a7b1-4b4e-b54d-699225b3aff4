package com.xhqb.spectre.admin.controller;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.model.dto.DecryptContentDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.util.AesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@RestController
@RequestMapping("/aes")
@Slf4j
public class AesController {
    @PostMapping("/decrypt")
    public AdminResult decrypt(@RequestBody DecryptContentDTO decryptContentDTO) {
        log.info("encryptData:{}", JsonLogUtil.toString(decryptContentDTO));
        if(Objects.isNull(decryptContentDTO)){
            return AdminResult.error("参数不能为空");
        }
        return AdminResult.success(AesUtil.decryptFromString(decryptContentDTO.getEncryptMobile(), Mode.CBC, Padding.ZeroPadding));
    }
}
