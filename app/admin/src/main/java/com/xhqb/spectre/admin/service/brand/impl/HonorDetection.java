package com.xhqb.spectre.admin.service.brand.impl;

import com.xhqb.spectre.admin.service.brand.BrandDetectionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 荣耀品牌
 */
@Component
@Slf4j
public class HonorDetection implements BrandDetectionStrategy {
    @Override
    public boolean isMatch(String originBrand, String os) {
        return originBrand.toLowerCase().contains("honor");
    }

    @Override
    public String getMappedBrand() {
        return "honor";
    }
}
