package com.xhqb.spectre.admin.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.admin.bidata.mapper.BidataSendStatMapper;
import com.xhqb.spectre.admin.model.dto.TestContentTaskDTO;
import com.xhqb.spectre.admin.readonly.mapper.SmsOrderReadonlyMapper;
import com.xhqb.spectre.admin.service.AutoTestTaskInvokeService;
import com.xhqb.spectre.admin.service.FeiShuAlert;
import com.xhqb.spectre.admin.service.TplService;
import com.xhqb.spectre.admin.service.test.tool.TestContentTaskService;
import com.xhqb.spectre.common.dal.entity.*;
import com.xhqb.spectre.common.dal.entity.test.tool.TestContentTaskDO;
import com.xhqb.spectre.common.dal.mapper.*;
import com.xhqb.spectre.common.dal.query.AutoTestBindQuery;
import com.xhqb.spectre.common.dal.query.AutoTestTaskQuery;
import com.xhqb.spectre.common.dal.query.TestContentTaskQuery;
import com.xhqb.spectre.common.dal.query.TplQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AutoTestTaskInvokeServiceImpl implements AutoTestTaskInvokeService {

    private static final String HOLIDAY_HOST_1 = "https://date.appworlds.cn";
    private static final String HOLIDAY_HOST_2 = "https://timor.tech/api/holiday/year/%s";

    @Resource
    private AutoTestTaskMapper autoTestTaskMapper;
    @Resource
    private AutoTestBindMapper autoTestBindMapper;
    @Resource
    private TestContentTaskService testContentTaskService;
    @Resource
    private TestWhiteMapper testWhiteMapper;
    @Resource
    private BidataSendStatMapper bidataSendStatMapper;
    @Resource
    private FeiShuAlert feiShuAlert;
    @Resource
    private SqlSessionFactory spectreReadSqlSessionFactory;
    @Resource
    private TestContentTaskRecordMapper testContentTaskRecordMapper;
    @Resource
    private TestContentTaskMapper testContentTaskMapper;
    @Resource
    private TplMapper tplMapper;
    @Resource
    private TplContentMapper tplContentMapper;
    @Resource
    private TplService tplService;

    @Value("${spectre.admin.auto.test.omini.strategy.id}")
    private String feishuAlertStrategyId;

    @Override
    public void invoke() {
        DateTime now = DateTime.now();
        try {
            AutoTestTaskQuery query = new AutoTestTaskQuery();
            query.setEnable(1);
            List<AutoTestTaskDO> autoTestTaskList = this.autoTestTaskMapper.selectByQuery(query);

            List<BrandInfoDO> brandInfoDOS = this.testWhiteMapper.selectByBrandInfo();
            if (Objects.isNull(brandInfoDOS)) {
                log.warn("No test cell phone number is available.");
                return;
            }
            Map<String, Integer> testPhoneInfo = brandInfoDOS.stream().collect(Collectors.toMap(BrandInfoDO::getBrand, BrandInfoDO::getCount));

            boolean isWorkDay = this.isWorkDay();
            List<AutoTestTaskDO> taskList = autoTestTaskList.stream()
                    // 过滤测试周期外的任务
                    .filter(this::filterTestCycle)
                    // 自然日、工作日需要区分
                    .filter(item -> this.filterWorkday(item, isWorkDay))
                    // 根据测试频率再过滤下
                    .filter(this::filterFrequency)
                    .collect(Collectors.toList());

            if (taskList.isEmpty()) {
                log.info("No tasks need to be executed.");
                return;
            }
            // 创建任务单
            taskList.forEach(item -> this.builderContentTask(item, testPhoneInfo));
            log.info("Auto test job invoke end. time consume {}ms", (System.currentTimeMillis() - now.getTime()));
        } catch (Exception e) {
            log.warn("Auto test job invoke error.", e);
        }
    }

    private void builderContentTask(AutoTestTaskDO autoTestTask, Map<String, Integer> testPhoneInfo) {
        try {
            DateTime now = DateTime.now();
            String type = autoTestTask.getType();
            List<String> tplCodeList = new ArrayList<>();

            // 获取top模板
            if (type.equals("0")) {
                tplCodeList.addAll(this.bidataSendStatMapper.selectTplCodeByStatDateAndCount(DateUtil.yesterday().toDateStr(), Integer.parseInt(autoTestTask.getBindTpl())));
            } else if (type.equals("1")) {
                tplCodeList.addAll(Arrays.asList(StringUtils.split(autoTestTask.getBindTpl(), ",")));
            }

            if (tplCodeList.isEmpty()) {
                log.info("No template is available.");
                return;
            }

            // 根据模板名称查找模板内容
            Map<String, String> tplContentMap = new HashMap<>();

            try (SqlSession sqlSession = spectreReadSqlSessionFactory.openSession(ExecutorType.BATCH)) {
                SmsOrderReadonlyMapper mapper = sqlSession.getMapper(SmsOrderReadonlyMapper.class);
                for (String tplCode : tplCodeList) {
                    SmsOrderDO smsOrderDO = mapper.selectLastByTplCode(tplCode, DateUtil.offsetDay(now, -3).getTime() / 1000, now.getTime() / 1000);
                    if (Objects.nonNull(smsOrderDO)) {
                        tplContentMap.put(tplCode, smsOrderDO.getSignName() + smsOrderDO.getContent());
                    }
                }
            }

            if (tplContentMap.isEmpty()) {
                log.info("No tpl content available.");
                return;
            }
            TestContentTaskDTO contentTaskDTO = new TestContentTaskDTO();
            contentTaskDTO.setName(StrUtil.format("AutoTestTask_{}_{}", autoTestTask.getId(), autoTestTask.getName()));
            contentTaskDTO.setContent(String.join("\n", tplContentMap.values()));
            contentTaskDTO.setBrandConfig(JSON.toJSONString(testPhoneInfo));
            String contentTaskId = this.testContentTaskService.add(contentTaskDTO);

            AutoTestBindDO entity = new AutoTestBindDO();
            entity.setAutoTestTaskId(autoTestTask.getId());
            entity.setTestContentTaskId(contentTaskId);
            entity.setTplCodes(String.join(",", tplCodeList));
            entity.setCreateTime(now);
            this.autoTestBindMapper.insert(entity);
            this.testContentTaskService.executeTask(contentTaskId, MapUtil.reverse(tplContentMap));
        } catch (Exception e) {
            log.warn("Auto test job inner invoke error.", e);
        }
    }

    private boolean filterFrequency(AutoTestTaskDO entity) {
        String testFrequency = entity.getTestFrequency();
        if (StringUtils.isBlank(testFrequency)) {
            return false;
        }
        int i = Integer.parseInt(testFrequency.substring(0, 1));
        Date date = this.autoTestBindMapper.selectLastDateByAutoTestTaskId(entity.getId());
        if (Objects.isNull(date)) {
            return true;
        }
        return DateUtil.beginOfDay(DateUtil.offsetDay(date, i)).isBeforeOrEquals(DateUtil.beginOfDay(DateTime.now()));
    }

    private boolean filterWorkday(AutoTestTaskDO entity, boolean isWorkDay) {
        if (isWorkDay) {
            return true;
        }
        return entity.getWorkDay().equals(0);
    }

    private boolean filterTestCycle(AutoTestTaskDO entity) {
        String testCycle = entity.getTestCycle();
        if (StringUtils.isBlank(testCycle)) {
            return false;
        }
        String[] split = testCycle.split(",");
        DateTime startDate = DateUtil.parseDate(split[0]);
        DateTime endDate = DateUtil.endOfDay(DateUtil.parseDate(split[1]));
        return DateUtil.isIn(DateTime.now(), startDate, endDate);
    }

    public void invokeSubFlow() {
        log.info("start sub flow, invoke now.");
        try {
            DateTime now = DateTime.now();
            DateTime startTime = DateUtil.beginOfDay(now);
            DateTime endTime = DateUtil.endOfDay(now);

            // 当天的数据
            AutoTestBindQuery bindQuery = new AutoTestBindQuery();
            bindQuery.setStartTime(startTime);

            bindQuery.setEndTime(endTime);
            List<AutoTestBindDO> currentBindList = this.autoTestBindMapper.selectByQuery(bindQuery);
            List<String> tastIdList = currentBindList.stream().map(AutoTestBindDO::getTestContentTaskId).collect(Collectors.toList());

            List<TestContentStatDO> todayStatList = this.testContentTaskRecordMapper.statByTaskIds(tastIdList, null, startTime, endTime);
            if (CollectionUtils.isEmpty(todayStatList)) {
                log.info("today data is null, break invoke.");
                return;
            }
            JSONObject dataMap = new JSONObject();

            // 数据封装
            JSONArray listJA = new JSONArray();
            for (TestContentStatDO testContentStatDO : todayStatList) {
                if (StringUtils.isBlank(testContentStatDO.getTplCode())) {
                    continue;
                }
                String tplCode = testContentStatDO.getTplCode();
                Integer reportCount = ObjectUtils.defaultIfNull(testContentStatDO.getReportCount(), 0);
                Integer sendCount = ObjectUtils.defaultIfNull(testContentStatDO.getSendCount(), 0);
                Integer reportSuccessCount = ObjectUtils.defaultIfNull(testContentStatDO.getReportSuccessCount(), 0);

                JSONObject tmpJO = new JSONObject();

                List<TestContentStatDO> testContentStatDOS = this.testContentTaskRecordMapper.statByTaskIds(null, testContentStatDO.getTplCode(), null, startTime);

                AtomicInteger historyReportCount = new AtomicInteger(0);
                AtomicInteger historyReportSuccessCount = new AtomicInteger(0);
                AtomicInteger historySendCount = new AtomicInteger(0);
                for (TestContentStatDO contentStatDO : testContentStatDOS) {
                    if (Objects.nonNull(contentStatDO.getReportCount())) {
                        historyReportCount.getAndAdd(contentStatDO.getReportCount());
                    }
                    if (Objects.nonNull(contentStatDO.getReportSuccessCount())) {
                        historyReportSuccessCount.getAndAdd(contentStatDO.getReportSuccessCount());
                    }
                    if (Objects.nonNull(contentStatDO.getSendCount())) {
                        historySendCount.getAndAdd(contentStatDO.getSendCount());
                    }

                }
                TestContentTaskQuery testContentTaskQuery = new TestContentTaskQuery();
                testContentTaskQuery.setContent(testContentStatDO.getContent());
                testContentTaskQuery.setTaskIdList(tastIdList);
                List<TestContentTaskDO> testContentTaskDOS = this.testContentTaskMapper.selectByQuery(testContentTaskQuery);

                Set<String> mobileType = new HashSet<>();
                AtomicInteger totalCount = new AtomicInteger(0);
                testContentTaskDOS.stream().map(TestContentTaskDO::getBrandConfig).map(JSON::parseObject).forEach(item -> {
                    mobileType.addAll(item.keySet());
                    for (Object value : item.values()) {
                        totalCount.addAndGet(Integer.parseInt(String.valueOf(value)));
                    }
                });
                TplQuery tplQuery = new TplQuery();
                tplQuery.setCode(tplCode);
                List<TplDO> tplDOS = this.tplMapper.selectByQuery(tplQuery);
                TplDO tplDO = tplDOS.get(0);
                this.tplService.setCreatorByReportId(tplDO);

                tmpJO.put("tplCode", ObjectUtils.defaultIfNull(tplCode, ""));
                tmpJO.put("creator", tplDO.getCreator());
                tmpJO.put("testMobileTypes", mobileType);
                tmpJO.put("testMobileCount", totalCount.get());

                tmpJO.put("currentReachRate", this.div(reportCount, sendCount));
                tmpJO.put("currentReportRate", this.div(reportSuccessCount, reportCount));
                tmpJO.put("historyReachRate", this.div(historyReportCount.get(), historySendCount.get()));
                tmpJO.put("historyReportRate", this.div(historyReportSuccessCount.get(), historyReportCount.get()));

                listJA.add(tmpJO);
            }
            if (listJA.isEmpty()) {
                log.info("not data, break alert.");
                return;
            }
            dataMap.put("list", listJA);
            dataMap.put("testTime", now.toString());
            // 发飞书告警
            this.feiShuAlert.sendFeiShuAlert(dataMap, this.feishuAlertStrategyId);
        } catch (Exception e) {
            log.warn("Auto test sub flow invoke error.", e);
        }
    }

    private double div(int num1, int num2) {
        if (num2 == 0) {
            return 0;
        }
        return NumberUtil.div(num1, num2) * 100;
    }

    private boolean isWorkDay() {
        DateTime now = DateTime.now();
        try (HttpResponse execute = HttpUtil.createGet(HOLIDAY_HOST_1).execute()) {
            Assert.isTrue(execute.isOk(), "请求失败");
            JSONObject responseJO = JSON.parseObject(execute.body());
            Assert.isTrue(responseJO.getInteger("code").equals(200), "请内部失败");
            return responseJO.getJSONObject("data").getBoolean("work");
        } catch (Exception e) {
            log.warn("request holiday api 1 error", e);
        }

        try (HttpResponse execute = HttpUtil.createGet(String.format(HOLIDAY_HOST_2, now.toString("yyyy-MM"))).execute()) {
            Assert.isTrue(execute.isOk(), "请求失败");
            JSONObject responseJO = JSON.parseObject(execute.body());
            Assert.isTrue(responseJO.getInteger("code").equals(0), "请内部失败");
            JSONObject holidayJO = responseJO.getJSONObject("holiday");
            JSONObject currentDayJO = holidayJO.getJSONObject(now.toString("MM-dd"));
            if (Objects.isNull(currentDayJO)) {
                Week week = now.dayOfWeekEnum();
                return week != Week.SATURDAY && week != Week.SUNDAY;
            }
            return !currentDayJO.getBoolean("holiday");
        } catch (Exception e) {
            log.warn("request holiday api 2 error", e);
        }

        // 兜底，硬编码判断工作日
        Week week = now.dayOfWeekEnum();
        return week != Week.SATURDAY && week != Week.SUNDAY;
    }
}
