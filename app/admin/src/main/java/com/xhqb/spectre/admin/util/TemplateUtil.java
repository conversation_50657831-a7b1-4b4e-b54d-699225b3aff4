package com.xhqb.spectre.admin.util;


import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 模板解析工具类
 */
public class TemplateUtil {


    static String TPL_CONTENT_PARAM2 = "(\\$\\{)([\\s\\S]*?)}";

    static String TPL_PARAM_SYMBOL = "[*]";

    //模板参数格式包含两种（1：[*]；2：${XXX}）
    static String TPL_PARAM_SYMBOL_REGEX = "\\[\\*]";


    /**
     * 解析并替换 [*] 形式的占位符。
     *
     * @param content 模板内容
     */
    public static String parseSequentialPlaceholders(String content, String... args) {

        // 原模板中包含 %%，则替换为 %, 兼容老的模板, 老模板内容更新后删除
        content = content.replaceAll("(?<!%)%%(?!%)", "%");

        // ${key} 形式的占位符替换为 [*]
        if (countKeyedPlaceholders(content) > 0) {
            content = content.replaceAll(TPL_CONTENT_PARAM2, TPL_PARAM_SYMBOL);
        }

        // 计算占位符个数
        int placeholderCount = countSequentialPlaceholders(content);

        // 如果不包含占位符则返回原值
        if (placeholderCount == 0) {
            return content;
        }

        // 替换占位符
        for (String param : args) {
            content = content.replaceFirst(TPL_PARAM_SYMBOL_REGEX,
                    Matcher.quoteReplacement(param));
        }
        return content;
    }

    /**
     * 解析并替换 ${key} 形式的占位符。
     */
    public static String parseKeyedPlaceholders(String content, Map<String, String> keyValuePairs) {
        StringBuilder sb = new StringBuilder(content);
        int placeholderStart;
        while ((placeholderStart = sb.indexOf("${")) != -1) {
            int placeholderEnd = sb.indexOf("}", placeholderStart);
            if (placeholderEnd == -1) break;

            String key = sb.substring(placeholderStart + 2, placeholderEnd);
            String replacement = keyValuePairs.getOrDefault(key, "");
            sb.replace(placeholderStart, placeholderEnd + 1, replacement);
        }
        return sb.toString();
    }

    /**
     * 判断是否可以覆盖占位符
     *
     * @param content
     * @param keyValuePairs
     * @return
     */
    public static boolean hasAllPlaceholdersWithPairs(String content, Map<String, String> keyValuePairs) {
        StringBuilder sb = new StringBuilder(content);
        int placeholderStart;

        while ((placeholderStart = sb.indexOf("${")) != -1) {
            int placeholderEnd = sb.indexOf("}", placeholderStart);
            if (placeholderEnd == -1) break;

            String key = sb.substring(placeholderStart + 2, placeholderEnd);
            String replacement = keyValuePairs.getOrDefault(key, "");

            if (replacement.isEmpty()) {
                return false;
            }

            sb.replace(placeholderStart, placeholderEnd + 1, replacement);
        }

        return true;
    }

    /**
     * 计算字符串中 [*] 形式的占位符个数
     *
     * @param content 模板内容
     * @return
     */
    public static int countSequentialPlaceholders(String content) {
        if (content == null) {
            return 0;
        }

        int count = 0;
        int idx = 0;
        while ((idx = content.indexOf(TPL_PARAM_SYMBOL, idx)) != -1) {
            count++;
            idx += TPL_PARAM_SYMBOL.length();
        }
        return count;
    }

    /**
     * 计算字符串中 ${key} 形式的占位符个数
     *
     * @param content 模板内容
     * @return
     */
    public static int countKeyedPlaceholders(String content) {
        if (content == null || content.isEmpty()) {
            return 0;
        }

        // 定义正则表达式
        Pattern pattern = Pattern.compile(TPL_CONTENT_PARAM2);
        Matcher matcher = pattern.matcher(content);

        int count = 0;
        while (matcher.find()) {
            count++;
        }

        return count;
    }

    public static int countAllPlaceholders(String content) {
        int sequentialCount = countSequentialPlaceholders(content);
        int keyCount = countKeyedPlaceholders(content);

        // 两者都为0时返回0
        if (sequentialCount == 0 && keyCount == 0) {
            return 0;
        }

        // 如果两种占位符都存在，返回0
        if (sequentialCount != 0 && keyCount != 0) {
            return sequentialCount + keyCount;
        }

        // 返回存在的占位符类型计数
        return sequentialCount != 0 ? sequentialCount : keyCount;
    }
}
