package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.ShortUrlDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ShortUrlVO;
import com.xhqb.spectre.admin.service.ShortUrlService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ShortUrlQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/11 11:28
 * @Description:
 */
@RestController
@RequestMapping("/shortUrl")
@Slf4j
public class ShortUrlController {

    @Autowired
    private ShortUrlService shortUrlService;

    /**
     * 查询短链列表
     *
     * @param shortUrlQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(@ModelAttribute ShortUrlQuery shortUrlQuery, Integer pageNum, Integer pageSize) {
        shortUrlQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<ShortUrlVO> commonPager = shortUrlService.listByPage(shortUrlQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询短链详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(shortUrlService.queryInfo(id));
    }

    /**
     * 生成短链
     *
     * @param shortUrlDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_SHORT_URL)
    public AdminResult create(@RequestBody ShortUrlDTO shortUrlDTO) {
        log.info("create shortUrl, shortUrlDTO: {}", JSON.toJSONString(shortUrlDTO));
        return AdminResult.success(shortUrlService.create(shortUrlDTO));
    }

    /**
     * 更新短链
     *
     * @param id
     * @param shortUrlDTO
     * @return
     */
    @PutMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_SHORT_URL)
    public AdminResult update(@PathVariable("id") Integer id, @RequestBody ShortUrlDTO shortUrlDTO) {
        log.info("update shortUrl, id: {}, shortUrlDTO: {}", id, JSON.toJSONString(shortUrlDTO));
        return AdminResult.success(shortUrlService.update(id, shortUrlDTO));
    }

    /**
     * 启用短链
     *
     * @param id
     * @return
     */
    @PostMapping("/enable/{id}")
    @LogOpTime(OpLogConstant.MODULE_SHORT_URL)
    public AdminResult enable(@PathVariable("id") Integer id) {
        log.info("enable shortUrl, id: {}", id);
        shortUrlService.enable(id);
        return AdminResult.success();
    }

    /**
     * 停用短链
     *
     * @param id
     * @return
     */
    @PostMapping("/disable/{id}")
    @LogOpTime(OpLogConstant.MODULE_SHORT_URL)
    public AdminResult disable(@PathVariable("id") Integer id) {
        log.info("disable shortUrl, id: {}", id);
        shortUrlService.disable(id);
        return AdminResult.success();
    }

    /**
     * 删除短链
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_SHORT_URL)
    public AdminResult delete(@PathVariable("id") Integer id) {
        log.info("delete shortUrl, id: {}", id);
        shortUrlService.delete(id);
        return AdminResult.success();
    }
}
