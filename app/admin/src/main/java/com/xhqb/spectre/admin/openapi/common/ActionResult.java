package com.xhqb.spectre.admin.openapi.common;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 响应结果
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActionResult<T> implements Serializable {

    /**
     * 响应码
     */
    private String code;
    /**
     * 响应消息
     */
    private String message;
    /**
     * 响应数据
     */
    private T data;

    public ActionResult(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    /**
     * 构建响应对象
     *
     * @param codeDefinition
     * @param <T>
     * @return
     */
    public static <T> ActionResult<T> make(CodeDefinition codeDefinition) {
        return make(codeDefinition, null);
    }

    /**
     * 构建响应对象
     *
     * @param codeDefinition
     * @param data
     * @param <T>
     * @return
     */
    public static <T> ActionResult<T> make(CodeDefinition codeDefinition, T data) {
        return new ActionResult<T>(codeDefinition.getCode(), codeDefinition.getMsg(), data);
    }

    /**
     * 成功响应
     *
     * @param data
     * @param <T>
     * @return
     */
    public static <T> ActionResult<T> success(T data) {
        return make(CodeDefinition.SUCCESS, data);
    }

    /**
     * 成功响应
     *
     * @param <T>
     * @return
     */
    public static <T> ActionResult<T> success() {
        return success(null);
    }


}
