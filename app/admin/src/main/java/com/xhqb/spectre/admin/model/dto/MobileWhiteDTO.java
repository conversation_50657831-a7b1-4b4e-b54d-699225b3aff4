package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 17:24
 * @Description:
 */
@Data
public class MobileWhiteDTO implements Serializable {

    private static final long serialVersionUID = 6088450410430614572L;

    @NotBlank(message = "应用编码不能为空")
    private String appCode;

//    @NotBlank(message = "限流规则类型不能为空")
//    private String cfgType;

    @NotEmpty(message = "手机号不能为空")
    @Size(max = 50, message = "最大支持{max}个手机号")
    private List<String> mobileList;

//    @NotBlank(message = "描述不能为空")
    @Size(max = 256, message = "描述最大为{max}个字符")
    private String description;
}
