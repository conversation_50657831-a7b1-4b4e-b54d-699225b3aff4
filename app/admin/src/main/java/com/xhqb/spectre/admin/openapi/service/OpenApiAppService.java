package com.xhqb.spectre.admin.openapi.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 应用授权服务
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
@Component
public class OpenApiAppService {

    /**
     * key -> appKey ,value -> appSecret
     */
    @Value("#{${spectre.admin.openapi.appMapping:}?:new java.util.HashMap()}")
    private Map<String, String> appMapping;

    /**
     * 通过appKey获取到appSecret
     *
     * @param appKey
     * @return
     */
    public String getAppSecret(String appKey) {
        if (Objects.isNull(appMapping) || appMapping.isEmpty()) {
            return null;
        }

        return appMapping.get(appKey);
    }

    public Map<String, String> getAppMapping() {
        return appMapping;
    }
}
