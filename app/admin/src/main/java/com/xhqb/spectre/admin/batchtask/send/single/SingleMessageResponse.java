package com.xhqb.spectre.admin.batchtask.send.single;

import com.xhqb.spectre.admin.batchtask.send.record.MessageSendFailedRecord;
import com.xhqb.spectre.admin.batchtask.send.record.MessageSendSuccessRecord;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 单条消息响应结果
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
public class SingleMessageResponse implements Serializable {

    /**
     * 总记录数
     */
    private Integer total;
    /**
     * 成功数
     */
    private Integer success;
    /**
     * 失败数
     */
    private Integer failure;

    /**
     * 短信发送状态成功记录
     */
    private List<MessageSendSuccessRecord> successSMSRecord;

    /**
     * 短信发送状态失败记录
     */
    private List<MessageSendFailedRecord> failureSMSRecord;
}
