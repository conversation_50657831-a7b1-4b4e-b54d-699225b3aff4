package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.GatewayUserDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.GatewayUserVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.GatewayUserQuery;

/**
 * 网关账号管理(王建政)
 *
 * <AUTHOR>
 * @date 2021/11/8
 */
public interface GatewayUserService {

    /**
     * 分页查询网关分配账号列表
     *
     * @param gatewayUserQuery
     * @return
     */
    CommonPager<GatewayUserVO> listByPage(GatewayUserQuery gatewayUserQuery);


    /**
     * 根据ID查询网关账号详情
     *
     * @param id
     * @return
     */
    GatewayUserVO getById(Integer id);

    /**
     * 添加网关账号信息
     *
     * @param gatewayUserDTO
     */
    void create(GatewayUserDTO gatewayUserDTO);


    /**
     * 更新网关账号信息
     *
     * @param id
     * @param gatewayUserDTO
     */
    void update(Integer id, GatewayUserDTO gatewayUserDTO);

    /**
     * 删除网关账号信息
     *
     * @param id
     * @return
     */
    AdminResult delete(Integer id);
}
