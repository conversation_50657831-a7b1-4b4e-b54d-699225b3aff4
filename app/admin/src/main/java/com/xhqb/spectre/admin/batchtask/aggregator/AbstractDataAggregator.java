package com.xhqb.spectre.admin.batchtask.aggregator;

import com.xhqb.spectre.admin.batchtask.parse.ContentItem;
import com.xhqb.spectre.admin.batchtask.validate.ValidateContext;
import com.xhqb.spectre.admin.batchtask.validate.ValidateResult;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.shade.com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * 定义具体做聚合操作的方法
 *
 * <AUTHOR>
 * @date 2021/9/28
 */
public abstract class AbstractDataAggregator implements DataAggregator {

    /**
     * @param validateContext
     * @param validateResult
     */
    @Override
    public void aggregate(ValidateContext validateContext, ValidateResult validateResult) {
        AggregatorResult aggregatorResult = this.doAggregate(validateContext.getValidateList(), validateContext, validateResult);
        if (Objects.isNull(aggregatorResult)) {
            throw new BizException("聚合结果数据为空");
        }

        List<ContentItem> badList = aggregatorResult.getBadList();
        if (!CommonUtil.isEmpty(badList)) {
            // badList不为空时 才设置
            // 防止其他的数据聚合器将数据冲掉
            validateResult.setBadList(badList);
        }

        List<ContentItem> phoneEmptyList = aggregatorResult.getPhoneEmptyList();
        if (!CommonUtil.isEmpty(phoneEmptyList)) {
            // 只有手机号码查询聚合器才会设置该列表
            validateResult.setPhoneEmptyList(phoneEmptyList);
        }

        List<ContentItem> phoneHaltList = aggregatorResult.getPhoneHaltList();
        if (!CommonUtil.isEmpty(phoneHaltList)) {
            // 只有手机号码查询聚合器才会设置该列表
            validateResult.setPhoneHaltList(phoneHaltList);
        }

        List<ContentItem> validateList = aggregatorResult.getValidateList();
        if (Objects.isNull(validateList)) {
            validateList = Lists.newArrayList();
        }
        validateContext.setValidateList(validateList);
    }

    /**
     * 返回聚合结果
     *
     * @param sourceDataList
     * @param validateContext
     * @param validateResult
     * @return
     */
    protected abstract AggregatorResult doAggregate(List<ContentItem> sourceDataList, ValidateContext validateContext, ValidateResult validateResult);

    /**
     * 获取标题定义位置
     *
     * @param name
     * @param validateContext
     * @return 返回空表示未找到指定的标题名称
     */
    protected Integer getTitlePos(String name, ValidateContext validateContext) {
        List<String> titleList = validateContext.getTitleList();
        for (int i = 0; i < titleList.size(); i++) {
            if (StringUtils.equalsIgnoreCase(titleList.get(i), name)) {
                return i;
            }
        }
        return null;
    }
}
