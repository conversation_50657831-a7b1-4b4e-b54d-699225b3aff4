package com.xhqb.spectre.admin.mq.consumer;

import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.admin.mq.consumer.handler.BatchTaskParamConsumerHandler;
import com.xhqb.spectre.admin.mq.consumer.handler.BatchTaskSubmitConsumerChandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 消费者配置
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Component
@Slf4j
public class ConsumerConfiguration {

    @Resource
    private BatchTaskSubmitConsumerChandler batchTaskSubmitConsumerChandler;
    @Resource
    private BatchTaskParamConsumerHandler batchTaskParamConsumerHandler;

    /**
     * 群发任务提交消息
     * spectre-admin-batch
     *
     * @param message
     */
    @MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[0]}",
            subscriptionType = SubscriptionType.Shared,
            clazz = String.class,
            receiverQueueSize = 1,
            ackTimeout = 60L)
    public void batchTaskSubmitMessage(String message) {
        try {
            batchTaskSubmitConsumerChandler.handler(message);
        } catch (Exception e) {
            log.error("群发任务提交消息消费失败,message ={}", message, e);
        }
    }

    /**
     * 群发任务参数消息
     * spectre-admin-batch-param
     *
     * @param message
     */
    @MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[1]}",
            subscriptionType = SubscriptionType.Shared,
            clazz = String.class,
            receiverQueueSize = 1,
            ackTimeout = 60L)
    public void batchTaskParamMessage(String message) {
        try {
            batchTaskParamConsumerHandler.handler(message);
        } catch (Exception e) {
            log.error("群发任务参数消费失败,message =  {}", message, e);
        }
    }
}
