package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.service.AreasInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/26 17:18
 * @Description:
 */
@RestController
@RequestMapping("/areasInfo")
@Slf4j
public class AreasInfoController {

    @Autowired
    private AreasInfoService areasInfoService;

    /**
     * 查询地域树
     *
     * @return
     */
    @GetMapping("/queryAreaTree")
    public AdminResult queryAreaTree() {
        return AdminResult.success(areasInfoService.selectAreaTree());
    }

}
