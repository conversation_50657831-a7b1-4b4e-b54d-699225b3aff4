package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.TplDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/17 15:26
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplEnumVO implements Serializable {

    private static final long serialVersionUID = -6496111525543407922L;

    private Integer id;

    private String code;

    private String title;

    private Integer status;

    public static TplEnumVO buildTplEnumVO(TplDO tplDO) {
        return TplEnumVO.builder()
                .id(tplDO.getId())
                .code(tplDO.getCode())
                .title(tplDO.getTitle())
                .status(tplDO.getStatus())
                .build();
    }
}
