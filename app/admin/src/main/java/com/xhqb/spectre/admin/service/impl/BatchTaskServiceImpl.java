package com.xhqb.spectre.admin.service.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xhqb.spectre.admin.batchtask.MessageSendFactory;
import com.xhqb.spectre.admin.batchtask.cid.CidStrategyFactory;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.detect.BatchDetectUpdateHandler;
import com.xhqb.spectre.admin.batchtask.limit.BatchSubmitRateLimitService;
import com.xhqb.spectre.admin.batchtask.result.impl.DeleteUploadResultHandler;
import com.xhqb.spectre.admin.batchtask.result.impl.UpdateTaskParamHandler;
import com.xhqb.spectre.admin.batchtask.send.SpectreApiBooster;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryContext;
import com.xhqb.spectre.admin.batchtask.send.factory.SingleFactoryResult;
import com.xhqb.spectre.admin.batchtask.send.record.MessageSendFailedRecord;
import com.xhqb.spectre.admin.batchtask.upload.cos.S3Helper;
import com.xhqb.spectre.admin.batchtask.utils.BatchTaskUtils;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.enums.CidStrategyEnum;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.BatchTaskDTO;
import com.xhqb.spectre.admin.model.dto.BatchTaskSendDTO;
import com.xhqb.spectre.admin.model.vo.BatchTaskDetailVO;
import com.xhqb.spectre.admin.model.vo.BatchTaskVO;
import com.xhqb.spectre.admin.model.vo.batchtask.CheckResultVO;
import com.xhqb.spectre.admin.model.vo.batchtask.QueryTaskSegmentVO;
import com.xhqb.spectre.admin.mq.consumer.handler.BatchTaskParamConsumerHandler;
import com.xhqb.spectre.admin.mq.message.BatchTaskSubmitMessage;
import com.xhqb.spectre.admin.mq.producer.impl.BatchTaskSubmitProducer;
import com.xhqb.spectre.admin.mq.send.SenderContext;
import com.xhqb.spectre.admin.mq.send.enums.MqTimerTypeEnum;
import com.xhqb.spectre.admin.readonly.mapper.SmsOrderReadonlyMapper;
import com.xhqb.spectre.admin.service.BatchTaskService;
import com.xhqb.spectre.admin.util.*;
import com.xhqb.spectre.common.constant.GenericConstants;
import com.xhqb.spectre.common.dal.entity.*;
import com.xhqb.spectre.common.dal.mapper.*;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.BatchTaskQuery;
import com.xhqb.spectre.common.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 群发短信
 *
 * <AUTHOR>
 * @date 2021/9/17
 */
@Service
@Slf4j
public class BatchTaskServiceImpl implements BatchTaskService {

    /**
     * 最大的测试消息长度
     */
    private static final int MAX_TEST_MESSAGE_LENGTH = 2000;
    /**
     * 批量参数插入数量
     */
    @Value("${spectre.admin.batchInsertParamCount:50}")
    private Integer batchInsertParamCount;

    @Autowired
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private TplMapper tplMapper;
    @Autowired
    private SignMapper signMapper;
    @Autowired
    private AppMapper appMapper;
    @Autowired
    private BatchTaskParamMapper batchTaskParamMapper;

    @Autowired
    private MessageSendFactory messageSendFactory;
    @Autowired
    private BatchTaskSubmitProducer batchTaskSubmitProducer;
    @Resource
    private BatchTaskParamConsumerHandler batchTaskParamConsumerHandler;
    @Resource
    private DeleteUploadResultHandler deleteUploadResultHandler;
    @Resource
    private UpdateTaskParamHandler updateTaskParamHandler;
    @Resource
    private VenusConfig venusConfig;
    @Autowired
    private S3Helper s3Helper;
    @Resource
    private BatchTestLogMapper batchTestLogMapper;
    @Resource
    private SpectreApiBooster spectreApiBooster;
    @Resource
    private SmsOrderReadonlyMapper smsOrderReadonlyMapper;
    @Resource
    private CidStrategyFactory cidStrategyFactory;
    @Resource
    private BatchDetectUpdateHandler batchDetectUpdateHandler;
    @Resource
    private BatchSubmitRateLimitService batchSubmitRateLimitService;

    /**
     * 分页查询群发短信任务列表
     *
     * @param batchTaskQuery
     * @return
     */
    @Override
    public CommonPager<BatchTaskVO> listByPage(BatchTaskQuery batchTaskQuery) {
        return PageResultUtils.result(
                () -> batchTaskMapper.countByQuery(batchTaskQuery),
                () -> batchTaskMapper.selectByQuery(batchTaskQuery).stream().map(BatchTaskVO::buildListQuery).collect(Collectors.toList())
        );
    }

    /**
     * 根据ID查询群发短信任务详情
     *
     * @param id
     * @return
     */
    @Override
    public BatchTaskDetailVO getById(Integer id) {
        // 查询群发任务对象
        BatchTaskDO batchTaskDO = selectById(id);
        BatchTaskVO batchTaskVO = BatchTaskVO.buildInfoQuery(batchTaskDO);
        String fileInfoList = batchTaskVO.getFileInfoList();
        batchTaskVO.setFileInfoList(null);
        BatchTaskDetailVO batchTaskDetailVO = JSON.parseObject(JSON.toJSONString(batchTaskVO), BatchTaskDetailVO.class);
        batchTaskDetailVO.setCheckResultList(JSON.parseArray(fileInfoList, CheckResultVO.class));
        // 查询参数列表
        List<BatchTaskParamDO> batchTaskParamDOList = batchTaskParamMapper.selectByTaskId(id);
        if (Objects.isNull(batchTaskParamDOList) || batchTaskParamDOList.isEmpty()) {
            return batchTaskDetailVO;
        }

        // 设置参数列表信息
        List<QueryTaskSegmentVO> taskParamList = batchTaskParamDOList.stream().map(s -> QueryTaskSegmentVO.builder()
                .taskParamId(s.getId())
                .fileMd5(s.getFileMd5())
                .build()).collect(Collectors.toList());
        batchTaskDetailVO.setTaskParamList(taskParamList);
        return batchTaskDetailVO;
    }

    /**
     * 根据ID取消群发任务
     *
     * @param id
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void cancelBatchTask(Integer id) {
        // 检查群发任务是否存在
        BatchTaskDO batchTaskDO = selectById(id);
        Integer status = batchTaskDO.getStatus();

        // 未提交 和 灰度结束才能够进行 取消操作
        boolean cancelAble = Objects.equals(status, BatchTaskStatusEnum.UN_COMMIT.getCode())
                || Objects.equals(status, BatchTaskStatusEnum.AB_WARNING.getCode());

        if (!cancelAble) {
            throw new BizException("群发任务状态不合法,取消操作失败");
        }

        doUpdateStatus(batchTaskDO.getId(), BatchTaskStatusEnum.CANCELLED.getCode());
        batchTaskParamConsumerHandler.addTaskCanceled(batchTaskDO.getId());
    }

    /**
     * 根据ID删除群发任务
     *
     * @param id
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void deleteBatchTask(Integer id) {
        // 检查群发任务是否存在
        BatchTaskDO batchTaskDO = selectById(id);
        Integer status = batchTaskDO.getStatus();
        if (!Objects.equals(status, BatchTaskStatusEnum.UN_COMMIT.getCode())) {
            throw new BizException("只能删除未提交的群发任务");
        }

        BatchTaskDO result = new BatchTaskDO();
        result.setId(id);
        result.setIsDelete(DeleteEnum.DELETED.getCode());
        result.setUpdater(SsoUserInfoUtil.getUserName());
        batchTaskMapper.updateByPrimaryKeySelective(result);
        // 删除分片数据
        batchTaskParamMapper.deleteByTaskId(id);

        log.info("删除群发任务成功 = {},operator = {}", id, result.getUpdater());
    }

    /**
     * 添加群发短信任务信息
     *
     * @param batchTaskDTO
     * @param creator
     * @return 返回批次号
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Integer create(BatchTaskDTO batchTaskDTO, String creator) {
        checkRequest(batchTaskDTO);
        // 构建群发数据对象
        BatchTaskDO batchTaskDO = buildBatchTaskDO(batchTaskDTO, null, true);
        if (StringUtils.isNotBlank(creator)) {
            // 传入的创建者不为空 那么则设置创建者信息
            batchTaskDO.setCreator(creator);
            batchTaskDO.setUpdater(creator);
        }

        // 保存时设置模板类型 ${xx} 或者 [*] 2022-02-22
        batchTaskDO.setTplType(CommonUtil.isTplPlaceholderModel(batchTaskDO.getContent())
                ? BatchTaskConstants.TplType.PLACEHOLDER : BatchTaskConstants.TplType.NORMAL);

        int batchTaskCount = getBatchTaskCount(batchTaskDTO.getCheckResultList());
        // 设置总数据量
        batchTaskDO.setTotalCount(batchTaskCount);
        // 设置 fileInfoList 存放各个文件的 checkResult
        batchTaskDO.setFileInfoList(JacksonUtils.toJSONString(batchTaskDTO.getCheckResultList()));
        // 首先保存task数据
        batchTaskMapper.insertSelective(batchTaskDO);
        List<QueryTaskSegmentVO> taskParamList = batchTaskDTO.getTaskParamList();
        if (CommonUtil.isEmpty(taskParamList)) {
            throw new BizException("任务参数列表不能够为空");
        }
        List<String> taskNoList = taskParamList.stream().map(s -> s.getTaskNo()).collect(Collectors.toList());
        // 批量更新任务参数taskId
        updateTaskParamHandler.handler(batchTaskDO, taskNoList);

        // 批量更新检测数据taskId 2022-02-22
        batchDetectUpdateHandler.update(batchTaskDTO.getCheckResultList(), batchTaskDO.getId());

        // 入库成功之后 进行缓存数据清理工作
        deleteUploadResultHandler.handler(taskNoList);
        return batchTaskDO.getId();
    }

    /**
     * 更新群发短信任务信息
     *
     * @param id
     * @param batchTaskDTO
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void update(Integer id, BatchTaskDTO batchTaskDTO) {
        checkRequest(batchTaskDTO);
        // 校验编辑的群发短信任务是否存在
        BatchTaskDO dbResult = selectById(id);
        // 构建群发数据对象
        BatchTaskDO batchTaskDO = buildBatchTaskDO(batchTaskDTO, id, false);
        batchTaskDO.setVersion(dbResult.getVersion());
        int batchTaskCount = getBatchTaskCount(batchTaskDTO.getCheckResultList());
        // 设置总数据量
        batchTaskDO.setTotalCount(batchTaskCount);
        int affectedRows = batchTaskMapper.updateByPrimaryKeyWithVersion(batchTaskDO);
        if (affectedRows <= 0) {
            throw new BizException("群发短信更新失败");
        }

        // 更新时 需要判断数据是否有删除或者新增的情况
        // 1. 新增时,taskParamList元素taskParamId为空
        // 2. 删除时,taskParamList与已存在的参数列表进行差集对比,删除多余的参数记录

        // 获取到taskParamId为空的数据
        List<QueryTaskSegmentVO> taskSegmentList = batchTaskDTO.getTaskParamList();
        if (CommonUtil.isEmpty(taskSegmentList)) {
            taskSegmentList = Lists.newArrayList();
        }

        // 更新的参数列表,该列表与数据库已存在的数据进行对比
        // 删除多余的数据信息
        List<Integer> updateParamIdList = taskSegmentList.stream()
                .filter(s -> Objects.nonNull(s.getTaskParamId()))
                .map(QueryTaskSegmentVO::getTaskParamId)
                .collect(Collectors.toList());
        List<BatchTaskParamDO> batchTaskParamDOList = batchTaskParamMapper.selectByTaskId(id);
        // 待删除的参数主键列表
        List<Integer> deleteParamIdIList = getDeleteParamIdIfNecessary(updateParamIdList, batchTaskParamDOList);
        if (Objects.nonNull(deleteParamIdIList) && !deleteParamIdIList.isEmpty()) {
            batchTaskParamMapper.batchDeleteById(deleteParamIdIList);
        }

        // 新增的参数列表
        List<String> taskNoList = taskSegmentList.stream()
                .filter(s -> Objects.isNull(s.getTaskParamId()))
                .map(s -> s.getTaskNo()).collect(Collectors.toList());

        if (!CommonUtil.isEmpty(taskNoList)) {
            // 有新增参数时 才进行新增处理
            // 批量更新任务参数taskId
            updateTaskParamHandler.handler(batchTaskDO, taskNoList);
            // 入库成功之后 进行缓存数据清理工作
            deleteUploadResultHandler.handler(taskNoList);
        }
    }

    /**
     * 群发短信任务提交
     *
     * @param id
     */
    @Override
    public void submit(Integer id) {
        BatchTaskDO batchTaskDO = selectById(id);
        // 验证群发任务状态,只有待提交(0)状态才可以进行提交操作
        Integer status = batchTaskDO.getStatus();
        // 是否允许提交
        boolean isSubmitAble = Objects.equals(status, BatchTaskStatusEnum.UN_COMMIT.getCode()) || Objects.equals(status, BatchTaskStatusEnum.AB_WARNING.getCode());
        if (!isSubmitAble) {
            throw new BizException("群发任务状态不合法");
        }

        // 立即发送 晚上20:00到次日9:00 不能够被提交
        if (venusConfig.getBatchTaskSubmitHourCheck() && Objects.equals(SendTypeEnum.IMMEDIATE.getType(), batchTaskDO.getSendType())) {
            // 20-24    0-9
            if (DateUtil.betweenHour(20, 24) || DateUtil.betweenHour(0, 9)) {
                throw new BizException("晚上20:00到次日9:00禁止提交");
            }
        }

        // 加入限流
        batchSubmitRateLimitService.join(id, batchTaskDO.getLimitRate());

        // 若做了灰度提交 那么则不做提交测试验证的逻辑了
        // 2021-12-31
        if (!Objects.equals(status, BatchTaskStatusEnum.AB_WARNING.getCode())) {
            // 群发任务提交之前，需要先进行群发短信测试
            // 测试通过之后 才能够进行提交 2021-12-14
            String smsTypeCode = batchTaskDO.getSmsTypeCode();
            if (!StringUtils.equals(smsTypeCode, MessageTypeEnum.VERIFY.getMessageType())) {
                // 不是验证码的提交 则需要验证是否做过测试
                List<String> checkConfig = cidStrategyFactory.toListByKey(CidStrategyEnum.BATCH_SUBMIT_CHECK.getCode());
                if (!CommonUtil.isEmpty(checkConfig)) {
                    // 当天时间
                    Date date = new Date();
                    Date startTime = cn.hutool.core.date.DateUtil.beginOfDay(date);
                    Date endTime = cn.hutool.core.date.DateUtil.endOfDay(date);
                    // 开启了提交测试验证 需要验证短信测试结果
                    SmsOrderDO smsOrderDO = smsOrderReadonlyMapper.batchTestQuery(id,startTime.getTime()/1000,endTime.getTime()/1000);
                    if (Objects.isNull(smsOrderDO)) {
                        log.info("未查询到发送成功的测试订单数据,taskId = {}", id);
                        throw new BizException("群发任务需要测试通过之后才能够提交");
                    }
                } else {
                    log.info("未开启提交测试验证,batchId =  {}", id);
                }
            }
        }

        try {
            // 群发任务提交，尝试先移除取消标记
            // 防止取消了的任务 再次提交
            batchTaskParamConsumerHandler.removeTaskCanceled(batchTaskDO.getId());
        } catch (Exception e) {
            log.error("移除群发任务取消标记失败,taskId ={}", id, e);
        }
        doUpdateStatus(batchTaskDO.getId(), BatchTaskStatusEnum.COMMITTED.getCode());
        // 如果是定时群发任务,那么到点了 则进行发送mq消息操作
        // 如果不是定时群发任务 则立即发送mq消息
        // 查询批量任务参数列表
        // 发送mq消息
        BatchTaskSubmitMessage batchTaskSubmitMessage = JSON.parseObject(JSON.toJSONString(batchTaskDO), BatchTaskSubmitMessage.class);
        SenderContext<BatchTaskSubmitMessage> senderContext = new SenderContext<>(batchTaskSubmitMessage);

        Integer sendType = batchTaskDO.getSendType();
        if (Objects.equals(SendTypeEnum.IMMEDIATE.getType(), sendType)) {
            // 立即发送
            senderContext.setMqTimerTypeEnum(MqTimerTypeEnum.NOW);
        } else if (Objects.equals(SendTypeEnum.DELAYED.getType(), sendType)) {
            // 定时发送
            senderContext.setMqTimerTypeEnum(MqTimerTypeEnum.DELIVER_AT);
            senderContext.setDeliverDate(DateUtil.intToDate(batchTaskDO.getSendTime()));
        } else {
            throw new BizException("不支持的发送类型");
        }
        try {
            log.info("群发短信任务提交  = {}", senderContext);
            batchTaskSubmitProducer.send(senderContext);
        } catch (Exception e) {
            log.error("发送消息队列失败,senderContext = {}", senderContext, e);
            throw new BizException("发送提交消息队列失败");
        }
    }

    /**
     * 测试群发短信操作
     *
     * @param id               群发短信批次号(主键)
     * @param batchTaskSendDTO
     * @return
     */
    @Override
    public Map<String, Object> send(Integer id, BatchTaskSendDTO batchTaskSendDTO) {
        ValidatorUtil.validate(batchTaskSendDTO);
        BatchTaskDO batchTaskDO = selectById(id);

        // 验证当前群发任务数据是否合法
        BatchTaskDTO batchTaskDTO = new BatchTaskDTO();
        batchTaskDTO.setSmsTypeCode(batchTaskDO.getSmsTypeCode());
        batchTaskDTO.setSendType(batchTaskDO.getSendType());
        batchTaskDTO.setAppCode(batchTaskDO.getAppCode());
        batchTaskDTO.setTplId(batchTaskDO.getTplId());
        batchTaskDTO.setSignId(batchTaskDO.getSignId());

        SignDO signDO = signMapper.selectByPrimaryKey(batchTaskDO.getSignId());

        String mobile = batchTaskSendDTO.getMobile();
        String[] mobileList = mobile.split(",");

        // 发送消息上线信息
        SingleFactoryContext singleFactoryContext = new SingleFactoryContext();
        // 设置批次号
        singleFactoryContext.setBatchId(id);
        // 设置requestId
        singleFactoryContext.setRequestId("batch_" + spectreApiBooster.randomValue());
        singleFactoryContext.setAppCode(batchTaskDO.getAppCode());
        singleFactoryContext.setMobileList(Arrays.asList(mobileList));
        singleFactoryContext.setChannelAccountId(batchTaskSendDTO.getChannelAccountId());
        if (Objects.nonNull(signDO)) {
            // 设置签名code
            singleFactoryContext.setSignCode(signDO.getCode());
        }


        // 做数据校验
        // 该方法会填充signName 和 tplCode参数
        setDataCheckRequest(singleFactoryContext, batchTaskDTO, false);

        // 营销设置设置smsTypeCode值
        // 要获取模板的短信类型

        TplDO tplDO = tplMapper.selectByPrimaryKey(batchTaskDO.getTplId());
        if (Objects.isNull(tplDO)) {
            throw new BizException("模板信息不存在");
        }


        String tplContent = tplDO.getContent();

        // [{"key":"name","value":"张三"},{"key":"phone","value":"132xxx"}]
        List<LinkedHashMap<String, String>> paramList = batchTaskSendDTO.getParamList();

        if (CommonUtil.isTplPlaceholderModel(tplContent)) {
            // 短信模板占位符模式
            List<Map<String, String>> placeHolderList = BatchTaskUtils.paramMappingToPlaceholderList(paramList, mobileList);
            singleFactoryContext.setPlaceHolderList(placeHolderList);
        } else {
            // [*] 占位符
            // 发送短信的参数
            List<List<String>> paramMapList = Lists.newArrayList();
            if (!CommonUtil.isEmpty(paramList)) {
                List<String> paramMapItem = paramList.stream().map(s -> s.get(GenericConstants.VALUE_PROP)).collect(Collectors.toList());
                for (int i = 0; i < mobileList.length; i++) {
                    paramMapList.add(paramMapItem);
                }
            } else {
                // 没有参数时 设置空参数
                for (int i = 0; i < mobileList.length; i++) {
                    paramMapList.add(Lists.newArrayList(""));
                }
            }

            // 设置待发送短信的参数信息
            singleFactoryContext.setParamMapList(paramMapList);
        }

        // 设置真实的模板类型
        singleFactoryContext.setSmsCodeType(tplDO.getSmsTypeCode());
        long start = System.currentTimeMillis();
        // 发送短信
        List<SingleFactoryResult> factoryResultList = messageSendFactory.singleSendMessage(singleFactoryContext);
        log.info("测试短信发送结果 = {},耗时 = {}", JSON.toJSONString(factoryResultList), (System.currentTimeMillis() - start));

        List<String> allFailMobile = SingleFactoryResult.getAllFailMobile(factoryResultList);
        // 保存测试发送日志
        try {
            String reqContent = JSON.toJSONString(singleFactoryContext);
            String respContent = JSON.toJSONString(factoryResultList);
            // 存在失败的手机号证明发送失败了
            // 测试请求状态 0->失败 1->成功
            int status = CommonUtil.isEmpty(allFailMobile) ? 1 : 0;
            BatchTestLogDO batchTestLogDO = new BatchTestLogDO();
            batchTestLogDO.setTaskId(id);
            batchTestLogDO.setReqContent(CommonUtil.substr(reqContent, MAX_TEST_MESSAGE_LENGTH));
            batchTestLogDO.setRespContent(CommonUtil.substr(respContent, MAX_TEST_MESSAGE_LENGTH));
            batchTestLogDO.setStatus(status);
            String creator = SsoUserInfoUtil.getUserName();
            batchTestLogDO.setCreator(creator);
            batchTestLogDO.setUpdater(creator);
            batchTestLogMapper.insertSelective(batchTestLogDO);
            log.info("保存群发测试日志成功, taskId = {}", id);
        } catch (Exception e) {
            log.error("保存群发测试日志失败, taskId = {}", id, e);
        }

        // 有失败数据 则需要将失败信息也获取来
        List<String> allFailErrorList = Lists.newArrayList();
        if (!CommonUtil.isEmpty(allFailMobile)) {
            for (SingleFactoryResult singleFactoryResult : factoryResultList) {
                List<MessageSendFailedRecord> failureSMSRecord = singleFactoryResult.getFailureSMSRecord();
                if (CommonUtil.isEmpty(failureSMSRecord)) {
                    continue;
                }
                for (MessageSendFailedRecord record : failureSMSRecord) {
                    allFailErrorList.add(record.getFileMsg());
                }
            }
        }
        Map<String, Object> sendFailMap = Maps.newHashMap();
        sendFailMap.put("sendFail", allFailMobile);
        sendFailMap.put("sendError", allFailErrorList);
        // 返回发送失败的手机号码
        return sendFailMap;
    }

    /**
     * 批量保存插入参数列表
     *
     * @param batchTaskParamDOList
     */
    @Override
    public List<Integer> batchInsertParamList(List<BatchTaskParamDO> batchTaskParamDOList) {
        if (Objects.isNull(batchTaskParamDOList) || batchTaskParamDOList.isEmpty()) {
            // 为空时 则不进行保存操作
            return null;
        }
        List<Integer> segmentList = new ArrayList<>(batchTaskParamDOList.size());

        // 大数量分批次批量保存
        int paramSize = batchTaskParamDOList.size();
        if (paramSize <= batchInsertParamCount) {
            batchTaskParamMapper.batchInsert(batchTaskParamDOList);
            setSegmentId(segmentList, batchTaskParamDOList);
            return segmentList;
        }

        int segment = paramSize / batchInsertParamCount;
        List<BatchTaskParamDO> subList;
        for (int i = 0; i < segment; i++) {
            subList = batchTaskParamDOList.subList(i * batchInsertParamCount, (i + 1) * batchInsertParamCount);
            batchTaskParamMapper.batchInsert(subList);
            setSegmentId(segmentList, subList);
        }

        if (paramSize % batchInsertParamCount != 0) {
            subList = batchTaskParamDOList.subList((paramSize / batchInsertParamCount) * batchInsertParamCount, paramSize);
            batchTaskParamMapper.batchInsert(subList);
            setSegmentId(segmentList, subList);
        }

        return segmentList;
    }

    /**
     * 群发任务克隆
     * <p>
     * 将传入的群发任务数据复制一份并重置原始状态
     *
     * @param id
     */
    @Override
    public void copy(Integer id) {
        BatchTaskDO batchTaskDO = selectById(id);
        // 重置群发任务
        batchTaskDO.setId(null);
        batchTaskDO.setCreateTime(new Date());
        batchTaskDO.setStatus(0);
        batchTaskDO.setSendStartTime(0);
        batchTaskDO.setSendEndTime(0);
        batchTaskDO.setVersion(0);
        batchTaskDO.setSentCount(0);
        batchTaskMapper.insertSelective(batchTaskDO);
        final Integer taskId = batchTaskDO.getId();
        // 查询参数列表
        List<BatchTaskParamDO> batchTaskParamDOList = batchTaskParamMapper.selectByTaskId(id);
        if (CommonUtil.isEmpty(batchTaskParamDOList)) {
            return;
        }
        batchTaskParamDOList.forEach(s -> {
            s.setId(null);
            s.setTaskId(taskId);
            s.setSendStatus(0);
            s.setCreateTime(new Date());
        });
        // 批量插入
        batchInsertParamList(batchTaskParamDOList);
    }

    /**
     * 更新群发任务状态
     *
     * @param id
     */
    @Override
    public void resetStatus(Integer id) {
        BatchTaskDO dbResult = selectById(id);
        if (Objects.equals(dbResult.getStatus(), BatchTaskStatusEnum.CANCELLED.getCode())) {
            throw new BizException("已取消状态不能够被修改了");
        }

        BatchTaskDO batchTaskDO = new BatchTaskDO();
        batchTaskDO.setId(id);
        batchTaskDO.setStatus(BatchTaskStatusEnum.UN_COMMIT.getCode());
        batchTaskMapper.updateByPrimaryKeySelective(batchTaskDO);
    }

    /**
     * 获取文件下载路径
     *
     * @param fileName
     * @return
     */
    @Override
    public String downloadUrl(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            throw new BizException("文件名称不能够为空");
        }
        if (StringUtils.startsWith(fileName, BatchTaskConstants.Commons.COS_BIGDATA_FLAG)) {
            // 如果是以bigdata开头的那么查询bigdata目录
            return s3Helper.preBigdataUrl(fileName, false);
        }
        return s3Helper.preAdminUrl(fileName);
    }

    private void setSegmentId(List<Integer> segmentList, List<BatchTaskParamDO> taskParamDOList) {
        taskParamDOList.forEach(s -> segmentList.add(s.getId()));
    }


    /**
     * 根据ID查询群发任务信息
     *
     * @param id
     * @return
     */
    private BatchTaskDO selectById(Integer id) {
        BatchTaskDO batchTaskDO = batchTaskMapper.selectByPrimaryKey(id);
        if (Objects.isNull(batchTaskDO)) {
            throw new BizException("未找到该群发任务");
        }
        return batchTaskDO;
    }

    /**
     * 验证请求参数
     *
     * @param batchTaskDTO
     * @param validate     是否需要进行validate验证
     */
    private void checkRequest(BatchTaskDTO batchTaskDTO, boolean... validate) {
        setDataCheckRequest(null, batchTaskDTO, validate);
    }

    /**
     * 验证请求参数并填充数据的操作
     *
     * @param singleFactoryContext
     * @param batchTaskDTO
     * @param validate
     */
    private void setDataCheckRequest(SingleFactoryContext singleFactoryContext, BatchTaskDTO batchTaskDTO, boolean... validate) {
        if (validate.length > 0 && validate[0]) {
            ValidatorUtil.validate(batchTaskDTO);
        }
        if (!MessageTypeEnum.contains(batchTaskDTO.getSmsTypeCode())) {
            throw new BizException("不支持该短信类型编码");
        }
        if (!SendTypeEnum.checkSendType(batchTaskDTO.getSendType())) {
            throw new BizException("不支持该发送类型");
        }

        // 检查应用是否存在
        AppDO appDO = appMapper.selectByCode(batchTaskDTO.getAppCode());
        if (Objects.isNull(appDO)) {
            throw new BizException("授权应用编码有误，该应用不存在");
        }

        // 检查签名是否存在
        SignDO signDO = signMapper.selectByPrimaryKey(batchTaskDTO.getSignId());
        if (Objects.isNull(signDO) || !Objects.equals(CommonConstant.STATUS_VALID, signDO.getStatus())) {
            throw new BizException("签名ID有误，该签名不存在或已停用");
        }

        TplDO tplDO = tplMapper.selectByPrimaryKey(batchTaskDTO.getTplId());
        if (Objects.isNull(tplDO) || !Objects.equals(CommonConstant.STATUS_VALID, tplDO.getStatus())) {
            throw new BizException("模板ID有误，该模板不存在或已停用");
        }

        // 设置模板内容
        batchTaskDTO.setContent(tplDO.getContent());

        // 根据检测结果第一条数据判断当前名单中的用户标识类型
        List<CheckResultVO> checkResultList = batchTaskDTO.getCheckResultList();
        if (Objects.nonNull(checkResultList) && !checkResultList.isEmpty()) {
            CheckResultVO checkResultVO = checkResultList.get(0);
            if (Objects.nonNull(checkResultVO)) {
                Integer userIdType = BatchTaskUserIdTypeEnum.getUserIdType(checkResultVO.getDataType());
                batchTaskDTO.setUserIdType(userIdType);
            }
        }

        if (Objects.nonNull(singleFactoryContext)) {
            // 填充数据
            singleFactoryContext.setSignName(signDO.getName());
            singleFactoryContext.setTplCode(tplDO.getCode());
        }

    }

    /**
     * 构建群发任务数据对象
     *
     * @param batchTaskDTO 数据请求对象
     * @param id           批次号 主键
     * @param isAdd        true 新增操作
     * @return
     */
    private BatchTaskDO buildBatchTaskDO(BatchTaskDTO batchTaskDTO, Integer id, boolean isAdd) {
        String userName = SsoUserInfoUtil.getUserName();
        BatchTaskDO batchTaskDO = BatchTaskDO.builder()
                .appCode(batchTaskDTO.getAppCode())
                .tplId(batchTaskDTO.getTplId())
                .signId(batchTaskDTO.getSignId())
                .smsTypeCode(batchTaskDTO.getSmsTypeCode())
                .content(batchTaskDTO.getContent())
                .userIdType(batchTaskDTO.getUserIdType())
                .sendType(batchTaskDTO.getSendType())
                .fileInfoList(JSON.toJSONString(batchTaskDTO.getCheckResultList()))
                .remark(batchTaskDTO.getRemark())
                .updater(userName)
                .bigdataId(batchTaskDTO.getBigdataId())
                .projectDesc(batchTaskDTO.getProjectDesc())
                .userGroup(batchTaskDTO.getUserGroup())
                .build();
        Date sendTime = batchTaskDTO.getSendTime();
        if (Objects.nonNull(sendTime)) {
            batchTaskDO.setSendTime(DateUtil.dateToInt(sendTime));
        }
        if (isAdd) {
            batchTaskDO.setCreator(userName);
        } else {
            batchTaskDO.setId(id);
        }

        Integer limitRate = batchTaskDTO.getLimitRate();
        if (Objects.nonNull(limitRate)) {
            batchTaskDTO.setLimitRate(limitRate);
        }

        return batchTaskDO;
    }

    /**
     * 获取到待删除的参数数据
     * <p>
     * 传入的DTO集合与已存在的参数列表进行对比
     * 若传入的DTO集合中taskParamId不为空，并与batchTaskParamDOList做差集，多余的数据则需要删除
     *
     * @param updateParamIdList
     * @param batchTaskParamDOList
     * @return 返回待删除的参数主键
     */
    private List<Integer> getDeleteParamIdIfNecessary(List<Integer> updateParamIdList, List<BatchTaskParamDO> batchTaskParamDOList) {
        // 不包含更新的ID就是待删除的ID
        return batchTaskParamDOList.stream()
                .filter(s -> !updateParamIdList.contains(s.getId()))
                .map(BatchTaskParamDO::getId).collect(Collectors.toList());
    }

    /**
     * 更新批量任务状态
     *
     * @param taskId
     * @param status
     */
    private void doUpdateStatus(Integer taskId, Integer status) {
        // 更新群发任务状态为 9->已取消
        BatchTaskDO result = new BatchTaskDO();
        result.setId(taskId);
        result.setStatus(status);
        String userName = SsoUserInfoUtil.getUserName();
        if (StringUtils.isNotBlank(userName)) {
            // 群发提交时 若是管理员操作 则更新操作者
            // 其他比如说灰度提交以及大数据提交等 不做更新
            result.setUpdater(userName);
        }

        batchTaskMapper.updateByPrimaryKeySelective(result);
    }

    /**
     * 获取到批量任务发送的总数量
     *
     * @param checkResultList
     * @return
     */
    private int getBatchTaskCount(List<CheckResultVO> checkResultList) {
        if (CommonUtil.isEmpty(checkResultList)) {
            return 0;
        }
        int count = 0;
        for (CheckResultVO checkResultVO : checkResultList) {
            // 计算task总数量
            count += CommonUtil.toInt(checkResultVO.getValidCount());
        }
        return count;
    }

}
