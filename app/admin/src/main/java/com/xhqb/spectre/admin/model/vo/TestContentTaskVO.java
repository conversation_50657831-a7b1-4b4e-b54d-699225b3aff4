package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.test.tool.TestContentTaskDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TestContentTaskVO extends TestContentTaskDO {
    private double checkResult;

    /**
     * 提交时间
     */
    private String submitTimeStr;

    /**
     * 完成时间
     */
    private String completeTimeStr;

    /**
     * 触达率
     */
    private double reachRate;

    /**
     * app上报率
     */
    private double appReportRate;

    private String tplCodes;
}
