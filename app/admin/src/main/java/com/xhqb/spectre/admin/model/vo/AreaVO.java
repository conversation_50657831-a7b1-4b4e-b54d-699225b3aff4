package com.xhqb.spectre.admin.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/15 17:48
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AreaVO implements Serializable {

    private static final long serialVersionUID = -2901570380896509644L;

    /**
     * 地址ID
     */
    private String id;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 省份简名
     */
    private String provinceShortName;

    /**
     * 城市简名
     */
    private String cityShortName;
}
