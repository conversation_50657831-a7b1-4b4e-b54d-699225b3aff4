package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.UpdateWxUrlLinkDTO;
import com.xhqb.spectre.admin.model.dto.WxUrlLinkDTO;
import com.xhqb.spectre.admin.model.vo.WxUrlLinkVO;
import com.xhqb.spectre.common.dal.entity.WxUrlLinkDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.WxUrlLinkQuery;

import java.util.List;


public interface UrlLinkService {
    CommonPager<WxUrlLinkVO> listByPage(WxUrlLinkQuery wxUrlLinkQuery);

    WxUrlLinkVO queryInfo(Long id);

    void create(WxUrlLinkDTO wxUrlLinkDTO);

    void update(Long id, UpdateWxUrlLinkDTO updateWxUrlLinkDTO);

    void enable(Long id);

    void disable(Long id);

    void bindShortUrl(Long id, String shortUrl);

    List<WxUrlLinkDO> getExpiredUrlLinks();

    void refresh(WxUrlLinkDO wxUrlLinkDO);
}
