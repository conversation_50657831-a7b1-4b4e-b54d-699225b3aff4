package com.xhqb.spectre.admin.omini;

import com.xhqb.msgcenter.model.iteam.MsgSendEntry;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 消息中心上下文内容
 *
 * <AUTHOR>
 * @date 2021/12/30
 */
public abstract class OminiMessageContext implements Serializable {

    /**
     * 消息中心后台分配的策略id
     */
    private String strategyId;

    public OminiMessageContext(String strategyId) {
        if (StringUtils.isBlank(strategyId)) {
            throw new IllegalArgumentException("策略ID不合法");
        }
        this.strategyId = strategyId;
    }

    public String getStrategyId() {
        return strategyId;
    }

    /**
     * 填充消息实体
     * <p>
     * 设置自己关注的消息属性
     *
     * @param msgSendEntry
     */
    protected abstract void doFillEntry(MsgSendEntry msgSendEntry);

    /**
     * 构建消息请求内容
     *
     * @return
     */
    public MsgSendEntry buildEntry() {
        MsgSendEntry msgSendEntry = new MsgSendEntry();
        msgSendEntry.setStrategyId(this.getStrategyId());
        this.doFillEntry(msgSendEntry);
        return msgSendEntry;
    }


}
