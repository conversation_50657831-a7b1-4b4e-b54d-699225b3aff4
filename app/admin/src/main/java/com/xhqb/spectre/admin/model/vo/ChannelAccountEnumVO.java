package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.ChannelAccountDO;
import com.xhqb.spectre.common.dal.entity.ChannelDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 13:52
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelAccountEnumVO implements Serializable {

    private static final long serialVersionUID = 2884425554488713986L;

    /**
     * 账号ID
     */
    private Integer id;

    /**
     * 账号名称
     */
    private String name;

    /**
     * 账号状态
     */
    private Integer status;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 渠道名称
     */
    private String channelName;

    public static ChannelAccountEnumVO buildChannelAccountEnumVO(ChannelAccountDO item) {
        ChannelDO channelDO = item.getChannelDO();
        return ChannelAccountEnumVO.builder()
                .id(item.getId())
                .name(item.getName())
                .status(item.getStatus())
                .smsTypeCode(item.getSmsTypeCode())
                .channelName(Objects.nonNull(channelDO) ? channelDO.getName() : "")
                .build();
    }
}
