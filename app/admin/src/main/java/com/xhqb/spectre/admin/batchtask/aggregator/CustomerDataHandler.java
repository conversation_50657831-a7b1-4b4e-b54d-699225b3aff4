package com.xhqb.spectre.admin.batchtask.aggregator;

import com.xhqb.spectre.admin.model.vo.batchtask.CustomerVO;

import java.util.List;
import java.util.Map;

/**
 * 针对 cid -> CustomerVO的处理器
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
public interface CustomerDataHandler extends DataHandler<List<String>, Map<String, CustomerVO>> {

    /**
     * 判断当前处理器是否支持指定的签名名称
     *
     * @param signName
     * @return
     */
    boolean supports(String signName);
}
