package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.service.SmsStatisticsService;
import com.xhqb.spectre.admin.statistics.query.SmsStatisClassQuery;
import com.xhqb.spectre.admin.statistics.query.SmsStatisQuery;
import com.xhqb.spectre.admin.statistics.vo.SmsStatisDetailVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/28 15:10
 * @Description:
 */
@RestController
@RequestMapping("/smsStatistics")
public class SmsStatisticsController {

    @Autowired
    private SmsStatisticsService statisticsService;

    /**
     * 按天查询数据统计
     *
     * @param query
     * @return
     */
    @GetMapping("/queryByDay")
    public AdminResult queryByDay(@ModelAttribute SmsStatisQuery query) {
        return AdminResult.success(statisticsService.queryByDay(query));
    }

    /**
     * 按天查询数据统计列表，分页查询
     *
     * @param query
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/queryPageByDay")
    public AdminResult queryPageByDay(@ModelAttribute SmsStatisQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<SmsStatisDetailVO> commonPager = statisticsService.queryPageByDay(query);
        return AdminResult.success(commonPager);
    }

    /**
     * 按月查询数据统计
     *
     * @param query
     * @return
     */
    @GetMapping("/queryByMonth")
    public AdminResult queryByMonth(@ModelAttribute SmsStatisQuery query) {
        return AdminResult.success(statisticsService.queryByMonth(query));
    }

    /**
     * 查询时间范围内某一类型的数据统计
     *
     * @param query
     * @return
     */
    @GetMapping("/queryClassSumByDay")
    public AdminResult queryClassSumByDay(@ModelAttribute SmsStatisClassQuery query) {
        return AdminResult.success(statisticsService.queryClassSumByDay(query));
    }

    /**
     * 按天查询某一类型的数据统计
     *
     * @param query
     * @return
     */
    @GetMapping("/queryClassSumListByDay")
    public AdminResult queryClassSumListByDay(@ModelAttribute SmsStatisClassQuery query) {
        return AdminResult.success(statisticsService.queryClassSumListByDay(query));
    }

    /**
     * 查询历史发送统计
     *
     * @return
     */
    @GetMapping("/queryHisData")
    public AdminResult queryHisData() {
        return AdminResult.success(statisticsService.queryHisData());
    }

    /**
     * 查询各维度枚举
     *
     * @param classEnumQuery
     * @return
     */
    @GetMapping("/queryClassEnum")
    public AdminResult queryClassEnum(SmsStatisClassQuery classEnumQuery) {
        return AdminResult.success(statisticsService.queryClassEnum(classEnumQuery));
    }

    /**
     * 按小时查询数据统计
     *
     * @param query
     * @return
     */
    @GetMapping("/queryByHour")
    public AdminResult queryByHour(@ModelAttribute SmsStatisQuery query) {
        return AdminResult.success(statisticsService.queryByHour(query));
    }

    /**
     * 按小时查询数据统计，分页查询
     *
     * @param query
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/queryPageByHour")
    public AdminResult queryPageByHour(@ModelAttribute SmsStatisQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<SmsStatisDetailVO> commonPager = statisticsService.queryPageByHour(query);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询小时时间范围内某一类型的数据统计
     *
     * @param query
     * @return
     */
    @GetMapping("/queryClassSumByHour")
    public AdminResult queryClassSumByHour(@ModelAttribute SmsStatisClassQuery query) {
        return AdminResult.success(statisticsService.queryClassSumByHour(query));
    }

    /**
     * 按小时查询某一类型的数据统计
     *
     * @param query
     * @return
     */
    @GetMapping("/queryClassSumListByHour")
    public AdminResult queryClassSumListByHour(@ModelAttribute SmsStatisClassQuery query) {
        return AdminResult.success(statisticsService.queryClassSumListByHour(query));
    }
}
