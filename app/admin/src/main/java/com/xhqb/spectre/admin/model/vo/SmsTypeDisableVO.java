package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.SmsTypeDisableDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2022/2/17 14:25
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsTypeDisableVO implements Serializable {

    private static final long serialVersionUID = -811464932550888035L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 运营商列表
     */
    private List<String> ispList;

    /**
     * 地域列表
     */
    private List<AreaVO> areaList;

    /**
     * 屏蔽开始时间
     */
    private String startTime;

    /**
     * 屏蔽结束时间
     */
    private String endTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新人
     */
    private String updater;

    public static SmsTypeDisableVO build(SmsTypeDisableDO item) {
        return SmsTypeDisableVO.builder()
                .id(item.getId())
                .smsTypeCode(item.getSmsTypeCode())
                .ispList(CommonUtil.ispToList(item.getIsps()))
                .areaList(CommonUtil.areaToList(item.getAreas()))
                .startTime(DateUtil.intToString(item.getStartTime()))
                .endTime(DateUtil.intToString(item.getEndTime()))
                .createTime(DateUtil.dateToString(item.getCreateTime()))
                .creator(item.getCreator())
                .updateTime(DateUtil.dateToString(item.getUpdateTime()))
                .updater(item.getUpdater())
                .build();
    }
}
