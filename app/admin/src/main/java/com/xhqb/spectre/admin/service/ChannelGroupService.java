package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.ChannelGroupDTO;
import com.xhqb.spectre.admin.model.vo.ChannelGroupVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ChannelGroupQuery;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2022/4/11 10:46
 * @Description:
 */
public interface ChannelGroupService {

    CommonPager<ChannelGroupVO> listByPage(ChannelGroupQuery query);

    ChannelGroupVO getById(Integer id);

    void create(ChannelGroupDTO dto);

    void update(ChannelGroupDTO dto);

    void delete(Integer id);

    void batchDelete(List<Integer> idList);

    List<ChannelGroupVO> getListBySmsType(String smsTypeCode, Integer signId);
}
