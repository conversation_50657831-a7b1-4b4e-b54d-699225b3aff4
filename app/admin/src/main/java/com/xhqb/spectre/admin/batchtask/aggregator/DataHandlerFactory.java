package com.xhqb.spectre.admin.batchtask.aggregator;

import com.xhqb.spectre.admin.batchtask.aggregator.datasource.CifCustomerBaseDataHandler;
import com.xhqb.spectre.admin.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.OrderComparator;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * DataSourceHandler处理
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Configuration
public class DataHandlerFactory {

    /**
     * 处理Customer数据的handler
     */
    private final List<CustomerDataHandler> customerDataHandlerList;
    /**
     * customer cid 兜底处理器
     */
    @Resource
    private CifCustomerBaseDataHandler cifCustomerBaseDataHandler;

    public DataHandlerFactory(ObjectProvider<List<CustomerDataHandler>> customerDataHandlerProvider) {
        this.customerDataHandlerList = customerDataHandlerProvider.getIfAvailable();
        if (Objects.nonNull(this.customerDataHandlerList)) {
            OrderComparator.sort(this.customerDataHandlerList);
        }
    }

    /**
     * 获取到出来Customer的handler处理器
     *
     * @param signName
     * @return
     */
    public CustomerDataHandler getCustomerDataHandler(String signName) {
        if (CommonUtil.isEmpty(customerDataHandlerList)) {
            return cifCustomerBaseDataHandler;
        }

        if (StringUtils.isBlank(signName)) {
            return cifCustomerBaseDataHandler;
        }

        for (CustomerDataHandler customerDataHandler : customerDataHandlerList) {
            if (customerDataHandler.supports(signName)) {
                return customerDataHandler;
            }
        }

        return cifCustomerBaseDataHandler;
    }
}
