package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.BindShortUrlDTO;
import com.xhqb.spectre.admin.model.dto.UpdateWxUrlLinkDTO;
import com.xhqb.spectre.admin.model.dto.WxUrlLinkDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.WxUrlLinkVO;
import com.xhqb.spectre.admin.service.UrlLinkService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.WxUrlLinkQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 小程序短链管理接口
 */
@RestController
@RequestMapping("/wx/url-link")
@Slf4j
public class WxUrlLinkController {

    @Autowired
    private UrlLinkService urlLinkService;

    /**
     * 查询小程序短链列表
     *
     * @param wxUrlLinkQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(@ModelAttribute WxUrlLinkQuery wxUrlLinkQuery, Integer pageNum, Integer pageSize) {
        wxUrlLinkQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<WxUrlLinkVO> commonPager = urlLinkService.listByPage(wxUrlLinkQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询小程序短链详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Long id) {
        return AdminResult.success(urlLinkService.queryInfo(id));
    }

    /**
     * 生成小程序短链
     *
     * @param wxUrlLinkDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_URL_LINK)
    public AdminResult create(@RequestBody WxUrlLinkDTO wxUrlLinkDTO) {
        log.info("create url link, wxUrlLinkDTO: {}", wxUrlLinkDTO);
        urlLinkService.create(wxUrlLinkDTO);
        return AdminResult.success();
    }

    /**
     * 更新小程序短链
     *
     * @param id
     * @param updateWxUrlLinkDTO
     * @return
     */
    @PutMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_URL_LINK)
    public AdminResult update(@PathVariable("id") Long id, @RequestBody UpdateWxUrlLinkDTO updateWxUrlLinkDTO) {
        log.info("update url link, id: {}, wxUrlLinkDTO: {}", id, updateWxUrlLinkDTO);
        urlLinkService.update(id, updateWxUrlLinkDTO);
        return AdminResult.success();
    }

    /**
     * 启用小程序短链
     *
     * @param id id
     * @return
     */
    @PostMapping("/enable/{id}")
    @LogOpTime(OpLogConstant.MODULE_URL_LINK)
    public AdminResult enable(@PathVariable("id") Long id) {
        log.info("enable url link, id: {}", id);
        urlLinkService.enable(id);
        return AdminResult.success();
    }

    /**
     * 停用小程序短链
     *
     * @param id id
     * @return
     */
    @PostMapping("/disable/{id}")
    @LogOpTime(OpLogConstant.MODULE_URL_LINK)
    public AdminResult disable(@PathVariable("id") Long id) {
        log.info("disable url link, id: {}", id);
        urlLinkService.disable(id);
        return AdminResult.success();
    }

    /**
     * 绑定短链地址
     * 新创建则不传shortUrl
     *
     * @param bindShortUrlDTO
     * @return
     */
    @PostMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_URL_LINK)
    public AdminResult bindShortUrl(@PathVariable("id") Long id, @RequestBody BindShortUrlDTO bindShortUrlDTO) {
        log.info("bind short url for url link, id: {}", bindShortUrlDTO);
        urlLinkService.bindShortUrl(id, bindShortUrlDTO.getShortUrl());
        return AdminResult.success();
    }
}
