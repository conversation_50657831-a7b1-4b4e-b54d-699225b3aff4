package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.model.dto.TypeWeightDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelTestTplVO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 测试模板名称
     */
    private String name;

    /**
     * 渠道账号
     */
    private Integer channelAccountId;

    /**
     * 短信模版id
     */
    private Integer smsTplId;

    /**
     * 模版编码
     */
    private String tplCode;

    /**
     * 签名
     */
    private Integer signId;

    /**
     * 测试来源 0:测试库，1:自定义
     */
    private Integer source;

    /**
     * 号码个数
     */
    private Integer mobileCount;

    /**
     * 测试号类型 0:空号，1:正常号,2:黑名单
     */
    private List<TypeWeightDTO> typeWeightDTOS;

    /**
     * 启用状态(0:未启用 1:启用)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 短信类型
     */
    private String smsTypeCode;

    /**
     * 发送量级
     */
    private Integer senderLevel;
}
