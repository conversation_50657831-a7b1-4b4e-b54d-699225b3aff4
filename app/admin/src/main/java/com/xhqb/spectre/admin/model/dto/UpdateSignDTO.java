package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/18 11:42
 * @Description:
 */
@Data
public class UpdateSignDTO implements Serializable {

    private static final long serialVersionUID = 5644093605283016688L;

    private Integer id;

    @NotBlank(message = "签名描述不能为空")
    @Size(max = 256, message = "签名描述最大为{max}个字符")
    private String description;
}
