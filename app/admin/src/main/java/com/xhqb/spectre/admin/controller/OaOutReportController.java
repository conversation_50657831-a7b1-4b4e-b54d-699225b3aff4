package com.xhqb.spectre.admin.controller;

import com.alibaba.excel.EasyExcel;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.OaReportDto;
import com.xhqb.spectre.admin.model.dto.OaUpdateReportDto;
import com.xhqb.spectre.admin.model.result.CommonResult;
import com.xhqb.spectre.admin.model.vo.OutExportVO;
import com.xhqb.spectre.admin.model.vo.ReportVO;
import com.xhqb.spectre.admin.service.oa.OaReportService;
import com.xhqb.spectre.admin.service.oa.vo.FlowDataAllInfo;
import com.xhqb.spectre.admin.util.DailyCounterUtil;
import com.xhqb.spectre.admin.util.FileResponseUtil;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.OaReportQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * OA 外部报备
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/oaOutReport")
@Slf4j
public class OaOutReportController {

    @Resource
    private OaReportService oaReportService;

    @Resource
    private DailyCounterUtil dailyCounterUtil;

    @GetMapping("")
    public CommonResult<CommonPager<ReportVO>> page(@ModelAttribute OaReportQuery oaReportQuery, Integer pageNum, Integer pageSize) {
        oaReportQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        oaReportQuery.setContentType("OUT");
        log.info("查询外部报备请求信息 oaReportQuery:{}", JsonLogUtil.toJSONString(oaReportQuery));
        return CommonResult.success(oaReportService.page(oaReportQuery));
    }

    /**
     * 保存外部报备请求信息
     *
     * @param oaReportDto 外部报备请求的数据传输对象，包含请求的相关信息
     * @return 保存操作的结果，封装在CommonResult<String>对象中
     */
    @PostMapping("/save")
    public CommonResult<String> save(@RequestBody OaReportDto oaReportDto) {
        log.info("保存外部报备请求信息 oaReportDto:{}", JsonLogUtil.toJSONString(oaReportDto));
        return CommonResult.success(oaReportService.saveOaOutReport(oaReportDto));
    }

    /**
     * 提交外部报备请求
     *
     * @param oaReportDto 外部报备请求的数据传输对象，包含请求的相关信息
     * @return 提交操作的结果，封装在CommonResult<String>对象中
     */
    @PostMapping("submit")
    public CommonResult<String> submit(@RequestBody OaReportDto oaReportDto) {
        log.info("提交外部报备请求信息 oaReportDto:{}", JsonLogUtil.toJSONString(oaReportDto));
        return CommonResult.success(oaReportService.submitOaOutReport(oaReportDto));
    }

    /**
     * 批量提交外部报备请求
     * <p>
     * 该方法接收一个包含多个contentId字符串列表，并将这些contentId批量提交为外部报备请求。
     *
     * @param contentList 包含多个contentId字符串列表
     * @return 提交结果，封装在CommonResult<String>对象中，通常包含操作状态和消息
     */
    @PostMapping("batchReport")
    public CommonResult<String> batchReport(@RequestBody List<String> contentList) {
        log.info("批量提交外部报备请求信息 contentList:{}", JsonLogUtil.toJSONString(contentList));
        return CommonResult.success(oaReportService.batchReportOaOutReport(contentList));
    }


    /**
     * 更新外部报备请求信息
     * <p>
     * 此方法用于更新外部报备请求的信息。
     *
     * @param oaUpdateReportDto 包含更新信息的外部报备请求数据传输对象
     * @return 更新操作的结果，封装在CommonResult<String>对象中
     */
    @PostMapping("update")
    public CommonResult<String> update(@RequestBody OaUpdateReportDto oaUpdateReportDto) {
        log.info("更新外部报备请求信息 oaReportDto:{}", JsonLogUtil.toJSONString(oaUpdateReportDto));
        return CommonResult.success(oaReportService.update(oaUpdateReportDto));
    }

    /**
     * 获取外部报备详情信息
     *
     * @param contentId 报备内容的ID
     * @return 包含报备详情信息的CommonResult对象
     */
    @GetMapping("detail")
    public CommonResult<ReportVO> detail(String contentId) {
        log.info("获取外部报备详情信息 contentId:{}", JsonLogUtil.toJSONString(
                contentId));
        return CommonResult.success(oaReportService.detail(contentId));
    }

    /**
     * 审批记录获取
     * <p>
     * 通过给定的contentId来审批对应的内部或外部报备请求。
     *
     * @param contentId 报备请求的唯一标识
     * @return 审批操作的结果，封装在CommonResult<List<FlowDataAllInfo>>对象中，其中包含了审批后的全部流程信息
     */
    @GetMapping("/approve")
    public CommonResult<List<FlowDataAllInfo>> approve(String contentId) {
        return CommonResult.success(oaReportService.approve(contentId));
    }

    /**
     * 删除外部报备请求信息
     * <p>
     * 该方法用于根据contentId删除对应的外部报备请求信息。
     *
     * @param contentId 需要删除的外部报备请求的唯一标识
     * @return 删除操作的结果，封装在CommonResult<String>对象中
     */
    @PostMapping("/delete")
    public CommonResult<String> delete(String contentId) {
        log.info("删除外部报备请求信息 contentId:{}", JsonLogUtil.toJSONString(contentId));
        return CommonResult.success(oaReportService.delete(contentId));
    }

    /**
     * 导出外部报备数据
     * @param response
     * @param oaReportQuery
     */
    @GetMapping("/download")
    public void downloadReport(HttpServletResponse response, @ModelAttribute OaReportQuery oaReportQuery) {
        try {
            oaReportQuery.setContentType("OUT");
            log.info("导出外部报备请求信息 oaReportQuery:{}", JsonLogUtil.toJSONString(oaReportQuery));
            List<OutExportVO> exportList = oaReportService.outExport(oaReportQuery);

            String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String sequence = dailyCounterUtil.getDailySequenceNumber(2);
            String fileName = "外部短信文案" + dateStr + sequence;

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    FileResponseUtil.buildContentDisposition(fileName, FileResponseUtil.XLSX));

            EasyExcel.write(response.getOutputStream(), OutExportVO.class)
                    .sheet("sheet1")
                    .doWrite(exportList);
        } catch (Exception e) {
            log.error("导出外部报备数据失败", e);
            throw new BizException("导出失败");
        }
    }

}
