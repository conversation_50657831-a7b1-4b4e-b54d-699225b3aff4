package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.BatchTaskReportVO;
import com.xhqb.spectre.admin.service.BatchTaskReportService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.BatchTaskReportDO;
import com.xhqb.spectre.common.dal.mapper.BatchTaskReportMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 群发参数报表
 *
 * <AUTHOR>
 * @date 2021/10/22
 */
@Service
@Slf4j
public class BatchTaskReportServiceImpl implements BatchTaskReportService {

    @Resource
    private BatchTaskReportMapper batchTaskReportMapper;

    /**
     * 查看群发报表信息
     *
     * @param taskId
     * @param taskParamId
     * @return
     */
    @Override
    public List<BatchTaskReportVO> queryReport(Integer taskId, Integer taskParamId) {
        if (Objects.isNull(taskId) && Objects.isNull(taskParamId)) {
            log.error("查看报表必须传入taskId或者paramId");
            throw new BizException("查看报表失败");
        }

        List<BatchTaskReportDO> batchTaskReportList = batchTaskReportMapper.selectByTaskIdAndParamId(taskId, taskParamId);
        if (CommonUtil.isEmpty(batchTaskReportList)) {
            return null;
        }
        return batchTaskReportList.stream().map(BatchTaskReportVO::buildInfoQuery).collect(Collectors.toList());
    }

    /**
     * 快速统计
     * <p>
     * 主要快速统计出失败类型 失败数量 成功数等
     *
     * @param taskId
     * @return
     */
    @Override
    public AdminResult quickStats(Integer taskId) {
        List<BatchTaskReportDO> batchTaskReportList = batchTaskReportMapper.selectByTaskIdAndParamId(taskId, null);
        if (CommonUtil.isEmpty(batchTaskReportList)) {
            return AdminResult.error("未查询到群发报表信息");
        }

        // 失败的数量
        int failureCount = 0;
        // 错误统计
        Map<String, Integer> failureStats = new HashMap<>(64);
        // 解析失败的reportId
        Set<Integer> reportParseError = new HashSet<>(16);
        for (BatchTaskReportDO batchTaskReport : batchTaskReportList) {
            if (Objects.nonNull(batchTaskReport.getFail())) {
                // 统计总失败数
                failureCount = failureCount + batchTaskReport.getFail();
            }
            try {
                parseErrorStat(batchTaskReport, failureStats);
            } catch (Exception e) {
                log.warn("解析errorStat失败,batchTaskReport = {}", JSON.toJSONString(batchTaskReport), e);
                reportParseError.add(batchTaskReport.getId());
            }
        }

        // 统计结果
        JSONObject result = new JSONObject();
        result.put("failureCount", failureCount);
        result.put("failureStats", failureStats);
        result.put("reportParseError", reportParseError);

        return AdminResult.success(result);
    }

    /**
     * 解析错误统计信息
     *
     * @param batchTaskReport
     * @param failureStats
     */
    private void parseErrorStat(BatchTaskReportDO batchTaskReport, Map<String, Integer> failureStats) {
        String errorStat = batchTaskReport.getErrorStat();
        if (StringUtils.isBlank(errorStat)) {
            return;
        }
        JSONArray paramJsonArray = JSON.parseArray(errorStat);
        for (int j = 0; j < paramJsonArray.size(); j++) {
            JSONObject param = paramJsonArray.getJSONObject(j);
            Integer errorMsgCount = failureStats.get(param.getString("fileMsg"));
            if (Objects.isNull(errorMsgCount)) {
                errorMsgCount = 0;
            }
            errorMsgCount++;
            failureStats.put(param.getString("fileMsg"), errorMsgCount);
        }
    }
}
