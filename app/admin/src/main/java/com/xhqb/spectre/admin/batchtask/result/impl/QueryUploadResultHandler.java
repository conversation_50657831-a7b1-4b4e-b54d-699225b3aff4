package com.xhqb.spectre.admin.batchtask.result.impl;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.model.vo.batchtask.QueryTaskSegmentVO;
import com.xhqb.spectre.admin.model.vo.batchtask.UploadQueryVO;
import com.xhqb.spectre.common.constant.RedisKeys;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 查询文件上传结果处理
 * <p>
 * 查询结果只返回文件的分片编号
 * <p>
 * 文件上传异步查询结果
 * 接口响应定义 :  https://yapi.xhdev.xyz/project/660/interface/api/9445
 * {
 * "taskParamItem"：{UploadQueryVO对象},
 * "checkResult":{},
 * "status":number [查询状态 0->正在处理 1->上传处理完成 2->上传处理失败]
 * }
 *
 * <AUTHOR>
 * @date 2021/10/1
 */
@Component
@Slf4j
public class QueryUploadResultHandler {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 上传结果处理
     * 查询结果只返回文件的分片编号
     * <p>
     * 文件上传异步查询结果
     * 接口响应定义 :  https://yapi.xhdev.xyz/project/660/interface/api/9445
     * {
     * "taskParamItem"：{UploadQueryVO对象},
     * "checkResult":{},
     * "status":number [查询状态 0->正在处理 1->上传处理完成 2->上传处理失败]
     * }
     *
     * @param taskNo 批次编号
     * @return
     */
    public UploadQueryVO handler(String taskNo) {
        UploadQueryVO uploadQueryVO = new UploadQueryVO();
        if (stringRedisTemplate.opsForHash().hasKey(RedisKeys.BatchTaskKeys.BATCH_TASK_UPLOAD_FLAG_HASH_KEY, taskNo)) {
            // 文件正在处理
            uploadQueryVO.setStatus(BatchTaskConstants.UploadQueryStatus.PROCESSING);
            return uploadQueryVO;
        }

        // taskNo 不存在 表示当前已经处理完成
        String uploadQueryResult = stringRedisTemplate.opsForValue().get(RedisKeys.BatchTaskKeys.BATCH_TASK_UPLOAD_RESULT_STR_KEY + ":" + taskNo);
        if (StringUtils.isBlank(uploadQueryResult)) {
            // 没有内容表示文件上传处理失败
            log.info("根据传入的taskNo未查询到文件处理结果,taskNo = {}", taskNo);
            uploadQueryVO.setStatus(BatchTaskConstants.UploadQueryStatus.FAIL);
            return uploadQueryVO;
        }

        uploadQueryVO = JSON.parseObject(uploadQueryResult, UploadQueryVO.class);
        log.info("上传文件处理结果分片数量 = {},taskNo = {}", uploadQueryVO.getTaskParamItem(), taskNo);
        QueryTaskSegmentVO segmentVO = uploadQueryVO.getTaskParamItem();
        if (Objects.nonNull(segmentVO)) {
            // 不会分片内容
            segmentVO.setSegmentList(null);
        }
        return uploadQueryVO;
    }
}
