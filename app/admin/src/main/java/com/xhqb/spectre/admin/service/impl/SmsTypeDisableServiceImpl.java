package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.SmsTypeDisableDTO;
import com.xhqb.spectre.admin.model.vo.SmsTypeDisableVO;
import com.xhqb.spectre.admin.service.SmsTypeDisableService;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.SmsTypeDisableDO;
import com.xhqb.spectre.common.dal.mapper.SmsTypeDisableMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.SmsTypeDisableQuery;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2022/2/17 14:30
 * @Description:
 */
@Service
@Slf4j
public class SmsTypeDisableServiceImpl implements SmsTypeDisableService {

    @Autowired
    private SmsTypeDisableMapper smsTypeDisableMapper;

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    @Override
    public CommonPager<SmsTypeDisableVO> listByPage(SmsTypeDisableQuery query) {
        return PageResultUtils.result(
                () -> smsTypeDisableMapper.countByQuery(query),
                () -> smsTypeDisableMapper.selectByQuery(query).stream().map(SmsTypeDisableVO::build).collect(Collectors.toList())
        );
    }

    /**
     * 详情查询
     *
     * @param id
     * @return
     */
    @Override
    public SmsTypeDisableVO getById(Integer id) {
        return SmsTypeDisableVO.build(validateAndSelectById(id));
    }

    /**
     * 创建
     *
     * @param dto
     */
    @Override
    public void create(SmsTypeDisableDTO dto) {
        //参数校验
        checkParam(dto);

        //新增记录
        SmsTypeDisableDO item = buildDO(dto, true);
        smsTypeDisableMapper.insertSelective(item);
    }

    /**
     * 更新
     *
     * @param dto
     */
    @Override
    public void update(SmsTypeDisableDTO dto) {
        //参数校验
        checkParam(dto);

        //存在性校验
        validateAndSelectById(dto.getId());

        //更新记录
        SmsTypeDisableDO item = buildDO(dto, false);
        smsTypeDisableMapper.updateByPrimaryKeySelective(item);
    }

    /**
     * 批量删除
     *
     * @param idList
     */
    @Override
    public void batchDelete(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            throw new BizException("ID列表不能为空");
        }
        smsTypeDisableMapper.deleteByIdList(idList, SsoUserInfoUtil.getUserName());
    }

    private SmsTypeDisableDO validateAndSelectById(Integer id) {
        SmsTypeDisableDO smsTypeDisableDO = smsTypeDisableMapper.selectByPrimaryKey(id);
        if (Objects.isNull(smsTypeDisableDO)) {
            throw new BizException("未找到该屏蔽记录");
        }
        return smsTypeDisableDO;
    }

    /**
     * 参数校验
     *
     * @param dto
     */
    private void checkParam(SmsTypeDisableDTO dto) {
        //参数格式校验
        ValidatorUtil.validate(dto);

        //短信类型校验
        if (!MessageTypeEnum.contains(dto.getSmsTypeCode())) {
            throw new BizException("不支持该短信类型编码");
        }

        //校验生效时间
        Date start = DateUtil.stringToDate(dto.getStartTime());
        Date end = DateUtil.stringToDate(dto.getEndTime());
        if (Objects.isNull(start) || Objects.isNull(end)) {
            throw new BizException("开始时间或结束时间有误");
        }
        if (end.before(new Date())) {
            throw new BizException("结束时间不能小于当前时间");
        }
        if (end.before(start)) {
            throw new BizException("开始时间不能大于结束时间");
        }
        //地域校验
        dto.getAreaList().forEach(ValidatorUtil::validate);
    }

    private SmsTypeDisableDO buildDO(SmsTypeDisableDTO item, boolean isAdd) {
        String userName = SsoUserInfoUtil.getUserName();
        SmsTypeDisableDO smsTypeDisableDO = SmsTypeDisableDO.builder()
                .smsTypeCode(item.getSmsTypeCode())
                .isps(String.join(",", item.getIspList()))
                .areas(JSON.toJSONString(item.getAreaList()))
                .startTime(DateUtil.stringToInt(item.getStartTime()))
                .endTime(DateUtil.stringToInt(item.getEndTime()))
                .updater(userName)
                .build();
        if (isAdd) {
            smsTypeDisableDO.setCreator(userName);
        } else {
            smsTypeDisableDO.setId(item.getId());
        }
        return smsTypeDisableDO;
    }
}
