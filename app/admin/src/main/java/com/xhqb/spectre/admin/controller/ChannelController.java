package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.ChannelDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ChannelVO;
import com.xhqb.spectre.admin.service.ChannelService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ChannelQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 11:19
 * @Description:
 */
@RestController
@RequestMapping("/channel")
@Slf4j
public class ChannelController {

    @Autowired
    private ChannelService channelService;

    /**
     * 查询渠道枚举
     *
     * @return
     */
    @GetMapping("/enum")
    public AdminResult queryEnum() {
        return AdminResult.success(channelService.queryEnum());
    }

    /**
     * 查询渠道列表
     *
     * @param channelQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryChannelList(@ModelAttribute ChannelQuery channelQuery, Integer pageNum, Integer pageSize) {
        channelQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<ChannelVO> commonPager = channelService.listByPage(channelQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询渠道详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryChannelInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(channelService.getById(id));
    }

    /**
     * 添加渠道
     *
     * @param channelDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_CHANNEL)
    public AdminResult createChannel(@RequestBody ChannelDTO channelDTO) {
        log.info("createChannel, channelDTO: {}", channelDTO);
        channelService.create(channelDTO);
        return AdminResult.success();
    }
}
