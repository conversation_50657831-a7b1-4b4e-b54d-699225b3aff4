package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.ParamsDTO;
import com.xhqb.spectre.admin.model.dto.ParamsDeleteDTO;
import com.xhqb.spectre.admin.model.dto.ParamsValueDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ParamsInfoVO;
import com.xhqb.spectre.admin.model.vo.ParamsPageVO;
import com.xhqb.spectre.admin.model.vo.ParamsValuePageVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ParamsQuery;

import java.util.List;

public interface ParamsService {
    CommonPager<ParamsPageVO> codePage(ParamsQuery paramsQuery);

    String codeAdd(ParamsDTO paramsDTO);

    String codeDelete(ParamsDeleteDTO paramsDTO);

    CommonPager<ParamsValuePageVO>  valuePage(ParamsQuery paramsQuery);

    String valueAdd(ParamsValueDTO paramsValueDTO);

    String valueDelete(ParamsDeleteDTO paramsDTO);
}
