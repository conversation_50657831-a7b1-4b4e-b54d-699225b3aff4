package com.xhqb.spectre.admin.openapi.utils;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * 签名工具
 * <p>
 * 1. 拼接字待加签的字符串 appKey+appCode+nonce+NonceValue+timestamp+TimeValue+appSecret
 * 2. 根据第1步生成的字符串进行sha1生成签名
 *
 * <AUTHOR>
 * @date 2021/12/15
 */
public class SignUtils {

    /**
     * 请求头 存储appCode
     */
    private static final String H_APP_KEY = "appKey";
    /**
     * 随机数
     */
    private static final String H_NONCE = "nonce";
    /**
     * 当前时间戳 (unix time 单位秒)
     */
    private static final String H_TIMESTAMP = "timestamp";
    /**
     * 请求头签名信息
     */
    private static final String H_SIGN = "sign";

    /**
     * 获取到签名值
     *
     * @param appKey   应用编码
     * @param nonce     随机数
     * @param timestamp unix time 时间搓 (秒)
     * @param appSecret 应用秘钥
     * @return
     */
    public static String getSign(String appKey, String nonce, String timestamp, String appSecret) {
        StringBuilder sb = new StringBuilder();
        sb.append(H_APP_KEY).append(appKey);
        sb.append(H_NONCE).append(nonce);
        sb.append(H_TIMESTAMP).append(timestamp);
        sb.append(appSecret);
        return DigestUtils.sha1Hex(sb.toString());
    }
}
