package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;


@Data
public class AddBlackSourceDTO implements Serializable {

    private static final long serialVersionUID = -4828278508301843544L;

    @NotBlank(message = "来源名称不能为空")
    private String name;

    @NotBlank(message = "来源编码不能为空")
    @Size(max = 32, message = "签名编码最大为{max}个字符")
    @Pattern(regexp = "^(?![_|0-9])(?!.*?_$)[A-Za-z0-9_]+$", message = "来源编码格式有误")
    private String code;

    @NotBlank(message = "来源描述不能为空")
    @Size(max = 256, message = "来源描述最大为{max}个字符")
    private String description;
}
