package com.xhqb.spectre.admin.mq.send.decorator;

import com.xhqb.spectre.admin.mq.send.ProducerSender;
import com.xhqb.spectre.admin.mq.send.SenderContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.TypedMessageBuilder;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * DELIVER_AFTER 延迟发送
 * <p>
 * 选择 DELIVER_AFTER 方式，那么就是由该时间减去当前时间 若小于等于0 则立即发送
 * <p>
 * 参考 messageBuilder.deliverAfter
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Slf4j
public class DeliverAfterSender extends AbstractSenderDecorator {

    public DeliverAfterSender(ProducerSender producerSender) {
        super(producerSender);
    }

    /**
     * 做数据装饰填充
     *
     * @param messageBuilder
     * @param senderContext
     * @param <T>
     */
    @Override
    protected <T> void doDecorate(TypedMessageBuilder<String> messageBuilder, SenderContext<T> senderContext) {
        Date deliverDate = senderContext.getDeliverDate();
        if (Objects.isNull(deliverDate)) {
            log.error("设置了消息延迟发送,但是deliverDate为空,消息改为立即发送,senderContext = {}", senderContext);
            return;
        }

        // 获取到延迟间隔 毫秒数
        long interval = deliverDate.getTime() - System.currentTimeMillis();
        if (interval <= 0) {
            log.error("设置了消息延迟发送,但是deliverDate已经小于等于当前时间,消息改为立即发送,senderContext = {}", senderContext);
            return;
        }
        // 设置延迟发送
        messageBuilder.deliverAfter(interval, TimeUnit.MILLISECONDS);
    }
}
