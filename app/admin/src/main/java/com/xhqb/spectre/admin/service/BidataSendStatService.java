package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.bidata.model.SendFormDO;
import com.xhqb.spectre.admin.model.vo.MultipleCompareVO;
import com.xhqb.spectre.admin.model.vo.SendDetailVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.MultipleCompareQuery;
import com.xhqb.spectre.common.dal.query.ReachRateQuery;
import com.xhqb.spectre.common.dal.query.SendQuery;

import java.util.List;
import java.util.Map;

/**
 * 发送统计
 *
 * <AUTHOR>
 */
public interface BidataSendStatService {
    Map<String,  List<SendFormDO>> sendTopTen(SendQuery query);

    Map<String, List<Object>> reachRate(ReachRateQuery query);

    Map<String, List<Object>> overview();

    CommonPager<SendDetailVO> sendDetail(SendQuery query);

    MultipleCompareVO multipleCompare(MultipleCompareQuery query);

    List<Object> overviewByQuery(SendQuery query);
}
