package com.xhqb.spectre.admin.job;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.msgcenter.common.core.mqItems.ReceiverInfo;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.model.dto.DateContext;
import com.xhqb.spectre.admin.service.FeiShuAlert;
import com.xhqb.spectre.admin.service.TopSendCountNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 运营TOP5飞书通知
 * （top支持配置，每日9.55进行飞书通知）
 */
@Component
@Job("operatorTopSendCountFeiShuNotifyJob")
@Slf4j
public class OperatorTopSendCountFeiShuNotifyJob implements SimpleJob {

    @Resource
    private TopSendCountNotificationService notificationService;

    @Resource
    private FeiShuAlert feiShuAlert;

    @Resource
    private VenusConfig venusConfig;

    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            log.info("[运营TOP飞书通知] 开始执行任务");

            // 初始化日期和配置
            DateContext dateContext = notificationService.initializeDateContext();
            int topN = notificationService.getTopNIspCode();
            Set<String> ispCodeSet = notificationService.parseIspCodes();

            // 获取所有创建者及其模板编码的映射关系
            Map<String, List<String>> creatorTplCodesMap = notificationService.getCreatorTplCodesMapping();

            if (creatorTplCodesMap.isEmpty()) {
                log.info("[运营TOP{}飞书通知] 无创建者数据", topN);
                return;
            }

            // 解析用户名和邮箱映射
            Map<String, String> userNameAndEmailMap = notificationService.parseUserNameAndEmailMapping();

            // 处理每个创建者的数据
            for (Map.Entry<String, List<String>> entry : creatorTplCodesMap.entrySet()) {
                String creator = entry.getKey();
                List<String> creatorTplCodes = entry.getValue();

                // 获取该创建者的TOP模板编码
                List<String> topTplCodes = notificationService.getCreatorTopTplCodes(
                        creatorTplCodes, dateContext, topN);

                if (CollectionUtil.isNotEmpty(topTplCodes)) {
                    // 处理该创建者的数据
                    List<JSONObject> creatorMessages = notificationService.processCreatorData(
                            topTplCodes, dateContext, ispCodeSet);

                    // 获取邮箱地址并发送通知
                    String email = notificationService.getEmailAddress(userNameAndEmailMap, creator);
                    sendFeiShuNotification(creatorMessages, topN, email);
                }
            }
            log.info("[运营TOP{}飞书通知] 任务执行完成", topN);
        } catch (Exception e) {
            log.error("[运营TOP飞书通知] 任务执行失败", e);
            throw e;
        }
    }


    /**
     * 发送飞书通知
     */
    private void sendFeiShuNotification(List<JSONObject> messageList, int topN, String emailAddr) {

        if (CollectionUtil.isEmpty(messageList)) {
            log.info("[运营TOP{}飞书通知] 无数据需要发送", topN);
            return;
        }
        log.info("[运营TOP{}] 发送飞书通知 内容size[{}]", topN, messageList.size());

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("reports", messageList);
        dataMap.put("topN", topN);
        ArrayList<ReceiverInfo> receiver = new ArrayList<>();
        ReceiverInfo receiverInfo = new ReceiverInfo();
        receiverInfo.setEmailAddr(emailAddr);
        receiver.add(receiverInfo);
        feiShuAlert.sendAppFeiShuAlert(dataMap, venusConfig.getOperatorTopSendCountFeiShuNotifyJob(), receiver);
    }
}
