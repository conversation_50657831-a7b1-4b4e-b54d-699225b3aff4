package com.xhqb.spectre.admin.batchtask.parse;

import java.io.InputStream;
import java.io.Serializable;
import java.util.List;

/**
 * 文件内容解析器上下文信息
 * 主要主要包含文件名称和文件的输入流
 *
 * <AUTHOR>
 * @date 2021/9/18
 */
public class ParseContext implements Serializable {

    /**
     * 处理的文件名称
     */
    private String fileName;
    /**
     * 处理文件输入流
     */
    private InputStream inputStream;
    /**
     * 文件存储地址
     */
    private String saveUrl;

    /**
     * 签名名称 签名名称用来选择cid校验逻辑
     */
    private String signName;

    /**
     * 手机状态验证列表 (空号,停机)
     */
    private List<String> phoneStatusList;

    /**
     * 文件上传
     */
    private InputStream uploadStream;

    public ParseContext(String fileName, InputStream inputStream, String saveUrl,InputStream uploadStream) {
        this.fileName = fileName;
        this.inputStream = inputStream;
        this.saveUrl = saveUrl;
        this.uploadStream = uploadStream;
    }

    public String getSignName() {
        return signName;
    }

    public ParseContext setSignName(String signName) {
        this.signName = signName;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public InputStream getInputStream() {
        return inputStream;
    }

    public String getSaveUrl() {
        return saveUrl;
    }


    public List<String> getPhoneStatusList() {
        return phoneStatusList;
    }

    public void setPhoneStatusList(List<String> phoneStatusList) {
        this.phoneStatusList = phoneStatusList;
    }

    public InputStream getUploadStream() {
        return uploadStream;
    }

    @Override
    public String toString() {
        return "ParseContext{" +
                "fileName='" + fileName + '\'' +
                ", saveUrl='" + saveUrl + '\'' +
                ", signName='" + signName + '\'' +
                '}';
    }
}
