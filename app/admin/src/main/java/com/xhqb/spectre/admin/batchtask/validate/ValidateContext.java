package com.xhqb.spectre.admin.batchtask.validate;


import com.xhqb.spectre.admin.batchtask.parse.ContentItem;
import com.xhqb.spectre.admin.batchtask.parse.ParseResult;
import com.xhqb.spectre.common.dal.entity.TplDO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/18
 */
@Data
public class ValidateContext extends ParseResult {

    /**
     * 验证时数据集合
     */
    private List<ContentItem> validateList;

    /**
     * 短信类型
     */
    private String smsTypeCode;

    private TplDO tplDO;

    public ValidateContext(ParseResult parseResult, TplDO tplDO) {
        this.setTitleList(parseResult.getTitleList());
        this.setContentType(parseResult.getContentType());
        this.setContentItemList(parseResult.getContentItemList());
        this.validateList = parseResult.getContentItemList();
        this.setFileMd5(parseResult.getFileMd5());
        this.setFileName(parseResult.getFileName());
        String signName = parseResult.getSignName();
        if (StringUtils.isNotBlank(signName)) {
            signName = signName.trim();
        }
        this.setSignName(signName);
        this.setPhoneStatusList(parseResult.getPhoneStatusList());

        //改为从模板中读取
        this.smsTypeCode = tplDO.getSmsTypeCode();

        this.tplDO = tplDO;
    }

}
