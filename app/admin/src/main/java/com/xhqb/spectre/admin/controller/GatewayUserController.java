package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.GatewayUserDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.GatewayUserVO;
import com.xhqb.spectre.admin.service.GatewayUserService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.GatewayUserQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 网关账号管理(王建政)
 *
 * <AUTHOR>
 * @date 2021/11/8
 */
@RestController
@RequestMapping("/gatewayUser")
@Slf4j
public class GatewayUserController {

    @Resource
    private GatewayUserService gatewayUserService;

    /**
     * 查询网关账号列表
     *
     * @param gatewayUserQuery 网关账号查询条件
     * @param pageNum          当前页码
     * @param pageSize         一页显示的记录数
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(GatewayUserQuery gatewayUserQuery, Integer pageNum, Integer pageSize) {
        gatewayUserQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<GatewayUserVO> commonPager = gatewayUserService.listByPage(gatewayUserQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(gatewayUserService.getById(id));
    }

    /**
     * 添加网关账号
     *
     * @param gatewayUserDTO 新增的网关账号信息
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_GATEWAY_USER)
    public AdminResult createInfo(@RequestBody GatewayUserDTO gatewayUserDTO) {
        log.info("create gatewayUserDTO = {}", JSON.toJSONString(gatewayUserDTO));
        gatewayUserService.create(gatewayUserDTO);
        return AdminResult.success();
    }

    /**
     * 更新网关账号
     *
     * @param id                      网关账号(主键)
     * @param gatewayUserDTO 修改的网关账号内容
     * @return
     */
    @PutMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_GATEWAY_USER)
    public AdminResult updateInfo(@PathVariable("id") Integer id, @RequestBody GatewayUserDTO gatewayUserDTO) {
        log.info("update gatewayUserDTO = {}, id = {}", JSON.toJSONString(gatewayUserDTO), id);
        gatewayUserService.update(id, gatewayUserDTO);
        return AdminResult.success();
    }

    /**
     * 删除网关账号
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_GATEWAY_USER)
    public AdminResult deleteInfo(@PathVariable("id") Integer id) {
        log.info("delete gatewayUser id = {}", id);
        return gatewayUserService.delete(id);
    }
}
