package com.xhqb.spectre.admin.service.brand.impl;

import com.xhqb.spectre.admin.service.brand.BrandDetectionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 小米品牌
 */
@Component
@Slf4j
public class XiaomiDetection implements BrandDetectionStrategy {
    @Override
    public boolean isMatch(String brand, String os) {
        return brand.toLowerCase().contains("xiaomi") || brand.toLowerCase().contains("redmi");
    }

    @Override
    public String getMappedBrand() {
        return "xiaomi";
    }
}
