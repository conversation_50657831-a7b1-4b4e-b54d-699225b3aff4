package com.xhqb.spectre.admin.batchtask.io;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.batchtask.ParamItemVO;
import com.xhqb.spectre.admin.openapi.utils.ServletUtils;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.BatchTaskDO;
import com.xhqb.spectre.common.dal.entity.BatchTaskLogDO;
import com.xhqb.spectre.common.dal.mapper.BatchTaskLogMapper;
import com.xhqb.spectre.common.dal.mapper.BatchTaskMapper;
import com.xhqb.spectre.common.enums.BatchTaskUserIdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 群发参数导出
 * <p>
 * 主要导出失败的参数信息
 *
 * <AUTHOR>
 * @date 2021/12/22
 */
@Component
@Slf4j
public class BatchParamExport {

    /**
     * 一次查询5000条数据
     */
    private static final int MAX_QUERY_SIZE = 5000;

    @Resource
    private BatchTaskLogMapper batchTaskLogMapper;
    @Resource
    private BatchTaskMapper batchTaskMapper;

    /**
     * 导出群发参数数据
     * <p>
     * 1. 首先判断当前群发任务类型  属于cid还是mobile
     * 2. 获取taskLog第一条记录 用于生成excel头部信息
     * 3. 组装excel内容数据
     * 4. 写入excel
     *
     * @param taskId
     * @param response
     * @throws Exception
     */
    public void export(Integer taskId, HttpServletResponse response) throws Exception {
        String taskType = this.getTaskType(taskId);

        List<BatchTaskLogDO> batchTaskLogList = this.scan(taskId);
        if (CommonUtil.isEmpty(batchTaskLogList)) {
            log.warn("导出群发参数数据,未查询到短信发送失败记录, taskId = {}", taskId);
            // throw new BizException("导出群发参数数据,未查询到短信发送失败记录");
            ServletUtils.write(response, AdminResult.error("未查询到短信发送失败记录"));
            return;
        }

        response.setCharacterEncoding("utf-8");
        response.setContentType("application/vnd.ms-excel");

        // excel头部信息
        List<List<String>> header = this.createHeader(taskType, batchTaskLogList.get(0));
        // excel内容列表
        List<List<String>> dataList = this.createDataList(taskType, batchTaskLogList);

        String fileName = taskType + "_" + taskId + "_" + System.currentTimeMillis();
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream())
                .useDefaultStyle(false)
                .head(header)
                .sheet("Sheet1")
                .doWrite(dataList);
    }

    /**
     * 获取任务类型
     *
     * @param taskId
     * @return 返回 cid 或者 mobile
     */
    private String getTaskType(Integer taskId) {
        BatchTaskDO batchTaskDO = batchTaskMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(batchTaskDO)) {
            log.warn("导出群发参数数据,群发任务不存在, taskId = {}", taskId);
            throw new BizException("导出群发参数数据,群发任务不存在");
        }

        // 名单中的用户标识类型，1：cid；2：手机号
        Integer userIdType = batchTaskDO.getUserIdType();
        if (Objects.equals(userIdType, BatchTaskUserIdTypeEnum.MOBILE.getType())) {
            return BatchTaskConstants.DataType.MOBILE;
        }

        return BatchTaskConstants.DataType.CID;
    }

    /**
     * 创建excel头部信息
     *
     * @param taskType
     * @param batchTaskLogDO
     * @return
     */
    private List<List<String>> createHeader(String taskType, BatchTaskLogDO batchTaskLogDO) {
        List<List<String>> header = new ArrayList<>();
        header.add(Lists.newArrayList(taskType));

        String param = batchTaskLogDO.getParam();
        ParamItemVO paramItemVO = JSON.parseObject(param, ParamItemVO.class);
        if (Objects.isNull(paramItemVO)) {
            log.warn("群发参数为空,不生成文件头列表 = {}", JSON.toJSONString(batchTaskLogDO));
            return new ArrayList<>();
        }
        // 移除辅助参数
        this.remAuxiliaryProperty(paramItemVO);

        Set<Map.Entry<String, String>> entries = paramItemVO.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            header.add(Lists.newArrayList(entry.getKey()));
        }
        header.add(Lists.newArrayList("错误描述"));
        return header;
    }

    /**
     * 创建excel内容数据列表
     *
     * @param taskType
     * @param batchTaskLogList
     * @return
     */
    private List<List<String>> createDataList(String taskType, List<BatchTaskLogDO> batchTaskLogList) {
        List<List<String>> dataList = new ArrayList<>(batchTaskLogList.size());
        List<String> data;
        for (BatchTaskLogDO batchTaskLogDO : batchTaskLogList) {
            data = this.createData(taskType, batchTaskLogDO);
            if (!CommonUtil.isEmpty(data)) {
                dataList.add(data);
            }
        }
        return dataList;
    }

    /**
     * 创建数据
     *
     * @param taskType
     * @param batchTaskLogDO
     * @return
     */
    private List<String> createData(String taskType, BatchTaskLogDO batchTaskLogDO) {
        List<String> colList = new ArrayList<>();
        String param = batchTaskLogDO.getParam();
        ParamItemVO paramItemVO = JSON.parseObject(param, ParamItemVO.class);
        if (Objects.isNull(paramItemVO)) {
            log.warn("群发参数为空,不生成文件数据列表 = {}", JSON.toJSONString(batchTaskLogDO));
            return null;
        }
        // 第一列的数据
        String firstColData = paramItemVO.get(taskType);
        colList.add(firstColData);
        this.remAuxiliaryProperty(paramItemVO);
        paramItemVO.entrySet().forEach(s -> colList.add(s.getValue()));
        // 错误描述信息
        colList.add(batchTaskLogDO.getDescription());
        return colList;
    }


    /**
     * 扫描出待导出的数据信息
     *
     * @param taskId
     * @return
     */
    private List<BatchTaskLogDO> scan(Integer taskId) {
        List<BatchTaskLogDO> container = this.createContainer(taskId);
        Integer lastId = 0;
        do {
            lastId = query(taskId, lastId, container);
        } while (lastId != null);
        return container;
    }

    /**
     * 查询待导出的数据
     *
     * @param taskId
     * @param lastId
     * @param container
     * @return
     */
    private Integer query(Integer taskId, Integer lastId, List<BatchTaskLogDO> container) {
        List<BatchTaskLogDO> batchTaskLogList = batchTaskLogMapper.exportQuery(taskId, lastId, MAX_QUERY_SIZE);
        if (CommonUtil.isEmpty(batchTaskLogList)) {
            return null;
        }
        container.addAll(batchTaskLogList);
        return batchTaskLogList.get(batchTaskLogList.size() - 1).getId();
    }

    /**
     * 创建数据容器
     *
     * @param taskId
     * @return
     */
    private List<BatchTaskLogDO> createContainer(Integer taskId) {
        Integer count = batchTaskLogMapper.exportCount(taskId);
        if (Objects.isNull(count) || count <= 0) {
            return new ArrayList<>();
        }
        return new ArrayList<>(count);
    }

    /**
     * 移除辅助的参数信息
     *
     * @param paramItemVO
     */
    private void remAuxiliaryProperty(ParamItemVO paramItemVO) {
        paramItemVO.remove(BatchTaskConstants.DataType.CID);
        paramItemVO.remove(BatchTaskConstants.DataType.MOBILE);
        paramItemVO.remove(BatchTaskConstants.DataType.APPLY_LOAN_RESULT);
        paramItemVO.remove(BatchTaskConstants.DataType.SKIP_SEND);
    }
}
