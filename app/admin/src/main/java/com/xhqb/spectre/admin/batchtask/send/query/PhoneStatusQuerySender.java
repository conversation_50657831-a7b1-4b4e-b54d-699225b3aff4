package com.xhqb.spectre.admin.batchtask.send.query;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.send.MessageSendResult;
import com.xhqb.spectre.admin.batchtask.send.MessageSender;
import com.xhqb.spectre.admin.batchtask.send.SpectreApiBooster;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.constant.Apis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 查询手机状态
 *
 * <AUTHOR>
 * @date 2022/1/18
 */
@Component
public class PhoneStatusQuerySender implements MessageSender<PhoneStatusRequest, PhoneStatusResponse> {

    @Autowired
    private VenusConfig venusConfig;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private SpectreApiBooster spectreApiBooster;

    /**
     * 发送消息处理
     *
     * @param messageRequest
     * @return
     * @throws Exception
     */
    @Override
    public MessageSendResult<PhoneStatusResponse> send(PhoneStatusRequest messageRequest) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        // 填充签名请求头
        spectreApiBooster.boost(messageRequest, headers);
        headers.add(BatchTaskConstants.HttpHeader.CONTENT_TYPE, BatchTaskConstants.HttpHeader.JSON_TYPE);
        HttpEntity<PhoneStatusRequest> requestEntity = new HttpEntity<>(messageRequest, headers);
        ResponseEntity<MessageSendResult> response = restTemplate.exchange(this.getContentApi(), HttpMethod.POST, requestEntity, MessageSendResult.class);
        if (!Objects.equals(response.getStatusCode(), HttpStatus.OK)) {
            return response.getBody();
        }

        String s = JSON.toJSONString(response.getBody());
        return JSON.parseObject(s, new TypeReference<MessageSendResult<PhoneStatusResponse>>() {
        });
    }

    /**
     * 短信发送地址
     *
     * @return
     */
    public String getContentApi() {
        return venusConfig.getSpectreApiHost() + Apis.SpectreApi.CHECK_PHONE_STATUS;
    }
}
