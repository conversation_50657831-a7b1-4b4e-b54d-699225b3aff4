package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.service.AutoTestTaskInvokeService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private AutoTestTaskInvokeService autoTestTaskInvokeService;

    @GetMapping("/auto_test_feishu_alert")
    public void autoTestFeiShuAlert() {
        this.autoTestTaskInvokeService.invokeSubFlow();
    }

    @GetMapping("/auto_test_invoke")
    public void autoTestInvoke() {
        this.autoTestTaskInvokeService.invoke();
    }
}
