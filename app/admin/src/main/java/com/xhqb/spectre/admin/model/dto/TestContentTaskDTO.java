package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;

@Data
public class TestContentTaskDTO {
    /**
     * 测试名称
     */
    @NotBlank(message = "测试名称不能为空")
    private String name;

    /**
     * 短信文案 换行  \n
     */
    @NotBlank(message = "短信文案不能为空")
    private String content;

    /**
     * 测试品牌配置 json 格式 {"huawei":2,"rongyao":1,"xiaomi":2,"oppo":1}
     */
    @NotBlank(message = "测试品牌配置不能为空")
    private String brandConfig;

    /**
     * 备注
     */
    private String remark;

    /**
     * 任务 id
     */
    private String taskId;
}
