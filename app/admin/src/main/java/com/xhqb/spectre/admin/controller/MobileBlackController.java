package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.MobileBlackDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.MobileBlackVO;
import com.xhqb.spectre.admin.service.MobileBlackService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.MobileBlackQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/26 20:45
 * @Description:
 */
@RestController
@RequestMapping("/mobileBlack")
@Slf4j
public class MobileBlackController {

    @Autowired
    private MobileBlackService mobileBlackService;

    /**
     * 查询黑名单列表
     *
     * @param mobileBlackQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(@ModelAttribute MobileBlackQuery mobileBlackQuery, Integer pageNum, Integer pageSize) {
        mobileBlackQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<MobileBlackVO> commonPager = mobileBlackService.listByPage(mobileBlackQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询黑名单详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(mobileBlackService.getById(id));
    }

    /**
     * 创建黑名单
     *
     * @param mobileBlackDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_MOBILE_BLACK)
    public AdminResult create(@RequestBody MobileBlackDTO mobileBlackDTO) {
        log.info("create mobileBlack, mobileBlackDTO: {}", JSON.toJSONString(mobileBlackDTO));
        mobileBlackService.create(mobileBlackDTO);
        return AdminResult.success();
    }

    /**
     * 删除黑名单
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_MOBILE_BLACK)
    public AdminResult delete(@PathVariable("id") Integer id) {
        log.info("delete mobileBlack, id: {}", id);
        mobileBlackService.delete(id);
        return AdminResult.success();
    }

    /**
     * 批量删除黑名单
     *
     * @param idList
     * @return
     */
    @PostMapping("/batchDelete")
    @LogOpTime(OpLogConstant.MODULE_MOBILE_BLACK)
    public AdminResult batchDelete(@RequestBody List<Integer> idList) {
        log.info("batchDelete mobileBlack, idList: {}", JSON.toJSONString(idList));
        mobileBlackService.batchDelete(idList);
        return AdminResult.success();
    }
}
