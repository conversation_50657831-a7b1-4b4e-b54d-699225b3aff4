package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.BatchTaskLogDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 群发任务日志
 *
 * <AUTHOR>
 * @date 2021/12/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskLogVO implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 群发任务ID
     */
    private Integer taskId;

    /**
     * 群发任务分片ID
     */
    private Integer taskParamId;

    /**
     * cid信息
     */
    private String cid;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 发送状态 0->失败 1->成功
     */
    private Integer status;

    /**
     * 发送描述信息
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 参数
     */
    private String param;
    /**
     * 错误信息
     */
    private String errCode;

    /**
     * 查询列表数据展现
     *
     * @param batchTaskLogDO
     * @return
     */
    public static BatchTaskLogVO buildListQuery(BatchTaskLogDO batchTaskLogDO) {
        return buildInfoQuery(batchTaskLogDO);
    }

    /**
     * 查询数据详情展现
     *
     * @param batchTaskLogDO
     * @return
     */
    public static BatchTaskLogVO buildInfoQuery(BatchTaskLogDO batchTaskLogDO) {
        return BatchTaskLogVO.builder()
                // 主键
                .id(batchTaskLogDO.getId())
                // 群发任务ID
                .taskId(batchTaskLogDO.getTaskId())
                // 群发任务分片ID
                .taskParamId(batchTaskLogDO.getTaskParamId())
                // cid信息
                .cid(batchTaskLogDO.getCid())
                // 手机号码
                .mobile(batchTaskLogDO.getMobile())
                // 发送状态 0->失败 1->成功
                .status(batchTaskLogDO.getStatus())
                // 发送描述信息
                .description(batchTaskLogDO.getDescription())
                // 创建时间
                .createTime(batchTaskLogDO.getCreateTime())
                // 更新时间
                .updateTime(batchTaskLogDO.getUpdateTime())
                // 参数信息
                .param(batchTaskLogDO.getParam())
                // 错误编码
                .errCode(batchTaskLogDO.getErrCode())
                .build();
    }
}
