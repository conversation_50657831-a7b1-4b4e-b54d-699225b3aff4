package com.xhqb.spectre.admin.service;


import com.alibaba.excel.util.DateUtils;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.result.GeneratorResult;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.entity.oa.TplContent;
import com.xhqb.spectre.common.dal.mapper.TplContentMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class SerialNumberGenerator {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private VenusConfig venusConfig;

    @Resource
    private TplContentMapper tplContentMapper;

    @Resource
    private TplMapper tplMapper;

    public String increment(String key) {
        Long increment = stringRedisTemplate.opsForValue().increment(key, 1L);
        stringRedisTemplate.expire(key, 1, TimeUnit.DAYS);
        return String.format("%04d", increment);
    }

    public GeneratorResult getTplResult(TplContent tplContent, SignDO signDO, TplDO tpl) {
        int retryIndex = 0;
        String code = "";
        String increment = "";
        while (retryIndex < venusConfig.getMaxRetryTimes()) {
            String yearFormatDate = DateUtils.format(new Date(), "yyyyMMdd");
            increment = increment("spectre:admin:serialNumber:tpl:" + yearFormatDate + ":");
            code = signDO.getCode() + "_" + tplContent.getSmsTypeCode() + "_" + yearFormatDate + "_" + increment;
            log.info("tplContent:{},signDO:{},tpl:{}", tplContent, signDO, tpl);
            TplDO modelTpl = tplMapper.selectByCodeAndSign(code, tpl.getSignId());
            if (Objects.isNull(modelTpl)) {
                break;
            }
            retryIndex++;

        }
        if (retryIndex == venusConfig.getMaxRetryTimes()) {
            throw new BizException("报备申请id" + tplContent.getContentId() + "生成模版code失败");
        }
        return new GeneratorResult(code, increment);
    }

    public GeneratorResult getCommonResult() {
        String yearFormatDate = DateUtils.format(new Date(), "yyyyMMdd");
        String code = "spectre:admin:serialNumber:common:" + yearFormatDate + ":";
        String increment = increment(code);
        return new GeneratorResult(code, increment);
    }

    public GeneratorResult getReportResult(String prefix) {
        int retryIndex = 0;
        String code = "";
        String increment = "";
        while (retryIndex < venusConfig.getMaxRetryTimes()) {
            String yearFormatDate = DateUtils.format(new Date(), "yyyyMMdd");
            increment = increment("spectre:admin:serialNumber:report:" + yearFormatDate + ":");
            code = prefix + yearFormatDate + increment;
            List<TplContent> modelList = tplContentMapper.selectByContentIdList(Collections.singletonList(code));
            if (CollectionUtils.isEmpty(modelList)) {
                break;
            }
            retryIndex++;

        }
        if (retryIndex == venusConfig.getMaxRetryTimes()) {
            throw new BizException("生成报备申请id失败");
        }
        return new GeneratorResult(code, increment);
    }

    /**
     * 生成短信策略ID
     * 格式：SR + 年月日 + 4位序号（每日从0001重新开始）
     */
    public String generateStrategyId() {
        // 获取当前日期
        String todayStr = DateUtils.format(new Date(), "yyyyMMdd");

        String sequenceStr = increment("spectre:admin:serialNumber:strategyId:" + todayStr + ":");

        return "SR" + todayStr + sequenceStr;
    }

    /**
     * 生成短信规则ID
     * 格式：RULE + 年月日 + 4位序号（每日从0001重新开始）
     */
    public String generateRuleId() {
        // 获取当前日期
        String todayStr = DateUtils.format(new Date(), "yyyyMMdd");

        String sequenceStr = increment("spectre:admin:serialNumber:ruleId:" + todayStr + ":");

        return "RULE" + todayStr + sequenceStr;
    }
}
