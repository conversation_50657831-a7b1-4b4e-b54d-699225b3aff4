package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.AddProjectDescDTO;
import com.xhqb.spectre.admin.model.dto.UpdateProjectDescDTO;
import com.xhqb.spectre.admin.model.vo.ProjectDescEnumVO;
import com.xhqb.spectre.admin.model.vo.ProjectDescVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ProjectDescQuery;

import java.util.List;

public interface ProjectDescService {

    CommonPager<ProjectDescVO> listByPage(ProjectDescQuery projectDescQuery);

    ProjectDescVO getById(Integer id);

    void create(AddProjectDescDTO addProjectDescDTO);

    void update(UpdateProjectDescDTO updateProjectDescDTO);

    void enable(Integer id);

    void disable(Integer id);

    void delete(Integer id);

    List<ProjectDescEnumVO> queryEnum(Integer status);
}
