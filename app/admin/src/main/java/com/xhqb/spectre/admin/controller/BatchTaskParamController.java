package com.xhqb.spectre.admin.controller;

import com.google.common.collect.Lists;
import com.xhqb.spectre.admin.batchtask.cover.BatchCoverFactory;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.BatchTaskParamVO;
import com.xhqb.spectre.admin.service.BatchTaskParamService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.BatchTaskParamDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.BatchTaskParamQuery;
import com.xhqb.spectre.common.enums.BatchTaskParamStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 群发参数
 *
 * <AUTHOR>
 * @date 2021/10/21
 */
@RestController
@RequestMapping("/batchTaskParam")
@Slf4j
public class BatchTaskParamController {

    @Resource
    private BatchTaskParamService batchTaskParamService;
    @Resource
    private BatchCoverFactory batchCoverFactory;

    /**
     * 查询群发参数列表
     *
     * @param batchTaskParamQuery 群发参数查询条件
     * @param pageNum             当前页码
     * @param pageSize            一页显示的记录数
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(BatchTaskParamQuery batchTaskParamQuery, Integer pageNum, Integer pageSize) {
        batchTaskParamQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        List<Integer> sendStatus = batchTaskParamQuery.getSendStatus();
        if (CommonUtil.isEmpty(sendStatus)) {
            // 默认展示0、1
            batchTaskParamQuery.setSendStatus(Lists.newArrayList(BatchTaskParamStatusEnum.UN_SEND.getCode(), BatchTaskParamStatusEnum.SENDING.getCode()));
        }
        Integer isDelete = batchTaskParamQuery.getIsDelete();
        if (Objects.isNull(isDelete)) {
            // 默认查询未删除的数据
            batchTaskParamQuery.setIsDelete(0);
        }
        CommonPager<BatchTaskParamVO> commonPager = batchTaskParamService.listByPage(batchTaskParamQuery);
        return AdminResult.success(commonPager);
    }


    /**
     * 根据taskId 查询群发任务列表
     *
     * @param taskId        群发任务编号
     * @param needParamJson true需要群发参数数据信息 其他值不需要
     * @return
     */
    @GetMapping("/getByTaskId")
    public AdminResult getByTaskId(Integer taskId, Boolean needParamJson) {
        return AdminResult.success(batchTaskParamService.getByTaskId(taskId, needParamJson));
    }

    /**
     * 删除群发参数
     * <p>
     * 只是将数据打上删除标记
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public AdminResult deleteInfo(@PathVariable("id") Integer id) {
        log.info("delete taskParam id = {}, user ={}", id, SsoUserInfoUtil.getUserName());
        batchTaskParamService.delete(id);
        return AdminResult.success();
    }

    /**
     * 重置群发接口
     * <p>
     * 会将deleted状态设置为0
     * 并将任务状态设置为0
     *
     * @param id
     * @return
     */
    @PutMapping("/{id}")
    public AdminResult resetInfo(@PathVariable("id") Integer id) {
        log.info("reset taskParam id = {}, user = {}", id, SsoUserInfoUtil.getUserName());
        batchTaskParamService.reset(id);
        return AdminResult.success();
    }

    /**
     * 补发群发分片任务
     *
     * @param id
     * @return
     */
    @PostMapping("/{id}")
    public AdminResult replayInfo(@PathVariable("id") Integer id) {
        log.info("replay taskParam id = {}, user = {}", id, SsoUserInfoUtil.getUserName());
        batchTaskParamService.replay(id);
        return AdminResult.success();
    }


    /**
     * 群发兜底出来
     *
     * @param taskId
     * @return
     */
    @PostMapping("/cover/{taskId}")
    public AdminResult cover(@PathVariable("taskId") Integer taskId) {
        log.info("cover taskId = {}, user = {}", taskId, SsoUserInfoUtil.getUserName());
        return batchCoverFactory.coverHandler(taskId);
    }

    /**
     * 移除群发兜底处理缓存标记
     *
     * @param taskId
     * @return
     */
    @PostMapping("/removeCoverKey/{taskId}")
    public AdminResult removeCoverKey(@PathVariable("taskId") Integer taskId) {
        log.info("removeCoverKey taskId = {}, user = {}", taskId, SsoUserInfoUtil.getUserName());
        batchCoverFactory.removeCoverKey(taskId);
        return AdminResult.success();
    }

    /**
     * 保存或更新群发分片数据信息
     *
     * @param batchTaskParamDO
     * @return
     */
    @PostMapping("/saveOrUpdate")
    public AdminResult saveOrUpdate(@RequestBody BatchTaskParamDO batchTaskParamDO) {
        batchTaskParamService.saveOrUpdate(batchTaskParamDO);
        return AdminResult.success();
    }
}
