package com.xhqb.spectre.admin.readonly.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.ShortUrlLogDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/8/8 14:42
 **/

@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class ShortUrlLogVO extends ShortUrlLogDO implements Serializable {
    private static final long serialVersionUID = -3656247748408187924L;
    /**
     * 点击时间
     */
    private String clickTimeVO;

    private String createTimeVO;
    /**
     * 短链地址
     */
    private String shortUrl;


    public static ShortUrlLogVO bulilderByDO(ShortUrlLogDO shortUrlLogDO, ShortUrlLogVO shortUrlLogVO,String shortUrl) {
        BeanUtils.copyProperties(shortUrlLogDO, shortUrlLogVO, "clickTime");
        shortUrlLogVO.setClickTimeVO(DateUtil.intToString(shortUrlLogDO.getClickTime()));
        shortUrlLogVO.setCreateTimeVO(DateUtil.dateToString(shortUrlLogDO.getCreateTime()));
        shortUrlLogVO.setShortUrl(shortUrl);
        return shortUrlLogVO;
    }

}
