package com.xhqb.spectre.admin.mq.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 群发参数结果
 * <p>
 * 主要记录发送的总记录数以及真正发送成功的数量
 *
 * <AUTHOR>
 * @date 2021/12/8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskParamResult {

    /**
     * 总记录数
     */
    private int total;

    /**
     * 成功数
     */
    private int success;
}
