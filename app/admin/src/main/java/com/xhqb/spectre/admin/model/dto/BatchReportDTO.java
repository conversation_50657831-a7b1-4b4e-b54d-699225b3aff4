package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Data
public class BatchReportDTO {

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    private String title;

    /**
     * 短信类型编码
     */
    @NotBlank(message = "短信类型编码不能为空")
    private String smsTypeCode;

    /**
     * 使用场景
     */
    @NotBlank(message = "使用场景不能为空")
    private String sceneCode;

    /**
     * 编码和短信内容映射 key: code-signId value:短信内容
     */
    private Map<String, String> codeContentMap;

    /**
     * 备注
     */
    private String remark;



}
