package com.xhqb.spectre.admin.job;

import cn.hutool.core.collection.CollectionUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.common.dal.entity.test.TestMobileDO;
import com.xhqb.spectre.common.dal.mapper.TestMobileMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


@Component
@Job("testMobileStatusRefreshJob")
@Slf4j
public class TestMobileStatusRefreshJob implements SimpleJob {

    @Resource
    private TestMobileMapper testMobileMapper;


    @Override
    public void execute(ShardingContext shardingContext) {
        long start = System.currentTimeMillis();
        log.info("testMobileStatusRefreshJob start:{}", start);
        List<TestMobileDO> modelList = testMobileMapper.selectInvalid();
        if (CollectionUtil.isEmpty(modelList)) {
            return;
        }
        List<String> mobiles = modelList.stream().map(TestMobileDO::getMobile).collect(Collectors.toList());
        int updateAffect = testMobileMapper.updateStatusByMobileList(mobiles);
        log.info("testMobileStatusRefreshJob 更新条数:{}|耗时:{}", updateAffect, (System.currentTimeMillis() - start));
    }
}
