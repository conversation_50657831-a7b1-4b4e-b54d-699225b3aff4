package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/17 16:23
 * @Description:
 */
@Data
public class TplTestDTO implements Serializable {

    private static final long serialVersionUID = -8920745916290032095L;

    /**
     * 参数列表
     */
    private List<LinkedHashMap<String, String>> paramList;

    /**
     * 手机号码,多个手机号码使用逗号分割
     */
    @NotBlank(message = "手机号码不能为空")
    private String mobile;
    /**
     * 渠道id，如果设置了该参数就不读取模版关联的渠道信息
     */
    private Integer channelAccountId;

    /**
     * 发送类型 0:正常发短信 1->基于内容发送
     */
    private Integer sendType;

    /**
     * 短信模板内容
     */
    private String content;

}
