package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.BatchTaskBigdataDTO;
import com.xhqb.spectre.admin.model.vo.BatchTaskBigdataVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.BatchTaskBigdataQuery;

/**
 * 大数据群发任务
 *
 * <AUTHOR>
 * @date 2021/10/28
 */
public interface BatchTaskBigdataService {


    /**
     * 分页查询大数据群发任务列表
     *
     * @param batchTaskBigdataQuery
     * @return
     */
    CommonPager<BatchTaskBigdataVO> listByPage(BatchTaskBigdataQuery batchTaskBigdataQuery);

    /**
     * 保存大数据群发任务信息
     *
     * @param batchTaskBigdataDTO
     */
    void create(BatchTaskBigdataDTO batchTaskBigdataDTO);

    /**
     * 更新大数据群发任务信息
     *
     * @param id
     * @param batchTaskBigdataDTO
     */
    void update(Integer id, BatchTaskBigdataDTO batchTaskBigdataDTO);
}
