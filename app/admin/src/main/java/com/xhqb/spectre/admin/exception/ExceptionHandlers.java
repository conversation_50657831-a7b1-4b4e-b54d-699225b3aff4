package com.xhqb.spectre.admin.exception;

import com.xhqb.spectre.admin.enums.RespCodeEnum;
import com.xhqb.spectre.admin.model.result.AdminResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

@Slf4j
@ResponseBody
@ControllerAdvice
public class ExceptionHandlers {

    @ExceptionHandler(BizException.class)
    protected AdminResult bizExceptionHandler(final BizException e) {
        log.warn(e.getMessage());
        return AdminResult.error(e);
    }

    @ExceptionHandler(Exception.class)
    protected AdminResult serverExceptionHandler(final Exception e) {
        log.error(e.getMessage(), e);
        return AdminResult.error(RespCodeEnum.SYSTEM_ERROR);
    }

    @ExceptionHandler(Throwable.class)
    protected AdminResult throwableHandler(final Throwable e) {
        log.error(e.getMessage(), e);
        return AdminResult.error(RespCodeEnum.SYSTEM_ERROR);
    }

    @ExceptionHandler(NullPointerException.class)
    protected AdminResult nullPointExceptionHandler(final NullPointerException e) {
        log.error(e.getMessage(), e);
        return AdminResult.error(RespCodeEnum.NULL_POINTER_ERROR);
    }

    @ExceptionHandler(DuplicateKeyException.class)
    protected AdminResult serverExceptionHandler(final DuplicateKeyException e) {
        log.warn(e.getMessage(), e);
        return AdminResult.error(RespCodeEnum.DB_UNIQUE_CONFLICT_ERROR);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    protected AdminResult handleHttpRequestMethodNotSupportedException(final HttpRequestMethodNotSupportedException e) {
        log.warn(e.getMessage(), e);
        return AdminResult.error("不支持" + e.getMethod() + "请求方法");
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    protected AdminResult handleMissingServletRequestParameterException(final MissingServletRequestParameterException e) {
        log.warn(e.getMessage(), e);
        return AdminResult.error(RespCodeEnum.PARAM_ERROR);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    protected AdminResult handleMethodArgumentTypeMismatchException(final MethodArgumentTypeMismatchException e) {
        log.warn(e.getMessage(), e);
        return AdminResult.error(RespCodeEnum.PARAM_ERROR);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    protected AdminResult handleHttpMessageNotReadableException(final HttpMessageNotReadableException e) {
        log.warn(e.getMessage(), e);
        return AdminResult.error(RespCodeEnum.PARAM_ERROR);
    }

    @ExceptionHandler(BindException.class)
    protected AdminResult handleBindException(final BindException e) {
        log.warn(e.getMessage());
        return AdminResult.error(RespCodeEnum.PARAM_ERROR.getCode(), "请求参数格式有误");
    }
}
