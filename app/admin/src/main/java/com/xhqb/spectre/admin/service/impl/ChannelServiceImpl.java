package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.ChannelDTO;
import com.xhqb.spectre.admin.model.vo.ChannelEnumVO;
import com.xhqb.spectre.admin.model.vo.ChannelVO;
import com.xhqb.spectre.admin.service.ChannelService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.ChannelDO;
import com.xhqb.spectre.common.dal.mapper.ChannelMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ChannelQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 11:21
 * @Description:
 */
@Service
@Slf4j
public class ChannelServiceImpl implements ChannelService {

    @Autowired
    private ChannelMapper channelMapper;

    /**
     * 查询渠道枚举
     *
     * @return
     */
    @Override
    public List<ChannelEnumVO> queryEnum() {
        return channelMapper.selectEnum().stream().map(ChannelEnumVO::buildChannelEnumVO).collect(Collectors.toList());
    }

    /**
     * 查询渠道列表
     *
     * @param channelQuery
     * @return
     */
    @Override
    public CommonPager<ChannelVO> listByPage(ChannelQuery channelQuery) {
        return PageResultUtils.result(
                () -> channelMapper.countByQuery(channelQuery),
                () -> channelMapper.selectByQuery(channelQuery).stream().map(ChannelVO::buildChannelVO).collect(Collectors.toList())
        );
    }

    /**
     * 查询渠道详情
     *
     * @param id
     * @return
     */
    @Override
    public ChannelVO getById(Integer id) {
        ChannelDO channelDO = validateAndSelectById(id);
        return ChannelVO.buildChannelVO(channelDO);
    }

    /**
     * 创建渠道
     *
     * @param channelDTO
     */
    @Override
    public void create(ChannelDTO channelDTO) {
        //校验
        checkParam(channelDTO);

        //写入渠道信息
        ChannelDO channelDO = buildChannelDO(channelDTO);
        channelMapper.insertSelective(channelDO);
    }

    private ChannelDO validateAndSelectById(Integer id) {
        ChannelDO channelDO = channelMapper.selectByPrimaryKey(id);
        if (Objects.isNull(channelDO)) {
            throw new BizException("未找到该渠道");
        }
        return channelDO;
    }

    private void checkParam(ChannelDTO channelDTO) {
        //参数格式校验
        ValidatorUtil.validate(channelDTO);

        //渠道编码校验
        ChannelDO exist = channelMapper.selectByCode(channelDTO.getCode());
        if (Objects.nonNull(exist)) {
            throw new BizException("渠道编码已存在");
        }
    }

    private ChannelDO buildChannelDO(ChannelDTO channelDTO) {
        String userName = SsoUserInfoUtil.getUserName();
        return ChannelDO.builder()
                .code(channelDTO.getCode())
                .name(channelDTO.getName())
                .creator(userName)
                .updater(userName)
                .build();
    }
}
