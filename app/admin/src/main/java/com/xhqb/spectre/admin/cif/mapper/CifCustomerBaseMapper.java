package com.xhqb.spectre.admin.cif.mapper;

import com.xhqb.spectre.admin.cif.entity.CifCustomerBaseDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * cifdb.t_customer_base 处理
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
public interface CifCustomerBaseMapper {

    /**
     * 根据cid获取用户手机号码
     *
     * @param id
     * @return
     */
    CifCustomerBaseDO selectByPrimaryKey(String id);

    /**
     * 根据cid列表查询用户信息
     *
     * @param idList     id列表
     * @param statusList 状态列表
     * @return
     */
    List<CifCustomerBaseDO> selectByIdList(@Param("idList") List<String> idList, @Param("statusList") List<String> statusList);


    /**
     * 根据cid列表查询用户信息
     *
     * @param mobileList 手机号码列表
     * @return
     */
    List<CifCustomerBaseDO> selectByMobileList(@Param("mobileList") Collection<String> mobileList);


    List<CifCustomerBaseDO> selectByCidList(@Param("idList") List<String> idList);

}
