package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.Data;

import java.io.Serializable;

@Data
public class TplHeatDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private String startDate;
    private String endDate;
    private String smsTypeCode;
    private String tplCode;
    private PageParameter pageParameter;

    public String getSmsTypeCode() {
        return CommonUtil.camelToSnake(smsTypeCode);
    }
}
