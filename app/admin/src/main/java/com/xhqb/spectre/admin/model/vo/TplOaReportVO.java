package com.xhqb.spectre.admin.model.vo;


import lombok.Data;

import java.util.Date;

import java.util.List;
import java.util.Set;

@Data
public class TplOaReportVO {

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 原始内容
     */
    private List<String> originalContent;

    /**
     *  标题
     */
    private String title;

    /**
     *  已报备短信内容
     */
    private Set<String> contentSet;

    /**
     * 报备人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date  updateTime;


    /**
     * 备注
     */
    private String remark;

    /**
     * 流程 id
     */
    private String flowId;

    /**
     * 审批状态
     */
    private Integer approveStatus;


    /**
     * 申请人 Id
     */
    private String userId;


}
