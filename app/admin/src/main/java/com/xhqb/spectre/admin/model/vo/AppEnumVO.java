package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.AppDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 10:23
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppEnumVO implements Serializable {

    private static final long serialVersionUID = -4143581547956351204L;

    private String code;

    private String name;

    public static AppEnumVO buildAppEnumVO(AppDO appDO) {
        return AppEnumVO.builder()
                .code(appDO.getCode())
                .name(appDO.getName())
                .build();
    }
}
