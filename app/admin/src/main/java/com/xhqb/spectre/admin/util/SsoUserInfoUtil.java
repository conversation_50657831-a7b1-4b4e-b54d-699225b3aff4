package com.xhqb.spectre.admin.util;

import com.xhqb.ucenter.sso.client.rpc.SsoUser;
import com.xhqb.ucenter.sso.client.util.SessionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Optional;

/**
 * 获取用户登入态下的用户信息
 */
public class SsoUserInfoUtil {

    /**
     * 获取当前登录用户姓名
     *
     * @return
     */
    public static String getUserName() {
        HttpServletRequest request = RequestUtil.getRequest();
        if (Objects.isNull(request)) {
            return "";
        }
        SsoUser ssoUser = SessionUtils.getUser(request);
        return Optional.ofNullable(ssoUser).map(SsoUser::getUserId).orElse("");
    }

    /**
     * 获取到登录对象
     *
     * @return
     */
    public static SsoUser getSubject() {
        HttpServletRequest request = RequestUtil.getRequest();
        if (Objects.isNull(request)) {
            return null;
        }
        return SessionUtils.getUser(request);
    }
}
