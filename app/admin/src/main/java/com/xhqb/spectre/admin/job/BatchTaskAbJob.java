package com.xhqb.spectre.admin.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.batchtask.ab.BatchTaskAbJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 群发短信灰度触达率任务
 * <p>
 * 统计短信发送的触达率
 *
 * <AUTHOR>
 * @date 2021/12/29
 */
@Component
@Job("batchTaskAbJob")
@Slf4j
public class BatchTaskAbJob implements SimpleJob {

    @Resource
    private BatchTaskAbJobHandler batchTaskAbJobHandler;

    @Override
    public void execute(ShardingContext shardingContext) {
        long start = System.currentTimeMillis();
        log.info("开始准备处理群发短信灰度任务");
        batchTaskAbJobHandler.jobHandler();
        log.info("处理群发短信灰度任务完成,耗时 ={}", (System.currentTimeMillis() - start));
    }
}
