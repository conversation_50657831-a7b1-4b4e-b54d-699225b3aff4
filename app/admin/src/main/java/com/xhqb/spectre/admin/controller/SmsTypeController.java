package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.SmsTypeVO;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 短信类型
 */
@RestController
@RequestMapping("/smsTypes")
@Slf4j
public class SmsTypeController {

    /**
     * 短信类型列表
     *
     * @return
     */
    @GetMapping("")
    public AdminResult getSmsTypes() {
        log.info("查询短信类型列表");
        List<SmsTypeVO> smsTypeVOList = Arrays.stream(MessageTypeEnum.values())
                .filter(messageType -> !messageType.equals(MessageTypeEnum.UNKNOWN))
                .map(SmsTypeVO::toSmsTypeVO)
                .collect(Collectors.toList());
        return AdminResult.success(smsTypeVOList);
    }
}
