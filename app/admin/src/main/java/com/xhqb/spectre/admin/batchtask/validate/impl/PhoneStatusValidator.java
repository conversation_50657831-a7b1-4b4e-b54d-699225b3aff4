package com.xhqb.spectre.admin.batchtask.validate.impl;

import com.xhqb.spectre.admin.batchtask.aggregator.impl.PhoneStatusAggregator;
import com.xhqb.spectre.admin.batchtask.cid.CidStrategyFactory;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.validate.ContentValidator;
import com.xhqb.spectre.admin.batchtask.validate.ValidateContext;
import com.xhqb.spectre.admin.batchtask.validate.ValidateResult;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 手机号码状态验证
 *
 * <AUTHOR>
 * @date 2022/1/18
 */
@Component
@Slf4j
public class PhoneStatusValidator implements ContentValidator {

    @Resource
    private PhoneStatusAggregator phoneStatusAggregator;
    @Resource
    private CidStrategyFactory cidStrategyFactory;

    /**
     * 数据验证
     *
     * @param validateContext
     * @param validateResult
     */
    @Override
    public void validate(ValidateContext validateContext, ValidateResult validateResult) {
        if (!MessageTypeEnum.isMarket(validateContext.getSmsTypeCode())) {
            // 非营销类群发 不再检测手机状态 (2024-09-04)
            return;
        }
        // List<String> phoneStatusList = cidStrategyFactory.toListByKey(CidStrategyEnum.PHONE_STATUS.getCode());
        List<String> phoneStatusList = validateContext.getPhoneStatusList();
        if (phoneStatusList != null) {
            phoneStatusList.remove(RepeatContentValidator.PHONE_STATUS);
        }
        if (CommonUtil.isEmpty(phoneStatusList)) {
            // 未配置手机状态过滤
            validateResult.setPhoneEmptyList(new ArrayList<>());
            validateResult.setPhoneHaltList(new ArrayList<>());
            return;
        }
        log.info("设置了手机号码状态校验策略,phoneStatusList = {}", phoneStatusList);
        try {
            phoneStatusAggregator.aggregate(validateContext, validateResult);
        } catch (Exception e) {
            // 手机状态校验失败不影响整体流程
            log.warn("手机号码策略校验失败,fileName = {},fileMd5 = {}", validateContext.getFileName(), validateContext.getFileMd5(), e);
        }

    }

    @Override
    public int getOrder() {
        return BatchTaskConstants.ValidatorOrdered.PHONE_STATUS;
    }

}
