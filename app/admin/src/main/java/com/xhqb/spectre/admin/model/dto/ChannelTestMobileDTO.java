package com.xhqb.spectre.admin.model.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
public class ChannelTestMobileDTO {
    /**
     * 测试手机号 191,192,193
     */
    @NotBlank(message = "mobiles不能为空")
    private String mobiles;
    /**
     * 测试手机号类型
     */
    @NotNull(message = "mobileType不能为空")
    @Range(min = 0, max = 2, message = "mobileType应为包含或不包含")
    private Integer type;

    @Size(min = 0, max = 255, message = "remark最大为{max}个字符")
    private String remark;

    /**
     * 空号检测  0 是不检测 1 是检测
     */
    private Integer detectTag;

    /**
     *  失效时间
     */
    private String expiredTime;
}
