package com.xhqb.spectre.admin.readonly.vo;

import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.SmsReceiptDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/30 15:24
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsReceiptVO implements Serializable {

    private static final long serialVersionUID = 549232538221505443L;

    private static final int STATUS_FAILED = 1;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 短信订单号
     */
    private String orderId;

    /**
     * 渠道账号ID
     */
    private Integer channelAccountId;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 模版编码
     */
    private String tplCode;

    /**
     * 渠道msgId
     */
    private String channelMsgId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 回执状态，1：接收成功，2：接收失败
     */
    private Integer status;

    /**
     * 发送短信的应答结果
     */
    private String reportStatus;

    /**
     * 回执描述
     */
    private String reportDesc;

    /**
     * Submit提交时间
     */
    private String submitTime;

    /**
     * 发送完成时间
     */
    private String doneTime;

    /**
     * 接收到的回执时间
     */
    private String recvReportTime;

    /**
     * 目标终端
     */
    private String destTerminalId;

    /**
     * 创建时间
     */
    private String createTime;

    public static SmsReceiptVO buildSmsReceiptVO(SmsReceiptDO item) {
        return SmsReceiptVO.builder()
                .id(item.getId())
                .orderId(String.valueOf(item.getOrderId()))
                .channelAccountId(item.getChannelAccountId())
                .channelCode(item.getChannelCode())
                .tplCode(item.getTplCode())
                .channelMsgId(item.getChannelMsgId())
                .mobile(CommonUtil.maskMobile(item.getMobile()))
                .status(formatStatus(item.getStatus()))
                .reportStatus(item.getReportStatus())
                .reportDesc(item.getReportDesc())
                .submitTime(DateUtil.intToString(item.getSubmitTime()))
                .recvReportTime(DateUtil.intToString(item.getRecvReportTime()))
                .doneTime(DateUtil.intToString(item.getDoneTime()))
                .destTerminalId(item.getDestTerminalId())
                .createTime(DateUtil.dateToString(item.getCreateTime()))
                .build();
    }

    /**
     * 格式化状态值，将失败状态统一为1，传给前端页面
     *
     * @param status
     * @return
     */
    private static int formatStatus(Integer status) {
        return status > 0 ? STATUS_FAILED : status;
    }
}
