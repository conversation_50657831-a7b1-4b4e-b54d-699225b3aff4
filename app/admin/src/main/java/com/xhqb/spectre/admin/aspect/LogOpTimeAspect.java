package com.xhqb.spectre.admin.aspect;

import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.mapper.OpTimeMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/29 14:08
 * @Description:
 */
@Component
@Aspect
@Slf4j
public class LogOpTimeAspect {

    @Autowired
    private OpTimeMapper opTimeMapper;

    @Pointcut(value = "@annotation(com.xhqb.spectre.admin.annotation.LogOpTime)")
    public void access() {

    }

    /**
     * 记录配置变更时间
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("access()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();
        try {
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            Method method = methodSignature.getMethod();
            LogOpTime annotation = method.getAnnotation(LogOpTime.class);
            opTimeMapper.updateOpTime(annotation.value(), DateUtil.getNow());
        } catch (Exception e) {
            log.error("doAround Exception", e);
        }

        return result;
    }
}
