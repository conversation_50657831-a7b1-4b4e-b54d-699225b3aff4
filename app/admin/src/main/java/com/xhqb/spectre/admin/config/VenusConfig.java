package com.xhqb.spectre.admin.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/10/11
 */
@Component
@Data
public class VenusConfig {

    /**
     * cmpp应用地址
     * 测试环境:https://spectre-test.xhdev.xyz/spectre-receiptcmpp
     */
    @Value("${spectre.admin.receiptCmppHost:http://localhost:8080/spectre-receiptcmpp}")
    private String receiptCmppHost;

    /**
     * cmpp应用端口
     */
    @Value("${spectre.admin.receiptCmppPort:8080}")
    private Integer receiptCmppPort;

    /**
     * cmpp状态阈值(秒)
     * cmpp缓存中的time 与 当前时间搓相减对比值
     * 用于判断cmpp状态是否正常
     */
    @Value("${spectre.admin.cmppStatusThresholdSecond:60}")
    private Integer cmppStatusThresholdSecond;

    /**
     * API服务器地址
     * 测试环境地址： https://spectre-test.xhdev.xyz/spectre-api
     */
    @Value("${spectre.admin.spectreApiHost:http://localhost:8080/spectre-api}")
    private String spectreApiHost;

    /**
     * log打印控制  true->打印日志 false->不打印日志
     */
    @Value("${spectre.admin.logEnable:false}")
    private Boolean logEnable;

    /**
     * spectre-api接口批量发送手机号码数量
     */
    @Value("${spectre.admin.apiMobileGroupCount:90}")
    private Integer apiMobileGroupCount;

    /**
     * 是否对群发任务提交进行时间校验
     * 晚上20:00到次日9:00 不能够立即发送
     */
    @Value("${spectre.admin.batchTaskSubmitHourCheck:true}")
    private Boolean batchTaskSubmitHourCheck;

    /**
     * 批量更新群发参数信息时一次更新条数
     */
    @Value("${spectre.admin.batchUpdateTaskParamCount:50}")
    private Integer batchUpdateTaskParamCount;

    /**
     * cmppServer服务器地址
     * 测试环境地址：
     */
    @Value("${spectre.admin.cmppServerHost:http://localhost:8080/}")
    private String cmppServerHost;

    /**
     * 大数据任务自动提交开关
     * true  -> 表示大数据任务处理完时自动提交群发任务
     * false -> 表示需要人工通过管理后台进行提交
     */
    @Value("${spectre.admin.taskBigdataSubmitEnable:false}")
    private Boolean taskBigdataSubmitEnable;

    /**
     * 短链编码的长度
     */
    @Value("${short-url.code.length:5}")
    private Integer shortCodeLength;

    /**
     * 短链域名
     */
    @Value("${short-url.domain:xh1.cn}")
    private String shortUrlDomain;

    /**
     * 是否需要进行完件过滤处理
     */
    @Value("${spectre.admin.batchTaskWanJianSwitch:true}")
    private Boolean batchTaskWanJianSwitch;

    /**
     * 在未定义cid策略时，是否使用默认用户状态开关，默认不使用
     */
    @Value("${spectre.admin.isUseDefaultUserStatusSwitch:false}")
    private Boolean isUseDefaultUserStatusSwitch;

    /**
     * 群发任务灰度分片数量
     */
    @Value("${spectre.admin.ab.segmentSize:2}")
    private Integer abSegmentSize;

    /**
     * 群发任务灰度触达率配置 只能在0-100之间
     */
    @Value("${spectre.admin.ab.rate:70}")
    private BigDecimal abRate;

    /**
     * 群发任务灰度时间 单位分钟
     */
    @Value("${spectre.admin.ab.jobMinute:5}")
    private Integer abJobMinute;

    /**
     * 群发灰度任务通知策略
     */
    @Value("${spectre.admin.ab.warning.strategyId:}")
    private String abWarningStrategyId;
    /**
     * 群发灰度任务通知钉钉账号
     */
    @Value("${spectre.admin.ab.warning.dingDingAccount:}")
    private String abWarningDingDingAccount;
    /**
     * 群发灰度任务钉钉机器人密钥
     */
    @Value("${spectre.admin.ab.warning.dingDingSecret:}")
    private String abWarningDingDingSecret;

    /**
     * 群发灰度任务钉钉机器人需要@的用户手机号列表
     */
    @Value("#{'${spectre.admin.ab.warning.dingDingAtMobile:}'.split(',')}")
    private List<String> abWarningDingDingAtMobile;

    /**
     * 创建人没有手机号码时默认通知的人
     */
    @Value("#{'${spectre.admin.ab.warning.missCreatorDefaultMobile:}'.split(',')}")
    private List<String> missCreatorDefaultMobile;

    /**
     * 默认的应用编码
     */
    @Value("${spectre.admin.defaultAppCode:spectre}")
    private String defaultAppCode;
    /**
     * 默认的应用密钥
     */
    @Value("${spectre.admin.defaultAppSecret:cefbda87d553a92b7d8b}")
    private String defaultAppSecret;

    /**
     * 关联发送记录列表显示的数据量
     */
    @Value("${spectre.admin.uplinkRelationOrderPageSize:5}")
    private Integer uplinkRelationOrderPageSize;

    /**
     * 关联发送记录列表默认七天
     */
    @Value("${spectre.admin.uplinkRelationOrderDays:-7}")
    private Integer uplinkRelationOrderDays;

    /**
     * 上行短信回复加入黑名单条件
     */
    @Value("${sms-job.smsUplink.black:T}")
    private String smsUplinkKeyword;


    /**
     * short url服务器地址
     * 测试环境地址： https://spectre-test.xhdev.xyz/spectre-api
     */
    @Value("${spectre.short.url.spectreShortUrlHost:http://localhost:8080/spectre-url-shortener}")
    private String spectreUrlShortenerHost;

    /**
     * 渠道短信更新记录时间天数
     */
    @Value("${spectre.admin.channelSmsUpdateRecordTimeDay:3}")
    private Integer channelSmsUpdateRecordTimeDay;

    /**
     * 告警天数
     */
    @Value("${spectre.admin.alarmDay:-1}")
    private Integer alarmDay;


    /**
     * 服务名
     */
    @Value("${venus.appId}")
    private String serverName;
    /**
     * omini 策略id
     */
    @Value("${test_channel_alert.omini_strategy_id:20240529000000018003}")
    private String testChannelAlertStrategyId;


    @Value("${spectre.sdk.sms.appCode:}")
    private String appCode;

    @Value("${spectre.sdk.sms.appSecret:}")
    private String appSecret;

    /**
     * 空号检测  true->检测 false->不检测
     */
    @Value("${spectre.admin.emptyDetectEnable:true}")
    private Boolean emptyDetectEnable;

    @Value("${spectre.admin.params.init.value.size:10}")
    private int paramsInitValueSize;


    /**
     * 群发提交速率限制
     */
    @Value("${spectre.admin.batchSubmitRateLimit:4}")
    private int batchSubmitRateLimit;

    /**
     * 群发提交速率开关
     */
    @Value("${spectre.admin.batchSubmitRateEnable:true}")
    private boolean batchSubmitRateEnable;


    @Value("${sharding.sql.print.enable:false}")
    private String sqlPrintEnable;

    /**
     * 2024-01-31 23:59:59
     */
    @Value("${sharding.start.time.millis:1706716799000}")
    private long shardingStartTimeMillis;

    @Value("${spectre.admin.bidata.send.stat.topTen.params:appCode,tplCode,channelCode,provinceShortName}")
    private String biDataSendStatTopTenParams;

    @Value("${spectre.admin.bidata.send.stat.reachRate.params:appCode,ispCode,channelCode,provinceShortName}")
    private String biDataSendStatReachRateParams;
    @Value("${spectre.admin.oa.appId:8A685CD6-5343-EB13-18F7-4ECED1875539}")
    private String oaAppId;

    @Value("${spectre.admin.oa.userInfoByUserId:51369}")
    private String userInfoByUserId;

    @Value("${spectre.admin.oa.oaCreateRequestInfo:{\"workcode\":\"sqrgh\",\"id\":\"sqr\",\"managerid\":\"zjsj\",\"subcompanyid1\":\"sqrgs\",\"jobtitle\":\"sqrgw\",\"departmentid\":\"sqrbm\",\"title\":\"bt\",\"smsTypeCode\":\"dxlx\",\"sceneCode\":\"dxsycj\",\"content\":\"dxnr\",\"contentType\":\"nbwbdx\",\"channelName\":\"qdjc\"}}")
    private String oaCreateRequestInfo;

    @Value("${spectre.admin.oa.workFlowId:39721}")
    private Integer oaWorkFlowId;

    @Value("${spectre.admin.oa.serviceUrl:https://oa.chinatopcredit.com}")
    private String oaServiceUrl;
    /**
     * 模拟数据开关
     * true -> 开启模拟数据
     * false -> 关闭模拟数据
     */
    @Value("${spectre.admin.mockEnable:false}")
    private Boolean mockEnable;

    @Value("${spectre.admin.mockDate:2024-11-03 00:00:00}")
    private String mockDate;

    @Value("${spectre.admin.maxRetryTimes:10}")
    private Integer maxRetryTimes;

    @Value("${spectre.admin.outChannel:{\"jinxintian\":\"金信天\",\"iflytek\":\"科大讯飞\",\"suniu\":\"速牛\",\"speech\":\"思必驰\"}}")
    private String outChannel;

    @Value("${spectre.admin.oaDetailTableDbName:formtable_main_345_dt1}")
    private String oaDetailTableDbName;

    @Value("${spectre.admin.oaDetailTableColumns:dxnr,mbid}")
    private String oaDetailTableColumns;


    /**
     * omini 策略id
     */
    @Value("${tpl_notify_applicant.omini_strategy_id:20241216000000070000}")
    private String tplNotifyApplicantStrategyId;

    @Value("${spectre.admin.xhqbSignId:1}")
    private Integer xhqbSignId;
    @Value("${spectre.admin.xhqbSignName:【小花钱包】}")
    private String xhqbSignName;

    @Value("${spectre.admin.queryDay:1}")
    private Integer queryDay;

    @Value("${spectre.admin.urlLinkExpiredDays:15}")
    private Integer urlLinkExpiredDays;

    @Value("${spectre.admin.testContentSignName:小花钱包,小花金融,小花AI助手}")
    private String testContentSignName;

    @Value("${spectre.admin.testContentAppCode:spectre-test}")
    private String testContentAppCode;

    @Value("${spectre.admin.testContentAppSecret:2d2518b2a20a8af6a2e8}")
    private String testContentAppSecret;

    @Value("${spectre.admin.testContentTplCode:http_spectre-test_market_tpl}")
    private String testContentTplCode;

    @Value("${spectre.admin.short.url.stat.enable:false}")
    private Boolean shortUrlCurlStatEnable;

    /**
     * 测试文案任务策略 id
     */
    @Value("${spectre.admin.test.content.task.notify.strategy.id:}")
    private String testContentTaskNotifyStrategyId;

    /**
     * 测试文案任务通知人员
     */
    @Value("${spectre.admin.test.content.task.notifier:}")
    private String testContentTaskNotifier;

    /**
     * 测试文案任务倒计时
     */
    @Value("${spectre.admin.test.content.task.countdown:300}")
    private Long testContentTaskCountdown;

    /**
     * 模板停用通知策略 id
     */
    @Value("${spectre.admin.tpl.disable.notify.strategyId:20250627000000079006}")
    private String tplDisableNotifyStrategyId;

    @Value("${spectre.admin.tpl.reach.rate.notify.strategyId:20250627000000079007}")
    private String TplReachRateLowNotifyStrategyId;

    /**
     * 模板创建人固定通知人员配置
     */
    @Value("${spectre.admin.creator.tpl.notify.user:chenmengyun}")
    private String creatorTplNotifyUser;

    @Value("${spectre.admin.tpl.reachRate.low.notify.smsType:market,notify}")
    private String reachRateLowNotifySmsType;

    @Value("${spectre.admin.tpl.reach.count.limit:0}")
    private Integer tplReachCountLimit;

    @Value("${spectre.admin.tpl.reach.rate.threshold:0.8}")
    private double reachRateThreshold;


    @Value("${spectre.admin.oa.expire.time:25L}")
    private Long oaExpireTime;


    @Value("${spectre.admin.effective.tpl.notify.strategyId:20250707000000079009}")
    private String effectiveTplNotifyStrategyId;

    /**
     * 模板固定通知人员配置
     */
    @Value("${spectre.admin.tpl.notify.user:chenlang}")
    private String tplNotifyUser;


    @Value("${spectre.admin.available.channel.notify.enable:false}")
    private Boolean availableChannelNotifyEnable;

    @Value("${spectre.admin.available.channel.notify.batch.send.count:50}")
    private Integer availableChannelNotifyBatchSendCount;

    /**
     * 运营商配置
     */
    @Value("${spectre.admin.ispCode.all.config:移动,联通,电信}")
    private String ispCodeAllConfig;

    @Value("${spectre.admin.topNForSendCount:5}")
    private int topNForSendCount;

    @Value("${spectre.admin.userNameAndEmail:yangjiqiu=<EMAIL>,wangxianlong=<EMAIL>}")
    private String userNameAndEmail;

    @Value("${spectre.admin.omini.strategy.id.sendIspReachRateToFeiShuJob:20250716000000079016}")
    private String sendIspReachRateToFeiShuJob;

    @Value("${spectre.admin.omini.strategy.id.alertTopSendCountToFeiShuJob:20250716000000079017}")
    private String alertTopSendCountToFeiShuJob;

    @Value("${spectre.admin.omini.strategy.id.operatorTopSendCountFeiShuNotifyJob:20250717000000079020}")
    private String operatorTopSendCountFeiShuNotifyJob;

    @Value("${spectre.admin.topNIspCode:10}")
    private int topNIspCode;


    @Value("${spectre.admin.resend.appCode:spectre-resend}")
    private String resendAppCode;

    @Value("${spectre.admin.resend.appSecret:bf39b21ed0fafcf1c36e}")
    private String resendAppSecret;

    /**
     * 日志打印是否开启
     *
     * @return
     */
    public boolean isLogEnable() {
        return Objects.equals(logEnable, true);
    }
}
