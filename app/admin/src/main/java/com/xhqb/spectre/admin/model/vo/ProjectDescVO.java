package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.ProjectDescDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectDescVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Integer id;

    private String description;

    private Integer status;

    private String createTime;

    private String creator;

    private String updateTime;

    private String updater;

    public static ProjectDescVO buildVO(ProjectDescDO projectDescDO) {
        return ProjectDescVO.builder()
                .id(projectDescDO.getId())
                .description(projectDescDO.getDescription())
                .status(projectDescDO.getStatus())
                .createTime(DateUtil.dateToString(projectDescDO.getCreateTime()))
                .creator(projectDescDO.getCreator())
                .updateTime(DateUtil.dateToString(projectDescDO.getUpdateTime()))
                .updater(projectDescDO.getUpdater())
                .build();
    }
}
