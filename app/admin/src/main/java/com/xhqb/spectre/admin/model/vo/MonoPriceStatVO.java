package com.xhqb.spectre.admin.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xhqb.spectre.admin.bidata.entity.ChannelPriceStatDO;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonoPriceStatVO {
    /**
     * 统计日期
     * 月份
     */
    @ExcelProperty({"", "日期"})
    private String statDate;
    /**
     * 渠道编码
     */
    @ExcelIgnore
    private String channelCode;
    @ExcelProperty({"", "供应商"})
    private String channelCodeName;

    @ExcelProperty({"触达率", "验证码"})
    private String verifyReachRate;
    @ExcelProperty({"触达率", "通知"})
    private String notifyReachRate;
    @ExcelProperty({"触达率", "营销"})
    private String marketReachRate;
    @ExcelProperty({"触达率", "催收"})
    private String collectorReachRate;
    @ExcelProperty({"触达率", "轻催"})
    private String lightCollectorReachRate;
    @ExcelProperty({"触达率", "重催"})
    private String severeCollectorReachRate;
    @ExcelProperty({"触达率", "债转"})
    private String debtSwapReachRate;
    @ExcelProperty({"触达率", "平均率"})
    private String avgReachRate;

    @ExcelProperty({"费用", "验证码"})
    private BigDecimal verifyPriceCount;
    @ExcelProperty({"费用", "通知"})
    private BigDecimal notifyPriceCount;
    @ExcelProperty({"费用", "营销"})
    private BigDecimal marketPriceCount;
    @ExcelProperty({"费用", "催收"})
    private BigDecimal collectorPriceCount;
    @ExcelProperty({"费用", "轻催"})
    private BigDecimal lightCollectorPriceCount;
    @ExcelProperty({"费用", "重催"})
    private BigDecimal severeCollectorPriceCount;
    @ExcelProperty({"费用", "债转"})
    private BigDecimal debtSwapPriceCount;
    @ExcelProperty({"费用", "总费用"})
    private BigDecimal totalPriceCount;

    //验证码计费数
    @ExcelProperty({"计费量(条)", "验证码"})
    private Integer verifyReachBillCount;
    //通知计费数
    @ExcelProperty({"计费量(条)", "通知"})
    private Integer notifyReachBillCount;
    //营销计费数
    @ExcelProperty({"计费量(条)", "营销"})
    private Integer marketReachBillCount;
    //行业通知计费数
    @ExcelProperty({"计费量(条)", "催收"})
    private Integer collectorReachBillCount;
    //轻催计费数
    @ExcelProperty({"计费量(条)", "轻催"})
    private Integer lightCollectorReachBillCount;
    //重催计费数
    @ExcelProperty({"计费量(条)", "重催"})
    private Integer severeCollectorReachBillCount;
    //债转计费数
    @ExcelProperty({"计费量(条)", "债转"})
    private Integer debtSwapReachBillCount;
    //合计计费数
    @ExcelProperty({"计费量(条)", "总计费量"})
    private Integer totalReachBillCount;

    public static MonoPriceStatVO convert(ChannelPriceStatDO channelPriceStatDO,
                                          String channelCodeName,
                                          List<String> selectedTypes) {
        MonoPriceStatVO vo = MonoPriceStatVO.builder()
                .statDate(channelPriceStatDO.getStatDate())
                .channelCode(channelPriceStatDO.getChannelCode())
                .channelCodeName(channelCodeName).build();

                BigDecimal totalPriceCountSum = BigDecimal.ZERO;
                double totalReachRateSum = 0.0;
                int reachRateCount = 0;
                int totalReachBillCountSum = 0;

                if (selectedTypes == null || selectedTypes.contains("verify")) {
                    vo.setVerifyReachRate(CommonUtil.double2String(channelPriceStatDO.getVerifyReachRate()));
                    vo.setVerifyPriceCount(convertFenToYuan(channelPriceStatDO.getVerifyPriceCount()));
                    vo.setVerifyReachBillCount(channelPriceStatDO.getVerifyReachBillCount());

                    if (channelPriceStatDO.getVerifyPriceCount() != null) {
                        totalPriceCountSum = totalPriceCountSum.add(convertFenToYuan(channelPriceStatDO.getVerifyPriceCount()));
                    }
                    if (channelPriceStatDO.getVerifyReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getVerifyReachRate();
                        reachRateCount++;
                    }
                    if (channelPriceStatDO.getVerifyReachBillCount() != null) {
                        totalReachBillCountSum += channelPriceStatDO.getVerifyReachBillCount();
                    }
                }

                if (selectedTypes == null || selectedTypes.contains("notify")) {
                    vo.setNotifyReachRate(CommonUtil.double2String(channelPriceStatDO.getNotifyReachRate()));
                    vo.setNotifyPriceCount(convertFenToYuan(channelPriceStatDO.getNotifyPriceCount()));
                    vo.setNotifyReachBillCount(channelPriceStatDO.getNotifyReachBillCount());

                    if (channelPriceStatDO.getNotifyPriceCount() != null) {
                        totalPriceCountSum = totalPriceCountSum.add(convertFenToYuan(channelPriceStatDO.getNotifyPriceCount()));
                    }
                    if (channelPriceStatDO.getNotifyReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getNotifyReachRate();
                        reachRateCount++;
                    }
                    if (channelPriceStatDO.getNotifyReachBillCount() != null) {
                        totalReachBillCountSum += channelPriceStatDO.getNotifyReachBillCount();
                    }
                }

                if (selectedTypes == null || selectedTypes.contains("market")) {
                    vo.setMarketReachRate(CommonUtil.double2String(channelPriceStatDO.getMarketReachRate()));
                    vo.setMarketPriceCount(convertFenToYuan(channelPriceStatDO.getMarketPriceCount()));
                    vo.setMarketReachBillCount(channelPriceStatDO.getMarketReachBillCount());

                    if (channelPriceStatDO.getMarketPriceCount() != null) {
                        totalPriceCountSum = totalPriceCountSum.add(convertFenToYuan(channelPriceStatDO.getMarketPriceCount()));
                    }
                    if (channelPriceStatDO.getMarketReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getMarketReachRate();
                        reachRateCount++;
                    }
                    if (channelPriceStatDO.getMarketReachBillCount() != null) {
                        totalReachBillCountSum += channelPriceStatDO.getMarketReachBillCount();
                    }
                }

                if (selectedTypes == null || selectedTypes.contains("collector")) {
                    vo.setCollectorReachRate(CommonUtil.double2String(channelPriceStatDO.getCollectorReachRate()));
                    vo.setCollectorPriceCount(convertFenToYuan(channelPriceStatDO.getCollectorPriceCount()));
                    vo.setCollectorReachBillCount(channelPriceStatDO.getCollectorReachBillCount());

                    if (channelPriceStatDO.getCollectorPriceCount() != null) {
                        totalPriceCountSum = totalPriceCountSum.add(convertFenToYuan(channelPriceStatDO.getCollectorPriceCount()));
                    }
                    if (channelPriceStatDO.getCollectorReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getCollectorReachRate();
                        reachRateCount++;
                    }
                    if (channelPriceStatDO.getCollectorReachBillCount() != null) {
                        totalReachBillCountSum += channelPriceStatDO.getCollectorReachBillCount();
                    }
                }

                if (selectedTypes == null || selectedTypes.contains("light_collector")) {
                    vo.setLightCollectorReachRate(CommonUtil.double2String(channelPriceStatDO.getLightCollectorReachRate()));
                    vo.setLightCollectorPriceCount(convertFenToYuan(channelPriceStatDO.getLightCollectorPriceCount()));
                    vo.setLightCollectorReachBillCount(channelPriceStatDO.getLightCollectorReachBillCount());

                    if (channelPriceStatDO.getLightCollectorPriceCount() != null) {
                        totalPriceCountSum = totalPriceCountSum.add(convertFenToYuan(channelPriceStatDO.getLightCollectorPriceCount()));
                    }
                    if (channelPriceStatDO.getLightCollectorReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getLightCollectorReachRate();
                        reachRateCount++;
                    }
                    if (channelPriceStatDO.getLightCollectorReachBillCount() != null) {
                        totalReachBillCountSum += channelPriceStatDO.getLightCollectorReachBillCount();
                    }
                }

                if (selectedTypes == null || selectedTypes.contains("severe_collector")) {
                    vo.setSevereCollectorReachRate(CommonUtil.double2String(channelPriceStatDO.getSevereCollectorReachRate()));
                    vo.setSevereCollectorPriceCount(convertFenToYuan(channelPriceStatDO.getSevereCollectorPriceCount()));
                    vo.setSevereCollectorReachBillCount(channelPriceStatDO.getSevereCollectorReachBillCount());

                    if (channelPriceStatDO.getSevereCollectorPriceCount() != null) {
                        totalPriceCountSum = totalPriceCountSum.add(convertFenToYuan(channelPriceStatDO.getSevereCollectorPriceCount()));
                    }

                    if (channelPriceStatDO.getSevereCollectorReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getSevereCollectorReachRate();
                        reachRateCount++;
                    }

                    if (channelPriceStatDO.getSevereCollectorReachBillCount() != null) {
                        totalReachBillCountSum += channelPriceStatDO.getSevereCollectorReachBillCount();
                    }
                }

                if (selectedTypes == null || selectedTypes.contains("debt_swap")) {
                    vo.setDebtSwapReachRate(CommonUtil.double2String(channelPriceStatDO.getDebtSwapReachRate()));
                    vo.setDebtSwapPriceCount(convertFenToYuan(channelPriceStatDO.getDebtSwapPriceCount()));
                    vo.setDebtSwapReachBillCount(channelPriceStatDO.getDebtSwapReachBillCount());

                    if (channelPriceStatDO.getDebtSwapPriceCount() != null) {
                        totalPriceCountSum = totalPriceCountSum.add(convertFenToYuan(channelPriceStatDO.getDebtSwapPriceCount()));
                    }
                    if (channelPriceStatDO.getDebtSwapReachRate() != null) {
                        totalReachRateSum += channelPriceStatDO.getDebtSwapReachRate();
                        reachRateCount++;
                    }
                    if (channelPriceStatDO.getDebtSwapReachBillCount() != null) {
                        vo.setDebtSwapReachRate(CommonUtil.double2String(channelPriceStatDO.getDebtSwapReachRate()));
                    }
                }

                vo.setTotalPriceCount(totalPriceCountSum);
                vo.setTotalReachBillCount(totalReachBillCountSum);
                if (reachRateCount > 0) {
                    vo.setAvgReachRate(CommonUtil.double2String(totalReachRateSum / reachRateCount));
                }

                return vo;
    }

    private static BigDecimal convertFenToYuan(Integer fen) {
        if (fen == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(fen).divide(new BigDecimal(1000), 3, RoundingMode.HALF_UP);
    }
}
