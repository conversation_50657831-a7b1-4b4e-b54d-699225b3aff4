package com.xhqb.spectre.admin.bidata.mapper;


import com.xhqb.spectre.admin.bidata.entity.*;
import com.xhqb.spectre.admin.bidata.model.ReachRateDTO;
import com.xhqb.spectre.admin.bidata.model.SendCountTopDO;
import com.xhqb.spectre.admin.bidata.model.SendFormDO;
import com.xhqb.spectre.admin.bidata.model.SendIspCodeDataDO;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.MultipleCompareQuery;
import com.xhqb.spectre.common.dal.query.ReachRateQuery;
import com.xhqb.spectre.common.dal.query.SendQuery;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * bidata t_test_send_stat 处理
 *
 * <AUTHOR>
 * @date 2024/10/31
 */
public interface BidataSendStatMapper {


    /**
     * 根据发送前十查询条件查询数据，并返回一个以code为键，Long类型为值的Map集合
     *
     * @param query 查询条件对象，包含查询所需的参数
     * @return 返回一个以code为键，Long类型为值的Map集合，其中Map的键是查询结果中的code字段，值是对应code的数量或ID
     */
    List<SendFormDO> selectBySendTopTenQuery(@Param("query") SendQuery query);

    /**
     * 根据ReachRateQuery查询条件查询到达率数据
     *
     * @param query ReachRateQuery查询条件对象
     * @return 返回ReachRateDTO类型的列表，包含查询到的到达率数据
     */
    List<ReachRateDTO> selectByReachRateQuery(@Param("query") ReachRateQuery query);

    List<SendStatDO> selectByTime(@Param("query") SendQuery query);


    List<SendFormDO> selectSendTopTenByTplCode(@Param("query") SendQuery query);

    List<SendStatDO> selectSendDetailByQuery(@Param("query") SendQuery query);

    int countSendDetailByQuery(@Param("query") SendQuery query);

    List<SendStatDO> selectByMultipleCompareQuery(@Param("query") MultipleCompareQuery query);


    /**
     * 根据供应商按月统计到达率
     *
     * @param startDate
     * @param endDate
     * @param channelCode
     * @return
     */
    List<ChannelReachStatDO> getMonthStatsByChannelCode(@Param("startDate") String startDate,
                                                        @Param("endDate") String endDate,
                                                        @Param("channelCode") String channelCode,
                                                        @Param("signList") List<String> signList);

    List<ChannelReachStatDO> getWeekStatsByChannelCode(@Param("startDate") String startDate,
                                                       @Param("endDate") String endDate,
                                                       @Param("channelCode") String channelCode,
                                                       @Param("signList") List<String> signList);

    List<ChannelReachStatDO> getMonthStatsByDate(@Param("startDate") String startDate,
                                                 @Param("endDate") String endDate,
                                                 @Param("signList") List<String> signList);

    List<ChannelReachStatDO> getWeekStatsByDate(@Param("startDate") String startDate,
                                                @Param("endDate") String endDate,
                                                @Param("signList") List<String> signList);

    List<TypeReachStatDO> getMonthStatsByType(@Param("startDate") String startDate,
                                              @Param("endDate") String endDate,
                                              @Param("smsTypeCode") String smsTypeCode,
                                              @Param("signList") List<String> signList);

    List<TypeReachStatDO> getWeekStatsByType(@Param("startDate") String startDate,
                                             @Param("endDate") String endDate,
                                             @Param("smsTypeCode") String smsTypeCode,
                                             @Param("signList") List<String> signList);


    /**
     * 根据供应商,短信类型,月份统计到达率
     *
     * @return
     */
    List<SubReachStatDO> getMonthSubReachStats(@Param("startDate") String startDate,
                                               @Param("endDate") String endDate,
                                               @Param("channelCode") String channelCode,
                                               @Param("channelCodes") List<String> channelCodes,
                                               @Param("smsTypeCode") String smsTypeCode,
                                               @Param("smsTypeCodes") List<String> smsTypeCodes,
                                               @Param("signList") List<String> signList,
                                               @Param("tplCode") String tplCode,
                                               @Param("pageParameter") PageParameter pageParameter);

    List<SubReachStatDO> getWeekSubReachStats(@Param("startDate") String startDate,
                                              @Param("endDate") String endDate,
                                              @Param("channelCode") String channelCode,
                                              @Param("channelCodes") List<String> channelCodes,
                                              @Param("smsTypeCode") String smsTypeCode,
                                              @Param("smsTypeCodes") List<String> smsTypeCodes,
                                              @Param("signList") List<String> signList,
                                              @Param("tplCode") String tplCode,
                                              @Param("pageParameter") PageParameter pageParameter);

    List<StatChannelDO> getStatChannelList(@Param("startDate") String startDate);

    List<StatSignDO> getStatSignList(@Param("startDate") String startDate);

    List<StatSmsTypeDO> getStatSmsTypeList(@Param("startDate") String startDate);

    List<TplReachRateDO> getAbnormalTplReachRates(@Param("pageParameter") PageParameter pageParameter);

    List<TplChannelRateDO> getAbnormalTplChannelReachRate(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("rateGap") double rateGap,
            @Param("minSendCount") int minSendCount
    );

    List<TopReachStatDO> queryTplChannelStats(@Param("startDate") String startDate,
                                              @Param("endDate") String endDate,
                                              @Param("smsTypeCode") String smsTypeCode);


    List<ReachRateDO> selectReachRateByTimeAndType(@Param("startDate") String startDate,
                                                   @Param("smsTypeCodes") List<String> smsType);

    List<TplHeatStatDO> queryOverallByTplHeat(@Param("query") SendQuery query);

    List<TplHeatStatDO> queryStatByTplHeat(@Param("query") SendQuery query);

    int queryStatByTplHeatCount(@Param("query") SendQuery query);

    List<String> selectTplCodeByStatDateAndCount(@Param("statDate") String statDate, @Param("count") int count);

    List<ChannelPriceStatDO> getMonthPriceStatsByChannelCode(@Param("startDate") String startDate,
                                                             @Param("endDate") String endDate,
                                                             @Param("channelCode") String channelCode,
                                                             @Param("signList") List<String> signList);

    List<ChannelPriceStatDO> getWeekPriceStatsByChannelCode(@Param("startDate") String startDate,
                                                            @Param("endDate") String endDate,
                                                            @Param("channelCode") String channelCode,
                                                            @Param("signList") List<String> signList);

    List<ChannelPriceStatDO> getMonthPriceStats(@Param("startDate") String startDate,
                                                @Param("endDate") String endDate,
                                                @Param("channelCodes") List<String> channelCodes,
                                                @Param("signList") List<String> signList);

    List<ChannelPriceStatDO> getWeekPriceStats(@Param("startDate") String startDate,
                                               @Param("endDate") String endDate,
                                               @Param("channelCodes") List<String> channelCodes,
                                               @Param("signList") List<String> signList);


    List<ChannelMonthlyStatementDO> getMonthlyStatementStats(@Param("startDate") String startDate,
                                                             @Param("endDate") String endDate,
                                                             @Param("channelCode") String channelCode,
                                                             @Param("signList") List<String> signList);

    List<ChannelMonthlyStatementDO> getMonthlyStatementRecords(@Param("startDate") String startDate,
                                                               @Param("endDate") String endDate,
                                                               @Param("channelCode") String channelCode,
                                                               @Param("signList") List<String> signList);

    List<TopReachStatDO> queryTopTplChannelStats(@Param("startDate") String startDate,
                                                 @Param("endDate") String endDate,
                                                 @Param("smsTypeCodes") List<String> smsTypeCodes);

    int updatePriceByChannelAndTypeAndDate(
            @Param("channelCode") String channelCode,
            @Param("smsTypeCode") String smsTypeCode,
            @Param("pricePerSms") int pricePerSms,
            @Param("statDate") String statDate
    );

    int updatePriceByChannelAndType(
            @Param("channelCode") String channelCode,
            @Param("smsTypeCode") String smsTypeCode,
            @Param("pricePerSms") int pricePerSms
    );

    List<SendIspCodeDataDO> selectDataByTimeAndIspCode(@Param("startDate") Date startDate,
                                                       @Param("endDate") Date endDate);

    List<String> selectTplCodeByDate(@Param("t1Date") String t1Date, @Param("topN") int topN);

    List<SendCountTopDO> selectSendCountTopByTplCodes(@Param("tplCodes") List<String> tplCodeList, @Param("t3Date") String t3Date);

    /**
     * 根据创建者的模板编码获取TOP发送量数据
     *
     * @param tplCodeList 模板编码列表
     * @param t1Date      T-1日期
     * @param topN        TOP数量
     * @return TOP发送量模板编码列表
     */
    List<String> selectTopTplCodesByCreatorTplCodes(@Param("tplCodeList") List<String> tplCodeList, @Param("t1Date") String t1Date, @Param("topN") int topN);

    /**
     * 更新 stat_date >= 指定日期 的价格
     */
    int updatePriceByChannelAndTypeAndDateFrom(
            @Param("statDate") String statDate,
            @Param("channelCode") String channelCode,
            @Param("smsTypeCode") String smsTypeCode,
            @Param("pricePerSms") int pricePerSms,
            @Param("signName") String signName
    );

    /**
     * 更新 stat_date < 指定日期 的价格
     */
    int updatePriceByChannelAndTypeAndDateBefore(
            @Param("statDate") String statDate,
            @Param("channelCode") String channelCode,
            @Param("smsTypeCode") String smsTypeCode,
            @Param("pricePerSms") int pricePerSms,
            @Param("signName") String signName
    );


    List<ChannelPriceStatDO> getMonthPriceStatsByDate(@Param("startDate") String startDate,
                                                      @Param("endDate") String endDate,
                                                      @Param("smsTypeCodes") List<String> smsTypeCodes,
                                                      @Param("signList") List<String> signList);

    List<ChannelPriceStatDO> getWeekPriceStatsByDate(@Param("startDate") String startDate,
                                                     @Param("endDate") String endDate,
                                                     @Param("smsTypeCodes") List<String> smsTypeCodes,
                                                     @Param("signList") List<String> signList);

    List<TypePriceStatDO> getMonthPriceStatsByType(@Param("startDate") String startDate,
                                              @Param("endDate") String endDate,
                                              @Param("channelCode") String channelCode,
                                              @Param("smsTypeCode") String smsTypeCode,
                                              @Param("signList") List<String> signList);

    List<TypePriceStatDO> getWeekPriceStatsByType(@Param("startDate") String startDate,
                                             @Param("endDate") String endDate,
                                             @Param("channelCode") String channelCode,
                                             @Param("smsTypeCode") String smsTypeCode,
                                             @Param("signList") List<String> signList);
}
