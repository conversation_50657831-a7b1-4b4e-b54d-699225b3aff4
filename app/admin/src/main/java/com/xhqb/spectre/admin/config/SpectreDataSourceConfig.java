package com.xhqb.spectre.admin.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.xhqb.kael.boot.autoconfigure.druid.DatasourceConfigUtils;
import com.xhqb.kael.boot.autoconfigure.druid.DefaultConnectionProperties;
import com.xhqb.kael.boot.autoconfigure.druid.DruidConnectionProperties;
import com.xhqb.spectre.admin.config.properties.SpectreDruidProperties;
import com.xhqb.spectre.admin.util.CommonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/27 13:45
 * @Description:
 */
@Configuration
@MapperScan(basePackages = "com.xhqb.spectre.common.dal.mapper", sqlSessionTemplateRef = "spectreSqlSessionTemplate")
@EnableConfigurationProperties(SpectreDruidProperties.class)
public class SpectreDataSourceConfig {

    /**
     * spectre数据源
     *
     * @param spectreDruidProperties
     * @return
     */
    @Bean(name = "spectreDataSource")
    public DataSource spectreDataSource(SpectreDruidProperties spectreDruidProperties) {
        DruidConnectionProperties defaultProperties = DruidConnectionProperties.withDefault(new DefaultConnectionProperties());
        BeanUtils.copyProperties(spectreDruidProperties, defaultProperties, CommonUtil.getNullPropertyNames(spectreDruidProperties));
        DruidDataSource dataSource = DatasourceConfigUtils.createDataSource(defaultProperties);
        dataSource.setName("spectre");
        return dataSource;
    }

    /**
     * session factory
     *
     * @param dataSource
     * @return
     * @throws Exception
     */
    @Bean(name = "spectreSqlSessionFactory")
    @Primary
    public SqlSessionFactory spectreSqlSessionFactory(@Qualifier("spectreDataSource") DataSource dataSource, ObjectProvider<List<Interceptor>> interceptorObjectProvider) throws Exception {
        final SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        //设置加解密插件
        List<Interceptor> interceptorList = interceptorObjectProvider.getIfAvailable();
        if (CollectionUtils.isNotEmpty(interceptorList)) {
            bean.setPlugins(interceptorList.toArray(new Interceptor[0]));
        }
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*Mapper.xml"));
        return bean.getObject();
    }

    /**
     * transaction manager
     *
     * @param dataSource
     * @return
     */
    @Bean(name = "spectreTransactionManager")
    @Primary
    public DataSourceTransactionManager spectreTransactionManager(@Qualifier("spectreDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * build sql session template
     *
     * @param sqlSessionFactory
     * @return
     */
    @Bean(name = "spectreSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate spectreSqlSessionTemplate(@Qualifier("spectreSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
