package com.xhqb.spectre.admin.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.service.UrlLinkService;
import com.xhqb.spectre.common.dal.entity.WxUrlLinkDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


/**
 * 小程序短链信息刷新任务
 */
@Component
@Job("wxUrlLinkJob")
@Slf4j
public class WxUrlLinkJob implements SimpleJob {

    @Resource
    private UrlLinkService urlLinkService;

    @Override
    public void execute(ShardingContext shardingContext) {
        long start = System.currentTimeMillis();
        log.info("开始刷新小程序短链信息;");
        try {
            List<WxUrlLinkDO> wxUrlLinkDOList = urlLinkService.getExpiredUrlLinks();
            if (wxUrlLinkDOList == null || wxUrlLinkDOList.isEmpty()) {
                log.info("没有需要刷新的短链信息");
                return;
            }
            wxUrlLinkDOList.forEach(urlLinkService::refresh);
        } catch (Exception e) {
            log.error("刷新小程序短链信息失败", e);
        }
        log.info("刷新小程序短链信息结束,耗时 = {}", (System.currentTimeMillis() - start));
    }
}
