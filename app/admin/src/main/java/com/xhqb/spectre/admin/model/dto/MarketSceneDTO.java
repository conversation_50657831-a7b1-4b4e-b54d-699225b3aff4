package com.xhqb.spectre.admin.model.dto;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 营销场景
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
@Data
public class MarketSceneDTO implements Serializable {

    /**
     * 名称
     */
    @NotEmpty(message = "营销场景名称不能够为空")
    private String name;

    /**
     * 状态，0：无效，1：有效
     */
    private Integer status;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
