package com.xhqb.spectre.admin.mq.event.listener;

import com.xhqb.spectre.admin.batchtask.limit.BatchSubmitRateLimitService;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.mq.consumer.handler.BatchTaskParamConsumerHandler;
import com.xhqb.spectre.admin.mq.event.BatchTaskParamEvent;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.BatchTaskDO;
import com.xhqb.spectre.common.dal.mapper.BatchTaskMapper;
import com.xhqb.spectre.common.enums.BatchTaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 发送消息完成之后的事件通知监听器
 * 1. 消息发送完成之后 任务数量需要进行自减操作
 * 2. 若发送短信成功数量大于0时，则需要更新t_batch_task发送成功数量
 * 3. 若任务剩余量小于等于0时 那么则需要更新t_batch_task任务状态以及发送完成时间
 *
 * <AUTHOR>
 * @date 2021/9/26
 */
@Component
@Slf4j
public class BatchTaskParamEventListener {

    @Resource
    private BatchTaskMapper batchTaskMapper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private BatchTaskParamConsumerHandler batchTaskParamConsumerHandler;
    @Resource
    private BatchSubmitRateLimitService batchSubmitRateLimitService;

    /**
     * 该方法会接收到短信消息发送完成的消息
     *
     * @param event
     */
    public void publish(BatchTaskParamEvent event) {
        long start = System.currentTimeMillis();
        log.info("收到短信发送完成通知, batchTaskParamEvent = {}", event);
        boolean isTaskFinish = this.countDown(event);
        BatchTaskDO batchTaskDO = new BatchTaskDO();
        batchTaskDO.setId(event.getTaskId());

        // 设置发送成功的数量
        Integer sendTotalCount = event.getSendTotalCount();
        if (Objects.nonNull(sendTotalCount) && sendTotalCount > 0) {
            batchTaskDO.setSentCount(sendTotalCount);
        }

        // 实际发送成功的数量
        Integer realSendCount = event.getRealSendCount();
        if (Objects.nonNull(realSendCount) && realSendCount > 0) {
            batchTaskDO.setRealSendCount(realSendCount);
        }

        if (isTaskFinish) {
            // 发送完成 需要更新发送时间
            batchTaskDO.setSendEndTime(DateUtil.getNow());
            batchTaskDO.setStatus(BatchTaskStatusEnum.SENT.getCode());
            // 删除待删除标记
            batchTaskParamConsumerHandler.removeTaskCanceled(batchTaskDO.getId());
            // 移除限流任务
            batchSubmitRateLimitService.leave(batchTaskDO.getId());
        }

        // 如果发送成功数量 或者 发送结束时间不为空 则需要进行数据更新
        // 否则 不进行更新
        boolean isNeedUpdate = Objects.nonNull(batchTaskDO.getSentCount()) || Objects.nonNull(batchTaskDO.getSendEndTime());
        if (!isNeedUpdate) {
            log.info("群发短信任务发送成功短信的数量为0,不进行任务数据更新操作,batchTaskParamEvent = {}", event);
            return;
        }

        this.updateBatchTask(event, batchTaskDO);

        log.info("处理短信发送完成通知耗时 = {}, batchTaskParamEvent = {}", (System.currentTimeMillis() - start), event);
    }

    /**
     * 数据自减,每次任务数量都减1
     *
     * @param event
     * @return 返回true 表示任务已做完成
     */
    private boolean countDown(BatchTaskParamEvent event) {
        Integer sendTotal = event.getSendTotalCount();
        if (Objects.nonNull(sendTotal)) {
            // 不为空 表示当前消费属于正常消息(不属于重复消费)
            // 则需要进行总数递减
            return this.subTaskCountFromCache(event.getTaskId(), event.getTaskParamId());
        }

        // 重复消费消息不做任何处理
        return false;
    }

    /**
     * 做群发任务更新操作
     *
     * @param event
     * @param batchTaskDO
     */
    private void updateBatchTask(BatchTaskParamEvent event, BatchTaskDO batchTaskDO) {
        long start = System.currentTimeMillis();
        int times = 0;
        while (!this.doUpdateBatchTask(event, batchTaskDO)) {
            times++;
        }
        log.info("更新群发任务发送成功数量,耗时 = {}, 执行次数 = {}, batchTaskParamEvent = {}", (System.currentTimeMillis() - start), times, event);
    }

    /**
     * 更新群发任务
     *
     * @param event
     * @param batchTaskDO
     * @return 返回true表示更新成功
     */
    private boolean doUpdateBatchTask(BatchTaskParamEvent event, BatchTaskDO batchTaskDO) {
        Integer version = getTaskVersion(event);
        batchTaskDO.setVersion(version);
        int affectedRows = batchTaskMapper.updateSentCountByIdWithVersion(batchTaskDO);
        return affectedRows > 0;
    }

    /**
     * 获取到群发任务版本号
     *
     * @param event
     * @return
     */
    private Integer getTaskVersion(BatchTaskParamEvent event) {
        BatchTaskDO dbResult = batchTaskMapper.selectByPrimaryKey(event.getTaskId());
        if (Objects.isNull(dbResult)) {
            log.error("未查询到群发任务,batchTaskParamEvent = {}", event);
            throw new BizException("未查询到群发任务");
        }
        return dbResult.getVersion();
    }


    /**
     * 将要做的任务数写入缓存
     *
     * @param taskId
     * @param taskCount
     */
    public void writeTaskCountToCache(int taskId, int taskCount) {
        // 缓存过期时间为3天
        stringRedisTemplate.opsForValue().set(RedisKeys.BatchTaskKeys.BATCH_TASK_COUNT_STR_KEY + ":" + taskId, String.valueOf(taskCount), 3, TimeUnit.DAYS);
        log.info("群发短信任务写入缓存的数量 = {}, taskId ={}", taskCount, taskId);
    }

    /**
     * 从缓存中减去要做的任务数量
     * 每次都减1
     *
     * @param taskId
     * @param taskParamId
     * @return 返回true 则表示任务已做完, 否则任务还未做完
     */
    public boolean subTaskCountFromCache(int taskId, int taskParamId) {
        Long count = stringRedisTemplate.opsForValue().decrement(RedisKeys.BatchTaskKeys.BATCH_TASK_COUNT_STR_KEY + ":" + taskId);
        log.info("当前任务剩余数量 = {},taskId = {},taskParamId = {}", count, taskId, taskParamId);
        return Objects.isNull(count) || count <= 0;
    }

}
