package com.xhqb.spectre.admin.omini.dingding;

import com.xhqb.msgcenter.common.core.mqItems.ReceiverInfo;
import com.xhqb.msgcenter.model.iteam.MsgSendEntry;
import com.xhqb.spectre.admin.omini.OminiMessageContext;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Map;

/**
 * 钉钉机消息
 *
 * <AUTHOR>
 * @date 2021/12/30
 */
public abstract class DingDingMessageContext extends OminiMessageContext {
    /**
     * 接受人的钉钉号或者机器配置地址
     */
    private String dingDingAccount;

    public DingDingMessageContext(String strategyId, String dingDingAccount) {
        super(strategyId);
        if (StringUtils.isBlank(dingDingAccount)) {
            throw new IllegalArgumentException("钉钉账号不合法");
        }
        this.dingDingAccount = dingDingAccount;
    }


    public String getDingDingAccount() {
        return dingDingAccount;
    }

    /**
     * 填充消息实体
     *
     * @param msgSendEntry
     */
    @Override
    protected void doFillEntry(MsgSendEntry msgSendEntry) {
        ArrayList<ReceiverInfo> receivers = new ArrayList<>();
        // 指定接受人的信息
        ReceiverInfo receiverInfo = new ReceiverInfo();
        receiverInfo.setDingdingAccount(this.getDingDingAccount());
        receivers.add(receiverInfo);
        msgSendEntry.setReceiver(receivers);

        // 构建消息参数
        Map<String, Object> parameters = this.parameters();
        msgSendEntry.setDingVariableJson(parameters);
    }

    /**
     * 钉钉请求参数
     *
     * @return
     */
    protected abstract Map<String, Object> parameters();

}
