package com.xhqb.spectre.admin.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.model.vo.AreaVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/17 16:46
 * @Description:
 */
@Slf4j
public class CommonUtil {

    private static final Pattern PHONE_PATTERN = Pattern.compile("^1\\d{10}$");

    private static final Pattern TPL_PARAM_PATTERN = Pattern.compile("\\[\\*]");
    private static final Pattern TPL_PARAM_PATTERN2 = Pattern.compile("\\$\\{[\\w|\u4e00-\u9fa5|-]+}");

    private static final char[] ALL_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890".toCharArray();

    /**
     * 生成app key
     *
     * @return
     */
    public static String getAppKey() {
        try {
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            String sha1 = Sha1Util.sha1(uuid, "");
            if (sha1.length() > 20) {
                sha1 = sha1.substring(0, 20);
            }
            return sha1;
        } catch (Exception e) {
            log.error("getAppKey exception", e);
            return "";
        }
    }

    /**
     * 判断手机号是否合法
     *
     * @param phone
     * @return
     */
    public static boolean isLegalPhone(String phone) {
        return PHONE_PATTERN.matcher(phone).matches();
    }

    /**
     * 获取短信模板参数个数
     *
     * @param tplContent
     * @return
     */
    public static Integer getTplParamCount(String tplContent) {
        Integer count = 0;
        //匹配[*]格式
        Matcher matcher = TPL_PARAM_PATTERN.matcher(tplContent);
        while (matcher.find()) {
            count++;
        }
        //匹配${XX}格式
        matcher = TPL_PARAM_PATTERN2.matcher(tplContent);
        while (matcher.find()) {
            count++;
        }
        return count;
    }

    /**
     * 判断模板内容是否是占位符模式
     *
     * @param tplContent
     * @return
     */
    public static boolean isTplPlaceholderModel(String tplContent) {
        if (StringUtils.isBlank(tplContent)) {
            return false;
        }
        return TPL_PARAM_PATTERN2.matcher(tplContent).find();
    }

    /**
     * 获取容器的大小
     * 如果容器为null 则返回null
     *
     * @param collection
     * @param <T>
     * @return
     */
    public static <T> Integer getCollectionSize(Collection<T> collection) {
        if (Objects.nonNull(collection)) {
            return collection.size();
        }
        return 0;
    }

    /**
     * 手机号掩码
     *
     * @param mobile
     * @return
     */
    public static String maskMobile(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return "";
        }
        return mobile.replaceAll("(\\d{3})\\d+(\\d{4})", "$1****$2");
    }

    /**
     * 手机号加密
     *
     * @param mobile
     * @return
     */
    public static String encryptMobile(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return "";
        }
        return AesUtil.encryptFromString(mobile, Mode.CBC, Padding.ZeroPadding);
    }

    /**
     * 运营商转为List结构
     *
     * @param isps
     * @return
     */
    public static List<String> ispToList(String isps) {
        return StringUtils.isEmpty(isps) ? Collections.emptyList() : Arrays.asList(isps.split(","));
    }

    /**
     * 地域信息转为List结构
     *
     * @param areas
     * @return
     */
    public static List<AreaVO> areaToList(String areas) {
        return StringUtils.isEmpty(areas) ? Collections.emptyList() : JSON.parseArray(areas, AreaVO.class);
    }

    /**
     * 判断容器类是否为空
     *
     * @param collection
     * @param <T>
     * @return
     */
    public static <T> boolean isEmpty(Collection<T> collection) {
        return Objects.isNull(collection) || collection.isEmpty();
    }

    /**
     * 会将null值转换成0
     *
     * @param num
     * @return
     */
    public static int toInt(Integer num) {
        return Objects.isNull(num) ? 0 : num;
    }

    /**
     * 生成62进制的随机字符串
     *
     * @param length
     * @return
     */
    public static String getRandStr(int length) {
        StringBuffer sb = new StringBuffer();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            sb.append(ALL_CHARS[random.nextInt(62)]);
        }
        return sb.toString();
    }

    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    /**
     * 截取字符串
     *
     * @param str
     * @param maxLength
     * @return
     */
    public static String substr(String str, int maxLength) {
        if (StringUtils.isBlank(str)) {
            return str;
        }

        if (str.length() <= maxLength) {
            return str;
        }

        return str.substring(0, maxLength);
    }

    public static <T> List<T> intersection(List<T> list1, List<T> list2) {
        List<T> intersection = new ArrayList<>(list1);
        intersection.retainAll(list2);
        return intersection;
    }

    public static <T> List<T> exclude(List<T> list1, List<T> list2) {
        List<T> excludedList = new ArrayList<>(list1);
        excludedList.removeAll(list2);
        return excludedList;
    }


    /**
     * 根据传入的类型和参数，获取对应类型的分组信息
     *
     * @param type   类型标识，用于指定要获取的分组信息类型
     * @param params 包含多个类型标识的字符串，用逗号分隔
     * @return 转换后的类型标识，如果不包含传入的类型，则默认为"appCode"，并转换为下划线形式
     */
    public static String getGroupByInfoByType(String type, String params) {

        Set<String> sendCountTypeSet = Arrays.stream(params.split(","))
                .collect(Collectors.toSet());

        if (!sendCountTypeSet.contains(type)) {
            type = "appCode";
        }

        return CharSequenceUtil.toUnderlineCase(type);
    }

    /**
     * 根据时间类型获取时间范围
     *
     * @param timeType 时间类型，可选值为："3"（上月）、"2"（最近7天）、""（默认昨天）
     * @return 时间范围列表，包含开始时间和结束时间
     */
    public static List<Date> timeByTimeType(String timeType) {

        List<Date> resultList = new ArrayList<>();

        Date curDate = new Date();

        if (Objects.equals("3", timeType)) {
            // 上月
            Date startDay = DateUtil.beginOfMonth(DateUtil.offsetMonth(curDate, -1));
            Date endDay = DateUtil.endOfMonth(DateUtil.offsetMonth(curDate, -1));
            resultList.add(DateUtil.beginOfDay(startDay));
            resultList.add(DateUtil.endOfDay(endDay));
            return resultList;
        }


        if (Objects.equals("2", timeType)) {
            // 7天
            Date startDay = DateUtils.addDays(curDate, -7);
            Date endDay = DateUtils.addDays(curDate, -2);
            resultList.add(DateUtil.beginOfDay(startDay));
            resultList.add(DateUtil.endOfDay(endDay));
            return resultList;
        }

        // 昨天
        Date yesterday = DateUtils.addDays(curDate, -1);
        resultList.add(DateUtil.beginOfDay(yesterday));
        resultList.add(DateUtil.endOfDay(yesterday));
        return resultList;

    }

    /**
     * 除法计算
     *
     * @param num1 被除数
     * @param num2 除数
     * @return double 商
     */
    public static double division(Long num1, Long num2) {
        if (ObjectUtils.isEmpty(num1) || ObjectUtils.isEmpty(num2) || num2 == 0) {
            return 0;
        }
        return (double) num1 / num2 * 100;
    }

    /**
     * 除法计算
     *
     * @param num1 被除数
     * @param num2 除数
     * @return double 商
     */
    public static double realDivision(Long num1, Long num2) {
        if (ObjectUtils.isEmpty(num1) || ObjectUtils.isEmpty(num2) || num2 == 0) {
            return 0;
        }
        return (double) num1 / num2;
    }

    /**
     * 驼峰转换
     */
    public static String camelToSnake(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        StringBuilder result = new StringBuilder();
        char[] chars = str.toCharArray();

        for (int i = 0; i < chars.length; i++) {
            char c = chars[i];
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    result.append('_');
                }
                result.append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }

        return result.toString();
    }

    public static String double2String(Double value) {
        if (value == null) {
            return "";
        }
        return String.format("%d%%", Math.round(value * 100));
    }

    public static String escapeJsonAndRemoveNewlines(String content) {
        try {
            if (org.springframework.util.StringUtils.isEmpty(content)) {
                return "";
            }
            String tempContent = content.replace("\n", "").replace("\r", "");
            return StringEscapeUtils.escapeJson(tempContent);
        } catch (Exception e) {
            log.warn("escapeJsonAndRemoveNewlines failed, content: {}", content, e);
        }
        return "";
    }
}
