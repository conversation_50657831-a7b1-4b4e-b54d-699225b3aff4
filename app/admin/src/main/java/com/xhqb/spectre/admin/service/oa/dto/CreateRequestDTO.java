package com.xhqb.spectre.admin.service.oa.dto;


import lombok.Data;

import java.util.Map;

@Data
public class CreateRequestDTO {

    /**
     * 标题
     */
    private String title;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 使用场景
     */
    private String sceneCode;

    /**
     * 短信内容
     */
    private String content;


    /**
     * 短信内容map key  contentId  value 短信内容
     */
    private Map<String,String> contentMap;

    /**
     * 流程Id 必填
     */
    private Integer workflowId;

    /**
     * 内容类型  OUT 外部 IN 内部
     */
    private String contentType;

    /**
     *  渠道名称
     */
    private String channelName;

}
