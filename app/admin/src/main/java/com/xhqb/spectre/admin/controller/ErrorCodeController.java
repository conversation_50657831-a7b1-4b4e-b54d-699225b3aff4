package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.ErrorCodeDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ErrorCodeVO;
import com.xhqb.spectre.admin.service.ErrorCodeService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ErrorCodeQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 错误码
 *
 * <AUTHOR>
 * @date 2021/10/20
 */
@RestController
@RequestMapping("/errorCode")
@Slf4j
public class ErrorCodeController {

    @Resource
    private ErrorCodeService errorCodeService;

    /**
     * 查询错误码列表
     *
     * @param errorCodeQuery 错误码查询条件
     * @param pageNum        当前页码
     * @param pageSize       一页显示的记录数
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(ErrorCodeQuery errorCodeQuery, Integer pageNum, Integer pageSize) {
        errorCodeQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<ErrorCodeVO> commonPager = errorCodeService.listByPage(errorCodeQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询错误码详情
     *
     * @param type
     * @param xhErrCode
     * @return
     */
    @GetMapping("/{type}/{xhErrCode}")
    public AdminResult queryInfo(@PathVariable("type") String type, @PathVariable("xhErrCode") Integer xhErrCode) {
        return AdminResult.success(errorCodeService.getByPrimaryKey(type, xhErrCode));
    }

    /**
     * 添加错误码
     *
     * @param errorCodeDTO 新增的错误码内容
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_ERR_CODE_MAPPING)
    public AdminResult createInfo(@RequestBody ErrorCodeDTO errorCodeDTO) {
        log.info("create errorCodeDTO = {}", JSON.toJSONString(errorCodeDTO));
        errorCodeService.create(errorCodeDTO);
        return AdminResult.success();
    }

    /**
     * 更新错误码
     *
     * @param errorCodeDTO 更新的错误码内容
     * @return
     */
    @PutMapping("")
    @LogOpTime(OpLogConstant.MODULE_ERR_CODE_MAPPING)
    public AdminResult updateInfo(@RequestBody ErrorCodeDTO errorCodeDTO) {
        log.info("update errorCodeDTO = {}", JSON.toJSONString(errorCodeDTO));
        errorCodeService.update(errorCodeDTO);
        return AdminResult.success();
    }
}
