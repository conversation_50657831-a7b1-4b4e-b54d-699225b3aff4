package com.xhqb.spectre.admin.mq.send;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.mq.send.enums.MqSendTypeEnum;
import com.xhqb.spectre.admin.mq.send.enums.MqTimerTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产者发送消息上下文信息
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SenderContext<T> implements Serializable {

    /**
     * 消息内容
     */
    private T msg;
    /**
     * MQ发送方式 默认同步发送
     */
    private MqSendTypeEnum mqSendTypeEnum = MqSendTypeEnum.DEFAULT_SEND;
    /**
     * MQ发送方式 默认立即发送
     */
    private MqTimerTypeEnum mqTimerTypeEnum = MqTimerTypeEnum.NOW;
    /**
     * 延迟发送的时间
     * 如果选择 DELIVER_AFTER 方式，那么就是由该时间减去当前时间 若小于等于0 则立即发送
     * 如果选择 DELIVER_AT 方式，那么就设置到该时间点再发送 若小于当前时间 则立即发送
     */
    private Date deliverDate;

    public SenderContext(T msg) {
        this.msg = msg;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
