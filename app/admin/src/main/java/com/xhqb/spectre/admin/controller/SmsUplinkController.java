package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.SmsUplinkVO;
import com.xhqb.spectre.admin.model.vo.UplinkRelationOrderVO;
import com.xhqb.spectre.admin.service.SmsUplinkService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.SmsUplinkQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 上行短信
 *
 * <AUTHOR>
 * @date 2021/10/13
 */
@RestController
@RequestMapping("/smsUplink")
@Slf4j
public class SmsUplinkController {

    @Resource
    private SmsUplinkService smsUplinkService;

    /**
     * 查询上行短信列表
     *
     * @param smsUplinkQuery 上行短信查询条件
     * @param pageNum        当前页码
     * @param pageSize       一页显示的记录数
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(SmsUplinkQuery smsUplinkQuery, Integer pageNum, Integer pageSize) {
        smsUplinkQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<SmsUplinkVO> commonPager = smsUplinkService.listByPage(smsUplinkQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 关联发送记录列表
     *
     * @param id
     * @return
     */
    @GetMapping("/relationOrder/{id}")
    public AdminResult relationOrder(@PathVariable("id") Long id) {
        CommonPager<UplinkRelationOrderVO> commonPager = smsUplinkService.relationOrder(id);
        return AdminResult.success(commonPager);
    }


}
