package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.service.SmsReceiptService;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.SmsReceiptQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/30 15:14
 * @Description:
 */
@RestController
@RequestMapping("/smsReceipt")
@Slf4j
public class SmsReceiptController {

    @Autowired
    private SmsReceiptService smsReceiptService;

    /**
     * 查询回执记录总数
     *
     * @param smsReceiptQuery
     * @return
     */
    @GetMapping("/queryTotalCount")
    public AdminResult queryTotalCount(@ModelAttribute SmsReceiptQuery smsReceiptQuery) {
        return AdminResult.success(smsReceiptService.queryTotalCount(smsReceiptQuery));
    }

    /**
     * 查询回执记录列表
     *
     * @param smsReceiptQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/queryList")
    public AdminResult queryList(@ModelAttribute SmsReceiptQuery smsReceiptQuery, Integer pageNum, Integer pageSize) {
        smsReceiptQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        return AdminResult.success(smsReceiptService.listByPage(smsReceiptQuery));
    }

    /**
     * 查询明文手机号
     *
     * @param id
     * @return
     */
    @GetMapping("/queryMobile")
    public AdminResult queryMobile(Long id) {
        return AdminResult.success(smsReceiptService.queryMobile(id));
    }
}
