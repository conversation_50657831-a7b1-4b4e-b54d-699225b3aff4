package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.admin.constant.CommonConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/18 17:46
 * @Description:
 */
@Data
public class TplDisableDTO implements Serializable {

    private static final long serialVersionUID = 7519265547145351732L;

    @NotEmpty(message = "运营商不能为空")
    private List<String> ispList;

    @NotEmpty(message = "地域不能为空")
    private List<AreaDTO> areaList;

    @NotBlank(message = "开始时间不能为空")
    @Pattern(regexp = CommonConstant.PATTERN_DATE_TIME, message = "开始时间格式为" + CommonConstant.DATE_TIME_FORMAT)
    private String startTime;

    @NotBlank(message = "结束时间不能为空")
    @Pattern(regexp = CommonConstant.PATTERN_DATE_TIME, message = "结束时间格式为" + CommonConstant.DATE_TIME_FORMAT)
    private String endTime;
}
