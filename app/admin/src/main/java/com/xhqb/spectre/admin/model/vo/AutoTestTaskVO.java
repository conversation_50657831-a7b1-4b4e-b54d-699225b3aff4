package com.xhqb.spectre.admin.model.vo;

import lombok.Data;

/**
 * 自动测试任务 vo
 *
 * <AUTHOR>
 * @date 2025-07-09 11:23:20
 */
@Data
public class AutoTestTaskVO {

    /**
     * id
     */
    private Long id;
    /**
     * 名称
     */
    private String name;

    /**
     * 任务类型，0：TOP模板，1：自定义模板
     */
    private String type;

    /**
     * 绑定的模板，当type = 0 时，value格式：10（1-100的数字）；当type = 1 时，value格式：tplName1,tolName2（英文逗号分割）
     */
    private String bindTpl;

    /**
     * 测试频率，取值：1天1次、2天1次、3天1次
     */
    private String testFrequency;

    /**
     * 测试周期，例如：2025-07-09,2025-07-09（英文逗号分割）
     */
    private String testCycle;

    /**
     * 仅工作日，0：自然日；1：工作日
     */
    private Integer workDay;

    /**
     * 是否启用，0：禁用；1：启用
     */
    private Integer enable;

    /**
     * 备注
     */
    private String remark;
}
