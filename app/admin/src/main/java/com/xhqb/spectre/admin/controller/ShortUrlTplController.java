package com.xhqb.spectre.admin.controller;

import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.ShortUrlTplDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.service.ShortUrlTplService;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ShortUrlTplQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 短链模版
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/04/08
 * @Description:
 */
@RestController
@RequestMapping("/shortUrlTpl")
@Slf4j
public class ShortUrlTplController {

    @Resource
    private ShortUrlTplService shortUrlTplService;

    /**
     * 短链模版分页列表
     * @param shortUrlTplQuery 请求参数
     * @param pageNum 页吗
     * @param pageSize 页面大小
     * @return
     */
    @GetMapping("")
    public AdminResult page(@ModelAttribute ShortUrlTplQuery shortUrlTplQuery, Integer pageNum, Integer pageSize){
        shortUrlTplQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        log.info("短链模版分页请求参数｜shortUrlTplQuery:{}", JsonLogUtil.toJSONString(shortUrlTplQuery));
        return AdminResult.success(shortUrlTplService.page(shortUrlTplQuery));
    }

    /**
     * 新增
     * @param shortUrlTplDTO 新增参数
     * @return 主键id
     */
    @PostMapping("/add")
    @LogOpTime(OpLogConstant.MODULE_SHORT_URL_TPL)
    public AdminResult add(@RequestBody ShortUrlTplDTO shortUrlTplDTO) {
        log.info("add shortUrl, shortUrlTplDTO: {}", JsonLogUtil.toJSONString(shortUrlTplDTO));
        ValidatorUtil.validate(shortUrlTplDTO);
        return AdminResult.success(shortUrlTplService.add(shortUrlTplDTO));
    }


    /**
     * 更新
     * @param shortUrlTplDTO 更新参数
     * @return 主键id
     */
    @PostMapping("/update")
    @LogOpTime(OpLogConstant.MODULE_SHORT_URL_TPL)
    public AdminResult update(@RequestBody ShortUrlTplDTO shortUrlTplDTO) {
        log.info("update shortUrl, shortUrlTplDTO: {}", JsonLogUtil.toJSONString(shortUrlTplDTO));
        ValidatorUtil.validate(shortUrlTplDTO);
        return AdminResult.success(shortUrlTplService.update(shortUrlTplDTO));
    }

    /**
     * 删除
     * @param id 主键id
     * @return 主键id
     */
    @PostMapping("/delete/{id}")
    @LogOpTime(OpLogConstant.MODULE_SHORT_URL_TPL)
    public AdminResult delete(@PathVariable("id") Integer id) {
        log.info("delete shortUrl, id: {}", id);
        return AdminResult.success(shortUrlTplService.delete(id));
    }

    /**
     * 详情
     * @param id 主键id
     * @return 主键id
     */
    @GetMapping("/detail/{id}")
    public AdminResult detail(@PathVariable("id") Integer id) {
        log.info("info shortUrl, id: {}", id);
        return AdminResult.success(shortUrlTplService.detail(id));
    }
}
