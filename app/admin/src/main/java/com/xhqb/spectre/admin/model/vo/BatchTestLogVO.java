package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.BatchTestLogDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 群发测试日志
 *
 * <AUTHOR>
 * @date 2021/12/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BatchTestLogVO implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 批次号
     */
    private Integer taskId;

    /**
     * 请求内容
     */
    private String reqContent;

    /**
     * 响应内容
     */
    private String respContent;

    /**
     * 测试请求状态 0->失败 1->成功
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private String updater;


    /**
     * 查询数据详情展现
     *
     * @param batchTestLogDO
     * @return
     */
    public static BatchTestLogVO buildInfoQuery(BatchTestLogDO batchTestLogDO) {
        return BatchTestLogVO.builder()
                // 主键
                .id(batchTestLogDO.getId())
                // 批次号
                .taskId(batchTestLogDO.getTaskId())
                // 请求内容
                .reqContent(batchTestLogDO.getReqContent())
                // 响应内容
                .respContent(batchTestLogDO.getRespContent())
                // 测试请求状态 0->失败 1->成功
                .status(batchTestLogDO.getStatus())
                // 创建时间
                .createTime(batchTestLogDO.getCreateTime())
                // 创建者
                .creator(batchTestLogDO.getCreator())
                // 更新时间
                .updateTime(batchTestLogDO.getUpdateTime())
                // 更新者
                .updater(batchTestLogDO.getUpdater())
                .build();
    }
}
