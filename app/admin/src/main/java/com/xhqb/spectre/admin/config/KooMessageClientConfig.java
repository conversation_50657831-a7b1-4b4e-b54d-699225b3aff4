package com.xhqb.spectre.admin.config;

import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.koomessage.v1.KooMessageClient;
import com.huaweicloud.sdk.koomessage.v1.region.KooMessageRegion;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class KooMessageClientConfig {

    @Value("${spectre.admin.kooMessage.ak}")
    private String kooMessageAK;

    @Value("${spectre.admin.kooMessage.sk}")
    private String kooMessageSK;

    @Bean
    public KooMessageClient kooMessageClient() {
        ICredential auth = new BasicCredentials()
                .withAk(kooMessageAK)
                .withSk(kooMessageSK);
        return KooMessageClient.newBuilder()
                .withCredential(auth)
                .withRegion(KooMessageRegion.valueOf("cn-north-4"))
                .build();
    }
}
