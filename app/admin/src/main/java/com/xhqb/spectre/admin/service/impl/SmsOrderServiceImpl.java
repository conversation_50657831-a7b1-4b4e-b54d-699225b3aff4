package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.cif.entity.CifCustomerBaseDO;
import com.xhqb.spectre.admin.cif.mapper.CifCustomerBaseMapper;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.readonly.vo.SmsOrderPageVO;
import com.xhqb.spectre.admin.readonly.vo.SmsOrderVO;
import com.xhqb.spectre.admin.openapi.response.QueryOrderVO;
import com.xhqb.spectre.admin.readonly.mapper.SmsOrderReadonlyMapper;
import com.xhqb.spectre.admin.service.SmsOrderService;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.query.SmsOrderQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/23 18:11
 * @Description:
 */
@Service
@Slf4j
public class SmsOrderServiceImpl implements SmsOrderService {

    @Autowired
    private SmsOrderReadonlyMapper smsOrderReadonlyMapper;
    @Resource
    private CifCustomerBaseMapper cifCustomerBaseMapper;

    /**
     * 查询记录总数
     *
     * @param smsOrderQuery
     * @return
     */
    @Override
    public Integer queryTotalCount(SmsOrderQuery smsOrderQuery) {
        checkParam(smsOrderQuery);
        boolean queryCidResult = setSmsOrderQueryMobileByCid(smsOrderQuery);
        if (!queryCidResult) {
            return 0;
        }
        setDefaultIspToNull(smsOrderQuery);
        setTimestampsFromSmsOrderQuery(smsOrderQuery);
        return smsOrderReadonlyMapper.countByQuery(smsOrderQuery);
    }

    /**
     * 查询发送记录列表
     *
     * @param smsOrderQuery
     * @return
     */
    @Override
    public SmsOrderPageVO listByPage(SmsOrderQuery smsOrderQuery) {
        boolean isQueryTotal = checkParam(smsOrderQuery);
        boolean queryCidResult = setSmsOrderQueryMobileByCid(smsOrderQuery);
        if (!queryCidResult) {
            return SmsOrderPageVO.builder()
                    .dataList(new ArrayList<>())
                    .totalCount(0)
                    .returnTotal(isQueryTotal)
                    .hasNextPage(false)
                    .build();
        }
        setDefaultIspToNull(smsOrderQuery);
        setTimestampsFromSmsOrderQuery(smsOrderQuery);
        List<SmsOrderVO> smsOrderVOList;
        Integer totalCount = 0;
        boolean hasNextPage = true;
        if (isQueryTotal) {
            totalCount = smsOrderReadonlyMapper.countByQuery(smsOrderQuery);
            smsOrderVOList = smsOrderReadonlyMapper.selectByQuery(smsOrderQuery).stream()
                    .map(SmsOrderVO::buildSmsOrderVO).collect(Collectors.toList());
        } else {
            //假分页处理，每次多查询一条记录，判断有无下一页
            int realPageSize = smsOrderQuery.getPageParameter().getPageSize() + 1;
            smsOrderQuery.getPageParameter().setPageSize(realPageSize);
            List<SmsOrderDO> smsOrderDOList = smsOrderReadonlyMapper.selectByQuery(smsOrderQuery);
            if (smsOrderDOList.size() < realPageSize) {
                hasNextPage = false;
            } else {
                //删除多查询出来的那条记录
                smsOrderDOList.remove(realPageSize - 1);
            }
            smsOrderVOList = smsOrderDOList.stream().map(SmsOrderVO::buildSmsOrderVO).collect(Collectors.toList());
        }
        return SmsOrderPageVO.builder()
                .dataList(smsOrderVOList)
                .totalCount(totalCount)
                .returnTotal(isQueryTotal)
                .hasNextPage(hasNextPage)
                .build();
    }

    /**
     * 查询明文手机号
     *
     * @param orderId
     * @return
     */
    @Override
    public String queryMobile(Long orderId) {
        if (Objects.isNull(orderId) || orderId == 0) {
            throw new BizException("订单号不能为空");
        }
        SmsOrderDO smsOrderDO = smsOrderReadonlyMapper.selectByOrderId(orderId);
        if (Objects.isNull(smsOrderDO)) {
            throw new BizException("未找到该发送记录");
        }
        return smsOrderDO.getMobile();
    }

    /**
     * 参数校验
     *
     * @param smsOrderQuery
     * @return boolean 是否查询总数，传入订单号或手机号时，才会去查询总数
     */
    private boolean checkParam(SmsOrderQuery smsOrderQuery) {
        if (StringUtils.isNotBlank(smsOrderQuery.getOrderId()) || StringUtils.isNotBlank(smsOrderQuery.getMobile())) {
            return true;
        }
        //订单号和手机号为空时，需校验时间
        String startTime = smsOrderQuery.getStartTime();
        String endTime = smsOrderQuery.getEndTime();
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            throw new BizException("订单号和手机号条件为空时，开始时间和结束时间必填");
        }
        Date start = DateUtil.stringToDate(startTime);
        Date end = DateUtil.stringToDate(endTime);
        if (Objects.isNull(start) || Objects.isNull(end)) {
            throw new BizException("时间格式有误");
        }
//        if (end.getTime() - start.getTime() > 30 * 86400 * 1000) {
//            throw new BizException("订单号和手机号条件为空时，查询时间跨度不能超过30天");
//        }

        if (StringUtils.isNotEmpty(smsOrderQuery.getSignName())) {
            String signName = smsOrderQuery.getSignName().replaceAll("(?:\\【|】|\\[|])", "");
            smsOrderQuery.setSignName("【" + signName + "】");
        }

        return false;
    }

    @Override
    public List<QueryOrderVO> queryOrder(List<String> requestIdList) {

        Date curDate = new Date();
        Date beginOfCurDate = cn.hutool.core.date.DateUtil.beginOfDay(curDate);
        long startTimestamp = DateUtils.addDays(beginOfCurDate, 3).getTime() / 1000;
        long endTimestamp = cn.hutool.core.date.DateUtil.endOfDay(curDate).getTime() / 1000;

        if (requestIdList.size() == 1) {
            SmsOrderDO smsOrderDO = smsOrderReadonlyMapper.selectByRequestId(requestIdList.get(0), startTimestamp, endTimestamp);
            return DO2VO(Collections.singletonList(smsOrderDO));
        } else {
            List<SmsOrderDO> smsOrderDOList = smsOrderReadonlyMapper.selectByRequestIds(requestIdList, startTimestamp, endTimestamp);
            List<SmsOrderDO> filter = new ArrayList<>();
            if (smsOrderDOList != null && smsOrderDOList.size() > 0) {
                HashMap<String, SmsOrderDO> map = new HashMap<>();
                for (SmsOrderDO smsOrderDO : smsOrderDOList) {
                    SmsOrderDO inner = map.get(smsOrderDO.getRequestId());
                    if (inner == null || inner.getCreateTime().before(smsOrderDO.getCreateTime())) {
                        map.put(smsOrderDO.getRequestId(), smsOrderDO);
                    }
                }
                filter = new ArrayList<>(map.values());
            }

            return DO2VO(filter);
        }
    }

    private List<QueryOrderVO> DO2VO(List<SmsOrderDO> dos) {
        List<QueryOrderVO> queryOrderVOList = new ArrayList<>();
        for (SmsOrderDO smsOrderDO : dos) {
            if (smsOrderDO != null) {
                QueryOrderVO queryOrderVO = QueryOrderVO.builder()
                        .requestId(smsOrderDO.getRequestId())
                        .orderId(smsOrderDO.getOrderId())
                        .mobile(smsOrderDO.getMobile())
                        .sendStatus(smsOrderDO.getSendStatus())
                        .reportStatus(smsOrderDO.getReportStatus())
                        .build();
                queryOrderVOList.add(queryOrderVO);
            }
        }
        return queryOrderVOList;
    }

    /**
     * 根据CID设置SmsOrderQuery的手机号码
     * <a href="https://www.tapd.cn/67739864/prong/stories/view/1167739864001163663">...</a>
     *
     * @param smsOrderQuery SmsOrderQuery，包含CID和其他相关信息
     */
    private boolean setSmsOrderQueryMobileByCid(SmsOrderQuery smsOrderQuery) {
        boolean cidTag = true;
        if (StringUtils.isNotBlank(smsOrderQuery.getCid())) {
            CifCustomerBaseDO cifCustomerBaseDO = cifCustomerBaseMapper.selectByPrimaryKey(smsOrderQuery.getCid());
            if (cifCustomerBaseDO != null && StringUtils.isNotBlank(cifCustomerBaseDO.getMobilePhone())) {
                smsOrderQuery.setMobile(cifCustomerBaseDO.getMobilePhone());
            } else {
                cidTag = false;
            }
        }

        return cidTag;
    }

    private void setTimestampsFromSmsOrderQuery(SmsOrderQuery smsOrderQuery) {
        Date startDate = DateUtil.stringToDate(smsOrderQuery.getStartTime());
        Date endDate = DateUtil.stringToDate(smsOrderQuery.getEndTime());

        if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            smsOrderQuery.setStartTimestamp(startDate.getTime() / 1000);
            smsOrderQuery.setEndTimestamp(endDate.getTime() / 1000);
        }
    }

    private void setDefaultIspToNull(SmsOrderQuery smsOrderQuery) {
        if (StringUtils.equals(smsOrderQuery.getIsp(), "default")) {
            smsOrderQuery.setIsp(null);
        }
    }
}
