package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.BatchTaskBigdataDTO;
import com.xhqb.spectre.admin.model.vo.BatchTaskBigdataVO;
import com.xhqb.spectre.admin.service.BatchTaskBigdataService;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.BatchTaskBigdataDO;
import com.xhqb.spectre.common.dal.mapper.BatchTaskBigdataMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.BatchTaskBigdataQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 大数据群发任务
 *
 * <AUTHOR>
 * @date 2021/10/28
 */
@Service
@Slf4j
public class BatchTaskBigdataServiceImpl implements BatchTaskBigdataService {

    @Resource
    private BatchTaskBigdataMapper batchTaskBigdataMapper;

    /**
     * 分页查询大数据群发任务列表
     *
     * @param batchTaskBigdataQuery
     * @return
     */
    @Override
    public CommonPager<BatchTaskBigdataVO> listByPage(BatchTaskBigdataQuery batchTaskBigdataQuery) {
        return PageResultUtils.result(
                () -> batchTaskBigdataMapper.countByQuery(batchTaskBigdataQuery),
                () -> batchTaskBigdataMapper.selectByQuery(batchTaskBigdataQuery).stream().map(BatchTaskBigdataVO::buildListQuery).collect(Collectors.toList())
        );
    }

    /**
     * 保存大数据群发任务信息
     *
     * @param batchTaskBigdataDTO
     */
    @Override
    public void create(BatchTaskBigdataDTO batchTaskBigdataDTO) {
        ValidatorUtil.validate(batchTaskBigdataDTO);
        BatchTaskBigdataDO batchTaskBigdataDO = buildBatchTaskBigdataDO(batchTaskBigdataDTO);
        batchTaskBigdataMapper.insertSelective(batchTaskBigdataDO);
    }

    /**
     * 更新大数据群发任务信息
     *
     * @param id
     * @param batchTaskBigdataDTO
     */
    @Override
    public void update(Integer id, BatchTaskBigdataDTO batchTaskBigdataDTO) {
        //参数格式校验
        selectById(id);
        BatchTaskBigdataDO batchTaskBigdataDO = buildBatchTaskBigdataDO(batchTaskBigdataDTO);
        batchTaskBigdataDO.setId(id);
        batchTaskBigdataMapper.updateByPrimaryKeySelective(batchTaskBigdataDO);
    }


    /**
     * 根据ID查询大数据群发任务
     *
     * @param id
     * @return
     */
    private BatchTaskBigdataDO selectById(Integer id) {
        BatchTaskBigdataDO batchTaskBigdataDO = batchTaskBigdataMapper.selectByPrimaryKey(id);
        if (Objects.isNull(batchTaskBigdataDO)) {
            throw new BizException("大数据群发任务不存在");
        }
        return batchTaskBigdataDO;
    }

    /**
     * 构建DO对象
     *
     * @param batchTaskBigdataDTO
     * @return
     */
    private BatchTaskBigdataDO buildBatchTaskBigdataDO(BatchTaskBigdataDTO batchTaskBigdataDTO) {
        BatchTaskBigdataDO result = new BatchTaskBigdataDO();
        // 业务应用CODE
        result.setAppCode(batchTaskBigdataDTO.getAppCode());
        // 模板ID
        result.setTplId(batchTaskBigdataDTO.getTplId());
        // 文件url地址
        result.setFileUrl(batchTaskBigdataDTO.getFileUrl());
        // 状态 ，0：待处理；1：已处理；2：处理失败；3：处理中 ；4：已废弃(删除) ；5：已超时(taskNo查询一直处于处理中)
        result.setStatus(batchTaskBigdataDTO.getStatus());
        // 描述信息
        result.setDescription(batchTaskBigdataDTO.getDescription());
        // 任务编号，由批量任务手动写入
        result.setTaskNo(batchTaskBigdataDTO.getTaskNo());
        // 群发任务批次号，由批次任务写入
        result.setTaskId(batchTaskBigdataDTO.getTaskId());
        // 预定发送时间
        Date sendTime = batchTaskBigdataDTO.getSendTime();
        if (Objects.nonNull(sendTime)) {
            result.setSendTime(DateUtil.dateToInt(sendTime));
        }
        return result;
    }

}
