package com.xhqb.spectre.admin.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.service.ChannelAccountService;
import com.xhqb.spectre.common.dal.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/21 16:03
 * @Description: 管理后台配置缓存刷新任务
 */
@Component
@Job("adminCacheRefreshJob")
@Slf4j
public class AdminCacheRefreshJob implements SimpleJob {

    @Autowired
    private ChannelAccountService channelAccountService;

    /**
     * 刷新管理后台配置缓存，每隔30分钟执行一次
     *
     * @param shardingContext
     */
    @Override
    public void execute(ShardingContext shardingContext) {
        // 刷新渠道账号缓存
        log.info("刷新渠道账号缓存开始");
        this.refreshChannelAccountCache();
        log.info("刷新渠道账号缓存结束");

    }

    /**
     * 刷新渠道账号缓存
     */
    public void refreshChannelAccountCache() {
        int pageSize = 2000;
        Integer id = 0;
        List<ChannelAccountDO> channelAccountDOList = channelAccountService.refreshCacheQuery(id, pageSize);
        while (Objects.nonNull(channelAccountDOList) && !channelAccountDOList.isEmpty()) {
            for (ChannelAccountDO channelAccountDO : channelAccountDOList) {
                // 写入缓存
                channelAccountService.channelAccountToRedis(channelAccountDO);
            }
            id = channelAccountDOList.get(channelAccountDOList.size() - 1).getId();
            channelAccountDOList = channelAccountService.refreshCacheQuery(id, pageSize);
        }
    }
}
