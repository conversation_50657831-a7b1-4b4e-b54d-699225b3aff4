package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.mq.producer.impl.SmsEventProducer;
import com.xhqb.spectre.admin.mq.send.SenderContext;
import com.xhqb.spectre.admin.mq.send.enums.MqSendTypeEnum;
import com.xhqb.spectre.admin.mq.send.enums.MqTimerTypeEnum;
import com.xhqb.spectre.common.enums.BlacklistActionEnum;
import com.xhqb.spectre.common.enums.BlacklistTypeEnum;
import com.xhqb.spectre.common.event.data.BlacklistEventData;
import com.xhqb.spectre.common.message.SmsEventMessage;
import com.xhqb.spectre.common.service.SmsEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service("adminSmsEventService")
@Slf4j
public class AdminSmsEventServiceImpl implements SmsEventService {

    @Autowired
    private SmsEventProducer smsEventProducer;

    @Override
    public void sendBlacklistEvent(String mobile, BlacklistActionEnum action,
                                   BlacklistTypeEnum blacklistType,
                                   String smsType, String reason,
                                   String operator, String source) {
        try {
            BlacklistEventData eventData = BlacklistEventData.builder()
                    .mobile(mobile)
                    .action(action)
                    .blacklistType(blacklistType)
                    .smsType(smsType)
                    .reason(reason)
                    .operateTime(LocalDateTime.now())
                    .build();

            SmsEventMessage eventMessage = SmsEventMessage.createBlacklistEvent(
                    JSON.toJSONString(eventData), source);

            SenderContext<SmsEventMessage> senderContext = new SenderContext<>();
            senderContext.setMsg(eventMessage);
            senderContext.setMqSendTypeEnum(MqSendTypeEnum.DEFAULT_SEND);
            senderContext.setMqTimerTypeEnum(MqTimerTypeEnum.NOW);

            smsEventProducer.send(senderContext);

            log.info("成功发送黑名单事件: mobile={}, action={}, type={}, source={}",
                    mobile, action.getCode(), blacklistType.getCode(), source);

        } catch (Exception e) {
            log.error("发送黑名单事件失败: mobile={}, action={}, error={}",
                    mobile, action.getCode(), e.getMessage(), e);
        }
    }
}
