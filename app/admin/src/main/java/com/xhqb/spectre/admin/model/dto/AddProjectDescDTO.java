package com.xhqb.spectre.admin.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class AddProjectDescDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    @NotBlank(message = "签名描述不能为空")
    @Size(max = 32, message = "签名描述最大为{max}个字符")
    private String description;

    private Integer status;
}
