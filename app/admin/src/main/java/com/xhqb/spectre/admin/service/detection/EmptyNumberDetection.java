package com.xhqb.spectre.admin.service.detection;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import com.google.common.collect.Lists;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.model.result.ShortCodeResult;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.mapper.TestMobileMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EmptyNumberDetection {

    @Resource
    private VenusConfig venusConfig;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private TestMobileMapper testMobileMapper;

    public List<String> sendDetect(List<String> requestMobiles) {
        // 空号检测
        List<String> resultList = batchDetect(requestMobiles);
        // 检测 mobile 是空号 更新检测时间并且 在检测时间至上增加 7 天
        List<String> updateList = CommonUtil.intersection(requestMobiles, resultList);
        if(!CollectionUtil.isEmpty(updateList)){
            testMobileMapper.updateDetectTimeByMobileList(updateList, DateUtils.addDays(new Date(), 7));
        }
        // 不是空号则进行删除
        List<String> deleteList = CommonUtil.exclude(requestMobiles, resultList);
        if(!CollectionUtil.isEmpty(updateList)){
            testMobileMapper.updateDeleteByMobileList(deleteList);
        }
        return resultList;
    }

    public List<String> batchDetect(List<String> requestMobiles) {
        List<List<String>> mobilePartition = Lists.partition(requestMobiles, 100);
        List<String> resultList = new ArrayList<>();
        for (List<String> mobileList : mobilePartition) {
            resultList.addAll(detect(mobileList));
        }
        return resultList;
    }

    /**
     * 检测空号 批量检测 最大 size 100
     * @param requestMobiles 检测空号 list
     * @return 检测结果 为空号 list
     */
    public List<String> detect(List<String> requestMobiles) {
        // 判断检测号码
        if (CollectionUtil.isEmpty(requestMobiles)){
            return new ArrayList<>();
        }

        // 构造请求参数
        Map<String, String> requestParam = MapUtil.<String, String>builder()
                .put("phoneNumbers", String.join(",", requestMobiles))
                .build();
        String timestampValue = (System.currentTimeMillis() / 1000) + "";
        String nonceValue = RandomUtil.randomNumbers(2);
        String signValue = SecureUtil.sha1(append(timestampValue, nonceValue));
        HttpHeaders headers = new HttpHeaders();
        headers.set("appKey", venusConfig.getAppCode());
        headers.set("timestamp", timestampValue);
        headers.set("nonce", nonceValue);
        headers.set("sign", signValue);
        headers.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestParam, headers);

        List<String> resultList = new ArrayList<>();
        ResponseEntity<CheckPhoneResult> response = null;
        try {
            // 调 api 接口
            response = restTemplate.exchange(venusConfig.getSpectreApiHost() + "/api/spectre/v3/checkPhone",
                    HttpMethod.POST, requestEntity, CheckPhoneResult.class);
            log.info("检测空号接口调用结果:{}", JsonLogUtil.toJSONString(response));
            // 解析 api 接口 response 并构造返回值
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null && response.getBody().getData() != null) {
                List<CheckPhoneResultItem> checkPhoneResultItems = response.getBody().getData().getPhoneResult().stream().filter(item -> item.getStatus() == 0).collect(Collectors.toList());
                resultList.addAll(checkPhoneResultItems.stream()
                        .map(CheckPhoneResultItem::getMobile).collect(Collectors.toList()));
                return resultList;
            }
        }catch (Exception e){
            log.warn("检测空号接口调用失败", e);
        }
        return resultList;
    }

    String append(String timestampValue, String nonceValue) {
        return "appKey" +
                venusConfig.getAppCode() +
                "nonce" +
                nonceValue +
                "timestamp" +
                timestampValue +
                venusConfig.getAppSecret();
    }

    @Data
    public static class CheckPhoneResult {

        private Integer code;

        private String msg;

        private CheckPhoneResultData data;

    }


    @Data
    public static class CheckPhoneResultData {

        private String requestId;

        private List<CheckPhoneResultItem> phoneResult;
    }

    @Data
    public static class CheckPhoneResultItem {

        private String mobile;

        private String lastTime;

        private String area;

        /**
         * 状态 -1:未查询, 0:空号, 1:正常, 2:停机, 3:库无, 4:沉默号, 5:风险号, 99:未知
         */
        private Integer status;
    }

}
