package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.vo.AutoTestTaskVO;
import com.xhqb.spectre.admin.model.vo.TestContentTaskVO;
import com.xhqb.spectre.common.dal.query.AutoTestOverrideQuery;
import com.xhqb.spectre.common.dal.entity.AutoTestTaskDO;
import com.xhqb.spectre.common.dal.query.AutoTestTaskQuery;
import com.xhqb.spectre.common.dal.page.CommonPager;

public interface AutoTestTaskService {

    CommonPager<AutoTestTaskDO> list(AutoTestTaskQuery query);

    AutoTestTaskDO detail(Long id);

    void add(AutoTestTaskVO vo);

    void edit(AutoTestTaskVO vo);

    CommonPager<TestContentTaskVO> override(AutoTestOverrideQuery query);
}
