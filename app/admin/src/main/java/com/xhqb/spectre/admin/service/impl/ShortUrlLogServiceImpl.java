package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.readonly.vo.ShortUrlLogPageVO;
import com.xhqb.spectre.admin.readonly.vo.ShortUrlLogVO;
import com.xhqb.spectre.admin.service.ShortUrlLogService;
import com.xhqb.spectre.common.dal.entity.ShortUrlLogDO;
import com.xhqb.spectre.common.dal.mapper.ShortUrlLogMapper;
import com.xhqb.spectre.common.dal.query.ShortUrlLogQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/8/8 13:43
 **/
@Service
public class ShortUrlLogServiceImpl implements ShortUrlLogService {
    @Resource
    private ShortUrlLogMapper shortUrlLogMapper;

    @Resource
    private VenusConfig venusConfig;

    /**
     * 根据条件查找总数
     *
     * @param shortUrlLogQuery
     * @return
     */
    @Override
    public int queryTotalCount(ShortUrlLogQuery shortUrlLogQuery) {
        shortUrlLogQuery.setShortUrl(getShortCodeByShortUrl(shortUrlLogQuery.getShortUrl()));
        return shortUrlLogMapper.countByQuery(shortUrlLogQuery);
    }

    /**
     * 查询数据列表不查总数
     *
     * @param shortUrlLogQuery
     * @return
     */
    @Override
    public ShortUrlLogPageVO listByPage(ShortUrlLogQuery shortUrlLogQuery) {
        shortUrlLogQuery.setShortUrl(getShortCodeByShortUrl(shortUrlLogQuery.getShortUrl()));
        //多加一个记录判断是否有下一页
        Integer realPageSize = shortUrlLogQuery.getPageParameter().getPageSize() + 1;
        boolean hasNextPage = true;
        shortUrlLogQuery.getPageParameter().setPageSize(realPageSize);
        List<ShortUrlLogDO> shortUrlLogDOList = shortUrlLogMapper.selectByQuery(shortUrlLogQuery);
        if (CollectionUtils.isEmpty(shortUrlLogDOList)) {
            return new ShortUrlLogPageVO(Collections.emptyList(), false);
        }
        if (shortUrlLogDOList.size() < realPageSize) {
            hasNextPage = false;
        } else {
            shortUrlLogDOList.remove(realPageSize - 1);
        }
        //转成前端数据
        List<ShortUrlLogVO> shortUrlLogVOList = shortUrlLogDOList.stream()
                .map(shortUrlLogDO -> ShortUrlLogVO.bulilderByDO(shortUrlLogDO, new ShortUrlLogVO(), buildShortUrl(shortUrlLogDO.getShortCode())))
                .collect(Collectors.toList());
        return ShortUrlLogPageVO.builder()
                .dataList(shortUrlLogVOList)
                .hasNextPage(hasNextPage)
                .build();
    }

    /**
     * 短链转短码
     *
     * @param shortUrl 短链接
     * @return 短码
     */
    private String getShortCodeByShortUrl(String shortUrl) {
        return Optional.ofNullable(shortUrl).map(s -> {
            int i = s.lastIndexOf("/") + 1;
            return i == 0 ? s : s.substring(i);
        }).orElse("");
    }

    /**
     * 生成短链
     *
     * @param shortCode 短链编码
     * @return
     */
    private String buildShortUrl(String shortCode) {
        return venusConfig.getShortUrlDomain() + "/" + shortCode;
    }
}
