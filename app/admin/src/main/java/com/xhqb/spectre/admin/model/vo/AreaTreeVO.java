package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.AreasInfoDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/26 17:56
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AreaTreeVO implements Serializable {

    private static final long serialVersionUID = 3866141671810952967L;

    /**
     * 地域ID
     */
    private String id;

    /**
     * 地域名称
     */
    private String name;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 省份简名
     */
    private String provinceShortName;

    /**
     * 城市简名
     */
    private String cityShortName;

    /**
     * 父级地域ID
     */
    private String parentId;

    /**
     * 子地域列表
     */
    private List<AreaTreeVO> children;

    public static AreaTreeVO buildAreaTreeVO(AreasInfoDO item) {
        List<AreaTreeVO> children = new ArrayList<>();
        return AreaTreeVO.builder()
                .id(item.getId())
                .name(item.getName())
                .province(item.getProvince())
                .city(item.getCity())
                .provinceShortName(item.getProvinceShortName())
                .cityShortName(item.getCityShortName())
                .parentId(item.getParentId())
                .children(children)
                .build();
    }
}
