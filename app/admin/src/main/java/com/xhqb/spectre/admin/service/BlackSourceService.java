package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.AddBlackSourceDTO;
import com.xhqb.spectre.admin.model.dto.AddSignDTO;
import com.xhqb.spectre.admin.model.dto.UpdateBlackSourceDTO;
import com.xhqb.spectre.admin.model.dto.UpdateSignDTO;
import com.xhqb.spectre.admin.model.vo.BlackSourceEnumVO;
import com.xhqb.spectre.admin.model.vo.BlackSourceVO;
import com.xhqb.spectre.admin.model.vo.SignEnumVO;
import com.xhqb.spectre.admin.model.vo.SignVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.BlackSourceQuery;
import com.xhqb.spectre.common.dal.query.SignQuery;

import java.util.List;


public interface BlackSourceService {

    CommonPager<BlackSourceVO> listByPage(BlackSourceQuery blackSourceQuery);

    BlackSourceVO getById(Integer id);

    void create(AddBlackSourceDTO addBlackSourceDTO);

    void update(UpdateBlackSourceDTO updateBlackSourceDTO);

    void enable(Integer id);

    void disable(Integer id);

    void delete(Integer id);

    List<BlackSourceEnumVO> queryEnum(Integer status);
}
