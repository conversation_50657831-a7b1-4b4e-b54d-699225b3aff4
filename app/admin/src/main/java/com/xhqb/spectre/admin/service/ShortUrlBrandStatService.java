package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.vo.ShortUrlBrandStatPageVO;
import com.xhqb.spectre.admin.model.vo.ShortUrlBrandStatProportionVO;
import com.xhqb.spectre.admin.model.vo.ShortUrlBrandStatTrendChartVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ShortUrlBrandStatQuery;

import java.util.Date;

public interface ShortUrlBrandStatService {
    CommonPager<ShortUrlBrandStatPageVO> page(ShortUrlBrandStatQuery shortUrlBrandStatQuery);

    @Deprecated
    ShortUrlBrandStatProportionVO brandProportion(ShortUrlBrandStatQuery shortUrlBrandStatQuery);
    @Deprecated
    ShortUrlBrandStatTrendChartVO trendChart(ShortUrlBrandStatQuery shortUrlBrandStatQuery);

    String runJob(Date curDateStr);

    ShortUrlBrandStatProportionVO pvProportion(ShortUrlBrandStatQuery shortUrlBrandStatQuery);

    ShortUrlBrandStatProportionVO uvProportion(ShortUrlBrandStatQuery shortUrlBrandStatQuery);
}
