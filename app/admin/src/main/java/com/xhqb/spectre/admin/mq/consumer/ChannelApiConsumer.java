package com.xhqb.spectre.admin.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.mq.message.ChannelTestData;
import com.xhqb.spectre.admin.service.ChannelTestTaskService;
import com.xhqb.spectre.common.dal.dto.mq.ApiChannelMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class ChannelApiConsumer {

    @Resource
    private ChannelTestTaskService channelTestTaskService;

    /**
     * 消费topic:spectre-channel-api-test
     *
     * @param message 消息
     */

    @MQConsumer(topic = "#{'${kael.mq.consumers}'.split(',')[2]}", subscriptionType = SubscriptionType.Shared,
            clazz = String.class, receiverQueueSize = 1, ackTimeout = 60L)
    public void consumer(String message) {
        long start = System.currentTimeMillis();
        if (Strings.isBlank(message)) {
            return;
        }

        List<ApiChannelMessage> apiChannelMessageList = JSON.parseArray(message, ApiChannelMessage.class);
        log.info("消费api渠道测试消息:{}", JsonLogUtil.toJSONString(apiChannelMessageList));
        List<String> failList = new ArrayList<>();
        try {
            failList = channelTestTaskService.apiChannelTestTask(apiChannelMessageList);
        } catch (Exception e) {
            log.warn("消费api渠道测试消息异常", e);
        }
        log.info("消费api渠道测试消息完成,失败个数:{},耗时:{}", failList.size(), System.currentTimeMillis() - start);
    }
}
