package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.ChannelAccountDisableDTO;
import com.xhqb.spectre.admin.model.vo.ChannelAccountDisableVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ChannelAccountDisableQuery;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/22 16:14
 * @Description:
 */
public interface ChannelAccountDisableService {

    CommonPager<ChannelAccountDisableVO> listByPage(ChannelAccountDisableQuery channelAccountDisableQuery);

    ChannelAccountDisableVO getById(Integer id);

    void create(ChannelAccountDisableDTO accountDisableDTO);

    void update(ChannelAccountDisableDTO accountDisableDTO);

    void delete(Integer id);
}
