package com.xhqb.spectre.admin.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TplChannelRateVO implements Serializable {

    private static final long serialVersionUID = 1L;
    private String tplCode;
    private List<ChannelStat> channels;
    // 总触达量
    private Integer reachTotal;

    @Data
    public static class ChannelStat {
        private String channelCode;
        private String channelCodeName;
        private Integer sendCount;
        private Integer reachCount;
        private String reachRate;
    }
}
