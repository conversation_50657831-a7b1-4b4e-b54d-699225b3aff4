package com.xhqb.spectre.admin.mq.send.impl;

import com.xhqb.spectre.admin.mq.send.ProducerSender;
import com.xhqb.spectre.admin.mq.send.SenderContext;
import com.xhqb.spectre.admin.mq.send.SenderResult;
import org.apache.pulsar.client.api.MessageId;
import org.apache.pulsar.client.api.TypedMessageBuilder;
import org.springframework.stereotype.Component;

/**
 * 同步消息发送
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Component
public class DefaultProducerSender implements ProducerSender {

    /**
     * 做mq消息发送
     *
     * @param messageBuilder
     * @param senderContext
     * @return
     * @throws Exception
     */
    @Override
    public <T> SenderResult send(TypedMessageBuilder<String> messageBuilder, SenderContext<T> senderContext) throws Exception {
        MessageId messageId = messageBuilder.send();
        return SenderResult.builder().messageId(messageId).build();
    }
}
