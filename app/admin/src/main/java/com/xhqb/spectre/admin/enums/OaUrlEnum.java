package com.xhqb.spectre.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OaUrlEnum {

    REGISTER("/api/ec/dev/auth/regist", "register"),
    APPLY_TOKEN("/api/ec/dev/auth/applytoken", "applyToken"),
    GET_HRM_USERINFO_WITH_PAGE("/api/hrm/resful/getHrmUserInfoWithPage", "getHrmUserInfoWithPage"),
    DO_CREATE_REQUEST("/api/workflow/paService/doCreateRequest", "doCreateRequest"),
    GET_REQUEST_OPERATOR_INFO("/api/workflow/paService/getRequestOperatorInfo", "getRequestOperatorInfo"),
    GET_WORKFLOW_REQUEST_LOGS("/api/workflow/paService/getWorkflowRequestLogs", "getWorkflowRequestLogs"),
    GET_WORK_FLOW_REQUEST("/api/workflow/paService/getWorkflowRequest", "getWorkflowRequest");


    private final String url;
    private final String desc;
}
