package com.xhqb.spectre.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SceneCodeEnum {
    REGISTERED("registered", "已注册用户"),
    UNREGISTERED("unregistered", "未注册用户"),
    PAID("paid", "已获额用户");
    private final String code;
    private final String desc;

    public static SceneCodeEnum getByCode(String code) {
        for (SceneCodeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("No matching code found");
    }
}
