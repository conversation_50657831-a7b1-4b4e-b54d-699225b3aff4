package com.xhqb.spectre.admin.mq.send.decorator;

import com.xhqb.spectre.admin.mq.send.ProducerSender;
import com.xhqb.spectre.admin.mq.send.SenderContext;
import com.xhqb.spectre.admin.mq.send.SenderResult;
import org.apache.pulsar.client.api.TypedMessageBuilder;

/**
 * <AUTHOR>
 * @date 2021/9/24
 */
public abstract class AbstractSenderDecorator implements ProducerSender {

    private ProducerSender producerSender;

    public AbstractSenderDecorator(ProducerSender producerSender) {
        this.producerSender = producerSender;
    }

    /**
     * 做mq消息发送
     *
     * @param messageBuilder
     * @param senderContext
     * @return
     * @throws Exception
     */
    @Override
    public <T> SenderResult send(TypedMessageBuilder<String> messageBuilder, SenderContext<T> senderContext) throws Exception {
        this.doDecorate(messageBuilder, senderContext);
        return producerSender.send(messageBuilder, senderContext);
    }

    /**
     * 做数据装饰填充
     *
     * @param messageBuilder
     * @param senderContext
     * @param <T>
     */
    protected abstract <T> void doDecorate(TypedMessageBuilder<String> messageBuilder, SenderContext<T> senderContext);
}
