package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.model.vo.AreaTreeVO;
import com.xhqb.spectre.admin.service.AreasInfoService;
import com.xhqb.spectre.admin.util.SimpleCache;
import com.xhqb.spectre.common.dal.entity.AreasInfoDO;
import com.xhqb.spectre.common.dal.mapper.AreasInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/26 17:18
 * @Description:
 */
@Slf4j
@Service
public class AreasInfoServiceImpl implements AreasInfoService {

    @Autowired
    private AreasInfoMapper areasInfoMapper;

    /**
     * 地域树结构的根节点ID
     */
    private static final String ROOT_AREA_ID = "100000";

    /**
     * 本地缓存过期时间30分钟
     */
    private static final long CACHE_EXPIRE_TIME = 30 * 60 * 1000;

    /**
     * 本地缓存key
     */
    private static final String AREA_CACHE_KEY = "areaTree";

    /**
     * 查询地域树结构
     *
     * @return
     */
    @Override
    public AreaTreeVO selectAreaTree() {
        //先从本地缓存读取
        AreaTreeVO areaTreeVO = SimpleCache.get(AREA_CACHE_KEY);
        if (Objects.nonNull(areaTreeVO)) {
            return areaTreeVO;
        }
        List<AreasInfoDO> areaList = areasInfoMapper.selectProvinceCity();
        Map<String, AreasInfoDO> areaMap = areaList.stream().collect(Collectors.toMap(AreasInfoDO::getId, Function.identity()));
        //获取地域父子节点关系
        Map<String, List<String>> parentIdMap = new HashMap<>();
        for (AreasInfoDO item : areaList) {
            String id = item.getId();
            String parentId = item.getParentId();
            if (parentIdMap.containsKey(parentId)) {
                parentIdMap.get(parentId).add(id);
            } else {
                List<String> tmpList = new ArrayList<>();
                tmpList.add(id);
                parentIdMap.put(parentId, tmpList);
            }
        }
        //从根节点开始构建树
        AreaTreeVO rootAreaVO = AreaTreeVO.buildAreaTreeVO(areaMap.get(ROOT_AREA_ID));
        buildAreaTree(rootAreaVO, parentIdMap, areaMap);
        //存入本地缓存
        SimpleCache.put(AREA_CACHE_KEY, rootAreaVO, CACHE_EXPIRE_TIME);

        return rootAreaVO;
    }

    private void buildAreaTree(AreaTreeVO areaTreeVO, Map<String, List<String>> parentIdMap, Map<String, AreasInfoDO> areaMap) {
        List<String> childIdList = parentIdMap.get(areaTreeVO.getId());
        if (CollectionUtils.isEmpty(childIdList)) {
            return;
        }
        for (String areaId : childIdList) {
            AreaTreeVO childItem = AreaTreeVO.buildAreaTreeVO(areaMap.get(areaId));
            buildAreaTree(childItem, parentIdMap, areaMap);
            areaTreeVO.getChildren().add(childItem);
        }
    }
}
