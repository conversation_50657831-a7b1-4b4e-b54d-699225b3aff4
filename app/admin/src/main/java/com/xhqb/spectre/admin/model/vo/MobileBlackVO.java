package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.constant.MobileBlackConstant;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.MobileBlackDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/27 15:08
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MobileBlackVO implements Serializable {

    private static final long serialVersionUID = 5195311102419476757L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 用户CID
     */
    private String cid;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 黑名单来源
     */
    private String source;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 描述，拉黑原因
     */
    private String description;

    /**
     * 是否人工添加
     */
    private Boolean manual;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 过期时间
     */
    private String expiredTime;

    public static MobileBlackVO buildMobileBlackVO(MobileBlackDO item, boolean isMobileMask) {
        String mobile = isMobileMask ? CommonUtil.maskMobile(item.getMobile()) : item.getMobile();
        return MobileBlackVO.builder()
                .id(item.getId())
                .cid(item.getCid())
                .mobile(mobile)
                .source(item.getSource())
                .smsTypeCode(item.getSmsTypeCode())
                .description(item.getDescription())
                .manual(item.getAddType().equals(MobileBlackConstant.ADD_TYPE_MANUAL))
                .createTime(DateUtil.dateToString(item.getCreateTime()))
                .creator(item.getCreator())
                .updateTime(DateUtil.dateToString(item.getUpdateTime()))
                .updater(item.getUpdater())
                .expiredTime(DateUtil.dateToString(item.getExpiredTime()))
                .build();
    }
}
