package com.xhqb.spectre.admin.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

@Data
public class UserShortUrlVO {

    /**
     * cid
     */
    @ExcelProperty(value = "cid")
    @ColumnWidth(20)
    private String cid;

    /**
     * 短链
     */
    @ExcelProperty(value = "短链")
    @ColumnWidth(20)
    private String shortUrl;


    /**
     * 主键id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 短链模版编码
     */
    @ExcelIgnore
    private String tplCode;

    /**
     * 长链
     */
    @ExcelIgnore
    private String longUrl;

    /**
     * 过期时间
     */
    @ExcelIgnore
    private String expiredDate;

    /**
     * 短链hash
     */
    @ExcelIgnore
    private String shortCode;

    /**
     * 批次id
     */
    @ExcelIgnore
    private String batchId;

    /**
     * 手机号
     */
    @ExcelIgnore
    private String mobile;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelIgnore
    private Date updateTime;

    /**
     * 创建人
     */
    @ExcelIgnore
    private String creator;

    /**
     * 更新人
     */
    @ExcelIgnore
    private String updater;

    /**
     * 删除标志
     */
    @ExcelIgnore
    private Integer isDelete;

    /**
     * 状态
     */
    @ExcelIgnore
    private Integer status;

    /**
     * 有效期
     */
    @ExcelIgnore
    private long validPeriod;

    /**
     * 是否过期 0:未过期 1:已过期
     */
    @ExcelIgnore
    private Integer expiredTag;
}
