package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.SmsHisStatisVO;
import com.xhqb.spectre.admin.service.SmsHisStatisService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.SmsHisStatisQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * 短信历史发送量统计表
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
@RestController
@RequestMapping("/smsHisStatis")
@Slf4j
public class SmsHisStatisController {

    @Resource
    private SmsHisStatisService smsHisStatisService;

    /**
     * 查询短信历史发送量统计列表
     *
     * @param smsHisStatisQuery 短信历史发送量统计查询条件
     * @param code              多个以逗号分割
     * @param pageNum           当前页码
     * @param pageSize          一页显示的记录数
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(SmsHisStatisQuery smsHisStatisQuery, String code, Integer pageNum, Integer pageSize) {
        if (StringUtils.isNotBlank(code)) {
            smsHisStatisQuery.setCodeList(Arrays.asList(StringUtils.split(code, ",")));
        }
        smsHisStatisQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<SmsHisStatisVO> commonPager = smsHisStatisService.listByPage(smsHisStatisQuery);
        return AdminResult.success(commonPager);
    }
}
