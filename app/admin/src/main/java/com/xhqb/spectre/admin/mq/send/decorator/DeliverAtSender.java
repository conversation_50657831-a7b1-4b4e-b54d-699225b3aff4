package com.xhqb.spectre.admin.mq.send.decorator;

import com.xhqb.spectre.admin.mq.send.ProducerSender;
import com.xhqb.spectre.admin.mq.send.SenderContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.TypedMessageBuilder;

import java.util.Date;
import java.util.Objects;

/**
 * 定时发送
 * <p>
 * 选择 DELIVER_AT 方式，那么就设置到该时间点再发送 若小于当前时间 则立即发送
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Slf4j
public class DeliverAtSender extends AbstractSenderDecorator {

    public DeliverAtSender(ProducerSender producerSender) {
        super(producerSender);
    }

    /**
     * 做数据装饰填充
     *
     * @param messageBuilder
     * @param senderContext
     * @param <T>
     */
    @Override
    protected <T> void doDecorate(TypedMessageBuilder<String> messageBuilder, SenderContext<T> senderContext) {
        Date deliverDate = senderContext.getDeliverDate();
        if (Objects.isNull(deliverDate)) {
            log.error("设置了消息定时发送,但是deliverDate为空,消息改为立即发送,senderContext = {}", senderContext);
            return;
        }

        long timestamp = deliverDate.getTime();
        // 获取到延迟间隔 毫秒数
        if (timestamp - System.currentTimeMillis() <= 0) {
            log.error("设置了消息定时发送,但是deliverDate已经小于等于当前时间,消息改为立即发送,senderContext = {}", senderContext);
            return;
        }
        // 设置延迟发送
        messageBuilder.deliverAt(timestamp);
    }
}
