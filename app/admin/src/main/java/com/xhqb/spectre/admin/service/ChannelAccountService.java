package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.ChannelAccountDTO;
import com.xhqb.spectre.admin.model.dto.CmppStatusDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ChannelAccountEnumVO;
import com.xhqb.spectre.admin.model.vo.ChannelAccountVO;
import com.xhqb.spectre.admin.model.vo.CmppAliveInfoVO;
import com.xhqb.spectre.common.dal.entity.ChannelAccountDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ChannelAccountQuery;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 11:21
 * @Description:
 */
public interface ChannelAccountService {

    List<ChannelAccountEnumVO> queryEnum(Integer status);

    /**
     * 渠道账号查询列表
     *
     * @param channelAccountQuery
     * @return
     */
    CommonPager<ChannelAccountVO> listByPage(ChannelAccountQuery channelAccountQuery);

    /**
     * 根据ID查询渠道账号详情信息
     *
     * @param id
     * @return
     */
    ChannelAccountVO getById(Integer id);

    /**
     * 保存渠道账号信息
     *
     * @param channelAccountDTO
     */
    void create(ChannelAccountDTO channelAccountDTO);

    /**
     * 更新渠道账号信息
     *
     * @param id
     * @param channelAccountDTO
     */
    void update(Integer id, ChannelAccountDTO channelAccountDTO);

    /**
     * 启用渠道账号信息
     *
     * @param id
     */
    void enable(Integer id);

    /**
     * 停用渠道账号信息
     *
     * @param id
     */
    void disable(Integer id);

    /**
     * 刷新渠道账号查询
     *
     * @param id
     * @param pageSize
     * @return
     */
    List<ChannelAccountDO> refreshCacheQuery(Integer id, Integer pageSize);

    /**
     * 渠道账号写入缓存
     *
     * @param channelAccountDO
     */
    void channelAccountToRedis(ChannelAccountDO channelAccountDO);

    /**
     * 查询Cmpp账号的在线状态
     *
     * @param id
     * @return
     */
    List<CmppAliveInfoVO> queryCmppAliveInfo(Integer id);

    /**
     * cmpp账号上线
     *
     * @param cmppStatusDTO
     * @return
     */
    AdminResult cmppOnline(CmppStatusDTO cmppStatusDTO);

    /**
     * cmpp账号下线
     *
     * @param cmppStatusDTO
     * @return
     */
    AdminResult cmppOffline(CmppStatusDTO cmppStatusDTO);


    /**
     * 初始化流水信息
     *
     * @return
     */
    AdminResult copy2Log();

}
