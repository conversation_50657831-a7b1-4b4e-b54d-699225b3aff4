package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.AddBlackSourceDTO;
import com.xhqb.spectre.admin.model.dto.AddSignDTO;
import com.xhqb.spectre.admin.model.dto.UpdateBlackSourceDTO;
import com.xhqb.spectre.admin.model.dto.UpdateSignDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.BlackSourceVO;
import com.xhqb.spectre.admin.model.vo.SignVO;
import com.xhqb.spectre.admin.service.BlackSourceService;
import com.xhqb.spectre.admin.service.SignService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.BlackSourceQuery;
import com.xhqb.spectre.common.dal.query.SignQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 黑名单来源
 */
@RestController
@RequestMapping("/blackSource")
@Slf4j
public class BlackSourceController {

    @Autowired
    private BlackSourceService blackSourceService;

    /**
     * 查询黑名单来源列表
     *
     * @param blackSourceQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryBlackSourceList(@ModelAttribute BlackSourceQuery blackSourceQuery, Integer pageNum, Integer pageSize) {
        blackSourceQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<BlackSourceVO> commonPager = blackSourceService.listByPage(blackSourceQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询黑名单来源详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryBlackSourceInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(blackSourceService.getById(id));
    }

    /**
     * 添加黑名单来源
     *
     * @param addBlackSourceDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_MOBILE_BLACK)
    public AdminResult createBlackSource(@RequestBody AddBlackSourceDTO addBlackSourceDTO) {
        log.info("addSignDTO: {}", JSON.toJSONString(addBlackSourceDTO));
        blackSourceService.create(addBlackSourceDTO);
        return AdminResult.success();
    }

    /**
     * 修改黑名单来源
     *
     * @param id
     * @param updateBlackSourceDTO
     * @return
     */
    @PutMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_MOBILE_BLACK)
    public AdminResult updateBlackSource(@PathVariable("id") Integer id, @RequestBody UpdateBlackSourceDTO updateBlackSourceDTO) {
        updateBlackSourceDTO.setId(id);
        log.info("updateBlackSourceDTO: {}", JSON.toJSONString(updateBlackSourceDTO));
        blackSourceService.update(updateBlackSourceDTO);
        return AdminResult.success();
    }

    /**
     * 启用黑名单来源
     *
     * @param id
     * @return
     */
    @PostMapping("/enable/{id}")
    @LogOpTime(OpLogConstant.MODULE_MOBILE_BLACK)
    public AdminResult enableSign(@PathVariable("id") Integer id) {
        log.info("enableBlackSource, id: {}", id);
        blackSourceService.enable(id);
        return AdminResult.success();
    }

    /**
     * 停用黑名单来源
     *
     * @param id
     * @return
     */
    @PostMapping("/disable/{id}")
    @LogOpTime(OpLogConstant.MODULE_MOBILE_BLACK)
    public AdminResult disableSign(@PathVariable("id") Integer id) {
        log.info("disableBlackSource, id: {}", id);
        blackSourceService.disable(id);
        return AdminResult.success();
    }

    /**
     * 删除黑名单来源
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_MOBILE_BLACK)
    public AdminResult deleteSign(@PathVariable("id") Integer id) {
        log.info("deleteBlackSource, id: {}", id);
        blackSourceService.delete(id);
        return AdminResult.success();
    }

    /**
     * 查询枚举-黑名单来源
     *
     * @param status
     * @return
     */
    @GetMapping("/enum")
    public AdminResult queryEnum(Integer status) {
        return AdminResult.success(blackSourceService.queryEnum(status));
    }
}
