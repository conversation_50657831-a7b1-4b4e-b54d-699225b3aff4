package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.SmsTypeDisableDTO;
import com.xhqb.spectre.admin.model.vo.SmsTypeDisableVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.SmsTypeDisableQuery;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2022/2/17 14:30
 * @Description:
 */
public interface SmsTypeDisableService {

    CommonPager<SmsTypeDisableVO> listByPage(SmsTypeDisableQuery query);

    SmsTypeDisableVO getById(Integer id);

    void create(SmsTypeDisableDTO dto);

    void update(SmsTypeDisableDTO dto);

    void batchDelete(List<Integer> idList);
}
