package com.xhqb.spectre.admin.model.dto;

import com.xhqb.spectre.admin.constant.CommonConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/18 11:42
 * @Description:
 */
@Data
public class AddSignDTO implements Serializable {

    private static final long serialVersionUID = -4828278508301843544L;

    @NotBlank(message = "签名名称不能为空")
    @Size(message = "签名名称最大为16个字符") //去掉前后的【】，最大为8个
    @Pattern(regexp = "^【[\\w|\u4e00-\u9fa5]{1,16}】$", message = "签名名称的格式有误")
    private String name;

    @NotBlank(message = "签名编码不能为空")
    @Size(max = 32, message = "签名编码最大为{max}个字符")
    @Pattern(regexp = "^(?![_|0-9])(?!.*?_$)[A-Za-z0-9_]+$", message = "签名编码格式有误")
    private String code;

    @NotBlank(message = "签名描述不能为空")
    @Size(max = 256, message = "签名描述最大为{max}个字符")
    private String description;
}
