package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.*;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.*;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.entity.TplOpRecordDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.TplChangeRecordsQuery;
import com.xhqb.spectre.common.dal.query.TplQuery;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/15 17:29
 * @Description:
 */
public interface TplService {

    CommonPager<TplVO> listByPage(TplQuery tplQuery);

    void create(AddTplDTO addTplDTO);

    void update(UpdateTplDTO updateTplDTO);

    TplVO getById(Integer id);

    void enable(Integer id);

    void disable(Integer id);

    void delete(Integer id);

    List<TplEnumVO> queryEnum(String code, String title, Integer status, Integer signId);

    List<String> test(Integer id, TplTestDTO tplTestDTO);

    TplVO queryTpl(String code, String signName);

    List<TplInfoVO> queryByAppCode(String appCode);

    List<TplExcelVO> downLoadActive(TplQuery tplQuery);

    AdminResult updateByTplIdList(BatchTplChannelDTO batchTplChannelDTO);

    String batchReport(BatchReportDTO batchReportDTO);

    CommonPager<TplOpRecordDO> queryChangeRecords(TplChangeRecordsQuery query);

    TplOpUpdateRecordVO queryUpdateRecords(TplChangeRecordsQuery query);


    List<TplOperationVO> exportTpl(ExportTplDTO exportTplDTO);

    Object importTplByJson(List<TplOperationVO> tplOperationVOList);

    Object deleteTplAvailableChannel();

    void setCreatorByReportId(TplDO tplDO);

    /**
     * 批量更新模版渠道权重
     */
    void batchUpdateTplChannelWeights(Integer tplId, List<TplChannelWeightDTO> tplChannelWeightDTOList);
}
