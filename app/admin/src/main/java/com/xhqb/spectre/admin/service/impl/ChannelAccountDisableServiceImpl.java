package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.ChannelAccountDisableDTO;
import com.xhqb.spectre.admin.model.vo.ChannelAccountDisableVO;
import com.xhqb.spectre.admin.service.ChannelAccountDisableService;
import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.constant.DisableTypeConstant;
import com.xhqb.spectre.common.dal.entity.ChannelAccountDO;
import com.xhqb.spectre.common.dal.entity.ChannelAccountDisableDO;
import com.xhqb.spectre.common.dal.mapper.ChannelAccountDisableMapper;
import com.xhqb.spectre.common.dal.mapper.ChannelAccountMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ChannelAccountDisableQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/22 16:15
 * @Description:
 */
@Slf4j
@Service
public class ChannelAccountDisableServiceImpl implements ChannelAccountDisableService {

    @Autowired
    private ChannelAccountDisableMapper accountDisableMapper;

    @Autowired
    private ChannelAccountMapper channelAccountMapper;

    /**
     * 列表查询
     *
     * @param accountDisableQuery
     * @return
     */
    @Override
    public CommonPager<ChannelAccountDisableVO> listByPage(ChannelAccountDisableQuery accountDisableQuery) {
        return PageResultUtils.result(
                () -> accountDisableMapper.countByQuery(accountDisableQuery),
                () -> accountDisableMapper.selectByQuery(accountDisableQuery).stream()
                        .map(ChannelAccountDisableVO::buildAccountDisableVO).collect(Collectors.toList())
        );
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @Override
    public ChannelAccountDisableVO getById(Integer id) {
        ChannelAccountDisableDO accountDisableDO = validateAndSelectById(id);
        return ChannelAccountDisableVO.buildAccountDisableVO(accountDisableDO);
    }

    /**
     * 添加屏蔽信息
     *
     * @param accountDisableDTO
     */
    @Override
    public void create(ChannelAccountDisableDTO accountDisableDTO) {
        //校验
        checkAddParam(accountDisableDTO);

        //写入屏蔽信息
        ChannelAccountDisableDO accountDisableDO = buildAccountDisableDO(accountDisableDTO, true);
        accountDisableMapper.insertSelective(accountDisableDO);
    }

    /**
     * 更新屏蔽信息
     *
     * @param accountDisableDTO
     */
    @Override
    public void update(ChannelAccountDisableDTO accountDisableDTO) {
        //校验
        checkUpdateParam(accountDisableDTO);

        //写入屏蔽信息
        ChannelAccountDisableDO accountDisableDO = buildAccountDisableDO(accountDisableDTO, false);
        accountDisableMapper.updateByPrimaryKeySelective(accountDisableDO);
    }

    /**
     * 删除屏蔽信息
     *
     * @param id
     */
    @Override
    public void delete(Integer id) {
        validateAndSelectById(id);
        //删除屏蔽信息
        accountDisableMapper.delete(id, SsoUserInfoUtil.getUserName());
    }

    private ChannelAccountDisableDO validateAndSelectById(Integer id) {
        ChannelAccountDisableDO accountDisableDO = accountDisableMapper.selectByPrimaryKey(id);
        if (Objects.isNull(accountDisableDO)) {
            throw new BizException("未找到该屏蔽信息");
        }
        return accountDisableDO;
    }

    private void checkCommonParam(ChannelAccountDisableDTO accountDisableDTO) {
        //参数格式校验
        ValidatorUtil.validate(accountDisableDTO);
        //校验生效时间
        Date start = DateUtil.stringToDate(accountDisableDTO.getStartTime());
        Date end = DateUtil.stringToDate(accountDisableDTO.getEndTime());
        assert end != null;
        if (end.before(new Date())) {
            throw new BizException("结束时间不能小于当前时间");
        }
        if (end.before(start)) {
            throw new BizException("开始时间不能大于结束时间");
        }
        //地域校验
        accountDisableDTO.getAreaList().forEach(ValidatorUtil::validate);

        //校验屏蔽类型和时间段参数
        Integer disableType = accountDisableDTO.getDisableType();
        if (disableType != null && disableType.equals(DisableTypeConstant.TYPE_PERIOD_TIME)) {
            // 校验时间段参数
            String periodStartTime = accountDisableDTO.getPeriodStartTime();
            String periodEndTime = accountDisableDTO.getPeriodEndTime();
            if (periodStartTime == null || periodStartTime.trim().isEmpty()) {
                throw new BizException("屏蔽类型为2时，时间段开始时间不能为空");
            }
            if (periodEndTime == null || periodEndTime.trim().isEmpty()) {
                throw new BizException("屏蔽类型为2时，时间段结束时间不能为空");
            }
            // 校验时间格式 HH:mm
            if (!periodStartTime.matches("^([01]?[0-9]|2[0-3]):[0-5][0-9]$")) {
                throw new BizException("时间段开始时间格式错误，应为HH:mm格式");
            }
            if (!periodEndTime.matches("^([01]?[0-9]|2[0-3]):[0-5][0-9]$")) {
                throw new BizException("时间段结束时间格式错误，应为HH:mm格式");
            }
            // 校验时间段逻辑
            String[] startParts = periodStartTime.split(":");
            String[] endParts = periodEndTime.split(":");
            int startMinutes = Integer.parseInt(startParts[0]) * 60 + Integer.parseInt(startParts[1]);
            int endMinutes = Integer.parseInt(endParts[0]) * 60 + Integer.parseInt(endParts[1]);
            if (startMinutes >= endMinutes) {
                throw new BizException("时间段开始时间不能大于或等于结束时间");
            }
        }

        //校验备注字段长度
        String remark = accountDisableDTO.getRemark();
        if (remark != null && remark.length() > 500) {
            throw new BizException("备注信息长度不能超过500个字符");
        }
    }

    public void checkAddParam(ChannelAccountDisableDTO accountDisableDTO) {
        checkCommonParam(accountDisableDTO);
        //校验渠道账号ID
        Integer channelAccountId = accountDisableDTO.getChannelAccountId();
        if (Objects.isNull(channelAccountId)) {
            throw new BizException("渠道账号ID不能为空");
        }
        ChannelAccountDO channelAccountDO = channelAccountMapper.selectByPrimaryKey(channelAccountId);
        if (Objects.isNull(channelAccountDO)) {
            throw new BizException("渠道账号ID有误，该账号不存在");
        }
        if (!channelAccountDO.getStatus().equals(CommonConstant.STATUS_VALID)) {
            throw new BizException("渠道账号未启用，不能配置屏蔽信息");
        }
    }

    private ChannelAccountDisableDO checkUpdateParam(ChannelAccountDisableDTO accountDisableDTO) {
        checkCommonParam(accountDisableDTO);
        //校验存在性
        ChannelAccountDisableDO accountDisableDO = validateAndSelectById(accountDisableDTO.getId());
        //校验渠道账号ID
        Integer channelAccountId = accountDisableDO.getChannelAccountId();
        ChannelAccountDO channelAccountDO = channelAccountMapper.selectByPrimaryKey(channelAccountId);
        if (Objects.isNull(channelAccountDO)) {
            throw new BizException("渠道账号ID有误，该账号不存在");
        }
        if (!channelAccountDO.getStatus().equals(CommonConstant.STATUS_VALID)) {
            throw new BizException("渠道账号未启用，不能配置屏蔽信息");
        }
        return accountDisableDO;
    }

    public ChannelAccountDisableDO buildAccountDisableDO(ChannelAccountDisableDTO item, boolean isAdd) {
        String userName = SsoUserInfoUtil.getUserName();
        ChannelAccountDisableDO accountDisableDO = ChannelAccountDisableDO.builder()
                .isps(String.join(",", item.getIspList()))
                .areas(JSON.toJSONString(item.getAreaList()))
                .maskStartTime(cn.hutool.core.date.DateUtil.parseDateTime(item.getStartTime()).getTime())
                .maskEndTime(cn.hutool.core.date.DateUtil.parseDateTime(item.getEndTime()).getTime())
                .disableType(item.getDisableType() != null ? item.getDisableType() : DisableTypeConstant.DEFAULT_TYPE)
                .periodStartTime(item.getPeriodStartTime())
                .periodEndTime(item.getPeriodEndTime())
                .remark(item.getRemark())
                .updater(userName)
                .build();
        if (isAdd) {
            accountDisableDO.setChannelAccountId(item.getChannelAccountId());
            accountDisableDO.setCreator(userName);
        } else {
            accountDisableDO.setId(item.getId());
        }
        return accountDisableDO;
    }
}
