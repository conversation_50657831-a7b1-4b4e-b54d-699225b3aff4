package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.BusinessLineDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 业务线
 *
 * <AUTHOR>
 * @date 2022/9/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessLineVO implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 状态，0：无效，1：有效
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;


    public static BusinessLineVO buildBusinessLineVO(BusinessLineDO businessLineDO) {
        if (Objects.isNull(businessLineDO)) {
            return null;
        }
        return BusinessLineVO.builder()
                .id(businessLineDO.getId())
                .name(businessLineDO.getName())
                .status(businessLineDO.getStatus())
                .createTime(businessLineDO.getCreateTime())
                .creator(businessLineDO.getCreator())
                .updateTime(businessLineDO.getUpdateTime())
                .updater(businessLineDO.getUpdater())
                .build();
    }

}
