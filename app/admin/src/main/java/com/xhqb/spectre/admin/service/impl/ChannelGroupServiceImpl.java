package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.constant.CommonConstant;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.ChannelGroupDTO;
import com.xhqb.spectre.admin.model.dto.ChannelGroupItemDTO;
import com.xhqb.spectre.admin.model.vo.ChannelGroupItemVO;
import com.xhqb.spectre.admin.model.vo.ChannelGroupVO;
import com.xhqb.spectre.admin.service.ChannelGroupService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.ChannelAccountDO;
import com.xhqb.spectre.common.dal.entity.ChannelGroupDO;
import com.xhqb.spectre.common.dal.entity.ChannelGroupItemDO;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.mapper.ChannelAccountMapper;
import com.xhqb.spectre.common.dal.mapper.ChannelGroupItemMapper;
import com.xhqb.spectre.common.dal.mapper.ChannelGroupMapper;
import com.xhqb.spectre.common.dal.mapper.SignMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ChannelGroupQuery;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2022/4/11 10:46
 * @Description:
 */
@Service
@Slf4j
public class ChannelGroupServiceImpl implements ChannelGroupService {

    @Autowired
    private ChannelGroupMapper channelGroupMapper;

    @Autowired
    private ChannelGroupItemMapper channelGroupItemMapper;

    @Autowired
    private ChannelAccountMapper channelAccountMapper;

    @Autowired
    private SignMapper signMapper;

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    @Override
    public CommonPager<ChannelGroupVO> listByPage(ChannelGroupQuery query) {
        return PageResultUtils.result(
                () -> channelGroupMapper.countByQuery(query),
                () -> channelGroupMapper.selectByQuery(query).stream().map(ChannelGroupVO::build).collect(Collectors.toList())
        );
    }

    /**
     * 查询渠道组详情
     *
     * @param id
     * @return
     */
    @Override
    public ChannelGroupVO getById(Integer id) {
        ChannelGroupDO groupDO = validateAndSelectById(id);
        List<ChannelGroupItemDO> itemDOList = channelGroupItemMapper.selectByGroupId(groupDO.getId());
        if (CollectionUtils.isEmpty(itemDOList)) {
            return ChannelGroupVO.build(groupDO, Collections.emptyList());
        }
        return ChannelGroupVO.build(groupDO, buildGroupItemVOList(itemDOList, getChannelAccountNameMap()));
    }

    /**
     * 创建
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void create(ChannelGroupDTO dto) {
        dto.setId(null);
        //校验
        checkParam(dto);

        //写入渠道组信息
        ChannelGroupDO groupDO = buildDO(dto);
        channelGroupMapper.insertSelective(groupDO);

        //写入渠道配置详情
        dto.getChannelInfoList().forEach(item -> channelGroupItemMapper.insertSelective(buildDO(item, groupDO.getId())));
    }

    /**
     * 更新
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(ChannelGroupDTO dto) {
        //校验
        checkParam(dto);
        Integer groupId = dto.getId();
        validateAndSelectById(groupId);

        //更新渠道组信息
        ChannelGroupDO groupDO = buildDO(dto);
        channelGroupMapper.updateByPrimaryKeySelective(groupDO);

        //更新渠道配置详情
        channelGroupItemMapper.deleteByGroupId(groupId);
        dto.getChannelInfoList().forEach(item -> channelGroupItemMapper.insertSelective(buildDO(item, groupId)));
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void delete(Integer id) {
        //校验
        validateAndSelectById(id);

        //删除渠道组
        String currentUser = SsoUserInfoUtil.getUserName();
        channelGroupMapper.delete(id, currentUser);

        //删除渠道配置详情
        channelGroupItemMapper.deleteByGroupId(id);
    }

    /**
     * 批量删除
     *
     * @param idList
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void batchDelete(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        String currentUser = SsoUserInfoUtil.getUserName();
        idList.forEach(id -> {
            //删除渠道组
            channelGroupMapper.delete(id, currentUser);

            //删除渠道配置详情
            channelGroupItemMapper.deleteByGroupId(id);
        });
    }

    /**
     * 根据短信类型获取列表
     *
     * @param smsTypeCode
     * @param signId
     * @return
     */
    @Override
    public List<ChannelGroupVO> getListBySmsType(String smsTypeCode, Integer signId) {
        if (StringUtils.isEmpty(smsTypeCode) || !MessageTypeEnum.contains(smsTypeCode) || Objects.isNull(signId)) {
            return Collections.emptyList();
        }
        //获取渠道组列表
        ChannelGroupQuery query = new ChannelGroupQuery();
        query.setSmsTypeCode(smsTypeCode);
        query.setSignId(signId);
        List<ChannelGroupDO> doList = channelGroupMapper.selectByQuery(query);
        if (CollectionUtils.isEmpty(doList)) {
            return Collections.emptyList();
        }
        //获取渠道详情列表
        List<Integer> idList = doList.stream().map(ChannelGroupDO::getId).collect(Collectors.toList());
        List<ChannelGroupItemDO> itemDOList = channelGroupItemMapper.selectByGroupIdList(idList);
        Map<Integer, List<ChannelGroupItemDO>> groupMap = itemDOList.stream().collect(Collectors.groupingBy(ChannelGroupItemDO::getGroupId));
        Map<Integer, String> channelAccountNameMap = getChannelAccountNameMap();

        return doList.stream().map(item -> ChannelGroupVO.build(item, buildGroupItemVOList(groupMap.get(item.getId()), channelAccountNameMap))).collect(Collectors.toList());
    }

    private ChannelGroupDO validateAndSelectById(Integer id) {
        ChannelGroupDO channelGroupDO = channelGroupMapper.selectByPrimaryKey(id);
        if (Objects.isNull(channelGroupDO)) {
            throw new BizException("未找到该渠道组记录");
        }
        return channelGroupDO;
    }

    private List<ChannelGroupItemVO> buildGroupItemVOList(List<ChannelGroupItemDO> itemDOList, Map<Integer, String> channelAccountNameMap) {
        if (CollectionUtils.isEmpty(itemDOList)) {
            return Collections.emptyList();
        }
        return itemDOList.stream()
                .map(item -> ChannelGroupItemVO.build(item, channelAccountNameMap.get(item.getChannelAccountId())))
                .collect(Collectors.toList());
    }

    private Map<Integer, String> getChannelAccountNameMap() {
        return channelAccountMapper.selectAll().stream().collect(Collectors.toMap(ChannelAccountDO::getId, ChannelAccountDO::getName));
    }

    /**
     * 参数校验
     *
     * @param dto
     */
    private void checkParam(ChannelGroupDTO dto) {
        //参数格式校验
        ValidatorUtil.validate(dto);

        //短信类型校验
        String smsTypeCode = dto.getSmsTypeCode();
        if (!MessageTypeEnum.contains(smsTypeCode)) {
            throw new BizException("不支持该短信类型编码");
        }
        //签名ID校验
        SignDO signDO = signMapper.selectByPrimaryKey(dto.getSignId());
        if (Objects.isNull(signDO) || !signDO.getStatus().equals(CommonConstant.STATUS_VALID)) {
            throw new BizException("签名ID有误，该签名不存在或已停用");
        }
        //校验渠道信息
        List<ChannelGroupItemDTO> channelList = dto.getChannelInfoList();
        Map<Integer, ChannelAccountDO> accountMap = channelAccountMapper.selectAll().stream().collect(Collectors.toMap(ChannelAccountDO::getId, Function.identity()));
        channelList.forEach(channelInfo -> {
            //校验字段格式
            ValidatorUtil.validate(channelInfo);
            //校验渠道账号
            ChannelAccountDO accountDO = accountMap.get(channelInfo.getChannelAccountId());
            if (Objects.isNull(accountDO)) {
                throw new BizException("未找到渠道账号，账号ID：" + channelInfo.getChannelAccountId());
            }
            if (accountDO.getStatus().equals(CommonConstant.STATUS_INVALID)) {
                throw new BizException("渠道账号已停用，账号：" + accountDO.getName());
            }
            if (!smsTypeCode.equals(accountDO.getSmsTypeCode())) {
                throw new BizException("渠道账号的短信类型与渠道组的短信类型不一致，账号：" + accountDO.getName());
            }
        });
    }

    private ChannelGroupDO buildDO(ChannelGroupDTO dto) {
        Integer id = dto.getId();
        boolean isAdd = Objects.isNull(id);
        String userName = SsoUserInfoUtil.getUserName();
        ChannelGroupDO groupDO = ChannelGroupDO.builder()
                .smsTypeCode(dto.getSmsTypeCode())
                .signId(dto.getSignId())
                .name(dto.getName())
                .description(dto.getDescription())
                .updater(userName)
                .build();
        if (isAdd) {
            groupDO.setCreator(userName);
        } else {
            groupDO.setId(id);
        }
        return groupDO;
    }

    private ChannelGroupItemDO buildDO(ChannelGroupItemDTO dto, Integer groupId) {
        return ChannelGroupItemDO.builder()
                .groupId(groupId)
                .channelAccountId(dto.getChannelAccountId())
                .isps(String.join(",", dto.getIspList()))
                .areaFilterType(dto.getAreaFilterType())
                .areas(JSON.toJSONString(dto.getAreaList()))
                .weight(dto.getWeight())
                .remark(dto.getRemark())
                .build();
    }
}
