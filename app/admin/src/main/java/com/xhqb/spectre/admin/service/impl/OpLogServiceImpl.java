package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.service.OpLogService;
import com.xhqb.spectre.common.dal.entity.OpLogDO;
import com.xhqb.spectre.common.dal.mapper.OpLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/16 15:21
 * @Description:
 */
@Slf4j
@Service
public class OpLogServiceImpl implements OpLogService {

    @Autowired
    private OpLogMapper opLogMapper;

    @Override
    @Async
    public void writeLog(OpLogDO opLogDO) {
        opLogMapper.insertSelective(opLogDO);
    }

}
