package com.xhqb.spectre.admin.job;

import cn.hutool.core.collection.CollectionUtil;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.google.common.collect.Lists;
import com.xhqb.kael.boot.autoconfigure.elasticjob.annotation.Job;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.model.vo.EffectiveNotifyTpl;
import com.xhqb.spectre.admin.service.FeiShuAlert;
import com.xhqb.spectre.admin.service.impl.TplServiceImpl;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.entity.support.TplChannelDOSupport;
import com.xhqb.spectre.common.dal.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 可用渠道不足通知
 */
@Component
@Job("availableChannelNotifyJob")
@Slf4j
public class AvailableChannelNotifyJob implements SimpleJob {

    private static final String TEMPLATE_NOTIFICATION_HISTORY = "sms:template:history:";
    private static final String LAST_NOTIFICATION_DATE = "sms:last_notification_date";
    private static final int MAX_CONSECUTIVE_DAYS = 3;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private TplMapper tplMapper;
    @Resource
    private TplChannelMapper tplChannelMapper;
    @Resource
    private VenusConfig venusConfig;
    @Resource
    private SignMapper signMapper;
    @Resource
    private FeiShuAlert feiShuAlert;
    @Resource
    private TplServiceImpl tplService;

    @Override
    public void execute(ShardingContext shardingContext) {
        try {
            dailyBatchNotification();
        } catch (Exception e) {
            log.error("可用渠道不足通知失败", e);
        }
    }

    private void dailyBatchNotification() {

        // 检查是否是今天第一次执行
        if (!isFirstExecutionToday()) {
            return;
        }

        // 获取所有符合条件的短信模板
        List<EffectiveNotifyTpl> effectiveNotifyTpl = effectiveNotify();
        if (CollectionUtil.isEmpty(effectiveNotifyTpl)) {
            return;
        }

        // 过滤出可以发送通知的模板
        List<EffectiveNotifyTpl> templatesToNotify = effectiveNotifyTpl.stream()
                .filter(template -> canSendNotification(template.getCode()))
                .collect(Collectors.toList());

        // 批量发送通知
        batchSendNotifications(templatesToNotify);

        // 更新通知历史记录
        updateNotificationHistory(templatesToNotify);

        // 更新最后执行日期
        updateLastExecutionDate();
    }


    private List<EffectiveNotifyTpl> effectiveNotify() {

        Map<Integer, String> signMap = signMapper.selectAll().stream()
                .collect(Collectors.toMap(SignDO::getId, SignDO::getName, (existing, replacement) -> existing));

        List<EffectiveNotifyTpl> resultList = new ArrayList<>();
        String reachRateLowNotifySmsType = venusConfig.getReachRateLowNotifySmsType();
        if (StringUtils.isEmpty(reachRateLowNotifySmsType)) {
            return resultList;
        }
        Set<String> smsTypeSet = Arrays.stream(reachRateLowNotifySmsType.split(","))
                .collect(Collectors.toSet());
        List<TplDO> tplDOS = tplMapper.selectAll().stream()
                .filter(tplDO -> smsTypeSet.contains(tplDO.getSmsTypeCode()))
                .collect(Collectors.toList());
        for (TplDO tplDO : tplDOS) {
            List<TplChannelDOSupport> channelDOList = tplChannelMapper.selectSupportByTplId(tplDO.getId());
            int size = CollectionUtil.isEmpty(channelDOList) ? 0 : channelDOList.size();
            if (size <= 1) {
                tplService.setCreatorByReportId(tplDO);
                EffectiveNotifyTpl effectiveNotifyTpl = new EffectiveNotifyTpl();
                effectiveNotifyTpl.setId(tplDO.getId());
                effectiveNotifyTpl.setCode(tplDO.getCode());
                effectiveNotifyTpl.setContent(signMap.getOrDefault(tplDO.getSignId(), "") + tplDO.getContent());
                effectiveNotifyTpl.setCreator(tplDO.getCreator());
                effectiveNotifyTpl.setAvailableChannelCount(size);
                resultList.add(effectiveNotifyTpl);
            }
        }
        return resultList;
    }


    private boolean canSendNotification(String tplCode) {
        String key = TEMPLATE_NOTIFICATION_HISTORY + tplCode;
        List<Object> history = redisTemplate.opsForList().range(key, 0, -1);
        if (history == null) {
            history = new ArrayList<>();
        }

        // 如果历史记录不足3天，可以发送
        if (history.size() < MAX_CONSECUTIVE_DAYS - 1) {
            return true;
        }

        // 检查最近三天是否都通知了
        return history.stream()
                .map(date -> LocalDate.parse((String) date))
                .noneMatch(date -> date.isAfter(LocalDate.now().minusDays(MAX_CONSECUTIVE_DAYS)));
    }

    private void batchSendNotifications(List<EffectiveNotifyTpl> templatesToNotify) {

        List<List<EffectiveNotifyTpl>> batchSendList = Lists.partition(templatesToNotify, venusConfig.getAvailableChannelNotifyBatchSendCount());
        for (List<EffectiveNotifyTpl> indexBatchList : batchSendList) {
            List<JSONObject> dataList = new ArrayList<>();
            indexBatchList.forEach(template -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("code", template.getCode());
                jsonObject.put("content", CommonUtil.escapeJsonAndRemoveNewlines(template.getContent()));
                jsonObject.put("creator", Objects.equals(template.getCreator(), "SYS") ? venusConfig.getCreatorTplNotifyUser() : template.getCreator());
                jsonObject.put("availableChannelCount", template.getAvailableChannelCount());
                dataList.add(jsonObject);
            });
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("dataList", dataList);
            dataMap.put("atUserList", CollectionUtil.newArrayList(venusConfig.getCreatorTplNotifyUser()));
            feiShuAlert.sendFeiShuAlert(dataMap, venusConfig.getEffectiveTplNotifyStrategyId());
        }


    }

    private void updateNotificationHistory(List<EffectiveNotifyTpl> templates) {
        String today = LocalDate.now().toString();
        templates.forEach(template -> {
            String key = TEMPLATE_NOTIFICATION_HISTORY + template.getCode();
            // 获取当前列表内容
            List<Object> history = redisTemplate.opsForList().range(key, 0, -1);
            if (history == null || !history.contains(today)) {
                redisTemplate.opsForList().leftPush(key, today);
                redisTemplate.opsForList().trim(key, 0, MAX_CONSECUTIVE_DAYS - 1);
            }
            redisTemplate.expire(key, 7, TimeUnit.DAYS);
        });
    }

    private boolean isFirstExecutionToday() {
        String lastDateStr = (String) redisTemplate.opsForValue().get(LAST_NOTIFICATION_DATE);
        if (lastDateStr == null) {
            return true;
        }
        if(venusConfig.getAvailableChannelNotifyEnable()){
            return true;
        }

        LocalDate lastDate = LocalDate.parse(lastDateStr);
        return !lastDate.equals(LocalDate.now());
    }

    private void updateLastExecutionDate() {
        redisTemplate.opsForValue().set(LAST_NOTIFICATION_DATE, LocalDate.now().toString());
        DateTime endOfDay = DateUtil.endOfDay(DateUtil.date());
        long expireSeconds =  endOfDay.getTime() - System.currentTimeMillis();
        redisTemplate.expire(LAST_NOTIFICATION_DATE, expireSeconds, TimeUnit.MILLISECONDS);

    }
}
