package com.xhqb.spectre.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.github.wujun234.uid.utils.DateUtils;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.bidata.entity.SendStatDO;
import com.xhqb.spectre.admin.bidata.mapper.BidataSendStatMapper;
import com.xhqb.spectre.admin.bidata.model.ReachRateDTO;
import com.xhqb.spectre.admin.bidata.model.SendFormDO;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.model.vo.MultipleCompareDataVO;
import com.xhqb.spectre.admin.model.vo.MultipleCompareVO;
import com.xhqb.spectre.admin.model.vo.SendDetailVO;
import com.xhqb.spectre.admin.service.BidataSendStatService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.QueryUtils;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.MultipleCompareQuery;
import com.xhqb.spectre.common.dal.query.ReachRateQuery;
import com.xhqb.spectre.common.dal.query.SendQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * Bidata 发送统计服务实现
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class BidataSendStatServiceImpl implements BidataSendStatService {


    @Resource
    private BidataSendStatMapper bidataSendStatMapper;

    @Resource
    private VenusConfig venusConfig;


    /**
     * 发送top10统计信息
     *
     * @param query 查询条件
     * @return 返回Map
     */
    @Override
    public Map<String, List<SendFormDO>> sendTopTen(SendQuery query) {

        String groupByInfoByType = CommonUtil.getGroupByInfoByType(query.getSendCountType(), venusConfig.getBiDataSendStatTopTenParams());
        query.setGroupByInfo(groupByInfoByType);
        if (isIspCodeAll(query.getIspCode())) {
            query.setIspCode(venusConfig.getIspCodeAllConfig());
        }

        QueryUtils.setQuery(query);
        List<SendFormDO> topTenList = bidataSendStatMapper.selectBySendTopTenQuery(query);

        if (CollectionUtil.isEmpty(topTenList)) {
            return Collections.emptyMap();
        }

        // 对topTenList进行排序并取前10条
        List<SendFormDO> sortedTopTenList = topTenList.stream()
                .sorted((o1, o2) -> Long.compare(o2.getSendCount(), o1.getSendCount())) // 降序排序
                .limit(10) // 取前10条
                .collect(Collectors.toList());

        Map<String, List<SendFormDO>> sortedMap = new LinkedHashMap<>();
        for (SendFormDO sendFormDO : sortedTopTenList) {
            sortedMap.put(sendFormDO.getCode(), Collections.singletonList(sendFormDO));
        }

        if (venusConfig.isLogEnable()) {
            log.info("[BidataSendStatServiceImpl] sendTopTen 排序结果：{}", JsonLogUtil.toJSONString(sortedMap));
        }

        if (!Objects.equals(groupByInfoByType, "tpl_code")) {
            return sortedMap;
        }

        Map<String, List<SendFormDO>> reusltMap = new LinkedHashMap<>();

        for (Map.Entry<String, List<SendFormDO>> entry : sortedMap.entrySet()) {
            String tplCode = entry.getKey();
            log.info("[BidataSendStatServiceImpl] tplCode ：{}", tplCode);
            query.setTplCode(tplCode);
            Map<String, SendFormDO> tempMap = bidataSendStatMapper.selectSendTopTenByTplCode(query)
                    .stream().collect(Collectors.toMap(SendFormDO::getCode, Function.identity(), (v1, v2) -> v1));
            reusltMap.put(tplCode, new ArrayList<>(tempMap.values()));
        }
        return reusltMap;
    }


    /**
     * 计算触达率
     *
     * @param query 查询条件
     * @return 返回触达率数据
     */
    @Override
    public Map<String, List<Object>> reachRate(ReachRateQuery query) {
        if (isIspCodeAll(query.getIspCode())) {
            query.setIspCode(venusConfig.getIspCodeAllConfig());
        }
        String groupByInfoByType = CommonUtil.getGroupByInfoByType(query.getReachRateType(), venusConfig.getBiDataSendStatReachRateParams());
        query.setGroupByInfo(groupByInfoByType);

        QueryUtils.setQuery(query);
        List<ReachRateDTO> rateDTOList = bidataSendStatMapper.selectByReachRateQuery(query);

        if (CollectionUtil.isEmpty(rateDTOList)) {
            return Collections.emptyMap();
        }


        return processQueryResults(rateDTOList);
    }

    @Override
    public Map<String, List<Object>> overview() {
        Map<String, List<Object>> resultMap = new HashMap<>();
        Date curDate = new Date();
        if (venusConfig.getMockEnable()) {
            curDate = DateUtil.parseDate(venusConfig.getMockDate());
        }
        resultMap.put("lastMonth", getResultListForDateRange(DateUtil.offsetMonth(curDate, -1), "lastMonth"));
        resultMap.put("weeks", getResultListForDateRange(DateUtils.addDays(curDate, -1), "weeks"));
        resultMap.put("yesterday", getResultListForDateRange(DateUtils.addDays(curDate, -1), "yesterday"));
        return resultMap;
    }

    @Override
    public CommonPager<SendDetailVO> sendDetail(SendQuery query) {

        if (isIspCodeAll(query.getIspCode())) {
            query.setIspCode(venusConfig.getIspCodeAllConfig());
        }
        if (Strings.isNotBlank(query.getTplCode())) {
            query.setTplCodes(Arrays.asList(query.getTplCode().split(",")));
        }
        if (Strings.isNotBlank(query.getChannelCode())) {
            query.setChannelCodes(Arrays.asList(query.getChannelCode().split(",")));
        }

        QueryUtils.setQuery(query);
        List<SendStatDO> modelList = bidataSendStatMapper.selectSendDetailByQuery(query);

        List<SendDetailVO> voList = modelList.stream().map(this::convertDOToDetailVo).collect(Collectors.toList());

        int count = bidataSendStatMapper.countSendDetailByQuery(query);
        return PageResultUtils.result(() -> count, () -> voList);
    }

    @Override
    public MultipleCompareVO multipleCompare(MultipleCompareQuery query) {

        if (isIspCodeAll(query.getIspCode())) {
            query.setIspCode(venusConfig.getIspCodeAllConfig());
        }
        // 预处理模板和渠道代码
        prepareQueryCodes(query);

        log.info("[BidataSendStatServiceImpl] multipleCompare 开始查询...");
        MultipleCompareVO multipleCompareVO = initBulidMultipleCompareVO(query);

        try {
            QueryUtils.setQuery(query);
            List<SendStatDO> modelList = bidataSendStatMapper.selectByMultipleCompareQuery(query);
            if (CollectionUtil.isEmpty(modelList)) {
                return multipleCompareVO;
            }

            Map<String, List<SendStatDO>> dateMap = modelList.stream().collect(Collectors.groupingBy(SendStatDO::getStatDate));
            List<String> dateList = multipleCompareVO.getDateList();

            List<MultipleCompareDataVO> dataList = processData(dateList, dateMap, query);
            multipleCompareVO.setDataList(dataList);
        } catch (Exception e) {
            log.error("[BidataSendStatServiceImpl] multipleCompare 查询或处理数据出错", e);
        }

        return multipleCompareVO;
    }

    @Override
    public List<Object> overviewByQuery(SendQuery query) {
        if (isIspCodeAll(query.getIspCode())) {
            query.setIspCode(venusConfig.getIspCodeAllConfig());
        }
        if (Strings.isNotBlank(query.getTplCode())) {
            query.setTplCodes(Arrays.asList(query.getTplCode().split(",")));
        }
        query.setPageParameter(null);
        QueryUtils.setQuery(query);
        List<SendStatDO> statList = bidataSendStatMapper.selectSendDetailByQuery(query);
        return getResultList(statList);
    }

    private void prepareQueryCodes(MultipleCompareQuery query) {
        if (Strings.isNotBlank(query.getTplCode())) {
            query.setTplCodes(Arrays.asList(query.getTplCode().split(",")));
        }
        if (Strings.isNotBlank(query.getChannelCode())) {
            query.setChannelCodes(Arrays.asList(query.getChannelCode().split(",")));
        }
    }

    private List<MultipleCompareDataVO> processData(List<String> dateList, Map<String, List<SendStatDO>> dateMap, MultipleCompareQuery query) {
        List<MultipleCompareDataVO> dataList = new ArrayList<>();
        for (String date : dateList) {
            List<SendStatDO> sendList = dateMap.getOrDefault(date, Collections.emptyList());
            log.info("[BidataSendStatServiceImpl] Processing date: {} sendList size:{}", date, sendList.size());

            MultipleCompareDataVO dataVO = processSendStatsForDate(sendList, query);
            dataList.add(dataVO);
        }
        return dataList;
    }

    private MultipleCompareDataVO processSendStatsForDate(List<SendStatDO> sendList, MultipleCompareQuery query) {
        MultipleCompareDataVO dataVO = new MultipleCompareDataVO();
        List<Long> barValues = new ArrayList<>();
        List<Double> foldLineValues = new ArrayList<>();


        Set<String> tplCodes = query.getTplCodes() == null ? Collections.emptySet() : new HashSet<>(query.getTplCodes());
        Set<String> channelCodes = query.getChannelCodes() == null ? Collections.emptySet() : new HashSet<>(query.getChannelCodes());

        boolean isTplAll = tplCodes.isEmpty();
        boolean isChannelAll = channelCodes.isEmpty();
        log.info("[BidataSendStatServiceImpl] Processing isTplAll:{} | isChannelAll:{}", isTplAll, isChannelAll);
        Map<String, List<SendStatDO>> groupedAllData = sendList.stream()
                .collect(Collectors.groupingBy(stat -> stat.getTplCode() + "-" + stat.getChannelCode()));
        Map<String, List<SendStatDO>> groupedTplCodeData = sendList.stream()
                .collect(Collectors.groupingBy(SendStatDO::getTplCode));
        Map<String, List<SendStatDO>> groupedChannelCodeData = sendList.stream()
                .collect(Collectors.groupingBy(SendStatDO::getChannelCode));

        if (isTplAll && isChannelAll) {
            processSendStats(sendList, barValues, foldLineValues);
        } else if (isTplAll) {
            for (String channelCode : channelCodes) {
                processSendStats(groupedChannelCodeData.getOrDefault(channelCode, Collections.emptyList()), barValues, foldLineValues);
            }
        } else if (isChannelAll) {
            for (String tplCode : tplCodes) {
                processSendStats(groupedTplCodeData.getOrDefault(tplCode, Collections.emptyList()), barValues, foldLineValues);
            }
        } else {
            for (String tplCode : tplCodes) {
                for (String channelCode : channelCodes) {
                    processSendStats(groupedAllData.getOrDefault(tplCode + "-" + channelCode, Collections.emptyList()), barValues, foldLineValues);
                }
            }
        }

        dataVO.setBarValues(barValues);
        dataVO.setFoldLineValues(foldLineValues);
        return dataVO;
    }

    private void processSendStats(List<SendStatDO> sendStatList, List<Long> barValues, List<Double> foldLineValues) {
        long sendCountSum = sendStatList.stream().mapToLong(SendStatDO::getSendCount).sum();
        long reachCountSum = sendStatList.stream().mapToLong(SendStatDO::getReachCount).sum();
        double reachRate = CommonUtil.division(reachCountSum, sendCountSum);
        barValues.add(sendCountSum);
        foldLineValues.add(reachRate);
    }

    private MultipleCompareVO initBulidMultipleCompareVO(MultipleCompareQuery query) {
        MultipleCompareVO multipleCompareVO = new MultipleCompareVO();
        List<String> dateList = new LinkedList<>();
        Date startTime = DateUtil.parseDateTime(query.getStartTime());
        Date endTime = DateUtil.parseDateTime(query.getEndTime());
        while (startTime.before(endTime)) {
            dateList.add(DateUtil.format(startTime, "yyyy-MM-dd"));
            startTime = DateUtil.offsetDay(startTime, 1);
        }
        multipleCompareVO.setDateList(dateList);

        List<String> barNames = new LinkedList<>();
        List<String> foldLineNames = new LinkedList<>();

        Set<String> tplCodes = CollectionUtil.isEmpty(query.getTplCodes()) ? Collections.emptySet() : new HashSet<>(query.getTplCodes());
        Set<String> channelCodes = CollectionUtil.isEmpty(query.getChannelCodes()) ? Collections.emptySet() : new HashSet<>(query.getChannelCodes());

        boolean isTplAll = tplCodes.isEmpty();
        boolean isChannelAll = channelCodes.isEmpty();

        for (String tplCode : (isTplAll ? CollectionUtil.newArrayList("all") : query.getTplCodes())) {
            for (String channelCode : (isChannelAll ? CollectionUtil.newArrayList("all") : query.getChannelCodes())) {
                barNames.add(tplCode + "-" + channelCode);
                foldLineNames.add(tplCode + "-" + channelCode);
            }
        }
        multipleCompareVO.setBarNames(barNames);
        multipleCompareVO.setFoldLineNames(foldLineNames);
        return multipleCompareVO;
    }

    private List<Long> initLongList(int size) {
        if (size <= 0) {
            return new ArrayList<>();
        }
        List<Long> list = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            list.add(0L);
        }
        return list;
    }

    private List<Double> initDoubleList(int size) {
        if (size <= 0) {
            return new ArrayList<>();
        }
        List<Double> list = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            list.add(0D);
        }
        return list;
    }

    private SendDetailVO convertDOToDetailVo(SendStatDO sendStatDO) {
        SendDetailVO vo = new SendDetailVO();
        BeanUtils.copyProperties(sendStatDO, vo);
        vo.setReachRate(CommonUtil.division(sendStatDO.getReachCount(), sendStatDO.getSendCount()));
        vo.setFirstReachRate(CommonUtil.division(sendStatDO.getFirstReachCount(), sendStatDO.getSendCount()));
        return vo;
    }

    private List<Object> getResultListForDateRange(Date baseDate, String type) {
        Date startDate = DateUtil.beginOfDay(isWeek(type) ? DateUtil.offsetWeek(baseDate, -1) : DateUtil.beginOfMonth(baseDate));
        Date endDate = DateUtil.endOfDay(isWeek(type) ? DateUtil.endOfWeek(baseDate) : (DateUtil.endOfMonth(baseDate)));
        if (isDay(type)) {
            startDate = DateUtil.beginOfDay(baseDate);
            endDate = DateUtil.endOfDay(baseDate);
        }
        if (venusConfig.isLogEnable()) {
            log.info("[{}] startDate: {}, endDate: {}", type, startDate, endDate);
        }
        SendQuery query = new SendQuery();
        query.setStartTime(DateUtil.formatDate(startDate));
        query.setEndTime(DateUtil.formatDate(endDate));
        query.setTableName(QueryUtils.channelTableName());
        List<SendStatDO> statList = bidataSendStatMapper.selectByTime(query);
        return getResultList(statList);
    }

    private List<Object> getResultList(List<SendStatDO> modelList) {
        if (CollectionUtil.isEmpty(modelList)) {
            return new ArrayList<>();
        }
        long reachCountSum = modelList.stream().mapToLong(SendStatDO::getReachCount).sum();
        long sendCountSum = modelList.stream().mapToLong(SendStatDO::getSendCount).sum();
        double reachRate = CommonUtil.division(reachCountSum, sendCountSum);
        List<Object> resultList = new ArrayList<>();
        resultList.add(sendCountSum);
        resultList.add(reachCountSum);
        resultList.add(reachRate);
        return resultList;
    }

    /**
     * 处理查询结果，将查询到的触达率数据按照指定规则转换成结果映射。
     *
     * @param rateDTOList 查询得到的触达率数据对象列表
     * @return 包含处理后的触达率数据的映射，键为代码，值为包含触达总数和触达率的列表
     */
    private static Map<String, List<Object>> processQueryResults(List<ReachRateDTO> rateDTOList) {
        Map<String, List<Object>> resultMap = new HashMap<>();

        Map<String, List<ReachRateDTO>> codeMap = rateDTOList.stream()
                .collect(Collectors.groupingBy(ReachRateDTO::getCode, Collectors.toList()));

        for (Map.Entry<String, List<ReachRateDTO>> entry : codeMap.entrySet()) {
            List<Object> list = new ArrayList<>();
            List<ReachRateDTO> valueList = entry.getValue();
            long totalSend = valueList.stream().mapToLong(ReachRateDTO::getSendCount).sum();
            long totalReach = valueList.stream().mapToLong(ReachRateDTO::getReachCount).sum();
            double rate = CommonUtil.division(totalReach, totalSend);
            list.add(totalReach);
            list.add(rate);
            resultMap.put(entry.getKey(), list);
        }
        return resultMap;
    }


    public boolean isWeek(String type) {
        return "weeks".equals(type);
    }

    public boolean isDay(String type) {
        return "yesterday".equals(type);
    }

    public boolean isIspCodeAll(String ispCode) {
        return "default".equals(ispCode);
    }

}
