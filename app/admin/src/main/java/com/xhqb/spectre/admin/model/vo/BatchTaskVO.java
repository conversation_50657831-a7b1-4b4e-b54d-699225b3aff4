package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.util.DateUtil;
import com.xhqb.spectre.common.dal.entity.BatchTaskDO;
import com.xhqb.spectre.common.dal.entity.SignDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 群发短信VO
 *
 * <AUTHOR>
 * @date 2021/9/17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskVO implements Serializable {

    /**
     * 批次号
     */
    private Integer id;
    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 模板ID
     */
    private Integer tplId;
    /**
     * 签名ID
     */
    private Integer signId;
    /**
     * 短信类型编码
     */
    private String smsTypeCode;
    /**
     * 内容
     */
    private String content;
    /**
     * 名单中的用户标识类型，1：cid；2：手机号
     */
    private Integer userIdType;
    /**
     * 发送类型，1：立即发送；2：定时发送
     */
    private Integer sendType;
    /**
     * 定时发送时间
     */
    private Date sendTime;
    /**
     * 文件信息，json数组，包含文件cos地址、文件名、文件md5等
     */
    private String fileInfoList;
    /**
     * 备注，发送原因
     */
    private String remark;
    /**
     * 状态 ，0：待提交；1：已提交；2：发送中；3：已发送；9：已取消
     */
    private Integer status;
    /**
     * 发送总条数
     */
    private Integer totalCount;
    /**
     * 已发送条数
     */
    private Integer sentCount;
    /**
     * 实际发送的开始时间
     */
    private Date sendStartTime;
    /**
     * 实际发送的结束时间
     */
    private Date sendEndTime;
    /**
     * 群发任务订单号
     */
    private String orderId;
    /**
     * 数据版本号
     */
    private Integer version;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String updater;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 实际发送量
     */
    private Integer realSendCount;

    /*
      =============================================
      =============================================
      =============================================
     */
    /**
     * 签名
     */
    private String signName;

    /**
     * 项目用途
     */
    private String projectDesc;

    /**
     * 用户分组
     */
    private String userGroup;

    /**
     * 分发速率，0表示不限速，正数表示每秒处理的号码数
     */
    private Integer limitRate;

    /**
     * 查询列表数据展现
     *
     * @param batchTaskDO
     * @return
     */
    public static BatchTaskVO buildListQuery(BatchTaskDO batchTaskDO) {
        SignDO sign = batchTaskDO.getSign();
        String signName = null;
        if (Objects.nonNull(sign)) {
            // 获取到签名的名称
            signName = sign.getName();
        }
        return BatchTaskVO.builder()
                // 批次号
                .id(batchTaskDO.getId())
                // 模板ID
                .tplId(batchTaskDO.getTplId())
                // 签名名称
                .signName(signName)
                // 短信类型
                .smsTypeCode(batchTaskDO.getSmsTypeCode())
                // 应用名称
                .appCode(batchTaskDO.getAppCode())
                // 内容
                .content(batchTaskDO.getContent())
                // 发送类型，1：立即发送；2：定时发送
                .sendType(batchTaskDO.getSendType())
                // 定时发送时间
                .sendTime(DateUtil.intToDate(batchTaskDO.getSendTime()))
                // 状态
                .status(batchTaskDO.getStatus())
                // 成功数
                .sentCount(batchTaskDO.getSentCount())
                // 总量数
                .totalCount(batchTaskDO.getTotalCount())
                // 实际发送量
                .realSendCount(batchTaskDO.getRealSendCount())
                // 实际发送的开始时间
                .sendStartTime(DateUtil.intToDate(batchTaskDO.getSendStartTime()))
                // 实际发送的结束时间
                .sendEndTime(DateUtil.intToDate(batchTaskDO.getSendEndTime()))
                // 申请时间
                .createTime(batchTaskDO.getCreateTime())
                // 发送时间
                .sendStartTime(DateUtil.intToDate(batchTaskDO.getSendStartTime()))
                // 操作人
                .updater(batchTaskDO.getUpdater())
                // 项目用途
                .projectDesc(batchTaskDO.getProjectDesc())
                // 用户分组
                .userGroup(batchTaskDO.getUserGroup())
                // 分发速率
                .limitRate(batchTaskDO.getLimitRate())
                .build();
    }

    /**
     * 查询数据详情展现
     *
     * @param batchTaskDO
     * @return
     */
    public static BatchTaskVO buildInfoQuery(BatchTaskDO batchTaskDO) {
        return BatchTaskVO.builder()
                // 批次号
                .id(batchTaskDO.getId())
                // 应用编码
                .appCode(batchTaskDO.getAppCode())
                // 模板ID
                .tplId(batchTaskDO.getTplId())
                // 签名ID
                .signId(batchTaskDO.getSignId())
                // 短信类型编码
                .smsTypeCode(batchTaskDO.getSmsTypeCode())
                // 内容
                .content(batchTaskDO.getContent())
                // 名单中的用户标识类型，1：cid；2：手机号
                .userIdType(batchTaskDO.getUserIdType())
                // 发送类型，1：立即发送；2：定时发送
                .sendType(batchTaskDO.getSendType())
                // 定时发送时间
                .sendTime(DateUtil.intToDate(batchTaskDO.getSendTime()))
                // 文件信息，json数组，包含文件cos地址、文件名、文件md5等
                .fileInfoList(batchTaskDO.getFileInfoList())
                // 备注，发送原因
                .remark(batchTaskDO.getRemark())
                // 状态 ，0：待提交；1：已提交；2：发送中；3：已发送；9：已取消
                .status(batchTaskDO.getStatus())
                // 发送总条数
                .totalCount(batchTaskDO.getTotalCount())
                // 已发送条数
                .sentCount(batchTaskDO.getSentCount())
                // 实际发送量
                .realSendCount(batchTaskDO.getRealSendCount())
                // 实际发送的开始时间
                .sendStartTime(DateUtil.intToDate(batchTaskDO.getSendStartTime()))
                // 实际发送的结束时间
                .sendEndTime(DateUtil.intToDate(batchTaskDO.getSendEndTime()))
                // 群发任务订单号
                .orderId(batchTaskDO.getOrderId())
                // 数据版本号
                .version(batchTaskDO.getVersion())
                // 创建人
                .creator(batchTaskDO.getCreator())
                // 创建时间
                .createTime(batchTaskDO.getCreateTime())
                // 修改人
                .updater(batchTaskDO.getUpdater())
                // 更新时间
                .updateTime(batchTaskDO.getUpdateTime())
                // 项目用途
                .projectDesc(batchTaskDO.getProjectDesc())
                // 用户分组
                .userGroup(batchTaskDO.getUserGroup())
                // 分发速率
                .limitRate(batchTaskDO.getLimitRate())
                .build();
    }

}
