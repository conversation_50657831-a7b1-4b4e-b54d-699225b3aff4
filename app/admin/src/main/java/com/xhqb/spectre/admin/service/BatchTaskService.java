package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.BatchTaskDTO;
import com.xhqb.spectre.admin.model.dto.BatchTaskSendDTO;
import com.xhqb.spectre.admin.model.vo.BatchTaskDetailVO;
import com.xhqb.spectre.admin.model.vo.BatchTaskVO;
import com.xhqb.spectre.common.dal.entity.BatchTaskParamDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.BatchTaskQuery;

import java.util.List;
import java.util.Map;

/**
 * 群发短信
 *
 * <AUTHOR>
 * @date 2021/9/17
 */
public interface BatchTaskService {

    /**
     * 分页查询群发短信任务列表
     *
     * @param batchTaskQuery
     * @return
     */
    CommonPager<BatchTaskVO> listByPage(BatchTaskQuery batchTaskQuery);

    /**
     * 根据ID查询群发短信任务详情
     *
     * @param id
     * @return
     */
    BatchTaskDetailVO getById(Integer id);

    /**
     * 根据ID取消群发任务
     *
     * @param id
     */
    void cancelBatchTask(Integer id);

    /**
     * 根据ID删除群发任务
     *
     * @param id
     */
    void deleteBatchTask(Integer id);

    /**
     * 添加群发短信任务信息
     *
     * @param batchTaskDTO
     * @param creator
     * @return 返回批次号
     */
    Integer create(BatchTaskDTO batchTaskDTO,String creator);

    /**
     * 更新群发短信任务信息
     *
     * @param id
     * @param batchTaskDTO
     */
    void update(Integer id, BatchTaskDTO batchTaskDTO);

    /**
     * 群发短信任务提交
     *
     * @param id
     */
    void submit(Integer id);

    /**
     * 测试群发短信操作
     *
     * @param id               群发短信批次号(主键)
     * @param batchTaskSendDTO
     * @return
     */
    Map<String, Object> send(Integer id, BatchTaskSendDTO batchTaskSendDTO);

    /**
     * 批量保存插入参数列表
     *
     * @param batchTaskParamDOList
     */
    List<Integer> batchInsertParamList(List<BatchTaskParamDO> batchTaskParamDOList);

    /**
     * 群发任务克隆
     * <p>
     * 将传入的群发任务数据复制一份并重置原始状态
     *
     * @param id
     */
    void copy(Integer id);

    /**
     * 更新群发任务状态
     *
     * @param id
     */
    void resetStatus(Integer id);

    /**
     * 获取文件下载路径
     *
     * @param fileName
     * @return
     */
    String downloadUrl(String fileName);
}
