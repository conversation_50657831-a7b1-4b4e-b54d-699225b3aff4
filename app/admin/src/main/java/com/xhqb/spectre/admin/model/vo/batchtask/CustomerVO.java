package com.xhqb.spectre.admin.model.vo.batchtask;

import com.xhqb.spectre.admin.cif.entity.CifCustomerBaseDO;
import com.xhqb.spectre.admin.cif.entity.CifEnterpriseCustomerDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @date 2021/9/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerVO implements Serializable {

    /**
     * 用户ID (cid等)
     */
    private String id;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 用户名称
     */
    private String name;
    /**
     * 状态
     */
    private String status;
    /**
     * 完件
     * PASS-通过
     * PRE-未完件
     * IN-完件操作中
     * ING-完件审批中
     * REFUSE-拒绝
     *
     * <p>
     * 需求：
     * ING-完件审批中
     * REFUSE-拒绝
     * PASS-通过
     * 都是完件，完件的不发营销
     */
    private String applyLoanResult;

    /**
     * customerBase 转换成CustomerVO
     *
     * @param cifCustomerBaseDO
     * @return
     */
    public static CustomerVO customerBase(CifCustomerBaseDO cifCustomerBaseDO) {
        return CustomerVO.builder()
                // id
                .id(cifCustomerBaseDO.getId())
                // 手机号码
                .mobile(cifCustomerBaseDO.getMobilePhone())
                // 客户名称
                .name(cifCustomerBaseDO.getCustomerName())
                // 状态
                .status(cifCustomerBaseDO.getStatus())
                // 完件
                .applyLoanResult(cifCustomerBaseDO.getApplyLoanResult())
                .build();
    }

    /**
     * enterpriseCustomer 转换成CustomerVO
     *
     * @param cifEnterpriseCustomerDO
     * @return
     */

    public static CustomerVO enterpriseCustomer(CifEnterpriseCustomerDO cifEnterpriseCustomerDO) {
        return CustomerVO.builder()
                // id
                .id(cifEnterpriseCustomerDO.getCid())
                // 手机号码
                .mobile(cifEnterpriseCustomerDO.getMobilePhone())
                // 客户名称
                .name(cifEnterpriseCustomerDO.getCustomerName())
                // 状态
                .status(cifEnterpriseCustomerDO.getStatus())
                // 完件
                .applyLoanResult(cifEnterpriseCustomerDO.getApplyLoanResult())
                .build();
    }
}
