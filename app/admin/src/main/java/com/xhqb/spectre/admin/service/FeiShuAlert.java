package com.xhqb.spectre.admin.service;

import cn.hutool.core.collection.CollUtil;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.msgcenter.common.core.mqItems.ReceiverInfo;
import com.xhqb.msgcenter.model.MsgSendRequest;
import com.xhqb.msgcenter.model.iteam.MsgSendEntry;
import com.xhqb.msgcenter.model.response.OminiSendResult;
import com.xhqb.msgcenter.sdk.OminiSendMessage;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class FeiShuAlert {

    @Resource
    private OminiSendMessage ominiSendMessage;
    @Resource
    private VenusConfig venusConfig;

    /**
     * 发送飞书告警
     */
    public void sendFeiShuAlert(Map<String, Object> dataMap, String strategyId) {
        MsgSendRequest msgSendRequest = new MsgSendRequest();

        // 飞书通知
        msgSendRequest.setMsgClient("1");
        msgSendRequest.setSystem(venusConfig.getServerName());

        MsgSendEntry msgSendEntry = new MsgSendEntry();
        msgSendEntry.setStrategyId(strategyId);
        HashMap<String, Object> params = new HashMap<>();
        params.put("msgType", "json");
        params.put("isDynamic", "1");
        params.putAll(dataMap);
        msgSendEntry.setMsgVariableJson(params);

        msgSendRequest.setMsgSendEntries(CollUtil.toList(msgSendEntry));
        log.info("飞书消息 msgSendRequest:{}", JsonLogUtil.toJSONString(msgSendRequest));

        try {
            OminiSendResult ominiSendResult = this.ominiSendMessage.sendMessage(msgSendRequest);
            if (ominiSendResult.isSuccess()) {
                log.info("推送告警信息成功");
                return;
            }
            log.info("推送告警信息失败，失败编码：{}", ominiSendResult.getCode());
        } catch (Exception e) {
            throw new BizException("推送告警信息失败");
        }
    }


    /**
     * 发送飞书告警
     */
    public void sendAppFeiShuAlert(Map<String, Object> dataMap, String strategyId, ArrayList<ReceiverInfo> receiver) {
        MsgSendRequest msgSendRequest = new MsgSendRequest();

        // 飞书通知
        msgSendRequest.setMsgClient("1");
        msgSendRequest.setSystem(venusConfig.getServerName());

        MsgSendEntry msgSendEntry = new MsgSendEntry();
        msgSendEntry.setStrategyId(strategyId);
        msgSendEntry.setReceiver(receiver);
        HashMap<String, Object> params = new HashMap<>();
        params.put("msgType", "json");
        params.put("isDynamic", "1");
        params.put("noticeType", "workMsg");
        params.putAll(dataMap);
        msgSendEntry.setMsgVariableJson(params);

        msgSendRequest.setMsgSendEntries(CollUtil.toList(msgSendEntry));
        log.info("飞书消息 msgSendRequest:{}", JsonLogUtil.toJSONString(msgSendRequest));

        try {
            OminiSendResult ominiSendResult = this.ominiSendMessage.sendMessage(msgSendRequest);
            if (ominiSendResult.isSuccess()) {
                log.info("推送告警信息成功");
                return;
            }
            log.info("推送告警信息失败，失败编码：{}", ominiSendResult.getCode());
        } catch (Exception e) {
            throw new BizException("推送告警信息失败");
        }
    }
}
