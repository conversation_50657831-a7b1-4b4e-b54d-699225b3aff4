package com.xhqb.spectre.admin.service.impl;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import com.xhqb.spectre.admin.model.vo.ChannelTestRecordVO;
import com.xhqb.spectre.admin.service.ChannelTestRecordService;
import com.xhqb.spectre.admin.util.AesUtil;
import com.xhqb.spectre.common.dal.entity.test.TestTaskLogDO;
import com.xhqb.spectre.common.dal.mapper.TestTaskLogMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ChannelTestRecordQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChannelTestRecordServiceImpl implements ChannelTestRecordService {

    @Resource
    private TestTaskLogMapper testTaskLogMapper;

    @Override
    public CommonPager<ChannelTestRecordVO> listByPage(ChannelTestRecordQuery channelTestRecordQuery) {
        return PageResultUtils.result(
                () -> testTaskLogMapper.countByQuery(channelTestRecordQuery),
                () -> testTaskLogMapper.selectByQuery(channelTestRecordQuery)
                        .stream().map(this::buildVO)
                        .collect(Collectors.toList())
        );
    }

    private ChannelTestRecordVO buildVO(TestTaskLogDO testTaskLogDO) {
        ChannelTestRecordVO channelTestRecordVO = new ChannelTestRecordVO();
        BeanUtils.copyProperties(testTaskLogDO, channelTestRecordVO);
        String mobile = testTaskLogDO.getMobile();
        channelTestRecordVO.setEncryptMobile(AesUtil.encryptFromString(mobile, Mode.CBC, Padding.ZeroPadding));
        channelTestRecordVO.setMobile(DesensitizedUtil.mobilePhone(mobile));
        return channelTestRecordVO;
    }
}
