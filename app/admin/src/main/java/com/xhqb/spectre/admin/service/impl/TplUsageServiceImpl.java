package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.readonly.mapper.TplUsageReadonlyMapper;
import com.xhqb.spectre.admin.service.TplUsageService;
import org.springframework.stereotype.Service;

import java.util.Set;

@Service
public class TplUsageServiceImpl implements TplUsageService {

    private final TplUsageReadonlyMapper tplUsageReadonlyMapper;

    public TplUsageServiceImpl(TplUsageReadonlyMapper tplUsageReadonlyMapper) {
        this.tplUsageReadonlyMapper = tplUsageReadonlyMapper;
    }

    @Override
    public Set<String> findTplCodesBySendTime(long start, long end) {
        return tplUsageReadonlyMapper.findTplCodesBySendTime(start, end);
    }
}
