package com.xhqb.spectre.admin.mq.producer.impl;

import com.xhqb.spectre.admin.mq.message.BatchTaskSubmitMessage;
import com.xhqb.spectre.admin.mq.producer.AbstractMessageProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 群发任务提交消费处理器
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Component
public class BatchTaskSubmitProducer extends AbstractMessageProducer<BatchTaskSubmitMessage> {

    /**
     * spectre-admin-batch topic信息
     */
    @Value("#{'${kael.mq.producers:}'.split(',')[0]}")
    private String taskTopic;

    /**
     * 设置生产者topic
     *
     * @return
     */
    @Override
    protected String getTopic() {
        return taskTopic;
    }
}
