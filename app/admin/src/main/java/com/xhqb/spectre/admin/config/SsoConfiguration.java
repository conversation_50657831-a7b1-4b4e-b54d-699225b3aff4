package com.xhqb.spectre.admin.config;

import com.xhqb.ucenter.sso.client.SmartContainer;
import com.xhqb.ucenter.sso.client.filter.LoginFilter;
import com.xhqb.ucenter.sso.client.filter.LogoutFilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SsoConfiguration {

    @Value("${sso.server.url}")
    private String serverUrl;

    @Value("${sso.server.innerUrl}")
    private String innerServerUrl;

    @Value("${sso.app.id}")
    private String appId;

    @Value("${sso.app.secret}")
    private String appSecret;

    @Bean
    @ConditionalOnProperty(name = "sso.filter.enabled", havingValue = "true")
    public FilterRegistrationBean<SmartContainer> smartContainer() {
        SmartContainer smartContainer = new SmartContainer();
        smartContainer.setServerUrl(serverUrl);
        smartContainer.setAppId(appId);
        smartContainer.setAppSecret(appSecret);
        smartContainer.setInnerServerUrl(innerServerUrl);
        // 忽略拦截URL,多个逗号分隔
        smartContainer.setExcludeUrls("/app/*,/h5/51.css,/open/api/*,/channelTestMobile/*,/channelTestMobile");
        smartContainer.setFilters(new LogoutFilter(), new LoginFilter());
        FilterRegistrationBean<SmartContainer> registration = new
                FilterRegistrationBean<>();
        registration.setFilter(smartContainer);
        registration.addUrlPatterns("/*");
        // 设置filter的优先级 尽量设置⾼优先级
        registration.setOrder(1);
        registration.setName("smartContainer");
        return registration;
    }
}
