package com.xhqb.spectre.admin.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppDailyStatsChartVO {
    private List<String> xData;
    private List<SeriesData> yData;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SeriesData {
        private String name; // 名称
        private String stack; // 堆叠分组
        private List<Object> data; // 数据（可以是数字或字符串 "-"）
    }
}
