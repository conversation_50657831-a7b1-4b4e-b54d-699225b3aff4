package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.BusinessLineDTO;
import com.xhqb.spectre.admin.model.vo.BusinessLineVO;
import com.xhqb.spectre.admin.service.BusinessLineService;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.ValidatorUtil;
import com.xhqb.spectre.common.dal.entity.BusinessLineDO;
import com.xhqb.spectre.common.dal.mapper.BusinessLineMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.BusinessLineQuery;
import com.xhqb.spectre.common.enums.DeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 业务线服务实现类
 *
 * <AUTHOR>
 * @date 2022/9/23
 */
@Service
@Slf4j
public class BusinessLineServiceImpl implements BusinessLineService {

    @Resource
    private BusinessLineMapper businessLineMapper;

    /**
     * 分页查询业务线列表
     *
     * @param businessLineQuery
     * @return
     */
    @Override
    public CommonPager<BusinessLineVO> listByPage(BusinessLineQuery businessLineQuery) {
        return PageResultUtils.result(
                () -> businessLineMapper.countByQuery(businessLineQuery),
                () -> businessLineMapper.selectByQuery(businessLineQuery).stream().map(BusinessLineVO::buildBusinessLineVO).collect(Collectors.toList())
        );
    }

    /**
     * 查询所有的业务信息信息
     *
     * @param status 状态，0：无效，1：有效 , 为空查所有
     * @return
     */
    @Override
    public List<BusinessLineVO> listAll(Integer status) {
        List<BusinessLineDO> businessLineList = businessLineMapper.listAll(status);
        if (CommonUtil.isEmpty(businessLineList)) {
            return Collections.emptyList();
        }
        return businessLineList.stream().map(BusinessLineVO::buildBusinessLineVO).collect(Collectors.toList());
    }

    /**
     * 查询业务线详情
     *
     * @param id
     * @return
     */
    @Override
    public BusinessLineVO getById(Integer id) {
        BusinessLineDO businessLine = businessLineMapper.selectByPrimaryKey(id);
        return BusinessLineVO.buildBusinessLineVO(businessLine);
    }

    /**
     * 新增业务线
     *
     * @param businessLineDTO
     * @return 返回新增业务线ID
     */
    @Override
    public Integer create(BusinessLineDTO businessLineDTO) {
        //参数格式校验
        ValidatorUtil.validate(businessLineDTO);
        String currentUser = SsoUserInfoUtil.getUserName();
        BusinessLineDO businessLineDO = BusinessLineDO.builder()
                .name(businessLineDTO.getName())
                .status(businessLineDTO.getStatus())
                .creator(currentUser)
                .updater(currentUser)
                .build();
        businessLineMapper.insertSelective(businessLineDO);
        return businessLineDO.getId();
    }

    /**
     * 更新业务线
     *
     * @param id
     * @param businessLineDTO
     * @return
     */
    @Override
    public Integer update(Integer id, BusinessLineDTO businessLineDTO) {
        if (Objects.isNull(businessLineMapper.selectByPrimaryKey(id))) {
            throw new BizException("业务线不存在");
        }
        //参数格式校验
        ValidatorUtil.validate(businessLineDTO);
        String currentUser = SsoUserInfoUtil.getUserName();
        BusinessLineDO businessLineDO = BusinessLineDO.builder()
                .id(id)
                .name(businessLineDTO.getName())
                .status(businessLineDTO.getStatus())
                .creator(currentUser)
                .updater(currentUser)
                .build();
        businessLineMapper.updateByPrimaryKeySelective(businessLineDO);
        return id;
    }

    /**
     * 业务线状态调整
     *
     * @param id
     * @param status 状态，0：无效，1：有效
     * @return
     */
    @Override
    public Integer status(Integer id, int status) {
        if (Objects.isNull(businessLineMapper.selectByPrimaryKey(id))) {
            throw new BizException("业务线不存在");
        }
        BusinessLineDO businessLineDO = BusinessLineDO.builder()
                .id(id)
                .status(status)
                .updater(SsoUserInfoUtil.getUserName())
                .build();
        businessLineMapper.updateByPrimaryKeySelective(businessLineDO);
        return id;
    }

    /**
     * 删除业务线
     *
     * @param id
     * @return
     */
    @Override
    public Integer deleteById(Integer id) {
        if (Objects.isNull(businessLineMapper.selectByPrimaryKey(id))) {
            throw new BizException("业务线不存在");
        }
        BusinessLineDO businessLineDO = BusinessLineDO.builder()
                .id(id)
                .isDelete(DeleteEnum.DELETED.getCode())
                .updater(SsoUserInfoUtil.getUserName())
                .build();
        businessLineMapper.updateByPrimaryKeySelective(businessLineDO);
        return id;
    }

    /**
     * 新增业务线与模板的关联关系
     *
     * @param tplId
     * @param businessLineId
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void addBusinessLineTpl(Integer tplId, Integer businessLineId) {
        if (Objects.isNull(tplId) || Objects.isNull(businessLineId)) {
            log.warn("新增业务线与模板的关联关系缺少必填参数, tplId = {}, businessLineId = {}", tplId, businessLineId);
            return;
        }
        String creator = SsoUserInfoUtil.getUserName();
        // 首先查看业务线与模板的关联关系是否存在，不存在则进行保存;
        // 存在一个并且与当前businessLineId一致，则不做任何操作;
        // 否则进行先删除 再做保存处理逻辑
        List<Integer> businessLineIdList = businessLineMapper.selectBusinessLineIdByTplId(tplId);
        if (CommonUtil.isEmpty(businessLineIdList)) {
            businessLineMapper.insertBusinessLineTpl(tplId, businessLineId, creator);
            return;
        }

        if (businessLineIdList.size() == 1 && Objects.equals(businessLineId, businessLineIdList.get(0))) {
            return;
        }

        businessLineMapper.deleteBusinessLineTpl(tplId, creator);
        businessLineMapper.insertBusinessLineTpl(tplId, businessLineId, creator);
    }

    /**
     * 根据模板ID查询到业务线ID
     *
     * @param tplId
     * @return
     */
    @Override
    public Integer getBusinessLineIdByTpl(Integer tplId) {
        List<Integer> businessLineIdList = businessLineMapper.selectBusinessLineIdByTplId(tplId);
        return CollectionUtils.isNotEmpty(businessLineIdList) ? businessLineIdList.get(0) : null;
    }

    @Override
    public int findOrCreateBusinessLine(String businessLineName) {

        BusinessLineDO modelDO = businessLineMapper.selectByName(businessLineName);
        if (Objects.nonNull(modelDO)) {
            return modelDO.getId();
        }

        String currentUser = SsoUserInfoUtil.getUserName();
        BusinessLineDO businessLineDO = BusinessLineDO.builder()
                .name(businessLineName)
                .status(1)
                .creator(currentUser)
                .updater(currentUser)
                .build();
        businessLineMapper.insertSelective(businessLineDO);
        return businessLineDO.getId();
    }

    @Override
    public String getBusinessLineByTpl(Integer tplId) {
        List<Integer> businessLineIdList = businessLineMapper.selectBusinessLineIdByTplId(tplId);
        Integer businessLineId = CollectionUtils.isNotEmpty(businessLineIdList) ? businessLineIdList.get(0) : null;
        if (Objects.isNull(businessLineId)) {
            return null;
        }
        BusinessLineDO businessLineDO = businessLineMapper.selectByPrimaryKey(businessLineId);

        return Objects.isNull(businessLineDO) ? null : businessLineDO.getName();
    }
}
