package com.xhqb.spectre.admin.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatSmsTypeVO {

    private List<SubSmsType> months;

    private List<SubSmsType> weeks;

    @Data
    @Builder
    public static class SubSmsType {
        /**
         * 渠道编码
         */
        private String smsTypeCode;

        private String smsTypeName;

        private Set<String> signList;

        public void addSign(String signName) {
            this.signList.add(signName);
        }
    }
}
