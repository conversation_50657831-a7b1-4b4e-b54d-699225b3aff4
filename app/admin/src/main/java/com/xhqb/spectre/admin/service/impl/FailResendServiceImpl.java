package com.xhqb.spectre.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonArray;
import com.xhqb.kael.util.json.JsonLogUtil;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.FailResendDTO;
import com.xhqb.spectre.admin.model.dto.FailResendRuleDTO;
import com.xhqb.spectre.admin.model.vo.FailResendDetailVO;
import com.xhqb.spectre.admin.model.vo.FailResendVO;
import com.xhqb.spectre.admin.service.FailResendService;
import com.xhqb.spectre.admin.service.FailResendStrategyService;
import com.xhqb.spectre.admin.service.SerialNumberGenerator;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.admin.util.TemplateUtil;
import com.xhqb.spectre.common.dal.entity.FailResendRuleDO;
import com.xhqb.spectre.common.dal.entity.FailResendStrategyDO;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.mapper.FailResendRuleMapper;
import com.xhqb.spectre.common.dal.mapper.FailResendStrategyMapper;
import com.xhqb.spectre.common.dal.mapper.SignMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.FailResendQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FailResendServiceImpl implements FailResendService {

    @Resource
    private FailResendStrategyMapper failResendStrategyMapper;
    @Resource
    private FailResendRuleMapper failResendRuleMapper;
    @Resource
    private SerialNumberGenerator serialNumberGenerator;
    @Resource
    private TplMapper tplMapper;
    @Resource
    private SignMapper signMapper;
    @Resource
    private FailResendStrategyService failResendStrategyService;

    @Override
    public CommonPager<FailResendVO> page(FailResendQuery failResendQuery) {
        return PageResultUtils.result(
                () -> failResendStrategyMapper.countByQuery(failResendQuery),
                () -> failResendStrategyMapper.selectByQuery(failResendQuery).stream()
                        .map(this::buildFailResendVO).collect(Collectors.toList())
        );
    }

    @Override
    public String add(FailResendDTO failResendDTO) {

        // 检验参数
        check(failResendDTO);
        // 判断 tplCode 是否存在绑定策略 绑定了就异常
        List<FailResendStrategyDO> modelDOList = failResendStrategyMapper.selectAll();
        checkTplCodeBinding(failResendDTO, modelDOList);

        // 根据原始 tplCode 得到 短信内容参数 和 目标模板 短信内容参数 比较 相等或者 目标模板参数等于 0 就是认为正确的 否则抛异常
        validateTplParameters(failResendDTO.getOriginalTplCodes(), failResendDTO.getRuleDTOList());

        // 通过 tplCode 获取 tplCode 对应的 signCode
        fetchTplAndPopulateSignCode(failResendDTO);
        // 新增策略
        FailResendStrategyDO strategyDO = buildStrategyDO(failResendDTO);
        strategyDO.setStatus(1);
        String strategyId = serialNumberGenerator.generateStrategyId();
        strategyDO.setStrategyId(strategyId);
        strategyDO.setCreator(SsoUserInfoUtil.getUserName());
        strategyDO.setCreateTime(new Date());
        failResendStrategyMapper.insertSelective(strategyDO);
        // 新增策略规则
        List<FailResendRuleDO> ruleDOList = buildStrategyRuleDOList(failResendDTO.getRuleDTOList(), strategyId);
        ruleDOList.forEach(ruleDO -> failResendRuleMapper.insertSelective(ruleDO));

        failResendStrategyService.refreshCache();
        return strategyId;
    }

    @Override
    public String update(FailResendDTO failResendDTO) {
        // 检验参数
        check(failResendDTO);

        FailResendStrategyDO strategyDO = failResendStrategyMapper.selectByStrategyId(failResendDTO.getStrategyId());
        if (strategyDO == null) {
            throw new BizException("失败补发不存在");
        }

        // 判断 tplCode 是否存在绑定策略 绑定了就异常
        List<FailResendStrategyDO> modelDOList = failResendStrategyMapper.selectAll().stream()
                .filter(modelDO -> !Objects.equals(modelDO.getStrategyId(), failResendDTO.getStrategyId()))
                .collect(Collectors.toList());
        checkTplCodeBinding(failResendDTO, modelDOList);

        // 根据原始 tplCode 得到 短信内容参数 和 目标模板 短信内容参数 比较 相等或者 目标模板参数等于 0 就是认为正确的 否则抛异常
        validateTplParameters(failResendDTO.getOriginalTplCodes(), failResendDTO.getRuleDTOList());

        // 通过 tplCode 获取 tplCode 对应的 signCode
        fetchTplAndPopulateSignCode(failResendDTO);

        // 更新策略
        FailResendStrategyDO updateStrategyDO = buildStrategyDO(failResendDTO);
        updateStrategyDO.setId(strategyDO.getId());
        failResendStrategyMapper.updateByPrimaryKeySelective(updateStrategyDO);

        // 更新策略规则 先删除再增加
        failResendRuleMapper.updateDeleteTagByStrategyId(failResendDTO.getStrategyId());
        List<FailResendRuleDO> ruleDOList = buildStrategyRuleDOList(failResendDTO.getRuleDTOList(), strategyDO.getStrategyId());
        ruleDOList.forEach(ruleDO -> failResendRuleMapper.insertSelective(ruleDO));
        failResendStrategyService.refreshCache();
        return strategyDO.getStrategyId();
    }

    @Override
    public String delete(String strategyId) {
        String deleteResult = "success";
        try {
            FailResendStrategyDO strategyDO = failResendStrategyMapper.selectByStrategyId(strategyId);
            if (strategyDO == null) {
                throw new BizException("失败补发不存在");
            }
            failResendStrategyMapper.updateDeleteTagByStrategyId(strategyId);
            failResendRuleMapper.updateDeleteTagByStrategyId(strategyId);
            failResendStrategyService.refreshCache();
        } catch (Exception e) {
            deleteResult = "fail";
        }
        return deleteResult;
    }

    @Override
    public FailResendDetailVO detail(String strategyId) {
        FailResendStrategyDO strategyDO = failResendStrategyMapper.selectByStrategyId(strategyId);
        if (strategyDO == null) {
            throw new BizException("失败补发不存在");
        }
        FailResendDetailVO failResendDetailVO = new FailResendDetailVO();

        failResendDetailVO.setStrategyId(strategyDO.getStrategyId());
        failResendDetailVO.setStrategyName(strategyDO.getStrategyName());
        failResendDetailVO.setTimePeriod(strategyDO.getTimePeriod());
        failResendDetailVO.setOriginalTplCodes(strategyDO.getOriginalTplCodes());
        failResendDetailVO.setRuleList(buildRuleList(strategyDO.getStrategyId()));
        failResendDetailVO.setRemark(strategyDO.getRemark());
        return failResendDetailVO;
    }

    @Override
    public String enable(String strategyId) {
        FailResendStrategyDO strategyDO = failResendStrategyMapper.selectByStrategyId(strategyId);
        if (strategyDO == null) {
            throw new BizException("失败补发不存在");
        }
        strategyDO.setStatus(0);
        failResendStrategyMapper.updateByPrimaryKeySelective(strategyDO);
        failResendStrategyService.refreshCache();
        return "success";
    }

    @Override
    public String disable(String strategyId) {
        FailResendStrategyDO strategyDO = failResendStrategyMapper.selectByStrategyId(strategyId);
        if (strategyDO == null) {
            throw new BizException("失败补发不存在");
        }
        strategyDO.setStatus(1);
        failResendStrategyMapper.updateByPrimaryKeySelective(strategyDO);
        failResendStrategyService.refreshCache();
        return "success";
    }

    private List<FailResendRuleDTO> buildRuleList(String strategyId) {

        List<FailResendRuleDO> ruleDOList = failResendRuleMapper.selectByStrategyId(strategyId);
        List<FailResendRuleDTO> ruleDTOList = new ArrayList<>();
        if (!CommonUtil.isEmpty(ruleDOList)) {
            for (FailResendRuleDO ruleDO : ruleDOList) {
                FailResendRuleDTO ruleDTO = new FailResendRuleDTO();
                ruleDTO.setSceneType(ruleDO.getSceneType());
                ruleDTO.setSceneValue(ruleDO.getSceneValue());
                ruleDTO.setRuleType(ruleDO.getRuleType());
                ruleDTO.setRuleValue(ruleDO.getRuleValue());
                ruleDTOList.add(ruleDTO);
            }
        }
        return ruleDTOList;
    }

    private List<FailResendRuleDO> buildStrategyRuleDOList(List<FailResendRuleDTO> ruleDTOList, String strategyId) {
        List<FailResendRuleDO> failResendRuleDOList = new ArrayList<>();

        Date curDate = new Date();
        for (FailResendRuleDTO ruleDTO : ruleDTOList) {
            FailResendRuleDO ruleDO = new FailResendRuleDO();
            ruleDO.setStrategyId(strategyId);
            ruleDO.setSceneType(StringUtils.isBlank(ruleDTO.getSceneType()) ? "0" : ruleDTO.getSceneType());
            ruleDO.setSceneValue(ruleDTO.getSceneValue());
            ruleDO.setRuleType(ruleDTO.getRuleType());
            ruleDO.setRuleValue(ruleDTO.getRuleValue());
            ruleDO.setIsDelete(0);
            ruleDO.setCreateTime(curDate);
            ruleDO.setUpdateTime(curDate);
            ruleDO.setRuleId(serialNumberGenerator.generateRuleId());
            failResendRuleDOList.add(ruleDO);
        }
        return failResendRuleDOList;
    }

    private FailResendStrategyDO buildStrategyDO(FailResendDTO failResendDTO) {
        FailResendStrategyDO failResendStrategy = new FailResendStrategyDO();
        failResendStrategy.setStrategyName(failResendDTO.getStrategyName());
        failResendStrategy.setTimePeriod(failResendDTO.getTimePeriod());
        failResendStrategy.setOriginalTplCodes(failResendDTO.getOriginalTplCodes());
        failResendStrategy.setUpdateTime(new Date());
        failResendStrategy.setUpdater(SsoUserInfoUtil.getUserName());
        failResendStrategy.setRemark(failResendDTO.getRemark());
        return failResendStrategy;
    }

    private void check(FailResendDTO failResendDTO) {
        if (StringUtils.isBlank(failResendDTO.getOriginalTplCodes())) {
            throw new BizException("请选择模板");
        }
        if (failResendDTO.getRuleDTOList() == null || failResendDTO.getRuleDTOList().isEmpty()) {
            throw new BizException("请添加执行项");
        }
        if (failResendDTO.getRuleDTOList().size() > 1) {
            throw new BizException("执行项最多只能添加一个");
        }
    }

    public FailResendVO buildFailResendVO(FailResendStrategyDO failResendStrategyDO) {
        FailResendVO failResendVO = new FailResendVO();
        BeanUtils.copyProperties(failResendStrategyDO, failResendVO);
        return failResendVO;
    }

    private void checkTplCodeBinding(FailResendDTO failResendDTO, List<FailResendStrategyDO> modelDOList) {
        if (!CollectionUtils.isEmpty(modelDOList)) {
            Set<String> modelTplCodes = new HashSet<>();
            for (FailResendStrategyDO failResendStrategyDO : modelDOList) {
                String[] indexModelTplCodes = failResendStrategyDO.getOriginalTplCodes().split(",");
                modelTplCodes.addAll(Arrays.asList(indexModelTplCodes));
            }
            String[] requestTplCodes = failResendDTO.getOriginalTplCodes().split(",");
            for (String requestTplCode : requestTplCodes) {
                if (modelTplCodes.contains(requestTplCode)) {
                    throw new BizException(String.format("模板编码%s已经绑定规则", requestTplCode));
                }
            }
        }
    }


    private void fetchTplAndPopulateSignCode(FailResendDTO failResendDTO) {
        List<FailResendRuleDTO> ruleDTOList = failResendDTO.getRuleDTOList();
        if (CollectionUtils.isEmpty(ruleDTOList)) {
            return;
        }

        // 准备批量查询的数据
        List<String> allTplCodes = new ArrayList<>();
        Map<String, JSONArray> ruleValueMap = new HashMap<>();

        //收集所有tplCode并建立映射关系
        for (FailResendRuleDTO ruleDTO : ruleDTOList) {
            String ruleValue = ruleDTO.getRuleValue();
            if (StringUtils.isBlank(ruleValue)) continue;

            JSONArray ruleConfigInfo = JSONArray.parseArray(ruleValue);
            if (CollectionUtils.isEmpty(ruleConfigInfo)) continue;

            // 保存每个规则DTO对应的JSONArray
            ruleValueMap.put(ruleValue, ruleConfigInfo);

            // 收集所有tplCode
            for (Object item : ruleConfigInfo) {
                JSONObject ruleConfig = (JSONObject) item;
                String tplCode = ruleConfig.getString("tpl_code");
                if (StringUtils.isNotBlank(tplCode)) {
                    allTplCodes.add(tplCode);
                }
            }
        }

        if (allTplCodes.isEmpty()) return;

        // 批量查询模板
        List<TplDO> modelTplList = tplMapper.selectByCodeList(allTplCodes);
        if (CollectionUtils.isEmpty(modelTplList)) {
            throw new BizException("未找到任何模板信息");
        }

        // 构建模板映射：tplCode -> TplDO
        Map<String, TplDO> tplMap = modelTplList.stream()
                .collect(Collectors.toMap(TplDO::getCode, tpl -> tpl, (v1, v2) -> v1));

        // 准备批量查询签名
        List<Integer> signIds = modelTplList.stream()
                .map(TplDO::getSignId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询签名
        List<SignDO> signList = signMapper.selectByIds(signIds);
        Map<Integer, SignDO> signMap = signList.stream()
                .collect(Collectors.toMap(SignDO::getId, sign -> sign));

        // 处理并填充签名码
        for (FailResendRuleDTO ruleDTO : ruleDTOList) {
            String ruleValue = ruleDTO.getRuleValue();
            JSONArray ruleConfigInfo = ruleValueMap.get(ruleValue);
            if (CollectionUtils.isEmpty(ruleConfigInfo)) continue;

            boolean requiresUpdate = false;
            List<String> processedTplCodes = new ArrayList<>();

            for (Object item : ruleConfigInfo) {
                JSONObject ruleConfig = (JSONObject) item;
                String tplCode = ruleConfig.getString("tpl_code");
                if (StringUtils.isBlank(tplCode)) continue;

                // 验证模板是否存在
                TplDO modelTpl = tplMap.get(tplCode);
                if (modelTpl == null) {
                    throw new BizException(String.format("模板编码 %s 不存在", tplCode));
                }

                // 获取签名
                SignDO signDO = signMap.get(modelTpl.getSignId());
                if (signDO == null) {
                    throw new BizException(String.format("模板 %s 绑定的签名不存在", tplCode));
                }

                // 填充签名码
                if (!signDO.getCode().equals(ruleConfig.getString("sign_ode"))) {
                    ruleConfig.put("sign_code", signDO.getCode());
                    processedTplCodes.add(tplCode);
                    requiresUpdate = true;
                }
            }

            // 更新规则值
            if (requiresUpdate) {
                ruleDTO.setRuleValue(JSON.toJSONString(ruleConfigInfo));
                log.info("更新规则配置: {}, 处理模板: {}", ruleDTO.getSceneValue(), processedTplCodes);
            }
        }
    }

    private void validateTplParameters(String originalTplCodes, List<FailResendRuleDTO> ruleDTOList) {
        // 1. 提取源模板代码
        List<String> sourceTplCodes = Arrays.asList(originalTplCodes.split(","));

        // 2. 提取目标模板代码
        List<String> targetTplCodes = extractTargetTplCodes(ruleDTOList);
        if (targetTplCodes.isEmpty()) {
            throw new BizException("执行项模板未配置");
        }

        // 3. 批量查询模板
        List<TplDO> sourceTplList = tplMapper.selectByCodeList(sourceTplCodes);
        List<TplDO> targetTplList = tplMapper.selectByCodeList(targetTplCodes);

        // 4. 预计算占位符数量
        Map<String, Integer> sourceParamCountMap = createParamCountMap(sourceTplList);
        Map<String, Integer> targetParamCountMap = createParamCountMap(targetTplList);

        // 5. 验证模板参数一致性
        validateParamConsistency(sourceParamCountMap, targetParamCountMap);
    }

    /**
     * 从规则DTO列表中提取目标模板代码
     */
    private List<String> extractTargetTplCodes(List<FailResendRuleDTO> ruleDTOList) {
        return ruleDTOList.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getRuleValue()))
                .flatMap(dto -> {
                    JSONArray ruleConfigInfo = JSONArray.parseArray(dto.getRuleValue());
                    return ruleConfigInfo.stream()
                            .map(item -> (JSONObject) item)
                            .filter(config -> StringUtils.isNotBlank(config.getString("tpl_code")))
                            .map(config -> config.getString("tpl_code"));
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建模板代码到参数数量的映射
     */
    private Map<String, Integer> createParamCountMap(List<TplDO> tplList) {
        return tplList.stream()
                .collect(Collectors.toMap(
                        TplDO::getCode,
                        tpl -> TemplateUtil.countAllPlaceholders(tpl.getContent()),
                        (first, second) -> first
                ));
    }

    /**
     * 验证源模板和目标模板的参数一致性
     */
    private void validateParamConsistency(Map<String, Integer> sourceParamCountMap,
                                          Map<String, Integer> targetParamCountMap) {
        for (Map.Entry<String, Integer> targetEntry : targetParamCountMap.entrySet()) {
            String tplCode = targetEntry.getKey();
            int targetCount = targetEntry.getValue();
            log.info("目标模板参数数量：{}", targetCount);
            // 目标模板参数为0总是有效
            if (targetCount == 0) continue;

            // 验证每个源模板的参数匹配
            for (Map.Entry<String, Integer> sourceEntry : sourceParamCountMap.entrySet()) {
                int sourceCount = sourceEntry.getValue();

                log.info("sourceCount: {}, targetCount: {}", sourceCount, targetCount);

                if (sourceCount != targetCount) {
                    throw new BizException(String.format(
                            "模板 %s 和模板 %s 的参数数量不匹配 (源: %d, 目标: %d)",
                            sourceEntry.getKey(), tplCode, sourceCount, targetCount
                    ));
                }
            }
        }
    }
}
