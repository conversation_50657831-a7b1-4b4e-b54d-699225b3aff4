package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.CodeMappingDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.CodeMappingVO;
import com.xhqb.spectre.admin.service.CodeMappingService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.CodeMappingQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 映射码
 *
 * <AUTHOR>
 * @date 2021/10/20
 */
@RestController
@RequestMapping("/codeMapping")
@Slf4j
public class CodeMappingController {

    @Resource
    private CodeMappingService codeMappingService;

    /**
     * 查询映射码列表
     *
     * @param codeMappingQuery 映射码查询条件
     * @param pageNum          当前页码
     * @param pageSize         一页显示的记录数
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(CodeMappingQuery codeMappingQuery, Integer pageNum, Integer pageSize) {
        codeMappingQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<CodeMappingVO> commonPager = codeMappingService.listByPage(codeMappingQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询错误码详情
     * <p>
     * 必须包含
     * channelCod
     * type
     * channelErrCode
     *
     * @param codeMappingQuery
     * @return
     */
    @PostMapping("/detail")
    public AdminResult queryInfo(@RequestBody CodeMappingQuery codeMappingQuery) {
        return AdminResult.success(codeMappingService.getByPrimaryKey(codeMappingQuery.getChannelCode(), codeMappingQuery.getType(), codeMappingQuery.getChannelErrCode()));
    }

    /**
     * 添加映射码
     *
     * @param codeMappingDTO 新增的映射码内容
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_ERR_CODE_MAPPING)
    public AdminResult createInfo(@RequestBody CodeMappingDTO codeMappingDTO) {
        log.info("create codeMappingDTO = {}", JSON.toJSONString(codeMappingDTO));
        codeMappingService.create(codeMappingDTO);
        return AdminResult.success();
    }

    /**
     * 更新映射码
     *
     * @param codeMappingDTO 更新的映射码内容
     * @return
     */
    @PutMapping("")
    @LogOpTime(OpLogConstant.MODULE_ERR_CODE_MAPPING)
    public AdminResult updateInfo(@RequestBody CodeMappingDTO codeMappingDTO) {
        log.info("update codeMappingDTO = {}", JSON.toJSONString(codeMappingDTO));
        codeMappingService.update(codeMappingDTO);
        return AdminResult.success();
    }
}
