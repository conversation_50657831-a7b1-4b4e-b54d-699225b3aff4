package com.xhqb.spectre.admin.batchtask.ab;

import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xhqb.spectre.admin.batchtask.limit.BatchSubmitRateLimitService;
import com.xhqb.spectre.admin.config.VenusConfig;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.mq.consumer.handler.BatchTaskParamConsumerHandler;
import com.xhqb.spectre.admin.mq.event.TaskParamResult;
import com.xhqb.spectre.admin.mq.message.BatchTaskParamMessage;
import com.xhqb.spectre.admin.util.CommonUtil;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.*;
import com.xhqb.spectre.common.dal.mapper.*;
import com.xhqb.spectre.common.enums.BatchTaskStatusEnum;
import com.xhqb.spectre.common.enums.DeleteEnum;
import com.xhqb.ucenter.sso.client.rpc.SsoUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 群发任务灰度提交
 *
 * <AUTHOR>
 * @date 2021/12/29
 */
@Component
@Slf4j
public class BatchTaskAbSubmitHandler {

    /**
     * 群发任务灰度提交线程池
     */
    private static final ExecutorService AB_SUBMIT_HANDLER_POOL = new ThreadPoolExecutor(
            8,
            40,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1024),
            new ThreadFactoryBuilder().setNameFormat("ab-submit-pool-%d").build(),
            new ThreadPoolExecutor.AbortPolicy()
    );

    @Resource
    private BatchTaskMapper batchTaskMapper;
    @Resource
    private BatchTaskParamMapper batchTaskParamMapper;
    @Resource
    private TplMapper tplMapper;
    @Resource
    private SignMapper signMapper;
    @Resource
    private BatchAbJobMapper batchAbJobMapper;
    @Autowired
    private VenusConfig venusConfig;
    @Resource
    private BatchTaskParamConsumerHandler batchTaskParamConsumerHandler;
    @Resource
    private BatchSubmitRateLimitService batchSubmitRateLimitService;

    /**
     * 群发任务灰度提交处理
     * <p>
     * 1. 检测群发任务状态是否合法(群发任务状态只能为待提交或者灰度结束)
     * 2. 提取指定数量的灰度数据
     * 3. 设置群发任务状态为灰度中
     * 4. 将灰度任务写入数据库中供警告任务做触达率计算
     * 5. 发送灰度数据
     *
     * @param taskId
     * @return
     */
    public AdminResult abSubmitHandler(Integer taskId) {
        log.info("群发任务灰度提交处理,taskId = {},user = {}", taskId, SsoUserInfoUtil.getUserName());
        BatchTaskDO batchTaskDO = batchTaskMapper.selectByPrimaryKey(taskId);
        // 群发任务状态检测
        AdminResult checkResult = this.checkTaskStatus(batchTaskDO, taskId);
        if (Objects.nonNull(checkResult)) {
            // 检测不通过
            return checkResult;
        }

        // 提取指定数量的灰度数据
        List<BatchTaskParamDO> batchTaskParamList = batchTaskParamMapper.fetchAbDataList(taskId, venusConfig.getAbSegmentSize());
        if (CommonUtil.isEmpty(batchTaskParamList)) {
            log.warn("群发任务灰度提交处理,未查询到有效的分片数据,taskId = {}", taskId);
            return AdminResult.error("未查询到有效的分片数据");
        }

        // 加入限流
        batchSubmitRateLimitService.join(batchTaskDO.getId(), batchTaskDO.getLimitRate());

        List<BatchTaskParamMessage> paramMessageList = this.buildParamMessage(batchTaskDO, batchTaskParamList);
        // 设置群发任务状态
        this.setBatchTaskStatus(taskId);

        // 保存灰度任务
        this.saveAbJob(taskId, batchTaskParamList);

        // 加入线程池处理
        AB_SUBMIT_HANDLER_POOL.execute(() -> doSendHandler(taskId, paramMessageList));

        return AdminResult.success();
    }

    /**
     * 检测群发任务状态是否合法(群发任务状态只能为待提交或者灰度结束)
     *
     * @param batchTaskDO
     * @param taskId
     * @return 返回空表示验证通过
     */
    private AdminResult checkTaskStatus(BatchTaskDO batchTaskDO, Integer taskId) {
        if (Objects.isNull(batchTaskDO)) {
            log.warn("群发任务灰度提交处理,群发任务不存在,taskId = {}", taskId);
            return AdminResult.error("群发任务不存在");
        }

        Integer deleteStatus = batchTaskDO.getIsDelete();
        if (Objects.equals(DeleteEnum.DELETED.getCode(), deleteStatus)) {
            log.warn("群发任务灰度提交处理,群发任务已被删除,batchTaskDO = {}", JSON.toJSONString(batchTaskDO));
            return AdminResult.error("群发任务已被删除");
        }

        // 状态 0：待提交；1：已提交；2：发送中；3：已发送；4：灰度中；5：灰度结束；9：已取消
        Integer status = batchTaskDO.getStatus();
        // 群发任务状态只能为待提交或者灰度结束 才能够进入到灰度提交
        boolean isValid = Objects.equals(status, BatchTaskStatusEnum.UN_COMMIT.getCode()) || Objects.equals(status, BatchTaskStatusEnum.AB_WARNING.getCode());
        if (!isValid) {
            log.warn("群发任务灰度提交处理,群发任务状态不合法,status = {},taskId = {}", status, taskId);
            return AdminResult.error("群发任务状态不合法");
        }

        // 检测通过
        return null;
    }

    /**
     * 构建参数消息
     *
     * @param batchTaskDO
     * @param batchTaskParamList
     * @return
     */
    private List<BatchTaskParamMessage> buildParamMessage(BatchTaskDO batchTaskDO, List<BatchTaskParamDO> batchTaskParamList) {
        List<BatchTaskParamMessage> messageList = new ArrayList<>();
        BatchTaskParamMessage batchTaskParamMessage = this.buildBaseMessage(batchTaskDO);
        String messageJson = JSON.toJSONString(batchTaskParamMessage);
        for (BatchTaskParamDO batchTaskParamDO : batchTaskParamList) {
            batchTaskParamMessage = JSON.parseObject(messageJson, BatchTaskParamMessage.class);
            batchTaskParamMessage.setTaskParamId(batchTaskParamDO.getId());
            messageList.add(batchTaskParamMessage);
        }
        return messageList;
    }


    /**
     * 构建基础的群发分片消息对象
     *
     * @param batchTaskDO
     * @return
     */
    private BatchTaskParamMessage buildBaseMessage(BatchTaskDO batchTaskDO) {
        Integer taskId = batchTaskDO.getId();
        TplDO tplDO = tplMapper.selectByPrimaryKey(batchTaskDO.getTplId());
        if (Objects.isNull(tplDO)) {
            log.warn("群发任务灰度提交处理,短信模板不存在,taskId = {}", taskId);
            throw new BizException("短信模板不存在");
        }
        SignDO signDO = signMapper.selectByPrimaryKey(batchTaskDO.getSignId());
        if (Objects.isNull(signDO)) {
            log.warn("群发任务灰度提交处理,签名信息不存在,taskId = {}", taskId);
            throw new BizException("签名信息不存在");
        }

        return BatchTaskParamMessage.builder()
                .taskId(taskId)
                // .taskParamId(taskParamDO.getId())
                .appCode(batchTaskDO.getAppCode())
                .tplCode(tplDO.getCode())
                .signName(signDO.getName())
                // 设置签名编码
                .signCode(signDO.getCode())
                .smsCodeType(tplDO.getSmsTypeCode())
                // 设置模板内容类型 2022-02-22
                .tplType(batchTaskDO.getTplType())
                .build();
    }

    /**
     * 更新群发任务状态为灰度中
     *
     * @param taskId
     */
    private void setBatchTaskStatus(Integer taskId) {
        BatchTaskDO batchTaskDO = new BatchTaskDO();
        batchTaskDO.setId(taskId);
        batchTaskDO.setStatus(BatchTaskStatusEnum.AB_SUBMIT.getCode());

        String userName = SsoUserInfoUtil.getUserName();
        if (StringUtils.isNotBlank(userName)) {
            batchTaskDO.setUpdater(userName);
        }

        batchTaskMapper.updateByPrimaryKeySelective(batchTaskDO);
        log.info("群发任务灰度提交处理,更新群发任务状态为灰度中,taskId = {}", taskId);
    }

    /**
     * 保存灰度任务
     *
     * @param taskId
     * @param batchTaskParamList
     */
    private void saveAbJob(Integer taskId, List<BatchTaskParamDO> batchTaskParamList) {
        List<Integer> collect = batchTaskParamList.stream().map(s -> s.getId()).collect(Collectors.toList());
        BatchAbJobDO batchAbJobDO = new BatchAbJobDO();
        batchAbJobDO.setTaskId(taskId);
        batchAbJobDO.setContent(JSON.toJSONString(collect));

        SsoUser subject = SsoUserInfoUtil.getSubject();
        if (Objects.nonNull(subject)) {
            // 设置提交者信息
            batchAbJobDO.setCreator(subject.getUserId());
            batchAbJobDO.setMobile(subject.getPhone());
        }

        batchAbJobMapper.insertSelective(batchAbJobDO);
        log.info("群发任务灰度提交处理,保存群发灰度任务成功,taskId = {}", taskId);
    }

    /**
     * 做短信发送处理
     *
     * @param taskId
     * @param paramMessageList
     */
    private void doSendHandler(Integer taskId, List<BatchTaskParamMessage> paramMessageList) {
        long start;
        // 发送的总数
        int total = 0;
        // 发送成功数
        int success = 0;
        TaskParamResult taskParamResult;
        for (BatchTaskParamMessage batchTaskParamMessage : paramMessageList) {
            start = System.currentTimeMillis();
            taskParamResult = batchTaskParamConsumerHandler.doHandler(batchTaskParamMessage, start);
            if (Objects.nonNull(taskParamResult)) {
                total += taskParamResult.getTotal();
                success += taskParamResult.getSuccess();
            }
        }
        BatchTaskDO batchTaskDO = batchTaskMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(batchTaskDO)) {
            log.warn("群发任务灰度提交处理,更新发送结果时群发任务数据不存在,taskId = {}", taskId);
            return;
        }

        // 总记录数
        int totalCount = CommonUtil.toInt(batchTaskDO.getTotalCount());

        // 新发送成功的短信数量
        int newSendCount = success + CommonUtil.toInt(batchTaskDO.getRealSendCount());
        // 新调用次数短信数量
        int newSentCount = total + CommonUtil.toInt(batchTaskDO.getSentCount());
        // 控制数据不超标显示
        if (newSendCount > totalCount) {
            log.info("群发任务灰度提交处理,实际多发了一些数据,newSendCount = {},taskId = {}", newSendCount, taskId);
            newSendCount = totalCount;
        }

        // 更新发送
        BatchTaskDO updateTask = new BatchTaskDO();

        if (newSentCount >= totalCount) {
            log.info("群发任务灰度提交处理,实际多发了一些数据,设置群发任务状态为已发送,newSentCount = {},taskId = {}", newSentCount, taskId);
            newSentCount = totalCount;
            // 短信数据已经发送完毕了 群发任务已结束
            updateTask.setStatus(BatchTaskStatusEnum.SENT.getCode());
        }

        updateTask.setId(taskId);
        updateTask.setRealSendCount(newSendCount);
        updateTask.setSentCount(newSentCount);
        batchTaskMapper.updateByPrimaryKeySelective(updateTask);
        log.info("更新群发任务发送数量成功, realSendCount = {},sentCount = {}, taskId = {}", updateTask.getRealSendCount(), updateTask.getSentCount(), taskId);
    }
}
