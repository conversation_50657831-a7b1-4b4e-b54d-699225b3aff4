package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.xhqb.spectre.admin.batchtask.BatchTaskFactory;
import com.xhqb.spectre.admin.batchtask.ab.BatchTaskAbJobHandler;
import com.xhqb.spectre.admin.batchtask.ab.BatchTaskAbSubmitHandler;
import com.xhqb.spectre.admin.batchtask.ab.BatchTaskForceSubmitHandler;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.batchtask.detect.BatchDetectDownHandler;
import com.xhqb.spectre.admin.batchtask.io.BatchParamExport;
import com.xhqb.spectre.admin.batchtask.io.UploadFileUtils;
import com.xhqb.spectre.admin.batchtask.result.impl.QueryUploadResultHandler;
import com.xhqb.spectre.admin.batchtask.strategy.FileLoadContext;
import com.xhqb.spectre.admin.batchtask.utils.BatchTaskUtils;
import com.xhqb.spectre.admin.model.dto.BatchTaskDTO;
import com.xhqb.spectre.admin.model.dto.BatchTaskSendDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.BatchTaskVO;
import com.xhqb.spectre.admin.service.BatchTaskService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.BatchAbJobDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.BatchTaskQuery;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Map;

/**
 * 群发短信
 *
 * <AUTHOR>
 * @date 2021/9/17
 */
@RestController
@RequestMapping("/batchTask")
@Slf4j
public class BatchTaskController {

    @Autowired
    private BatchTaskService batchTaskService;
    @Autowired
    private BatchTaskFactory batchTaskFactory;
    @Resource
    private QueryUploadResultHandler queryUploadResultHandler;
    @Resource
    private BatchParamExport batchParamExport;
    @Resource
    private BatchTaskAbSubmitHandler batchTaskAbSubmitHandler;
    @Resource
    private BatchTaskForceSubmitHandler batchTaskForceSubmitHandler;
    @Resource
    private BatchTaskAbJobHandler batchTaskAbJobHandler;
    @Resource
    private BatchDetectDownHandler batchDetectDownHandler;

    /**
     * 查询群发任务列表
     *
     * @param batchTaskQuery 群发短信任务查询条件
     * @param pageNum        当前页码
     * @param pageSize       一页显示的记录数
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(BatchTaskQuery batchTaskQuery, Integer pageNum, Integer pageSize) {
        batchTaskQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<BatchTaskVO> commonPager = batchTaskService.listByPage(batchTaskQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询群发任务详情
     *
     * @param id 群发短信批次号(主键)
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(batchTaskService.getById(id));
    }

    /**
     * 添加群发任务
     *
     * @param batchTaskDTO 新增的群发任务内容
     * @return
     */
    @PostMapping("")
    public AdminResult createInfo(@RequestBody BatchTaskDTO batchTaskDTO) {
        log.info("create batchTaskDTO = {}", JSON.toJSONString(batchTaskDTO));
        batchTaskService.create(batchTaskDTO, null);
        return AdminResult.success();
    }

    /**
     * 更新群发任务接口
     *
     * @param id           群发短信批次号(主键)
     * @param batchTaskDTO 修改的群发任务内容
     * @return
     */
    @PutMapping("/{id}")
    public AdminResult updateInfo(@PathVariable("id") Integer id, @RequestBody BatchTaskDTO batchTaskDTO) {
        log.info("update batchTaskDTO = {}, id = {}", JSON.toJSONString(batchTaskDTO), id);
        batchTaskService.update(id, batchTaskDTO);
        return AdminResult.success();
    }

    /**
     * 取消群发任务接口
     *
     * @param id 群发短信批次号(主键)
     * @return
     */
    @PutMapping("/cancel/{id}")
    public AdminResult cancelBatchTask(@PathVariable("id") Integer id) {
        log.info("cancelBatchTask id = {}", id);
        batchTaskService.cancelBatchTask(id);
        return AdminResult.success();
    }

    /**
     * 删除群发任务接口
     *
     * @param id 群发短信批次号(主键)
     * @return
     */
    @PutMapping("/delete/{id}")
    public AdminResult deleteBatchTask(@PathVariable("id") Integer id) {
        log.info("deleteBatchTask id = {}", id);
        batchTaskService.deleteBatchTask(id);
        return AdminResult.success();
    }

    /**
     * 文件上传接口
     *
     * @param file        文件上传新
     * @param tplId       模板类型
     * @param signId      签名ID
     * @param phoneStatus 手机状态检测  多值使用逗号分割  (空号,停机)
     * @return
     * @throws IOException
     */
    @PostMapping("/web/upload")
    public AdminResult webUpload(@RequestParam("file") MultipartFile file,
                                 @RequestParam(value = "tplId", required = false) Integer tplId,
                                 @RequestParam(value = "signId", required = false) String signId,
                                 @RequestParam(value = "phoneStatus", required = false) String phoneStatus,
                                 @RequestParam(value = "smsTypeCode", required = false) String smsTypeCode) throws IOException {
        String originalFilename = file.getOriginalFilename();
        log.info("文件上传fileName = {}, tplId = {}, signId = {}, phoneStatus = {}, username = {}",
                originalFilename, tplId, signId, phoneStatus, SsoUserInfoUtil.getUserName());

        if (StringUtils.isBlank(smsTypeCode)) {
            return AdminResult.error("文件上传未提供短信类型");
        }

        // 修复群发克隆任务phoneStatus为undefined的情况
        if (StringUtils.equalsIgnoreCase(phoneStatus, BatchTaskConstants.Commons.UNDEFINED)) {
            phoneStatus = "";
        }
        int toUseSignId = 0;
        if (StringUtils.isNotBlank(signId)) {
            try {
                // 将signId转换成整数
                // 没有选择签名时 前端会传一个undefined值给后台 .>.
                toUseSignId = Integer.parseInt(signId);
            } catch (Exception e) {
                // ignore e
                log.warn("传入的签名id值不合法,signId = {}", signId);
                return AdminResult.error("请先选择签名再进行文件上传");
            }
        }

        // 上传的文件是否是csv
        boolean isCsv = StringUtils.endsWithAny(originalFilename, ".csv");
        if (!isCsv) {
            // 不是csv则判断是否是excel文件
            if (!BatchTaskUtils.isExcel(file.getInputStream(), originalFilename)) {
                log.warn("当前文件不是合法的excel文件, fileName = {}", originalFilename);
                return AdminResult.error("文件类型不正确");
            }
        }

        File localFile = UploadFileUtils.saveFile(file);
        FileLoadContext context = FileLoadContext.builder()
                .file(localFile)
                // 不过滤cos文件上传
                .filterCos(false)
                .build();
        Map<String, String> uploadResult = batchTaskFactory.asyncHandler(context, originalFilename, toUseSignId, phoneStatus, smsTypeCode, tplId);
        return AdminResult.success(uploadResult);
    }

    /**
     * 地址上传接口
     *
     * @param uploadUrl   文件解析地址
     * @param signId      签名ID
     * @param phoneStatus 手机状态检测  多值使用逗号分割  (空号,停机)
     * @return
     * @throws IOException
     */
    @PostMapping("/url/upload")
    public AdminResult urlUpload(@RequestBody String uploadUrl,
                                 @RequestParam(value = "tplId", required = false) Integer tplId,
                                 @RequestParam(value = "signId", required = false) Integer signId,
                                 @RequestParam(value = "phoneStatus", required = false) String phoneStatus,
                                 @RequestParam(value = "smsTypeCode", required = false) String smsTypeCode) throws IOException {
        // 处理文件url上传
        String originalFilename = uploadUrl.substring(uploadUrl.lastIndexOf('/') + 1);
        log.info("地址上传fileName = {},signId = {},phoneStatus = {}, username = {}", originalFilename, signId, phoneStatus, SsoUserInfoUtil.getUserName());
        File localFile = UploadFileUtils.saveFile(uploadUrl);
        FileLoadContext context = FileLoadContext.builder()
                .file(localFile)
                // 过滤cos文件上传
                .filterCos(true)
                .build();
        Map<String, String> uploadResult = batchTaskFactory.asyncHandler(context, originalFilename, signId, phoneStatus, smsTypeCode, tplId);
        return AdminResult.success(uploadResult);
    }

    /**
     * 文件上传查询接口
     *
     * @param taskNo
     * @return
     */
    @GetMapping("/upload/query/{taskNo}")
    public AdminResult uploadQuery(@PathVariable("taskNo") String taskNo) {
        return AdminResult.success(queryUploadResultHandler.handler(taskNo));
    }

    /**
     * 群发任务提交接口
     *
     * @param batchTaskSendDTO
     * @return
     */
    @PostMapping("submit")
    public AdminResult submit(@RequestBody BatchTaskSendDTO batchTaskSendDTO) {
        log.info("batch task submit id = {}", batchTaskSendDTO.getId());
        batchTaskService.submit(batchTaskSendDTO.getId());
        return AdminResult.success();
    }

    /**
     * 测试短信接口
     *
     * @param batchTaskSendDTO
     * @return
     */
    @PostMapping("send")
    public AdminResult send(@RequestBody BatchTaskSendDTO batchTaskSendDTO) {
        log.info("batch task test send ,batchTaskSendDTO = {}", JSON.toJSONString(batchTaskSendDTO));
        Map<String, Object> sendFailMap = batchTaskService.send(batchTaskSendDTO.getId(), batchTaskSendDTO);
        return AdminResult.success(sendFailMap);
    }


    /**
     * 群发任务克隆接口
     *
     * @param batchTaskSendDTO
     * @return
     */
    @PostMapping("copy")
    public AdminResult copy(@RequestBody BatchTaskSendDTO batchTaskSendDTO) {
        log.info("batch task submit id = {}", batchTaskSendDTO.getId());
        batchTaskService.copy(batchTaskSendDTO.getId());
        return AdminResult.success();
    }

    /**
     * 重置群发任务状态
     * <p>
     * 只会设置为默认状态
     *
     * @return
     */
    @PostMapping("/resetStatus")
    public AdminResult resetStatus(Integer id) {
        log.info("batch task resetStatus id = {}", id);
        batchTaskService.resetStatus(id);
        return AdminResult.success();
    }

    /**
     * 获取下载文件路径
     *
     * @param fileName
     * @return
     */
    @GetMapping("/downloadUrl/{fileName}")
    public AdminResult downloadUrl(@PathVariable("fileName") String fileName) {
        Map<Object, Object> result = ImmutableMap.builder().put("downloadUrl", batchTaskService.downloadUrl(fileName)).build();
        return AdminResult.success(result);
    }

    /**
     * * 群发参数导出
     * <p>
     * 主要导出失败的参数信息
     *
     * @param taskId
     * @param response
     * @throws Exception
     */
    @GetMapping("/export")
    public void export(Integer taskId, HttpServletResponse response) throws Exception {
        log.info("群发参数导出 id = {}", taskId);
        batchParamExport.export(taskId, response);
    }

    /**
     * 群发任务灰度提交接口
     *
     * @param batchTaskSendDTO
     * @return
     */
    @PostMapping("/abSubmit")
    public AdminResult abSubmit(@RequestBody BatchTaskSendDTO batchTaskSendDTO) {
        return batchTaskAbSubmitHandler.abSubmitHandler(batchTaskSendDTO.getId());
    }

    /**
     * 群发任务灰度强制提交接口
     *
     * @param batchTaskSendDTO
     * @return
     */
    @PostMapping("/abForceSubmit")
    public AdminResult abForceSubmit(@RequestBody BatchTaskSendDTO batchTaskSendDTO) {
        return batchTaskForceSubmitHandler.abForceSubmit(batchTaskSendDTO.getId());
    }

    /**
     * 告警通知
     *
     * @param abJobDO
     * @return
     */
    @PostMapping("/warningNotify")
    public AdminResult warningNotify(@RequestBody BatchAbJobDO abJobDO) {
        // abJobDO.getTaskId(), abJobDO.getRate(), abJobDO.getMobile()
        batchTaskAbJobHandler.sendWarningMessage(abJobDO);
        return AdminResult.success();
    }


    /**
     * 群发文件检测结果导出
     * <p>
     * 主要导出群发文件上传检测过滤的数据
     *
     * @param taskId
     * @param downType 下载类型 0->无效CID 1->空号类型 2->停机类型 3->参数缺失 4->数据重复  ，为空时查询所有
     * @param response
     * @throws Exception
     */
    @GetMapping("/detectDown")
    public void detectDown(Integer taskId, Integer downType, HttpServletResponse response) throws Exception {
        log.info("群发参数导出 id = {}", taskId);
        batchDetectDownHandler.down(taskId, downType, response);
    }
}
