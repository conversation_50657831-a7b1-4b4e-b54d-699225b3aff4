package com.xhqb.spectre.admin.model.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/21 17:40
 * @Description:
 */
@Data
public class AppDTO implements Serializable {

    private static final long serialVersionUID = 5780602967505862495L;

    private Integer id;

    private String code;

    @NotBlank(message = "应用名称不能为空")
    @Size(max = 32, message = "应用名称最大为{max}个字符")
    private String name;

//    @NotBlank(message = "回调url不能为空")
//    @Size(max = 256, message = "回调url最大为{max}个字符")
//    private String cbUrl;

    /**
     * 短信内容API类型，0：不支持；1：支持；2：支持并且免模板检测
     */
    @NotNull(message = "请选择短信内容API类型")
    @Range(min = 0, max = 2, message = "短信内容API类型有误")
    private Integer contentApiType;

    //    @NotBlank(message = "应用描述不能为空")
    @Size(max = 256, message = "应用描述最大为{max}个字符")
    private String description;
}
