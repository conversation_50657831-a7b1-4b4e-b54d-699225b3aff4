package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.dto.ChannelTestTplDTO;
import com.xhqb.spectre.admin.model.vo.ChannelTestTplDetailVO;
import com.xhqb.spectre.admin.model.vo.ChannelTestTplVO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.ChannelTestTplQuery;

public interface ChannelTestTplService {
    CommonPager<ChannelTestTplVO> listByPage(ChannelTestTplQuery channelTestTplQuery);

    Long add(ChannelTestTplDTO channelTestTplDTO);

    Long update(ChannelTestTplDTO channelTestTplDTO);

    ChannelTestTplDetailVO detail(Long id);

    void enable(Long id);

    void disable(Long id);
}
