package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.model.vo.SmsUplinkVO;
import com.xhqb.spectre.admin.model.vo.UplinkRelationOrderVO;
import com.xhqb.spectre.common.dal.entity.SmsUplinkDO;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.query.SmsUplinkQuery;

import java.util.List;

/**
 * 上行短信
 *
 * <AUTHOR>
 * @date 2021/10/13
 */
public interface SmsUplinkService {

    /**
     * 分页查询上行短信列表
     *
     * @param smsUplinkQuery
     * @return
     */
    CommonPager<SmsUplinkVO> listByPage(SmsUplinkQuery smsUplinkQuery);

    /**
     * 关联发送记录列表
     *
     * @param id
     * @return
     */
    CommonPager<UplinkRelationOrderVO> relationOrder(Long id);


    /**
     * 上行短信扫描
     *
     * @param lastId
     * @param pageSize
     * @return
     */
    List<SmsUplinkDO> scanUplink(Long lastId, Integer pageSize);

    int updateByPrimaryKeySelective(SmsUplinkDO record);


    /**
     * 从指定id查询指定条数的上行短信
     * @param lastSmsUplinkId  上一条记录id
     * @param pageSize  查询数量大小
     * @return
     */
    Long selectIdByMarket(Long lastSmsUplinkId, int pageSize,List<SmsUplinkDO>smsUplinkDOList);



}
