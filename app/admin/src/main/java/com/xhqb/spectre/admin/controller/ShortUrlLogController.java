package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.service.ShortUrlLogService;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ShortUrlLogQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/8/8 11:57
 **/
@RestController
@RequestMapping("/shortUrlLog")
public class ShortUrlLogController {
    @Autowired
    private ShortUrlLogService shortUrlLogService;

    /**
     * 查询列表数据
     * @param shortUrlLogQuery  查询参数
     * @param pageNum  当前页
     * @param pageSize  每页大小
     * @return  响应
     */
    @GetMapping("")
    public AdminResult queryList(@ModelAttribute ShortUrlLogQuery shortUrlLogQuery, Integer pageNum, Integer pageSize) {
        shortUrlLogQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        return AdminResult.success(shortUrlLogService.listByPage(shortUrlLogQuery));
    }

    /**
     * 查询列表总数
     * @param shortUrlLogQuery  查询参数
     * @return  响应  总数据
     */
    @GetMapping("/queryTotalCount")
    public AdminResult queryTotalCount(@ModelAttribute ShortUrlLogQuery shortUrlLogQuery) {
        return AdminResult.success(shortUrlLogService.queryTotalCount(shortUrlLogQuery));
    }
}
