package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.AddProjectDescDTO;
import com.xhqb.spectre.admin.model.dto.UpdateProjectDescDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.ProjectDescVO;
import com.xhqb.spectre.admin.service.ProjectDescService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.ProjectDescQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/projectDesc")
@Slf4j
public class ProjectDescController {

    @Autowired
    private ProjectDescService projectDescService;

    /**
     * 查询项目用途列表
     *
     * @param projectDescQuery
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryProjectDescList(@ModelAttribute ProjectDescQuery projectDescQuery, Integer pageNum, Integer pageSize) {
        projectDescQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<ProjectDescVO> commonPager = projectDescService.listByPage(projectDescQuery);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询项目用途详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(projectDescService.getById(id));
    }

    /**
     * 添加项目用途
     *
     * @param addProjectDescDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_PROJECT_DESC)
    public AdminResult createProjectDesc(@RequestBody AddProjectDescDTO addProjectDescDTO) {
        log.info("addProjectDescDTO: {}", JSON.toJSONString(addProjectDescDTO));
        if (addProjectDescDTO.getStatus() != 0) {
            addProjectDescDTO.setStatus(1);
        }
        projectDescService.create(addProjectDescDTO);
        return AdminResult.success();
    }

    /**
     * 修改项目用途
     *
     * @param id
     * @param updateProjectDescDTO
     * @return
     */
    @PutMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_PROJECT_DESC)
    public AdminResult updateProjectDesc(@PathVariable("id") Integer id, @RequestBody UpdateProjectDescDTO updateProjectDescDTO) {
        updateProjectDescDTO.setId(id);
        log.info("updateProjectDescDTO: {}", updateProjectDescDTO);
        projectDescService.update(updateProjectDescDTO);
        return AdminResult.success();
    }

    /**
     * 启用项目用途
     *
     * @param id
     * @return
     */
    @PostMapping("/enable/{id}")
    @LogOpTime(OpLogConstant.MODULE_PROJECT_DESC)
    public AdminResult enableProjectDesc(@PathVariable("id") Integer id) {
        log.info("enableProjectDesc, id: {}", id);
        projectDescService.enable(id);
        return AdminResult.success();
    }

    /**
     * 停用项目用途
     *
     * @param id
     * @return
     */
    @PostMapping("/disable/{id}")
    @LogOpTime(OpLogConstant.MODULE_PROJECT_DESC)
    public AdminResult disableProjectDesc(@PathVariable("id") Integer id) {
        log.info("disableProjectDesc, id: {}", id);
        projectDescService.disable(id);
        return AdminResult.success();
    }

    /**
     * 删除项目用途
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_PROJECT_DESC)
    public AdminResult deleteProjectDesc(@PathVariable("id") Integer id) {
        log.info("deleteProjectDesc, id: {}", id);
        projectDescService.delete(id);
        return AdminResult.success();
    }

    /**
     * 查询项目用途枚举
     *
     * @param status
     * @return
     */
    @GetMapping("/enum")
    public AdminResult queryEnum(Integer status) {
        return AdminResult.success(projectDescService.queryEnum(status));
    }
}
