package com.xhqb.spectre.admin.batchtask.upload.cos;

import cn.hutool.crypto.SecureUtil;
import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.*;
import com.xhqb.kael.util.datetime.DateTool;
import com.xhqb.spectre.admin.batchtask.constants.BatchTaskConstants;
import com.xhqb.spectre.admin.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URL;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class S3Helper {

    private static final char SLASH_BAR_CH = '/';
    private static final String SLASH_BAR_STR = "/";
    public static final String COS_ADMIN_DIR = "/admin";


    /**
     * 临时文件存储位置
     */
    public static final String TMP_SAVE_DIR = "/tmp/upload/";

    @Autowired
    private AmazonS3Client amazonS3Client;
    @Value("${spectre.admin.cos.bucketName:/xh-service-test}")
    private String bucketName;
    /**
     * 文件过期时间(单位毫秒,默认30分钟)
     */
    @Value("${spectre.admin.cos.expirationMillis:1800000}")
    private Integer expirationMillis;

    /**
     * 下载文件过期时间(单位毫秒,默认3天)
     */
    @Value("${spectre.admin.cos.download.expirationMillis:259200000}")
    private Long downloadExpirationMillis;


    private static final Logger logger = LoggerFactory.getLogger(S3Helper.class);

    /**
     * 生成admin url地址
     *
     * @param objectName
     * @return
     */
    public String preAdminUrl(String objectName) {
        return preSignedUrl(bucketName + BatchTaskConstants.Commons.COS_ADMIN_DIR, objectName, HttpMethod.GET);
    }

    /**
     * 生成大数据url地址
     *
     * @param objectName
     * @param isOriginal 是否属于原生的文件地址 true->是 false->不是[需要进行文件地址的转换]
     * @return
     */
    public String preBigdataUrl(String objectName, boolean isOriginal) {
        if (Objects.equals(isOriginal, true)) {
            // 获取最后一个斜杠的位置
            return preSignedUrl(bucketName + BatchTaskConstants.Commons.COS_BIGDATA_DIR, objectName, HttpMethod.GET);
        }
        // 如果是以bigdata开头的那么查询bigdata目录
        // 移除 bigdata$_$ 前缀 获取到真正的文件地址
        int prefixLen = BatchTaskConstants.Commons.COS_BIGDATA_FLAG.length() + BatchTaskConstants.Commons.COS_FILE_NAME_SPLIT.length();
        objectName = objectName.substring(prefixLen);
        return preSignedUrl(bucketName + BatchTaskConstants.Commons.COS_BIGDATA_DIR, objectName, HttpMethod.GET);
    }

    /**
     * 根据文件名及文件路径生成一个预签名的URL
     *
     * @param path
     * @param objectName
     * @return
     */
    public String preSignedUrl(String path, String objectName) {
        return preSignedUrl(path, objectName, HttpMethod.GET);
    }

    public String preSignedUrl(String bucketName, String objectName, HttpMethod httpMethod) {
        // 重置桶名路径和文件名称
        return getEntry(bucketName, objectName, httpMethod, expirationMillis);

    }

    public String preSignedUrl(GeneratePresignedUrlRequest request) {
        AmazonS3 s3client = client();
        URL url = s3client.generatePresignedUrl(request);
        return url.toString();
    }

    /**
     * 文件上传
     * <p>
     * 使用默认的桶名称进行上传
     *
     * @param objectName
     * @param inputStream
     * @return
     */
    public String upload(String objectName, InputStream inputStream) {
        // 默认上传至 admin 目录
        return upload(bucketName + BatchTaskConstants.Commons.COS_ADMIN_DIR, objectName, inputStream);
    }

    public String upload(String bucketName, String objectName, InputStream inputStream) {
        try {
            logger.info("begin upload to s3, bucketName={}, objectName={}", bucketName, objectName);
            // 重置桶名称 和 文件名称
            // objectName中的目录与桶名称进行拼接
            Entry entry = new Entry(bucketName, objectName);
            bucketName = entry.getBucketName();
            objectName = entry.getObjectName();

            AmazonS3 s3Client = client();
            ObjectMetadata metadata = new ObjectMetadata();
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    bucketName, objectName, inputStream, metadata);
            s3Client.putObject(putObjectRequest);

            GeneratePresignedUrlRequest generatePresignedUrlRequest =
                    new GeneratePresignedUrlRequest(bucketName, objectName);
            generatePresignedUrlRequest.setMethod(HttpMethod.PUT);
            generatePresignedUrlRequest.setContentType("image/jpeg");
            URL url = s3Client.generatePresignedUrl(generatePresignedUrlRequest);

            logger.info("end upload to s3, bucketName={}, objectName={}, url={}", bucketName, objectName, url.toString());
            return url.toString();
        } catch (Exception e) {
            logger.error("文件上传到s3失败,fileName = {}", objectName, e);
            throw new BizException("上传文件失败，请稍后重试");
        }
    }

    /**
     * 文件下载 --- 从s3下载到本地 保存到临时文件
     *
     * @param objectName 文件名
     * @return 文件
     */
    public File download(String objectName) {
        AmazonS3 s3client = client();
        String regBucketName = bucketName + COS_ADMIN_DIR;
        String regObjectName = "/" + objectName.substring(0, 2) + "/" + objectName;
        Entry entry = new Entry(regBucketName, regObjectName);
        regBucketName = entry.getBucketName();
        objectName = entry.getObjectName();
        logger.info("bucketName:{} | objectName:{}", regBucketName, objectName);
        S3Object object = s3client.getObject(new GetObjectRequest(regBucketName, objectName));
        File file;
        try {
            file = this.doSaveFile(object.getObjectContent(), objectName);
        } catch (Exception e) {
            log.info("保存文件不成功", e);
            throw new BizException("保存文件失败");
        }
        return file;
    }


    /**
     * 保存文件
     *
     * @param in
     * @param fileName
     * @return
     * @throws IOException
     */
    public File doSaveFile(InputStream in, String fileName) throws IOException {
        File dir = new File(TMP_SAVE_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String toUseName = SecureUtil.md5(UUID.randomUUID().toString()) + "_" + fileName;
        File file = new File(TMP_SAVE_DIR + SLASH_BAR_STR + toUseName);
        try (InputStream inputStream = in; OutputStream outputStream = new FileOutputStream(file)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
        }
        return file;
    }

    public String getBucketName(String channelCode, String type) {
        StringBuffer imagePathBuilder = new StringBuffer();
        imagePathBuilder.append(bucketName);
        //根据渠道区分文件夹
        imagePathBuilder.append("/").append(channelCode);
        imagePathBuilder.append("/").append(type).append("/").append(DateTool.getStringDateFree("yyyyMMdd"));
        String buckerName = imagePathBuilder.toString();


        return buckerName;
    }

    public String getObjectKey(String channelCode, String type) {
        StringBuffer objectKey = new StringBuffer();
        return objectKey.append(type).append(DateTool.getStringDateFree("yyyyMMddHHmmssSSS"))
                .append(DateTool.getRandom(5)).append(".jpg").toString();
    }


    public AmazonS3 client() {
        return amazonS3Client;
    }


    /**
     * 大数据文件上传
     * <p>
     * 上传至大数据目录(主要用于测试)
     *
     * @param objectName
     * @param inputStream
     * @return
     */
    public String bigdataUpload(String objectName, InputStream inputStream) {
        // 默认上传至 admin 目录
        return upload(bucketName + BatchTaskConstants.Commons.COS_BIGDATA_DIR, objectName, inputStream);
    }


    private class Entry {

        private String bucketName;
        private String objectName;

        Entry(String bucketName, String objectName) {
            if (StringUtils.isBlank(bucketName)) {
                throw new IllegalArgumentException("bucketName不能够为空");
            }
            if (StringUtils.isBlank(objectName)) {
                throw new IllegalArgumentException("objectName不能够为空");
            }
            this.bucketName = bucketName;
            this.objectName = objectName;
            init(bucketName, objectName);
        }

        /**
         * 初始化桶名 和 文件名称
         *
         * @param bucketName
         * @param objectName
         */
        private void init(String bucketName, String objectName) {
            String dir = "";
            String fileName = objectName;
            int index = objectName.lastIndexOf(SLASH_BAR_CH);
            if (index != -1) {
                // 生成目录
                dir = objectName.substring(0, index);
                // 生成文件名称
                fileName = objectName.substring(index + 1);
            } else {
                // 如果传入的objectName之前没有目录 那么则获取该objectName前2位作为目录
                dir = objectName.substring(0, 2);
            }

            if (!dir.startsWith(SLASH_BAR_STR)) {
                dir = SLASH_BAR_STR + dir;
            }

            // 包装桶名称
            bucketNameWrapper(bucketName, dir);
            // 设置文件名称
            setObjectName(fileName);
        }

        /**
         * 桶名称包装
         *
         * @param bucketName
         * @param dir
         * @return
         */
        private void bucketNameWrapper(String bucketName, String dir) {
            if (StringUtils.isBlank(dir)) {
                setBucketName(bucketName);
                return;
            }

            if (StringUtils.endsWith(bucketName, SLASH_BAR_STR)) {
                // 移除最后的斜杠
                bucketName = bucketName.substring(0, bucketName.length() - 1);
            }
            setBucketName(bucketName + dir);
        }

        private void setBucketName(String bucketName) {
            this.bucketName = bucketName;
        }

        private void setObjectName(String objectName) {
            this.objectName = objectName;
        }

        public String getBucketName() {
            return bucketName;
        }

        public String getObjectName() {
            return objectName;
        }
    }

    public String preAdminUrlThreeDay(String objectName) {
        return preSignedUrlThreeDay(bucketName + BatchTaskConstants.Commons.COS_ADMIN_DIR, objectName, HttpMethod.GET);
    }

    public String preSignedUrlThreeDay(String bucketName, String objectName, HttpMethod httpMethod) {
        // 重置桶名路径和文件名称
        return getEntry(bucketName, objectName, httpMethod, downloadExpirationMillis);

    }

    private String getEntry(String bucketName, String objectName, HttpMethod httpMethod, long expirationMillis) {
        Entry entry = new Entry(bucketName, objectName);
        bucketName = entry.getBucketName();
        objectName = entry.getObjectName();
        GeneratePresignedUrlRequest request =
                new GeneratePresignedUrlRequest(bucketName, objectName, httpMethod);
        Date expiration = new Date(System.currentTimeMillis() + expirationMillis);
        request.setExpiration(expiration);
        return preSignedUrl(request);
    }

}
