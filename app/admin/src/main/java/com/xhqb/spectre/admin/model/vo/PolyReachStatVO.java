package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.bidata.entity.TypeReachStatDO;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PolyReachStatVO {
    /**
     * 统计日期
     * 月份
     */
    private List<String> statDate;
    /**
     * 渠道编码
     */
    private String channelCode;

    private String channelCodeName;

    private List<Integer> reachCount;

    private List<Integer> reachBillCount;

    private List<String> reachRate;

    public void add(TypeReachStatDO typeReachStatDO) {
        this.statDate.add(typeReachStatDO.getStatDate());
        this.reachCount.add(typeReachStatDO.getReachCount());
        this.reachBillCount.add(typeReachStatDO.getReachBillCount());
        this.reachRate.add(CommonUtil.double2String(typeReachStatDO.getReachRate()));
    }

}
