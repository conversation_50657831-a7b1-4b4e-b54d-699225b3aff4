package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dto.FailResendMatchResultDTO;

/**
 * 补发策略
 */
public interface FailResendStrategyService {

    void initCache();

    void refreshCache();

    /**
     * 判断模板是否在策略中
     *
     * @param tplCode
     * @return
     */
    boolean isTplInStrategy(String tplCode);

    /**
     * 匹配策略
     *
     * @param smsOrderDO
     * @return
     */
    FailResendMatchResultDTO matchStrategy(SmsOrderDO smsOrderDO);
}
