package com.xhqb.spectre.admin.batchtask.upload.impl;

import com.xhqb.spectre.admin.batchtask.io.UploadFileUtils;
import com.xhqb.spectre.admin.batchtask.upload.UploadContext;
import com.xhqb.spectre.admin.batchtask.upload.UploadHandler;
import com.xhqb.spectre.admin.batchtask.upload.cos.S3Helper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.UUID;

/**
 * COS文件上传处理
 *
 * <AUTHOR>
 * @date 2021/9/22
 */
@Slf4j
public class CosUploadHandler implements UploadHandler {

    @Autowired
    private S3Helper s3Helper;

    /**
     * 文件上传处理
     *
     * @param context
     * @return 返回文件上传地址
     */
    @Override
    public String handler(UploadContext context) {
        long start = System.currentTimeMillis();
        String fileName = context.getFileName();
        String md5 = DigestUtils.md5Hex(UUID.randomUUID() + fileName);
        // cos文件名称 md前2位/md5值
        String cosFileName = UploadFileUtils.genCosObjectName(md5 + "." + UploadFileUtils.getFileSuffix(fileName));
        String saveUrl = s3Helper.upload(cosFileName, context.getInputStream());
        log.info("COS文件上传耗时 = {},fileName = {}, saveUrl = {}", (System.currentTimeMillis() - start), fileName, saveUrl);
        return getDownload(saveUrl);
    }

    /**
     * 获取到文件下载地址
     *
     * @param saveUrl
     * @return
     */
    private String getDownload(String saveUrl) {
        if (StringUtils.isBlank(saveUrl)) {
            return saveUrl;
        }

        int index = saveUrl.indexOf('?');
        if (index == -1) {
            // 未找到?字符串
            return saveUrl;
        }

        return saveUrl.substring(0, index);
    }

}
