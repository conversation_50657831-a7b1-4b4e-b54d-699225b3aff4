package com.xhqb.spectre.admin.util.excel;

import java.util.List;

public class MultiLevelHeader {
    private String mainTitle;
    private String field;
    private List<String> subTitles;

    public MultiLevelHeader(String mainTitle, String field, List<String> subTitles) {
        this.mainTitle = mainTitle;
        this.field = field;
        this.subTitles = subTitles;
    }

    public MultiLevelHeader(String mainTitle, String field) {
        this.mainTitle = mainTitle;
        this.field = field;
        this.subTitles = null;
    }

    public String getMainTitle() {
        return mainTitle;
    }

    public String getField() {
        return field;
    }

    public List<String> getSubTitles() {
        return subTitles;
    }

    public boolean isMultiLevel() {
        return subTitles != null && !subTitles.isEmpty();
    }
}
