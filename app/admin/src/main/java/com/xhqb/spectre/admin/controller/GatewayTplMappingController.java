package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.annotation.LogOpTime;
import com.xhqb.spectre.admin.model.dto.GatewayTplMappingDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.GatewayTplMappingVO;
import com.xhqb.spectre.admin.model.vo.GatewayTplVO;
import com.xhqb.spectre.admin.service.GatewayTplMappingService;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.GatewayTplMappingQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/26 16:59
 * @Description:
 */
@RestController
@RequestMapping("/gatewayTplMapping")
@Slf4j
public class GatewayTplMappingController {

    @Autowired
    private GatewayTplMappingService gatewayTplMappingService;

    /**
     * 查询列表
     *
     * @param query
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(@ModelAttribute GatewayTplMappingQuery query, Integer pageNum, Integer pageSize) {
        query.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<GatewayTplMappingVO> commonPager = gatewayTplMappingService.listByPage(query);
        return AdminResult.success(commonPager);
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public AdminResult queryInfo(@PathVariable("id") Integer id) {
        return AdminResult.success(gatewayTplMappingService.getById(id));
    }

    /**
     * 创建
     *
     * @param gatewayTplMappingDTO
     * @return
     */
    @PostMapping("")
    @LogOpTime(OpLogConstant.MODULE_GATEWAY_TPL_MAPPING)
    public AdminResult create(@RequestBody GatewayTplMappingDTO gatewayTplMappingDTO) {
        log.info("create gatewayTplMapping, gatewayTplMappingDTO: {}", JSON.toJSONString(gatewayTplMappingDTO));
        gatewayTplMappingService.create(gatewayTplMappingDTO);
        return AdminResult.success();
    }

    /**
     * 更新
     *
     * @param id
     * @param gatewayTplMappingDTO
     * @return
     */
    @PutMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_GATEWAY_TPL_MAPPING)
    public AdminResult update(@PathVariable("id") Integer id, @RequestBody GatewayTplMappingDTO gatewayTplMappingDTO) {
        log.info("update gatewayTplMapping, id: {}, gatewayTplMappingDTO: {}", id, JSON.toJSONString(gatewayTplMappingDTO));
        gatewayTplMappingService.update(id, gatewayTplMappingDTO);
        return AdminResult.success();
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @LogOpTime(OpLogConstant.MODULE_GATEWAY_TPL_MAPPING)
    public AdminResult delete(@PathVariable("id") Integer id) {
        log.info("delete gatewayTplMapping, id: {}", id);
        gatewayTplMappingService.delete(id);
        return AdminResult.success();
    }

    /**
     * 查询网关用户的模板列表
     *
     * @return
     */
    @GetMapping("/queryTplList")
    public AdminResult queryTplList() {
        return AdminResult.success(gatewayTplMappingService.queryTplList());
    }



}
