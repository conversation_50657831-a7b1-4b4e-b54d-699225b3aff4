package com.xhqb.spectre.admin.model.dto;

import lombok.Data;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class ShortUrlTplDTO {

    /**
     * 长链URL
     */
    @NotBlank(message = "长链URL不能为空")
    @Size(max = 512, message = "长链URL最大为{max}个字符")
    @URL(message = "长链URL格式有误")
    private String longUrl;

    /**
     * 有效期。1~30
     */
    @Min(value = 1, message = "有效期的最小值不能小于1")
//    @Max(value = 30, message = "有效期的最大值不能大于30")
    private Integer validPeriod;

    /**
     * 短链模版编码
     */
    @NotBlank(message = "短链模版编码不能为空")
    private String tplCode;

    /**
     * 模版名称
     */
    @Size(max = 256, message = "模版名称最大为{max}个字符")
    private String name;

    /**
     * 描述
     */
    @Size(max = 256, message = "描述最大为{max}个字符")
    private String description;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 手机号是否加密(0:不加密,1:加密)
     */
    private Integer isEncrypt;
}
