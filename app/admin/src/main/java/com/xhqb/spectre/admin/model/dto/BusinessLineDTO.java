package com.xhqb.spectre.admin.model.dto;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 业务线DTO
 *
 * <AUTHOR>
 * @date 2022/9/23
 */
@Data
public class BusinessLineDTO implements Serializable {

    /**
     * 名称
     */
    @NotEmpty(message = "业务线名称不能够为空")
    private String name;

    /**
     * 状态，0：无效，1：有效
     */
    private Integer status;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
