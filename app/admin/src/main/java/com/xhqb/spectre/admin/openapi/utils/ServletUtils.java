package com.xhqb.spectre.admin.openapi.utils;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.admin.openapi.auth.RequestHeader;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * servlet工具
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
@Slf4j
public class ServletUtils {

    /**
     * 获取到请求头信息
     *
     * @param request
     * @return
     */
    public static RequestHeader toHeader(HttpServletRequest request) {
        Map<String, String> mapping = new HashMap<>(16);
        Enumeration<String> headerNames = request.getHeaderNames();
        String key, value;
        while (headerNames.hasMoreElements()) {
            key = headerNames.nextElement();
            value = request.getHeader(key);
            mapping.put(key, value);
        }
        return new RequestHeader(mapping);
    }

    /**
     * 回写内容
     *
     * @param response
     * @param result
     */
    public static void write(HttpServletResponse response, Object result) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-type", "application/json;charset=UTF-8");
        response.setStatus(200);
        try (PrintWriter writer = response.getWriter()) {
            writer.write(JSON.toJSONString(result));
            writer.flush();
        } catch (IOException ex) {
            log.error("response write detected error", ex);
        }
    }
}
