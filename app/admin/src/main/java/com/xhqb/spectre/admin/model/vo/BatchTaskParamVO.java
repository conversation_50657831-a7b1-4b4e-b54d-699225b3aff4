package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.common.dal.entity.BatchTaskParamDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/10/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BatchTaskParamVO implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 批次号
     */
    private Integer taskId;

    /**
     * 发送状态
     */
    private Integer sendStatus;

    /**
     * 文件md5值
     */
    private String fileMd5;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 参数JSON
     */
    private String paramJsonArray;

    /**
     * 删除标记
     */
    private Integer isDelete;

    /**
     * 文件切分起始位置
     */
    private Integer startOffset;

    /**
     * 文件切分结束位置
     */
    private Integer endOffset;

    /**
     * 查询列表数据展现
     *
     * @param batchTaskParamDO
     * @return
     */
    public static BatchTaskParamVO buildListQuery(BatchTaskParamDO batchTaskParamDO) {
        return BatchTaskParamVO.builder()
                // 主键
                .id(batchTaskParamDO.getId())
                // 批次号
                .taskId(batchTaskParamDO.getTaskId())
                // 发送状态
                .sendStatus(batchTaskParamDO.getSendStatus())
                // 文件md5值
                .fileMd5(batchTaskParamDO.getFileMd5())
                // 创建时间
                .createTime(batchTaskParamDO.getCreateTime())
                // 更新时间
                .updateTime(batchTaskParamDO.getUpdateTime())
                // 删除标记
                .isDelete(batchTaskParamDO.getIsDelete())
                // 文件切分起始位置
                .startOffset(batchTaskParamDO.getStartOffset())
                // 文件切分结束位置
                .endOffset(batchTaskParamDO.getEndOffset())
                .build();
    }


    /**
     * 查询数据详情展现
     *
     * @param batchTaskParamDO
     * @return
     */
    public static BatchTaskParamVO buildInfoQuery(BatchTaskParamDO batchTaskParamDO) {
        return BatchTaskParamVO.builder()
                // 主键
                .id(batchTaskParamDO.getId())
                // 批次号
                .taskId(batchTaskParamDO.getTaskId())
                // 发送状态
                .sendStatus(batchTaskParamDO.getSendStatus())
                // 文件md5值
                .fileMd5(batchTaskParamDO.getFileMd5())
                // 创建时间
                .createTime(batchTaskParamDO.getCreateTime())
                // 更新时间
                .updateTime(batchTaskParamDO.getUpdateTime())
                // 参数JSON
                .paramJsonArray(batchTaskParamDO.getParamJsonArray())
                // 删除标记
                .isDelete(batchTaskParamDO.getIsDelete())
                // 文件切分起始位置
                .startOffset(batchTaskParamDO.getStartOffset())
                // 文件切分结束位置
                .endOffset(batchTaskParamDO.getEndOffset())
                .build();
    }

}
