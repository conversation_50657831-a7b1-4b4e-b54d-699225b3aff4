package com.xhqb.spectre.admin.controller;

import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.DownLoadUrlVO;
import com.xhqb.spectre.admin.service.FileOperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 通用文件操作
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/30 15:02
 */
@Slf4j
@RestController
@RequestMapping("/fileOperation")
public class FileOperationController {

    @Resource
    private FileOperationService fileOperationService;


    /**
     * 文件上传
     * @param file 上传的文件
     * @return url(文件url)
     * @throws IOException
     */
    @PostMapping("/upload")
    public AdminResult upload(@RequestParam("file") MultipartFile file) throws IOException {
        return AdminResult.success(fileOperationService.upload(file));
    }

    /**
     * 文件下载
     */
    @GetMapping("/downLoadUrl/{fileName}")
    public AdminResult downLoadUrl(@PathVariable("fileName") String fileName) {
        long start = System.currentTimeMillis();
        log.info("文件下载|开始时间:{}", start);
        DownLoadUrlVO loadUrlVO = fileOperationService.downLoad(fileName);
        log.info("文件下载耗时:{}", System.currentTimeMillis() - start);
        return AdminResult.success(loadUrlVO);
    }

}
