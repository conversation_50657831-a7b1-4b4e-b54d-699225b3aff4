package com.xhqb.spectre.admin.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xhqb.spectre.admin.batchtask.io.UploadFileUtils;
import com.xhqb.spectre.admin.batchtask.upload.cos.S3Helper;
import com.xhqb.spectre.admin.model.dto.BatchTaskBigdataDTO;
import com.xhqb.spectre.admin.model.dto.BatchTaskDTO;
import com.xhqb.spectre.admin.model.result.AdminResult;
import com.xhqb.spectre.admin.model.vo.BatchTaskBigdataVO;
import com.xhqb.spectre.admin.service.BatchTaskBigdataService;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageParameter;
import com.xhqb.spectre.common.dal.query.BatchTaskBigdataQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * 大数据群发任务
 *
 * <AUTHOR>
 * @date 2021/10/28
 */
@RestController
@RequestMapping("/batchTaskBigdata")
@Slf4j
public class BatchTaskBigdataController {

    @Resource
    private BatchTaskBigdataService batchTaskBigdataService;
    @Autowired
    private S3Helper s3Helper;

    /**
     * 查询大数据群发任务列表
     *
     * @param batchTaskBigdataQuery 大数据群发任务查询条件
     * @param pageNum               当前页码
     * @param pageSize              一页显示的记录数
     * @return
     */
    @GetMapping("")
    public AdminResult queryList(BatchTaskBigdataQuery batchTaskBigdataQuery, Integer pageNum, Integer pageSize) {
        batchTaskBigdataQuery.setPageParameter(new PageParameter(pageNum, pageSize));
        CommonPager<BatchTaskBigdataVO> commonPager = batchTaskBigdataService.listByPage(batchTaskBigdataQuery);
        return AdminResult.success(commonPager);
    }


    /**
     * 添加大数据任务任务接口
     *
     * @param batchTaskBigdataDTO 大数据群发任务内容
     * @return
     */
    @PostMapping("")
    public AdminResult createInfo(@RequestBody BatchTaskBigdataDTO batchTaskBigdataDTO) {
        log.info("create batchTaskBigdataDTO = {}", JSON.toJSONString(batchTaskBigdataDTO));
        batchTaskBigdataService.create(batchTaskBigdataDTO);
        return AdminResult.success();
    }


    /**
     * 更新大数据群发任务接口
     *
     * @param id                  主键
     * @param batchTaskBigdataDTO 修改大数据群发任务内容
     * @return
     */
    @PutMapping("/{id}")
    public AdminResult updateInfo(@PathVariable("id") Integer id, @RequestBody BatchTaskBigdataDTO batchTaskBigdataDTO) {
        log.info("update batchTaskBigdataDTO = {}, id = {}", JSON.toJSONString(batchTaskBigdataDTO), id);
        batchTaskBigdataService.update(id, batchTaskBigdataDTO);
        return AdminResult.success();
    }

    /**
     * 大数据文件上传接口
     *
     * @param file 文件上传新
     * @return
     * @throws Exception
     */
    @PostMapping("/web/upload")
    public AdminResult webUpload(@RequestParam("file") MultipartFile file) throws Exception {
        String fileName = file.getOriginalFilename();
        String md5 = DigestUtils.md5Hex(UUID.randomUUID() + fileName);
        // cos文件名称 md前2位/md5值
        String cosFileName = UploadFileUtils.genCosObjectName(md5 + "." + UploadFileUtils.getFileSuffix(fileName));
        String url = s3Helper.bigdataUpload(cosFileName, file.getInputStream());
        return AdminResult.success(Lists.newArrayList(url));
    }

    /**
     * 查询大数据文件地址
     *
     * @param fileName
     * @return
     */
    @GetMapping("/queryDownload/{fileName}")
    public AdminResult queryDownload(@PathVariable("fileName") String fileName) {
        String url = s3Helper.preBigdataUrl(fileName.substring(0, 2) + "/" + fileName, true);
        return AdminResult.success(Lists.newArrayList(url));
    }
}
