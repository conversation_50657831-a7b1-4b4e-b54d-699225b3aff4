package com.xhqb.spectre.admin.config.properties;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/17 17:51
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ConfigurationProperties(prefix = "kael.datasource.druid.spectre")
public class SpectreDruidProperties extends DruidProperties {
}
