package com.xhqb.spectre.admin.mq.producer;

import com.alibaba.fastjson.JSON;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.mq.send.ProducerSender;
import com.xhqb.spectre.admin.mq.send.SenderContext;
import com.xhqb.spectre.admin.mq.send.SenderResult;
import com.xhqb.spectre.admin.mq.send.decorator.DeliverAfterSender;
import com.xhqb.spectre.admin.mq.send.decorator.DeliverAtSender;
import com.xhqb.spectre.admin.mq.send.enums.MqSendTypeEnum;
import com.xhqb.spectre.admin.mq.send.enums.MqTimerTypeEnum;
import com.xhqb.spectre.admin.mq.send.impl.AsyncProducerSender;
import com.xhqb.spectre.admin.mq.send.impl.DefaultProducerSender;
import org.apache.pulsar.client.api.TypedMessageBuilder;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * 抽象生产者，具体的生产者需要实现获取具体的topic方法
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
public abstract class AbstractMessageProducer<T> implements MessageProducer<T> {

    @Autowired
    private MQTemplate<String> mqTemplate;
    @Autowired
    private DefaultProducerSender defaultProducerSender;
    @Autowired
    private AsyncProducerSender asyncProducerSender;

    /**
     * @param senderContext
     * @return
     * @throws Exception
     */
    @Override
    public SenderResult send(SenderContext<T> senderContext) throws Exception {
        ProducerSender producerSender = this.getProducerSender(senderContext);
        producerSender = this.producerSenderWrapper(producerSender, senderContext);
        TypedMessageBuilder<String> messageBuilder = mqTemplate.createMessage(getTopic(), JSON.toJSONString(senderContext.getMsg()));
        return producerSender.send(messageBuilder, senderContext);
    }

    /**
     * 设置生产者topic
     *
     * @return
     */
    protected abstract String getTopic();

    /**
     * 获取到生成消息发送器
     *
     * @param senderContext
     * @return
     */
    private ProducerSender getProducerSender(SenderContext<T> senderContext) {
        MqSendTypeEnum mqSendTypeEnum = senderContext.getMqSendTypeEnum();
        if (MqSendTypeEnum.DEFAULT_SEND == mqSendTypeEnum) {
            return defaultProducerSender;
        }
        if (MqSendTypeEnum.SEND_ASYNC == mqSendTypeEnum) {
            return asyncProducerSender;
        }

        throw new BizException("不支持的消息发送方式");
    }

    /**
     * 消息发送器包装
     *
     * @param producerSender
     * @param senderContext
     * @return
     */
    private ProducerSender producerSenderWrapper(ProducerSender producerSender, SenderContext<T> senderContext) {
        MqTimerTypeEnum mqTimerTypeEnum = senderContext.getMqTimerTypeEnum();
        if (Objects.isNull(mqTimerTypeEnum) || MqTimerTypeEnum.NOW == mqTimerTypeEnum) {
            return producerSender;
        }

        if (MqTimerTypeEnum.DELIVER_AFTER == mqTimerTypeEnum) {
            // 延迟模式
            return new DeliverAfterSender(producerSender);
        }

        if (MqTimerTypeEnum.DELIVER_AT == mqTimerTypeEnum) {
            // 定时模式
            return new DeliverAtSender(producerSender);
        }

        throw new BizException("不支持的消息定时模式");
    }
}
