package com.xhqb.spectre.admin.batchtask.send.record;

import lombok.Data;

import java.io.Serializable;

/**
 * 消息发送失败记录
 * 参考: com.xhqb.spectre.api.model.smsresp.SMSSendFailedRecord
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
public class MessageSendFailedRecord implements Serializable {

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 接口响应错误码
     */
    private String fileCode;

    /**
     * 接口响应错误信息
     */
    private String fileMsg;
}
