package com.xhqb.spectre.admin.model.vo;

import com.xhqb.spectre.admin.bidata.entity.TplReachRateDO;
import com.xhqb.spectre.admin.util.CommonUtil;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class TplReachRateVO implements Serializable {

    private List<String> headers;
    private List<RowData> rows;

    private Integer totalCount;

    public TplReachRateVO(List<String> headers) {
        this.headers = headers;
        this.rows = new ArrayList<>();
    }

    @Data
    @Builder
    public static class RowData {
        private String tplCode;

        private String week1;
        private String week2;
        private String week3;
        private String week4;
    }



    public static RowData convert(TplReachRateDO tplReachRateDO) {
        return RowData.builder().tplCode(tplReachRateDO.getTplCode())
                .week1(CommonUtil.double2String(tplReachRateDO.getWeek1()))
                .week2(CommonUtil.double2String(tplReachRateDO.getWeek2()))
                .week3(CommonUtil.double2String(tplReachRateDO.getWeek3()))
                .week4(CommonUtil.double2String(tplReachRateDO.getWeek4()))
                .build();
    }
}
