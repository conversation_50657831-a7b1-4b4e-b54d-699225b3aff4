package com.xhqb.spectre.admin.service.impl;

import cn.hutool.json.JSONUtil;
import com.xhqb.kael.sequencegenerator.DistributedSequence;
import com.xhqb.spectre.admin.exception.BizException;
import com.xhqb.spectre.admin.model.dto.ChannelTestTplDTO;
import com.xhqb.spectre.admin.model.dto.TypeWeightDTO;
import com.xhqb.spectre.admin.model.vo.ChannelTestTplDetailVO;
import com.xhqb.spectre.admin.model.vo.ChannelTestTplVO;
import com.xhqb.spectre.admin.service.ChannelTestTplService;
import com.xhqb.spectre.admin.util.SsoUserInfoUtil;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.entity.test.TestTplDO;
import com.xhqb.spectre.common.dal.mapper.SignMapper;
import com.xhqb.spectre.common.dal.mapper.TestTplMapper;
import com.xhqb.spectre.common.dal.mapper.TplMapper;
import com.xhqb.spectre.common.dal.page.CommonPager;
import com.xhqb.spectre.common.dal.page.PageResultUtils;
import com.xhqb.spectre.common.dal.query.ChannelTestTplQuery;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChannelTestTplServiceImpl implements ChannelTestTplService {

    @Resource
    private TestTplMapper channelTestTplMapper;
    @Resource
    private SignMapper signMapper;

    @Resource
    private TplMapper tplMapper;


    @Override
    public CommonPager<ChannelTestTplVO> listByPage(ChannelTestTplQuery channelTestTplQuery) {
        List<ChannelTestTplVO> testTplVOS = channelTestTplMapper.selectByQuery(channelTestTplQuery).stream()
                .map(this::buildVO).collect(Collectors.toList());
        List<ChannelTestTplVO> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(testTplVOS)) {
            List<Integer> smsTplIds = testTplVOS.stream().map(ChannelTestTplVO::getSmsTplId).collect(Collectors.toList());
            Map<Integer, TplDO> tplDOMap = tplMapper.selectByIdListAndTplCode(smsTplIds, channelTestTplQuery.getTplCode()).stream()
                    .collect(Collectors.toMap(TplDO::getId, tplDO -> tplDO));
            testTplVOS.forEach(testTplVO -> {
                if (tplDOMap.containsKey(testTplVO.getSmsTplId())) {
                    TplDO tplDO = tplDOMap.get(testTplVO.getSmsTplId());
                    testTplVO.setTplCode(tplDO.getCode());
                    testTplVO.setSignId(tplDO.getSignId());
                    resultList.add(testTplVO);
                }
            });
        }
        return PageResultUtils.result(
                resultList::size,
                () -> resultList
        );
    }

    @Override
    public Long add(ChannelTestTplDTO channelTestTplDTO) {
        // 唯一原则
        TestTplDO model = channelTestTplMapper.selectByChannelAccountIdAndSmsTplId(channelTestTplDTO.getChannelAccountId(), channelTestTplDTO.getSmsTplId());
        if (Objects.nonNull(model)) {
            throw new BizException("渠道账号下模版已经存在测试模版");
        }
        // 构建DO
        TestTplDO testTplDO = buildDO(channelTestTplDTO);
        testTplDO.setCreator(SsoUserInfoUtil.getUserName());
        testTplDO.setUpdater(SsoUserInfoUtil.getUserName());
        testTplDO.setCreateTime(new Date());
        testTplDO.setUpdateTime(new Date());
        channelTestTplMapper.insertBySelective(testTplDO);
        return testTplDO.getId();
    }

    @Override
    public Long update(ChannelTestTplDTO channelTestTplDTO) {
        // 根据id判断是否存在
        TestTplDO model = channelTestTplMapper.selectByPrimaryKey(channelTestTplDTO.getId());
        if (Objects.isNull(model)) {
            throw new BizException("不存在的测试模版");
        }
        TestTplDO testTplDO = buildDO(channelTestTplDTO);
        testTplDO.setUpdater(SsoUserInfoUtil.getUserName());
        testTplDO.setUpdateTime(new Date());
        if (channelTestTplMapper.updateByPrimaryKeySelective(testTplDO) < 0) {
            throw new BizException("更新失败");
        }
        return channelTestTplDTO.getId();
    }

    @Override
    public ChannelTestTplDetailVO detail(Long id) {
        // 根据id判断是否存在
        TestTplDO model = channelTestTplMapper.selectByPrimaryKey(id);
        if (Objects.isNull(model)) {
            throw new BizException("不存在的测试模版");
        }
        // 构建VO
        ChannelTestTplDetailVO channelTestTplDetailVO = buildDetailVO(model);
        TplDO tplDO = tplMapper.selectByPrimaryKey(model.getSmsTplId());
        if (Objects.isNull(tplDO)) {
            throw new BizException("不存在的模版");
        }
        SignDO signDO = signMapper.selectByPrimaryKey(tplDO.getSignId());
        if (Objects.isNull(signDO)) {
            throw new BizException("不存在的签名");
        }
        channelTestTplDetailVO.setTplCode(tplDO.getCode());
        channelTestTplDetailVO.setSignId(signDO.getId());
        channelTestTplDetailVO.setTplName(tplDO.getTitle());
        return channelTestTplDetailVO;
    }

    @Override
    public void enable(Long id) {
        TestTplDO model = new TestTplDO();
        model.setId(id);
        model.setStatus(1);
        model.setUpdater(SsoUserInfoUtil.getUserName());
        channelTestTplMapper.updateByPrimaryKeySelective(model);
    }

    @Override
    public void disable(Long id) {
        TestTplDO model = new TestTplDO();
        model.setId(id);
        model.setStatus(0);
        model.setUpdater(SsoUserInfoUtil.getUserName());
        channelTestTplMapper.updateByPrimaryKeySelective(model);
    }


    private ChannelTestTplDetailVO buildDetailVO(TestTplDO model) {
        ChannelTestTplDetailVO channelTestTplDetailVO = new ChannelTestTplDetailVO();
        BeanUtils.copyProperties(model, channelTestTplDetailVO);
        channelTestTplDetailVO.setTypeWeightDTOS(new ArrayList<>());
        if (!Strings.isBlank(model.getTypeWeight())) {
            channelTestTplDetailVO.setTypeWeightDTOS(JSONUtil.toList(model.getTypeWeight(), TypeWeightDTO.class));
        }
        return channelTestTplDetailVO;
    }

    private TestTplDO buildDO(ChannelTestTplDTO channelTestTplDTO) {
        TestTplDO testTplDO = new TestTplDO();
        BeanUtils.copyProperties(channelTestTplDTO, testTplDO);
        testTplDO.setTypeWeight(JSONUtil.toJsonStr(channelTestTplDTO.getTypeWeightDTOS()));
        return testTplDO;
    }

    ChannelTestTplVO buildVO(TestTplDO testTplDO) {
        ChannelTestTplVO channelTestTplVO = new ChannelTestTplVO();
        BeanUtils.copyProperties(testTplDO, channelTestTplVO);
        channelTestTplVO.setTypeWeightDTOS(new ArrayList<>());
        if (!Strings.isBlank(testTplDO.getTypeWeight())) {
            channelTestTplVO.setTypeWeightDTOS(JSONUtil.toList(testTplDO.getTypeWeight(), TypeWeightDTO.class));
        }
        return channelTestTplVO;
    }
}
