package com.xhqb.spectre.admin.service;

import com.xhqb.spectre.admin.cif.entity.CifEnterpriseCustomerDO;
import com.xhqb.spectre.admin.model.vo.batchtask.CustomerVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * cifdb.t_enterprise_customer_base 处理
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
public interface CifEnterpriseCustomerService {

    /**
     * 根据cid获取用户信息
     *
     * @param id
     * @return
     */
    CifEnterpriseCustomerDO getById(String id);

    /**
     * 根据cid列表查询用户信息
     *
     * @param cidList
     * @param smsTypeCode
     * @return
     */
    List<CifEnterpriseCustomerDO> queryByCidList(List<String> cidList, String smsTypeCode);

    /**
     * 查询结果返回map
     *
     * @param cidList
     * @param smsTypeCode
     * @return
     */
    Map<String, CustomerVO> mapByCidList(List<String> cidList, String smsTypeCode);

    /**
     * 根据手机号查询用户列表
     *
     * @param mobileList 手机号码列表
     * @return
     */
    List<CifEnterpriseCustomerDO> selectByMobileList(Collection<String> mobileList);
}
