#(?????????)
venus.stack=dev1
#(ops ?????????????????????) venus.meta=http://10.32.8.42:8080,http://10.32.8.7:8080 #(ops ??appId)
venus.appId=spectre-admin
venus.meta=http://10.32.8.42:8080,http://10.32.8.7:8080
#(?????????)
venus.env=dev




# management
### management.server.port : default 8080
management.server.port=9080
management.endpoints.web.base-path=/admin
## endpoints
management.endpoints.enabled-by-default=true
management.endpoints.web.exposure.include=info,health,configprops,prometheus,env,loggers
## endpoint
management.endpoint.prometheus.cache.time-to-live=0ms
management.endpoint.health.show-details=always
## health
management.health.defaults.enabled=true
## metrics
management.metrics.web.server.auto-time-requests=true

management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.metrics.distribution.sla.http.server.requests=1ms,5ms
