<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.admin.statistics.mapper.SmsStatisByDayMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.admin.statistics.entity.SmsStatisByDayDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="date" jdbcType="DATE" property="date" />
    <id column="class1" jdbcType="INTEGER" property="class1" />
    <id column="class2" jdbcType="VARCHAR" property="class2" />
    <id column="class3" jdbcType="VARCHAR" property="class3" />
    <id column="class4" jdbcType="VARCHAR" property="class4" />
    <id column="class5" jdbcType="VARCHAR" property="class5" />
    <id column="class6" jdbcType="VARCHAR" property="class6" />
    <id column="class7" jdbcType="VARCHAR" property="class7" />
    <result column="f1" jdbcType="INTEGER" property="f1" />
    <result column="f2" jdbcType="INTEGER" property="f2" />
    <result column="f3" jdbcType="INTEGER" property="f3" />
    <result column="f4" jdbcType="INTEGER" property="f4" />
    <result column="f5" jdbcType="INTEGER" property="f5" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <resultMap id="EnumResultMap" type="com.xhqb.spectre.admin.statistics.entity.ClassEnumDO">
    <result column="enum_name" property="enumName" jdbcType="VARCHAR" />
  </resultMap>

  <resultMap id="SumResultMap" type="com.xhqb.spectre.admin.statistics.entity.SmsStatisSumDO">
    <result column="date" property="date" jdbcType="VARCHAR" />
    <result column="sendCount" property="sendCount" jdbcType="INTEGER" />
    <result column="billCount" property="billCount" jdbcType="INTEGER" />
    <result column="class_value" property="classValue" jdbcType="VARCHAR" />
  </resultMap>

  <resultMap id="SumMonthResultMap" type="com.xhqb.spectre.admin.statistics.entity.SmsStatisSumDO">
    <result column="month" property="date" jdbcType="VARCHAR" />
    <result column="sendCount" property="sendCount" jdbcType="INTEGER" />
    <result column="billCount" property="billCount" jdbcType="INTEGER" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    date, class1, class2, class3, class4, class5, class6, class7, f1, f2, f3, f4, f5,
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.xhqb.spectre.admin.statistics.entity.SmsStatisByDayKey" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from t_sms_class_statis_byday
    where date = #{date,jdbcType=DATE}
      and class1 = #{class1,jdbcType=INTEGER}
      and class2 = #{class2,jdbcType=VARCHAR}
      and class3 = #{class3,jdbcType=VARCHAR}
      and class4 = #{class4,jdbcType=VARCHAR}
      and class5 = #{class5,jdbcType=VARCHAR}
      and class6 = #{class6,jdbcType=VARCHAR}
      and class7 = #{class7,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.xhqb.spectre.admin.statistics.entity.SmsStatisByDayKey" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_sms_class_statis_byday
    where date = #{date,jdbcType=DATE}
      and class1 = #{class1,jdbcType=INTEGER}
      and class2 = #{class2,jdbcType=VARCHAR}
      and class3 = #{class3,jdbcType=VARCHAR}
      and class4 = #{class4,jdbcType=VARCHAR}
      and class5 = #{class5,jdbcType=VARCHAR}
      and class6 = #{class6,jdbcType=VARCHAR}
      and class7 = #{class7,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.admin.statistics.entity.SmsStatisByDayDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_sms_class_statis_byday (date, class1, class2,
      class3, class4, class5,
      class6, class7, f1,
      f2, f3, f4, f5,
      create_time, update_time)
    values (#{date,jdbcType=DATE}, #{class1,jdbcType=INTEGER}, #{class2,jdbcType=VARCHAR},
      #{class3,jdbcType=VARCHAR}, #{class4,jdbcType=VARCHAR}, #{class5,jdbcType=VARCHAR},
      #{class6,jdbcType=VARCHAR}, #{class7,jdbcType=VARCHAR}, #{f1,jdbcType=INTEGER},
      #{f2,jdbcType=INTEGER}, #{f3,jdbcType=INTEGER}, #{f4,jdbcType=INTEGER}, #{f5,jdbcType=INTEGER},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.admin.statistics.entity.SmsStatisByDayDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_sms_class_statis_byday
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="date != null">
        date,
      </if>
      <if test="class1 != null">
        class1,
      </if>
      <if test="class2 != null">
        class2,
      </if>
      <if test="class3 != null">
        class3,
      </if>
      <if test="class4 != null">
        class4,
      </if>
      <if test="class5 != null">
        class5,
      </if>
      <if test="class6 != null">
        class6,
      </if>
      <if test="class7 != null">
        class7,
      </if>
      <if test="f1 != null">
        f1,
      </if>
      <if test="f2 != null">
        f2,
      </if>
      <if test="f3 != null">
        f3,
      </if>
      <if test="f4 != null">
        f4,
      </if>
      <if test="f5 != null">
        f5,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="class1 != null">
        #{class1,jdbcType=INTEGER},
      </if>
      <if test="class2 != null">
        #{class2,jdbcType=VARCHAR},
      </if>
      <if test="class3 != null">
        #{class3,jdbcType=VARCHAR},
      </if>
      <if test="class4 != null">
        #{class4,jdbcType=VARCHAR},
      </if>
      <if test="class5 != null">
        #{class5,jdbcType=VARCHAR},
      </if>
      <if test="class6 != null">
        #{class6,jdbcType=VARCHAR},
      </if>
      <if test="class7 != null">
        #{class7,jdbcType=VARCHAR},
      </if>
      <if test="f1 != null">
        #{f1,jdbcType=INTEGER},
      </if>
      <if test="f2 != null">
        #{f2,jdbcType=INTEGER},
      </if>
      <if test="f3 != null">
        #{f3,jdbcType=INTEGER},
      </if>
      <if test="f4 != null">
        #{f4,jdbcType=INTEGER},
      </if>
      <if test="f5 != null">
        #{f5,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.admin.statistics.entity.SmsStatisByDayDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_sms_class_statis_byday
    <set>
      <if test="f1 != null">
        f1 = #{f1,jdbcType=INTEGER},
      </if>
      <if test="f2 != null">
        f2 = #{f2,jdbcType=INTEGER},
      </if>
      <if test="f3 != null">
        f3 = #{f3,jdbcType=INTEGER},
      </if>
      <if test="f4 != null">
        f4 = #{f4,jdbcType=INTEGER},
      </if>
      <if test="f5 != null">
        f5 = #{f5,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where date = #{date,jdbcType=DATE}
      and class1 = #{class1,jdbcType=INTEGER}
      and class2 = #{class2,jdbcType=VARCHAR}
      and class3 = #{class3,jdbcType=VARCHAR}
      and class4 = #{class4,jdbcType=VARCHAR}
      and class5 = #{class5,jdbcType=VARCHAR}
      and class6 = #{class6,jdbcType=VARCHAR}
      and class7 = #{class7,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.admin.statistics.entity.SmsStatisByDayDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_sms_class_statis_byday
    set f1 = #{f1,jdbcType=INTEGER},
      f2 = #{f2,jdbcType=INTEGER},
      f3 = #{f3,jdbcType=INTEGER},
      f4 = #{f4,jdbcType=INTEGER},
      f5 = #{f5,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where date = #{date,jdbcType=DATE}
      and class1 = #{class1,jdbcType=INTEGER}
      and class2 = #{class2,jdbcType=VARCHAR}
      and class3 = #{class3,jdbcType=VARCHAR}
      and class4 = #{class4,jdbcType=VARCHAR}
      and class5 = #{class5,jdbcType=VARCHAR}
      and class6 = #{class6,jdbcType=VARCHAR}
      and class7 = #{class7,jdbcType=VARCHAR}
  </update>

  <select id="selectSumList" parameterType="com.xhqb.spectre.admin.statistics.query.SmsStatisQuery" resultMap="SumResultMap">
    select `date`, sum(`f1`) as sendCount, sum(`f2`) as billCount
    from t_sms_class_statis_byday
    <where>
      <if test="startDate != null and startDate != ''">and `date` <![CDATA[ >= ]]> #{startDate}</if>
      <if test="endDate != null and endDate != ''">and `date` <![CDATA[ <= ]]> #{endDate}</if>
      <if test="batchId != null">and class1 = #{batchId}</if>
      <if test="smsTypeCode != null and smsTypeCode != ''">and class2 = #{smsTypeCode}</if>
      <if test="tplCode != null and tplCode != ''">and class3 = #{tplCode}</if>
      <if test="channelCode != null and channelCode != ''">and class4 = #{channelCode}</if>
      <if test="province != null and province != ''">and class5 = #{province}</if>
      <if test="city != null and city != ''">and class6 = #{city}</if>
      <if test="isp != null and isp != ''">and class7 = #{isp}</if>
    </where>
    group by `date`
  </select>

  <select id="selectSumListCount" parameterType="com.xhqb.spectre.admin.statistics.query.SmsStatisQuery" resultType="java.lang.Integer">
    select count(*) from
    (
        select count(`date`)
        from t_sms_class_statis_byday
        <where>
          <if test="startDate != null and startDate != ''">and `date` <![CDATA[ >= ]]> #{startDate}</if>
          <if test="endDate != null and endDate != ''">and `date` <![CDATA[ <= ]]> #{endDate}</if>
          <if test="batchId != null">and class1 = #{batchId}</if>
          <if test="smsTypeCode != null and smsTypeCode != ''">and class2 = #{smsTypeCode}</if>
          <if test="tplCode != null and tplCode != ''">and class3 = #{tplCode}</if>
          <if test="channelCode != null and channelCode != ''">and class4 = #{channelCode}</if>
          <if test="province != null and province != ''">and class5 = #{province}</if>
          <if test="city != null and city != ''">and class6 = #{city}</if>
          <if test="isp != null and isp != ''">and class7 = #{isp}</if>
        </where>
        group by `date`
    ) t
  </select>

  <select id="selectSumListByPage" parameterType="com.xhqb.spectre.admin.statistics.query.SmsStatisQuery" resultMap="SumResultMap">
    select `date`, sum(`f1`) as sendCount, sum(`f2`) as billCount
    from t_sms_class_statis_byday
    <where>
      <if test="startDate != null and startDate != ''">and `date` <![CDATA[ >= ]]> #{startDate}</if>
      <if test="endDate != null and endDate != ''">and `date` <![CDATA[ <= ]]> #{endDate}</if>
      <if test="batchId != null">and class1 = #{batchId}</if>
      <if test="smsTypeCode != null and smsTypeCode != ''">and class2 = #{smsTypeCode}</if>
      <if test="tplCode != null and tplCode != ''">and class3 = #{tplCode}</if>
      <if test="channelCode != null and channelCode != ''">and class4 = #{channelCode}</if>
      <if test="province != null and province != ''">and class5 = #{province}</if>
      <if test="city != null and city != ''">and class6 = #{city}</if>
      <if test="isp != null and isp != ''">and class7 = #{isp}</if>
    </where>
    group by `date`
    order by `date` desc
    limit #{pageParameter.offset}, #{pageParameter.pageSize}
  </select>

  <select id="selectSumListByMonth" parameterType="com.xhqb.spectre.admin.statistics.query.SmsStatisQuery" resultMap="SumMonthResultMap">
    select DATE_FORMAT(`date`, '%Y-%m') as `month`, sum(`f1`) as sendCount, sum(`f2`) as billCount
    from t_sms_class_statis_byday
    <where>
      <if test="startDate != null and startDate != ''">and `date` <![CDATA[ >= ]]> #{startDate}</if>
      <if test="endDate != null and endDate != ''">and `date` <![CDATA[ <= ]]> #{endDate}</if>
      <if test="batchId != null">and class1 = #{batchId}</if>
      <if test="smsTypeCode != null and smsTypeCode != ''">and class2 = #{smsTypeCode}</if>
      <if test="tplCode != null and tplCode != ''">and class3 = #{tplCode}</if>
      <if test="channelCode != null and channelCode != ''">and class4 = #{channelCode}</if>
      <if test="province != null and province != ''">and class5 = #{province}</if>
      <if test="city != null and city != ''">and class6 = #{city}</if>
      <if test="isp != null and isp != ''">and class7 = #{isp}</if>
    </where>
    group by `month`
  </select>

  <select id="selectClassSum" resultMap="SumResultMap">
    select ${columnName} as `class_value`, sum(`f1`) as sendCount, sum(`f2`) as billCount
    from t_sms_class_statis_byday
    <where>
      <if test="startDate != null and startDate != ''">and `date` <![CDATA[ >= ]]> #{startDate}</if>
      <if test="endDate != null and endDate != ''">and `date` <![CDATA[ <= ]]> #{endDate}</if>
    </where>
    group by ${columnName}
  </select>

  <select id="selectClassSumList" resultMap="SumResultMap">
    select `date`, ${columnName} as `class_value`, sum(`f1`) as sendCount, sum(`f2`) as billCount
    from t_sms_class_statis_byday
    <where>
      <if test="startDate != null and startDate != ''">and `date` <![CDATA[ >= ]]> #{startDate}</if>
      <if test="endDate != null and endDate != ''">and `date` <![CDATA[ <= ]]> #{endDate}</if>
    </where>
    group by `date`, ${columnName}
  </select>

  <select id="selectClassEnum" resultMap="EnumResultMap">
    select distinct ${columnName} as `enum_name`
    from t_sms_class_statis_byday
    <where>
      <if test="startDate != null and startDate != ''">and `date` <![CDATA[ >= ]]> #{startDate}</if>
      <if test="endDate != null and endDate != ''">and `date` <![CDATA[ <= ]]> #{endDate}</if>
    </where>
  </select>
</mapper>