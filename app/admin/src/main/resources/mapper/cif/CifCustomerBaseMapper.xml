<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.admin.cif.mapper.CifCustomerBaseMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.admin.cif.entity.CifCustomerBaseDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="mobile_phone_enc" property="mobilePhone"/>
        <result column="customer_name" property="customerName"/>
        <result column="status" property="status"/>
        <result column="apply_loan_result" property="applyLoanResult"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id,mobile_phone_enc,customer_name,status,apply_loan_result
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from t_customer_base
        where id = #{id}
    </select>

    <select id="selectByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_customer_base
        where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="statusList != null">
            and status in
            <foreach collection="statusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByMobileList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_customer_base
        where mobile_phone_enc in
        <foreach collection="mobileList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectByCidList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_customer_base
        where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>