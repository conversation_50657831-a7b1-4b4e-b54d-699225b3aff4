<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.admin.cif.mapper.CifEnterpriseCustomerMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.admin.cif.entity.CifEnterpriseCustomerDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="cid" property="cid"  />
    <result column="mobile_phone" property="mobilePhone"  />
    <result column="customer_name" property="customerName"  />
    <result column="status" property="status"  />
    <result column="apply_loan_result" property="applyLoanResult"  />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id,cid,mobile_phone,customer_name,status,apply_loan_result
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from t_enterprise_customer_base
    where id = #{id}
  </select>

  <select id="selectByCidList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_enterprise_customer_base
    where cid in
    <foreach collection="cidList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    <if test="statusList != null">
      and status in
      <foreach collection="statusList" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="selectByMobileList" resultMap="BaseResultMap">
    select
    distinct cid,mobile_phone
    from t_enterprise_customer_base
    where mobile_phone in
    <foreach collection="mobileList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

</mapper>