<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.admin.bidata.mapper.BidataPlatformSendStatMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.admin.bidata.entity.SendStatDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="stat_date" property="statDate" jdbcType="VARCHAR"/>
        <result column="app_code" property="appCode" jdbcType="VARCHAR"/>
        <result column="tpl_code" property="tplCode" jdbcType="VARCHAR"/>
        <result column="isp_code" property="ispCode" jdbcType="VARCHAR"/>
        <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR"/>
        <result column="province_short_name" property="provinceShortName" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="send_count" property="sendCount" jdbcType="INTEGER"/>
        <result column="reach_count" property="reachCount" jdbcType="INTEGER"/>
        <result column="first_reach_count" property="firstReachCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="SendIspCodeDataMap" type="com.xhqb.spectre.admin.bidata.model.SendIspCodeDataDO">
        <result column="stat_date" property="statDate" jdbcType="VARCHAR"/>
        <result column="isp_code" property="ispCode" jdbcType="VARCHAR"/>
        <result column="total_send_count" property="sendCount" jdbcType="BIGINT"/>
        <result column="total_reach_count" property="reachCount" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , stat_date, app_code, tpl_code
        , isp_code, sms_type_code, province_short_name
        , channel_code,send_count, reach_count
        , create_time, update_time,first_reach_count
    </sql>


    <select id="selectReachRateByTimeAndType" resultType="com.xhqb.spectre.admin.bidata.entity.ReachRateDO">
        SELECT
        tpl_code as tplCode,
        SUM(reach_count) * 1.0 / NULLIF(SUM(send_count), 0) AS reachRate,
        SUM(send_count) AS sendCount
        FROM
        t_sms_platform_send_stat
        WHERE
        stat_date = #{startDate}
        AND sms_type_code IN
        <foreach item="item" collection="smsTypeCodes" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY tpl_code
    </select>

    <select id="selectTplCodeByDate" resultType="java.lang.String">
        SELECT tpl_code
        FROM t_sms_platform_send_stat
        WHERE stat_date >= #{t1Date}
        GROUP BY tpl_code
        ORDER BY SUM(send_count) DESC
            LIMIT #{topN}
    </select>


    <select id="selectTopTplCodesByCreatorTplCodes" resultType="java.lang.String">
        SELECT tpl_code
        FROM t_sms_platform_send_stat
        WHERE stat_date = #{t1Date}
        AND tpl_code IN
        <foreach item="item" collection="tplCodeList" separator="," close=")" open="(">
            #{item}
        </foreach>
        GROUP BY tpl_code
        ORDER BY SUM(send_count) DESC
        LIMIT #{topN}
    </select>

    <select id="selectSendCountTopByTplCodes" resultType="com.xhqb.spectre.admin.bidata.model.SendCountTopDO">
        SELECT stat_date AS statDate,
        tpl_code AS tplCode,
        isp_code AS ispCode,
        SUM(send_count) AS sendCount,
        SUM(reach_count) AS reachCount
        FROM t_sms_platform_send_stat
        WHERE stat_date >= #{t3Date}
        AND tpl_code IN
        <foreach item="item" collection="tplCodes" separator="," close=")" open="(">
            #{item}
        </foreach>
        GROUP BY stat_date,tpl_code,isp_code
    </select>

    <select id="selectDataByTimeAndIspCode" resultMap="SendIspCodeDataMap">
        SELECT CASE
                   WHEN isp_code IN ('电信', '联通', '移动') THEN isp_code
                   ELSE '其他'
                   END          AS isp_code,
               stat_date,
               SUM(send_count)  AS total_send_count,
               SUM(reach_count) AS total_reach_count
        FROM t_sms_platform_send_stat
        WHERE stat_date <![CDATA[ >= ]]> DATE_FORMAT(#{startDate}, '%Y-%m-%d')
          and stat_date <![CDATA[ <= ]]> DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        GROUP BY isp_code, stat_date
    </select>

</mapper>
