<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.admin.bidata.mapper.BidataSendStatMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.admin.bidata.entity.SendStatDO">

        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="stat_date" property="statDate" jdbcType="VARCHAR"/>
        <result column="app_code" property="appCode" jdbcType="VARCHAR"/>
        <result column="tpl_code" property="tplCode" jdbcType="VARCHAR"/>
        <result column="isp_code" property="ispCode" jdbcType="VARCHAR"/>
        <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR"/>
        <result column="province_short_name" property="provinceShortName" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="send_count" property="sendCount" jdbcType="INTEGER"/>
        <result column="reach_count" property="reachCount" jdbcType="INTEGER"/>
        <result column="first_reach_count" property="firstReachCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <resultMap id="MonthResultMap" type="com.xhqb.spectre.admin.bidata.entity.ChannelReachStatDO">
        <result column="stat_date" property="statDate" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="verify_reach_count" property="verifyReachCount" jdbcType="INTEGER"/>
        <result column="notify_reach_count" property="notifyReachCount" jdbcType="INTEGER"/>
        <result column="market_reach_count" property="marketReachCount" jdbcType="INTEGER"/>
        <result column="collector_reach_count" property="collectorReachCount" jdbcType="INTEGER"/>
        <result column="light_collector_reach_count" property="lightCollectorReachCount" jdbcType="INTEGER"/>
        <result column="severe_collector_reach_count" property="severeCollectorReachCount" jdbcType="INTEGER"/>
        <result column="debt_swap_reach_count" property="debtSwapReachCount" jdbcType="INTEGER"/>
        <result column="verify_reach_bill_count" property="verifyReachBillCount" jdbcType="INTEGER"/>
        <result column="notify_reach_bill_count" property="notifyReachBillCount" jdbcType="INTEGER"/>
        <result column="market_reach_bill_count" property="marketReachBillCount" jdbcType="INTEGER"/>
        <result column="collector_reach_bill_count" property="collectorReachBillCount" jdbcType="INTEGER"/>
        <result column="light_collector_reach_bill_count" property="lightCollectorReachBillCount" jdbcType="INTEGER"/>
        <result column="severe_collector_reach_bill_count" property="severeCollectorReachBillCount" jdbcType="INTEGER"/>
        <result column="debt_swap_reach_bill_count" property="debtSwapReachBillCount" jdbcType="INTEGER"/>
        <result column="verify_reach_rate" property="verifyReachRate" jdbcType="DOUBLE"/>
        <result column="notify_reach_rate" property="notifyReachRate" jdbcType="DOUBLE"/>
        <result column="market_reach_rate" property="marketReachRate" jdbcType="DOUBLE"/>
        <result column="collector_reach_rate" property="collectorReachRate" jdbcType="DOUBLE"/>
        <result column="light_collector_reach_rate" property="lightCollectorReachRate" jdbcType="DOUBLE"/>
        <result column="severe_collector_reach_rate" property="severeCollectorReachRate" jdbcType="DOUBLE"/>
        <result column="debt_swap_reach_rate" property="debtSwapReachRate" jdbcType="DOUBLE"/>
        <result column="avg_reach_rate" property="avgReachRate" jdbcType="DOUBLE"/>
        <result column="total_reach_count" property="totalReachCount" jdbcType="INTEGER"/>
        <result column="total_reach_bill_count" property="totalReachBillCount" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="TypeResultMap" type="com.xhqb.spectre.admin.bidata.entity.TypeReachStatDO">
        <result column="stat_date" property="statDate" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="reach_count" property="reachCount" jdbcType="INTEGER"/>
        <result column="reach_bill_count" property="reachBillCount" jdbcType="INTEGER"/>
        <result column="reach_rate" property="reachRate" jdbcType="DOUBLE"/>
        <result column="avg_reach_rate" property="avgReachRate" jdbcType="DOUBLE"/>
        <result column="total_reach_count" property="totalReachCount" jdbcType="INTEGER"/>
        <result column="total_reach_bill_count" property="totalReachBillCount" jdbcType="INTEGER"/>
    </resultMap>
    <resultMap id="DateResultMap" type="com.xhqb.spectre.admin.bidata.entity.DateReachStatDO">
        <result column="stat_week" property="statWeek" jdbcType="VARCHAR"/>
        <result column="verify_reach_count" property="verifyReachCount" jdbcType="INTEGER"/>
        <result column="notify_reach_count" property="notifyReachCount" jdbcType="INTEGER"/>
        <result column="market_reach_count" property="marketReachCount" jdbcType="INTEGER"/>
        <result column="collector_reach_count" property="collectorReachCount" jdbcType="INTEGER"/>
        <result column="light_collector_reach_count" property="lightCollectorReachCount" jdbcType="INTEGER"/>
        <result column="severe_collector_reach_count" property="severeCollectorReachCount" jdbcType="INTEGER"/>
        <result column="debt_swap_reach_count" property="debtSwapReachCount" jdbcType="INTEGER"/>
        <result column="verify_reach_bill_count" property="verifyReachBillCount" jdbcType="INTEGER"/>
        <result column="notify_reach_bill_count" property="notifyReachBillCount" jdbcType="INTEGER"/>
        <result column="market_reach_bill_count" property="marketReachBillCount" jdbcType="INTEGER"/>
        <result column="collector_reach_bill_count" property="collectorReachBillCount" jdbcType="INTEGER"/>
        <result column="light_collector_reach_bill_count" property="lightCollectorReachBillCount" jdbcType="INTEGER"/>
        <result column="severe_collector_reach_bill_count" property="severeCollectorReachBillCount" jdbcType="INTEGER"/>
        <result column="debt_swap_reach_bill_count" property="debtSwapReachBillCount" jdbcType="INTEGER"/>
        <result column="verify_reach_rate" property="verifyReachRate" jdbcType="DOUBLE"/>
        <result column="notify_reach_rate" property="notifyReachRate" jdbcType="DOUBLE"/>
        <result column="market_reach_rate" property="marketReachRate" jdbcType="DOUBLE"/>
        <result column="collector_reach_rate" property="collectorReachRate" jdbcType="DOUBLE"/>
        <result column="light_collector_reach_rate" property="lightCollectorReachRate" jdbcType="DOUBLE"/>
        <result column="severe_collector_reach_rate" property="severeCollectorReachRate" jdbcType="DOUBLE"/>
        <result column="debt_swap_reach_rate" property="debtSwapReachRate" jdbcType="DOUBLE"/>
        <result column="avg_reach_rate" property="avgReachRate" jdbcType="DOUBLE"/>
        <result column="total_reach_count" property="totalReachCount" jdbcType="INTEGER"/>
        <result column="total_reach_bill_count" property="totalReachBillCount" jdbcType="INTEGER"/>
    </resultMap>
    <resultMap id="SubResultMap" type="com.xhqb.spectre.admin.bidata.entity.SubReachStatDO">
        <result column="stat_date" property="statDate" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="tpl_code" property="tplCode" jdbcType="VARCHAR"/>
        <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR"/>
        <result column="sign_name" property="signName" jdbcType="VARCHAR"/>
        <result column="reach_count" property="reachCount" jdbcType="INTEGER"/>
        <result column="reach_bill_count" property="reachBillCount" jdbcType="INTEGER"/>
        <result column="reach_rate" property="reachRate" jdbcType="DOUBLE"/>
        <result column="price_count" property="priceCount" jdbcType="INTEGER"/>
    </resultMap>
    <resultMap id="ChannelResultMap" type="com.xhqb.spectre.admin.bidata.entity.StatChannelDO">
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="sign_name" property="signName" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="SmsTypeResultMap" type="com.xhqb.spectre.admin.bidata.entity.StatSmsTypeDO">
        <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR"/>
        <result column="sign_name" property="signName" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="SignResultMap" type="com.xhqb.spectre.admin.bidata.entity.StatSignDO">
        <result column="stat_date" property="statDate" jdbcType="VARCHAR"/>
        <result column="sign_name" property="signName" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="TplReachResultMap" type="com.xhqb.spectre.admin.bidata.entity.TplReachRateDO">
        <result column="tpl_code" property="tplCode" jdbcType="VARCHAR"/>
        <result column="week1" property="week1" jdbcType="DOUBLE"/>
        <result column="week2" property="week2" jdbcType="DOUBLE"/>
        <result column="week3" property="week3" jdbcType="DOUBLE"/>
        <result column="week4" property="week4" jdbcType="DOUBLE"/>
    </resultMap>
    <resultMap id="TplChannelResultMap" type="com.xhqb.spectre.admin.bidata.entity.TplChannelRateDO">
        <result column="tpl_code" property="tplCode" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="send_count" property="sendCount" jdbcType="INTEGER"/>
        <result column="reach_count" property="reachCount" jdbcType="INTEGER"/>
        <result column="reach_rate" property="reachRate" jdbcType="DOUBLE"/>
    </resultMap>
    <resultMap id="TopStatResultMap" type="com.xhqb.spectre.admin.bidata.entity.TopReachStatDO">
        <id property="tplCode" column="tpl_code"/>
        <result property="signName" column="sign_name" jdbcType="VARCHAR"/>
        <result property="channelCode" column="channel_code"/>
        <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR"/>
        <result property="sendCount" column="send_count"/>
        <result property="reachCount" column="reach_count"/>
        <result property="reachRate" column="reach_rate"/>
    </resultMap>
    <resultMap id="TplHeatStatResultMap" type="com.xhqb.spectre.admin.bidata.entity.TplHeatStatDO">
        <result property="statDate" column="stat_date" jdbcType="VARCHAR"/>
        <result property="tplCode" column="tpl_code" jdbcType="VARCHAR"/>
        <result property="sendCount" column="send_count" jdbcType="INTEGER"/>
        <result property="reachCount" column="reach_count"/>
        <result property="reachBillCount" column="reach_bill_count" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="PriceResultMap" type="com.xhqb.spectre.admin.bidata.entity.ChannelPriceStatDO">
        <result column="stat_date" property="statDate" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="verify_reach_bill_count" property="verifyReachBillCount" jdbcType="INTEGER"/>
        <result column="notify_reach_bill_count" property="notifyReachBillCount" jdbcType="INTEGER"/>
        <result column="market_reach_bill_count" property="marketReachBillCount" jdbcType="INTEGER"/>
        <result column="collector_reach_bill_count" property="collectorReachBillCount" jdbcType="INTEGER"/>
        <result column="light_collector_reach_bill_count" property="lightCollectorReachBillCount" jdbcType="INTEGER"/>
        <result column="severe_collector_reach_bill_count" property="severeCollectorReachBillCount" jdbcType="INTEGER"/>
        <result column="debt_swap_reach_bill_count" property="debtSwapReachBillCount" jdbcType="INTEGER"/>
        <result column="verify_reach_rate" property="verifyReachRate" jdbcType="DOUBLE"/>
        <result column="notify_reach_rate" property="notifyReachRate" jdbcType="DOUBLE"/>
        <result column="market_reach_rate" property="marketReachRate" jdbcType="DOUBLE"/>
        <result column="collector_reach_rate" property="collectorReachRate" jdbcType="DOUBLE"/>
        <result column="light_collector_reach_rate" property="lightCollectorReachRate" jdbcType="DOUBLE"/>
        <result column="severe_collector_reach_rate" property="severeCollectorReachRate" jdbcType="DOUBLE"/>
        <result column="debt_swap_reach_rate" property="debtSwapReachRate" jdbcType="DOUBLE"/>
        <result column="avg_reach_rate" property="avgReachRate" jdbcType="DOUBLE"/>
        <result column="verify_price_count" property="verifyPriceCount" jdbcType="INTEGER"/>
        <result column="notify_price_count" property="notifyPriceCount" jdbcType="INTEGER"/>
        <result column="market_price_count" property="marketPriceCount" jdbcType="INTEGER"/>
        <result column="collector_price_count" property="collectorPriceCount" jdbcType="INTEGER"/>
        <result column="light_collector_price_count" property="lightCollectorPriceCount" jdbcType="INTEGER"/>
        <result column="severe_collector_price_count" property="severeCollectorPriceCount" jdbcType="INTEGER"/>
        <result column="debt_swap_price_count" property="debtSwapPriceCount" jdbcType="INTEGER"/>
        <result column="total_price_count" property="totalPriceCount" jdbcType="BIGINT"/>
        <result column="total_reach_bill_count" property="totalReachBillCount" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="MonthlyStatementResultMap" type="com.xhqb.spectre.admin.bidata.entity.ChannelMonthlyStatementDO">
        <result column="stat_date" property="statDate" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR"/>
        <result column="total_bill_count" property="totalBillCount" jdbcType="INTEGER"/>
        <result column="total_price_count" property="totalPriceCount" jdbcType="BIGINT"/>
        <result column="reach_rate" property="reachRate" jdbcType="DOUBLE"/>
        <result property="signName" column="sign_name" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="SendIspCodeDataMap" type="com.xhqb.spectre.admin.bidata.model.SendIspCodeDataDO">
        <result column="stat_date" property="statDate" jdbcType="VARCHAR"/>
        <result column="isp_code" property="ispCode" jdbcType="VARCHAR"/>
        <result column="total_send_count" property="sendCount" jdbcType="BIGINT"/>
        <result column="total_reach_count" property="reachCount" jdbcType="BIGINT"/>
    </resultMap>
    <resultMap id="TypePriceResultMap" type="com.xhqb.spectre.admin.bidata.entity.TypePriceStatDO">
        <result column="stat_date" property="statDate" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="price_count" property="priceCount" jdbcType="INTEGER"/>
        <result column="reach_bill_count" property="reachBillCount" jdbcType="INTEGER"/>
        <result column="reach_rate" property="reachRate" jdbcType="DOUBLE"/>
        <result column="avg_reach_rate" property="avgReachRate" jdbcType="DOUBLE"/>
        <result column="total_price_count" property="totalPriceCount" jdbcType="BIGINT"/>
        <result column="total_reach_bill_count" property="totalReachBillCount" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , stat_date, app_code, tpl_code
        , isp_code, sms_type_code, province_short_name
        , channel_code,send_count, reach_count
        , create_time, update_time,first_reach_count
    </sql>


    <select id="selectBySendTopTenQuery" resultType="com.xhqb.spectre.admin.bidata.model.SendFormDO">
        select ${query.groupByInfo} as code,sum(send_count) as sendCount
        from ${query.tableName}
        <where>
            <if test="query.startTime != null">
                stat_date <![CDATA[ >= ]]> DATE_FORMAT( #{query.startTime},'%Y-%m-%d')
            </if>
            <if test="query.endTime != null">
                and stat_date <![CDATA[ <= ]]> DATE_FORMAT( #{query.endTime},'%Y-%m-%d')
            </if>
            <if test="query.appCode != null and query.appCode != ''">
                and app_code = #{query.appCode}
            </if>
            <if test="query.ispCode != null and query.ispCode != ''">
                and isp_code in
                <foreach collection="query.ispCode.split(',')" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="query.tplCode != null and query.tplCode != ''">
                and tpl_code = #{query.tplCode}
            </if>
            <if test="query.channelCode != null and query.channelCode != ''">
                and channel_code = #{query.channelCode}
            </if>
            <if test="query.provinceShortName != null and query.provinceShortName != ''">
                and province_short_name = #{query.provinceShortName}
            </if>
            <if test="query.smsTypeCode != null and query.smsTypeCode != ''">
                and sms_type_code = #{query.smsTypeCode}
            </if>
        </where>
        group by ${query.groupByInfo}
    </select>
    <select id="selectByReachRateQuery" resultType="com.xhqb.spectre.admin.bidata.model.ReachRateDTO">
        select ${query.groupByInfo} as code, sum(send_count) as sendCount, sum(reach_count) as reachCount
        from ${query.tableName}
        <where>
            <if test="query.startTime != null">
                stat_date <![CDATA[ >= ]]> DATE_FORMAT( #{query.startTime},'%Y-%m-%d')
            </if>
            <if test="query.endTime != null">
                and stat_date <![CDATA[ <= ]]> DATE_FORMAT( #{query.endTime},'%Y-%m-%d')
            </if>
            <if test="query.appCode != null and query.appCode != ''">
                and app_code = #{query.appCode}
            </if>
            <if test="query.ispCode != null and query.ispCode != ''">
                and isp_code in
                <foreach collection="query.ispCode.split(',')" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="query.tplCode != null and query.tplCode != ''">
                and tpl_code = #{query.tplCode}
            </if>
            <if test="query.channelCode != null and query.channelCode != ''">
                and channel_code = #{query.channelCode}
            </if>
            <if test="query.provinceShortName != null and query.provinceShortName != ''">
                and province_short_name = #{query.provinceShortName}
            </if>
            <if test="query.smsTypeCode != null and  query.smsTypeCode != ''">
                and sms_type_code = #{query.smsTypeCode}
            </if>
        </where>
        group by ${query.groupByInfo}
    </select>
    <select id="selectByTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${query.tableName}
        <where>
            <if test="query.startTime != null">
                stat_date <![CDATA[ >= ]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and stat_date <![CDATA[ <= ]]> #{query.endTime}
            </if>
        </where>
    </select>
    <select id="selectSendTopTenByTplCode" resultType="com.xhqb.spectre.admin.bidata.model.SendFormDO">
        select channel_code as code, sum(send_count) as sendCount
        from ${query.tableName}
        <where>
            <if test="query.startTime != null">
                stat_date <![CDATA[ >= ]]> DATE_FORMAT( #{query.startTime},'%Y-%m-%d')
            </if>
            <if test="query.endTime != null">
                and stat_date <![CDATA[ <= ]]> DATE_FORMAT( #{query.endTime},'%Y-%m-%d')
            </if>
            <if test="query.appCode != null and query.appCode != ''">
                and app_code = #{query.appCode}
            </if>
            <if test="query.ispCode != null and query.ispCode != ''">
                and isp_code in
                <foreach collection="query.ispCode.split(',')" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="query.tplCode != null and query.tplCode != ''">
                and tpl_code = #{query.tplCode}
            </if>
            <if test="query.channelCode != null and query.channelCode != ''">
                and channel_code = #{query.channelCode}
            </if>
            <if test="query.provinceShortName != null and query.provinceShortName != ''">
                and province_short_name = #{query.provinceShortName}
            </if>
            <if test="query.smsTypeCode != null and  query.smsTypeCode != ''">
                and sms_type_code = #{query.smsTypeCode}
            </if>
        </where>
        group by channel_code
    </select>
    <select id="selectSendDetailByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${query.tableName}
        <where>
            <if test="query.startTime != null">
                stat_date <![CDATA[ >= ]]> DATE_FORMAT( #{query.startTime},'%Y-%m-%d')
            </if>
            <if test="query.endTime != null">
                and stat_date <![CDATA[ <= ]]> DATE_FORMAT( #{query.endTime},'%Y-%m-%d')
            </if>
            <if test="query.appCode != null and query.appCode != ''">
                and app_code = #{query.appCode}
            </if>
            <if test="query.ispCode != null and query.ispCode != ''">
                and isp_code in
                <foreach collection="query.ispCode.split(',')" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="query.provinceShortName != null and query.provinceShortName != ''">
                and province_short_name = #{query.provinceShortName}
            </if>
            <if test="query.smsTypeCode != null and query.smsTypeCode !=''">
                and sms_type_code = #{query.smsTypeCode}
            </if>
            <if test="query.channelCodes != null and  query.channelCodes.size() >0">
                and channel_code in
                <foreach collection="query.channelCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.tplCodes != null and  query.tplCodes.size() >0">
                and tpl_code in
                <foreach collection="query.tplCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by stat_date desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset}, #{query.pageParameter.pageSize}
        </if>
    </select>
    <select id="countSendDetailByQuery" resultType="java.lang.Integer">
        select count(*)
        from ${query.tableName}
        <where>
            <if test="query.startTime != null">
                stat_date <![CDATA[ >= ]]> DATE_FORMAT( #{query.startTime},'%Y-%m-%d')
            </if>
            <if test="query.endTime != null">
                and stat_date <![CDATA[ <= ]]> DATE_FORMAT( #{query.endTime},'%Y-%m-%d')
            </if>
            <if test="query.appCode != null and query.appCode !=''">
                and app_code = #{query.appCode}
            </if>
            <if test="query.ispCode != null and query.ispCode != ''">
                and isp_code in
                <foreach collection="query.ispCode.split(',')" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="query.provinceShortName != null and query.provinceShortName !=''">
                and province_short_name = #{query.provinceShortName}
            </if>
            <if test="query.smsTypeCode != null and query.smsTypeCode != ''">
                and sms_type_code = #{query.smsTypeCode}
            </if>
            <if test="query.channelCodes != null and  query.channelCodes.size() >0">
                and channel_code in
                <foreach collection="query.channelCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.tplCodes != null and  query.tplCodes.size() >0">
                and tpl_code in
                <foreach collection="query.tplCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectByMultipleCompareQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${query.tableName}
        <where>
            <if test="query.startTime != null">
                stat_date <![CDATA[ >= ]]> DATE_FORMAT( #{query.startTime},'%Y-%m-%d')
            </if>
            <if test="query.endTime != null">
                and stat_date <![CDATA[ <= ]]> DATE_FORMAT( #{query.endTime},'%Y-%m-%d')
            </if>
            <if test="query.appCode != null and query.appCode != ''">
                and app_code = #{query.appCode}
            </if>
            <if test="query.ispCode != null and query.ispCode != ''">
                and isp_code in
                <foreach collection="query.ispCode.split(',')" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="query.provinceShortName != null and query.provinceShortName != ''">
                and province_short_name = #{query.provinceShortName}
            </if>
            <if test="query.smsTypeCode != null and query.smsTypeCode != ''">
                and sms_type_code = #{query.smsTypeCode}
            </if>
            <if test="query.channelCodes != null and  query.channelCodes.size() >0">
                and channel_code in
                <foreach collection="query.channelCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.tplCodes != null and query.tplCodes.size() >0">
                and tpl_code in
                <foreach collection="query.tplCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getMonthStatsByChannelCode" resultMap="MonthResultMap">
        SELECT
        LEFT(stat_date, 7) AS stat_date,
        channel_code AS channel_code,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END), 0) AS verify_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END), 0) AS notify_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END), 0) AS market_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END), 0) AS collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END), 0) AS
        light_collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END), 0) AS
        severe_collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END), 0) AS debt_swap_reach_count,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN bill_reach_count END), 0) AS verify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN bill_reach_count END), 0) AS notify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN bill_reach_count END), 0) AS market_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN bill_reach_count END), 0) AS collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN bill_reach_count END), 0) AS
        light_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN bill_reach_count END), 0) AS
        severe_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN bill_reach_count END), 0) AS debt_swap_reach_bill_count,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'verify' THEN send_count END), 0), 2), 0) AS verify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'notify' THEN send_count END), 0), 2), 0) AS notify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'market' THEN send_count END), 0), 2), 0) AS market_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'collector' THEN send_count END), 0), 2), 0) AS collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'light_collector' THEN send_count END), 0), 2), 0) AS
        light_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN send_count END), 0), 2), 0) AS
        severe_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN send_count END), 0), 2), 0) AS debt_swap_reach_rate,

        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        COALESCE(SUM(reach_count), 0) AS total_reach_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count


        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="channelCode != null and channelCode != ''">
            AND channel_code = #{channelCode}
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        channel_code, LEFT(stat_date, 7)
        ORDER BY
        LEFT(stat_date, 7) DESC
    </select>

    <select id="getWeekStatsByChannelCode" resultMap="MonthResultMap">
        SELECT
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ) AS stat_date,
        channel_code,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END), 0) AS verify_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END), 0) AS notify_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END), 0) AS market_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END), 0) AS collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END), 0) AS
        light_collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END), 0) AS
        severe_collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END), 0) AS debt_swap_reach_count,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN bill_reach_count END), 0) AS verify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN bill_reach_count END), 0) AS notify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN bill_reach_count END), 0) AS market_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN bill_reach_count END), 0) AS collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN bill_reach_count END), 0) AS
        light_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN bill_reach_count END), 0) AS
        severe_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN bill_reach_count END), 0) AS debt_swap_reach_bill_count,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'verify' THEN send_count END), 0), 2), 0) AS verify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'notify' THEN send_count END), 0), 2), 0) AS notify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'market' THEN send_count END), 0), 2), 0) AS market_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'collector' THEN send_count END), 0), 2), 0) AS collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'light_collector' THEN send_count END), 0), 2), 0) AS
        light_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN send_count END), 0), 2), 0) AS
        severe_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN send_count END), 0), 2), 0) AS debt_swap_reach_rate,

        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        COALESCE(SUM(reach_count), 0) AS total_reach_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count

        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="channelCode != null and channelCode != ''">
            AND channel_code = #{channelCode}
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ),
        channel_code
        ORDER BY
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ) DESC
    </select>

    <select id="getMonthStatsByDate" resultMap="MonthResultMap">
        SELECT
        LEFT(stat_date, 7) AS stat_date,
        channel_code AS channel_code,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END), 0) AS verify_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END), 0) AS notify_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END), 0) AS market_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END), 0) AS collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END), 0) AS
        light_collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END), 0) AS
        severe_collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END), 0) AS debt_swap_reach_count,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN bill_reach_count END), 0) AS verify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN bill_reach_count END), 0) AS notify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN bill_reach_count END), 0) AS market_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN bill_reach_count END), 0) AS collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN bill_reach_count END), 0) AS
        light_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN bill_reach_count END), 0) AS
        severe_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN bill_reach_count END), 0) AS debt_swap_reach_bill_count,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'verify' THEN send_count END), 0), 2), 0) AS verify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'notify' THEN send_count END), 0), 2), 0) AS notify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'market' THEN send_count END), 0), 2), 0) AS market_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'collector' THEN send_count END), 0), 2), 0) AS collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'light_collector' THEN send_count END), 0), 2), 0) AS
        light_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN send_count END), 0), 2), 0) AS
        severe_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN send_count END), 0), 2), 0) AS debt_swap_reach_rate,


        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        -- 总 reach_count
        COALESCE(SUM(reach_count), 0) AS total_reach_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count

        FROM
        t_sms_send_stat
        WHERE
        channel_code != ''
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        LEFT(stat_date, 7), channel_code
        ORDER BY
        LEFT(stat_date, 7) DESC
    </select>

    <select id="getWeekStatsByDate" resultMap="MonthResultMap">
        SELECT
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ) AS stat_date,
        channel_code AS channel_code,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END), 0) AS verify_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END), 0) AS notify_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END), 0) AS market_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END), 0) AS collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END), 0) AS
        light_collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END), 0) AS
        severe_collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END), 0) AS debt_swap_reach_count,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN bill_reach_count END), 0) AS verify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN bill_reach_count END), 0) AS notify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN bill_reach_count END), 0) AS market_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN bill_reach_count END), 0) AS collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN bill_reach_count END), 0) AS
        light_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN bill_reach_count END), 0) AS
        severe_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN bill_reach_count END), 0) AS debt_swap_reach_bill_count,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'verify' THEN send_count END), 0), 2), 0) AS verify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'notify' THEN send_count END), 0), 2), 0) AS notify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'market' THEN send_count END), 0), 2), 0) AS market_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'collector' THEN send_count END), 0), 2), 0) AS collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'light_collector' THEN send_count END), 0), 2), 0) AS
        light_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN send_count END), 0), 2), 0) AS
        severe_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN send_count END), 0), 2), 0) AS debt_swap_reach_rate,


        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        -- 总 reach_count
        COALESCE(SUM(reach_count), 0) AS total_reach_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count

        FROM t_sms_send_stat
        WHERE channel_code != ''
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>

        GROUP BY
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ),
        channel_code

        ORDER BY stat_date DESC
    </select>

    <select id="getMonthSubReachStats" resultMap="SubResultMap">
        SELECT
        LEFT(stat_date, 7) AS stat_date,
        channel_code AS channel_code,
        tpl_code AS tpl_code,
        sign_name AS sign_name,
        sms_type_code AS sms_type_code,
        SUM(reach_count) AS reach_count,
        SUM(bill_reach_count) AS reach_bill_count,
        COALESCE(SUM(price), 0) AS price_count,
        ROUND(SUM(reach_count) /
        NULLIF(SUM(send_count), 0), 2) AS reach_rate
        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <choose>
            <when test="channelCodes != null and channelCodes.size() > 0">
                AND channel_code IN
                <foreach item="channelCode" collection="channelCodes" separator="," open="(" close=")">
                    #{channelCode}
                </foreach>
            </when>
            <when test="channelCode != null and channelCode != ''">
                AND channel_code = #{channelCode}
            </when>
        </choose>
        <choose>
            <when test="smsTypeCode != null and smsTypeCode != ''">
                AND sms_type_code = #{smsTypeCode}
            </when>
            <when test="smsTypeCodes != null and smsTypeCodes.size() > 0">
                AND sms_type_code IN
                <foreach item="item" collection="smsTypeCodes" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </when>
        </choose>

        <if test="tplCode != null and tplCode != ''">
            AND tpl_code = #{tplCode}
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        tpl_code, sign_name, LEFT(stat_date, 7)
        ORDER BY
        tpl_code, sign_name, LEFT(stat_date, 7) DESC
        <if test="pageParameter != null">
            limit #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>

    <select id="getWeekSubReachStats" resultMap="SubResultMap">
        SELECT
        DATE_FORMAT(stat_date, '%yW%v') AS stat_date,
        channel_code AS channel_code,
        tpl_code AS tpl_code,
        sign_name AS sign_name,
        sms_type_code AS sms_type_code,
        SUM(reach_count) AS reach_count,
        SUM(bill_reach_count) AS reach_bill_count,
        COALESCE(SUM(price), 0) AS price_count,
        ROUND(SUM(reach_count) /
        NULLIF(SUM(send_count), 0), 2) AS reach_rate
        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>

        <!-- 其他筛选条件 -->

        <choose>
            <when test="channelCodes != null and channelCodes.size() > 0">
                AND channel_code IN
                <foreach item="channelCode" collection="channelCodes" separator="," open="(" close=")">
                    #{channelCode}
                </foreach>
            </when>
            <when test="channelCode != null and channelCode != ''">
                AND channel_code = #{channelCode}
            </when>
        </choose>
        <choose>
            <when test="smsTypeCodes != null and smsTypeCodes.size() > 0">
                AND sms_type_code IN
                <foreach item="item" collection="smsTypeCodes" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </when>
            <when test="smsTypeCode != null and smsTypeCode != ''">
                AND sms_type_code = #{smsTypeCode}
            </when>
        </choose>
        <if test="tplCode != null and tplCode != ''">
            AND tpl_code = #{tplCode}
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>

        GROUP BY
        tpl_code, sign_name, DATE_FORMAT(stat_date, '%yW%v')
        ORDER BY
        tpl_code, sign_name, DATE_FORMAT(stat_date, '%yW%v') DESC

        <if test="pageParameter != null">
            LIMIT #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>

    <select id="getStatChannelList" resultMap="ChannelResultMap">
        SELECT
        channel_code,
        sign_name
        FROM
        t_sms_send_stat
        WHERE
        channel_code != ''
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        GROUP BY
        channel_code, sign_name
    </select>

    <select id="getStatSignList" resultMap="SignResultMap">
        SELECT
        LEFT(stat_date, 7) AS stat_date,
        sign_name
        FROM
        t_sms_send_stat
        WHERE
        channel_code != ''
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        GROUP BY
        LEFT(stat_date, 7), sign_name
    </select>

    <select id="getStatSmsTypeList" resultMap="SmsTypeResultMap">
        SELECT
        sms_type_code,
        sign_name
        FROM
        t_sms_send_stat
        WHERE
        channel_code != ''
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        GROUP BY
        sms_type_code, sign_name
    </select>

    <select id="getMonthStatsBySmsType" resultMap="MonthResultMap">
        SELECT
        LEFT(stat_date, 7) AS stat_date,
        channel_code AS channel_code,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END), 0) AS verify_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END), 0) AS notify_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END), 0) AS market_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END), 0) AS collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END), 0) AS
        light_collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END), 0) AS
        severe_collector_reach_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END), 0) AS debt_swap_reach_count,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN bill_reach_count END), 0) AS verify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN bill_reach_count END), 0) AS notify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN bill_reach_count END), 0) AS market_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN bill_reach_count END), 0) AS collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN bill_reach_count END), 0) AS
        light_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN bill_reach_count END), 0) AS
        severe_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN bill_reach_count END), 0) AS debt_swap_reach_bill_count,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'verify' THEN send_count END), 0), 2), 0) AS verify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'notify' THEN send_count END), 0), 2), 0) AS notify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'market' THEN send_count END), 0), 2), 0) AS market_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'collector' THEN send_count END), 0), 2), 0) AS collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'light_collector' THEN send_count END), 0), 2), 0) AS
        light_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN send_count END), 0), 2), 0) AS
        severe_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN send_count END), 0), 2), 0) AS debt_swap_reach_rate,


        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 1), 0) AS avg_reach_rate,

        -- 总 reach_count
        COALESCE(SUM(reach_count), 0) AS total_reach_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count

        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="channelCode != null and channelCode != ''">
            AND channel_code = #{channelCode}
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        channel_code, LEFT(stat_date, 7)
        ORDER BY
        LEFT(stat_date, 7), channel_code DESC
    </select>


    <select id="getMonthStatsByType" resultMap="TypeResultMap">
        SELECT
        LEFT(stat_date, 7) AS stat_date,
        channel_code AS channel_code,

        COALESCE(SUM(reach_count), 0) AS reach_count,

        COALESCE(SUM(bill_reach_count), 0) AS reach_bill_count,

        COALESCE(ROUND(
        SUM(reach_count) /
        NULLIF(SUM(send_count), 0), 2), 0) AS reach_rate,


        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        -- 总 reach_count
        COALESCE(SUM(reach_count), 0) AS total_reach_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count

        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="smsTypeCode != null and smsTypeCode != ''">
            AND sms_type_code = #{smsTypeCode}
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        channel_code, LEFT(stat_date, 7)
        ORDER BY
        LEFT(stat_date, 7), channel_code DESC
    </select>

    <select id="getWeekStatsByType" resultMap="TypeResultMap">
        SELECT
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ) AS stat_date,
        channel_code,

        COALESCE(SUM(reach_count), 0) AS reach_count,

        COALESCE(SUM(bill_reach_count), 0) AS reach_bill_count,

        COALESCE(ROUND(
        SUM(reach_count) /
        NULLIF(SUM(send_count), 0), 2), 0) AS reach_rate,

        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        COALESCE(SUM(reach_count), 0) AS total_reach_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count

        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="smsTypeCode != null and smsTypeCode != ''">
            AND sms_type_code = #{smsTypeCode}
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ),
        channel_code
        ORDER BY
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ),
        channel_code DESC
    </select>


    <select id="getAbnormalTplReachRates" resultMap="TplReachResultMap">
        <![CDATA[
        WITH weekly_stats AS (
            SELECT
                tpl_code,
                YEARWEEK(STR_TO_DATE(stat_date, '%Y-%m-%d'), 3) AS year_week,
                ROUND(SUM(reach_count) / NULLIF(SUM(send_count), 0), 2) AS reach_rate
            FROM t_sms_send_stat
            WHERE stat_date >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 28 DAY), '%Y-%m-%d')
              AND tpl_code != ''
            GROUP BY tpl_code, YEARWEEK(STR_TO_DATE(stat_date, '%Y-%m-%d'), 3)
        ),

        pivot_table AS (
            SELECT
                tpl_code,
                MAX(CASE WHEN year_week = YEARWEEK(DATE_SUB(CURDATE(), INTERVAL 0 DAY), 3) THEN reach_rate END) AS week1,
                MAX(CASE WHEN year_week = YEARWEEK(DATE_SUB(CURDATE(), INTERVAL 7 DAY), 3) THEN reach_rate END) AS week2,
                MAX(CASE WHEN year_week = YEARWEEK(DATE_SUB(CURDATE(), INTERVAL 14 DAY), 3) THEN reach_rate END) AS week3,
                MAX(CASE WHEN year_week = YEARWEEK(DATE_SUB(CURDATE(), INTERVAL 21 DAY), 3) THEN reach_rate END) AS week4
            FROM weekly_stats
            GROUP BY tpl_code
        ),
        decline_or_low_check AS (
            SELECT
                tpl_code,
                week1,
                week2,
                week3,
                week4,
                GREATEST(COALESCE(week1, -1), COALESCE(week2, -1), COALESCE(week3, -1), COALESCE(week4, -1)) -
                LEAST(
                    CASE WHEN week1 IS NOT NULL THEN week1 ELSE 1 END,
                    CASE WHEN week2 IS NOT NULL THEN week2 ELSE 1 END,
                    CASE WHEN week3 IS NOT NULL THEN week3 ELSE 1 END,
                    CASE WHEN week4 IS NOT NULL THEN week4 ELSE 1 END
                ) AS diff,

                CASE
                    WHEN (
                        (week4 IS NOT NULL AND week3 IS NOT NULL AND week4 > week3) AND
                        (week3 IS NOT NULL AND week2 IS NOT NULL AND week3 > week2) AND
                        (week2 IS NOT NULL AND week1 IS NOT NULL AND week2 > week1)
                    )
                    THEN 1 ELSE 0
                END AS is_declining,

                CASE
                    WHEN (week1 IS NOT NULL AND week1 <= 0.2)
                       OR (week2 IS NOT NULL AND week2 <= 0.2)
                       OR (week3 IS NOT NULL AND week3 <= 0.2)
                       OR (week4 IS NOT NULL AND week4 <= 0.2)
                    THEN 1 ELSE 0
                END AS has_low_week
            FROM pivot_table
        )

        SELECT
            tpl_code,
            CAST(week4 AS CHAR) AS week4,
            CAST(week3 AS CHAR) AS week3,
            CAST(week2 AS CHAR) AS week2,
            CAST(week1 AS CHAR) AS week1
        FROM decline_or_low_check
        WHERE (is_declining = 1 AND diff >= 0.3)
           OR has_low_week = 1
        ORDER BY tpl_code
        ]]>
        <if test="pageParameter != null">
            limit #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>


    <select id="getAbnormalTplChannelReachRate" resultMap="TplChannelResultMap">
    <![CDATA[
        WITH reach_stats AS (SELECT tpl_code,
                                    channel_code,
                                    SUM(send_count)                                         AS send_cnt,
                                    SUM(reach_count)                                        AS reach_cnt,
                                    ROUND(SUM(reach_count) / NULLIF(SUM(send_count), 0), 4) AS reach_rate
                             FROM t_sms_send_stat
                             WHERE stat_date BETWEEN #{startDate} AND #{endDate}
                             GROUP BY tpl_code, channel_code),
             tpl_max_min AS (SELECT tpl_code,
                                    MAX(reach_rate) AS max_rate,
                                    MIN(reach_rate) AS min_rate
                             FROM reach_stats
                             GROUP BY tpl_code
                             HAVING (MAX(reach_rate) - MIN(reach_rate)) >= #{rateGap})
        SELECT r.tpl_code,
               r.channel_code,
               r.send_cnt             AS send_count,
               r.reach_cnt            AS reach_count,
               ROUND(r.reach_rate, 2) AS reach_rate
        FROM reach_stats r
                 JOIN tpl_max_min t ON r.tpl_code = t.tpl_code
        WHERE r.send_cnt > #{minSendCount}
        ORDER BY r.tpl_code, r.reach_rate DESC
        ]]>
  </select>

    <select id="queryTplChannelStats" resultMap="TopStatResultMap">
        WITH top_tpls AS (
        SELECT tpl_code
        FROM (
        SELECT tpl_code, SUM(reach_count) AS total_reach
        FROM t_sms_send_stat
        WHERE sms_type_code = #{smsTypeCode}
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        GROUP BY tpl_code
        ORDER BY total_reach DESC
        LIMIT 20
        ) t
        )
        SELECT
        s.tpl_code,
        s.channel_code,
        SUM(s.send_count) AS send_count,
        SUM(s.reach_count) AS reach_count,
        ROUND(SUM(s.reach_count) / NULLIF(SUM(s.send_count), 0), 2) AS reach_rate
        FROM t_sms_send_stat s
                 JOIN top_tpls t ON s.tpl_code = t.tpl_code
        WHERE 1=1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND s.stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND s.stat_date <= #{endDate} ]]>
        </if>
        GROUP BY s.tpl_code, s.channel_code
        ORDER BY s.tpl_code, reach_count DESC
    </select>

    <select id="selectReachRateByTimeAndType" resultType="com.xhqb.spectre.admin.bidata.entity.ReachRateDO">
        SELECT
        tpl_code as tplCode,
        SUM(reach_count) * 1.0 / NULLIF(SUM(send_count), 0) AS reachRate,
        SUM(send_count) AS sendCount
        FROM
        t_sms_send_stat
        WHERE
        stat_date = #{startDate}
        AND sms_type_code IN
        <foreach item="item" collection="smsTypeCodes" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY tpl_code
    </select>

    <select id="queryOverallByTplHeat" resultMap="TplHeatStatResultMap">
        SELECT
        stat_date,
        <choose>
            <when test="query.tplCode != null and query.tplCode != ''">
                tpl_code,
            </when>
            <otherwise>
                '' AS tpl_code,
            </otherwise>
        </choose>
        SUM(send_count) AS send_count,
        SUM(reach_count) AS reach_count,
        SUM(bill_reach_count) AS reach_bill_count
        from ${query.tableName}
        WHERE
        stat_date BETWEEN #{query.startTime} AND #{query.endTime}
        AND sms_type_code = #{query.smsTypeCode}
        <choose>
            <when test="query.tplCode != null and query.tplCode != ''">
                AND tpl_code = #{query.tplCode}
            </when>
            <when test="query.tplCodes != null and query.tplCodes.size() > 0">
                AND tpl_code IN
                <foreach item="tplCode" collection="query.tplCodes" separator="," open="(" close=")">
                    #{tplCode}
                </foreach>
            </when>
        </choose>

        GROUP BY stat_date
        ORDER BY stat_date, reach_bill_count DESC
    </select>

    <select id="queryStatByTplHeat" resultMap="TplHeatStatResultMap">
        SELECT
        stat_date,
        tpl_code,
        SUM(send_count) AS send_count,
        SUM(reach_count) AS reach_count,
        SUM(bill_reach_count) AS reach_bill_count
        from ${query.tableName}
        WHERE
        stat_date BETWEEN #{query.startTime} AND #{query.endTime}
        AND sms_type_code = #{query.smsTypeCode}
        <choose>
            <when test="query.tplCode != null and query.tplCode != ''">
                AND tpl_code = #{query.tplCode}
            </when>
            <when test="query.tplCodes != null and query.tplCodes.size() > 0">
                AND tpl_code IN
                <foreach item="tplCode" collection="query.tplCodes" separator="," open="(" close=")">
                    #{tplCode}
                </foreach>
            </when>
        </choose>
        GROUP BY stat_date, tpl_code
        ORDER BY stat_date, reach_bill_count DESC
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset}, #{query.pageParameter.pageSize}
        </if>
    </select>

    <select id="queryStatByTplHeatCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM (
        SELECT
        stat_date,
        tpl_code
        from ${query.tableName}
        WHERE
        stat_date BETWEEN #{query.startTime} AND #{query.endTime}
        AND sms_type_code = #{query.smsTypeCode}
        <choose>
            <when test="query.tplCode != null and query.tplCode != ''">
                AND tpl_code = #{query.tplCode}
            </when>
            <when test="query.tplCodes != null and query.tplCodes.size() > 0">
                AND tpl_code IN
                <foreach item="tplCode" collection="query.tplCodes" separator="," open="(" close=")">
                    #{tplCode}
                </foreach>
            </when>
        </choose>
        GROUP BY stat_date, tpl_code
        ) grouped;
    </select>


    <select id="getMonthPriceStatsByChannelCode" resultMap="PriceResultMap">
        SELECT
        LEFT(stat_date, 7) AS stat_date,
        channel_code AS channel_code,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN price END), 0) AS verify_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN price END), 0) AS notify_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN price END), 0) AS market_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN price END), 0) AS collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN price END), 0) AS light_collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN price END), 0) AS severe_collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN price END), 0) AS debt_swap_reach_count,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN bill_reach_count END), 0) AS verify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN bill_reach_count END), 0) AS notify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN bill_reach_count END), 0) AS market_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN bill_reach_count END), 0) AS collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN bill_reach_count END), 0) AS
        light_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN bill_reach_count END), 0) AS
        severe_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN bill_reach_count END), 0) AS debt_swap_reach_bill_count,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'verify' THEN send_count END), 0), 2), 0) AS verify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'notify' THEN send_count END), 0), 2), 0) AS notify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'market' THEN send_count END), 0), 2), 0) AS market_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'collector' THEN send_count END), 0), 2), 0) AS collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'light_collector' THEN send_count END), 0), 2), 0) AS
        light_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN send_count END), 0), 2), 0) AS
        severe_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN send_count END), 0), 2), 0) AS debt_swap_reach_rate,

        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        -- 总 reach_count
        COALESCE(SUM(price), 0) AS total_price_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count


        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="channelCode != null and channelCode != ''">
            AND channel_code = #{channelCode}
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        channel_code, LEFT(stat_date, 7)
        ORDER BY
        LEFT(stat_date, 7) DESC
    </select>

    <select id="getWeekPriceStatsByChannelCode" resultMap="PriceResultMap">
        SELECT
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ) AS stat_date,
        channel_code,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN price END), 0) AS verify_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN price END), 0) AS notify_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN price END), 0) AS market_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN price END), 0) AS collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN price END), 0) AS light_collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN price END), 0) AS severe_collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN price END), 0) AS debt_swap_reach_count,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN bill_reach_count END), 0) AS verify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN bill_reach_count END), 0) AS notify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN bill_reach_count END), 0) AS market_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN bill_reach_count END), 0) AS collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN bill_reach_count END), 0) AS
        light_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN bill_reach_count END), 0) AS
        severe_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN bill_reach_count END), 0) AS debt_swap_reach_bill_count,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'verify' THEN send_count END), 0), 2), 0) AS verify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'notify' THEN send_count END), 0), 2), 0) AS notify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'market' THEN send_count END), 0), 2), 0) AS market_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'collector' THEN send_count END), 0), 2), 0) AS collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'light_collector' THEN send_count END), 0), 2), 0) AS
        light_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN send_count END), 0), 2), 0) AS
        severe_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN send_count END), 0), 2), 0) AS debt_swap_reach_rate,

        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        COALESCE(SUM(price), 0) AS total_price_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count

        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="channelCode != null and channelCode != ''">
            AND channel_code = #{channelCode}
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ),
        channel_code
        ORDER BY
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ) DESC
    </select>

    <select id="getMonthPriceStats" resultMap="PriceResultMap">
        SELECT
        LEFT(stat_date, 7) AS stat_date,
        '' AS channel_code,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN price END), 0) AS verify_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN price END), 0) AS notify_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN price END), 0) AS market_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN price END), 0) AS collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN price END), 0) AS light_collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN price END), 0) AS severe_collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN price END), 0) AS debt_swap_price_count,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN bill_reach_count END), 0) AS verify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN bill_reach_count END), 0) AS notify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN bill_reach_count END), 0) AS market_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN bill_reach_count END), 0) AS collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN bill_reach_count END), 0) AS
        light_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN bill_reach_count END), 0) AS
        severe_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN bill_reach_count END), 0) AS debt_swap_reach_bill_count,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'verify' THEN send_count END), 0), 2), 0) AS verify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'notify' THEN send_count END), 0), 2), 0) AS notify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'market' THEN send_count END), 0), 2), 0) AS market_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'collector' THEN send_count END), 0), 2), 0) AS collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'light_collector' THEN send_count END), 0), 2), 0) AS
        light_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN send_count END), 0), 2), 0) AS
        severe_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN send_count END), 0), 2), 0) AS debt_swap_reach_rate,

        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        COALESCE(SUM(price), 0) AS total_price_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count


        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="channelCodes != null and channelCodes.size() > 0">
            AND channel_code IN
            <foreach collection="channelCodes" item="channelCode" open="(" separator="," close=")">
                #{channelCode}
            </foreach>
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        LEFT(stat_date, 7)
        ORDER BY
        LEFT(stat_date, 7) DESC
    </select>

    <select id="getWeekPriceStats" resultMap="PriceResultMap">
        SELECT
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ) AS stat_date,
        '' AS channel_code,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN price END), 0) AS verify_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN price END), 0) AS notify_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN price END), 0) AS market_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN price END), 0) AS collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN price END), 0) AS light_collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN price END), 0) AS severe_collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN price END), 0) AS debt_swap_price_count,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN bill_reach_count END), 0) AS verify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN bill_reach_count END), 0) AS notify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN bill_reach_count END), 0) AS market_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN bill_reach_count END), 0) AS collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN bill_reach_count END), 0) AS
        light_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN bill_reach_count END), 0) AS
        severe_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN bill_reach_count END), 0) AS debt_swap_reach_bill_count,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'verify' THEN send_count END), 0), 2), 0) AS verify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'notify' THEN send_count END), 0), 2), 0) AS notify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'market' THEN send_count END), 0), 2), 0) AS market_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'collector' THEN send_count END), 0), 2), 0) AS collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'light_collector' THEN send_count END), 0), 2), 0) AS
        light_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN send_count END), 0), 2), 0) AS
        severe_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN send_count END), 0), 2), 0) AS debt_swap_reach_rate,

        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        COALESCE(SUM(price), 0) AS total_price_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count

        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="channelCodes != null and channelCodes.size() > 0">
            AND channel_code IN
            <foreach collection="channelCodes" item="channelCode" open="(" separator="," close=")">
                #{channelCode}
            </foreach>
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        )
        ORDER BY
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ) DESC
    </select>


    <select id="getMonthlyStatementStats" resultMap="MonthlyStatementResultMap">
        SELECT
        LEFT(stat_date, 7) AS stat_date,
        channel_code,
        sms_type_code,
        TRIM(REPLACE(sign_name, '\r', '')) AS sign_name,

        COALESCE(SUM(bill_reach_count), 0) AS total_bill_count,

        COALESCE(SUM(price), 0) AS total_price_count,

        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS reach_rate
        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="channelCode != null and channelCode != ''">
            AND channel_code = #{channelCode}
        </if>
        <if test="signList != null and signList.size() > 0">
            AND TRIM(REPLACE(sign_name, '\r', '')) IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        channel_code, LEFT(stat_date, 7), sms_type_code, TRIM(REPLACE(sign_name, '\r', ''))
        ORDER BY sms_type_code
    </select>

    <select id="getMonthlyStatementRecords" resultMap="MonthlyStatementResultMap">
        SELECT
        stat_date,
        channel_code,
        sms_type_code,
        TRIM(REPLACE(sign_name, '\r', '')) as sign_name,

        COALESCE(SUM(bill_reach_count), 0) AS total_bill_count,

        COALESCE(SUM(price), 0) AS total_price_count,

        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS reach_rate
        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="channelCode != null and channelCode != ''">
            AND channel_code = #{channelCode}
        </if>
        <if test="signList != null and signList.size() > 0">
            AND TRIM(REPLACE(sign_name, '\r', '')) IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        channel_code, stat_date, sms_type_code, TRIM(REPLACE(sign_name, '\r', ''))
    </select>

    <select id="queryTopTplChannelStats" resultMap="TopStatResultMap">
        WITH top_tpls AS (
        SELECT tpl_code
        FROM (
        SELECT tpl_code, SUM(reach_count) AS total_reach
        FROM t_sms_send_stat
        WHERE 1=1
        <if test="smsTypeCodes != null and smsTypeCodes.size() > 0">
            AND sms_type_code IN
            <foreach collection="smsTypeCodes" item="smsTypeCode" open="(" separator="," close=")">
                #{smsTypeCode}
            </foreach>
        </if>

        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        GROUP BY tpl_code
        ORDER BY total_reach DESC
        LIMIT 50
        ) t
        )
        SELECT
        s.tpl_code,
        s.sign_name,
        s.channel_code,
        s.sms_type_code,
        SUM(s.send_count) AS send_count,
        SUM(s.reach_count) AS reach_count,
        ROUND(SUM(s.reach_count) / NULLIF(SUM(s.send_count), 0), 2) AS reach_rate
        FROM t_sms_send_stat s
        JOIN top_tpls t ON s.tpl_code = t.tpl_code
        WHERE 1=1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND s.stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND s.stat_date <= #{endDate} ]]>
        </if>
        GROUP BY s.tpl_code, s.sign_name, s.channel_code
        ORDER BY s.tpl_code, reach_count DESC
    </select>
    <select id="selectDataByTimeAndIspCode" resultMap="SendIspCodeDataMap">
        SELECT CASE
                   WHEN isp_code IN ('电信', '联通', '移动') THEN isp_code
                   ELSE '其他'
                   END          AS isp_code,
               stat_date,
               SUM(send_count)  AS total_send_count,
               SUM(reach_count) AS total_reach_count
        FROM t_sms_send_stat
        WHERE stat_date <![CDATA[ >= ]]> DATE_FORMAT(#{startDate}, '%Y-%m-%d')
          and stat_date <![CDATA[ <= ]]> DATE_FORMAT(#{endDate}, '%Y-%m-%d')
        GROUP BY isp_code, stat_date
    </select>
    <select id="selectTplCodeByDate" resultType="java.lang.String">
        SELECT tpl_code
        FROM t_sms_send_stat
        WHERE stat_date >= #{t1Date}
        GROUP BY tpl_code
        ORDER BY SUM(send_count) DESC
            LIMIT #{topN}
    </select>
    <select id="selectSendCountTopByTplCodes" resultType="com.xhqb.spectre.admin.bidata.model.SendCountTopDO">
        SELECT stat_date AS statDate,
        tpl_code AS tplCode,
        isp_code AS ispCode,
        SUM(send_count) AS sendCount,
        SUM(reach_count) AS reachCount
        FROM t_sms_send_stat
        WHERE stat_date >= #{t3Date}
        AND tpl_code IN
        <foreach item="item" collection="tplCodes" separator="," close=")" open="(">
            #{item}
        </foreach>
        GROUP BY stat_date,tpl_code,isp_code
    </select>

    <update id="updatePriceByChannelAndTypeAndDate">
        UPDATE t_sms_send_stat
        SET price = #{pricePerSms} * bill_reach_count
        WHERE stat_date = #{statDate}
          AND channel_code = #{channelCode}
          AND sms_type_code = #{smsTypeCode}
    </update>

    <update id="updatePriceByChannelAndType">
        UPDATE t_sms_send_stat
        SET price = #{pricePerSms} * bill_reach_count
        WHERE channel_code = #{channelCode}
          AND sms_type_code = #{smsTypeCode}
    </update>
    <select id="selectTplCodeByStatDateAndCount" resultType="java.lang.String">
        select tpl_code
        from t_sms_send_stat
        where stat_date = #{statDate}
        group by tpl_code
        order by sum(send_count) desc
            limit #{count}
    </select>


    <select id="selectTopTplCodesByCreatorTplCodes" resultType="java.lang.String">
        SELECT tpl_code
        FROM t_sms_send_stat
        WHERE stat_date = #{t1Date}
        AND tpl_code IN
        <foreach item="item" collection="tplCodeList" separator="," close=")" open="(">
            #{item}
        </foreach>
        GROUP BY tpl_code
        ORDER BY SUM(send_count) DESC
        LIMIT #{topN}
    </select>

    <update id="updatePriceByChannelAndTypeAndDateFrom">
        UPDATE t_sms_send_stat
        SET price = #{pricePerSms} * bill_reach_count
        WHERE <![CDATA[ stat_date >= #{statDate} ]]>
          AND channel_code = #{channelCode}
          AND sms_type_code = #{smsTypeCode}
            <if test="signName != null and signName != ''">
                <![CDATA[ AND TRIM(REPLACE(sign_name, '\r', '')) = #{signName} ]]>
            </if>
    </update>


    <update id="updatePriceByChannelAndTypeAndDateBefore">
        UPDATE t_sms_send_stat
        SET price = #{pricePerSms} * bill_reach_count
        WHERE <![CDATA[ stat_date < #{statDate} ]]>
        AND channel_code = #{channelCode}
        AND sms_type_code = #{smsTypeCode}
        <if test="signName != null and signName != ''">
            <![CDATA[ AND TRIM(REPLACE(sign_name, '\r', '')) = #{signName} ]]>
        </if>
    </update>

    <select id="getMonthPriceStatsByDate" resultMap="PriceResultMap">
        SELECT
        LEFT(stat_date, 7) AS stat_date,
        channel_code AS channel_code,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN price END), 0) AS verify_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN price END), 0) AS notify_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN price END), 0) AS market_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN price END), 0) AS collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN price END), 0) AS light_collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN price END), 0) AS severe_collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN price END), 0) AS debt_swap_price_count,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN bill_reach_count END), 0) AS verify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN bill_reach_count END), 0) AS notify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN bill_reach_count END), 0) AS market_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN bill_reach_count END), 0) AS collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN bill_reach_count END), 0) AS
        light_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN bill_reach_count END), 0) AS
        severe_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN bill_reach_count END), 0) AS debt_swap_reach_bill_count,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'verify' THEN send_count END), 0), 2), 0) AS verify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'notify' THEN send_count END), 0), 2), 0) AS notify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'market' THEN send_count END), 0), 2), 0) AS market_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'collector' THEN send_count END), 0), 2), 0) AS collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'light_collector' THEN send_count END), 0), 2), 0) AS
        light_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN send_count END), 0), 2), 0) AS
        severe_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN send_count END), 0), 2), 0) AS debt_swap_reach_rate,


        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        -- 总 reach_count
        COALESCE(SUM(price), 0) AS total_price_count,
        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count

        FROM
        t_sms_send_stat
        WHERE
        channel_code != ''
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="smsTypeCodes != null and smsTypeCodes.size() > 0">
            AND sms_type_code IN
            <foreach collection="smsTypeCodes" item="smsTypeCode" open="(" separator="," close=")">
                #{smsTypeCode}
            </foreach>
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        LEFT(stat_date, 7), channel_code
        ORDER BY
        LEFT(stat_date, 7) DESC
    </select>

    <select id="getWeekPriceStatsByDate" resultMap="PriceResultMap">
        SELECT
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ) AS stat_date,
        channel_code AS channel_code,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN price END), 0) AS verify_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN price END), 0) AS notify_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN price END), 0) AS market_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN price END), 0) AS collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN price END), 0) AS light_collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN price END), 0) AS severe_collector_price_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN price END), 0) AS debt_swap_price_count,

        COALESCE(SUM(CASE WHEN sms_type_code = 'verify' THEN bill_reach_count END), 0) AS verify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'notify' THEN bill_reach_count END), 0) AS notify_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'market' THEN bill_reach_count END), 0) AS market_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'collector' THEN bill_reach_count END), 0) AS collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'light_collector' THEN bill_reach_count END), 0) AS
        light_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN bill_reach_count END), 0) AS
        severe_collector_reach_bill_count,
        COALESCE(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN bill_reach_count END), 0) AS debt_swap_reach_bill_count,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'verify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'verify' THEN send_count END), 0), 2), 0) AS verify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'notify' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'notify' THEN send_count END), 0), 2), 0) AS notify_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'market' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'market' THEN send_count END), 0), 2), 0) AS market_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'collector' THEN send_count END), 0), 2), 0) AS collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'light_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'light_collector' THEN send_count END), 0), 2), 0) AS
        light_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'severe_collector' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'severe_collector' THEN send_count END), 0), 2), 0) AS
        severe_collector_reach_rate,

        COALESCE(ROUND(
        SUM(CASE WHEN sms_type_code = 'debt_swap' THEN reach_count END) /
        NULLIF(SUM(CASE WHEN sms_type_code = 'debt_swap' THEN send_count END), 0), 2), 0) AS debt_swap_reach_rate,


        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        -- 总 reach_count
        COALESCE(SUM(price), 0) AS total_price_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count

        FROM t_sms_send_stat
        WHERE channel_code != ''
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="smsTypeCodes != null and smsTypeCodes.size() > 0">
            AND sms_type_code IN
            <foreach collection="smsTypeCodes" item="smsTypeCode" open="(" separator="," close=")">
                #{smsTypeCode}
            </foreach>
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>

        GROUP BY
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ),
        channel_code

        ORDER BY stat_date DESC
    </select>



    <select id="getMonthPriceStatsByType" resultMap="TypePriceResultMap">
        SELECT
        LEFT(stat_date, 7) AS stat_date,
        channel_code AS channel_code,

        COALESCE(SUM(price), 0) AS price_count,

        COALESCE(SUM(bill_reach_count), 0) AS reach_bill_count,

        COALESCE(ROUND(
        SUM(reach_count) /
        NULLIF(SUM(send_count), 0), 2), 0) AS reach_rate,


        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        -- 总 reach_count
        COALESCE(SUM(price), 0) AS total_price_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count

        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="channelCode != null and channelCode != ''">
            AND channel_code = #{channelCode}
        </if>
        <if test="smsTypeCode != null and smsTypeCode != ''">
            AND sms_type_code = #{smsTypeCode}
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        channel_code, LEFT(stat_date, 7)
        ORDER BY
        LEFT(stat_date, 7), channel_code DESC
    </select>

    <select id="getWeekPriceStatsByType" resultMap="TypePriceResultMap">
        SELECT
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ) AS stat_date,
        channel_code,

        COALESCE(SUM(price), 0) AS price_count,

        COALESCE(SUM(bill_reach_count), 0) AS reach_bill_count,

        COALESCE(ROUND(
        SUM(reach_count) /
        NULLIF(SUM(send_count), 0), 2), 0) AS reach_rate,

        COALESCE(ROUND(AVG(CASE
        WHEN send_count > 0 THEN (reach_count * 1.0 / send_count)
        ELSE NULL
        END), 2), 0) AS avg_reach_rate,

        COALESCE(SUM(price), 0) AS total_price_count,

        COALESCE(SUM(bill_reach_count), 0) AS total_reach_bill_count

        FROM
        t_sms_send_stat
        WHERE 1 = 1
        <if test="startDate != null and startDate != ''">
            <![CDATA[ AND stat_date >= #{startDate} ]]>
        </if>
        <if test="endDate != null and endDate != ''">
            <![CDATA[ AND stat_date <= #{endDate} ]]>
        </if>
        <if test="channelCode != null and channelCode != ''">
            AND channel_code = #{channelCode}
        </if>
        <if test="smsTypeCode != null and smsTypeCode != ''">
            AND sms_type_code = #{smsTypeCode}
        </if>
        <if test="signList != null and signList.size() > 0">
            AND sign_name IN
            <foreach collection="signList" item="sign" open="(" separator="," close=")">
                #{sign}
            </foreach>
        </if>
        GROUP BY
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ),
        channel_code
        ORDER BY
        CONCAT(
        RIGHT(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%x'), 2),
        'W',
        LPAD(DATE_FORMAT(STR_TO_DATE(stat_date, '%Y-%m-%d'), '%v'), 2, '0')
        ),
        channel_code DESC
    </select>
</mapper>
