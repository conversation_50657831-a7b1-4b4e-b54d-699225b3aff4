package com.xhqb.spectre.admin.util;

import org.junit.Test;

public class SimpleCacheTest {

    @Test
    public void testPut1() {
        // Setup

        // Run the test
        SimpleCache.put("key", "value");

        // Verify the results
    }

    @Test
    public void testPut2() {
        // Setup

        // Run the test
        SimpleCache.put("key", "value", 0L);

        // Verify the results
    }

    @Test
    public void testGet() {
        // Setup

        // Run the test
        final String result = SimpleCache.get("key");

        // Verify the results
    }

    @Test
    public void testRemove() {
        // Setup

        // Run the test
        final String result = SimpleCache.remove("key");

        // Verify the results
    }
}
