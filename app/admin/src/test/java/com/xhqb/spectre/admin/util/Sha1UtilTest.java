package com.xhqb.spectre.admin.util;

import org.junit.Test;

import java.security.NoSuchAlgorithmException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

public class Sha1UtilTest {

    @SuppressWarnings("ResultOfMethodCallIgnored")
    @Test
    public void testSha1() throws Exception {
        assertThat(Sha1Util.sha1("data", "salt")).isNotEmpty();
    }
}
