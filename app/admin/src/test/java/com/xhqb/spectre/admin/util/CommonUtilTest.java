package com.xhqb.spectre.admin.util;

import com.xhqb.spectre.admin.model.vo.AreaVO;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.assertj.core.api.InstanceOfAssertFactories.MAP;

public class CommonUtilTest {

    @Test
    public void testGetAppKey() {
        assertThat(CommonUtil.getAppKey()).isNotEmpty();
    }

    @Test
    public void testIsLegalPhone() {
        assertThat(CommonUtil.isLegalPhone("19926435412")).isTrue();
    }

    @Test
    public void testGetTplParamCount() {
        assertThat(CommonUtil.getTplParamCount("尊敬的[*]，欢迎使用小花钱包")).isEqualTo(1);
    }

    @Test
    public void testGetCollectionSize() {
        // Setup
        final Collection<String> collection = Arrays.asList("a", "b");

        // Run the test
        final Integer result = CommonUtil.getCollectionSize(collection);

        // Verify the results
        assertThat(result).isEqualTo(2);
    }

    @Test
    public void testMaskMobile() {
        assertThat(CommonUtil.maskMobile("19926435412")).isEqualTo("199****5412");
    }

    @Test
    public void testIspToList() {
        assertThat(CommonUtil.ispToList("移动,电信")).isEqualTo(Arrays.asList("移动", "电信"));
        assertThat(CommonUtil.ispToList("")).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testAreaToList() {
        // Setup
        final List<AreaVO> expectedResult = Arrays.asList(new AreaVO("100000", "default", "", "default", ""));

        // Run the test
        final List<AreaVO> result = CommonUtil.areaToList("[{\"city\":\"\",\"cityShortName\":\"\",\"id\":\"100000\",\"province\":\"default\",\"provinceShortName\":\"default\"}]");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testIsEmpty() {
        // Setup
        final Collection<Integer> collection = Arrays.asList();

        // Run the test
        final boolean result = CommonUtil.isEmpty(collection);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testGetRandStr() {
        Set<String> set = new HashSet<>();
        for (int i = 0; i < 10000; i++) {
            set.add(CommonUtil.getRandStr(5));
        }
        System.out.println(set.size());
    }

    // ==================== escapeJsonAndRemoveNewlines 方法测试 ====================

    @Test
    @DisplayName("测试null输入")
    public void testEscapeJsonAndRemoveNewlines_WithNull() {
        // Given
        String input = null;

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertEquals("", result, "null输入应该返回空字符串");
    }

    @Test
    @DisplayName("测试空字符串输入")
    public void testEscapeJsonAndRemoveNewlines_WithEmptyString() {
        // Given
        String input = "";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertEquals("", result, "空字符串输入应该返回空字符串");
    }

    @Test
    @DisplayName("测试只包含空格的字符串")
    public void testEscapeJsonAndRemoveNewlines_WithWhitespaceOnly() {
        // Given
        String input = "   ";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertEquals("   ", result, "只包含空格的字符串应该保持原样");
    }

    @Test
    @DisplayName("测试普通字符串（无特殊字符）")
    public void testEscapeJsonAndRemoveNewlines_WithNormalString() {
        // Given
        String input = "Hello World";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertEquals("Hello World", result, "普通字符串应该保持原样");
    }

    @Test
    @DisplayName("测试包含换行符的字符串")
    public void testEscapeJsonAndRemoveNewlines_WithNewlines() {
        // Given
        String input = "Hello\nWorld\nTest";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertEquals("HelloWorldTest", result, "换行符应该被移除");
    }

    @Test
    @DisplayName("测试包含回车符的字符串")
    public void testEscapeJsonAndRemoveNewlines_WithCarriageReturns() {
        // Given
        String input = "Hello\rWorld\rTest";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertEquals("HelloWorldTest", result, "回车符应该被移除");
    }

    @Test
    @DisplayName("测试包含换行符和回车符的字符串")
    public void testEscapeJsonAndRemoveNewlines_WithNewlinesAndCarriageReturns() {
        // Given
        String input = "Hello\r\nWorld\n\rTest\r\n";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertEquals("HelloWorldTest", result, "换行符和回车符都应该被移除");
    }

    @Test
    @DisplayName("测试包含JSON特殊字符的字符串")
    public void testEscapeJsonAndRemoveNewlines_WithJsonSpecialCharacters() {
        // Given
        String input = "Hello \"World\" with 'quotes' and \\backslash";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertTrue(result.contains("\\\""), "双引号应该被转义");
        assertTrue(result.contains("\\\\"), "反斜杠应该被转义");
        assertFalse(result.contains("\"World\""), "原始双引号不应该存在");
    }

    @Test
    @DisplayName("测试包含制表符的字符串")
    public void testEscapeJsonAndRemoveNewlines_WithTabs() {
        // Given
        String input = "Hello\tWorld\tTest";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertTrue(result.contains("\\t"), "制表符应该被转义为\\t");
    }

    @Test
    @DisplayName("测试包含退格符的字符串")
    public void testEscapeJsonAndRemoveNewlines_WithBackspace() {
        // Given
        String input = "Hello\bWorld";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertTrue(result.contains("\\b"), "退格符应该被转义为\\b");
    }

    @Test
    @DisplayName("测试包含换页符的字符串")
    public void testEscapeJsonAndRemoveNewlines_WithFormFeed() {
        // Given
        String input = "Hello\fWorld";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertTrue(result.contains("\\f"), "换页符应该被转义为\\f");
    }

    @Test
    @DisplayName("测试复杂混合字符串")
    public void testEscapeJsonAndRemoveNewlines_WithComplexMixedContent() {
        // Given
        String input = "{\n  \"name\": \"John\\Doe\",\r\n  \"age\": 30,\n  \"city\": \"New\tYork\"\r}";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertFalse(result.contains("\n"), "不应该包含换行符");
        assertFalse(result.contains("\r"), "不应该包含回车符");
        assertTrue(result.contains("\\\""), "双引号应该被转义");
        assertTrue(result.contains("\\\\"), "反斜杠应该被转义");
        assertTrue(result.contains("\\t"), "制表符应该被转义");
    }
    @Test
    @DisplayName("测试特殊控制字符")
    public void testEscapeJsonAndRemoveNewlines_WithControlCharacters() {
        // Given
        String input = "Hello\u0001\u0002\u0003World";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertNotNull(result, "结果不应该为null");
        // 控制字符应该被适当处理（具体行为取决于StringEscapeUtils.escapeJson的实现）
    }

    @Test
    @DisplayName("测试极长字符串")
    public void testEscapeJsonAndRemoveNewlines_WithVeryLongString() {
        // Given
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            sb.append("Hello\nWorld\r");
        }
        String input = sb.toString();

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertNotNull(result, "结果不应该为null");
        assertFalse(result.contains("\n"), "不应该包含换行符");
        assertFalse(result.contains("\r"), "不应该包含回车符");
        assertTrue(result.length() > 0, "结果长度应该大于0");
    }

    @Test
    @DisplayName("测试只包含需要移除的字符")
    public void testEscapeJsonAndRemoveNewlines_WithOnlyRemovableCharacters() {
        // Given
        String input = "\n\r\n\r";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertEquals("", result, "只包含换行符和回车符的字符串应该返回空字符串");
    }

    @Test
    @DisplayName("测试JSON格式字符串")
    public void testEscapeJsonAndRemoveNewlines_WithJsonFormatString() {
        // Given
        String input = "{\n\"key1\":\"value1\",\r\n\"key2\":\"value\\with\\backslash\"\n}";

        // When
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);

        // Then
        assertFalse(result.contains("\n"), "不应该包含换行符");
        assertFalse(result.contains("\r"), "不应该包含回车符");
        assertTrue(result.contains("\\\""), "双引号应该被转义");
        assertTrue(result.contains("\\\\"), "反斜杠应该被转义");
        assertTrue(result.startsWith("{"), "应该以{开始");
        assertTrue(result.endsWith("}"), "应该以}结束");
    }

    @Test
    @DisplayName("测试单个特殊字符")
    public void testEscapeJsonAndRemoveNewlines_WithSingleSpecialCharacters() {
        // 测试单个双引号
        assertEquals("\\\"", CommonUtil.escapeJsonAndRemoveNewlines("\""));

        // 测试单个反斜杠
        assertEquals("\\\\", CommonUtil.escapeJsonAndRemoveNewlines("\\"));

        // 测试单个换行符
        assertEquals("", CommonUtil.escapeJsonAndRemoveNewlines("\n"));

        // 测试单个回车符
        assertEquals("", CommonUtil.escapeJsonAndRemoveNewlines("\r"));

        // 测试单个制表符
        assertEquals("\\t", CommonUtil.escapeJsonAndRemoveNewlines("\t"));
    }

    @Test
    @DisplayName("测试连续特殊字符")
    public void testEscapeJsonAndRemoveNewlines_WithConsecutiveSpecialCharacters() {
        // 测试连续双引号
        String result1 = CommonUtil.escapeJsonAndRemoveNewlines("\"\"\"");
        assertTrue(result1.contains("\\\""), "连续双引号应该被转义");

        // 测试连续反斜杠
        String result2 = CommonUtil.escapeJsonAndRemoveNewlines("\\\\\\");
        assertTrue(result2.contains("\\\\"), "连续反斜杠应该被转义");

        // 测试连续换行符
        assertEquals("", CommonUtil.escapeJsonAndRemoveNewlines("\n\n\n"));

        // 测试连续回车符
        assertEquals("", CommonUtil.escapeJsonAndRemoveNewlines("\r\r\r"));
    }

    @Test
    @DisplayName("测试边界情况组合")
    public void testEscapeJsonAndRemoveNewlines_WithBoundaryConditionCombinations() {
        // 空字符串前后有换行符
        assertEquals("", CommonUtil.escapeJsonAndRemoveNewlines("\n\r"));

        // 只有空格和换行符
        assertEquals(" ", CommonUtil.escapeJsonAndRemoveNewlines(" \n"));

        // 特殊字符混合
        String input = "\"\\\n\r\t";
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);
        assertTrue(result.contains("\\\""), "应该包含转义的双引号");
        assertTrue(result.contains("\\\\"), "应该包含转义的反斜杠");
        assertTrue(result.contains("\\t"), "应该包含转义的制表符");
        assertFalse(result.contains("\n"), "不应该包含换行符");
        assertFalse(result.contains("\r"), "不应该包含回车符");
    }

    @Test
    @DisplayName("测试性能边界")
    public void testEscapeJsonAndRemoveNewlines_PerformanceBoundary() {
        // 测试大量重复字符
        String input = new String(new char[10000]).replace('\0', 'a') + "\n\r";

        long startTime = System.currentTimeMillis();
        String result = CommonUtil.escapeJsonAndRemoveNewlines(input);
        long endTime = System.currentTimeMillis();

        assertNotNull(result, "结果不应该为null");
        assertFalse(result.contains("\n"), "不应该包含换行符");
        assertFalse(result.contains("\r"), "不应该包含回车符");
        assertTrue(endTime - startTime < 1000, "处理时间应该在合理范围内"); // 1秒内完成
    }
}
