package com.xhqb.spectre.admin.service.impl;

import com.xhqb.spectre.common.dal.entity.OpLogDO;
import com.xhqb.spectre.common.dal.mapper.OpLogMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Calendar;
import java.util.GregorianCalendar;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OpLogServiceImplTest {

    @Mock
    private OpLogMapper mockOpLogMapper;

    @InjectMocks
    private OpLogServiceImpl opLogServiceImplUnderTest;

    @Test
    public void testWriteLog() {
        // Setup
        final OpLogDO opLogDO = new OpLogDO(0, "system", 0, "reqMethod", "reqUri", "actionName", "reqParam", "reqIp", "operator", 0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockOpLogMapper.insertSelective(new OpLogDO(0, "system", 0, "reqMethod", "reqUri", "actionName", "reqParam", "reqIp", "operator", 0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))).thenReturn(0);

        // Run the test
        opLogServiceImplUnderTest.writeLog(opLogDO);

        // Verify the results
        verify(mockOpLogMapper).insertSelective(new OpLogDO(0, "system", 0, "reqMethod", "reqUri", "actionName", "reqParam", "reqIp", "operator", 0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()));
    }
}
