package com.xhqb.spectre.admin.util;

import org.junit.Test;

import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.GregorianCalendar;

import static org.assertj.core.api.Assertions.assertThat;

public class DateUtilTest {

    @Test
    public void testParseLocalDateTime1() {
        assertThat(DateUtil.parseLocalDateTime("2020-01-01 00:00:00")).isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testParseLocalDateTime2() {
        assertThat(DateUtil.parseLocalDateTime("2020-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss")).isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testAcquireMinutesBetween() {
        assertThat(DateUtil.acquireMinutesBetween(LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0))).isEqualTo(0L);
    }

    @Test
    public void testAcquireMillisBetween() {
        assertThat(DateUtil.acquireMillisBetween(LocalDateTime.of(2020, 1, 1, 0, 0, 0), LocalDateTime.of(2020, 1, 1, 0, 0, 0))).isEqualTo(0L);
    }

    @Test
    public void testFormatLocalDateTimeFromTimestamp() {
        assertThat(DateUtil.formatLocalDateTimeFromTimestamp(1577808000000L)).isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testFormatLocalDateTimeFromTimestampBySystemTimezone() {
        assertThat(DateUtil.formatLocalDateTimeFromTimestampBySystemTimezone(1577808000000L)).isEqualTo(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testLocalDateTimeToString1() {
        assertThat(DateUtil.localDateTimeToString(LocalDateTime.of(2020, 1, 1, 0, 0, 0))).isEqualTo("2020-01-01 00:00:00");
    }

    @Test
    public void testLocalDateTimeToString2() {
        assertThat(DateUtil.localDateTimeToString(LocalDateTime.of(2020, 1, 1, 0, 0, 0), "yyyy-MM-dd HH:mm:ss")).isEqualTo("2020-01-01 00:00:00");
    }

    @Test
    public void testDateToString() {
        assertThat(DateUtil.dateToString(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isEqualTo("2020-01-01 00:00:00");
    }

    @Test
    public void testStringToDate() {
        assertThat(DateUtil.stringToDate("2020-01-01 00:00:00")).isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testIntToDate() {
        assertThat(DateUtil.intToDate(1577808000)).isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testDateToInt() {
        assertThat(DateUtil.dateToInt(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isEqualTo(1577808000);
    }

    @Test
    public void testIntToString() {
        assertThat(DateUtil.intToString(1633171362)).isEqualTo("2021-10-02 18:42:42");
    }

    @Test
    public void testStringToInt() {
        assertThat(DateUtil.stringToInt("2021-10-02 18:42:42")).isEqualTo(1633171362);
    }

    @Test
    public void testGetNow() {
        assertThat(DateUtil.getNow()).isNotEqualTo(0);
    }
}
