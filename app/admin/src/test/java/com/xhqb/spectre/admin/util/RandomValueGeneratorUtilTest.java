package com.xhqb.spectre.admin.util;

import org.junit.Assert;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;


/**
 * spectre
 *
 * @author: cl
 * @date: 2024/06/03
 */
public class RandomValueGeneratorUtilTest {


    @Test
    public void testRandomValuesInputLessTargetSize() {
        List<String> requestList = new ArrayList<>();
        requestList.add("1");
        Assert.assertEquals(RandomValueGeneratorUtil.randomValues(requestList, 2).size(), 2);

    }

    @Test
    public void testRandomValuesInputEqTargetSize() {
        List<String> requestList = new ArrayList<>();
        requestList.add("1");
        requestList.add("2");
        Assert.assertEquals(RandomValueGeneratorUtil.randomValues(requestList, 2).size(), 2);

    }

    @Test
    public void testRandomValuesInputBigTargetSize() {
        List<String> requestList = new ArrayList<>();
        requestList.add("1");
        requestList.add("2");
        requestList.add("3");
        Assert.assertEquals(RandomValueGeneratorUtil.randomValues(requestList, 1).size(), 1);

    }

    @Test
    public void testRandomValuesInputLessTargetSize1() {
        List<String> requestList = new ArrayList<>();
        requestList.add("1");
        Assert.assertEquals(RandomValueGeneratorUtil.randomValues(requestList, 2, 0).size(), 1);

    }

    @Test
    public void testRandomValuesInputBigTargetSize3() {
        List<String> requestList = new ArrayList<>();
        requestList.add("1");
        requestList.add("2");
        requestList.add("3");
        Assert.assertEquals(RandomValueGeneratorUtil.randomValues(requestList, 1, 0).size(), 1);

    }

}