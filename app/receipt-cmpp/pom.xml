<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>app</artifactId>
        <groupId>com.xhqb.spectre</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>spectre-receiptcmpp</artifactId>
    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb.spectre</groupId>
            <artifactId>common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb.spectre</groupId>
            <artifactId>service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb</groupId>
            <artifactId>kael-mq-starter</artifactId>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>org.mybatis.spring.boot</groupId>-->
            <!--<artifactId>mybatis-spring-boot-starter</artifactId>-->
            <!--<version>1.3.2</version>-->
        <!--</dependency>-->

        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-druid</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-venus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-infra</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-sequence</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-messaging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-aws</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.xhqb.msg-center</groupId>
            <artifactId>msg-center-service</artifactId>
            <version>5.9.0.RELEASE-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-lucifer</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>