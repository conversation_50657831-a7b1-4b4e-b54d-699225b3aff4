package com.xhqb.spectre.cmpp.cmpp20.client.codec.request;

import com.xhqb.spectre.cmpp.cmpp20.client.message.Message;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.TerminateRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageCodec;

import java.util.List;

public class TerminateRequestMessageCodec  extends MessageToMessageCodec<Message, TerminateRequestMessage> {
    PacketType packetType;

    public static final byte[] emptyBytes = new byte[0];

    public TerminateRequestMessageCodec() {
        this(CmppPacketType.CMPPTERMINATEREQUEST);
    }

    public TerminateRequestMessageCodec(PacketType packetType) {
        this.packetType = packetType;
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, Message msg, List<Object> out) throws Exception {
        int commandId =  msg.getHeader().getCommandId();
        if (packetType.getCommandId() != commandId)
        {
            //不解析，交给下一个codec
            out.add(msg);
            return;
        }

        TerminateRequestMessage requestMessage = new TerminateRequestMessage(msg.getHeader());
        out.add(requestMessage);
    }

    @Override
    protected void encode(ChannelHandlerContext ctx, TerminateRequestMessage msg, List<Object> out) throws Exception {
        msg.setBodyBuffer(emptyBytes);
        msg.getHeader().setBodyLength(msg.getBodyBuffer().length);
        out.add(msg);
    }
}

