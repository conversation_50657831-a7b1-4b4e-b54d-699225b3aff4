package com.xhqb.spectre.cmpp.util;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.cmpp.dto.SmsInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class SmsInfoCacheUtil {

    private static Long timeout = 3L;

    private static StringRedisTemplate stringRedisTemplate;

    SmsInfoCacheUtil(StringRedisTemplate redisTemplate) {
        stringRedisTemplate = redisTemplate;
    }

    @Value("${cache.timeout:3}")
    public void setTimeout(String timeout) {
        if (!StringUtils.isEmpty(timeout)) {
            SmsInfoCacheUtil.timeout = Long.valueOf(timeout);
        }
    }

    public static void setSmsInfo(String key, SmsInfo smsInfo) {
        try {
            stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(smsInfo), SmsInfoCacheUtil.timeout, TimeUnit.DAYS);
        } catch (RejectedExecutionException rje) {
            log.warn("redis线程断开; msg={}", rje.getMessage());
        } catch (Exception e) {
            log.warn("redis操作异常; msg={}", e.getMessage());
        }
    }

    public static SmsInfo getSmsInfo(String key) {
        SmsInfo smsInfo = null;
        String json = stringRedisTemplate.opsForValue().get(key);
        if (json != null && !StringUtils.isEmpty(json)) {
            smsInfo = JSON.parseObject(json, SmsInfo.class);
        }
        return smsInfo;
    }

    public static Boolean deleteSmsInfo(String key, long expireTime) {
        return stringRedisTemplate.expire(key, expireTime, TimeUnit.SECONDS);
    }

}
