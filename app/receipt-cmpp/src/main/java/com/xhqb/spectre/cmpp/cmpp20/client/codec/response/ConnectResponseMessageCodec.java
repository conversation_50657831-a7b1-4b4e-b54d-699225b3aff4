package com.xhqb.spectre.cmpp.cmpp20.client.codec.response;

import com.xhqb.spectre.cmpp.cmpp20.client.message.Message;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.ConnectResponseMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.response.ConnectResponseBody;
import com.xhqb.spectre.cmpp.cmpp20.util.ByteUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageCodec;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/16 10:38
 */
@Slf4j
public class ConnectResponseMessageCodec extends MessageToMessageCodec<Message, ConnectResponseMessage> {

    private PacketType packetType;

    public ConnectResponseMessageCodec() {
        this.packetType = CmppPacketType.CONNECTRESPONSE;
    }


    @Override
    protected void encode(ChannelHandlerContext ctx, ConnectResponseMessage msg, List<Object> out) throws Exception {
        log.info("------------connect response encode------------");
        out.add(msg);
    }

    /**
     * cilent只做解码
     *
     * @param ctx
     * @param msg
     * @param out
     * @throws Exception
     */
    @Override
    protected void decode(ChannelHandlerContext ctx, Message msg, List<Object> out) throws Exception {
        log.info("------------connect response decode------------");
        int commandId = msg.getHeader().getCommandId();
        if (commandId != packetType.getCommandId()) {
            // 不解析，交给下一个codec
            out.add(msg);
            return;
        }
        ConnectResponseMessage responseMessage = new ConnectResponseMessage(msg.getHeader());

        byte[] body = msg.getBodyBuffer();
        ByteBuf bodyBuffer = Unpooled.wrappedBuffer(body);
        responseMessage.setStatus(bodyBuffer.readUnsignedByte());
        responseMessage.setAuthenticatorIsmg(ByteUtil.byteBufToByteArray(bodyBuffer, ConnectResponseBody.AUTHENTICATORISMG.getFieldLength()));
        responseMessage.setVersion(bodyBuffer.readUnsignedByte());

        ReferenceCountUtil.release(bodyBuffer);
        out.add(responseMessage);
    }
}
