package com.xhqb.spectre.cmpp.service.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/19 11:06
 */
@ToString
@Getter
@Setter
public class MessageDto implements Serializable {

    private static final long serialVersionUID = 4335087135743477011L;

    /**
     * 渠道账号ID
     */
    private String channelId;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 附加内容,不做业务处理,回执时原样返回
     */
    private String addition;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 渠道编码
     */
    private String channelCode;


    private Integer resend;


    private String smsTypeCode;

    private Integer reqSrc;

    private String requestId;

    private String gatewayUserName;

    /**
     * 订单表名后缀(yyyyMM)[由PosterConsumer类中进行填充]
     */
    private String tableNameSuffix;
}
