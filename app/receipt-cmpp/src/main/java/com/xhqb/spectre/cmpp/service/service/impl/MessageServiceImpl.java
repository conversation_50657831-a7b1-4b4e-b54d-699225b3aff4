package com.xhqb.spectre.cmpp.service.service.impl;

import com.xhqb.spectre.cmpp.service.model.dto.MessageDto;
import com.xhqb.spectre.cmpp.service.model.result.MessageSendResult;
import com.xhqb.spectre.cmpp.service.service.MessageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-05-14
 */
@Service
public class MessageServiceImpl implements MessageService {

    private static final Logger logger = LoggerFactory.getLogger(MessageServiceImpl.class);

//	@Autowired
//	private MessageMapper messageMapper;
//
//	@Autowired
//	private BatchMessageMapper batchMessageMapper;

    @Autowired
    private SmsChannelServiceAggregator smsChannelService;

//	@Autowired
//	private SMSHealthIndicator smsHealthIndicator;

//	@Autowired
//	private StringRedisTemplate stringRedisTemplate;
//
//	@Autowired
//	private RedisTemplate<String, Object> redisTemplate;
//
//	private static final int BATCH_MESSAGE_COUNT = 500;

    @Override
    public MessageSendResult send(String channel, List<MessageDto> messageDtoList) {
        MessageSendResult result = smsChannelService.send(channel, messageDtoList);
        return result;
    }

}
