package com.xhqb.spectre.cmpp.service.controller;

import com.xhqb.spectre.cmpp.service.exception.BaseException;
import com.xhqb.spectre.cmpp.service.model.enums.ResultEnum;
import com.xhqb.spectre.cmpp.service.model.result.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;

/**
 * <AUTHOR>
 * @date 2020-05-14
 */
@Slf4j
public class BaseController {

    @ExceptionHandler({BaseException.class})
    public BaseResult exception(BaseException e) {
        log.info("业务异常:{},message:{}", e.getBaseResultEnum(), e.getMessage());
        return new BaseResult(false, ResultEnum.FAILED.getCode(), ResultEnum.FAILED.getMessage());
    }

    @ExceptionHandler({Exception.class})
    public BaseResult exception(Exception e) {
        log.error("Controller未处理异常", e);
        return new BaseResult(false, ResultEnum.EXCEPTION.getCode(), ResultEnum.EXCEPTION.getMessage());
    }
}
