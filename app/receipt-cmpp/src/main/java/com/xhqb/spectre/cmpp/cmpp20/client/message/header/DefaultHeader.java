package com.xhqb.spectre.cmpp.cmpp20.client.message.header;

import lombok.ToString;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/7 15:11
 */
@ToString
public class DefaultHeader implements Header {
    private static final long serialVersionUID = -56459574791858948L;

    private int totalLength;

    private int headerLength;

    private int bodyLength;

    private int commandId;

    private int sequenceId;

//    private long nodeId;


    @Override
    public int getTotalLength() {
        return totalLength;
    }

    @Override
    public void setTotalLength(int length) {
        totalLength = length;
    }

    @Override
    public int getHeaderLength() {
        return this.headerLength;
    }

    @Override
    public void setHeaderLength(int length) {
        this.headerLength = length;
    }

    @Override
    public int getBodyLength() {
        return this.bodyLength;
    }

    @Override
    public void setBodyLength(int length) {
        this.bodyLength = length;
    }

    @Override
    public int getCommandId() {
        return this.commandId;
    }

    @Override
    public void setCommandId(int commandId) {
        this.commandId = commandId;
    }

    @Override
    public int getSequenceId() {
        return this.sequenceId;
    }

    @Override
    public void setSequenceId(int sequenceId) {
        this.sequenceId = sequenceId;
    }

}
