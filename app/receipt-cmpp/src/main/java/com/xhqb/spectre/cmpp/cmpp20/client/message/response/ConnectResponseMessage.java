package com.xhqb.spectre.cmpp.cmpp20.client.message.response;

import com.xhqb.spectre.cmpp.cmpp20.client.message.DefaultMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.Header;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/8 17:03
 */
@ToString(callSuper = true)
@Getter
@Setter
public class ConnectResponseMessage extends DefaultMessage {

    private static final long serialVersionUID = 1438991653960636436L;
    private int status = 3;

    private byte[] authenticatorIsmg = new byte[0];

    private short version = 0x20;

    public ConnectResponseMessage(Header header) {
        super(CmppPacketType.CONNECTRESPONSE, header);
    }
}
