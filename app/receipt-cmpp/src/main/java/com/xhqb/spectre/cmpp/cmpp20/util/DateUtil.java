package com.xhqb.spectre.cmpp.cmpp20.util;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 日期(时间)工具类
 *
 * @author: xiaoxiaoxiang
 * @date: 2019/9/27 11:05
 */
public final class DateUtil {
    public static final DateTimeFormatter CMPP_DATE_FORMATTER = DateTimeFormatter.ofPattern("MMddhhmmss");
    private static final ZoneId ZONE_ID = ZoneId.systemDefault();

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private DateUtil() {
    }

    /**
     * 获取当日的00:00:00时间
     *
     * @return
     */
    public static Date getTodayZeroTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime zeroTime = LocalDateTime.of(now.toLocalDate(), LocalTime.MIN);
        return localDateTimeToDate(zeroTime);
    }

    /**
     * 获取某日的00:00:00时间
     *
     * @param dateText
     * @return
     */
    public static Date getDayZeroTime(String dateText) {
        LocalDate date = LocalDate.parse(dateText, DATE_FORMATTER);
        LocalDateTime zeroTime = LocalDateTime.of(date, LocalTime.MIN);
        return localDateTimeToDate(zeroTime);
    }


    /**
     * 获取当日的23:59:59时间
     *
     * @return
     */
    public static Date getTodayLastTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastTime = LocalDateTime.of(now.toLocalDate(), LocalTime.MAX);
        return localDateTimeToDate(lastTime);
    }

    /**
     * 获取某日的23:59:59时间
     *
     * @param dateText
     * @return
     */
    public static Date getDayLastTime(String dateText) {
        LocalDate date = LocalDate.parse(dateText, DATE_FORMATTER);
        LocalDateTime lastTime = LocalDateTime.of(date, LocalTime.MAX);
        return localDateTimeToDate(lastTime);
    }


    /**
     * 获取YYYY-MM-DD格式的日期字符串
     *
     * @param date
     * @return
     */
    public static String getDateText(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(date.toInstant(), ZONE_ID);
        return DATE_FORMATTER.format(localDateTime);
    }

    /**
     * 获取指定dateTimeFormatter格式的日期字符串
     *
     * @param dateTimeFormatter
     * @return
     */
    public static String getNowText(DateTimeFormatter dateTimeFormatter) {
        LocalDateTime localDateTime = LocalDateTime.now();
        return dateTimeFormatter.format(localDateTime);
    }

    /**
     * 获取当前日期YYYY-MM-DD格式的日期字符串
     *
     * @return
     */
    public static String getNowText() {
        return getNowText(DATE_FORMATTER);
    }

    /**
     * 时间戳转字符串
     *
     * @param timestamp
     * @param format
     * @return
     */
    public static String timestampToText(long timestamp, String format) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZONE_ID);
        DateTimeFormatter dateTimeFormatter = format == null ? DATE_FORMATTER : DateTimeFormatter.ofPattern(format);
        return dateTimeFormatter.format(localDateTime);
    }

    /**
     * 判断当天是否是周末
     *
     * @return
     */
    public static boolean todayIsWeekend() {
        LocalDateTime now = LocalDateTime.now();
        DayOfWeek dayOfWeek = now.getDayOfWeek();
        boolean isWeekend = false;
        switch (dayOfWeek) {
            case SATURDAY:
                isWeekend = true;
                break;
            case SUNDAY:
                isWeekend = true;
                break;
            default:
                break;
        }
        return isWeekend;
    }

    private static Date localDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZONE_ID).toInstant());
    }
}
