package com.xhqb.spectre.cmpp.cmpp20.client.handler;

import com.xhqb.spectre.cmpp.cmpp20.client.ChannelManager;
import com.xhqb.spectre.cmpp.cmpp20.client.CmppClientEntity;
import com.xhqb.spectre.cmpp.cmpp20.client.bussiness.CmppClientBusinessHandler;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.ActiveTestRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.ConnectRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.DeliverRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.ActiveTestResponseMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.ConnectResponseMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.DeliverResponseMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.SubmitResponseMessage;
import com.xhqb.spectre.cmpp.enums.ChannelStatusEnum;
import com.xhqb.spectre.cmpp.service.service.cmppclienthandler.XhCmppClientBussinessHandler;
import com.xhqb.spectre.cmpp.util.OminiUtil;
import io.netty.channel.Channel;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.EventLoop;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/16 18:07
 */
@Slf4j
public class CmppClientHandler extends ChannelDuplexHandler {

    private CmppClientBusinessHandler businessHandler;

    private OminiUtil ominiUtil;

    public CmppClientHandler(CmppClientBusinessHandler businessHandler, OminiUtil ominiUtil) {
        this.businessHandler = businessHandler;
        this.ominiUtil = ominiUtil;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        Channel channel = ctx.channel();
        CmppClientEntity clientEntity = ChannelManager.getCmppClientEntity(channel);
        doConnectRequest(ctx.channel(), clientEntity);
        ctx.fireChannelActive();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {

        Channel channel = ctx.channel();
        CmppClientEntity clientEntity = ChannelManager.getCmppClientEntity(channel);
        log.info("channel is inactive: {}", clientEntity);
        // 当连接channel inactive时  落入数据库channel 在线状态 + 钉钉提醒 cmpp

        //  admin 管理后台线上 每个渠道状态，手动拉起channel ，只需要 修改zk node 数据更新
        ctx.fireChannelInactive();

        Long status = ChannelManager.getChannelStatus(String.valueOf(clientEntity.getId()));
        if (ChannelStatusEnum.WORKING.getStatus().equals(status)) {
            ChannelManager.setChannelStatus(clientEntity.getId().toString(), ChannelStatusEnum.SHUTDOWN);
        }

        if (ChannelStatusEnum.WORKING.getStatus().equals(status) ||
            ChannelStatusEnum.SHUTDOWN.getStatus().equals(status)) {
            final EventLoop eventLoop = ctx.channel().eventLoop();
            eventLoop.schedule(() -> ChannelManager
                .retry(clientEntity.getId().toString()), 1L, TimeUnit.MINUTES);
            log.info("channel连接重试; id={}, status={}, isOpen={}, isActive={}, localAddress={}",
                    clientEntity.getId(),
                    status,
                    channel.isOpen(),
                    channel.isActive(),
                    channel.localAddress());
        }
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (msg instanceof ConnectResponseMessage) {
            handleConnectResponse(ctx, (ConnectResponseMessage) msg);
        } else if (msg instanceof SubmitResponseMessage) {
            businessHandler.submitRespHandle((SubmitResponseMessage) msg);
        } else if (msg instanceof DeliverRequestMessage) {
            DeliverRequestMessage requestMsg = (DeliverRequestMessage) msg;
            DeliverResponseMessage responseMessage = new DeliverResponseMessage(requestMsg.getHeader().getSequenceId());
            responseMessage.setResult((short) 0);
            responseMessage.setMsgId(requestMsg.getMsgId());
            ctx.writeAndFlush(responseMessage);
            // 业务处理
            Channel channel = ctx.channel();
            CmppClientEntity clientEntity = ChannelManager.getCmppClientEntity(channel);
            log.debug("channelId: {}, smsTypeCode: {}, platform: {}", clientEntity.getId(),
                clientEntity.getSmsTypeCode(), clientEntity.getPlatform());
            businessHandler.deliverHandle(requestMsg, clientEntity);
        } else if (msg instanceof ActiveTestRequestMessage) {
            ActiveTestRequestMessage requestMsg = (ActiveTestRequestMessage) msg;
            ActiveTestResponseMessage responseMessage = new ActiveTestResponseMessage(requestMsg.getHeader().getSequenceId());
            ctx.writeAndFlush(responseMessage);
        } else if (msg instanceof ActiveTestResponseMessage) {
            ActiveTestResponseMessage responseMsg = (ActiveTestResponseMessage) msg;
            Channel channel = ctx.channel();
            CmppClientEntity clientEntity = ChannelManager.getCmppClientEntity(channel);
            log.info("platform: {}, timestamp {}, channelAccountId {}", clientEntity.getPlatform(), System.currentTimeMillis(), clientEntity.getId());
            ((XhCmppClientBussinessHandler) businessHandler).refreshLastActiveTime(clientEntity.getId(), System.currentTimeMillis());

        }
        ctx.fireChannelRead(msg);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        Channel channel = ctx.channel();
        CmppClientEntity clientEntity = ChannelManager.getCmppClientEntity(channel);
        log.warn("渠道节点断开; id={}, username={}, host={}",
                clientEntity.getId(),
                clientEntity.getUserName(),
                clientEntity.getServerHost());
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("channelAccountId", String.valueOf(clientEntity.getId()));
            param.put("channelCode", clientEntity.getPlatform());
            ominiUtil.sendMessage(ominiUtil.getCmppChannelUnactivated(), param);
        } catch (Exception e) {
            log.error("通知发送失败", e);
        }
        ctx.close();
    }

    protected void doConnectRequest(Channel ch, CmppClientEntity clientEntity) {
        if (clientEntity == null) {
            log.error("doConnectRequest clientEntity is null, channelId: {}", ch.id().asLongText());
            // TODO 重连
            ch.closeFuture();
            return;
        }
        ConnectRequestMessage req = new ConnectRequestMessage();
        req.build(clientEntity.getUserName(), clientEntity.getPassword());
        ch.writeAndFlush(req);
    }

    private void handleConnectResponse(ChannelHandlerContext ctx, ConnectResponseMessage responseMessage) {
        log.info("连接返回结果:{}", responseMessage);
        int status = responseMessage.getStatus();
        if (status == 0) {
            CmppClientEntity clientEntity = ChannelManager.getCmppClientEntity(ctx.channel());
            ChannelManager.setChannelRetry(clientEntity.getId().toString(), 0L);
            ChannelManager.setChannelStatus(clientEntity.getId().toString(), ChannelStatusEnum.WORKING);
            log.info("登录成功; channelAccountID:{}, 账号:{}, localAddress={}",
                    clientEntity.getId(), clientEntity.getUserName(), ctx.channel().localAddress());
        } else {
            CmppClientEntity clientEntity = ChannelManager.getCmppClientEntity(ctx.channel());
            log.warn("登录失败; (status = {}), clientEntity:{}", status, clientEntity);
            if (clientEntity != null) {
                ChannelManager.setChannelStatus(String.valueOf(clientEntity.getId()), ChannelStatusEnum.ACCOUNT_ERROR);
                //发送通知
                Map<String, Object> param = new HashMap<>();
                param.put("channelAccountId", String.valueOf(clientEntity.getId()));
                param.put("channelCode", clientEntity.getPlatform());
                ominiUtil.sendMessage(ominiUtil.getConnectResponseError(), param);
            }
            ctx.close();
        }
    }
}
