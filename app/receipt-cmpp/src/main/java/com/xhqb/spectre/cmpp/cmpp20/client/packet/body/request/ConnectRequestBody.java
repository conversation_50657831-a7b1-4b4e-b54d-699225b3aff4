package com.xhqb.spectre.cmpp.cmpp20.client.packet.body.request;

import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppDataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.DataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.PacketBody;

/**
 * ConnectRequestBody
 *
 * @author: xiaoxiaoxiang
 * @date: 2020/5/15 16:21
 */
public enum ConnectRequestBody implements PacketBody {

    /**
     * Source_Addr
     */
    SOURCEADDR(CmppDataType.OCTERSTRING, 6),

    /**
     * AuthenticatorSource
     */
    AUTHENTICATORSOURCE(CmppDataType.OCTERSTRING, 16),

    /**
     * Version
     */
    VERSION(CmppDataType.UNSIGNEDINT, 1),

    /**
     * Timestamp
     */
    TIMESTAMP(CmppDataType.UNSIGNEDINT, 4);

    /**
     * body总长度
     */
    private static final int BODYLENGTH = SOURCEADDR.fieldLength
        + AUTHENTICATORSOURCE.fieldLength
        + VERSION.fieldLength
        + TIMESTAMP.fieldLength;
    /**
     * 数据类型
     */
    private DataType dataType;
    /**
     * 长度
     */
    private int fieldLength;

    private ConnectRequestBody(DataType dataType, int fieldLength) {
        this.dataType = dataType;
        this.fieldLength = fieldLength;
    }

    @Override
    public int getBodyLength() {
        return BODYLENGTH;
    }

    @Override
    public DataType getDataType() {
        return this.dataType;
    }

    @Override
    public int getFieldLength() {
        return this.fieldLength;
    }
}
