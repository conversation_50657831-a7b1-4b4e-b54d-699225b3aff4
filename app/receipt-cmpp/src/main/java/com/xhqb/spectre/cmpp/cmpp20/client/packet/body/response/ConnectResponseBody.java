package com.xhqb.spectre.cmpp.cmpp20.client.packet.body.response;

import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppDataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.DataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.PacketBody;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/15 17:02
 */
public enum ConnectResponseBody implements PacketBody {

    /**
     * Status
     */
    STATUS(CmppDataType.UNSIGNEDINT, 4),

    /**
     * AuthenticatorISMG
     */
    AUTHENTICATORISMG(CmppDataType.OCTERSTRING, 16),

    /**
     * Version
     */
    VERSION(CmppDataType.UNSIGNEDINT, 1);

    /**
     * body总长度
     */
    private static final int BODYLENGTH = STATUS.fieldLength
        + AUTHENTICATORISMG.fieldLength
        + VERSION.fieldLength;
    /**
     * 数据类型
     */
    private DataType dataType;
    /**
     * 长度
     */
    private int fieldLength;

    private ConnectResponseBody(DataType dataType, int fieldLength) {
        this.dataType = dataType;
        this.fieldLength = fieldLength;
    }

    @Override
    public int getBodyLength() {
        return BODYLENGTH;
    }

    @Override
    public DataType getDataType() {
        return this.dataType;
    }

    @Override
    public int getFieldLength() {
        return this.fieldLength;
    }
}
