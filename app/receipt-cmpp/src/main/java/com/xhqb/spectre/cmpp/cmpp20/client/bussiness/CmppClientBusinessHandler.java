package com.xhqb.spectre.cmpp.cmpp20.client.bussiness;

import com.xhqb.spectre.cmpp.cmpp20.client.CmppClientEntity;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.DeliverRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.SubmitRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.SubmitResponseMessage;

/**
 * 业务处理
 *
 * @author: xiaoxiaoxiang
 * @date: 2020/6/2 10:19
 */
public interface CmppClientBusinessHandler {

    /**
     * submit事件业务处理
     *
     * @param message
     */
    void submitHandle(SubmitRequestMessage message, SmsMsg smsMsg);

    /**
     * submit_resp事件业务处理
     *
     * @param message
     */
    void submitRespHandle(SubmitResponseMessage message);

    /**
     * deliver事件业务处理
     *
     * @param message
     */
    void deliverHandle(DeliverRequestMessage message, CmppClientEntity clientEntity);
}
