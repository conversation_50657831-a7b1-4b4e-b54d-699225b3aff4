package com.xhqb.spectre.cmpp.cmpp20.client.message;

import com.alibaba.fastjson.annotation.JSONField;
import com.xhqb.spectre.cmpp.cmpp20.client.deserializer.CustomerPacketTypeDeserializer;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.DefaultHeader;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.Header;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;
import com.xhqb.spectre.cmpp.cmpp20.util.SequenceUtil;
import lombok.ToString;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/7 15:22
 */
@ToString
public class DefaultMessage implements Message {
    private static final long serialVersionUID = 9097644074615662715L;

    @JSONField(deserializeUsing = CustomerPacketTypeDeserializer.class)
    private PacketType packetType;

    private Header head;

    private byte[] bodyBuffer;

    public DefaultMessage() {
    }

    public DefaultMessage(PacketType packetType) {
        this(packetType, null);
    }

    public DefaultMessage(PacketType packetType, Header header) {
        this.packetType = packetType;
        if (header == null) {
            header = new DefaultHeader();
            header.setSequenceId(SequenceUtil.getSequenceNo());
        }
        header.setCommandId(packetType.getCommandId());
        setHeader(header);
    }

    public DefaultMessage(PacketType packetType, int sequenceId) {
        this.packetType = packetType;
        Header header = new DefaultHeader();
        header.setSequenceId(sequenceId);
        header.setCommandId(packetType.getCommandId());
        setHeader(header);
    }

    @Override
    public PacketType getPacketType() {
        return this.packetType;
    }

    @Override
    public void setPacketType(PacketType packetType) {
        this.packetType = packetType;
    }

    @Override
    public Header getHeader() {
        return this.head;
    }

    @Override
    public void setHeader(Header head) {
        this.head = head;
    }

    @Override
    public byte[] getBodyBuffer() {
        return this.bodyBuffer;
    }

    @Override
    public void setBodyBuffer(byte[] bodyBuffer) {
        this.bodyBuffer = bodyBuffer;
    }
}
