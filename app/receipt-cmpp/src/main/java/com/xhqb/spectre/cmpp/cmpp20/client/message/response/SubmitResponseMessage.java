package com.xhqb.spectre.cmpp.cmpp20.client.message.response;

import com.xhqb.spectre.cmpp.cmpp20.client.message.DefaultMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.MsgId;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.Header;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/8 17:03
 */
@ToString(callSuper = true)
@Getter
@Setter
public class SubmitResponseMessage extends DefaultMessage {

    private static final long serialVersionUID = -2070893294395545617L;

    private MsgId msgId;

    private short result = 0;

    public SubmitResponseMessage(Header header) {
        super(CmppPacketType.SUBMITRESPONSE, header);
    }
}
