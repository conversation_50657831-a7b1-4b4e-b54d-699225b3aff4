package com.xhqb.spectre.cmpp.cmpp20.client.wrapper;

import com.xhqb.spectre.cmpp.cmpp20.client.CmppClientEntity;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.DeliverRequestMessage;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class DeliverRequestMessageWrapper extends DeliverRequestMessage {
    private static final long serialVersionUID = -1L;
    private CmppClientEntity clientEntity;
}