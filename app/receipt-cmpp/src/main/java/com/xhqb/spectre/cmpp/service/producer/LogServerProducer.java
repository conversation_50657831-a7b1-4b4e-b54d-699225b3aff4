package com.xhqb.spectre.cmpp.service.producer;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.cmpp.constant.CmppBusinessConstants;
import com.xhqb.spectre.cmpp.dto.SmsInfo;
import com.xhqb.spectre.cmpp.util.SmsInfoCacheUtil;
import com.xhqb.spectre.common.dal.dto.mq.SendDeliverDTO;
import com.xhqb.spectre.common.dal.entity.CmppRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.MessageId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LogServerProducer implements IProducer {

    @Autowired
    private RedisTemplate<String, Object> jsonRedisTemplate;

    @Autowired
    private MQTemplate<String> mqTemplate;

    //spectre-deliver-resq
    @Value("#{'${kael.mq.producers:}'.split(',')[0]}")
    private String spectreDeliverResq;

    //spectre-submit-resq
    @Value("#{'${kael.mq.producers:}'.split(',')[1]}")
    private String spectreSubmitResq;

    public void submitResp(SmsInfo smsInfo) {
        if (!"0".equals(smsInfo.getPlatformStatus())) {
            SmsInfo requestInfo = SmsInfoCacheUtil.getSmsInfo(CmppBusinessConstants.CMPP_SUBMIT_REQUEST_KEY + ":" + smsInfo.getSubmitSeqId());
            log.info("发送状态队列: {}", smsInfo);
            CmppRecord cmppRecord = new CmppRecord();
            cmppRecord.setMobilePhone(requestInfo.getMobile());
            cmppRecord.setAddition(requestInfo.getAddition());
            cmppRecord.setOrderId(requestInfo.getOrderId());
            cmppRecord.setPkTotal(requestInfo.getPkTotal());
            cmppRecord.setPkNumber(requestInfo.getPkNumber());
            cmppRecord.setPartnerPlatform(requestInfo.getPartnerPlatform());
            cmppRecord.setSubmitSeqId(requestInfo.getSubmitSeqId());

            //发送结果
            cmppRecord.setPlatformStatus(smsInfo.getPlatformStatus());
            cmppRecord.setPlatformTime(smsInfo.getPlatformTime());
            cmppRecord.setReportMsgId(smsInfo.getSubMsgId());
            cmppRecord.setReportStatus("");
            cmppRecord.setReportTime("");

            SendDeliverDTO sendDeliverDTO = SendDeliverDTO.builder()
                .type("cmpp")
                .cmppRecord(cmppRecord)
                .build();
            mqTemplate.sendAsync(spectreSubmitResq, JSONObject.toJSONString(sendDeliverDTO));
            log.info("发送状态完成: orderId={}", cmppRecord.getOrderId());
        }
    }

    public void deliverResp(SmsInfo smsInfo) {
        SmsInfo requestInfo = SmsInfoCacheUtil.getSmsInfo(CmppBusinessConstants.CMPP_SUBMIT_REQUEST_KEY + ":" +
            smsInfo.getSubmitSeqId());
        log.info("回执状态队列: {}", smsInfo);
        CmppRecord cmppRecord = new CmppRecord();
        cmppRecord.setMobilePhone(requestInfo.getMobile());
        cmppRecord.setAddition(requestInfo.getAddition());
        cmppRecord.setOrderId(requestInfo.getOrderId());
        cmppRecord.setPkTotal(requestInfo.getPkTotal());
        cmppRecord.setPkNumber(requestInfo.getPkNumber());
        cmppRecord.setPartnerPlatform(requestInfo.getPartnerPlatform());
        cmppRecord.setSubmitSeqId(requestInfo.getSubmitSeqId());

        //发送结果
        SmsInfo submitInfo = SmsInfoCacheUtil.getSmsInfo(CmppBusinessConstants.CMPP_SUBMIT_RESPONSE_KEY + ":" + smsInfo.getSubMsgId());
        cmppRecord.setPlatformStatus(submitInfo.getPlatformStatus());
        cmppRecord.setPlatformTime(submitInfo.getPlatformTime());
        cmppRecord.setReportMsgId(submitInfo.getSubMsgId());

        cmppRecord.setReportStatus(smsInfo.getReportStatus());
        cmppRecord.setReportTime(smsInfo.getReportTime());

        SendDeliverDTO sendDeliverDTO = SendDeliverDTO.builder()
            .type("cmpp")
            .cmppRecord(cmppRecord)
            .build();
        MessageId messageId = mqTemplate.send(spectreSubmitResq, JSONObject.toJSONString(sendDeliverDTO));
        log.info("回执消息完成: messageId={}, orderId={}", messageId, cmppRecord.getOrderId());
    }
}
