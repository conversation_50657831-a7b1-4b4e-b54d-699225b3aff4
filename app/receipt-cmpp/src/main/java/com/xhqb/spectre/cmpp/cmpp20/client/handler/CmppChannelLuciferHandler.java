package com.xhqb.spectre.cmpp.cmpp20.client.handler;

import com.xhqb.spectre.cmpp.cmpp20.client.CmppClient;
import com.xhqb.spectre.cmpp.cmpp20.client.message.DefaultMessage;
import com.xhqb.spectre.cmpp.lucifer.handler.CmppLuciferHandler;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPromise;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * cmpp消息埋点处理器
 *
 * <AUTHOR>
 * @date 2021/10/27
 */
public class CmppChannelLuciferHandler extends ChannelDuplexHandler {

    private static final Logger logger = LoggerFactory.getLogger(CmppChannelLuciferHandler.class);

    /**
     * 埋点处理
     */
    private CmppLuciferHandler cmppLuciferHandler;
    /**
     * cmpp 客户端
     */
    private CmppClient cmppClient;

    public CmppChannelLuciferHandler(CmppClient cmppClient) {
        this.cmppClient = cmppClient;
        this.cmppLuciferHandler = cmppClient.getCmppLuciferHandler();
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (!(msg instanceof DefaultMessage)) {
            super.channelRead(ctx, msg);
            return;
        }
        try {
            // 做埋点处理
            cmppLuciferHandler.preHandle((DefaultMessage) msg);
        } catch (Exception e) {
            logger.error("channelRead cmpp协议消息做埋点处理失败,msg = {}", msg, e);
        } finally {
            super.channelRead(ctx, msg);
        }
    }

    @Override
    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
        if (!(msg instanceof DefaultMessage)) {
            super.write(ctx, msg, promise);
            return;
        }

        try {
            // 做埋点处理
            cmppLuciferHandler.preHandle((DefaultMessage) msg);
        } catch (Exception e) {
            logger.error("write cmpp协议消息做埋点处理失败,msg = {}", msg, e);
        } finally {
            super.write(ctx, msg, promise);
        }
    }
}
