package com.xhqb.spectre.cmpp.cmpp20.client.codec;

import com.xhqb.spectre.cmpp.cmpp20.client.message.Message;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPromise;
import io.netty.handler.codec.MessageToMessageCodec;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/16 10:40
 */
@Slf4j
public class BodyCodecAggregator extends ChannelDuplexHandler {

    private ConcurrentHashMap<Integer, MessageToMessageCodec> codecMap = new ConcurrentHashMap<Integer, MessageToMessageCodec>();

    public BodyCodecAggregator() {
        for (PacketType packetType : CmppPacketType.values()) {
            codecMap.put(packetType.getCommandId(), packetType.getCodec());
        }
    }

    public static String handlerName() {
        return "cmppBodyCodec";
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        int commandId = ((Message) msg).getHeader().getCommandId();
        MessageToMessageCodec codec = codecMap.get(commandId);
        if (codec == null) {
            log.error("invalid commandId:{}", commandId);
        } else {
            codec.channelRead(ctx, msg);
        }
    }

    @Override
    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
        try {
            int commandId = ((Message) msg).getHeader().getCommandId();
            MessageToMessageCodec codec = codecMap.get(commandId);
            codec.write(ctx, msg, promise);
        } catch (Exception ex) {
            log.error("BodyCodecAggregator write exception", ex);
            promise.tryFailure(ex);
        }
    }
}
