package com.xhqb.spectre.cmpp.service.controller;

import com.xhqb.spectre.cmpp.cmpp20.client.ChannelManager;
import com.xhqb.spectre.cmpp.cmpp20.client.CmppClientEntity;
import com.xhqb.spectre.cmpp.service.model.dto.ChannelDetailDTO;
import com.xhqb.spectre.cmpp.service.model.result.BaseResult;
import com.xhqb.spectre.cmpp.service.model.result.ChannelStatusResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@Slf4j
public class ChannelController {

    @PostMapping(value = "/channel/refresh/{key}", headers = {"content-type=application/json"})
    public BaseResult refresh(@PathVariable String key) {
        log.info("接口刷新账号; key={}", key);
        CmppClientEntity clientEntity = ChannelManager.getClientEntity(key);
        BaseResult baseResult = new BaseResult();
        if (clientEntity == null) {
            baseResult.setFailure("10001", "无此渠道");
            return baseResult;
        }
        ChannelManager.refresh(key);
        baseResult.setSuccess();
        return baseResult;
    }

    @PostMapping(value = "/channel/offline/{key}", headers = {"content-type=application/json"})
    public BaseResult offline(@PathVariable String key) {
        log.info("接口下线账号; key={}", key);
        CmppClientEntity clientEntity = ChannelManager.getClientEntity(key);
        BaseResult baseResult = new BaseResult();
        if (clientEntity == null) {
            baseResult.setFailure("10001", "无此渠道");
            return baseResult;
        }
        ChannelManager.offline(key);
        baseResult.setSuccess();
        return baseResult;
    }

    @GetMapping(value = "/channel/detail", headers = {"content-type=application/json"})
    public ChannelStatusResult detail() {

        List<ChannelDetailDTO> channelDetailDTOS = ChannelManager.getChannelDetail();
        ChannelStatusResult channelStatusResult = ChannelStatusResult.builder()
            .channelDetailList(channelDetailDTOS).build();
        channelStatusResult.setMaxRetryNum(ChannelManager.maxRetryNum);
        channelStatusResult.setSuccess();
        log.info("接口查询状态;");
        return channelStatusResult;
    }

    @PostMapping(value = "/channel/resetRetryNum/{key}", headers = {"content-type=application/json"})
    public BaseResult resetRetryNum(@PathVariable String key) {
        log.info("接口重置渠道重试次数; key={}", key);
        BaseResult baseResult = new BaseResult();
        CmppClientEntity clientEntity = ChannelManager.getClientEntity(key);

        if (clientEntity == null) {
            baseResult.setFailure("10001", "无此渠道");
            return baseResult;
        }

        ChannelManager.setChannelRetry(key, 0L);
        baseResult.setSuccess();
        return baseResult;
    }
}
