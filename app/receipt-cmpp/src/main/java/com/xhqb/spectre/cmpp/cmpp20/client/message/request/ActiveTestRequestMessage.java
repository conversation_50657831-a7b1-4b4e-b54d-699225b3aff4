package com.xhqb.spectre.cmpp.cmpp20.client.message.request;

import com.xhqb.spectre.cmpp.cmpp20.client.message.DefaultMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.Header;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/7 17:28
 */
public class ActiveTestRequestMessage extends DefaultMessage {

    private static final long serialVersionUID = 4405877856153385140L;

    public ActiveTestRequestMessage() {
        super(CmppPacketType.ACTIVETESTREQUEST);
    }

    public ActiveTestRequestMessage(Header header) {
        super(CmppPacketType.ACTIVETESTREQUEST, header);
    }
}
