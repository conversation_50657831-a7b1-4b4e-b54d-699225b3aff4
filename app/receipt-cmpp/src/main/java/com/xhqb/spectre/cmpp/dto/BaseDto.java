package com.xhqb.spectre.cmpp.dto;

import lombok.ToString;

import java.util.Date;

/**
 * @Description: dto基类
 * @Auther: xiaoxiaoxiang
 * @Date: 2018/8/15 15:40
 */
@ToString
public class BaseDto {

    /**
     * 主键ID
     */
    private String id;
    // 起始页
    private int startPage = -1;
    // 起始数
    private int startNum;
    // 每夜页数
    private int pageNum;

    /**
     * 数据状态
     */
    private String status;

    /**
     * 数据版本号
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 修改人
     */
    private String modifier;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getStartPage() {
        return startPage;
    }

    public void setStartPage(int startPage) {
        this.startPage = startPage;
    }

    public int getStartNum() {
        return this.startPage == -1 ? -1 : this.startPage == 0 ? 0 : (this.startPage - 1) * pageNum;
    }

    public void setStartNum(int startNum) {
        this.startNum = startNum;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }
}
