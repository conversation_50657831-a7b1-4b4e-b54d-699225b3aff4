package com.xhqb.spectre.cmpp.cmpp20.client.message.response;

import com.xhqb.spectre.cmpp.cmpp20.client.message.DefaultMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.MsgId;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.Header;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/8 17:03
 */
@ToString(callSuper = true)
@Getter
@Setter
public class DeliverResponseMessage extends DefaultMessage {
    private static final long serialVersionUID = 9092436258065325216L;

    private MsgId msgId;

    private short result = 0;

    public DeliverResponseMessage(Header header) {
        super(CmppPacketType.DELIVERRESPONSE, header);
    }

    public DeliverResponseMessage(int sequenceId) {
        super(CmppPacketType.DELIVERRESPONSE, sequenceId);
    }
}
