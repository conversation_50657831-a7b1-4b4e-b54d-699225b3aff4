package com.xhqb.spectre.cmpp.cmpp20.client.message.request;

import com.xhqb.spectre.cmpp.cmpp20.client.message.DefaultMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.MsgId;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.Header;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/7 17:28
 */
@ToString(callSuper = true)
@Getter
@Setter
@Slf4j
public class DeliverRequestMessage extends DefaultMessage {

    private static final long serialVersionUID = -2836128166650866384L;

    private MsgId msgId;

    private String destId = ""; //﻿SP 的服务代码，一般 4--6 位

    private String serviceId = ""; //﻿业务类型，是数字、字母和符号的组合

    private short tppid = 0; //﻿GSM 协议类型

    private short tpudhi = 0; //﻿GSM 协议类型

    private short msgFmt = 8; //﻿信息格式 ﻿8： UCS2 编码

    private String srcTerminalId = ""; //﻿源终端 MSISDN 号码

    private short registeredDelivery = 1; //﻿是否为状态报告 0：非状态报告 1：状态报告

    private short msgLength;

    private byte[] msgContent;

    private String msgContentText;

    private String reserve = "";

    private DeliverReportMessage reportMessage;

    public DeliverRequestMessage() {
        super(CmppPacketType.DELIVERREQUEST);
    }

    public DeliverRequestMessage(Header header) {
        super(CmppPacketType.DELIVERREQUEST, header);
    }
}
