package com.xhqb.spectre.cmpp.cmpp20.client.bussiness;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/6/16 18:00
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsMsg implements Serializable {

    private static final long serialVersionUID = 7505715110594408518L;

    private String channelId;

    private String mobile;

    private String content;

    private String addition;

    private String tplCode;

    private String channelCode;

    private Integer resend;

    private String smsTypeCode;

    private Integer reqSrc;

    private String requestId;

    private String gatewayUserName;

    /**
     * 订单表名后缀(yyyyMM)[由PosterConsumer类中进行填充]
     */
    private String tableNameSuffix;
}
