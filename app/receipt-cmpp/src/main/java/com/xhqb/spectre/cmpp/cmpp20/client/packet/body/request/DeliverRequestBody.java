package com.xhqb.spectre.cmpp.cmpp20.client.packet.body.request;

import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppDataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.DataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.PacketBody;

/**
 * DeliverRequestBody
 *
 * @author: xiaoxiaoxiang
 * @date: 2020/5/15 16:21
 */
public enum DeliverRequestBody implements PacketBody {

    /**
     * Msg_Id
     */
    MSGID(CmppDataType.UNSIGNEDINT, 8),
    /**
     * Dest_Id
     */
    DESTID(CmppDataType.OCTERSTRING, 21),
    /**
     * Service_Id
     */
    SERVICEID(CmppDataType.OCTERSTRING, 10),
    /**
     * TP_pId
     */
    TPPID(CmppDataType.UNSIGNEDINT, 1),
    /**
     * TP_udhi
     */
    TPUDHI(CmppDataType.UNSIGNEDINT, 1),
    /**
     * Msg_Fmt
     */
    MSGFMT(CmppDataType.UNSIGNEDINT, 1),
    /**
     * Src_terminal_Id
     */
    SRCTERMINALID(CmppDataType.OCTERSTRING, 21),
    /**
     * Registered_Delivery
     */
    REGISTEREDDELIVERY(CmppDataType.UNSIGNEDINT, 1),
    /**
     * Msg_Length
     */
    MSGLENGTH(CmppDataType.UNSIGNEDINT, 1),
    /**
     * Msg_Content
     */
    MSGCONTENT(CmppDataType.OCTERSTRING, 0),
    /**
     * Reserve
     */
    RESERVE(CmppDataType.OCTERSTRING, 8);

    /**
     * body总长度
     */
    private static final int BODYLENGTH = MSGID.fieldLength
        + DESTID.fieldLength
        + SERVICEID.fieldLength
        + TPPID.fieldLength
        + TPUDHI.fieldLength
        + MSGFMT.fieldLength
        + SRCTERMINALID.fieldLength
        + REGISTEREDDELIVERY.fieldLength
        + MSGLENGTH.fieldLength
//            + MSGCONTENT.fieldLength
        + RESERVE.fieldLength;
    /**
     * 数据类型
     */
    private DataType dataType;
    /**
     * 长度
     */
    private int fieldLength;

    private DeliverRequestBody(DataType dataType, int fieldLength) {
        this.dataType = dataType;
        this.fieldLength = fieldLength;
    }

    @Override
    public int getBodyLength() {
        return BODYLENGTH;
    }

    @Override
    public DataType getDataType() {
        return this.dataType;
    }

    @Override
    public int getFieldLength() {
        return this.fieldLength;
    }
}
