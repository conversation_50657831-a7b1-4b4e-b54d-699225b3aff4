package com.xhqb.spectre.cmpp.cmpp20.client.codec.request;

import com.xhqb.spectre.cmpp.cmpp20.client.message.Message;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.ConnectRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.request.ConnectRequestBody;
import com.xhqb.spectre.cmpp.cmpp20.util.ByteUtil;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageCodec;
import io.netty.util.CharsetUtil;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/16 10:38
 */
@Slf4j
public class ConnectRequestMessageCodec extends MessageToMessageCodec<Message, ConnectRequestMessage> {

    private PacketType packetType;

    public ConnectRequestMessageCodec() {
        this.packetType = CmppPacketType.CONNECTREQUEST;
    }

    /**
     * client端只需要处理encode
     *
     * @param ctx
     * @param msg
     * @param out
     * @throws Exception
     */
    @Override
    protected void encode(ChannelHandlerContext ctx, ConnectRequestMessage msg, List<Object> out) throws Exception {
        log.info("------------connect request encode------------");

        // body总长度
        ByteBuf bodyBuffer = ctx.alloc().buffer(ConnectRequestBody.AUTHENTICATORSOURCE.getBodyLength());
        // 写入Source_Addr
        bodyBuffer.writeBytes(ByteUtil.ensureLength(msg.getSourceAddr().getBytes(CharsetUtil.UTF_8),
            ConnectRequestBody.SOURCEADDR.getFieldLength()));
        // 写入AuthenticatorSource
        bodyBuffer.writeBytes(ByteUtil.ensureLength(msg.getAuthenticatorSource(),
            ConnectRequestBody.AUTHENTICATORSOURCE.getFieldLength()));
        // 写入version
        bodyBuffer.writeByte(msg.getVersion());
        // 写入timestamp
        bodyBuffer.writeInt(msg.getTimestamp());

        msg.setBodyBuffer(ByteUtil.byteBufToByteArray(bodyBuffer, bodyBuffer.readableBytes()));
        msg.getHeader().setBodyLength(msg.getBodyBuffer().length);
        ReferenceCountUtil.release(bodyBuffer);
        out.add(msg);
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, Message msg, List<Object> out) throws Exception {
        log.info("------------connect request decode------------");
        int commandId = msg.getHeader().getCommandId();
        if (packetType.getCommandId() != commandId) {
            //不解析，交给下一个codec
            out.add(msg);
            return;
        }
        out.add(msg);
    }
}
