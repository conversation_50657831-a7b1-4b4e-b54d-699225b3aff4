package com.xhqb.spectre.cmpp.cmpp20.client.handler;

import com.xhqb.spectre.cmpp.cmpp20.client.codec.BodyCodecAggregator;
import com.xhqb.spectre.cmpp.cmpp20.client.codec.HeaderCodec;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;


/**
 * CodecChannelHandler
 *
 * @author: xiaoxiaoxiang
 * @date: 2020/5/15 17:30
 */
public class CodecChannelHandler extends ChannelInitializer<SocketChannel> {

    public static String handlerName() {
        return "cmppCodec";
    }

    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        ChannelPipeline pipeline = ch.pipeline();

        pipeline.addBefore(handlerName(), "frameDecoder", new LengthFieldBasedFrameDecoder(4 * 1024, 0, 4, -4, 0, true));

        pipeline.addBefore(handlerName(), HeaderCodec.handlerName(), new HeaderCodec());

        pipeline.addBefore(handlerName(), BodyCodecAggregator.handlerName(), new BodyCodecAggregator());
    }
}
