package com.xhqb.spectre.cmpp.util;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.msgcenter.model.MsgSendRequest;
import com.xhqb.msgcenter.model.iteam.MsgSendEntry;
import com.xhqb.msgcenter.sdk.util.OminiSignatureUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class OminiUtil {

    @Value("${omini.host}")
    private String host;

    @Value("${omini.uri}")
    private String uri;

    @Value("${omini.accessKeySecret}")
    private String accessKeySecret;

    @Value("${omini.strategyId.cmppChannelUnactivated}")
    private String cmppChannelUnactivated;

    @Value("${omini.strategyId.connectResponseError}")
    private String connectResponseError;

    public String getCmppChannelUnactivated() {
        return cmppChannelUnactivated;
    }

    //账号错误
    public String getConnectResponseError() {
        return connectResponseError;
    }

    public static MsgSendRequest createMsgSendRequest(String strategyId, Map<String, Object> map, String accessKeySecret) {
        MsgSendEntry msgSendEntry = new MsgSendEntry();
        msgSendEntry.setStrategyId(strategyId);
        msgSendEntry.setMsgVariableJson(map);
        ArrayList<MsgSendEntry> arrayList = new ArrayList<>();
        arrayList.add(msgSendEntry);
        MsgSendRequest msgSendRequest = new MsgSendRequest();
        msgSendRequest.setMsgSendEntries(arrayList);
        Long timestamp = System.currentTimeMillis();
        msgSendRequest.setTimestamp(timestamp);
        String sign = OminiSignatureUtil.makeSignature(msgSendRequest, accessKeySecret);
        msgSendRequest.setSign(sign);
        return msgSendRequest;
    }

    public void sendMessage(String strategyId, Map<String, Object> map) {
        try {
            MsgSendRequest msgSendRequest = createMsgSendRequest(strategyId, map, accessKeySecret);
            String json = JSONObject.toJSONString(msgSendRequest);
            post(json);
        } catch (Exception e) {
            log.error("发送通知异常;", e);
        }
    }

    private void post(String json) {
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("Accept-Charset", "UTF-8");
        String url = host + uri;
        String response = CommonHttpUtil.doPost(url, json, header);
        log.info("告警通知; url={}, request={}, response={}", url, json, response);
    }
}
