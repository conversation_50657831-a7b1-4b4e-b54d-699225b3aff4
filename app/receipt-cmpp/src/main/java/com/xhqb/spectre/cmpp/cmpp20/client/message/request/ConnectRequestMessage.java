package com.xhqb.spectre.cmpp.cmpp20.client.message.request;

import com.xhqb.spectre.cmpp.cmpp20.client.message.DefaultMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.Header;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.util.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/7 17:28
 */
@ToString(callSuper = true)
@Getter
@Setter
@Slf4j
public class ConnectRequestMessage extends DefaultMessage {
    private static final long serialVersionUID = 229435093441144042L;

    private String sourceAddr = "";

    private byte[] authenticatorSource = new byte[0];

    /**
     * cmpp2.0 版本号的值是32
     */
    private byte version = 0x20;

    private int timestamp = 0;

    public ConnectRequestMessage() {
        super(CmppPacketType.CONNECTREQUEST);
    }

    public ConnectRequestMessage(Header header) {
        super(CmppPacketType.CONNECTREQUEST, header);
    }

    public void build(String username, String password) {
        String timeText = DateUtil.getNowText(DateUtil.CMPP_DATE_FORMATTER);
        this.sourceAddr = username;
        this.authenticatorSource = calculate(username, password, timeText);
        this.timestamp = Integer.parseInt(timeText);
    }

    /**
     * 计算authenticatorSource
     *
     * @param username
     * @param password
     * @param timeText
     * @return
     */
    private byte[] calculate(String username, String password, String timeText) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] data = (username + "\0\0\0\0\0\0\0\0\0" + password + timeText).getBytes();
            return md5.digest(data);
        } catch (NoSuchAlgorithmException e) {
            log.error("AuthenticatorSource计算失败:{}", e);
        }
        return null;
    }
}
