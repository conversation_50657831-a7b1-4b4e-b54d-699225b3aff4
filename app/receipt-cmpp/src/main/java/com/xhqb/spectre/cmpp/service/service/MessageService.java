package com.xhqb.spectre.cmpp.service.service;


import com.xhqb.spectre.cmpp.service.model.dto.MessageDto;
import com.xhqb.spectre.cmpp.service.model.result.MessageSendResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-05-14
 */
public interface MessageService {

    /**
     * 发送短信
     *
     * @param messageDtoList
     * @return
     */
    MessageSendResult send(String channel, List<MessageDto> messageDtoList);
}
