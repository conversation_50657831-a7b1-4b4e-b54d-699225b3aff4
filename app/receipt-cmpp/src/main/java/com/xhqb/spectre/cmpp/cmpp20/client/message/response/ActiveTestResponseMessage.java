package com.xhqb.spectre.cmpp.cmpp20.client.message.response;

import com.xhqb.spectre.cmpp.cmpp20.client.message.DefaultMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.Header;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/8 17:03
 */
@ToString(callSuper = true)
@Getter
@Setter
public class ActiveTestResponseMessage extends DefaultMessage {

    private static final long serialVersionUID = 1351505546554411209L;

    private short reserved = 0;

    public ActiveTestResponseMessage(Header header) {
        super(CmppPacketType.ACTIVETESTRESPONSE, header);
    }

    public ActiveTestResponseMessage(int sequenceId) {
        super(CmppPacketType.ACTIVETESTRESPONSE, sequenceId);
    }
}
