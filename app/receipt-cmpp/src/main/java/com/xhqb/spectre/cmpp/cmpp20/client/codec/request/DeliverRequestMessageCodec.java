package com.xhqb.spectre.cmpp.cmpp20.client.codec.request;

import com.xhqb.spectre.cmpp.cmpp20.client.message.Message;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.DeliverReportMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.DeliverRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.request.DeliverReport;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.request.DeliverRequestBody;
import com.xhqb.spectre.cmpp.cmpp20.util.ByteUtil;
import com.xhqb.spectre.cmpp.cmpp20.util.MsgIdUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageCodec;
import io.netty.util.CharsetUtil;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.List;


/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/16 10:38
 */
@Slf4j
public class DeliverRequestMessageCodec extends MessageToMessageCodec<Message, DeliverRequestMessage> {

    private PacketType packetType;

    public DeliverRequestMessageCodec() {
        this.packetType = CmppPacketType.DELIVERREQUEST;
    }

    @Override
    protected void encode(ChannelHandlerContext ctx, DeliverRequestMessage msg, List<Object> out) throws Exception {
        out.add(msg);
    }

    /**
     * client只做解码
     *
     * @param ctx
     * @param msg
     * @param out
     * @throws Exception
     */
    @Override
    protected void decode(ChannelHandlerContext ctx, Message msg, List<Object> out) throws Exception {
        int commandId = msg.getHeader().getCommandId();
        if (packetType.getCommandId() != commandId) {
            // 不解析，交给下一个codec
            out.add(msg);
            return;
        }
        DeliverRequestMessage requestMessage = new DeliverRequestMessage(msg.getHeader());

        ByteBuf bodyBuffer = Unpooled.wrappedBuffer(msg.getBodyBuffer());

        byte[] msgIdBytes = ByteUtil.byteBufToByteArray(bodyBuffer, DeliverRequestBody.MSGID.getFieldLength());
//        log.info("------------deliver request msgIdBytes: {}", msgIdBytes);
        requestMessage.setMsgId(MsgIdUtil.bytes2MsgId(msgIdBytes));

        requestMessage.setDestId(bodyBuffer.readCharSequence(DeliverRequestBody.DESTID.getFieldLength(),
            CharsetUtil.UTF_8).toString().trim());
        requestMessage.setServiceId(bodyBuffer.readCharSequence(DeliverRequestBody.SERVICEID.getFieldLength(),
            CharsetUtil.UTF_8).toString().trim());

        requestMessage.setTppid(bodyBuffer.readUnsignedByte());
        requestMessage.setTpudhi(bodyBuffer.readUnsignedByte());
        requestMessage.setMsgFmt(bodyBuffer.readUnsignedByte());

        requestMessage.setSrcTerminalId(bodyBuffer.readCharSequence(DeliverRequestBody.SRCTERMINALID.getFieldLength(),
            CharsetUtil.UTF_8).toString().trim());
        // java.nio.charset.Charset.forName("GB2312")
        short registeredDelivery = bodyBuffer.readUnsignedByte();
        requestMessage.setRegisteredDelivery(registeredDelivery);

        short msgLength = (short) (bodyBuffer.readUnsignedByte() & 0xffff);
        requestMessage.setMsgLength((msgLength));

        if (registeredDelivery == 0) {
            // 非状态报告
            byte[] contentbytes = new byte[msgLength];
            bodyBuffer.readBytes(contentbytes);
            requestMessage.setMsgContent(contentbytes);

            //长短信处理
            String msgContentText;
            if (contentbytes.length > 6 && contentbytes[0] == 5 && contentbytes[1] == 0 && contentbytes[2] == 3) {
                byte[] msgBytes = new byte[contentbytes.length - 6];
                System.arraycopy(contentbytes, 6, msgBytes, 0, contentbytes.length - 6);
                //msgContentText = new String(msgBytes, "UnicodeBigUnmarked");
                msgContentText = byte2String(msgBytes, requestMessage.getMsgFmt());
                log.info("上行长短信, 总条数:{}, 第{}条", contentbytes[4], contentbytes[5]);
            } else {
                //msgContentText = new String(contentbytes, "UnicodeBigUnmarked");
                msgContentText = byte2String(contentbytes, requestMessage.getMsgFmt());
            }

            requestMessage.setMsgContentText(msgContentText);
            log.info("CmppDeliverRequestMessage - 非状态报告, msgId:{}", requestMessage.getMsgId());
        } else {
            // 状态报告
            if (msgLength == DeliverRequestBody.MSGID.getBodyLength()) {
                log.warn("CmppDeliverRequestMessage - MsgContent length is {}. should be {}.", msgLength,
                    DeliverRequestBody.MSGID.getBodyLength());
            }
            DeliverReportMessage reportMessage = new DeliverReportMessage();

            byte[] reportMsgIdBytes = ByteUtil.byteBufToByteArray(bodyBuffer, DeliverReport.MSGID.getFieldLength());
//            log.info("------------deliver request report msgId: {}", requestMessage.getMsgId());
            reportMessage.setMsgId(MsgIdUtil.bytes2MsgId(reportMsgIdBytes));
            reportMessage.setStat(bodyBuffer.readCharSequence(DeliverReport.STAT.getFieldLength(),
                CharsetUtil.UTF_8).toString().trim());
            reportMessage.setSubmitTime(bodyBuffer.readCharSequence(DeliverReport.SUBMITTIME.getFieldLength(),
                CharsetUtil.UTF_8).toString().trim());
            reportMessage.setDoneTime(bodyBuffer.readCharSequence(DeliverReport.DONETIME.getFieldLength(),
                CharsetUtil.UTF_8).toString().trim());
            reportMessage.setDestTerminalId(bodyBuffer.readCharSequence(DeliverReport.DESTTERMINALID.getFieldLength(),
                CharsetUtil.UTF_8).toString().trim());
            reportMessage.setSmscSequence(bodyBuffer.readUnsignedInt());
            requestMessage.setReportMessage(reportMessage);
        }

        requestMessage.setReserve(bodyBuffer.readCharSequence(DeliverRequestBody.RESERVE.getFieldLength(),
            CharsetUtil.UTF_8).toString().trim());

//        log.info("------------deliver request :{}", requestMessage);
        ReferenceCountUtil.release(bodyBuffer);
        out.add(requestMessage);
    }

    private String byte2String(byte[] bytes, short fmt) {

        if (bytes == null || bytes.length == 0) {
            return "";
        }

        switch (fmt) {
            case 0:
            case 3:
            case 4:
                return new String(bytes, StandardCharsets.ISO_8859_1);
            case 15:
                return new String(bytes,  Charset.forName("GBK"));
            default:
                return new String(bytes, StandardCharsets.UTF_16BE);
        }
    }
}
