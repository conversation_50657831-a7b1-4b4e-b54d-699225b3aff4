package com.xhqb.spectre.cmpp.service.service.cmppclienthandler;

import com.xhqb.spectre.cmpp.cmpp20.client.CmppClientEntity;
import com.xhqb.spectre.cmpp.cmpp20.client.bussiness.CmppClientBusinessHandler;
import com.xhqb.spectre.cmpp.cmpp20.client.bussiness.SmsMsg;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.DeliverRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.SubmitRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.SubmitResponseMessage;
import com.xhqb.spectre.cmpp.zookeeper.ZookeeperUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

import static com.xhqb.spectre.cmpp.constant.CmppBusinessConstants.CMPP_LAST_ACTIVE_TIME;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/6/2 10:55
 */
@Component
@Slf4j
@Data
public class XhCmppClientBussinessHandler implements CmppClientBusinessHandler {

    @Autowired
    private ZookeeperUtil zookeeperUtil;

    private ConcurrentHashMap<Integer, Long> aliveMap = new ConcurrentHashMap();

    @Autowired
    private AsyncXhCmppClientBusinessHandler businessHandler;

    public void notifyZookeeperNode(final String path, final Object data) {
        zookeeperUtil.insertZkNode(path, data);
    }

    public void refreshLastActiveTime(Integer channelAccountId, long lastActiveTime) {
        aliveMap.put(channelAccountId, lastActiveTime);
    }

    @Override
    public void submitHandle(SubmitRequestMessage message, SmsMsg smsMsg) {
        businessHandler.submitHandleV3(message, smsMsg);
    }

    @Override
    public void submitRespHandle(SubmitResponseMessage message) {
        businessHandler.submitRespHandleV3(message);
    }

    @Override
    public void deliverHandle(DeliverRequestMessage message, CmppClientEntity clientEntity) {
        businessHandler.deliverHandleV3(message, clientEntity);
    }
}
