package com.xhqb.spectre.cmpp.cmpp20.client.message;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 生成算法如下：
 * 采用64位（8字节）的整数：
 * 时间（格式为MMDDHHMMSS，即月日时分秒）：bit64~bit39，其中
 * bit64~bit61：月份的二进制表示；
 * bit60~bit56：日的二进制表示；
 * bit55~bit51：小时的二进制表示；
 * bit50~bit45：分的二进制表示；
 * bit44~bit39：秒的二进制表示；
 * 短信网关代码：bit38~bit17，把短信网关的代码转换为整数填写到该字段中。
 * 序列号：bit16~bit1，顺序增加，步长为1，循环使用。
 * 各部分如不能填满，左补零，右对齐。
 * <p>
 * 注: client不负责生成msgId,只负责将byte[]转对象
 *
 * @author: xiaoxiaoxiang
 * @date: 2020/6/8 15:25
 */
@Getter
@NoArgsConstructor
public class MsgId implements Serializable {
    private static final long serialVersionUID = -1783483970441614924L;

    private int month;
    private int day;
    private int hour;
    private int minutes;
    private int seconds;
    private int gateId;
    private int sequenceId;

    public MsgId(int month, int day, int hour, int minutes, int seconds, int gateId, int sequenceId) {
        this.month = month;
        this.day = day;
        this.hour = hour;
        this.minutes = minutes;
        this.seconds = seconds;
        this.gateId = gateId;
        this.sequenceId = sequenceId;
    }

    @Override
    public String toString() {
        return String.format("%1$02d%2$02d%3$02d%4$02d%5$02d%6$07d%7$05d",
            month, day, hour, minutes, seconds, gateId, sequenceId);
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + day;
        result = prime * result + gateId;
        result = prime * result + hour;
        result = prime * result + minutes;
        result = prime * result + month;
        result = prime * result + seconds;
        result = prime * result + sequenceId;
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        MsgId other = (MsgId) obj;
        if (day != other.day) {
            return false;
        }
        if (gateId != other.gateId) {
            return false;
        }
        if (hour != other.hour) {
            return false;
        }
        if (minutes != other.minutes) {
            return false;
        }
        if (month != other.month) {
            return false;
        }
        if (seconds != other.seconds) {
            return false;
        }
        if (sequenceId != other.sequenceId) {
            return false;
        }
        return true;
    }
}
