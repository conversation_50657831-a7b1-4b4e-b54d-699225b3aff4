package com.xhqb.spectre.cmpp.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/6/2 17:02
 */
@ToString
@Getter
@Setter
public class SmsInfo implements Serializable {
    private static final long serialVersionUID = -9153843904957499902L;

    private String queueName;
    /**
     * 手机号
     */
    private String mobile;

    /**
     * 业务调用方传入的业务Id,用来作信息关联
     */
    private Long orderId;

    /**
     * 业务调用方传入的附加参数(不做处理,结果回执时原样返回)
     */
    private String addition;

    /**
     * 短信拆分后总数量
     */
    private Integer pkTotal;

    /**
     * 短信拆分后第几条
     */
    private Integer pkNumber;

    /**
     * 子消息条数(长短信会进行拆分)
     */
//    private int subMsgCount;

    /**
     * 供应商返回状态
     * 长短信情况下,虽然会有多个submit_resp,但对运营商来说,不可能部分submit成功,部分失败,
     * 所以该属性就没有放入SmsSubInfo
     */
    private String platformStatus;

    /**
     * 供应商返回状态时间
     */
    private String platformTime;

    /**
     * 运营商回执状态
     */
    private String reportStatus;

    /**
     * 运营商回执时间
     */
    private String reportTime;

    /**
     * 长短信拆分后的子短信标识
     */
    private String subMsgId;

    /**
     * 供应商
     */
    private String partnerPlatform;

    /**
     * submit事件的sequenceId
     */
    private int submitSeqId;

    /**
     * 发送状态:
     * 1: submit
     * 2: submitResp
     * 4. deliver
     * 设计思路同linux文件权限
     * 3 = 1 + 2
     * 5 = 4 + 1
     * 6 = 4 + 2
     * 7 = 1 + 2 + 4
     */
    private int recordStatus;

    private int unMatchCount = 0;

    private String tplCode;

    private String channelId;

    private Integer resend;

    private String smsTypeCode;

    private String destTerminalId;

    private String submitTime;

    private Integer recvSendTime;

    private Integer reqSrc;

    private String gatewayUserName;

    private String requestId;

    private String destId;

    /**
     * 订单表名后缀(yyyyMM)[由PosterConsumer类中进行填充]
     */
    private String tableNameSuffix;
}
