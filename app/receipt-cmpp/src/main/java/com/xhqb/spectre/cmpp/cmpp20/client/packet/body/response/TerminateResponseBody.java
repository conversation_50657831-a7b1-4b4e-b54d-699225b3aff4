package com.xhqb.spectre.cmpp.cmpp20.client.packet.body.response;

import com.xhqb.spectre.cmpp.cmpp20.client.packet.DataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.PacketBody;

public enum TerminateResponseBody implements PacketBody {
    ;
    private DataType dataType;
    private boolean isFixFiledLength;
    private int length;

    private TerminateResponseBody(DataType dataType, boolean isFixFiledLength, int length) {
        this.dataType = dataType;
        this.isFixFiledLength = isFixFiledLength;
        this.length = length;
    }
    @Override
    public DataType getDataType() {
        return dataType;
    }

    @Override
    public int getFieldLength() {
        return length;
    }

    @Override
    public int getBodyLength() {

        return 0;
    }
}