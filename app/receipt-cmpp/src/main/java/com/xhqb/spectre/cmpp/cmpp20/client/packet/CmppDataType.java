package com.xhqb.spectre.cmpp.cmpp20.client.packet;

/**
 * 数据类型
 *
 * @author: xiaoxia<PERSON>iang
 * @date: 2020/5/15 15:54
 */
public enum CmppDataType implements DataType {

    /**
     * 无符号整数
     */
    UNSIGNEDINT,
    /**
     * 定长字符串,位数不足时,如果左补0则补ASCII表示的零,如果右补0则补二进制的零
     */
    OCTERSTRING;

//    private int commandId;

//    private CmppDataType(int commandId) {
//        this.commandId = commandId;
//    }
//
//    public int getCommandId() {
//        return commandId;
//    }
//
//    public int getAllCommandId() {
//        int defaultId = 0x0;
//        int allCommandId = 0x0;
//        for(CmppDataType dataType : CmppDataType.values()) {
//            allCommandId |= dataType.commandId;
//        }
//        return allCommandId ^ defaultId;
//    }
}
