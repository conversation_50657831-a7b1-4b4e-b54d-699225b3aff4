//package com.xhqb.sms.channel.biz.com.xhqb.spectre.httpreceipt.service.config;
//
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.transaction.annotation.EnableTransactionManagement;
//
///**
// * MybatisConfiguration
// * @author: xiaoxiaoxiang
// * @date: 2019/9/11 10:09
// */
//@Configuration
//@ConditionalOnProperty(name = "kael.datasource.druid.resource-id")
//@MapperScan("com.xhqb.sms.channel.common.dal.dao")
//@EnableTransactionManagement
//public class MybatisConfig {
//}
