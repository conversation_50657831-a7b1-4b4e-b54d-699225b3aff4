package com.xhqb.spectre.cmpp.service.controller;

import com.xhqb.spectre.cmpp.cmpp20.client.ChannelManager;
import com.xhqb.spectre.cmpp.cmpp20.client.submit.CoupleSubmitRespDaemon;
import com.xhqb.spectre.cmpp.enums.ChannelStatusEnum;
import com.xhqb.spectre.cmpp.service.constant.BaseResultEnum;
import com.xhqb.spectre.cmpp.service.exception.ChannelException;
import com.xhqb.spectre.cmpp.service.model.dto.MessageDto;
import com.xhqb.spectre.cmpp.service.model.enums.ResultEnum;
import com.xhqb.spectre.cmpp.service.model.request.MessageSendRequest;
import com.xhqb.spectre.cmpp.service.model.result.BaseResult;
import com.xhqb.spectre.cmpp.service.model.result.MessageSendResult;
import com.xhqb.spectre.cmpp.service.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR>
 * @date 2020-05-14
 */
@RestController
@Slf4j
public class MessageController {// extends BaseController {

    @Autowired
    private MessageService messageService;

    @Autowired
    CoupleSubmitRespDaemon coupleSubmitRespDaemon;

    /**
     * 发送短信
     *
     * @param message
     * @return
     */
    @PostMapping(value = "/{channel}/sendWithNoDelay", headers = {"content-type=application/json"})
    public MessageSendResult send(@PathVariable String channel, @RequestBody MessageSendRequest message) {
        log.info("收到http请求; message={}", message);
        MessageSendResult result;
        //如果没有可用渠道直接返回错误
        MessageDto messageDto = message.getMessageDtoList().get(0);
        Long channelStatus = ChannelManager.getChannelStatus(messageDto.getChannelId());
        if (!ChannelStatusEnum.WORKING.getStatus().equals(channelStatus)) {
            log.info("渠道状态错误; channelAccountId:{}, channelStatus:{}", messageDto.getChannelId(), channelStatus);
            return new MessageSendResult(false, ResultEnum.CHANNEL_MISS);
        }

        try {
            result = messageService.send(channel, message.getMessageDtoList());
        } catch (ChannelException e) {
            log.error("channel 异常 channelAccountId {}", message.getMessageDtoList().get(0).getChannelId());
            return new MessageSendResult(false, ResultEnum.CHANNEL_MISS);
        }

        return result;
    }

    @PostMapping(value = "/{channel}/send", headers = {"content-type=application/json"})
    public Object sendWithDelay(@PathVariable String channel, @RequestBody MessageSendRequest message) {
        log.info("收到http请求; message={}", message);

        //如果没有可用渠道直接返回错误
        MessageDto messageDto = message.getMessageDtoList().get(0);
        Long channelStatus = ChannelManager.getChannelStatus(messageDto.getChannelId());
        if (!ChannelStatusEnum.WORKING.getStatus().equals(channelStatus)) {
            log.info("渠道状态错误; channelAccountId:{}, channelStatus:{}", messageDto.getChannelId(), channelStatus);
            return new MessageSendResult(false, ResultEnum.CHANNEL_MISS);
        }

        MessageSendResult timeoutReq = new MessageSendResult(false, ResultEnum.REQ_TIMEOUT);
        DeferredResult<MessageSendResult> deferredResult = new DeferredResult<MessageSendResult>(10000l, timeoutReq);

        // 这个不支持群发直接返回结果，只支持单条发送
        coupleSubmitRespDaemon.getOrderMap().put(message.getMessageDtoList().get(0).getAddition(), deferredResult);

        try {
            MessageSendResult result = messageService.send(channel, message.getMessageDtoList());
        } catch (ChannelException e) {
            log.error("channel 异常 channelAccountId {}", message.getMessageDtoList().get(0).getChannelId());
            coupleSubmitRespDaemon.getOrderMap().get(message.getMessageDtoList().get(0).getAddition())
                    .setResult(new MessageSendResult(false, ResultEnum.CHANNEL_MISS));
            coupleSubmitRespDaemon.getOrderMap().remove(message.getMessageDtoList().get(0).getAddition());
        }


        return deferredResult;
//		if (result.isSuccess()) {
//			return new BaseResult(BaseResultEnum.SUCCESS, "发送成功");
//		} else {
//			return new BaseResult(BaseResultEnum.FAIL, result.getFailCode());
//		}
    }


//	@PostMapping("/send")
//	public BaseResult send(@RequestBody @Valid MessageDTO message, BindingResult bindingResult) {
//		if (bindingResult.hasErrors()) {
//			return new BaseResult(BaseResultEnum.ILLEGAL_ARGUMENT, bindingResult.getFieldError().getDefaultMessage());
//		}
//
//		SendMessageResult result = messageService.send(message);
//		if (result.isSuccess()) {
//			return new BaseResult(BaseResultEnum.SUCCESS, "发送成功");
//		} else {
//			return new BaseResult(BaseResultEnum.FAIL, result.getFailCode());
//		}
//	}

    /**
     * 批量发送短信
     *
     * @param
     * @return
     */
//	@PostMapping("/batchSend")
//	public BaseResult batchSend(@RequestBody @Valid BatchMessageDTO batchMessage, BindingResult bindingResult) {
//		if (bindingResult.hasErrors()) {
//			return new BaseResult(BaseResultEnum.ILLEGAL_ARGUMENT, bindingResult.getFieldError().getDefaultMessage());
//		}
//		messageService.batchSend(batchMessage);
//		return new BaseResult(BaseResultEnum.SUCCESS, "发送成功");
//	}
    @GetMapping("/test")
    public String test() {
        return "test";
    }
}
