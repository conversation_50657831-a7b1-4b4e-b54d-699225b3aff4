package com.xhqb.spectre.cmpp.lucifer.aspect;

import com.xhqb.spectre.cmpp.lucifer.context.ApiLuciferContext;
import com.xhqb.spectre.cmpp.lucifer.handler.ApiLuciferHandler;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * http api埋点 拦截
 *
 * <AUTHOR>
 * @date 2021/10/14
 */
@Aspect
@Component
@Slf4j
public class ApiLuciferAspect {

    @Resource
    private ApiLuciferHandler apiLuciferHandler;

    /**
     * 做api埋点拦截操作
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("@within(org.springframework.stereotype.Controller) || @within(org.springframework.web.bind.annotation.RestController)")
    public Object apiAspect(ProceedingJoinPoint joinPoint) throws Throwable {
        ApiLuciferContext context = new ApiLuciferContext();
        apiLuciferHandler.preHandle(context);
        try {
            return joinPoint.proceed();
        } finally {
            doFinalHandler(context);
        }
    }


    /**
     * 后置final处理
     *
     * @param context
     */
    private void doFinalHandler(ApiLuciferContext context) {
        try {
            HttpServletRequest request = context.getRequest();
            HttpServletResponse response = context.getResponse();
            String mappingUri = request.getRequestURI();
            String status = String.valueOf(response.getStatus());
            // request length
            long contentSize = getContentLength(request);
            context.setLabels(mappingUri, status);
            context.setContentSize(contentSize);
            apiLuciferHandler.postHandle(context);
        } catch (Throwable e) {
            log.error("doFinalHandler detected error", e);
        }

    }

    private long getContentLength(HttpServletRequest request) {
        long contentLength = request.getContentLength();
        if (contentLength < 0) {
            contentLength = 0;
        }
        return contentLength;
    }
}
