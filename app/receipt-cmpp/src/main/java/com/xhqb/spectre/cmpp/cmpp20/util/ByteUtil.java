package com.xhqb.spectre.cmpp.cmpp20.util;

import io.netty.buffer.ByteBuf;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/16 11:39
 */
public final class ByteUtil {

    private ByteUtil() {
    }

    /**
     * 保证字节数据的长度,如果原字节数据长度不够,则补0
     *
     * @param array
     * @param minLength
     * @return
     */
    public static byte[] ensureLength(byte[] array, int minLength) {
        return ensureLength(array, minLength, 0);
    }

    /**
     * 保证字节数据的长度
     *
     * @param array
     * @param minLength
     * @param padding
     * @return
     */
    public static byte[] ensureLength(byte[] array, int minLength, int padding) {
        if (minLength < 0) {
            throw new IllegalArgumentException(String.format("Invalid minLength: %s", minLength));
        }
        if (padding < 0) {
            throw new IllegalArgumentException(String.format("Invalid padding: %s", padding));
        }
        if (array.length == minLength) {
            return array;
        }
        return array.length > minLength ? copyOf(array, minLength) : copyOf(array, minLength + padding);
    }

    private static byte[] copyOf(byte[] original, int length) {
        byte copy[] = new byte[length];
        System.arraycopy(original, 0, copy, 0, Math.min(original.length, length));
        return copy;
    }

    /**
     * @param buf
     * @param length
     * @return
     */
    public static byte[] byteBufToByteArray(ByteBuf buf, int length) {
        byte[] result = new byte[length];
        buf.readBytes(result);
        return result;
    }
}
