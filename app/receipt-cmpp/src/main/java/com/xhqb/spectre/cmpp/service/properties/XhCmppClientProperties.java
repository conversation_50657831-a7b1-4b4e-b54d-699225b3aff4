package com.xhqb.spectre.cmpp.service.properties;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/6/2 11:27
 */
@Component
@ToString
@Getter
@Setter
public class XhCmppClientProperties {

    /**
     * 合作方(同一个合作方下不同渠道保持一致,比如营销和行业,保持一致)
     */
    private String platform;

    /**
     * cmpp server host
     */
    private String serverHost;

    /**
     * cmpp server port
     */
    private int serverPort;

    /**
     * 登录用户名
     */
    private String userName;

    /**
     * 登录密码
     */
    private String password;

    /**
     * cmpp协议参数
     */
    private String serviceId;

    /**
     * cmpp协议参数
     */
    private String srcId;

    /**
     * cmpp协议参数
     */
    private String msgSrc;
}
