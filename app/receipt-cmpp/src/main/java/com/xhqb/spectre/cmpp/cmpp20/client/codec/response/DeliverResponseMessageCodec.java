package com.xhqb.spectre.cmpp.cmpp20.client.codec.response;

import com.xhqb.spectre.cmpp.cmpp20.client.message.Message;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.DeliverResponseMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.response.DeliverResponseBody;
import com.xhqb.spectre.cmpp.cmpp20.util.ByteUtil;
import com.xhqb.spectre.cmpp.cmpp20.util.MsgIdUtil;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageCodec;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/16 10:38
 */
@Slf4j
public class DeliverResponseMessageCodec extends MessageToMessageCodec<Message, DeliverResponseMessage> {

    private PacketType packetType;

    public DeliverResponseMessageCodec() {
        this.packetType = CmppPacketType.DELIVERRESPONSE;
    }

    /**
     * client只处理编码
     *
     * @param ctx
     * @param msg
     * @param out
     * @throws Exception
     */
    @Override
    protected void encode(ChannelHandlerContext ctx, DeliverResponseMessage msg, List<Object> out) throws Exception {
        ByteBuf bodyBuffer = ctx.alloc().buffer(DeliverResponseBody.MSGID.getBodyLength());
        bodyBuffer.writeBytes(ByteUtil.ensureLength(MsgIdUtil.msgId2Bytes(msg.getMsgId()), DeliverResponseBody.MSGID.getFieldLength()));
        bodyBuffer.writeByte(msg.getResult());

        msg.setBodyBuffer(ByteUtil.byteBufToByteArray(bodyBuffer, bodyBuffer.readableBytes()));
        msg.getHeader().setBodyLength(msg.getBodyBuffer().length);
//        log.info("------------deliver response: {}", msg);
        ReferenceCountUtil.release(bodyBuffer);
        out.add(msg);
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, Message msg, List<Object> out) throws Exception {
        int commandId = msg.getHeader().getCommandId();
        if (packetType.getCommandId() != commandId) {
            // 不解析，交给下一个codec
            out.add(msg);
            return;
        }
        out.add(msg);
    }
}
