package com.xhqb.spectre.cmpp.cmpp20.client.packet.body.request;

import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppDataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.DataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.PacketBody;

/**
 * SubmitRequestBody
 *
 * @author: xiaoxiaoxiang
 * @date: 2020/5/15 16:21
 */
public enum SubmitRequestBody implements PacketBody {
    /**
     * Msg_Id
     */
    MSGID(CmppDataType.UNSIGNEDINT, 8),
    /**
     * Pk_total
     */
    PKTOTAL(CmppDataType.UNSIGNEDINT, 1),
    /**
     * Pk_number
     */
    PKNUMBER(CmppDataType.UNSIGNEDINT, 1),
    /**
     * Registered_Delivery
     */
    REGISTEREDDELIVERY(CmppDataType.UNSIGNEDINT, 1),
    /**
     * Msg_level
     */
    MSGLEVEL(CmppDataType.UNSIGNEDINT, 1),
    /**
     * Service_Id
     */
    SERVICEID(CmppDataType.OCTERSTRING, 10),
    /**
     * Fee_UserType
     */
    FEEUSERTYPE(CmppDataType.UNSIGNEDINT, 1),
    /**
     * Fee_terminal_Id
     */
    FEETERMINALID(CmppDataType.OCTERSTRING, 21),
    /**
     * TP_pId
     */
    TPPID(CmppDataType.UNSIGNEDINT, 1),
    /**
     * TP_udhi
     */
    TPUDHI(CmppDataType.UNSIGNEDINT, 1),
    /**
     * Msg_Fmt
     */
    MSGFMT(CmppDataType.UNSIGNEDINT, 1),
    /**
     * Msg_src
     */
    MSGSRC(CmppDataType.OCTERSTRING, 6),
    /**
     * FeeType
     */
    FEETYPE(CmppDataType.OCTERSTRING, 2),
    /**
     * FeeCode
     */
    FEECODE(CmppDataType.OCTERSTRING, 6),
    /**
     * ValId_Time
     */
    VALIDTIME(CmppDataType.OCTERSTRING, 17),
    /**
     * At_Time
     */
    ATTIME(CmppDataType.OCTERSTRING, 17),
    /**
     * Src_Id
     */
    SRCID(CmppDataType.OCTERSTRING, 21),
    /**
     * DestUsr_tl
     */
    DESTUSRTL(CmppDataType.UNSIGNEDINT, 1),
    /**
     * Dest_terminal_Id
     */
    DESTTERMINALID(CmppDataType.OCTERSTRING, 21),
    /**
     * Msg_Length
     */
    MSGLENGTH(CmppDataType.UNSIGNEDINT, 1),
    /**
     * Msg_Content
     */
    MSGCONTENT(CmppDataType.OCTERSTRING, 0),
    /**
     * Reserve
     */
    RESERVE(CmppDataType.OCTERSTRING, 8);

    /**
     * body总长度
     */
    private static final int BODYLENGTH = MSGID.fieldLength
        + PKTOTAL.fieldLength
        + PKNUMBER.fieldLength
        + REGISTEREDDELIVERY.fieldLength
        + MSGLEVEL.fieldLength
        + SERVICEID.fieldLength
        + FEEUSERTYPE.fieldLength
        + FEETERMINALID.fieldLength
        + TPPID.fieldLength
        + TPUDHI.fieldLength
        + MSGFMT.fieldLength
        + MSGSRC.fieldLength
        + FEETYPE.fieldLength
        + FEECODE.fieldLength
        + VALIDTIME.fieldLength
        + ATTIME.fieldLength
        + SRCID.fieldLength
        + DESTUSRTL.fieldLength
//            + DESTTERMINALID.fieldLength
        + MSGLENGTH.fieldLength
//            + MSGCONTENT.fieldLength
        + RESERVE.fieldLength;
    /**
     * 数据类型
     */
    private DataType dataType;
    /**
     * 长度
     */
    private int fieldLength;

    private SubmitRequestBody(DataType dataType, int fieldLength) {
        this.dataType = dataType;
        this.fieldLength = fieldLength;
    }

    @Override
    public int getBodyLength() {
        return BODYLENGTH;
    }

    @Override
    public DataType getDataType() {
        return this.dataType;
    }

    @Override
    public int getFieldLength() {
        return this.fieldLength;
    }
}
