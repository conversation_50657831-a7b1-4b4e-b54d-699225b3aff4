package com.xhqb.spectre.cmpp.service.model.result;

import com.xhqb.spectre.cmpp.service.model.enums.ResultEnum;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/19 11:27
 */
public class MessageBatchSendResult extends BaseResult {

    private static final long serialVersionUID = 2896460386295331401L;

    public MessageBatchSendResult() {
    }

    public MessageBatchSendResult(boolean success, ResultEnum resultEnum) {
        super(success, resultEnum);
    }

    public MessageBatchSendResult(boolean success, String otherResultCode, String otherResultMsg) {
        super(success, otherResultCode, otherResultMsg);
    }
}
