package com.xhqb.spectre.cmpp.cmpp20.client.handler;

import com.xhqb.spectre.cmpp.cmpp20.client.ChannelManager;
import com.xhqb.spectre.cmpp.cmpp20.client.CmppClient;
import com.xhqb.spectre.cmpp.cmpp20.client.CmppClientEntity;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.ActiveTestRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.ActiveTestResponseMessage;
import com.xhqb.spectre.cmpp.enums.ChannelStatusEnum;
import com.xhqb.spectre.cmpp.service.service.cmppclienthandler.XhCmppClientBussinessHandler;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 7.4.7	链路检测（CMPP¬_ACTIVE_TEST）操作
 *
 * <AUTHOR>
 * @date 2021/10/14
 */
@Slf4j
public class ActiveTestClientHandler extends SimpleChannelInboundHandler<ActiveTestResponseMessage> {
    /**
     * 链路检测请求
     */
    private ActiveTestRequestMessage activeTestRequestMessage = new ActiveTestRequestMessage();
    /**
     * cmpp 客户端
     */
    private CmppClient cmppClient;
    /**
     * cmpp客户端业务处理器
     */
    private XhCmppClientBussinessHandler businessHandler;

    public ActiveTestClientHandler(CmppClient cmppClient) {
        this.cmppClient = cmppClient;
        this.businessHandler = (XhCmppClientBussinessHandler) this.cmppClient.getBusinessHandler();
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, ActiveTestResponseMessage msg) throws Exception {
        Channel channel = ctx.channel();
        CmppClientEntity clientEntity = ChannelManager.getCmppClientEntity(channel);
        if (cmppClient.isLogEnable()) {
            log.info("platform: {}, timestamp {}, channelAccountId {}", clientEntity.getPlatform(), System.currentTimeMillis(), clientEntity.getId());
        }
        businessHandler.refreshLastActiveTime(clientEntity.getId(), System.currentTimeMillis());
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        try {
            boolean isIdleEvent = evt instanceof IdleStateEvent;
            if (!isIdleEvent) {
                // 不是空闲事件 不做处理
                return;
            }

            IdleStateEvent event = (IdleStateEvent) evt;

            if (cmppClient.isLogEnable()) {
                log.info("IdleStateEvent state:{}", event.state());
            }

            if (!IdleState.WRITER_IDLE.equals(event.state())) {
                // 不是空闲写状态 不做处理
                return;
            }

            Channel channel = ctx.channel();
            CmppClientEntity clientEntity = ChannelManager.getCmppClientEntity(channel);
            if (Objects.isNull(clientEntity)) {
                // 防止空指针异常
                clientEntity = new CmppClientEntity();
            }
            if (cmppClient.isLogEnable()) {
                log.info("active test request key {} platform {}, username {} localAddress {}",
                        channel,
                        clientEntity.getPlatform(),
                        clientEntity.getUserName(),
                        channel.localAddress());
            }
            final String clientId = Objects.nonNull(clientEntity.getId()) ? clientEntity.getId() + "" : null;
            ctx.writeAndFlush(activeTestRequestMessage).addListener((ChannelFutureListener) future -> {
                if (!future.isSuccess()) {
                    if (!future.channel().isActive()) {
                        log.info("心跳检测渠道已断开; channelAccountID={}", clientId);
                        future.channel().close();
                        // 设置channel状态
                        ChannelManager.setChannelStatus(clientId, ChannelStatusEnum.SHUTDOWN);
                    }
                } else {
                    ChannelManager.setChannelStatus(clientId, ChannelStatusEnum.WORKING);
                }
            });
        } finally {
            // 事件往下传递
            super.userEventTriggered(ctx, evt);
        }
    }
}
