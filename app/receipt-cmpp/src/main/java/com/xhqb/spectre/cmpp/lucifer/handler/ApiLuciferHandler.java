package com.xhqb.spectre.cmpp.lucifer.handler;

import com.xhqb.kael.lucifer.telemetry.PrometheusCounterMetrics;
import com.xhqb.kael.lucifer.telemetry.PrometheusGaugeMetrics;
import com.xhqb.kael.lucifer.telemetry.PrometheusHistogramMetrics;
import com.xhqb.spectre.cmpp.lucifer.context.ApiLuciferContext;
import com.xhqb.spectre.cmpp.lucifer.LuciferHandler;
import io.prometheus.client.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * http api埋点
 * <p>
 * 只处理提供http api的方法
 *
 * <AUTHOR>
 * @date 2021/10/14
 */
@Component
@Slf4j
public class ApiLuciferHandler implements LuciferHandler<ApiLuciferContext> {

    private final double[] METRIC_BUCKETS =
            new double[]{.005, .01, .025, .05, .075, .1, .25, .5, .75, 1, 2.5, 5, 7.5, 10, 15, 20, 30};

    public final Summary RECEIVED_BYTES = Summary.build()
            .namespace("http")
            .name("requests_size_bytes")
            .labelNames("uri", "status")
            .help("Request size in bytes.").register();
    /**
     * 正在处理的请求
     */
    public final Gauge GAUGE = new PrometheusGaugeMetrics("spectre_cmpp_api_gauge", "gauge description")
            .createWithLabels("uri");

    /**
     * 接口请求次数
     */
    public final Counter COUNTER = new PrometheusCounterMetrics("spectre_cmpp_api_request_total", "description")
            .createWithLabels("uri", "status");

    /**
     * 接口处理耗时
     */
    public final Histogram HISTOGRAM = new PrometheusHistogramMetrics("spectre_cmpp_api_histogram", "histogram description", METRIC_BUCKETS)
            .createWithLabels("uri", "status");

    /**
     * 前置处理
     */
    @Override
    public void preHandle(ApiLuciferContext context) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            context.setRequest(request).setResponse(attributes.getResponse());
            String apiName = request.getRequestURI();
            GAUGE.labels(apiName).inc();
            context.setApiName(apiName);
        } catch (Exception e) {
            log.error("http api埋点前置处理失败", e);
        }

    }

    /**
     * 后置处理
     */
    @Override
    public void postHandle(ApiLuciferContext context) {
        try {
            String[] labels = context.getLabels();
            // 请求总数
            COUNTER.labels(labels).inc();
            // 请求时间
            HISTOGRAM.labels(labels)
                    .observe((System.nanoTime() - context.getStartNanoTime()) / Collector.NANOSECONDS_PER_SECOND);
            RECEIVED_BYTES.labels(labels).observe(context.getContentSize());
            GAUGE.labels(context.getApiName()).dec();
        } catch (Exception e) {
            log.error("http api埋点后置处理失败, apiName = {}", context.getApiName(), e);
        }
    }

}
