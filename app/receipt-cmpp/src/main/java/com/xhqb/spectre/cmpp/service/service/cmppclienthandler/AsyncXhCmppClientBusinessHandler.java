package com.xhqb.spectre.cmpp.service.service.cmppclienthandler;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.cmpp.cmpp20.client.CmppClientEntity;
import com.xhqb.spectre.cmpp.cmpp20.client.bussiness.SmsMsg;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.DeliverRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.SubmitRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.SubmitResponseMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.submit.CoupleSubmitRespDaemon;
import com.xhqb.spectre.cmpp.cmpp20.client.wrapper.DeliverRequestMessageWrapper;
import com.xhqb.spectre.cmpp.cmpp20.util.ClockUtil;
import com.xhqb.spectre.cmpp.constant.CmppBusinessConstants;
import com.xhqb.spectre.cmpp.dto.SmsInfo;
import com.xhqb.spectre.cmpp.util.SmsInfoCacheUtil;
import com.xhqb.spectre.common.dal.dto.mq.SubmitResqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;

import static com.xhqb.spectre.cmpp.constant.CmppBusinessConstants.CMPP_SUBMIT_REQUEST_KEY;
import static com.xhqb.spectre.cmpp.constant.CmppBusinessConstants.CMPP_SUBMIT_RESPONSE_KEY;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/6/2 10:57
 */
@Slf4j
@Component
public class AsyncXhCmppClientBusinessHandler {

    @Autowired
    private RedisTemplate<String, Object> jsonRedisTemplate;

    @Value("#{'${kael.mq.producers:}'.split(',')[1]}")
    private String spectreSubmitResq;

    @Value("${spectre.mq.submit.delay:1}")
    private Long mqSubmitDelay;


    @Value("${print.sequenceId.enable:true}")
    private volatile boolean printSequenceIdEnable;

    @Autowired
    private MQTemplate<String> mqTemplate;

    @Autowired
    private CoupleSubmitRespDaemon coupleSubmitRespDaemon;

    /**
     * submit事件的sequenceId，用来关联submit与submit_resp事件消息
     * submit_resp的msgId,用来关联submit_resp和deliver事件消息
     */

    /**
     * 注意: 在向channel写入submit信息前调用次方法,此方法不能异步
     * 如果写入redis时发现key已存在,则属于sequenceId重复,抛出异常
     *
     * @param message
     * @param addition
     */
    public void submitHandle(SubmitRequestMessage message, String addition, String platform) {
//        log.info("threadName:{}", Thread.currentThread().getName());
        SmsInfo smsInfo = new SmsInfo();
        smsInfo.setMobile(message.getDestterminalId()[0]);
        smsInfo.setAddition(addition);
        smsInfo.setOrderId(Long.parseLong(addition));
        smsInfo.setPkTotal((int) message.getPktotal());
        smsInfo.setPkNumber((int) message.getPknumber());
        smsInfo.setPartnerPlatform(platform);
        smsInfo.setSubmitSeqId(message.getHeader().getSequenceId());
        smsInfo.setRecordStatus(CmppBusinessConstants.SUBMIT_STATUS);
        log.info("submitHandle smsInfo:{}", smsInfo);
        jsonRedisTemplate.opsForList().rightPush(CmppBusinessConstants.CMPP_ALL_RECORD_KEY, smsInfo);
    }

    /**
     * 直接
     *
     * @param message
     */
    public void submitHandleV3(SubmitRequestMessage message, SmsMsg smsMsg) {
        SmsInfo smsInfo = new SmsInfo();
        smsInfo.setMobile(message.getDestterminalId()[0]);
        smsInfo.setAddition(smsMsg.getAddition());
        smsInfo.setOrderId(Long.parseLong(smsMsg.getAddition()));
        smsInfo.setPkTotal((int) message.getPktotal());
        smsInfo.setPkNumber((int) message.getPknumber());
        smsInfo.setPartnerPlatform(smsMsg.getChannelCode());
        smsInfo.setSubmitSeqId(message.getHeader().getSequenceId());
        smsInfo.setRecordStatus(CmppBusinessConstants.SUBMIT_STATUS);
        smsInfo.setTplCode(smsMsg.getTplCode());
        smsInfo.setChannelId(smsMsg.getChannelId());
        smsInfo.setResend(smsMsg.getResend());
        smsInfo.setSmsTypeCode(smsMsg.getSmsTypeCode());
        smsInfo.setRecvSendTime(ClockUtil.INS.tick());
        smsInfo.setReqSrc(smsMsg.getReqSrc());
        smsInfo.setRequestId(smsMsg.getRequestId());
        smsInfo.setGatewayUserName(smsMsg.getGatewayUserName());
        smsInfo.setTableNameSuffix(smsMsg.getTableNameSuffix());

//        log.info("提交请求后入队; smsInfo:{}", smsInfo);
        if (printSequenceIdEnable) {
            log.info("submitHandleV3|channelId:{}|submitSeqId:{}|requestId:{}", smsMsg.getChannelId(), smsInfo.getSubmitSeqId(), smsMsg.getRequestId());
        }
        //jsonRedisTemplate.opsForHash().put(CMPP_SUBMIT_REQUEST_KEY, smsInfo.getSubmitSeqId(), smsInfo);
        SmsInfoCacheUtil.setSmsInfo(CMPP_SUBMIT_REQUEST_KEY + ":" + smsInfo.getSubmitSeqId(), smsInfo);
    }

    /**
     * 注意deliver事件消息比submit_resp事件消息先回来的情况
     *
     * @param message
     */
    @Async
    public void submitRespHandle(SubmitResponseMessage message) {
//        log.info("threadName:{}", Thread.currentThread().getName());
        submitRespHandleV2(message);
    }

    private void submitRespHandleV2(SubmitResponseMessage message) {
        SmsInfo smsInfo = new SmsInfo();
        String msgId = message.getMsgId().toString();
        smsInfo.setPlatformStatus(String.valueOf(message.getResult()));
        smsInfo.setPlatformTime(String.valueOf(ClockUtil.INS.now()));
        smsInfo.setSubMsgId(msgId);
        smsInfo.setSubmitSeqId(message.getHeader().getSequenceId());
        smsInfo.setRecordStatus(CmppBusinessConstants.SUBMIT_RESP_STATUS);
        log.info("submitRespHandleV2 smsInfo:{}", smsInfo);
        jsonRedisTemplate.opsForList().rightPush(CmppBusinessConstants.CMPP_ALL_RECORD_KEY, smsInfo);
    }

    @Async
    public void submitRespHandleV3(SubmitResponseMessage message) {
        SmsInfo smsInfo = new SmsInfo();
        String msgId = message.getMsgId().toString();
        smsInfo.setPlatformStatus(String.valueOf(message.getResult()));
        smsInfo.setPlatformTime(String.valueOf(ClockUtil.INS.now()));
        smsInfo.setSubMsgId(msgId);
        smsInfo.setSubmitSeqId(message.getHeader().getSequenceId());
        smsInfo.setRecordStatus(CmppBusinessConstants.SUBMIT_RESP_STATUS);
//        log.info("submitRespHandleV3 smsInfo:{}", smsInfo);

        String submitRequestCacheKey = CmppBusinessConstants.CMPP_SUBMIT_REQUEST_KEY + ":" + message.getHeader().getSequenceId();
        SmsInfo smsInfoSubmitReq = SmsInfoCacheUtil.getSmsInfo(submitRequestCacheKey);
        if (smsInfoSubmitReq == null) {
            log.warn("smsInfoSubmitReq is null|sequenceId:{}", message.getHeader().getSequenceId());
            return;
        }
        smsInfo.setChannelId(smsInfoSubmitReq.getChannelId());
        smsInfo.setTplCode(smsInfoSubmitReq.getTplCode());
        smsInfo.setTableNameSuffix(smsInfoSubmitReq.getTableNameSuffix());
        SmsInfoCacheUtil.setSmsInfo(CMPP_SUBMIT_RESPONSE_KEY + ":" + msgId, smsInfo);

        // 串联submit response
        SubmitResqDTO submitResqDTO = SubmitResqDTO.builder().build();
        submitResqDTO.setChannelMsgId(msgId);
        submitResqDTO.setOrderId(smsInfoSubmitReq.getOrderId());
        submitResqDTO.setResult((int) message.getResult());
        submitResqDTO.setRecvSubmitTime(Long.valueOf(smsInfo.getPlatformTime()));
        submitResqDTO.setResend(smsInfoSubmitReq.getResend());
        submitResqDTO.setChannelCode(smsInfoSubmitReq.getPartnerPlatform());
        submitResqDTO.setSmsTypeCode(smsInfoSubmitReq.getSmsTypeCode());
        submitResqDTO.setRecvSendTime(smsInfoSubmitReq.getRecvSendTime());
        submitResqDTO.setReqSrc(smsInfoSubmitReq.getReqSrc());
        submitResqDTO.setGatewayUserName(smsInfoSubmitReq.getGatewayUserName());
        submitResqDTO.setRequestId(smsInfoSubmitReq.getRequestId());
        // 设置订单分表后缀
        submitResqDTO.setTableNameSuffix(smsInfoSubmitReq.getTableNameSuffix());
        log.info("发送状态mq; mq:{}, message:{}, submitRequestCacheKey:{}", spectreSubmitResq, submitResqDTO, submitRequestCacheKey);

        if (mqSubmitDelay != 0L) {
            mqTemplate.createMessage(spectreSubmitResq, JSONObject.toJSONString(submitResqDTO))
                    .key(submitResqDTO.getOrderId().toString())
                    .deliverAfter(mqSubmitDelay, TimeUnit.MILLISECONDS)
                    .sendAsync();
        } else {
            mqTemplate.createMessage(spectreSubmitResq, JSONObject.toJSONString(submitResqDTO))
                    .key(submitResqDTO.getOrderId().toString())
                    .sendAsync();
        }

    }

    /**
     * 注意deliver事件消息比submit_resp事件消息先回来的情况
     *
     * @param message
     */
    @Async
    public void deliverHandle(DeliverRequestMessage message, CmppClientEntity clientEntity) {
//        log.info("threadName:{}", Thread.currentThread().getName());
        if (message.getReportMessage() == null) {
            log.info("收到非状态报告,message:{}", message);
            return;
        }
        deliverHandleV2(message, clientEntity);
    }

    private void deliverHandleV2(DeliverRequestMessage message, CmppClientEntity clientEntity) {
        String msgId = message.getReportMessage().getMsgId().toString();
        SmsInfo smsInfo = new SmsInfo();
        smsInfo.setSubMsgId(msgId);
        smsInfo.setReportStatus(message.getReportMessage().getStat());
        smsInfo.setReportTime(message.getReportMessage().getDoneTime());
        smsInfo.setPartnerPlatform(clientEntity.getPlatform());
        smsInfo.setRecordStatus(CmppBusinessConstants.DELIVER_STATUS);
        log.info("deliverHandleV2 smsInfo:{}", smsInfo);
        jsonRedisTemplate.opsForList().rightPush(CmppBusinessConstants.CMPP_ALL_RECORD_KEY, smsInfo);
    }

    @Async
    public void deliverHandleV3(DeliverRequestMessage message, CmppClientEntity clientEntity) {
        DeliverRequestMessageWrapper wrapper = DeliverRequestMessageWrapper.builder().build();
        BeanUtils.copyProperties(message, wrapper);
        wrapper.setClientEntity(clientEntity);
        try {
            jsonRedisTemplate.opsForList().rightPush(CmppBusinessConstants.CMPP_DELIVER_REQUEST_KEY, wrapper);
        } catch (RejectedExecutionException rje) {
            log.warn("redis线程断开; msg={}", rje.getMessage());
        } catch (Exception e) {
            log.warn("redis异常; msg={}", e.getMessage());
        }
    }
}
