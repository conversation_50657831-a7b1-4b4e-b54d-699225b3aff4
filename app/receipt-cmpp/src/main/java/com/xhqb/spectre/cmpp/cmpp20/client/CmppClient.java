package com.xhqb.spectre.cmpp.cmpp20.client;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.cmpp.cmpp20.client.bussiness.CmppClientBusinessHandler;
import com.xhqb.spectre.cmpp.cmpp20.client.bussiness.SmsMsg;
import com.xhqb.spectre.cmpp.cmpp20.client.handler.ActiveTestClientHandler;
import com.xhqb.spectre.cmpp.cmpp20.client.handler.CmppChannelLuciferHandler;
import com.xhqb.spectre.cmpp.cmpp20.client.handler.CmppClientHandler;
import com.xhqb.spectre.cmpp.cmpp20.client.handler.CodecChannelHandler;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.SubmitRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.reactive.BlockingWritableMsgHolder;
import com.xhqb.spectre.cmpp.cmpp20.util.SequenceUtil;
import com.xhqb.spectre.cmpp.lucifer.handler.CmppLuciferHandler;
import com.xhqb.spectre.cmpp.service.constant.BaseResultEnum;
import com.xhqb.spectre.cmpp.service.exception.ChannelException;
import com.xhqb.spectre.cmpp.service.service.cmppclienthandler.XhCmppClientBussinessHandler;
import com.xhqb.spectre.cmpp.util.OminiUtil;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.util.CharsetUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static com.xhqb.spectre.cmpp.constant.CmppBusinessConstants.CMPP_BLOCKING_WRITABLE_MESSAGE_KEY;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/9 10:33
 */
@ToString(callSuper = true)
@Getter
@Setter
@Slf4j
@Component
public class CmppClient implements Serializable {

    public static final String CMPP_CLIENT_ENTITY_KEY = "cmppClientEntityKey";
    private static final long serialVersionUID = 337051931356368266L;
    public static final int MSX_MSG_LENGTH = 140;

    /**
     * cmpp 链路检测时间(心跳检测时间,单位秒)
     */
    @Value("${spectre.receiptcmpp.cmppActiveTestIdleTimeSecond:10}")
    private Integer cmppActiveTestIdleTimeSecond;
    /**
     * cmpp 链路检测 日志打印控制开关  true->开启 false->关闭
     */
    @Value("${spectre.receiptcmpp.cmppActiveTestLogEnable:false}")
    private Boolean cmppActiveTestLogEnable;

    @Autowired
    private CmppClientBusinessHandler businessHandler;

    private EventLoopGroup workerGroup = new NioEventLoopGroup();

    private Random random = new Random();

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ChannelManager channelManager;

    @Autowired
    private OminiUtil ominiUtil;
    /**
     * 埋点处理
     */
    @Autowired
    private CmppLuciferHandler cmppLuciferHandler;

    public static byte[] getMsgBytes(byte[] msg, int start, int end) {
        byte[] msgByte = new byte[end - start];
        int j = 0;
        for (int i = start; i < end; i++) {
            msgByte[j] = msg[i];
            j++;
        }
        return msgByte;
    }

    public Map<String, CmppClientEntity> getCmppClientEntityMap() {
        return channelManager.getCmppClientEntityMap();
    }

    public ConcurrentHashMap<String, Channel> getChannelMap() {
        return channelManager.getChannelMap();
    }

    public ChannelManager getChannelManager() {
        return channelManager;
    }

    /**
     * 初始化Bootstrap
     */
    @PostConstruct
    private void init() {
        // 配置客户端的NIO线程组
        channelManager.bootstrap.group(workerGroup)
            .channel(NioSocketChannel.class)
//                .option(ChannelOption.TCP_NODELAY, true)
//                .option(ChannelOption.SO_RCVBUF, 2048)
//                .option(ChannelOption.SO_SNDBUF, 2048)
//                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
//                .option(ChannelOption.RCVBUF_ALLOCATOR, new FixedRecvByteBufAllocator(1024))
            .handler(initChannel());
    }

    /**
     * 初始化pipeline
     *
     * @return
     */
    private ChannelInitializer<?> initChannel() {
        return new ChannelInitializer<SocketChannel>() {
            @Override
            protected void initChannel(SocketChannel ch) throws Exception {
//                ch.pipeline().addLast(new LoggingHandler(LogLevel.INFO));
                ch.pipeline().addLast(new IdleStateHandler(0, cmppActiveTestIdleTimeSecond, 0, TimeUnit.SECONDS));
                ch.pipeline().addLast(CodecChannelHandler.handlerName(), new CodecChannelHandler());
                // cmpp埋点处理器
                ch.pipeline().addLast(new CmppChannelLuciferHandler(CmppClient.this));
                ch.pipeline().addLast(new ActiveTestClientHandler(CmppClient.this));
                ch.pipeline().addLast(new CmppClientHandler(businessHandler, ominiUtil));
            }
        };
    }

    /**
     * 建立链接
     */
    public void connect() {
        for (Map.Entry<String, CmppClientEntity> entry : getCmppClientEntityMap().entrySet()) {
            channelManager.connect(entry.getKey(), entry.getValue());
        }
    }

    public boolean submit(SmsMsg smsMsg) {
        CmppClientEntity cmppClientEntity = ChannelManager.cmppClientEntityMap.get(smsMsg.getChannelId());
        if (cmppClientEntity == null) {
            log.error("cmppClientEntity is null, channelId: {}", smsMsg.getChannelId());
            // channelAccountId 有误
            throw new ChannelException(BaseResultEnum.ILLEGAL_ARGUMENT);
        }
        SubmitRequestMessage submitMessage = assemblySubmitRequestMessage(cmppClientEntity, smsMsg);
        return submit(submitMessage, smsMsg, cmppClientEntity.getPlatform());
    }

    private boolean submit(SubmitRequestMessage submitRequestMessage, SmsMsg smsMsg, String platform) {
        Channel channel = getChannelMap().get(smsMsg.getChannelId());
        // 校验异常
        // compareAliveTime(Integer.valueOf(smsMsg.getChannelId()));
        if (channel == null) {
            log.error("channel is null, channelId: {}", smsMsg.getChannelId());
            // channelAccountId 有误
            throw new ChannelException(BaseResultEnum.ILLEGAL_ARGUMENT);
        }
        if (isActive(channel)) {
            if (submitRequestMessage.getMsgLength() <= MSX_MSG_LENGTH) {
                // 先业务处理,再写channel,保证businessHandler.submitHandle()先执行,这样能减少一些后续业务异步处理的复杂度
                businessHandler.submitHandle(submitRequestMessage, smsMsg);
                // 写入到channel
                channel.writeAndFlush(submitRequestMessage);
            } else {
                // 长短信拆分
                List<SubmitRequestMessage> submitRequestMessageList = splitLongMessage(submitRequestMessage);
                for (SubmitRequestMessage message : submitRequestMessageList) {
                    // 先业务处理,再写channel
                    businessHandler.submitHandle(message, smsMsg);
                    // 写入到channel
                    channel.writeAndFlush(message);
                }
            }
            return true;
        } else {
            String jsonString = JSONObject.toJSONString(BlockingWritableMsgHolder.builder()
                .platform(platform).requestMessage(submitRequestMessage)
                .smsMsg(smsMsg).build());
            log.info("jsonString {}", jsonString);
            redisTemplate.opsForList().rightPush(CMPP_BLOCKING_WRITABLE_MESSAGE_KEY, jsonString);

        }
        return false;
    }

    private void compareAliveTime(Integer channelAccountId) {
        long start = System.currentTimeMillis();
        long lastAliveTime = ((XhCmppClientBussinessHandler) businessHandler).getAliveMap().getOrDefault(channelAccountId, 0l);
        if (start - lastAliveTime > 60000) {
            // 离健康状态超过60s 报错
            throw new com.xhqb.spectre.cmpp.service.exception.ChannelException(BaseResultEnum.SERVER_ERROR);
        }
    }

    public static boolean isActive(Channel channel) {
        if (channel == null) {
            log.info("channel is null");
            return false;
        }
        if (!channel.isOpen() || !channel.isActive() || !channel.isWritable()) {
            //channel没开 或 没激活
            log.info("channel is not active; isOpen:{}, isActive:{}, isWritable:{}",
                channel.isOpen(), channel.isActive(), channel.isWritable());
            return false;
        }
        return true;
    }

    private SubmitRequestMessage assemblySubmitRequestMessage(CmppClientEntity cmppClientEntity, SmsMsg smsMsg) {
        SubmitRequestMessage submitMessage = new SubmitRequestMessage();
        submitMessage.setDestterminalId(new String[]{smsMsg.getMobile()});
        submitMessage.setDestUsrTl((byte) submitMessage.getDestterminalId().length);
        if (cmppClientEntity.getServiceId() != null && cmppClientEntity.getServiceId().length() > 0) {
            submitMessage.setServiceId(cmppClientEntity.getServiceId());
        }
        if (cmppClientEntity.getSrcId() != null && cmppClientEntity.getSrcId().length() > 0) {
            submitMessage.setSrcId(cmppClientEntity.getSrcId());
        }
        submitMessage.setMsgSrc(cmppClientEntity.getMsgSrc());
        submitMessage.setMsgContentText(smsMsg.getContent());
        submitMessage.setMsgContent(smsMsg.getContent().getBytes(CharsetUtil.UTF_16BE));
        submitMessage.setMsgLength(submitMessage.getMsgContent().length);
        return submitMessage;
    }

    /**
     * 根据GSM协议:
     * PID一般文本的时候设0
     * UDHI 设为0的时候表示短消息内容中不包含头部信息;设为1的时候表示短消息内容中包含头部信息
     * 如果是长短信拆分,是需要设置头部信息的
     *
     * @param submit
     * @return
     */
    public List<SubmitRequestMessage> splitLongMessage(SubmitRequestMessage submit) {
        byte[] msgContent = submit.getMsgContent();
        int msgLength = submit.getMsgLength();
        int maxLength = 140;
        int msgSendCount = msgLength % (maxLength - 6) == 0 ? msgLength / (maxLength - 6)
            : msgLength / (maxLength - 6) + 1;
        List<SubmitRequestMessage> messageList = new ArrayList<>(msgSendCount);
        //短信息内容头拼接
        byte[] msgHead = new byte[6];
        // 为了随机填充msgHead[3]
        random.nextBytes(msgHead);
        msgHead[0] = 0x05;
        msgHead[1] = 0x00;
        msgHead[2] = 0x03;
        msgHead[4] = (byte) msgSendCount;
        msgHead[5] = 0x01;

        for (int i = 0; i < msgSendCount; i++) {
            msgHead[5] = (byte) (i + 1);
            byte[] needMsg = null;
            //消息头+消息内容拆分
            if (i != msgSendCount - 1) {
                int start = (maxLength - 6) * i;
                int end = (maxLength - 6) * (i + 1);
                needMsg = getMsgBytes(msgContent, start, end);
            } else {
                int start = (maxLength - 6) * i;
                int end = msgLength;
                needMsg = getMsgBytes(msgContent, start, end);
            }
            int subLength = needMsg.length + msgHead.length;
            byte[] sendMsg = new byte[subLength];
            System.arraycopy(msgHead, 0, sendMsg, 0, 6);
            System.arraycopy(needMsg, 0, sendMsg, 6, needMsg.length);

            SubmitRequestMessage subMessage = new SubmitRequestMessage(SequenceUtil.getSequenceNo());
            subMessage.setPktotal((byte) msgSendCount);
            subMessage.setPknumber((byte) (i + 1));
            subMessage.setServiceId(submit.getServiceId());
            subMessage.setTpudhi((byte) 0x01);
            subMessage.setMsgSrc(submit.getMsgSrc());
            subMessage.setSrcId(submit.getSrcId());
            subMessage.setDestUsrTl(submit.getDestUsrTl());
            subMessage.setDestterminalId(submit.getDestterminalId());
            subMessage.setMsgLength(subLength);
            subMessage.setMsgContent(sendMsg);
            messageList.add(subMessage);
        }
        return messageList;
    }

    /**
     * 是否开启日志打印开关
     *
     * @return true开启
     */
    public boolean isLogEnable() {
        return Objects.equals(true, cmppActiveTestLogEnable);
    }
}
