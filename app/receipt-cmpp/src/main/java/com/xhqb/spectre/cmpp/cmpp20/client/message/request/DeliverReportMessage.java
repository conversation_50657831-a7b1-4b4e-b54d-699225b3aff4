package com.xhqb.spectre.cmpp.cmpp20.client.message.request;

import com.xhqb.spectre.cmpp.cmpp20.client.message.MsgId;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description:
 * @author: xiaoxia<PERSON>iang
 * @date: 2020/5/7 17:28
 */
@ToString
@Getter
@Setter
public class DeliverReportMessage implements Serializable {

    private static final long serialVersionUID = -1174867365444739638L;

    private MsgId msgId; //﻿信息标识 ﻿SP 提交短信（ CMPP_SUBMIT）操作时，与 SP相连的 ISMG 产生的 Msg_Id。

    private String stat = ""; //﻿发送短信的应答结

    private String submitTime = ""; //﻿YYMMDDHHMM

    private String doneTime = ""; //﻿YYMMDDHHMM

    private String destTerminalId = ""; //﻿目的终端 MSISDN 号码

    private long smscSequence = 0; //﻿取自 SMSC 发送状态报告的消息体中的消息标识

}
