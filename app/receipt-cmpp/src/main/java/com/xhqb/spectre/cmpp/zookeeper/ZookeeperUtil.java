package com.xhqb.spectre.cmpp.zookeeper;

import com.alibaba.fastjson.JSONObject;
import org.I0Itec.zkclient.ZkClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ZookeeperUtil {

    @Autowired
    private ZkClient zkClient;

    public void insertZkNode(final String path, final Object data) {
        createZkNode(path);
        zkClient.writeData(path, null == data ? "" : JSONObject.toJSONString(data));
    }

    public void createZkNode(final String path) {
        if (!zkClient.exists(path)) {
            zkClient.createPersistent(path, true);
        }
    }

    public void deleteZkPath(final String path) {
        if (zkClient.exists(path)) {
            zkClient.delete(path);
        }
    }

    public void deleteZkPathRecursive(final String path) {
        if (zkClient.exists(path)) {
            zkClient.deleteRecursive(path);
        }
    }

}
