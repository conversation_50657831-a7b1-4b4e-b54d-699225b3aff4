package com.xhqb.spectre.cmpp.lucifer.context;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;

/**
 * 埋点上下文信息
 *
 * <AUTHOR>
 * @date 2021/10/14
 */
public class ApiLuciferContext implements Serializable {

    /**
     * api http request
     */
    private HttpServletRequest request;
    /**
     * api http response
     */
    private HttpServletResponse response;

    /**
     * 开始执行时间
     */
    private Long startNanoTime;
    /**
     * 标签  mappingUri, status, labels
     */
    private String[] labels;
    /**
     * 内容大小
     */
    private Long contentSize;
    /**
     * 接口名称
     */
    private String apiName;

    public ApiLuciferContext() {
        this.startNanoTime = System.nanoTime();
    }

    public ApiLuciferContext(String... labels) {
        this();
        this.labels = labels;
    }

    public Long getStartNanoTime() {
        return startNanoTime;
    }

    public String[] getLabels() {
        return labels;
    }

    public ApiLuciferContext setLabels(String... labels) {
        this.labels = labels;
        return this;
    }

    public Long getContentSize() {
        return contentSize;
    }

    public ApiLuciferContext setContentSize(Long contentSize) {
        this.contentSize = contentSize;
        return this;
    }

    public HttpServletRequest getRequest() {
        return request;
    }

    public ApiLuciferContext setRequest(HttpServletRequest request) {
        this.request = request;
        return this;
    }

    public HttpServletResponse getResponse() {
        return response;
    }

    public ApiLuciferContext setResponse(HttpServletResponse response) {
        this.response = response;
        return this;
    }

    public String getApiName() {
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }
}
