//package com.xhqb.sms.channel.biz.com.xhqb.spectre.httpreceipt.service.config;
//
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Configuration;
//
///**
// * <AUTHOR>
// * @date 2020-05-13
// */
//@Configuration
//@ConfigurationProperties(prefix="sms.channel.tencent.cmpp20")
//public class TencentCmppSMSConfig {
//
//    private String host;
//    private int port;
//    private String account;
//    private String password;
//
//    public String getHost() {
//        return host;
//    }
//
//    public void setHost(String host) {
//        this.host = host;
//    }
//
//    public int getPort() {
//        return port;
//    }
//
//    public void setPort(int port) {
//        this.port = port;
//    }
//
//    public String getAccount() {
//        return account;
//    }
//
//    public void setAccount(String account) {
//        this.account = account;
//    }
//
//    public String getPassword() {
//        return password;
//    }
//
//    public void setPassword(String password) {
//        this.password = password;
//    }
//
////    @Bean
////    public NettyClient nettyClient() {
////        NettyClient client = new NettyClient(host, port, account, password);
//////        client.start();
////        return client;
////    }
//}
//
