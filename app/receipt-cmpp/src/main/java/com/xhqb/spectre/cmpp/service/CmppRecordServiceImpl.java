package com.xhqb.spectre.cmpp.service;

import com.xhqb.spectre.cmpp.dto.SmsInfo;
import com.xhqb.spectre.common.dal.mapper.CmppRecordMapper;
import com.xhqb.spectre.common.dal.entity.CmppRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * CmppRecordService 内部使用 不提供dubbo调用
 * @author: xiaoxiaoxiang
 * @date: 2020/12/16 10:24
 */
@Slf4j
@Service
public class CmppRecordServiceImpl implements CmppRecordService {

    @Autowired
    private CmppRecordServiceHelper cmppRecordServiceHelper;

    @Autowired
    private CmppRecordMapper cmppRecordMapper;

    @Override
    public List<CmppRecord> findSubmitCoupledByMsgIds(List<String> msgIds) {
        if (msgIds == null || msgIds.isEmpty()) {
            return null;
        }
        log.info("findSubmitCoupledByMsgIds msgIds:{}", msgIds);
        List<CmppRecord> cmppRecordList = cmppRecordMapper.findSubmitCoupledByMsgIds(msgIds);
        log.info("findSubmitCoupledByMsgIds cmppRecordList:{}", cmppRecordList);
        return cmppRecordList;
    }

//    @Transactional(rollbackFor = Exception.class)
    @Override
    public void modifyDeliverInfo(CmppRecord cmppRecord) {
        cmppRecordMapper.updateDeliverInfoById(cmppRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchInsert(List<SmsInfo> smsInfoList) {
        if (smsInfoList != null && !smsInfoList.isEmpty()) {
            List<CmppRecord> cmppRecordList = new ArrayList<>(smsInfoList.size());
            smsInfoList.forEach(smsInfo -> {
                CmppRecord cmppRecord = cmppRecordServiceHelper.smsInfoToCmppRecord(smsInfo);
                cmppRecordList.add(cmppRecord);
            });
            cmppRecordMapper.batchInsert(cmppRecordList);
        }
    }
}
