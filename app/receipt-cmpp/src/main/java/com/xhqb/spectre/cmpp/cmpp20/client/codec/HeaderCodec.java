package com.xhqb.spectre.cmpp.cmpp20.client.codec;

import com.xhqb.spectre.cmpp.cmpp20.client.message.DefaultMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.Message;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.DefaultHeader;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.Header;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.header.PacketHeaderStructure;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageCodec;

import java.util.List;

/**
 * header 编码处理
 *
 * @author: xiaoxiaoxiang
 * @date: 2020/5/15 17:40
 */
public class HeaderCodec extends MessageToMessageCodec<ByteBuf, Message> {

    public static String handlerName() {
        return "cmppHeaderCodec";
    }

    @Override
    protected void encode(ChannelHandlerContext ctx, Message msg, List<Object> out) throws Exception {
        int headerLength = PacketHeaderStructure.COMMANDID.getHeaderLength();
        // total = headerLength + bodyLength
        int totalLength = msg.getBodyBuffer().length + headerLength;

        ByteBuf buf = ctx.alloc().buffer(totalLength);
        buf.writeInt(totalLength);
        buf.writeInt(msg.getHeader().getCommandId());
        buf.writeInt(msg.getHeader().getSequenceId());
        if (totalLength > headerLength) {
            buf.writeBytes(msg.getBodyBuffer());
        }
        out.add(buf);
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf msg, List<Object> out) throws Exception {
        Header header = new DefaultHeader();
        header.setTotalLength(msg.readInt());
        header.setCommandId(msg.readInt());
        header.setSequenceId(msg.readInt());
        header.setHeaderLength(PacketHeaderStructure.COMMANDID.getHeaderLength());
        header.setBodyLength(header.getTotalLength() - header.getHeaderLength());

        Message message = new DefaultMessage();
        message.setHeader(header);

        if (header.getBodyLength() > 0) {
            message.setBodyBuffer(new byte[header.getBodyLength()]);
            assert (header.getBodyLength() == msg.readableBytes());
            msg.readBytes(message.getBodyBuffer());
        } else {
            message.setBodyBuffer(new byte[0]);
        }
        out.add(message);
    }
}
