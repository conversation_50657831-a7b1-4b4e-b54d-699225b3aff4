package com.xhqb.spectre.cmpp.config;

import com.xhqb.spectre.cmpp.cmpp20.client.reactive.BlockingWritableMsgSubscriber;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import static com.xhqb.spectre.cmpp.constant.CmppBusinessConstants.CMPP_BLOCKING_WRITABLE_MESSAGE_KEY;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/6/10 16:58
 */
@Configuration
public class RedisConfig {

    @Bean(name = "smsInfoJsonRedisTemplate")
    @ConditionalOnMissingBean(name = "smsInfoJsonRedisTemplate")
    public RedisTemplate<String, ?> smsInfoJsonRedisTemplate(
            RedisConnectionFactory redisConnectionFactory, StringRedisSerializer stringRedisSerializer) {
        RedisTemplate<String, ?> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        GenericJackson2JsonRedisSerializer jackson2JsonRedisSerializer = new GenericJackson2JsonRedisSerializer();
        template.setKeySerializer(stringRedisSerializer);
        // 设置值（value）的序列化采用FastJsonRedisSerializer。
        template.setValueSerializer(jackson2JsonRedisSerializer);
        return template;
    }

}
