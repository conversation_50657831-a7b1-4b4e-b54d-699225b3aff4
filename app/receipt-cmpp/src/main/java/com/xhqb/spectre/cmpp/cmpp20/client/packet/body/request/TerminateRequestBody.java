package com.xhqb.spectre.cmpp.cmpp20.client.packet.body.request;

import com.xhqb.spectre.cmpp.cmpp20.client.packet.DataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.PacketBody;

public enum TerminateRequestBody implements PacketBody {
    ;
    private DataType dataType;
    private boolean isFixFiledLength;
    private int length;

    private TerminateRequestBody(DataType dataType, boolean isFixFiledLength, int length) {
        this.dataType = dataType;
        this.isFixFiledLength = isFixFiledLength;
        this.length = length;
    }
    @Override
    public DataType getDataType() {
        return dataType;
    }

    @Override
    public int getFieldLength() {
        return length;
    }

    @Override
    public int getBodyLength() {

        return 0;
    }
}