package com.xhqb.spectre.cmpp.service;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.kael.sequencegenerator.DistributedSequence;
import com.xhqb.spectre.cmpp.dto.SmsInfo;
import com.xhqb.spectre.common.dal.dto.mq.CmppDeliverResqDTO;
import com.xhqb.spectre.common.dal.dto.mq.DeliverResqDTO;
import com.xhqb.spectre.common.dal.entity.CmppRecord;
import com.xhqb.spectre.common.enums.ProtocolTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.aws.messaging.core.QueueMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/12/16 13:55
 */
@Slf4j
@Service
public class CmppRecordServiceHelper implements CmppRecordHelper {

    @Autowired
    private DistributedSequence distributedSequence;

    @Autowired
    private QueueMessagingTemplate messagingTemplate;

    @Autowired
    private MQTemplate<String> mqTemplate;

    //spectre-deliver-resq
    @Value("#{'${kael.mq.producers:}'.split(',')[0]}")
    private String spectreDeliverResq;

    //spectre-submit-resq
    @Value("#{'${kael.mq.producers:}'.split(',')[1]}")
    private String spectreSubmitResq;

    @Value("${spectre.mq.deliver.delay:2}")
    private Long mqDeliverDelay;

    /**
     * 回执消息发Q
     *
     * @param smsInfoList
     */
//    @Async("sqsThreadPool")
    @Override
    public void sendSmsReceiptList(List<SmsInfo> smsInfoList) {
        if (smsInfoList == null || smsInfoList.isEmpty()) {
            return;
        }
        smsInfoList.forEach(this::sendSmsReceipt);
    }

    /**
     * 回执消息发Q
     *
     * @param cmppRecordList
     */
    @Async("sqsThreadPool")
    @Override
    public void sendSmsReceiptListV2(List<CmppRecord> cmppRecordList) {
        if (cmppRecordList == null || cmppRecordList.isEmpty()) {
            return;
        }
        cmppRecordList.forEach(this::sendSmsReceiptV2);
    }

    public void sendSmsReceipt(SmsInfo smsInfo) {
        CmppDeliverResqDTO cmppDeliverResqDTO = CmppDeliverResqDTO
                .builder()
                .orderId(smsInfo.getOrderId())
                .channelAccountId(Integer.valueOf(smsInfo.getChannelId()))
                .channelCode(smsInfo.getPartnerPlatform())
                .destTerminalId(smsInfo.getDestTerminalId())
                .doneTime(smsInfo.getReportTime())
                .channelMsgId(smsInfo.getSubMsgId())
                .recvReportTime(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()))
                .reportStatus(smsInfo.getReportStatus())
                .mobile(smsInfo.getMobile())
                .submitTime(smsInfo.getSubmitTime())
                .addition(smsInfo.getAddition())
                .tplCode(smsInfo.getTplCode())
                .resend(smsInfo.getResend())
                .smsTypeCode(smsInfo.getSmsTypeCode())
                .recvSendTime(smsInfo.getRecvSendTime())
                .reqSrc(smsInfo.getReqSrc())
                .gatewayUserName(smsInfo.getGatewayUserName())
                .requestId(smsInfo.getRequestId())
                .tableNameSuffix(smsInfo.getTableNameSuffix())
                .build();

        DeliverResqDTO deliverResqDTO = DeliverResqDTO.builder()
                .type(ProtocolTypeEnum.CMPP.getName())
                .cmppDeliverResqDTO(cmppDeliverResqDTO)
                .build();

        log.info("发送回执mq; mq:{}, message:{}", spectreDeliverResq, deliverResqDTO);
        if (mqDeliverDelay != 0L) {
            mqTemplate.createMessage(spectreDeliverResq, JSONObject.toJSONString(deliverResqDTO))
                    .property("blockIfQueueFull", "1")
                    .key(deliverResqDTO.getCmppDeliverResqDTO().getOrderId().toString())
                    .deliverAfter(mqDeliverDelay, TimeUnit.MILLISECONDS)
                    .sendAsync();
        } else {
            mqTemplate.createMessage(spectreDeliverResq, JSONObject.toJSONString(deliverResqDTO))
                    .property("blockIfQueueFull", "1")
                    .key(deliverResqDTO.getCmppDeliverResqDTO().getOrderId().toString())
                    .sendAsync();
        }
    }

    private void sendSmsReceiptV2(CmppRecord cmppRecord) {
        log.info("sendSmsReceiptV2 curThread:{},send q cmppRecord: {}", Thread.currentThread().getName(), cmppRecord);
    }

    /**
     * 类型转化
     * 以后可以尝试使用MapStruct
     *
     * @param smsInfo
     * @return
     */
    public CmppRecord smsInfoToCmppRecord(SmsInfo smsInfo) {
        CmppRecord cmppRecord = new CmppRecord();
        cmppRecord.init(distributedSequence.nextStr(cmppRecord.getSeqName()));
        cmppRecord.setOrderId(smsInfo.getOrderId());
        cmppRecord.setMobilePhone(smsInfo.getMobile());
        cmppRecord.setPkNumber(smsInfo.getPkNumber());
        cmppRecord.setPkTotal(smsInfo.getPkTotal());
        cmppRecord.setSubmitSeqId(smsInfo.getSubmitSeqId());
        cmppRecord.setPartnerPlatform(smsInfo.getPartnerPlatform());
        cmppRecord.setPlatformStatus(smsInfo.getPlatformStatus());
        cmppRecord.setPlatformTime(smsInfo.getPlatformTime());
        cmppRecord.setReportMsgId(smsInfo.getSubMsgId());
        cmppRecord.setReportStatus(smsInfo.getReportStatus());
        cmppRecord.setReportTime(smsInfo.getReportTime());
        cmppRecord.setRecordStatus(smsInfo.getRecordStatus());
        cmppRecord.setAddition(smsInfo.getAddition());
        return cmppRecord;
    }
}
