package com.xhqb.spectre.cmpp.cmpp20.client.deserializer;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;

import java.lang.reflect.Type;

public class CustomerPacketTypeDeserializer implements ObjectDeserializer {

    @Override
    public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        String value = parser.parseObject(String.class);
        // DataType 数据类型 枚举
        if (PacketType.class.isAssignableFrom((Class) type)) {
            return (T) CmppPacketType.valueOf(value);
        }
        // Charset 字符编码 枚举
//            else if (DocumentProperties.Charset.class.isAssignableFrom((Class) type)) {
//                return (T) Charset.from(value);
//            }
        return null;
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }


}
