package com.xhqb.spectre.cmpp.service.constant;

/**
 * 接口错误码
 *
 * <AUTHOR>
 * @date 2020-05-14
 */
public enum BaseResultEnum {

    SUCCESS(true, 1),
    FAIL(false, 0),
    ILLEGAL_ARGUMENT(false, -1),
    SEND_TOO_FREQUENTLY(false, -2000),
    SERVER_ERROR(false, -9999);

    boolean success;
    int code;

    BaseResultEnum(boolean success, int code) {
        this.success = success;
        this.code = code;
    }

    public boolean isSuccess() {
        return success;
    }

    public int getCode() {
        return code;
    }

}
