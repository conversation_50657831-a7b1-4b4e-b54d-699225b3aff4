package com.xhqb.spectre.cmpp.service.controller;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.cmpp.constant.CmppBusinessConstants;
import com.xhqb.spectre.cmpp.dto.SmsInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 缓存数据排查-查询用
 */
@Slf4j
@RestController
@RequestMapping("/query")
public class QueryController {

    @Autowired
    private RedisTemplate<String, Object> jsonRedisTemplate;

    @GetMapping(value = "/length")
    public String allLength() {
        Long allRecordSize = jsonRedisTemplate.opsForList().size(CmppBusinessConstants.CMPP_ALL_RECORD_KEY);
        Long deliverRecordSize = jsonRedisTemplate.opsForList().size(CmppBusinessConstants.CMPP_DELIVER_RECORD_KEY);
        Long uncoupledRecordSize = jsonRedisTemplate.opsForList().size(CmppBusinessConstants.CMPP_UNCOUPLED_RECORD_KEY);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("allRecordSize", allRecordSize);
        jsonObject.put("deliverRecordSize", deliverRecordSize);
        jsonObject.put("uncoupledRecordSize", uncoupledRecordSize);
        return jsonObject.toJSONString();
    }

    @GetMapping(value = "/deliverRecord/{msgId}")
    public String deliverRecord(@PathVariable(value = "msgId") String msgId) {
        if (msgId == null) {
            return null;
        }
        Long allRecordSize = jsonRedisTemplate.opsForList().size(CmppBusinessConstants.CMPP_DELIVER_RECORD_KEY);
        if (allRecordSize == null) {
            allRecordSize = 0L;
        }
        SmsInfo result = null;
        boolean flag = false;
        for (long i = 0; i < allRecordSize; i += 1000L) {
            List<Object> smsInfoList = jsonRedisTemplate.opsForList().range(CmppBusinessConstants.CMPP_DELIVER_RECORD_KEY, i, i + 1000);
            for (Object obj : smsInfoList) {
                SmsInfo smsInfo = (SmsInfo) obj;
                if (msgId.equals(smsInfo.getSubMsgId())) {
                    result = smsInfo;
                    smsInfo.setAddition(String.valueOf(i));
                    flag = true;
                    break;
                }
            }
            if (flag) {
                break;
            }
        }
        if (result == null) {
            return null;
        } else {
            return result.toString();
        }
    }

    @PostMapping(value = "/record", headers = {"content-type=application/json"})
    public String pushRecord(@RequestBody List<SmsInfo> smsInfoList) {
        if (smsInfoList != null && !smsInfoList.isEmpty()) {
            for (SmsInfo smsInfo : smsInfoList) {
                log.info("pushRecord smsInfo:{}", smsInfo);
                jsonRedisTemplate.opsForList().rightPush(CmppBusinessConstants.CMPP_ALL_RECORD_KEY, smsInfo);
            }
            return "success";
        } else {
            return "failed";
        }
    }
}
