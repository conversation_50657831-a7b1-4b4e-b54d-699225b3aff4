package com.xhqb.spectre.cmpp.cmpp20.client.message.request;

import com.xhqb.spectre.cmpp.cmpp20.client.message.DefaultMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.Header;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;

public class TerminateRequestMessage  extends DefaultMessage {
    private static final long serialVersionUID = 814288661389104951L;

    public TerminateRequestMessage(Header header) {
        super(CmppPacketType.CMPPTERMINATEREQUEST, header);
    }
    public TerminateRequestMessage() {
        super(CmppPacketType.CMPPTERMINATEREQUEST);
    }
    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return String.format("CmppTerminateRequestMessage [toString()=%s]",
                super.toString());
    }

}

