package com.xhqb.spectre.cmpp.service.service.impl;

import com.xhqb.spectre.cmpp.cmpp20.client.CmppClient;
import com.xhqb.spectre.cmpp.cmpp20.client.bussiness.SmsMsg;
import com.xhqb.spectre.cmpp.service.constant.Channels;
import com.xhqb.spectre.cmpp.service.model.dto.MessageDto;
import com.xhqb.spectre.cmpp.service.model.enums.ResultEnum;
import com.xhqb.spectre.cmpp.service.model.result.MessageSendResult;
import com.xhqb.spectre.cmpp.service.service.SmsChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @date 2020-05-14
 */
@Service(Channels.TENCENT_CMPP_SMS)
public class TencentCmppChannelServiceImpl implements SmsChannelService {

    @Autowired
    private CmppClient cmppClient;

    @Override
    public MessageSendResult send(MessageDto message) {
        SmsMsg smsMsg = SmsMsg.builder()
                .channelId(message.getChannelId())
                .addition(message.getAddition())
                .mobile(message.getMobile())
                .content(message.getContent())
                .tplCode(message.getTplCode())
                .channelCode(message.getChannelCode())
                .resend(message.getResend())
                .smsTypeCode(message.getSmsTypeCode())
                .reqSrc(message.getReqSrc())
                .requestId(message.getRequestId())
                .gatewayUserName(message.getGatewayUserName())
                .tableNameSuffix(message.getTableNameSuffix())
                .build();

        boolean result = cmppClient.submit(smsMsg);
        // TODO 重试
        if (result) {
            return new MessageSendResult();
        }
        return new MessageSendResult(false, ResultEnum.EXCEPTION);
    }

    @Override
    public String getChannel() {
        return Channels.TENCENT_CMPP_SMS;
    }
}
