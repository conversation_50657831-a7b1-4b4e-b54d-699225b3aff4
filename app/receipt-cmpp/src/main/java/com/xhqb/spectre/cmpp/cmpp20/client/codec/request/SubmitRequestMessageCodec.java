package com.xhqb.spectre.cmpp.cmpp20.client.codec.request;

import com.xhqb.spectre.cmpp.cmpp20.client.message.Message;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.SubmitRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.request.SubmitRequestBody;
import com.xhqb.spectre.cmpp.cmpp20.util.ByteUtil;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageCodec;
import io.netty.util.CharsetUtil;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/16 10:38
 */
@Slf4j
public class SubmitRequestMessageCodec extends MessageToMessageCodec<Message, SubmitRequestMessage> {

    private PacketType packetType;

    public SubmitRequestMessageCodec() {
        this.packetType = CmppPacketType.SUBMITREQUEST;
    }

    /**
     * client端只需要处理encode
     *
     * @param ctx
     * @param msg
     * @param out
     * @throws Exception
     */
    @Override
    protected void encode(ChannelHandlerContext ctx, SubmitRequestMessage msg, List<Object> out) throws Exception {
        // body总长度
        ByteBuf bodyBuffer = ctx.alloc().buffer(SubmitRequestBody.MSGID.getBodyLength() + msg.getMsgLength()
            + msg.getDestUsrTl() * SubmitRequestBody.DESTTERMINALID.getFieldLength());
        // 写入Msg_Id
        bodyBuffer.writeLong(0L);
        // 写入Pk_total
        bodyBuffer.writeByte(msg.getPktotal());
        // 写入Pk_number
        bodyBuffer.writeByte(msg.getPknumber());
        // 写入Registered_Delivery
        bodyBuffer.writeByte(msg.getRegisteredDelivery());
        // 写入Msg_level
        bodyBuffer.writeByte(msg.getMsgLevel());
        // 写入Service_Id
        bodyBuffer.writeBytes(ByteUtil.ensureLength(msg.getServiceId().getBytes(CharsetUtil.UTF_8),
            SubmitRequestBody.SERVICEID.getFieldLength()));
        // 写入Fee_UserType
        bodyBuffer.writeByte(msg.getFeeUserType());
        // 写入Fee_terminal_Id
        bodyBuffer.writeBytes(ByteUtil.ensureLength(msg.getFeeTerminalId().getBytes(CharsetUtil.UTF_8),
            SubmitRequestBody.FEETERMINALID.getFieldLength()));
        // 写入TP_pId
        bodyBuffer.writeByte(msg.getTppid());
        // 写入TP_udhi
        bodyBuffer.writeByte(msg.getTpudhi());
        // 写入Msg_Fmt
        bodyBuffer.writeByte(msg.getMsgFmt());
        // 写入Msg_src
        bodyBuffer.writeBytes(ByteUtil.ensureLength(msg.getMsgSrc().getBytes(CharsetUtil.UTF_8),
            SubmitRequestBody.MSGSRC.getFieldLength()));
        // 写入FeeType
        bodyBuffer.writeBytes(ByteUtil.ensureLength(msg.getFeeType().getBytes(CharsetUtil.UTF_8),
            SubmitRequestBody.FEETYPE.getFieldLength()));
        // 写入FeeCode
        bodyBuffer.writeBytes(ByteUtil.ensureLength(msg.getFeeCode().getBytes(CharsetUtil.UTF_8),
            SubmitRequestBody.FEECODE.getFieldLength()));
        // 写入ValId_Time
        bodyBuffer.writeBytes(ByteUtil.ensureLength(msg.getValIdTime().getBytes(CharsetUtil.UTF_8),
            SubmitRequestBody.VALIDTIME.getFieldLength()));
        // 写入At_Time
        bodyBuffer.writeBytes(ByteUtil.ensureLength(msg.getAtTime().getBytes(CharsetUtil.UTF_8),
            SubmitRequestBody.ATTIME.getFieldLength()));
        // 写入Src_Id
        bodyBuffer.writeBytes(ByteUtil.ensureLength(msg.getSrcId().getBytes(CharsetUtil.UTF_8),
            SubmitRequestBody.SRCID.getFieldLength()));
        // 写入DestUsr_tl
        bodyBuffer.writeByte(msg.getDestUsrTl());
        // 写入Dest_terminal_Id
        for (int i = 0; i < msg.getDestUsrTl(); i++) {
            String[] destTermId = msg.getDestterminalId();
            bodyBuffer.writeBytes(ByteUtil.ensureLength(destTermId[i].getBytes(CharsetUtil.UTF_8),
                SubmitRequestBody.DESTTERMINALID.getFieldLength()));
        }
        // 写入Msg_Length
        bodyBuffer.writeByte(msg.getMsgLength());
        // 写入Msg_Content
        bodyBuffer.writeBytes(msg.getMsgContent());
        // 写入Reserve
        bodyBuffer.writeBytes(ByteUtil.ensureLength(msg.getReserve().getBytes(CharsetUtil.UTF_8),
            SubmitRequestBody.RESERVE.getFieldLength()));

        msg.setBodyBuffer(ByteUtil.byteBufToByteArray(bodyBuffer, bodyBuffer.readableBytes()));
        msg.getHeader().setBodyLength(msg.getBodyBuffer().length);
        ReferenceCountUtil.release(bodyBuffer);
        out.add(msg);
    }


    @Override
    protected void decode(ChannelHandlerContext ctx, Message msg, List<Object> out) throws Exception {
        int commandId = msg.getHeader().getCommandId();
        if (packetType.getCommandId() != commandId) {
            //不解析，交给下一个codec
            out.add(msg);
            return;
        }
        out.add(msg);
    }
}
