package com.xhqb.spectre.cmpp.service.model.validation;

import org.hibernate.validator.constraints.Length;

import javax.validation.Constraint;
import javax.validation.Payload;
import javax.validation.ReportAsSingleViolation;
import javax.validation.constraints.NotBlank;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;


/**
 * <AUTHOR>
 * @date 2020-05-14
 */
@Documented
@Constraint(validatedBy = {})
@NotBlank
@Length(max = 200)
@Target({FIELD})
@Retention(RUNTIME)
@ReportAsSingleViolation
public @interface Content {

    String message() default "{com.xhqb.sms.constraints.Content.message}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
