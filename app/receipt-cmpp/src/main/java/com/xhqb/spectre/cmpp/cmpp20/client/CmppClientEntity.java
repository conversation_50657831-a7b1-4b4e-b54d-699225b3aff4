package com.xhqb.spectre.cmpp.cmpp20.client;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/9 10:33
 */
@ToString
@Getter
@Setter
public class CmppClientEntity implements Serializable {

    private static final long serialVersionUID = -1L;

    private Integer id;

    /**
     * 合作方(同一个合作方下不同渠道保持一致,比如营销和行业,保持一致)
     */
    private String platform;

    /**
     * 短信类型
     */
    private String smsTypeCode;

    /**
     * cmpp server host
     */
    private String serverHost;

    /**
     * cmpp server port
     */
    private int serverPort;

    /**
     * 登录用户名
     */
    private String userName;

    /**
     * 登录密码
     */
    private String password;

    /**
     * serviceId
     */
    private String serviceId;

    /**
     * srcId
     */
    private String srcId;

    /**
     * srcId
     */
    private String msgSrc;

    /**
     * init
     *
     * @param serverHost
     * @param serverPort
     * @param userName
     * @param password
     */
    public void init(String platform, String serverHost, int serverPort, String userName,
                     String password, String serviceId, String srcId, String msgSrc) {
        this.platform = platform;
        this.serverHost = serverHost;
        this.serverPort = serverPort;
        this.userName = userName;
        this.password = password;
        this.serviceId = serviceId;
        this.srcId = srcId;
        this.msgSrc = msgSrc;
    }
}
