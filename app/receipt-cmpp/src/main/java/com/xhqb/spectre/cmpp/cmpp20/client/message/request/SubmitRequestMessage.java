package com.xhqb.spectre.cmpp.cmpp20.client.message.request;

import com.xhqb.spectre.cmpp.cmpp20.client.message.DefaultMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.MsgId;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.Header;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/7 17:28
 */
@ToString(callSuper = true)
@Getter
@Setter
@Slf4j
public class SubmitRequestMessage extends DefaultMessage {

    private static final long serialVersionUID = 5088684711552360029L;

    private MsgId msgId;

    private byte pktotal = 1;

    private byte pknumber = 1;

    private byte registeredDelivery = 1;

    private byte msgLevel = 0;

    private String serviceId = "";

    private byte feeUserType = 2;

    private String feeTerminalId = "";

    private byte tppid = 0;

    private byte tpudhi = 0;

    private byte msgFmt = 8;

    private String msgSrc = "";

    private String feeType = "01";

    private String feeCode = "000000";

    private String valIdTime = "";

    private String atTime = "";

    private String srcId = "";

    private byte destUsrTl = 1;

    private String[] destterminalId;

    private int msgLength;

    private byte[] msgContent;

    private String reserve = "";

    private String msgContentText;

    public SubmitRequestMessage() {
        super(CmppPacketType.SUBMITREQUEST);
    }

    public SubmitRequestMessage(Header header) {
        super(CmppPacketType.SUBMITREQUEST, header);
    }

    public SubmitRequestMessage(int sequenceId) {
        super(CmppPacketType.SUBMITREQUEST, sequenceId);
    }
}
