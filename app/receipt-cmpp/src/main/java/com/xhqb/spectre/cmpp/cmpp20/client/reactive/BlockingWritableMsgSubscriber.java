package com.xhqb.spectre.cmpp.cmpp20.client.reactive;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.ParserConfig;
import com.xhqb.spectre.cmpp.cmpp20.client.ChannelManager;
import com.xhqb.spectre.cmpp.cmpp20.client.CmppClient;
import com.xhqb.spectre.cmpp.cmpp20.client.CmppClientEntity;
import com.xhqb.spectre.cmpp.cmpp20.client.bussiness.CmppClientBusinessHandler;
import com.xhqb.spectre.cmpp.cmpp20.client.bussiness.SmsMsg;
import com.xhqb.spectre.cmpp.cmpp20.client.deliver.CoupleDeliverRequestDaemon;
import com.xhqb.spectre.cmpp.cmpp20.client.deserializer.CustomerPacketTypeDeserializer;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.SubmitRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;
import com.xhqb.spectre.cmpp.util.OminiUtil;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.xhqb.spectre.cmpp.constant.CmppBusinessConstants.CMPP_BLOCKING_WRITABLE_MESSAGE_KEY;


@Slf4j
@Component
public class BlockingWritableMsgSubscriber {

    static {
        ParserConfig.getGlobalInstance().putDeserializer(PacketType.class, new CustomerPacketTypeDeserializer());
    }

    @Autowired
    private CmppClient cmppClient;

    @Autowired
    private CmppClientBusinessHandler businessHandler;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    OminiUtil ominiUtil;

    @Async("sqsThreadPool")
    public void onNext() {
        // redis 发布订阅太挫了，不会阻塞，多个线程立即消费，不符合业务要求
        for (; ; ) {
            if(CoupleDeliverRequestDaemon.isShutdown) {
                log.info("已经收到应用退出的信号啦.");
                return ;
            }
            try {
                String messageStr = (String) redisTemplate.opsForList().leftPop(CMPP_BLOCKING_WRITABLE_MESSAGE_KEY, 3L, TimeUnit.SECONDS);
                if (messageStr != null) {
                    log.info("messageStr {}", messageStr);
                    JSONObject object = JSONObject.parseObject(messageStr);
                    SubmitRequestMessage submitRequestMessage = object.getObject("requestMessage", new TypeReference<SubmitRequestMessage>() {
                    });
                    SmsMsg smsMsg = object.getObject("smsMsg", new TypeReference<SmsMsg>() {
                    });
                    String platform = object.getString("platform");

                    while (true) {
                        // 动态更新channelId 对象的channel渠道，可以动态拉起channel，以便及时更新起来
                        Channel channel = cmppClient.getChannelMap().get(smsMsg.getChannelId());
                        // 这里面有一个从头到尾一直阻塞的channel 的情况，导致队列里面无法进一步消费，需要防止
                        if (CmppClient.isActive(channel)) {
                            if (submitRequestMessage.getMsgLength() < CmppClient.MSX_MSG_LENGTH) {
                                // 先业务处理,再写channel,保证businessHandler.submitHandle()先执行,这样能减少一些后续业务异步处理的复杂度
                                businessHandler.submitHandle(submitRequestMessage, smsMsg);
                                // 写入到channel
                                channel.writeAndFlush(submitRequestMessage);
                            } else {
                                // 长短信拆分
                                List<SubmitRequestMessage> submitRequestMessageList = cmppClient.splitLongMessage(submitRequestMessage);
                                for (SubmitRequestMessage message : submitRequestMessageList) {
                                    // 先业务处理,再写channel
                                    businessHandler.submitHandle(message, smsMsg);
                                    // 写入到channel
                                    channel.writeAndFlush(message);
                                }
                            }
                            break;
                        } else {
                            if (!channel.isActive() || !channel.isOpen()) {
                                // 服务不可用直接废弃
                                CmppClientEntity clientEntity = ChannelManager.getCmppClientEntity(channel);
                                log.info("clientEntity={}", clientEntity);
                                if (clientEntity != null) {
                                    Map<String, Object> param = new HashMap<>();
                                    param.put("channelAccountId", String.valueOf(clientEntity.getId()));
                                    param.put("channelCode", clientEntity.getPlatform());
                                    ominiUtil.sendMessage(ominiUtil.getCmppChannelUnactivated(), param);
                                }
                                log.error("渠道不可用;");
                                break;
                            }
                            try {
                                TimeUnit.SECONDS.sleep(2l);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
            } catch (Exception e) {
                if(!CoupleDeliverRequestDaemon.isShutdown) {
                    log.error("", e);
                } else {
                    log.info("shutdown 忽略 leftPop 异常");
                }
            }

        }


    }

}
