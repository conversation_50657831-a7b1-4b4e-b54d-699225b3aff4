package com.xhqb.spectre.cmpp.constant;

/**
 * 消息记录状态常量
 * @author: xiaoxiaoxiang
 * @date: 2020/12/14 17:15
 */
public final class CmppBusinessConstants {
    private CmppBusinessConstants() {
    }

    /**
     * submit状态
     */
    public static final int SUBMIT_STATUS = 1;

    /**
     * submit_resp状态
     */
    public static final int SUBMIT_RESP_STATUS = 2;

    /**
     * submit + submit_resp 状态
     */
    public static final int SUBMIT_COUPLED_STATUS = 3;

    /**
     * deliver状态
     */
    public static final int DELIVER_STATUS = 4;

    /**
     * submit + submit_resp + deliver状态
     */
    public static final int DELIVER_COUPLED_STATUS = 7;

    public static final String CMPP_LAST_ACTIVE_TIME = "spectre:cmpp:active:time";

    public static final String CMPP_BLOCKING_WRITABLE_MESSAGE_KEY = "spectre:blocking:writable:message";

    /**
     * 发送submit request
     */
    public static final String CMPP_SUBMIT_REQUEST_KEY = "spectre:cmpp:submit:request";

    /**
     * 收到submit response
     */
    public static final String CMPP_SUBMIT_RESPONSE_KEY = "spectre:cmpp:submit:response";

    /**
     * 收到deliver request
     */
    public static final String CMPP_DELIVER_REQUEST_KEY = "spectre:cmpp:deliver:request";

    /**
     * 收到上行短信 request
     */
    public static final String CMPP_UPLINK_REQUEST_KEY = "spectre:cmpp:uplink:request";

    /**
     * 全部消息记录缓存(可以理解为一级缓存)
     */
    public static final String CMPP_ALL_RECORD_KEY = "spectre:cmpp:all:record";

    /**
     * deliver状态消息记录缓存(可以理解为二级缓存)
     */
    public static final String CMPP_DELIVER_RECORD_KEY = "spectre:cmpp:deliver:record";

    /**
     * 不成对的状态消息记录缓存(可以理解为二级缓存)
     */
    public static final String CMPP_UNCOUPLED_RECORD_KEY = "spectre:cmpp:uncoupled:record";
}
