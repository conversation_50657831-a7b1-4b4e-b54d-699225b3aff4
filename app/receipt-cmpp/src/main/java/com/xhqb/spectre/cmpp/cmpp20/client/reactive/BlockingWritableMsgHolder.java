package com.xhqb.spectre.cmpp.cmpp20.client.reactive;

import com.xhqb.spectre.cmpp.cmpp20.client.bussiness.SmsMsg;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.SubmitRequestMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BlockingWritableMsgHolder implements Serializable {

    private static final long serialVersionUID = -4540905888838697139L;

    private SubmitRequestMessage requestMessage;

    private SmsMsg smsMsg;

    private String platform;

}
