package com.xhqb.spectre.cmpp.enums;


public enum ChannelStatusEnum {

    BOOTING(0L, "启动中"),
    WORKING(1L, "运行中"),
    SHUTDOWN(2L, "被动下线"),
    ACCOUNT_ERROR(3L, "账号异常"),
    OFFLINE(4L, "手动下线");

    private Long status;

    private String desc;


    public Long getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    ChannelStatusEnum(Long status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
