package com.xhqb.spectre.cmpp;

import com.xhqb.spectre.cmpp.cmpp20.client.CmppClient;
import com.xhqb.spectre.cmpp.cmpp20.client.deliver.CoupleDeliverRequestDaemon;
import com.xhqb.spectre.cmpp.cmpp20.client.reactive.BlockingWritableMsgSubscriber;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableAsync
@ComponentScan("com.xhqb.spectre")
@MapperScan("com.xhqb.spectre.common.dal")
@EnableAutoConfiguration(exclude={DataSourceAutoConfiguration.class})
//@EnableConfigurationProperties({XhCmppClientProperties.class})
//@SpringBootApplication(exclude = com.xhqb.kael.boot.autoconfigure.sentinel.SentinelAutoConfiguration.class)
//@ImportResource(value = "classpath:applicationContext.xml")
public class SmsChannelApplication extends SpringBootServletInitializer implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    private CmppClient cmppClient;

    @Autowired
    private CoupleDeliverRequestDaemon coupleDeliverRequestDaemon;

    @Autowired
    private BlockingWritableMsgSubscriber blockingWritableMsgSubscriber;

    @Value("${deliver.thread.count:4}")
    private int deliverThreadCount;

    /**
     * 独立部署的时候的入口
     * 本地执行需要添加jvm参数：-Dhttp.port=8080 -Dcontext.path=/t -Denv=dev -Dstack=dev
     *
     * @param args
     */
    public static void main(String[] args) {
        configureApplication(new SpringApplicationBuilder()).run(args);
    }

    private static SpringApplicationBuilder configureApplication(SpringApplicationBuilder builder) {
        return builder.sources(SmsChannelApplication.class);
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        cmppClient.connect();

        for (int i = 0; i < deliverThreadCount; i++) {
            coupleDeliverRequestDaemon.disposeDeliverRequest();
        }

        blockingWritableMsgSubscriber.onNext();
    }
}
