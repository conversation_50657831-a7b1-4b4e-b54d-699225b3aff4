package com.xhqb.spectre.cmpp.cmpp20.client;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.cmpp.enums.ChannelStatusEnum;
import com.xhqb.spectre.cmpp.service.model.dto.ChannelDetailDTO;
import com.xhqb.spectre.cmpp.service.model.dto.ChannelStatusDTO;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.ChannelAccount;
import com.xhqb.spectre.common.enums.ProtocolTypeEnum;
import com.xhqb.spectre.common.utils.CommonUtil;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.EventLoop;
import io.netty.util.Attribute;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.net.InetSocketAddress;
import java.net.UnknownHostException;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ChannelManager {

    public static ConcurrentHashMap<String, Channel> channelMap = new ConcurrentHashMap<>();

    public static ConcurrentHashMap<String, CmppClientEntity> cmppClientEntityMap = new ConcurrentHashMap<>();

    public static ConcurrentHashMap<String, Long> channelStatus = new ConcurrentHashMap<>();

    public static ConcurrentHashMap<String, Long> channelRetry = new ConcurrentHashMap<>();

    public static String ip;

    public static Bootstrap bootstrap = new Bootstrap();

    public static Integer maxRetryNum = 2;
    public static Integer retryInterval = 30;

    @Value("${cmpp.maxRetryNum}")
    public void setMaxRetryNum(Integer maxRetryNum) {
        ChannelManager.maxRetryNum = maxRetryNum;
    }

    @Value("${cmpp.retryInterval:30}")
    public void setRetryInterval(Integer retryInterval) {
        ChannelManager.retryInterval = retryInterval;
    }

    public static StringRedisTemplate stringRedisTemplate;

    @Autowired
    ChannelManager(StringRedisTemplate stringRedisTemplate) {
        ChannelManager.stringRedisTemplate = stringRedisTemplate;
    }

    @PostConstruct
    void init() {
        try {
            ChannelManager.ip = CommonUtil.getInetAddress().getHostAddress();
            log.info("IP:{}", ip);
        } catch (UnknownHostException e) {
            log.error("UnknownHostException", e);
        }

        Map<Object, Object> accountMap = stringRedisTemplate.opsForHash()
            .entries(RedisKeys.ChannelAccountKeys.CHANNEL_ACCOUNT_HASH_KEY);
        log.info("初始化渠道配置, 缓存总数={}", accountMap.size());
        accountMap.forEach((k, v) -> {
            CmppClientEntity cmppClientEntity = createClientEntity(v.toString());
            if (cmppClientEntity != null) {
                log.info("载入渠道; 渠道商={},渠道ID={},账号名={}", cmppClientEntity.getPlatform()
                    , cmppClientEntity.getId(), cmppClientEntity.getUserName());
                ChannelManager.cmppClientEntityMap.put(k.toString(), cmppClientEntity);
                ChannelManager.setChannelStatus(k.toString(), ChannelStatusEnum.BOOTING);
                ChannelManager.setChannelRetry(k.toString(), 0L);
            }
        });
        log.info("渠道总数; size={}", cmppClientEntityMap.size());
    }

    public ConcurrentHashMap<String, CmppClientEntity> getCmppClientEntityMap() {
        return cmppClientEntityMap;
    }

    public CmppClientEntity getCmppClientEntity(String key) {
        return cmppClientEntityMap.get(key);
    }


    public ConcurrentHashMap<String, Channel> getChannelMap() {
        return channelMap;
    }

    public static CmppClientEntity getClientEntity(String key) {
        String string = (String) stringRedisTemplate.opsForHash()
            .get(RedisKeys.ChannelAccountKeys.CHANNEL_ACCOUNT_HASH_KEY, key);
        return createClientEntity(string);
    }

    private static CmppClientEntity createClientEntity(String string) {
        try {
            ChannelAccount channelAccount = JSONObject.parseObject(string, ChannelAccount.class);
            if (ProtocolTypeEnum.CMPP.getValue().equals(channelAccount.getProtocol()) &&
                channelAccount.getStatus() == 1) {
                JSONObject jsonObject = JSONObject.parseObject(channelAccount.getJsonMapping());
                CmppClientEntity clientEntity = new CmppClientEntity();
                clientEntity.setId(channelAccount.getId());
                clientEntity.setPlatform(channelAccount.getChannelCode());
                clientEntity.setSmsTypeCode(channelAccount.getSmsTypeCode());
                clientEntity.setUserName(channelAccount.getKey());
                clientEntity.setServerHost(jsonObject.getString("serverHost"));
                clientEntity.setServerPort(Integer.valueOf(jsonObject.getString("serverPort")));
                clientEntity.setPassword(jsonObject.getString("password"));
                clientEntity.setServiceId(jsonObject.getString("serviceId"));
                clientEntity.setSrcId(jsonObject.getString("srcId"));
                clientEntity.setMsgSrc(jsonObject.getString("msgSrc"));
                return clientEntity;
            }
            return null;
        } catch (Exception e) {
            log.error("CmppClientEntity 解析异常;", e);
            return null;
        }
    }

    public static synchronized void refresh(String key) {
        log.info("渠道账号配置刷新; channelAccountID:{}", key);
        CmppClientEntity clientEntity = ChannelManager.getClientEntity(key);
        ChannelManager.cmppClientEntityMap.put(key, clientEntity);
        if (clientEntity == null) {
            log.info("配置错误;");
        } else {
            //先关闭当前channel
            offline(key);
            ChannelManager.cmppClientEntityMap.put(key, clientEntity);
            ChannelManager.setChannelRetry(key, 0L);
            ChannelManager.setChannelStatus(key, ChannelStatusEnum.OFFLINE);
            ChannelManager.connect(key, clientEntity);
        }
    }

    public static synchronized void retry(String key) {
        log.info("渠道{}第{}次重试", key, ChannelManager.channelRetry.get(key) + 1);
        CmppClientEntity clientEntity = ChannelManager.cmppClientEntityMap.get(key);
        if (clientEntity == null) {
            log.info("配置错误;");
        } else {
            offline(key);
            ChannelManager.cmppClientEntityMap.put(key, clientEntity);
            ChannelManager.setChannelRetry(key, ChannelManager.channelRetry.get(key) + 1L);
            ChannelManager.connect(key, clientEntity);
        }
    }

    public static synchronized void offline(String key) {
        Channel channel = channelMap.get(key);
        if (channel != null) {
            log.info("offline; key={}, isOpen={}, isActive={}", key, channel.isOpen(), channel.isActive());
            ChannelManager.setChannelStatus(key, ChannelStatusEnum.OFFLINE);
            if(channel.isOpen()) {
                channel.close().addListener(future -> {
                    log.info("断开连接 channelAccountID:{}, future:{}, localAddress{}, remoteAddress:{}",
                            key,
                            future.isSuccess(),
                            channel.localAddress(),
                            channel.remoteAddress().toString());
                });
            }
        }
    }

    public static synchronized void connect(String key, CmppClientEntity clientEntity) {
        InetSocketAddress address = getAddress(clientEntity);
        ChannelManager.setChannelStatus(key, ChannelStatusEnum.BOOTING);
        bootstrap.connect(address).addListener(new ChannelFutureListener() {
            public void operationComplete(ChannelFuture future) {
                if (!future.isSuccess()) {
                    future.channel().close();
                    Long retry = getChannelRetry(key);
                    ChannelManager.setChannelStatus(key, ChannelStatusEnum.SHUTDOWN);
                    log.info("渠道{}第{}次重试失败", key, retry);
                    long delay = 1;
                    if (retry > ChannelManager.maxRetryNum) {
                        delay = retryInterval;
                    }
                    setChannelRetry(key, retry + 1L);
                    final EventLoop loop = future.channel().eventLoop();
                    loop.schedule(() -> {
                        if (ChannelStatusEnum.SHUTDOWN.getStatus().equals(ChannelManager.getChannelStatus(key))) {
                            log.info("渠道{}第{}次失败后重试", key, retry);
                            try {
                                bootstrap.connect(address).addListener(this);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }, delay, TimeUnit.MINUTES);

                } else {
                    Channel channel = future.channel();
                    AttributeKey<CmppClientEntity> clientEntityKey = AttributeKey.newInstance(channel.id().asLongText());
                    channel.attr(clientEntityKey).set(clientEntity);
                    ChannelManager.channelMap.put(key, channel);
                    ChannelManager.setChannelStatus(key, ChannelStatusEnum.WORKING);
                    log.info("渠道{}第{}次连接成功", key, getChannelRetry(key));
                }
            }
        });
    }

    public static synchronized void setChannelStatus(String key, ChannelStatusEnum channelStatusEnum) {
        ChannelManager.channelStatus.put(key, channelStatusEnum.getStatus());
        String k = MessageFormat.format(RedisKeys.CmppKeys.CHANNEL_ACCOUNT_STATUS_KEY, key);
        stringRedisTemplate.opsForHash().put(k, ChannelManager.ip,
            channelStatusEnum.getStatus().toString() + "," + System.currentTimeMillis());
        log.info("更新channel状态; key={}, status={}", key, channelStatusEnum.getDesc());
    }

    public static Long getChannelStatus(String key) {
        return channelStatus.get(key);
    }

    public static void refreshChannelStatus() {
        String stamp = String.valueOf(System.currentTimeMillis());
        channelStatus.forEach((k, v) -> {
            String key = MessageFormat.format(RedisKeys.CmppKeys.CHANNEL_ACCOUNT_STATUS_KEY, k);
            stringRedisTemplate.opsForHash().put(key, ChannelManager.ip,
                v + "," + stamp);
        });
    }

    public static void setChannelRetry(String key, Long n) {
        ChannelManager.channelRetry.put(key, n);
    }

    public static Long getChannelRetry(String key) {
        return channelRetry.get(key);
    }

    public static InetSocketAddress getAddress(CmppClientEntity clientEntity) {
        return new InetSocketAddress(clientEntity.getServerHost(),
            clientEntity.getServerPort());
    }

    public static CmppClientEntity getCmppClientEntity(Channel channel) {
        AttributeKey<CmppClientEntity> attributeKey = AttributeKey.valueOf(channel.id().asLongText());
        Attribute<CmppClientEntity> clientEntityAttribute = channel.attr(attributeKey);
        return clientEntityAttribute.get();
    }

    public static Boolean containsChannelKey(String key) {
        return channelMap.containsKey(key);
    }

    public static List<ChannelDetailDTO> getChannelDetail() {
        ChannelManager.clearIP();

        Map<String, CmppClientEntity> clientEntityMap = ChannelManager.cmppClientEntityMap;

        List<ChannelDetailDTO> channelDetailDTOS = new ArrayList<>();
        clientEntityMap.values().stream().forEach(entity -> {
            String statusKey = MessageFormat.format(RedisKeys.CmppKeys.CHANNEL_ACCOUNT_STATUS_KEY, entity.getId().toString());
            Map<Object, Object> statusMap = stringRedisTemplate.opsForHash().entries(statusKey);
            List<ChannelStatusDTO> channelStatusDTOS = new ArrayList<>();
            statusMap.forEach((i, s) -> {
                ChannelStatusDTO channelStatusDTO = ChannelStatusDTO.builder().ip(i.toString())
                    .status(Long.valueOf(s.toString().split(",")[0]))
                    .build();
                channelStatusDTOS.add(channelStatusDTO);
            });
            ChannelDetailDTO channelDetailDTO = ChannelDetailDTO.builder()
                .channelAccountId(entity.getId())
                .channelCode(entity.getPlatform())
                .channelStatusList(channelStatusDTOS).build();
            channelDetailDTOS.add(channelDetailDTO);
        });
        return channelDetailDTOS;
    }

    public static void clearIP() {
        Long stamp = System.currentTimeMillis();
        Set<String> ipSet = new HashSet<>();
        Set<String> activeSet = new HashSet<>();
        cmppClientEntityMap.forEach((key1, value) -> {
            String key = MessageFormat.format(RedisKeys.CmppKeys.CHANNEL_ACCOUNT_STATUS_KEY, key1);
            Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(key);
            entries.forEach((i, v) -> {
                ipSet.add(i.toString());
                String[] a = v.toString().split(",");
                if (Arrays.asList(a).size() < 2 || Long.valueOf(a[1]) > stamp - 180000L) {
                    activeSet.add(i.toString());
                }
            });
        });
        if (activeSet.size() > 0) {
            ipSet.removeAll(activeSet);
            cmppClientEntityMap.forEach((key, value) -> {
                String statusKey = MessageFormat.format(RedisKeys.CmppKeys.CHANNEL_ACCOUNT_STATUS_KEY, key);
                for (Object anIpSet : ipSet) {
                    stringRedisTemplate.opsForHash().delete(statusKey, anIpSet);
                }
            });
        }
    }
}
