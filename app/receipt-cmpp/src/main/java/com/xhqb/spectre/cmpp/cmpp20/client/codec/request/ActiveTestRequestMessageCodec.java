package com.xhqb.spectre.cmpp.cmpp20.client.codec.request;

import com.xhqb.spectre.cmpp.cmpp20.client.message.Message;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.ActiveTestRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageCodec;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/16 10:38
 */
@Slf4j
public class ActiveTestRequestMessageCodec extends MessageToMessageCodec<Message, ActiveTestRequestMessage> {

    private PacketType packetType;

    public ActiveTestRequestMessageCodec() {
        this.packetType = CmppPacketType.ACTIVETESTREQUEST;
    }


    @Override
    protected void encode(ChannelHandlerContext ctx, ActiveTestRequestMessage msg, List<Object> out) throws Exception {
//        log.info("------------active test request encode------------");
        msg.setBodyBuffer(new byte[0]);
        msg.getHeader().setBodyLength(msg.getBodyBuffer().length);
        out.add(msg);
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, Message msg, List<Object> out) throws Exception {
//        log.info("------------active test request encode------------");
        int commandId = msg.getHeader().getCommandId();
        if (packetType.getCommandId() != commandId) {
            //不解析，交给下一个codec
            out.add(msg);
            return;
        }
        ActiveTestRequestMessage requestMessage = new ActiveTestRequestMessage(msg.getHeader());
        out.add(requestMessage);
    }
}
