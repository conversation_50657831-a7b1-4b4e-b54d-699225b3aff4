package com.xhqb.spectre.cmpp.cmpp20.client.codec.response;

import com.xhqb.spectre.cmpp.cmpp20.client.message.Message;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.ActiveTestResponseMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.response.ActiveTestResponseBody;
import com.xhqb.spectre.cmpp.cmpp20.util.ByteUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageCodec;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/16 10:38
 */
@Slf4j
public class ActiveTestResponseMessageCodec extends MessageToMessageCodec<Message, ActiveTestResponseMessage> {

    private PacketType packetType;

    public ActiveTestResponseMessageCodec() {
        this.packetType = CmppPacketType.ACTIVETESTRESPONSE;
    }

    @Override
    protected void encode(ChannelHandlerContext ctx, ActiveTestResponseMessage msg, List<Object> out) throws Exception {
//        log.info("------------active test response encode------------");
        ByteBuf bodyBuffer = ctx.alloc().buffer(ActiveTestResponseBody.RESERVED.getBodyLength());
        bodyBuffer.writeByte(msg.getReserved());
        msg.setBodyBuffer(ByteUtil.byteBufToByteArray(bodyBuffer, bodyBuffer.readableBytes()));
        msg.getHeader().setBodyLength(msg.getBodyBuffer().length);
        ReferenceCountUtil.release(bodyBuffer);
        out.add(msg);
    }

    /**
     * cilent只做解码
     *
     * @param ctx
     * @param msg
     * @param out
     * @throws Exception
     */
    @Override
    protected void decode(ChannelHandlerContext ctx, Message msg, List<Object> out) throws Exception {
//        log.info("------------active test response encode------------");
        int commandId = msg.getHeader().getCommandId();
        if (packetType.getCommandId() != commandId) {
            //不解析，交给下一个codec
            out.add(msg);
            return;
        }

        ActiveTestResponseMessage responseMessage = new ActiveTestResponseMessage(msg.getHeader());
        ByteBuf bodyBuffer = Unpooled.wrappedBuffer(msg.getBodyBuffer());

        //甘肃测试环境回包缺少reserved字段，这里要容错
        if (bodyBuffer.readableBytes() > 0) {
            responseMessage.setReserved(bodyBuffer.readByte());
        }

        ReferenceCountUtil.release(bodyBuffer);
        out.add(responseMessage);
    }
}
