package com.xhqb.spectre.cmpp.service.model.result;

import com.xhqb.spectre.cmpp.service.model.enums.ResultEnum;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/19 11:27
 */
public class MessageSendResult extends BaseResult {

    private static final long serialVersionUID = -1115096155547997430L;

    public MessageSendResult() {
    }

    public MessageSendResult(boolean success, ResultEnum resultEnum) {
        super(success, resultEnum);
    }

    public MessageSendResult(boolean success, String otherResultCode, String otherResultMsg) {
        super(success, otherResultCode, otherResultMsg);
    }
}
