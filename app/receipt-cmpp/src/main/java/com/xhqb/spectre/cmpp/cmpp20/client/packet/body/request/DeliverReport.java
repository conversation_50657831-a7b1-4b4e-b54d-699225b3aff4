package com.xhqb.spectre.cmpp.cmpp20.client.packet.body.request;

import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppDataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.DataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.PacketBody;

/**
 * DeliverRequestBody
 *
 * @author: xiaoxiaoxiang
 * @date: 2020/5/15 16:21
 */
public enum DeliverReport implements PacketBody {

    /**
     * Msg_Id
     */
    MSGID(CmppDataType.UNSIGNEDINT, 8),
    /**
     * Stat
     */
    STAT(CmppDataType.OCTERSTRING, 7),
    /**
     * Submit_time
     */
    SUBMITTIME(CmppDataType.OCTERSTRING, 10),
    /**
     * Done_time
     */
    DONETIME(CmppDataType.OCTERSTRING, 10),
    /**
     * Dest_terminal_Id
     */
    DESTTERMINALID(CmppDataType.OCTERSTRING, 21),
    /**
     * SMSC_sequence
     */
    SMSCSEQUENCE(CmppDataType.UNSIGNEDINT, 4);

    /**
     * body总长度
     */
    private static final int BODYLENGTH = MSGID.fieldLength
        + STAT.fieldLength
        + SUBMITTIME.fieldLength
        + DONETIME.fieldLength
        + DESTTERMINALID.fieldLength
        + SMSCSEQUENCE.fieldLength;
    /**
     * 数据类型
     */
    private DataType dataType;
    /**
     * 长度
     */
    private int fieldLength;

    private DeliverReport(DataType dataType, int fieldLength) {
        this.dataType = dataType;
        this.fieldLength = fieldLength;
    }

    @Override
    public int getBodyLength() {
        return BODYLENGTH;
    }

    @Override
    public DataType getDataType() {
        return this.dataType;
    }

    @Override
    public int getFieldLength() {
        return this.fieldLength;
    }
}
