package com.xhqb.spectre.cmpp.cmpp20.client.message;


import com.xhqb.spectre.cmpp.cmpp20.client.message.header.Header;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;

import java.io.Serializable;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/7 15:17
 */
public interface Message extends Serializable {

    PacketType getPacketType();

    void setPacketType(PacketType packetType);

    Header getHeader();

    void setHeader(Header head);

    byte[] getBodyBuffer();

    void setBodyBuffer(byte[] bodyBuffer);
}
