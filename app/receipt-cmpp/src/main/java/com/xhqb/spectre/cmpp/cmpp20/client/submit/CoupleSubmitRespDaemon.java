package com.xhqb.spectre.cmpp.cmpp20.client.submit;

import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Data
public class CoupleSubmitRespDaemon {

    // 注意及时删除orderMap 里面的key value,防止内存泄露
    Map<String, DeferredResult> orderMap = new ConcurrentHashMap();


}
