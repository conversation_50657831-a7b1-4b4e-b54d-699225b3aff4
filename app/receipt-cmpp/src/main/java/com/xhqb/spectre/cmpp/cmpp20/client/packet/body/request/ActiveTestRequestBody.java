package com.xhqb.spectre.cmpp.cmpp20.client.packet.body.request;

import com.xhqb.spectre.cmpp.cmpp20.client.packet.DataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.PacketBody;

/**
 * ConnectRequestBody
 *
 * @author: xia<PERSON>ia<PERSON>iang
 * @date: 2020/5/15 16:21
 */
public enum ActiveTestRequestBody implements PacketBody {

    ;

    /**
     * body总长度
     */
    private static final int BODYLENGTH = 0;
    /**
     * 数据类型
     */
    private DataType dataType;
    /**
     * 长度
     */
    private int fieldLength;

    private ActiveTestRequestBody(DataType dataType, int fieldLength) {
        this.dataType = dataType;
        this.fieldLength = fieldLength;
    }

    @Override
    public int getBodyLength() {
        return BODYLENGTH;
    }

    @Override
    public DataType getDataType() {
        return this.dataType;
    }

    @Override
    public int getFieldLength() {
        return this.fieldLength;
    }
}
