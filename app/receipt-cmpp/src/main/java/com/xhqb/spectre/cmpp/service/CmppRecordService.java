package com.xhqb.spectre.cmpp.service;

import com.xhqb.spectre.cmpp.dto.SmsInfo;
import com.xhqb.spectre.common.dal.entity.CmppRecord;

import java.util.List;

/**
 * CmppRecordService 内部使用 不提供dubbo调用
 * @author: xiaoxiaoxiang
 * @date: 2020/12/16 10:25
 */
public interface CmppRecordService {

    /**
     * 根据msgIds查询submitCoupled状态的消息记录
     * @param msgIds
     * @return
     */
    List<CmppRecord> findSubmitCoupledByMsgIds(List<String> msgIds);

    /**
     * 更新Deliver信息
     * @param cmppRecord
     */
    void modifyDeliverInfo(CmppRecord cmppRecord);

    /**
     * 批量插入
     * @param smsInfoList
     */
    void batchInsert(List<SmsInfo> smsInfoList);
}
