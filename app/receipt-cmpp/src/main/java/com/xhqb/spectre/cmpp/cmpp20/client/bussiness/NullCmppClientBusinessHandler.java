package com.xhqb.spectre.cmpp.cmpp20.client.bussiness;

import com.xhqb.spectre.cmpp.cmpp20.client.CmppClientEntity;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.DeliverRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.SubmitRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.SubmitResponseMessage;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/6/2 10:30
 */
public class NullCmppClientBusinessHandler implements CmppClientBusinessHandler {

    @Override
    public void submitHandle(SubmitRequestMessage message, SmsMsg smsMsg) {

    }

    @Override
    public void submitRespHandle(SubmitResponseMessage message) {

    }

    @Override
    public void deliverHandle(DeliverRequestMessage message, CmppClientEntity clientEntity) {

    }
}
