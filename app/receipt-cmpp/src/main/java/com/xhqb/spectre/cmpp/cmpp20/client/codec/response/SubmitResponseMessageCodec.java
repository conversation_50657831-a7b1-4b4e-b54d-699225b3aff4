package com.xhqb.spectre.cmpp.cmpp20.client.codec.response;

import com.xhqb.spectre.cmpp.cmpp20.client.message.Message;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.SubmitResponseMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.response.SubmitResponseBody;
import com.xhqb.spectre.cmpp.cmpp20.util.ByteUtil;
import com.xhqb.spectre.cmpp.cmpp20.util.MsgIdUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageCodec;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/16 10:38
 */
@Slf4j
public class SubmitResponseMessageCodec extends MessageToMessageCodec<Message, SubmitResponseMessage> {

    private PacketType packetType;

    public SubmitResponseMessageCodec() {
        this.packetType = CmppPacketType.SUBMITRESPONSE;
    }


    @Override
    protected void encode(ChannelHandlerContext ctx, SubmitResponseMessage msg, List<Object> out) throws Exception {
        out.add(msg);
    }

    /**
     * cilent只做解码
     *
     * @param ctx
     * @param msg
     * @param out
     * @throws Exception
     */
    @Override
    protected void decode(ChannelHandlerContext ctx, Message msg, List<Object> out) throws Exception {
        int commandId = msg.getHeader().getCommandId();
        if (packetType.getCommandId() != commandId) {
            // 不解析，交给下一个codec
            out.add(msg);
            return;
        }
        SubmitResponseMessage responseMessage = new SubmitResponseMessage(msg.getHeader());
        ByteBuf bodyBuffer = Unpooled.wrappedBuffer(msg.getBodyBuffer());

        byte[] msgIdBytes = ByteUtil.byteBufToByteArray(bodyBuffer, SubmitResponseBody.MSGID.getFieldLength());
//        log.info("------------submit response msgIdBytes: {}", msgIdBytes);
        responseMessage.setMsgId(MsgIdUtil.bytes2MsgId(msgIdBytes));
        responseMessage.setResult(bodyBuffer.readUnsignedByte());
//        log.info("submit response: {}", responseMessage);
        ReferenceCountUtil.release(bodyBuffer);
        out.add(responseMessage);
    }
}
