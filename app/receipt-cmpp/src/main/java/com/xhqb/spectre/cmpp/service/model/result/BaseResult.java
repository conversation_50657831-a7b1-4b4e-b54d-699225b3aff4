package com.xhqb.spectre.cmpp.service.model.result;

import com.xhqb.spectre.cmpp.service.model.enums.ResultEnum;
import lombok.ToString;

import java.io.Serializable;

/**
 * BaseResult
 */
@ToString
public class BaseResult implements Serializable {

    private static final long serialVersionUID = -5365652051082595951L;
    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 结果枚举
     */
    private String resultCode;

    /**
     * 结果描述
     */
    private String resultMsg;

    /**
     * Constructor.
     */
    public BaseResult() {
        setSuccess();
    }

    /**
     * Constructor.
     *
     * @param success
     * @param otherResultCode
     * @param otherResultMsg
     */
    public BaseResult(boolean success, String otherResultCode, String otherResultMsg) {
        this.success = success;
        this.resultCode = otherResultCode;
        this.resultMsg = otherResultMsg;
    }

    public BaseResult(boolean success, ResultEnum resultEnum) {
        this.success = success;
        this.resultCode = resultEnum.getCode();
        this.resultMsg = resultEnum.getMessage();
    }

    /**
     * 执行成功
     */
    public void setSuccess() {
        this.success = true;
        this.resultCode = ResultEnum.SUCCESS.getCode();
        this.resultMsg = "执行成功";
    }

    /**
     * 执行失败
     */
    public void setFailure(String otherResultCode, String otherResultMsg) {
        this.success = false;
        this.resultCode = otherResultCode;
        this.resultMsg = otherResultMsg;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

}
