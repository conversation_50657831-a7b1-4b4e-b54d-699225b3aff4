package com.xhqb.spectre.cmpp.zookeeper;

import org.I0Itec.zkclient.ZkClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class ZookeeperConfig {


    @Bean
    @ConditionalOnMissingBean(ZkClient.class)
    public ZkClient zkClient(final CmppZookeeperProperties zookeeperProp) {
        return new ZkClient(zookeeperProp.getUrl(), zookeeperProp.getSessionTimeout(), zookeeperProp.getConnectionTimeout());
    }
}
