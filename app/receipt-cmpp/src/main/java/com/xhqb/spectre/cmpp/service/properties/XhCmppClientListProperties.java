package com.xhqb.spectre.cmpp.service.properties;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/6/2 11:27
 */
@Component
@ConfigurationProperties(prefix = "cmpp20.client")
@ToString
@Getter
@Setter
public class XhCmppClientListProperties {

    /**
     * cmpp server host
     */
    private Map<String, XhCmppClientProperties> clientProperties;
}
