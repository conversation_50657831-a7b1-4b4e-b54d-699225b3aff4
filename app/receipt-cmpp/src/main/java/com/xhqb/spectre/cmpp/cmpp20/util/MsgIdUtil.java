package com.xhqb.spectre.cmpp.cmpp20.util;

import com.xhqb.spectre.cmpp.cmpp20.client.message.MsgId;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.request.SubmitRequestBody;

import java.nio.ByteBuffer;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/6/8 15:35
 */
public final class MsgIdUtil {

    private MsgIdUtil() {
    }

    public static byte[] msgId2Bytes(MsgId msgId) {
        byte[] bytes = new byte[SubmitRequestBody.MSGID.getFieldLength()];
        long result = 0;
        if (msgId != null) {
            result |= (long) msgId.getMonth() << 60L;
            result |= (long) msgId.getDay() << 55L;
            result |= (long) msgId.getHour() << 50L;
            result |= (long) msgId.getMinutes() << 44L;
            result |= (long) msgId.getSeconds() << 38L;
            result |= (long) msgId.getGateId() << 16L;
            result |= (long) msgId.getSequenceId() & 0xffffL;
        }
        ByteBuffer.wrap(bytes).putLong(result);
        return bytes;
    }

    public static MsgId bytes2MsgId(byte[] bytes) {
        assert (bytes.length == SubmitRequestBody.MSGID.getFieldLength());
        long result = ByteBuffer.wrap(bytes).getLong();
        int month = (int) ((result >>> 60) & 0xf);
        int day = (int) ((result >>> 55) & 0x1f);
        int hour = (int) ((result >>> 50) & 0x1f);
        int minutes = (int) ((result >>> 44) & 0x3f);
        int seconds = (int) ((result >>> 38) & 0x3f);
        int gateId = (int) ((result >>> 16) & 0x3fffff);
        int sequenceId = (int) (result & 0xffff);
        MsgId msgId = new MsgId(month, day, hour, minutes, seconds, gateId, sequenceId);
        return msgId;
    }
}
