package com.xhqb.spectre.cmpp.cmpp20.client.packet.header;

import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppDataType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.DataType;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/15 17:49
 */
public enum PacketHeaderStructure implements PacketHeader {

    /**
     * Total_Length
     */
    TOTALLENGTH(CmppDataType.UNSIGNEDINT, 4),

    /**
     * Command_Id
     */
    COMMANDID(CmppDataType.UNSIGNEDINT, 4),

    /**
     * Sequence_Id
     */
    SEQUENCEID(CmppDataType.OCTERSTRING, 4);

    /**
     * body总长度
     */
    private static final int HEADERLENGTH = TOTALLENGTH.fieldLength
        + COMMANDID.fieldLength
        + SEQUENCEID.fieldLength;
    /**
     * 数据类型
     */
    private DataType dataType;
    /**
     * 长度
     */
    private int fieldLength;

    private PacketHeaderStructure(DataType dataType, int fieldLength) {
        this.dataType = dataType;
        this.fieldLength = fieldLength;
    }


    @Override
    public DataType getDataType() {
        return dataType;
    }

    @Override
    public int getFieldLength() {
        return fieldLength;
    }

    @Override
    public int getHeaderLength() {
        return HEADERLENGTH;
    }
}
