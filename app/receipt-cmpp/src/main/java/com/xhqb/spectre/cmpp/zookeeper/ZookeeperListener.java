package com.xhqb.spectre.cmpp.zookeeper;

import com.xhqb.spectre.cmpp.cmpp20.client.ChannelManager;
import com.xhqb.spectre.cmpp.cmpp20.client.CmppClient;
import com.xhqb.spectre.cmpp.cmpp20.client.CmppClientEntity;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.util.AttributeKey;
import io.netty.util.concurrent.GenericFutureListener;
import lombok.extern.slf4j.Slf4j;
import org.I0Itec.zkclient.IZkDataListener;
import org.I0Itec.zkclient.ZkClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class ZookeeperListener implements InitializingBean {

    @Autowired
    private ZkClient zkClient;

    @Autowired
    private CmppClient cmppClient;

    public static final String reconnect_path = "/spectre-cmpp/reconnect";

    public static final String discard_path = "/spectre-cmpp/discard";

    @Override
    public void afterPropertiesSet() throws Exception {


        zkClient.subscribeDataChanges(reconnect_path, new IZkDataListener() {
            @Override
            public void handleDataChange(final String dataPath, final Object data) {
                log.info("data {}", data);
                //
                Map<String, CmppClientEntity> cmppClientEntityMap = cmppClient.getCmppClientEntityMap();

                ConcurrentHashMap<String, Channel> channelMap = cmppClient.getChannelMap();

                for (Map.Entry<String, CmppClientEntity> entry : cmppClientEntityMap.entrySet()) {

                    CmppClientEntity clientEntity = entry.getValue();
                    Channel channel = channelMap.get(entry.getKey());
                    if (!channel.isActive() || !channel.isOpen()) {
                        log.info("inactive {}", entry.getKey());
//                        Bootstrap bootstrap = cmppClient.getBootstrap();
                        Bootstrap bootstrap = ChannelManager.bootstrap;

                        log.info("reconnect {}", entry.getKey());
                        ChannelFuture channelFuture = bootstrap.connect(clientEntity.getServerHost(),
                            clientEntity.getServerPort());
                        channelFuture.addListener(new GenericFutureListener<ChannelFuture>() {
                            @Override
                            public void operationComplete(ChannelFuture future) throws Exception {
                                if (future.isSuccess()) {
                                    Channel channel = channelFuture.channel();
                                    AttributeKey<CmppClientEntity> clientEntityKey = AttributeKey.newInstance(channel.id().asLongText());
                                    channel.attr(clientEntityKey).set(clientEntity);
                                    channelMap.put(entry.getKey(), channel);
                                    log.info("---------{}----connect success----------", entry.getKey());
                                } else {
                                    log.error("--------{}----connect failed: {}", entry.getKey(), future.cause());
                                    // TODO 重连
                                    future.channel().close();
                                }
                            }
                        });
                    }
                }
            }

            @Override
            public void handleDataDeleted(final String dataPath) {

            }

        });
    }
}
