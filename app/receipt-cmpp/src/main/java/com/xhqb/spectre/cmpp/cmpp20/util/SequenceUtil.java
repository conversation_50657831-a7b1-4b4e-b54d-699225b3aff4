package com.xhqb.spectre.cmpp.cmpp20.util;

import org.apache.commons.lang3.RandomUtils;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * @author: xiaoxiaoxiang
 * @date: 2020/5/15 15:51
 */
public final class SequenceUtil {
    private final static AtomicInteger SEQUENCEID = new AtomicInteger(RandomUtils.nextInt());

    private SequenceUtil() {
    }

    // TODO 分布式情况下,会有小概率导致sequenceId一样,比如生成了一样的随机数,或者负载均衡权重不一样,
    // TODO 导致某一台的sequenceId追上了其他服务,后续还是优化成可循环的分布式序列号
    // TODO 考虑int越界情况
    public static int getSequenceNo() {
        return SEQUENCEID.incrementAndGet();
    }
}
