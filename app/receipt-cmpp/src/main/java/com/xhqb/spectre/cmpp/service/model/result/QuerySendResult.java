package com.xhqb.spectre.cmpp.service.model.result;

import com.xhqb.spectre.cmpp.service.model.enums.ResultEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/19 11:27
 */
@ToString
@Getter
@Setter
public class QuerySendResult extends BaseResult {

    private static final long serialVersionUID = -8697835669969821366L;

    private byte sendStatus;

    private String content;

    private Date receiveDate;

    private String failCode;

    public QuerySendResult() {
    }

    public QuerySendResult(boolean success, ResultEnum resultEnum) {
        super(success, resultEnum);
    }

    public QuerySendResult(boolean success, String otherResultCode, String otherResultMsg) {
        super(success, otherResultCode, otherResultMsg);
    }
}
