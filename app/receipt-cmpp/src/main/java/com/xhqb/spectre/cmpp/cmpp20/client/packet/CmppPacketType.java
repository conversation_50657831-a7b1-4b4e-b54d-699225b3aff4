package com.xhqb.spectre.cmpp.cmpp20.client.packet;

import com.xhqb.spectre.cmpp.cmpp20.client.codec.request.*;
import com.xhqb.spectre.cmpp.cmpp20.client.codec.response.*;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.PacketBody;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.request.*;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.body.response.*;
import io.netty.handler.codec.MessageToMessageCodec;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/16 10:36
 */
@Slf4j
public enum CmppPacketType implements PacketType {

    /**
     * connect request packetType
     */
    CONNECTREQUEST(0x00000001, ConnectRequestBody.class, ConnectRequestMessageCodec.class),
    /**
     * connect response packetType
     */
    CONNECTRESPONSE(0x80000001, ConnectResponseBody.class, ConnectResponseMessageCodec.class),

    CMPPTERMINATEREQUEST(0x00000002, TerminateRequestBody.class, TerminateRequestMessageCodec.class),

    CMPPTERMINATERESPONSE(0x80000002, TerminateResponseBody.class, TerminateResponseMessageCodec.class),
    /**
     * submit request packetType
     */
    SUBMITREQUEST(0x00000004, SubmitRequestBody.class, SubmitRequestMessageCodec.class),
    /**
     * submit response packetType
     */
    SUBMITRESPONSE(0x80000004, SubmitResponseBody.class, SubmitResponseMessageCodec.class),
    /**
     * deliver request packetType
     */
    DELIVERREQUEST(0x00000005, DeliverRequestBody.class, DeliverRequestMessageCodec.class),
    /**
     * deliver response packetType
     */
    DELIVERRESPONSE(0x80000005, DeliverResponseBody.class, DeliverResponseMessageCodec.class),
//    CMPPQUERYREQUEST(0x00000006, CmppQueryRequest.class,CmppQueryRequestMessageCodec.class),
//    CMPPQUERYRESPONSE(0x80000006, CmppQueryResponse.class,CmppQueryResponseMessageCodec.class),
//    CMPPCANCELREQUEST(0x00000007, CmppCancelRequest.class,CmppCancelRequestMessageCodec.class),
//    CMPPCANCELRESPONSE(0x80000007, CmppCancelResponse.class,CmppCancelResponseMessageCodec.class),
    /**
     * active test request packetType
     */
    ACTIVETESTREQUEST(0x00000008, ActiveTestRequestBody.class, ActiveTestRequestMessageCodec.class),
    /**
     * active test response packetType
     */
    ACTIVETESTRESPONSE(0x80000008, ActiveTestResponseBody.class, ActiveTestResponseMessageCodec.class);

    private int commandId;

    private Class<? extends PacketBody> packetBody;

    private Class<? extends MessageToMessageCodec> codec;

    CmppPacketType(int commandId, Class<? extends PacketBody> packetBody, Class<? extends MessageToMessageCodec> codec) {
        this.commandId = commandId;
        this.packetBody = packetBody;
        this.codec = codec;
    }


    @Override
    public int getCommandId() {
        return commandId;
    }

    @Override
    public MessageToMessageCodec getCodec() {
        MessageToMessageCodec messageCodec = null;
        try {
            messageCodec = codec.newInstance();
        } catch (InstantiationException e) {
            log.error("CmppPacketType getCodec InstantiationException: ", e);
        } catch (IllegalAccessException e) {
            log.error("CmppPacketType getCodec IllegalAccessException: ", e);
        }
        return messageCodec;
    }
}
