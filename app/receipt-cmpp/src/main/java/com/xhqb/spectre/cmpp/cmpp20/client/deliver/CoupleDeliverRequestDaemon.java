package com.xhqb.spectre.cmpp.cmpp20.client.deliver;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.cmpp.cmpp20.client.wrapper.DeliverRequestMessageWrapper;
import com.xhqb.spectre.cmpp.constant.CmppBusinessConstants;
import com.xhqb.spectre.cmpp.dto.SmsInfo;
import com.xhqb.spectre.cmpp.service.CmppRecordService;
import com.xhqb.spectre.cmpp.service.CmppRecordServiceHelper;
import com.xhqb.spectre.cmpp.util.SmsInfoCacheUtil;
import com.xhqb.spectre.common.dal.dto.mq.CmppDeliverUplinkDTO;
import com.xhqb.spectre.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class CoupleDeliverRequestDaemon implements InitializingBean, DisposableBean {

    @Autowired
    private RedisTemplate<String, Object> jsonRedisTemplate;

    @Autowired
    private MQTemplate<String> mqTemplate;

    @Autowired
    private CmppRecordService cmppRecordService;

    @Autowired
    private CmppRecordServiceHelper cmppRecordServiceHelper;

    @Value("#{'${kael.mq.producers:}'.split(',')[2]}")
    private String spectreUplinkResq;

    @Value("${submit.request.cache.del.enable:false}")
    private volatile boolean delSubmitRequestCacheEnable;

    @Value("${submit.request.cache.del.ttl:60}")
    private long defaultDeleteCacheTTL;

    /**
     * 应用是否退出 true->已退出
     */
    public static volatile boolean isShutdown;

    @Override
    public void afterPropertiesSet() throws Exception {
//        disposeDeliverRequest();
        isShutdown = false;
    }

    @Async("deliverRequestExecutor")
    public void disposeDeliverRequest() {
        for (; ; ) {
            if (isShutdown) {
                log.info("已经收到应用退出的信号啦.");
                return;
            }
            // 不能因为异常导致守护线程中断
            try {
                DeliverRequestMessageWrapper message = (DeliverRequestMessageWrapper) jsonRedisTemplate.opsForList()
                        .leftPop(CmppBusinessConstants.CMPP_DELIVER_REQUEST_KEY, 3L, TimeUnit.SECONDS);
                if (message != null) {
                    if (message.getRegisteredDelivery() == 0) {
                        //上行短信
                        CmppDeliverUplinkDTO cmppDeliverUplinkDTO = CmppDeliverUplinkDTO.builder()
                                .channelMsgId(message.getMsgId().toString())
                                .destTerminalId(message.getDestId())
                                .mobile(message.getSrcTerminalId())
                                .recvUplinkTime(DateUtil.getNow())
                                .msgContent(message.getMsgContentText())
                                .msgLength((int) message.getMsgLength())
                                .createTime(new Date())
                                .channelAccountId(message.getClientEntity().getId())
                                .channelCode(message.getClientEntity().getPlatform())
                                .smsTypeCode(message.getClientEntity().getSmsTypeCode())
                                .build();
                        log.info("发送上行mq; mq:{}, message:{}", spectreUplinkResq, cmppDeliverUplinkDTO);
                        mqTemplate.sendAsync(spectreUplinkResq, JSONObject.toJSONString(cmppDeliverUplinkDTO));
                    } else {
                        //回执短信
                        String submitResponseCacheKey =  CmppBusinessConstants.CMPP_SUBMIT_RESPONSE_KEY + ":" +
                                message.getReportMessage().getMsgId().toString();
                        SmsInfo smsInfoSubmitResp = SmsInfoCacheUtil.getSmsInfo(submitResponseCacheKey);
                        if (smsInfoSubmitResp != null) {
                            SmsInfo smsInfoDeliverReq = new SmsInfo();
//                            log.info("smsInfoSubmitResp {}", smsInfoSubmitResp);
                            smsInfoDeliverReq.setSubmitSeqId(smsInfoSubmitResp.getSubmitSeqId());
                            smsInfoDeliverReq.setPlatformTime(smsInfoSubmitResp.getPlatformTime());
                            smsInfoDeliverReq.setPlatformStatus(smsInfoSubmitResp.getPlatformStatus());

                            String submitRequestCacheKey = CmppBusinessConstants.CMPP_SUBMIT_REQUEST_KEY + ":" + smsInfoSubmitResp.getSubmitSeqId();
                            SmsInfo smsInfoSubmitReq = SmsInfoCacheUtil.getSmsInfo(submitRequestCacheKey);
                            if (smsInfoSubmitReq != null) {
//                                log.info("smsInfoSubmitReq {}", smsInfoSubmitReq);
                                smsInfoDeliverReq.setPkTotal(smsInfoSubmitReq.getPkTotal());
                                smsInfoDeliverReq.setPkNumber(smsInfoSubmitReq.getPkNumber());
                                smsInfoDeliverReq.setAddition(smsInfoSubmitReq.getAddition());
                                smsInfoDeliverReq.setOrderId(smsInfoSubmitReq.getOrderId());
                                smsInfoDeliverReq.setPartnerPlatform(smsInfoSubmitReq.getPartnerPlatform());
                                smsInfoDeliverReq.setMobile(smsInfoSubmitReq.getMobile());
                                smsInfoDeliverReq.setChannelId(smsInfoSubmitReq.getChannelId());
                                smsInfoDeliverReq.setTplCode(smsInfoSubmitReq.getTplCode());
                                smsInfoDeliverReq.setResend(smsInfoSubmitReq.getResend());
                                smsInfoDeliverReq.setSmsTypeCode(smsInfoSubmitReq.getSmsTypeCode());
                                smsInfoDeliverReq.setRecvSendTime(smsInfoSubmitReq.getRecvSendTime());
                                smsInfoDeliverReq.setReqSrc(smsInfoSubmitReq.getReqSrc());
                                smsInfoDeliverReq.setGatewayUserName(smsInfoSubmitReq.getGatewayUserName());
                                smsInfoDeliverReq.setRequestId(smsInfoSubmitReq.getRequestId());

                                smsInfoDeliverReq.setSubMsgId(message.getReportMessage().getMsgId().toString());
                                smsInfoDeliverReq.setReportStatus(message.getReportMessage().getStat());
                                smsInfoDeliverReq.setReportTime(message.getReportMessage().getDoneTime());
                                smsInfoDeliverReq.setRecordStatus(CmppBusinessConstants.DELIVER_STATUS);
                                smsInfoDeliverReq.setDestTerminalId(message.getReportMessage().getDestTerminalId());
                                smsInfoDeliverReq.setSubmitTime(message.getReportMessage().getSubmitTime());
                                smsInfoDeliverReq.setDestId(message.getDestId());

                                smsInfoDeliverReq.setTableNameSuffix(smsInfoSubmitReq.getTableNameSuffix());

                                // 入库
                                //cmppRecordService.batchInsert(ImmutableList.of(smsInfoDeliverReq));
                                // 发Q
                                cmppRecordServiceHelper.sendSmsReceiptList(ImmutableList.of(smsInfoDeliverReq));


                                // 尝试将submit request cache删除, 不删除长短信
                                if (delSubmitRequestCacheEnable && Objects.equals(smsInfoSubmitReq.getPkTotal(), 1)) {
                                    try {
                                        SmsInfoCacheUtil.deleteSmsInfo(submitRequestCacheKey, defaultDeleteCacheTTL);
                                        SmsInfoCacheUtil.deleteSmsInfo(submitResponseCacheKey, defaultDeleteCacheTTL);
                                    } catch (Exception e) {
                                        log.warn("删除submit request cache fail|key:{}, errorMsg:{}",
                                                submitRequestCacheKey, e.getMessage());
                                    }
                                }
                            }
                        } else {
                            Integer submitTime = DateUtil.strToIntTime(message.getReportMessage().getSubmitTime());
                            if (submitTime != null) {
                                Integer now = DateUtil.getNow();
                                if (submitTime + 259200 > now) {
                                    // deliver 数据先回来，submitresponse 没有回来 下次再做处理
                                    jsonRedisTemplate.opsForList().rightPush(CmppBusinessConstants.CMPP_DELIVER_REQUEST_KEY, message);
                                } else {
                                    //判断时间超过72小时抛掉
                                    log.info("回执过期;message:{}", message);
                                }
                            }
                        }
                    }
                } else {
                    Thread.sleep(1000);
                }
            } catch (Exception e) {
                if (!isShutdown) {
                    log.error("couple deliver request daemon error", e);
                } else {
                    log.info("shutdown 忽略 leftPop 异常");
                }
            }
        }
    }


    @Override
    public void destroy() throws Exception {
        isShutdown = true;
    }
}
