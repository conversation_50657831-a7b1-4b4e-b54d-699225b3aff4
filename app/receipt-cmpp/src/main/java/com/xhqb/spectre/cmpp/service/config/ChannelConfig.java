//package com.xhqb.sms.channel.biz.com.xhqb.spectre.httpreceipt.service.config;
//
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Configuration;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2020-05-14
// */
//@Configuration
//@ConfigurationProperties(prefix="sms.channel")
//public class ChannelConfig {
//
//    private List<String> available;
//
//    private String batchSend;
//
//    public void setAvailable(List<String> available) {
//        this.available = available;
//    }
//
//    public List<String> getAvailable() {
//        return available;
//    }
//
//    public String getBatchSend() {
//        return batchSend;
//    }
//
//    public void setBatchSend(String batchSend) {
//        this.batchSend = batchSend;
//    }
//}
