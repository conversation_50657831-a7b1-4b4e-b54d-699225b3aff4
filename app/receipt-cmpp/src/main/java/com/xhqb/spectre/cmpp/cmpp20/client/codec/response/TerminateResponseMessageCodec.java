package com.xhqb.spectre.cmpp.cmpp20.client.codec.response;

import com.xhqb.spectre.cmpp.cmpp20.client.message.Message;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.TerminateResponseMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.PacketType;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageCodec;

import java.util.List;

public class TerminateResponseMessageCodec extends MessageToMessageCodec<Message, TerminateResponseMessage> {
    private PacketType packetType;

    public static final byte[] emptyBytes = new byte[0];

    public TerminateResponseMessageCodec() {
        this(CmppPacketType.CMPPTERMINATERESPONSE);
    }

    public TerminateResponseMessageCodec(PacketType packetType) {
        this.packetType = packetType;
    }


    @Override
    protected void decode(ChannelHandlerContext ctx, Message msg, List<Object> out) throws Exception {
        int commandId =  msg.getHeader().getCommandId();
        if(packetType.getCommandId() != commandId) {
            //不解析，交给下一个codec
            out.add(msg);
            return;
        } ;

        TerminateResponseMessage responseMessage = new TerminateResponseMessage(msg.getHeader());
        out.add(responseMessage);
    }

    @Override
    protected void encode(ChannelHandlerContext ctx, TerminateResponseMessage msg, List<Object> out) throws Exception {
        msg.setBodyBuffer(emptyBytes);
        msg.getHeader().setBodyLength(msg.getBodyBuffer().length);
        out.add(msg);

    }

}
