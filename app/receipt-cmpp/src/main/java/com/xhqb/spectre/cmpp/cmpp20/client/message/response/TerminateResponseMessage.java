package com.xhqb.spectre.cmpp.cmpp20.client.message.response;

import com.xhqb.spectre.cmpp.cmpp20.client.message.DefaultMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.header.Header;
import com.xhqb.spectre.cmpp.cmpp20.client.packet.CmppPacketType;

public class TerminateResponseMessage  extends DefaultMessage {
    private static final long serialVersionUID = -2657187574508760595L;

    public TerminateResponseMessage(int sequenceId) {
        super(CmppPacketType.CMPPTERMINATERESPONSE,sequenceId);
    }
    public TerminateResponseMessage(Header header) {
        super(CmppPacketType.CMPPTERMINATERESPONSE,header);
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return String.format("CmppTerminateResponseMessage [toString()=%s]",
                super.toString());
    }
}

