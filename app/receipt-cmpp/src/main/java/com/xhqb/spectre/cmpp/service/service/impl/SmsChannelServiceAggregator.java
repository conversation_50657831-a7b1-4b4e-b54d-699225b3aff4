package com.xhqb.spectre.cmpp.service.service.impl;

import com.xhqb.spectre.cmpp.service.constant.Channels;
import com.xhqb.spectre.cmpp.service.model.dto.MessageDto;
import com.xhqb.spectre.cmpp.service.model.result.MessageSendResult;
import com.xhqb.spectre.cmpp.service.service.SmsChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/5/19 18:26
 */
@Service
public class SmsChannelServiceAggregator {

    @Autowired
    @Qualifier(Channels.TENCENT_CMPP_SMS)
    private SmsChannelService tencentCmppChannelServiceImpl;

//    private Map<String, SmsChannelService> channelServiceMap;

//    public SmsChannelServiceAggregator() {
//        channelServiceMap.put(Channels.TENCENT_CMPP_SMS, tencentCmppChannelServiceImpl);
//    }

    public MessageSendResult send(String channel, List<MessageDto> messageDtoList) {
//        SmsChannelService smsChannelService = channelServiceMap.get(channel);
        SmsChannelService smsChannelService = tencentCmppChannelServiceImpl;
        for (MessageDto messageDto : messageDtoList) {
            MessageSendResult result = smsChannelService.send(messageDto);
        }
        return new MessageSendResult();
    }
}
