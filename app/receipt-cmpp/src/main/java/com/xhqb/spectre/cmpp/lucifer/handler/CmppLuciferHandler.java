package com.xhqb.spectre.cmpp.lucifer.handler;

import com.google.common.collect.Lists;
import com.xhqb.kael.lucifer.telemetry.PrometheusCounterMetrics;
import com.xhqb.spectre.cmpp.cmpp20.client.message.DefaultMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.DeliverRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.request.SubmitRequestMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.ConnectResponseMessage;
import com.xhqb.spectre.cmpp.cmpp20.client.message.response.SubmitResponseMessage;
import com.xhqb.spectre.cmpp.lucifer.LuciferHandler;
import io.prometheus.client.Counter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * cmpp协议埋点处理
 * <p>
 * 只处理 ConnectResponse、SubmitRequest、SubmitResponse、DeliverRequest
 *
 * <AUTHOR>
 * @date 2021/10/27
 */
@Component
@Slf4j
public class CmppLuciferHandler implements LuciferHandler<DefaultMessage> {

    /**
     * 接收做埋点的消息clazz
     */
    private static final List<Class<? extends DefaultMessage>> ACCEPT_LUCIFER_CLAZZ = Lists.newArrayList(
            ConnectResponseMessage.class,
            SubmitRequestMessage.class,
            SubmitResponseMessage.class,
            DeliverRequestMessage.class
    );

    /**
     * cmpp协议处理次数
     * connect submit deliver
     */
    private final Counter CMPP_MESSAGE_TYPE_COUNTER = new PrometheusCounterMetrics("cmpp_message_type_handler_total", "cmpp协议消息处理次数")
            .createWithLabels("cmppMessageType");

    /**
     * DefaultMessage 类缓存
     */
    private Map<Class<? extends DefaultMessage>, String> clazzCache = new ConcurrentHashMap<>(16);

    /**
     * 前置处理
     *
     * @param context
     */
    @Override
    public void preHandle(DefaultMessage context) {
        if (!acceptLuciferMessage(context)) {
            // 该消息不需要做埋点处理
            return;
        }
        CMPP_MESSAGE_TYPE_COUNTER.labels(getLabelName(context)).inc();
    }

    /**
     * 后置处理
     *
     * @param context
     */
    @Override
    public void postHandle(DefaultMessage context) {

    }

    /**
     * 判断是否需要进行埋点的消息
     *
     * @param context
     * @return 返回true表示当前消息需要做埋点操作
     */
    private boolean acceptLuciferMessage(DefaultMessage context) {
        return ACCEPT_LUCIFER_CLAZZ.contains(context.getClass());
    }

    /**
     * 获取到label名称
     *
     * @param defaultMessage
     * @return
     */
    private String getLabelName(DefaultMessage defaultMessage) {
        String labelName = clazzCache.putIfAbsent(defaultMessage.getClass(), defaultMessage.getClass().getSimpleName());
        if (StringUtils.isNotBlank(labelName)) {
            return labelName;
        }

        labelName = clazzCache.get(defaultMessage.getClass());
        if (StringUtils.isBlank(labelName)) {
            labelName = defaultMessage.getClass().getSimpleName();
        }
        return labelName;
    }

}
