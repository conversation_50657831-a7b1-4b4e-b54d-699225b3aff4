package com.xhqb.spectre.cmpp.controller;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.cmpp.dto.SmsInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.aws.messaging.core.QueueMessagingTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @program smschannel-parent
 * @description:
 * @author: zengxiao
 * @create: 2021/08/12 09:49:25
 */
@Controller
@RequestMapping("/smsChannel")
public class SmsChannelController {

    @Autowired
    private QueueMessagingTemplate messagingTemplate;


    @PostMapping("/sendMq")
    @ResponseBody
    public String sendMq(@RequestBody SmsInfo smsInfo){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("phone", "1");
        jsonObject.put("msgLength", "1");
        jsonObject.put("msgIndex", "1");
        jsonObject.put("platformStatus", "1");
        jsonObject.put("notifyTime", "1");
        jsonObject.put("operatorStatus", "1");
        jsonObject.put("reportTime", "1");
        jsonObject.put("partnerPlatform", "1");
        jsonObject.put("addition", "1");
        messagingTemplate.convertAndSend(smsInfo.getQueueName(), jsonObject.toJSONString());
        return "success";
    }
}
