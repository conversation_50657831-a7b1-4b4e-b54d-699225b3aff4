# 不提供dubbo服务
#dubbo:
#  application:
#    name: sms-channel
#    owner: xiaoxiaoxiang
#    organization: xhqb
#  protocol:
#    threadpool: 10
#    threads: 10
#  consumer:
#     version: 2.0
#     timeout: 5000
#     retries: 0
#  provider:
#    version: 2.0
#    timeout: 5000

########## zookeeper ########
#kael.zookeeper.host: ***********:2181

#management:
#  endpoints:
#    web:
#      exposure:
#        include: '*'
#  endpoint:
#    shutdown:
#      enabled: false
#    health:
#      show-details: always

spring:
  redis:
    resourceId: "sms-channel"
    timeout: 5000ms

# sequence
#kael:
#  sequence:
#    namespace: sms-channel
#    env: test
#    url: ${kael.zookeeper.host}

# db
kael.datasource.druid:
  resource-id: "sms-channel"
  url: **************************************************************************************************************************
#  test-container:
#    enabled: true

# mybatis
#mybatis:
#  mapper-locations:
#    - classpath:com/xhqb/sms/channel/common/dal/mapper/*.xml
#  type-aliases-package: com.xhqb.kael.example.mybatis.model

# resources
kael.config.resources:
  #  druid:
  #    sms-channel:
  #      username: xhtest
  #      password: DuFQY5VLVjuIdUPNS7o9eoiBazmwP2IGH+iqYvxfIUeC79o6RUZcRxRvw0JLdTRyBqLgNZybLbn/de+4tpBdzw==
  #      driverClassName: com.mysql.cj.jdbc.Driver
  #      connectionProperties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAI6fuRC58lLwqw9jwGnQ6oSgUW/lu/pVN5cEFjt/Dr9H/u1gw4cbqKFrUUMH9rK/Hj9/6N8DiuEPPyVxzVtTuJkCAwEAAQ==
  redis:
    sms-channel:
      # test1
      host: ***********
      port: 6379
      timeout: 5000ms

# 为了不对server-config2里的配置文件造成影响,发布测试/生产时,清注释
#cmpp20:
#  client:
#    clientProperties:
#      tencentMarketing:
#        # 腾讯营销
#        platform : Tencent
#        serverHost: *************
#        serverPort: 12000
#        userName: 101485
#        password: 18396293f3
#        msgSrc: 101485
#      tencentIndustry:
#        # 腾讯行业
#        platform : Tencent
#        serverHost: *************
#        serverPort: 12000
#        userName: 101479
#        password: 4f1b2299d0
#        msgSrc: 101479
#      miaoXinVerificationCode:
#        # 秒信-三网-金融验证码
#        platform : miaoxin
#        serverHost: **************
#        serverPort: 7890
#        userName: 10012z
#        password: 8yz6mmsvwu
#        serviceId: X109921032
#        srcId: 10692313
#        msgSrc: 10012z
#      miaoXinIndustry:
#        # 秒信-三网-金融通知(即行业短信)
#        platform : miaoxin
#        serverHost: **************
#        serverPort: 7890
#        userName: 10013a
#        password: up1jygk7kt
#        serviceId: X109921034
#        srcId: 10692313
#        msgSrc: 10013a

smsReceipt:
  config:
    queueName: sms-channelReceipt-dev