package com.xhqb.spectre.cmpp;

import com.xhqb.spectre.cmpp.dto.SmsInfo;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * RedisTemplateMocker
 *
 * @author: xiaoxiaoxiang
 * @date: 2019/10/19 14:22
 */
public class RedisTemplateMocker {

    /**
     * mock RedisTemplate
     *
     * @return
     */
    public RedisTemplate<String, Object> mockJsonRedisTemplate(RedisTemplate<String, Object> jsonRedisTemplate) {
        SmsInfo smsInfo = new SmsInfo();
        List<Object> smsInfoList = new ArrayList<>();
        smsInfoList.add(smsInfo);
        ListOperations<String, Object> listOperations = Mockito.mock(ListOperations.class);
        Mockito.when(jsonRedisTemplate.opsForList()).thenReturn(listOperations);
        Mockito.when(listOperations.rightPush(ArgumentMatchers.anyString(),
            ArgumentMatchers.any(SmsInfo.class))).thenReturn(1L);
        Mockito.when(listOperations.size(ArgumentMatchers.anyString())).thenReturn(1L);
        Mockito.when(listOperations.range(ArgumentMatchers.anyString(), ArgumentMatchers.anyLong(), ArgumentMatchers.anyLong()))
            .thenReturn(smsInfoList);
        return jsonRedisTemplate;
    }
}
