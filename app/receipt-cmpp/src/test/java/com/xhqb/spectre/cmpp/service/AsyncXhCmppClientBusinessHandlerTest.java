//package com.xhqb.spectre.cmpp.service;
//
//import com.xhqb.spectre.cmpp.RedisTemplateMocker;
//import com.xhqb.spectre.cmpp.cmpp20.client.message.request.SubmitRequestMessage;
//import com.xhqb.spectre.cmpp.service.service.cmppclienthandler.AsyncXhCmppClientBusinessHandler;
//import io.netty.util.CharsetUtil;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.Spy;
//import org.mockito.junit.MockitoJUnitRunner;
//import org.springframework.data.redis.core.RedisTemplate;
//
///**
// * @description:
// * @author: xiaoxiaoxiang
// * @date: 2020/12/28 13:50
// */
//@RunWith(MockitoJUnitRunner.Silent.class)
//public class AsyncXhCmppClientBusinessHandlerTest {
//
//    @Mock
//    private RedisTemplate<String, Object> jsonRedisTemplate;
//
//    @Spy
//    @InjectMocks
//    private AsyncXhCmppClientBusinessHandler asyncXhCmppClientBusinessHandler;
//
//    private RedisTemplateMocker redisTemplateMocker;
//
//    @Before
//    public void setUp() throws Exception {
//        MockitoAnnotations.initMocks(this);
//        redisTemplateMocker = new RedisTemplateMocker();
//    }
//
//    @Test
//    public void submitHandleTest() {
//        SubmitRequestMessage message = initSubmitRequestMessage();
//        jsonRedisTemplate = redisTemplateMocker.mockJsonRedisTemplate(jsonRedisTemplate);
//        asyncXhCmppClientBusinessHandler.submitHandle(message, "123", "miaoXin");
//    }
//
//    private SubmitRequestMessage initSubmitRequestMessage() {
//        SubmitRequestMessage submitMessage = new SubmitRequestMessage();
//        submitMessage.setDestterminalId(new String[]{"***********"});
//        submitMessage.setDestUsrTl((byte) submitMessage.getDestterminalId().length);
//        submitMessage.setServiceId("test");
//        submitMessage.setSrcId("test");
//        submitMessage.setMsgSrc("test");
//        submitMessage.setMsgContentText("test");
//        submitMessage.setMsgContent("test".getBytes(CharsetUtil.UTF_16BE));
//        submitMessage.setMsgLength(submitMessage.getMsgContent().length);
//        submitMessage.setPktotal((byte) 1);
//        submitMessage.setPknumber((byte) 1);
//        return submitMessage;
//    }
//}
