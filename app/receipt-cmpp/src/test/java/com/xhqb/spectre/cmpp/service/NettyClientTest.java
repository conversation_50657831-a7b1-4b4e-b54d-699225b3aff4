//package com.xhqb.spectre.cmpp.service;
//
//import com.xhqb.spectre.cmpp.cmpp20.client.CmppClient;
//import com.xhqb.spectre.cmpp.cmpp20.client.CmppClientEntity;
//import com.xhqb.spectre.cmpp.cmpp20.client.bussiness.SmsMsg;
//import com.xhqb.spectre.cmpp.cmpp20.client.message.MsgId;
//import com.xhqb.spectre.cmpp.cmpp20.util.MsgIdUtil;
//
//import java.util.HashMap;
//import java.util.Map;
//import java.util.concurrent.locks.LockSupport;
//
///**
// * @description:
// * @author: xiaoxiaoxiang
// * @date: 2020/5/15 10:22
// */
//public class NettyClientTest {
//
//    //    @Test
//    public void sendTest() throws InterruptedException {
//        CmppClientEntity clientEntity = cmppClientEntity();
//        CmppClient client = cmppClient(clientEntity);
//        client.connect();
//        Thread.sleep(3000L);
//        String mobile = "15271944243";
//        String content = "【小花钱包】您有最高50000元额度待激活，立刻激活提钱，24小时有效，极速放款，门槛低，好友邀请，更能获得奖金，快来参与，戳 xhqb.com 回复T退订";
////        message.setMsgContent("【小花钱包】您有最高50000元额度待激活，立刻激活提钱，24小时有效，戳 xhqb.com 回T退订".getBytes(CharsetUtil.UTF_16BE));
//        // 【小花钱包】123456 为您的登录验证码，请于5分钟内填写，如非本人操作，请忽略本短信。
//        SmsMsg smsMsg = new SmsMsg();
//        smsMsg.build("miaoXinMarketing", mobile, content, "test");
////        client = Mockito.spy(client);
////        Mockito.when(client.isActive(ArgumentMatchers.any())).thenReturn(true);
//        client.submit(smsMsg);
//        LockSupport.park();
//    }
//
//    private CmppClientEntity cmppClientEntity() {
//        CmppClientEntity clientEntity = new CmppClientEntity();
//        clientEntity.init("miaoXin", "120.133.40.140", 7890, "logci", "a8h5lox437",
//            "1001ci", "1400360015", "10692313");
//        return clientEntity;
//    }
//
//    private CmppClient cmppClient(CmppClientEntity clientEntity) {
//        Map<String, CmppClientEntity> cmppClientEntityMap = new HashMap<>(1);
//        cmppClientEntityMap.put("tencentIndustry", clientEntity);
//        CmppClient client = new CmppClient(cmppClientEntityMap);
//        return client;
//    }
//
//    //    @Test
//    public void test() {
//        byte[] msgIdBytes = new byte[]{-100, -71, -16, 74, -29, 29, 18, -126};
//        MsgId msgId = MsgIdUtil.bytes2MsgId(msgIdBytes);
//        System.out.println(msgId.toString());
//    }
//}
