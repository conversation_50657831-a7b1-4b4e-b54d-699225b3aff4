package com.xhqb.spectre.cmpp.service.controller;

import com.google.common.collect.Lists;
import com.xhqb.spectre.cmpp.RedisTemplateMocker;
import com.xhqb.spectre.cmpp.cmpp20.client.CmppClientEntity;
import com.xhqb.spectre.cmpp.cmpp20.client.codec.HeaderCodec;
import com.xhqb.spectre.cmpp.dto.SmsInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.List;


/**
 * Controller最好还是采用MockMvc的方式写测试用例(注释代码), 不要像我这样写
 *
 * @author: xiaoxiaoxiang
 * @date: 2020/12/29 09:20
 */
//@RunWith(SpringRunner.class)
//@WebMvcTest(QueryController.class)
@RunWith(MockitoJUnitRunner.Silent.class)
public class QueryControllerTest {

    @Mock
    private RedisTemplate<String, Object> jsonRedisTemplate;

    @Spy
    @InjectMocks
    private QueryController queryController;

    private RedisTemplateMocker redisTemplateMocker;
    @Spy
    @InjectMocks
    private CmppClientEntity cmppClientEntity;
    @Spy
    @InjectMocks
    private HeaderCodec headerCodec;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        redisTemplateMocker = new RedisTemplateMocker();
    }


    @Test
    public void init() {
        cmppClientEntity.init("1", "1", 1, "1", "1", "1", "1", "1");
        System.out.println(cmppClientEntity.getMsgSrc() + cmppClientEntity.getPassword() + cmppClientEntity.getPlatform() +
            cmppClientEntity.getServerHost() + cmppClientEntity.getServerPort() + cmppClientEntity.getServiceId() +
            cmppClientEntity.getSrcId() + cmppClientEntity.getUserName());
    }

    @Test
    public void deliverRecord() {
        jsonRedisTemplate = redisTemplateMocker.mockJsonRedisTemplate(jsonRedisTemplate);
        List<Object> smsInfoList = Lists.newArrayList();
        SmsInfo smsInfo = new SmsInfo();
        smsInfo.setSubMsgId("1");
        smsInfoList.add(smsInfo);
        Mockito.when(jsonRedisTemplate.opsForList().range(Mockito.anyObject(), Mockito.anyLong(), Mockito.anyLong())).thenReturn(smsInfoList);

        queryController.deliverRecord("1");
    }

    @Test
    public void allLengthTest() {
        jsonRedisTemplate = redisTemplateMocker.mockJsonRedisTemplate(jsonRedisTemplate);
        queryController.allLength();
    }


    @Test
    public void smsInfoListTest() {
        SmsInfo smsInfo = new SmsInfo();
        List<SmsInfo> smsInfoList = new ArrayList<>();
        smsInfoList.add(smsInfo);
        jsonRedisTemplate = redisTemplateMocker.mockJsonRedisTemplate(jsonRedisTemplate);
        queryController.pushRecord(smsInfoList);
    }


//    @Autowired
//    private MockMvc mockMvc;
//
//    @Test
//    public void allLengthTest() {
//        try {
//            MvcResult mvcResult = this.mockMvc.perform(MockMvcRequestBuilders.get("/query/length"))
//                    .andDo(MockMvcResultHandlers.print())
//                    .andExpect(MockMvcResultMatchers.status().isOk())
//                    .andReturn();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

}
