package com.xhqb.spectre.cmpp.cmpp20.util;

import com.xhqb.spectre.cmpp.cmpp20.client.message.MsgId;
import org.junit.Test;

/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2021/2/1 15:39
 */
public class MsgIdUtilTest {

    @Test
    public void test() {
        byte[] msgIdBytes = new byte[]{-50, -85, 4, 5, 22, -6, 67, -15};
        MsgId msgId = MsgIdUtil.bytes2MsgId(msgIdBytes);
    }


    @Test
    public void test2() {
        MsgId msgId = new MsgId(2, 1, 14, 23, 24, 22, 32);
        byte[] bytes = MsgIdUtil.msgId2Bytes(msgId);
    }
}
