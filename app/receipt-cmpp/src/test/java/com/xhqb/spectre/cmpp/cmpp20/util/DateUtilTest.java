package com.xhqb.spectre.cmpp.cmpp20.util;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;


/**
 * @description:
 * @author: xiaoxiaoxiang
 * @date: 2020/12/18 11:08
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class DateUtilTest {

    @Test
    public void getNowText() {
        DateUtil.getNowText();
        System.out.println(DateUtil.CMPP_DATE_FORMATTER);
    }

    @Test
    public void testodayIsWeekendt() {
        DateUtil.todayIsWeekend();
    }

    @Test
    public void timestampToText() {
        DateUtil.timestampToText(System.currentTimeMillis(), null);
    }

    @Test
    public void getTodayZeroTime() {
        DateUtil.getTodayZeroTime();
    }

    @Test
    public void getDayZeroTime() {
        DateUtil.getDayZeroTime("2021-01-01");
    }

    @Test
    public void getTodayLastTime() {
        DateUtil.getTodayLastTime();
    }
}
