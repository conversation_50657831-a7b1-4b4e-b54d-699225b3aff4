# *CMPP接入服务系统功能改进说明*

<style type="text/css">
    h1 { counter-reset: h2counter; }
    h2 { counter-reset: h3counter; }
    h3 { counter-reset: h4counter; }
    h4 { counter-reset: h5counter; }
    h5 { counter-reset: h6counter; }
    h6 { }
    h2:before {
      counter-increment: h2counter;
      content: counter(h2counter) ".\0000a0\0000a0";
    }
    h3:before {
      counter-increment: h3counter;
      content: counter(h2counter) "."
                counter(h3counter) ".\0000a0\0000a0";
    }
    h4:before {
      counter-increment: h4counter;
      content: counter(h2counter) "."
                counter(h3counter) "."
                counter(h4counter) ".\0000a0\0000a0";
    }
    h5:before {
      counter-increment: h5counter;
      content: counter(h2counter) "."
                counter(h3counter) "."
                counter(h4counter) "."
                counter(h5counter) ".\0000a0\0000a0";
    }
    h6:before {
      counter-increment: h6counter;
      content: counter(h2counter) "."
                counter(h3counter) "."
                counter(h4counter) "."
                counter(h5counter) "."
                counter(h6counter) ".\0000a0\0000a0";
    }
</style>

[TOC]

## *系统说明*

### *概述*

系统框架图如下：

```mermaid
    graph TB;
    subgraph 客户端
        client(接入方)
    end
    client-->gateway
    subgraph 接入层
        gateway(CMPP网关)
    end
    gateway-->redis
    gateway-->mysql
    gateway-->mq
    logserver-->mq
    logserver-->redis
    subgraph 数据层
        mysql(MySQL)
        redis(Redis)
        mq(MQ)
    end
```

### 改进功能列表

#### CMPP网关服务

##### 连接登录

+ 增加Redis连接会话管理
+ 增加未送达Delive消息拉取

##### 提交短信

+ 增加短信签名检查
+ 增加模版匹配检查
+ 增加Redis短信消息存储
+ 接入短信V3系统提交短信发送请求
  
##### 短信送达报告

+ 通过与LogServer之间的私有协议交互实现实时报告实时及时送达
+ 通过Redis存储管理短信送达状态

#### Log Server服务

##### 提交短信

+ 增加CMPP渠道来源的短信支持

##### 短信送达报告

+ 增加对CMPP网关私有协议交互通知
