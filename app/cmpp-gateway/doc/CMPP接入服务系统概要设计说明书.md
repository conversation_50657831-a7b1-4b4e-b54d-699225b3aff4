<style type="text/css">
    h1 { counter-reset: h2counter; }
    h2 { counter-reset: h3counter; }
    h3 { counter-reset: h4counter; }
    h4 { counter-reset: h5counter; }
    h5 { counter-reset: h6counter; }
    h6 { }
    h2:before {
      counter-increment: h2counter;
      content: counter(h2counter) ".\0000a0\0000a0";
    }
    h3:before {
      counter-increment: h3counter;
      content: counter(h2counter) "."
                counter(h3counter) ".\0000a0\0000a0";
    }
    h4:before {
      counter-increment: h4counter;
      content: counter(h2counter) "."
                counter(h3counter) "."
                counter(h4counter) ".\0000a0\0000a0";
    }
    h5:before {
      counter-increment: h5counter;
      content: counter(h2counter) "."
                counter(h3counter) "."
                counter(h4counter) "."
                counter(h5counter) ".\0000a0\0000a0";
    }
    h6:before {
      counter-increment: h6counter;
      content: counter(h2counter) "."
                counter(h3counter) "."
                counter(h4counter) "."
                counter(h5counter) "."
                counter(h6counter) ".\0000a0\0000a0";
    }
</style>

# *CMPP接入服务系统概要设计说明书*

---

+ Author: wangjz
+ Create Date: 2021.11.22
+ Update Date: 2021.11.24

---

[TOC]

## *引言*

### *编写目的*

本说明书基于CMPP客户端提交发送短信等相关接入功能的后台服务系统需求进行编写。该服务系统实现响应客户端的CMPP连接请求、提交短信请求、链路检测请求、拆除连接请求，以及推送短信送达请求等功能。
本说明书主要描述CMPP接入服务系统服务的结构设计、主要功能模块设计，包括数据存储设计和出错处理设计等。

### *背景和问题*

随着小花钱包业务的发展，营销短信发送相关的功能与性能需求迅速增加。在这样的业务发展背景下，产生了现有的短信发送平台升级，增加提供CMPP协议接入方式，支持多通路、高可达率的短信通道的服务系统等为项目需求。
针对CMPP协议接入的部分，本文档提出相关的服务解决方案。

### *预期读者*

本说明的预期读者有：

+ 服务需求管理人员
+ 项目经理
+ 服务设计人员
+ 服务开发人员
+ 服务测试人员
+ 文档编写人员
+ 其他有系统或组织关联的人员

### *参考资料*

（略）

---

## *总体设计*

### *系统概述*

CMPP网关作为CMPP客户端的接入服务提供方，通过CMPP2.0/3.0协议（中国移动通信互联网短信网关接口协议，China Mobile Peer to Peer）与外部CMPP客户端交互。CMPP网关服务再通过内部私有协议，将客户端请求送交至Route服务，由Route服务作为中间件，完成必要的短信渠道选路、向Receipt服务递送短信请求、短信消息与链路映射、获取短信送达通知等功能。Route服务处理完成后，处理结果推送至CMPP网关，由CMPP网关推送至对应的客户端。

系统框架图如下：

```mermaid
    graph TB;
    subgraph 客户端
        client(接入方)
    end
    client-->gateway
    subgraph 接入层
        gateway(CMPP网关)
    end
    gateway-->redis
    gateway-->mysql
    gateway-->mq
    logserver-->mq
    logserver-->redis
    subgraph 数据层
        mysql(MySQL)
        redis(Redis)
        mq(MQ)
    end
```

### *功能描述*

系统主要支持的CMPP客户端请求有连接登录、提交短信、链路检测、拆除连接等功能。

#### *连接登录*

（修改Route服务集成进log server）
连接登录功能的数据流图如下：

```mermaid
graph LR;
    CMPP接入方-->|1.CMPP_CONNECT连接请求|CMPP网关;
    CMPP网关-->|2.验证连接信息|MySQL
    CMPP网关-->|3.存储接入方连接Session信息|Redis;
    CMPP网关-->|4.获取连接用户未转发成功的送交短信请求|Redis;
    CMPP网关-->|5.推送CMPP_DELIVER送交短信请求|CMPP接入方;
    CMPP接入方-->|6.送交短信请求回复|CMPP网关;
    CMPP网关-->|7.修改短信请求与连接Session关联信息|Redis;
    CMPP网关-->|8.修改短信请求状态为已完成|Redis;
```

+ 业务流程点如下：
  1. CMPP接入方（客户端）向CMPP网关发出连接请求
  2. CMPP网关验证接入方的账户合法性等，验证失败返回接入方相关错误信息
  3. 验证成功后，CMPP网关将接入方相关Session信息存入Redis
  4. CMPP网关查询Redis，获取连接用户未转发成功的送达短信请求列表
  5. CMPP网关将连接用户未转发成功的送达短信请求列表，编码为CMPP协议中的CMPP_DELIVER请求消息，推送至CMPP接入方
  6. CMPP接入方发送送达短信请求回复至CMPP网关
  7. Route服务接收到送达短信请求回复后，修改Redis中短信请求与连接Session关联信息
  8. Route服务修改Redis中该短信的状态为已完成

#### *提交短信*

提交短信功能的数据流图如下：

```mermaid
graph LR;
    CMPP接入方-->|1.提交CMPP_SUBMIT短信请求|CMPP网关;
    CMPP网关-->|2.存储短信请求与连接Session关联信息|Redis;
    CMPP网关-->|3.写入提交短信请求|MQ;
    CMPP网关-->|4.定时读取网关关联的最新的送交短信请求信息|Redis;
    CMPP网关-->|5.推送CMPP_DELIVER送达短信请求|CMPP接入方;
    CMPP接入方-->|6.回复送达短信请求|CMPP网关;
    CMPP网关-->|7.修改短信请求与连接Session关联信息|Redis;
    CMPP网关-->|8.修改短信请求状态为已完成|Redis;
```

+ 业务流程点如下：
  1. CMPP接入方提交CMPP_SUBMIT短信请求至CMPP网关
  2. CMPP网关将短信请求与连接Session关联信息存储至Redis
  3. CMPP网关写入提交短信请求至MQ
  4. CMPP网关从Redis定时读取网关关联的最新的送交短信请求信息
  5. CMPP网关推送CMPP_DELIVER送达短信请求至CMPP接入方
  6. CMPP接入方回复送达短信请求至CMPP网关
  7. CMPP网关修改短信请求与连接Session关联信息：Route服务接收到送达短信请求回复后，修改Redis中短信请求与连接Session关联信息
  8. CMPP网关修改短信请求状态为已完成：Route服务修改Redis中该短信的状态为已完成

#### *链路检测*

链路检测功能分为：CMPP网关发起的链路检测、CMPP接入方发起的链路检测

##### *CMPP网关发起的链路检测*

CMPP网关发起的链路检测功能的数据流图如下：

```mermaid
graph LR;
    CMPP网关-->|1.发起CMPP_ACTIVE_TEST链路检测请求|CMPP接入方;
    CMPP接入方-->|2.回复链路检测请求|CMPP网关;
    CMPP网关-->|3.更新接入方连接Session信息|Redis;
```

+ 业务流程点如下：
  1. CMPP网关发送CMPP_ACTIVE_TEST链路检测请求至CMPP接入方
  2. CMPP接入方回复链路检测请求至CMPP网关
  3. CMPP网关更新Redis存储的接入方连接Session信息

##### *CMPP接入方发起的链路检测*

CMPP接入方发起的链路检测功能的数据流图如下：

```mermaid
graph LR;
    CMPP接入方-->|1.发起CMPP_ACTIVE_TEST链路检测|CMPP网关;
    CMPP网关-->|2.更新接入方连接Session信息|Redis;
    CMPP网关-->|3.回复链路检测请求|CMPP接入方;
```

+ 业务流程点如下：
  1. CMPP接入方发起CMPP_ACTIVE_TEST链路检测请求至CMPP网关
  2. CMPP网关更新Redis存储的接入方连接Session信息
  3. CMPP网关回复链路检测请求至CMPP接入方

#### *拆除连接*

拆除连接功能的数据流图如下：

```mermaid
graph LR;
    CMPP接入方-->|1.发起CMPP_TERMINATE拆除连接请求|CMPP网关;
    CMPP网关-->|2.删除接入方相关连接Session信息|Redis;
    CMPP网关-->|3.回复拆除连接请求|CMPP接入方;
    CMPP网关-->|4.断开链接|CMPP接入方;
```

+ 业务流程点如下：
  1. CMPP接入方发起CMPP_TERMINATE拆除连接请求至CMPP网关
  2. CMPP网关删除Redis中接入方相关连接Session信息
  3. CMPP网关回复拆除连接请求至CMPP接入方
  4. CMPP网关断开与CMPP接入方的链接连接

### *交互协议*

#### *CMPP接入协议*

用于CMPP接入方与CMPP网关交互，应用中国移动通信互联网短信网关接口协议(China Mobile Peer to Peer, CMPP)

+ 参考文档：
  + 中国移动通信互联网短信网关接口协议 China Mobile Peer to Peer, CMPP

### *数据存储设计*

#### *Redis数据存储*

##### *用户Session*

用户Session主要用于连接用户数目限制与用户某一连接断开后，选择其他连接发送。

单个用户多连接管理，应用Set数据格式存储

| Redis属性 | 数值格式                         | 描述                                   |
| :------- | :------------------------------ | :------------------------------------ |
| Key      | Spectre:CMPPGateway:Session:\<User_Name\>           | User_Name为连接用户名                   |
| Member   | \<Connect_IP\>:\<Connect_Port\> | 用户接入的CMPP网关内网可访问的IP和端口(UDP) |

##### *短信消息与用户Session关联信息*

应用HashSet数据格式存储

| Redis属性 | 数值格式                         | 描述                       |
| :------- | :------------------------------ | :------------------------ |
| Key      | Spectre:CMPPGateway:Msg:\<Msg_Id\> | Msg_Id：CMPP网关生成的消息ID    |
| Field1   | Session                         | 提交消息的CMPP网关信息（用于内部通信）|
| Value1   | \<Connect_IP\>:\<Connect_Port\> | Connect_IP：CMPP网关IP，Connect_Port：CMPP网关端口  |
| Field2   | UserName                        | 提交短信消息的连接用户名            |
| Field3   | MsgContent                      | 短信消息内容                      |
| Field4   | State                           | 短信状态                         |
| Field5   | DestId                          | 目的号码                         |
| Field6   | DestterminalId                  | 短信目标手机                      |
| Field7   | SubmitTime                      | 短信提交时间                      |
| Value7   | \<Time\>                        | YYMMDDHHMM（YY 为年的后两位 00-99，MM：01-12，DD：01-31，HH：00-23，MM：00-59）|
| Field8   | DoneTime                        | 短信完成时间                      |
| Value8   | \<Time\>                        | YYMMDDHHMM                      |

##### *用户最新消息列表状态*

应用HashSet数据格式存储

| Redis属性 | 数值格式               | 描述                 |
| :------- | :-------------------- | :------------------ |
| Key      | Spectre:CMPPGateway:UserMsg:\<User_Name\> | User_Name：连接用户名 |
| Field    | \<Msg_Id\>            | Msg_Id：用户当前待完成的消息ID（CMPP网关生成ID） |
| Value    | \<Msg_Status\>        | Msg_Status：消息状态, 0无状态 1已提交 2已送达 |

##### *网关服务关联的消息列表*

应用HashSet数据格式存储

| Redis属性 | 数值格式                                 | 描述                                  |
| :------- | :-------------------------------------- | :----------------------------------- |
| Key      | Spectre:CMPPGateway:Server:\<Connect_IP\>:\<Connect_Port\> | Connect_IP：CMPP网关IP，Connect_Port：CMPP网关端口 |
| Field    | \<Msg_Id\>                              | Msg_Id：CMPP网关生成的消息ID            |
| Value    | \<Msg_Status\>                          |  Msg_Status：消息状态 0无状态 1已提交 2已送达 |

#### *网关内部通信私有协议*

CMPP网关与Route服务应用基于UDP的Protobuf协议进行通信。协议为二进制报文格式，报文分为消息头和消息体两部分。

消息头固定12字节，格式如下：

| 字段名        | 字节数 | 类型              | 描述                     |
| :----------- | :---- | :--------------- | :----------------------- |
| Total_Length | 4     | Unsigned Integer | 消息总长度(含消息头及消息体) |
| Command_Id   | 4     | Unsigned Integer | 命令或响应类型             |
| Sequence_Id  | 4     | Unsigned Integer | 消息流水号,顺序累加,步长为1,循环使用（一对请求和应答消息的流水号必须相同）|

消息体应用Protobuf格式，具体业务功能的交互协议设计如下：

##### *送达短信通知*

命令请求类型：DELIVER
命令响应类型：DELIVER_RESP

请求消息体定义：

| 字段名     | 类型          | 描述                     |
| :-------- | :----------- | :---------------------- |
| User_Name | string       | 连接用户名                |
| Msg_Id    | string       | 消息ID                   |

响应消息体定义：

| 字段名 | 类型  | 描述                     |
| :---- | :---- | :---------------------- |
| code  | int32 | 错误码。0表示成功 1连接失效 2客户端响应超时 |

##### *Command_Id定义*

| 消息                 | 值         | 描述           |
| :------------------ | :--------- | :------------ |
| DELIVER             | 0x00000001 | 送达短信通知    |
| DELIVER_RESP        | 0x80000001 | 送达短信通知回复 |

#### *MQ数据存储*

应用短信V3现有的MQ消息格式，参考短信V3项目相关说明
