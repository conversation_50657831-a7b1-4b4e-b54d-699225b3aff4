package com.xhqb.spectre.cmpp_gateway;

/*
 * @Author: huangyanxiong
 * @Date: 2021/12/1 19:59
 * @Description:
 */

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.regex.Pattern;

@RunWith(MockitoJUnitRunner.class)
public class SmsServiceTest {


    @Test
    public void test1() {
        String signName = "【小花钱包】";
        String content = "尊敬的[*]，您申请的[*]元尊享花额度已审批通过，详情请登陆小花钱包App查看。";
        String smsContent = "【小花钱包】尊敬的11，您申请的1000元尊享花额度已审批通过，详情请登陆小花钱包App查看。";
        String pattern = "^" + signName + content.replaceAll("\\[\\*]", "\\.{1,20}") + "$";
        Pattern regex = Pattern.compile(pattern);
        String pattern2 = "^黄\\d{1,10}$";
        System.out.println(pattern2);
        Pattern regex2 = Pattern.compile(pattern2);
        System.out.println(pattern);
        System.out.println(regex.matcher(smsContent).matches());
        System.out.println(regex2.matcher("黄111").matches());
    }

}
