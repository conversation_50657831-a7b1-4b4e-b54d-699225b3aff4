package com.xhqb.spectre.cmpp_gateway.server.connect.manager;

import com.xhqb.spectre.cmpp_gateway.CustomSpringContextAware;
import com.xhqb.spectre.cmpp_gateway.cache.BaseDataCache;
import com.xhqb.spectre.cmpp_gateway.config.VenusConfig;
import com.xhqb.spectre.cmpp_gateway.model.entity.CMPPGatewayMsg;
import com.xhqb.spectre.cmpp_gateway.service.RedisService;
import com.xhqb.spectre.cmpp_gateway.service.SmsService;
import com.xhqb.spectre.cmpp_gateway.util.CmppMessageUtils;
import com.xhqb.spectre.common.dal.entity.GatewayUserDO;
import com.zx.sms.codec.cmpp.msg.CmppConnectRequestMessage;
import com.zx.sms.codec.cmpp.msg.CmppDeliverRequestMessage;
import com.zx.sms.connect.manager.EndpointConnector;
import com.zx.sms.connect.manager.EndpointEntity;
import com.zx.sms.connect.manager.EndpointManager;
import com.zx.sms.session.cmpp.SessionLoginManager;
import com.zx.sms.session.cmpp.SessionState;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;

import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Set;


@Slf4j
class CustomCmppSessionLoginManager extends SessionLoginManager {

    private final int MAX_CONNECT_NUMBER = 20;

    public CustomCmppSessionLoginManager(EndpointEntity entity) {
        super(entity);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        if (state == SessionState.Connect) {
            CustomSpringContextAware customSpringContextAware = new CustomSpringContextAware();
            ApplicationContext context = customSpringContextAware.getContext();
            RedisService redisService = context.getBean(RedisService.class);
            redisService.delUseSession(entity.getId());
        }

        Channel ch = ctx.channel();
        if (!entity.getId().equals("server")) {
            if (state == SessionState.Connect) {
                EndpointConnector conn = entity.getSingletonConnector();
                if (conn != null)
                    conn.removeChannel(ch);
                log.warn("Connection closed . {} , connect count : {}", entity, conn == null ? 0 : conn.getConnectionNum());
            } else {
                log.warn("session is not created. the entity is {}.channel remote is {}", entity, ctx.channel().remoteAddress());
            }
        }
        ctx.fireChannelInactive();
    }

    @Override
    protected EndpointEntity queryEndpointEntityByMsg(Object msg) {
        if (msg instanceof CmppConnectRequestMessage) {
            CmppConnectRequestMessage message = (CmppConnectRequestMessage) msg;
            String username = message.getSourceAddr();
            short version = message.getVersion();

            GatewayUserDO user = BaseDataCache.getInstance().getGatewayUser(username);
            if (user == null) {
                log.error("user name: {} invalid", username);
                return null;
            }

            CustomSpringContextAware customSpringContextAware = new CustomSpringContextAware();
            ApplicationContext context = customSpringContextAware.getContext();
            RedisService redisService = context.getBean(RedisService.class);

            try {
                Set<String> sessions = redisService.getUseSession(username);
                if (sessions.size() >= MAX_CONNECT_NUMBER) {
                    log.error("current username: {} over max connect number", username);
                    return null;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            CustomCMPPServerChildEndpointEntity entity = new CustomCMPPServerChildEndpointEntity();
            entity.setId(username);
            entity.setMsgSrc(user.getMsgSrc());
            entity.setSpCode(user.getSpCode());
            entity.setServiceId(user.getServiceId());
            entity.setChartset(StandardCharsets.UTF_8);
            entity.setGroupName(user.getGroupName());
            entity.setUserName(user.getUserName());
            entity.setPassword(user.getPassword());
            entity.setVersion(version);
            entity.setReSendFailMsg(false);
            entity.setLiftTime(30 * 1000);

            EndpointManager.INS.addEndpointEntity(entity);
            return entity;
        }

        return null;
    }

    @Override
    protected void doLoginSuccess(ChannelHandlerContext ctx, EndpointEntity entity, Object msg) {
        if (msg instanceof CmppConnectRequestMessage) {
            try {
                CustomSpringContextAware customSpringContextAware = new CustomSpringContextAware();
                ApplicationContext context = customSpringContextAware.getContext();
                RedisService redisService = context.getBean(RedisService.class);
                SmsService smsService = context.getBean(SmsService.class);

                CmppConnectRequestMessage message = (CmppConnectRequestMessage) msg;
                String username = message.getSourceAddr();
                redisService.setUseSession(username);
                super.doLoginSuccess(ctx, entity, msg);

                // add client remote ip and port to entity
                String host = ((InetSocketAddress) ctx.channel().remoteAddress()).getAddress().getHostAddress();
                int port = ((InetSocketAddress) ctx.channel().remoteAddress()).getPort();

                entity.setHost(host);
                entity.setPort(port);
                EndpointManager.INS.addEndpointEntity(entity);

                log.info("login success: {}", entity);

                //判断是否重推deliver
                VenusConfig venusConfig = context.getBean(VenusConfig.class);
                String ignoreUser = venusConfig.getDeliverIgnoreUser();
                if (StringUtils.isNotBlank(ignoreUser)) {
                    List<String> ignoreUserList = Arrays.asList(ignoreUser.split(","));
                    if (ignoreUserList.contains(username)) {
                        return;
                    }
                }

                List<CMPPGatewayMsg> msgList = redisService.getDeliverUserMsg(username);
                if (msgList.size() == 0) {
                    log.info("no msg to deliver, username: {}", username);
                } else {
                    for (CMPPGatewayMsg cmppGatewayMsg : msgList) {
                        sendCmppDeliverRequestMessage(ctx, cmppGatewayMsg, username, redisService, smsService);
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    private void sendCmppDeliverRequestMessage(ChannelHandlerContext ctx, CMPPGatewayMsg cmppGatewayMsg, String userName, RedisService redisService, SmsService smsService) {
        String msgId = cmppGatewayMsg.getMsgId();
        CmppDeliverRequestMessage requestMessage = CmppMessageUtils.buildCmppDeliverRequestMessage(cmppGatewayMsg);

        int msgStatus = 4;
        ChannelFuture channelFuture = ctx.channel().writeAndFlush(requestMessage);
        if (channelFuture == null) {
            log.error("send deliver message to {} failure, msg id: {}", userName, msgId);
        } else {
            log.info("send deliver message to {} succeed, msg id: {}", userName, msgId);
            if (cmppGatewayMsg.getState().equals("DELIVRD")) {
                redisService.delUserMsg(userName, msgId);
            }
            msgStatus = 3;
        }

        smsService.sendDeliverMessage2MQ(cmppGatewayMsg, msgStatus);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        if (entity.getId().equals("server")) {
            ctx.close();
            return;
        }
        if (state == SessionState.DisConnect) {
            log.error("login error entity : " + entity.toString(), cause);
            ctx.close();
        } else {
            ctx.fireExceptionCaught(cause);
        }
    }
}