package com.xhqb.spectre.cmpp_gateway.util;

import com.xhqb.spectre.cmpp_gateway.cache.BaseDataCache;
import com.xhqb.spectre.cmpp_gateway.model.entity.PhoneIsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Objects;

//编码：utf-8
//性能：每秒解析300万+
//环境：CPU i7-7700K + DDR2400 16G + win10 X64 (Release)

@Slf4j
public class PhoneSearchFastUtil {

    private long[][] prefmap = new long[200][2];//  000-199
    private long[][] phonemap;
    private long[] phoneArr;
    private String[] addrArr;
    private String[] ispArr;
    private static final String PHONE_DAT_FILE_PATH = "phone/qqzeng-phone.dat";
    private static final String PHONE_DAT_HTTP_PATH = "http://pkg.xhdev.xyz/download/zdata/phone/qqzeng-phone.dat";

    private static PhoneSearchFastUtil instance = null;

    private PhoneSearchFastUtil() {
        //先从缓存中读取
        byte[] data = BaseDataCache.getInstance().getPhoneData();
        if (Objects.isNull(data)) {
            //缓存为空，从http获取，获取不到再读取本地文件
            data = getFromHttp();
            if (Objects.isNull(data)) {
                data = getFromFile();
            }
            BaseDataCache.getInstance().refreshPhoneData(data);
        }

        long PrefSize = BytesToLong(data[0], data[1], data[2], data[3]);
        long RecordSize = BytesToLong(data[4], data[5], data[6], data[7]);

        long descLength = BytesToLong(data[8], data[9], data[10], data[11]);
        long ispLength = BytesToLong(data[12], data[13], data[14], data[15]);

        //内容数组
        int descOffset = (int) (16 + PrefSize * 9 + RecordSize * 7);
        String descString = new String(Arrays.copyOfRange(data, descOffset, (int) (descOffset + descLength)));
        addrArr = descString.split("&");

        //运营商数组
        int ispOffset = (int) (16 + PrefSize * 9 + RecordSize * 7 + descLength);
        String ispString = new String(Arrays.copyOfRange(data, ispOffset, (int) (ispOffset + ispLength)));
        ispArr = ispString.split("&");

        //前缀区
        int m = 0;
        for (int k = 0; k < PrefSize; k++) {
            int i = k * 9 + 16;
            int n = data[i] & 0xFF;
            prefmap[n][0] = BytesToLong(data[i + 1], data[i + 2], data[i + 3], data[i + 4]);
            prefmap[n][1] = BytesToLong(data[i + 5], data[i + 6], data[i + 7], data[i + 8]);
            if (m < n) {
                for (; m < n; m++) {
                    prefmap[m][0] = 0;
                    prefmap[m][1] = 0;
                }
            }
            m++;
        }

        //索引区
        phoneArr = new long[(int) RecordSize];
        phonemap = new long[(int) RecordSize][2];
        for (int i = 0; i < RecordSize; i++) {
            int p = 16 + (int) PrefSize * 9 + (i * 7);
            phoneArr[i] = BytesToLong(data[p], data[1 + p], data[2 + p], data[3 + p]);
            phonemap[i][0] = BytesToLong(data[4 + p], data[5 + p], (byte) 0, (byte) 0);
            phonemap[i][1] = BytesToLong(data[6 + p], (byte) 0, (byte) 0, (byte) 0);
        }
    }

    public static synchronized PhoneSearchFastUtil getInstance() {
        if (instance == null)
            instance = new PhoneSearchFastUtil();
        return instance;
    }

    public String Get(String phone) {
        int pref = Integer.parseInt(phone.substring(0, 3));
        int val = Integer.parseInt(phone.substring(0, 7));
        int low = (int) prefmap[pref][0], high = (int) prefmap[pref][1];
        if (high == 0) {
            return "";
        }
        int cur = low == high ? low : BinarySearch(low, high, val);
        if (cur != -1) {
            return addrArr[(int) phonemap[cur][0]] + "|" + ispArr[(int) phonemap[cur][1]];
        } else {
            return "";
        }
    }

    public PhoneIsp getIsp(String phone) {
        String isp = PhoneSearchFastUtil.getInstance().Get(phone);
        if (StringUtils.isEmpty(isp)) {
            return null;
        }
        String[] isps = isp.split("\\|");
        if (isps.length != 6) {
            return null;
        }
        return PhoneIsp.buildPhoneIsp(isps, phone);
    }

    private int BinarySearch(int low, int high, long k) {
        int M = 0;
        while (low <= high) {
            int mid = (low + high) >> 1;

            long phoneNum = phoneArr[mid];
            if (phoneNum >= k) {
                M = mid;
                if (mid == 0) {
                    break;
                }
                high = mid - 1;
            } else
                low = mid + 1;
        }
        return M;
    }

    private long BytesToLong(byte a, byte b, byte c, byte d) {
        return (a & 0xFFL) | ((b << 8) & 0xFF00L) | ((c << 16) & 0xFF0000L) | ((d << 24) & 0xFF000000L);

    }

    private byte[] getFromHttp() {
        try {
            URL url = new URL(PHONE_DAT_HTTP_PATH);
            return download(url);
        } catch (IOException e) {
            log.error("getHttpFile IOException", e);
        }
        return null;
    }

    private byte[] getFromFile() {
        try {
            URL url = PhoneSearchFastUtil.class.getClassLoader().getResource(PHONE_DAT_FILE_PATH);
            Path path = Paths.get(url.getPath());
            return Files.readAllBytes(path);
        } catch (IOException e) {
            log.error("PhoneSearchFastUtil IOException", e);
        }
        return null;
    }

    private byte[] download(URL url) throws IOException {
        URLConnection uc = url.openConnection();
        int len = uc.getContentLength();
        try (InputStream is = new BufferedInputStream(uc.getInputStream())) {
            byte[] data = new byte[len];
            int offset = 0;
            while (offset < len) {
                int read = is.read(data, offset, data.length - offset);
                if (read < 0) {
                    break;
                }
                offset += read;
            }
            if (offset < len) {
                throw new IOException(
                    String.format("Read %d bytes; expected %d", offset, len));
            }
            return data;
        }
    }
}