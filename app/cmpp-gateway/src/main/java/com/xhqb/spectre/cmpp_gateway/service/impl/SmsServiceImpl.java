package com.xhqb.spectre.cmpp_gateway.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.github.wujun234.uid.impl.CachedUidGenerator;
import com.xhqb.spectre.cmpp_gateway.cache.BaseDataCache;
import com.xhqb.spectre.cmpp_gateway.cache.TplMappingItem;
import com.xhqb.spectre.cmpp_gateway.config.VenusConfig;
import com.xhqb.spectre.cmpp_gateway.constant.CommonConstant;
import com.xhqb.spectre.cmpp_gateway.constant.LimitConstants;
import com.xhqb.spectre.cmpp_gateway.enums.CfgTypeLimitCheckEnum;
import com.xhqb.spectre.cmpp_gateway.enums.LimitCheckResultEnum;
import com.xhqb.spectre.cmpp_gateway.enums.RespCodeEnum;
import com.xhqb.spectre.cmpp_gateway.exception.BizException;
import com.xhqb.spectre.cmpp_gateway.model.entity.CMPPGatewayMsg;
import com.xhqb.spectre.cmpp_gateway.model.entity.PhoneIsp;
import com.xhqb.spectre.cmpp_gateway.model.result.BaseResult;
import com.xhqb.spectre.cmpp_gateway.service.FrequencyLimitService;
import com.xhqb.spectre.cmpp_gateway.service.SmsService;
import com.xhqb.spectre.cmpp_gateway.util.ChannelUtils;
import com.xhqb.spectre.cmpp_gateway.util.PhoneNumberUtils;
import com.xhqb.spectre.common.dal.dto.SmsTypeDisableData;
import com.xhqb.spectre.common.dal.dto.TplData;
import com.xhqb.spectre.common.dal.entity.GatewayUserDO;
import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.cmpp_gateway.util.PhoneSearchFastUtil;
import com.xhqb.spectre.common.dal.dto.AppSendLimitData;
import com.xhqb.spectre.common.mq.ChannelCode;
import com.xhqb.spectre.common.mq.DeliverMessageMQ;
import com.xhqb.spectre.common.mq.MessageMQ;
import com.xhqb.spectre.common.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/29 19:16
 * @Description:
 */
@Service
@Slf4j
public class SmsServiceImpl implements SmsService {

    @Autowired
    private VenusConfig venusConfig;

    @Resource
    private MQTemplate<String> mqTemplate;

    @Autowired
    private FrequencyLimitService frequencyLimitService;

    /**
     * 雪花ID生成器
     */
    @Autowired
    private CachedUidGenerator cachedUidGenerator;

    private static final PhoneSearchFastUtil finder = PhoneSearchFastUtil.getInstance();

    private static final Pattern smsPattern = Pattern.compile("^(【[\\w|\u4e00-\u9fa5]{1,8}】)");

    @Override
    public void sendDeliverMessage2MQ(CMPPGatewayMsg msg, int msgStatus) {
        DeliverMessageMQ deliverMessageMQ = DeliverMessageMQ.builder()
                .msgId(msg.getMsgId())
                .userName(msg.getUserName())
                .submitTime(msg.getSubmitTime())
                .destTerminalId(msg.getDestterminalId())
                .doneTime(msg.getDoneTime())
                .smsStatus(msg.getState())
                .messageStatus(msgStatus)
                .deliveredTime((int) (System.currentTimeMillis() / 1000))
                .build();
        BaseResult<DeliverMessageMQ> mqDeliverResult = BaseResult.success(deliverMessageMQ);
        String messageMQTopic = venusConfig.getMqDeliverSMSMessage();
        DeliverMessageMQ<BaseBodyMessage> bodyMessage = mqDeliverResult.getData();
        dispatchSMSDeliverMessage2QAsync(messageMQTopic, bodyMessage);
    }

    @Override
    public Boolean dispatchSMSMessage2QAsync(String messageMQTopic, MessageMQ<BaseBodyMessage> bodyMessage) {
        mqTemplate.sendAsync(messageMQTopic, JSONObject.toJSONString(bodyMessage)).thenAccept(msgId -> {
            log.info("sms send object {}, MsgID: {}", bodyMessage, msgId);
        }).exceptionally(e -> {
            log.error(e.getMessage(), e);
            return null;
        });
        return Boolean.TRUE;
    }

    @Override
    public void dispatchSMSDeliverMessage2QAsync(String messageMQTopic, DeliverMessageMQ<BaseBodyMessage> bodyMessage) {
        mqTemplate.createMessage(messageMQTopic, JSONObject.toJSONString(bodyMessage)).key(bodyMessage.getMsgId()).sendAsync();
    }

    /**
     * 构建短信发送的MQ消息体
     *
     * @param mobile
     * @param smsContent
     * @param username
     * @return
     */
    @Override
    public BaseResult<MessageMQ> buildMessageMQ(String mobile, String smsContent, String username) {
        try {
            //参数校验
            GatewayUserDO gatewayUser = BaseDataCache.getInstance().getGatewayUser(username);
            checkParam(mobile, smsContent, username, gatewayUser);

            //校验短信内容
            TplData tplData = checkSmsContent(smsContent, gatewayUser);

            //黑名单
            checkMobileBlack(tplData.getSmsTypeCode(), mobile, tplData.getAppCode());

            //获取手机号归属地信息
            PhoneIsp phoneIsp = getPhoneIsp(mobile);

            //校验模板是否可用
            checkTpl(tplData, phoneIsp);

            //获取可用渠道列表
            List<ChannelCode> channelList = getChannelList(tplData, phoneIsp);

            //限流检查
            checkSendLimit(tplData.getAppCode(), mobile, tplData.getSmsTypeCode(), smsContent);

            //组装MQ消息体
            MessageMQ message = buildMessage(tplData, phoneIsp, smsContent, channelList, username);

            return BaseResult.success(message);
        } catch (BizException e) {
            log.warn("buildMessageMQ BizException, code:{}, msg: {}", e.getCode(), e.getMsg());
            return BaseResult.error(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("buildMessageMQ Exception", e);
            return BaseResult.systemError();
        }
    }

    /**
     * 请求参数校验
     *
     * @param mobile
     * @param smsContent
     * @param username
     * @param gatewayUserDO
     */
    private void checkParam(String mobile, String smsContent, String username, GatewayUserDO gatewayUserDO) {
        if (!PhoneNumberUtils.isXhMobileNum(mobile)) {
            throw new BizException("手机号非法");
        }
        if (StringUtils.isBlank(smsContent)) {
            throw new BizException("短信内容为空");
        }
        if (Objects.isNull(username)) {
            throw new BizException("网关用户名为空");
        }
        if (Objects.isNull(gatewayUserDO)) {
            throw new BizException("网关用户不存在，用户名：" + username);
        }
    }

    /**
     * 短信内容校验
     * 1、签名校验；2、模板校验
     *
     * @param smsContent
     * @param gatewayUser
     * @return
     */
    private TplData checkSmsContent(String smsContent, GatewayUserDO gatewayUser) {
        //签名校验
        String signName = getSignName(smsContent);
        if (StringUtils.isEmpty(signName)) {
            throw new BizException(RespCodeEnum.SIGN_EMPTY);
        }
        Integer signId = BaseDataCache.getInstance().getSignIdByName(signName);
        Set<Integer> signIdSet = gatewayUser.getSignIdSet();
        if (Objects.isNull(signId) || CollectionUtils.isEmpty(signIdSet) || !signIdSet.contains(signId)) {
            throw new BizException(RespCodeEnum.SIGN_NOT_SUPPORTED);
        }

        //模板校验
        TplData tplData = BaseDataCache.getInstance().getTplInfo(gatewayUser.getTplCode(), signId);
        if (Objects.isNull(tplData)) {
            throw new BizException(RespCodeEnum.TPL_NOT_FOUND);
        }
        if (gatewayUser.getIsCheckTpl() == CommonConstant.TPL_CHECK_CLOSE) {
            //不开启模板文案检测，直接返回
            return tplData;
        }
        Integer tplId = tplData.getId();
        List<TplMappingItem> list = BaseDataCache.getInstance().getTplMappingList(tplId);
        if (CollectionUtils.isEmpty(list)) {
            throw new BizException(RespCodeEnum.TPL_NOT_APPLY, "网关模板未配置支持的短信文案，模板ID：" + tplId);
        }
        boolean isMatch = false;
        String tplContent = excludeSignName(smsContent, signName);
        for (TplMappingItem item : list) {
            String content = item.getTplContent();
            if (item.isContainsParam()) {
                //文案包含变量，进行正则匹配
                Pattern pattern = item.getPattern();
                if (pattern.matcher(tplContent).matches()) {
                    isMatch = true;
                    break;
                }
            } else {
                if (content.equals(tplContent)) {
                    isMatch = true;
                    break;
                }
            }
        }
        if (!isMatch) {
            throw new BizException(RespCodeEnum.TPL_NOT_APPLY, "短信内容未进行备案，模板ID：" + tplId + "，短信内容：" + smsContent);
        }
        //TODO 敏感内容校验

        return tplData;
    }

    /**
     * 黑名单检查
     *
     * @param smsType
     * @param mobile
     * @param appCode
     */
    private void checkMobileBlack(String smsType, String mobile, String appCode) {
        boolean isBlack = BaseDataCache.getInstance().containsBlack(smsType, mobile, appCode);
        if (isBlack) {
            throw new BizException(RespCodeEnum.MOBILE_BLACK);
        }
    }

    /**
     * 应用限流检查
     *
     * @param appCode
     * @param phone
     * @param smsType
     * @param smsContent
     */
    private void checkSendLimit(String appCode, String phone, String smsType, String smsContent) {
        //不需要限流，直接返回
        if (!isNeedLimit(appCode, phone)) {
            return;
        }
        //限流检查
        List<AppSendLimitData> appSendLimitList = getAppLimitList(appCode, smsType);
        for (AppSendLimitData limitData : appSendLimitList) {
            try {
                LimitCheckResultEnum resultCode = frequencyLimitService.checkSendLimitFrequencyFrom(appCode, limitData, phone, smsType, smsContent);
                if (resultCode != LimitCheckResultEnum.APP_SEND_LIMIT_PASS) {
                    log.warn("frequency check not pass, statusCode: {}, statusMsg: {}", resultCode.getStatusCode(), resultCode.getStatusMsg());
                    throw new BizException(RespCodeEnum.SEND_LIMIT);
                }
            } catch (BizException e) {
                throw e;
            } catch (Exception e) {
                log.warn("checkSendLimitFrequencyFrom exception", e);
            }
        }
    }

    /**
     * 构建默认限流数据
     *
     * @param appCode
     * @param smsType
     * @return
     */
    private List<AppSendLimitData> buildDefaultSendLimit(String appCode, String smsType) {
        List<AppSendLimitData> appSendLimitList = new ArrayList<>();
        if (CommonUtil.isVerifySms(smsType)) {
            AppSendLimitData verifyLimit = new AppSendLimitData(appCode, CfgTypeLimitCheckEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_VERIFY.getCfgType(), String.valueOf(LimitConstants.DEFAULT_MOBILE_MAX_VERIFY));
            appSendLimitList.add(verifyLimit);
        }
        AppSendLimitData halfM = new AppSendLimitData(appCode, CfgTypeLimitCheckEnum.APP_SEND_LIMIT_MOBILE_MAZX_COUNT_HALF_MINUTE.getCfgType(), String.valueOf(LimitConstants.DEFAULT_MOBILE_MAX_HALF_MINUTE));
        appSendLimitList.add(halfM);

        AppSendLimitData hour = new AppSendLimitData(appCode, CfgTypeLimitCheckEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_HOUR.getCfgType(), String.valueOf(LimitConstants.DEFAULT_MOBILE_MAX_HOUR));
        appSendLimitList.add(hour);

        AppSendLimitData day = new AppSendLimitData(appCode, CfgTypeLimitCheckEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_DAY.getCfgType(), String.valueOf(LimitConstants.DEFAULT_MOBILE_MAX_DAY));
        appSendLimitList.add(day);

        AppSendLimitData sameDay = new AppSendLimitData(appCode, CfgTypeLimitCheckEnum.APP_SEND_LIMIT_MOBILE_SAME_CONTENT_MAX_CYCLE.getCfgType(), String.valueOf(LimitConstants.DEFAULT_MOBILE_SAME_MAX_CYCLE));
        appSendLimitList.add(sameDay);

        return appSendLimitList;
    }

    /**
     * 判断是否需要限流
     *
     * @param appCode
     * @param phone
     * @return
     */
    private boolean isNeedLimit(String appCode, String phone) {
        //不开启限流
        if (!venusConfig.getSendLimitEnabled()) {
            return false;
        }
        //白名单，不进行限流
        if (BaseDataCache.getInstance().containsWhite(appCode, CommonConstant.WHITE_APP_SEND_LIMIT, phone)) {
            return false;
        }
        return true;
    }

    /**
     * 获取限流配置
     *
     * @param appCode
     * @param smsType
     * @return
     */
    private List<AppSendLimitData> getAppLimitList(String appCode, String smsType) {
        List<AppSendLimitData> appSendLimitList = BaseDataCache.getInstance().getAppLimitList(appCode);
        if (CollectionUtils.isEmpty(appSendLimitList)) {
            appSendLimitList = buildDefaultSendLimit(appCode, smsType);
        }
        return appSendLimitList;
    }

    /**
     * 获取手机号归属地信息
     *
     * @param mobile
     * @return
     */
    private PhoneIsp getPhoneIsp(String mobile) {
        PhoneIsp phoneIsp = finder.getIsp(mobile);
        if (Objects.isNull(phoneIsp)) {
            log.warn("getIsp failed, phone: {}", mobile);
            phoneIsp = PhoneIsp.buildDefault(mobile);
        }
        return phoneIsp;
    }

    /**
     * 模板校验
     *
     * @param tplData
     * @param phoneIsp
     */
    private void checkTpl(TplData tplData, PhoneIsp phoneIsp) {
        //判断短信类型是否被屏蔽
        List<SmsTypeDisableData> list = BaseDataCache.getInstance().getDisabledList(tplData.getSmsTypeCode());
        if (ChannelUtils.isSmsTypeDisabled(list, phoneIsp)) {
            throw new BizException(RespCodeEnum.SMS_TYPE_DISABLED);
        }

        //判断模板是否被屏蔽
        if (ChannelUtils.isTplDisabled(tplData, phoneIsp)) {
            throw new BizException(RespCodeEnum.TPL_PHONE_DISABLED);
        }
    }

    /**
     * 获取可用的渠道列表
     *
     * @param phoneIsp
     * @return
     */
    private List<ChannelCode> getChannelList(TplData tplData, PhoneIsp phoneIsp) {
        List<ChannelCode> channelList = ChannelUtils.getChannelList(tplData, phoneIsp);
        if (CollectionUtils.isEmpty(channelList)) {
            throw new BizException(RespCodeEnum.NO_AVAILABLE_CHANNEL);
        }
        return channelList;
    }

    /**
     * 构建MQ消息体
     *
     * @param tplData
     * @param phoneIsp
     * @param smsContent
     * @param channelList
     * @param username
     * @return
     */
    private MessageMQ buildMessage(TplData tplData, PhoneIsp phoneIsp, String smsContent, List<ChannelCode> channelList, String username) {
        return MessageMQ.builder()
                .appCode(tplData.getAppCode())
                .phone(phoneIsp.getPhone())
                .province(phoneIsp.getProvince())
                .city(phoneIsp.getCity())
                .isp(phoneIsp.getIsp())
                .smsCode(tplData.getSmsTypeCode())
                .channelCodeSet(channelList)
                .orderId(String.valueOf(cachedUidGenerator.getUID()))
                .signName(tplData.getSignName())
                .sliceId(CommonConstant.DEFAULT_SLICE_NUMBER)
                .sendType(CommonConstant.SEND_TYPE_CURRENT)
                .receiveTime(String.valueOf(System.currentTimeMillis()))
                .tplCode(tplData.getCode())
                .content(excludeSignName(smsContent, tplData.getSignName()))
                .reqSrc(CommonConstant.DEFAULT_REQ_SRC)
                //requestId由调用方设置
                .requestId("")
                .gatewayUserName(username)
                .build();
    }

    /**
     * 短信内容排除掉签名名称
     *
     * @param smsContent
     * @param signName
     * @return
     */
    private String excludeSignName(String smsContent, String signName) {
        return smsContent.substring(signName.length());
    }

    /**
     * 从短信内容提取签名
     *
     * @param smsContent
     * @return
     */
    private String getSignName(String smsContent) {
        Matcher matcher = smsPattern.matcher(smsContent);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}
