package com.xhqb.spectre.cmpp_gateway.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.Optional;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class PhoneIsp {

    private String phone;

    private String province;

    private String city;

    private String postCode;

    private String phoneCode;

    private String cityCode;

    private String isp;

    public static PhoneIsp buildPhoneIsp(String[] isps, String phone) {
        if (isps.length != 6) {
            return null;
        }
        return Optional.of(isps).map(item -> PhoneIsp.builder()
                .province(item[0])
                .city(item[1])
                .postCode(item[2])
                .phoneCode(item[3])
                .cityCode(item[4])
                .isp(item[5])
                .phone(phone)
                .build()).orElse(null);
    }

    /**
     * 默认为深圳移动
     * @param phone
     * @return
     */
    public static PhoneIsp buildDefault(String phone) {
        return PhoneIsp.builder()
                .province("广东")
                .city("深圳")
                .postCode("518000")
                .phoneCode("0755")
                .cityCode("440300")
                .isp("移动")
                .phone(phone)
                .build();
    }
}
