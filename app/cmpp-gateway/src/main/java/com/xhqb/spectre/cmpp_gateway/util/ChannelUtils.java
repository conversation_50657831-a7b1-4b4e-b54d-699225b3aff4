package com.xhqb.spectre.cmpp_gateway.util;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.cmpp_gateway.cache.BaseDataCache;
import com.xhqb.spectre.cmpp_gateway.constant.CommonConstant;
import com.xhqb.spectre.cmpp_gateway.model.entity.PhoneIsp;
import com.xhqb.spectre.common.dal.dto.*;
import com.xhqb.spectre.common.enums.TplChannelAreaFilterEnum;
import com.xhqb.spectre.common.mq.ChannelCode;
import com.xhqb.spectre.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.Calendar;

/**
 * 渠道处理逻辑
 */
@Slf4j
public class ChannelUtils {

    /**
     * 判断是否命中模版禁用
     *
     * @param tplData
     * @param phoneIsp
     */
    public static boolean isTplDisabled(TplData tplData, PhoneIsp phoneIsp) {
        List<TplDisableData> disableData = tplData.getDisableInfoList();
        if (CollectionUtils.isEmpty(disableData)) {
            return false;
        }
        for (TplDisableData item : disableData) {
            if (isMatch(item.getStartTime(), item.getEndTime(), item.getAreaList(), item.getIspList(), phoneIsp)) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param startTime
     * @param endTime
     * @param areaDataList
     * @param ispList
     * @param phoneIsp
     * @return
     */
    private static boolean isMatch(String startTime, String endTime, List<AreaData> areaDataList, List<String> ispList, PhoneIsp phoneIsp) {
        Date startDate = DateUtil.stringToDate(startTime);
        Date endDate = DateUtil.stringToDate(endTime);
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return false;
        }
        try {
            Date currDate = new Date();
            if (startDate.before(currDate) && endDate.after(currDate)
                    && isIspMatch(ispList, phoneIsp.getIsp())
                    && isAreaMatch(areaDataList, phoneIsp)) {
                // 在时间范围内，判断运营商和地域是否匹配
                return true;
            }
        } catch (Exception e) {
            log.error("ChannelUtils isMatch exception", e);
        }
        return false;
    }

    /**
     * 判断是否匹配（支持新的屏蔽类型）
     *
     * @param disableData
     * @param phoneIsp
     * @return
     */
    private static boolean isMatchWithType(ChannelAccountDisableData disableData, PhoneIsp phoneIsp) {
        Date startDate = DateUtil.stringToDate(disableData.getStartTime());
        Date endDate = DateUtil.stringToDate(disableData.getEndTime());
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return false;
        }
        try {
            Date currDate = new Date();
            if (startDate.before(currDate) && endDate.after(currDate)
                    && isIspMatch(disableData.getIspList(), phoneIsp.getIsp())) {
                // 在时间范围内，判断屏蔽类型
                Integer disableType = disableData.getDisableType();
                if (disableType == null || disableType.equals(com.xhqb.spectre.common.constant.DisableTypeConstant.TYPE_FULL_PERIOD)) {
                    // 类型1：整个时间段屏蔽
                    return isAreaMatch(disableData.getAreaList(), phoneIsp);
                } else if (disableType.equals(com.xhqb.spectre.common.constant.DisableTypeConstant.TYPE_PERIOD_TIME)) {
                    // 类型2：固定时间段屏蔽
                    if (isInPeriodTime(disableData.getPeriodStartTime(), disableData.getPeriodEndTime())) {
                        return isAreaMatch(disableData.getAreaList(), phoneIsp);
                    }
                }
            }
        } catch (Exception e) {
            log.error("ChannelUtils isMatchWithType exception", e);
        }
        return false;
    }

    /**
     * 判断当前时间是否在指定的时间段内
     *
     * @param periodStartTime 时间段开始时间（HH:mm格式）
     * @param periodEndTime   时间段结束时间（HH:mm格式）
     * @return
     */
    private static boolean isInPeriodTime(String periodStartTime, String periodEndTime) {
        if (periodStartTime == null || periodEndTime == null) {
            return false;
        }
        try {
            Calendar now = Calendar.getInstance();
            int currentHour = now.get(Calendar.HOUR_OF_DAY);
            int currentMinute = now.get(Calendar.MINUTE);
            int currentMinutes = currentHour * 60 + currentMinute;

            String[] startParts = periodStartTime.split(":");
            String[] endParts = periodEndTime.split(":");
            int startMinutes = Integer.parseInt(startParts[0]) * 60 + Integer.parseInt(startParts[1]);
            int endMinutes = Integer.parseInt(endParts[0]) * 60 + Integer.parseInt(endParts[1]);

            return currentMinutes >= startMinutes && currentMinutes < endMinutes;
        } catch (Exception e) {
            log.error("ChannelUtils isInPeriodTime error: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否命中运营商
     *
     * @return
     */
    private static boolean isIspMatch(List<String> ispList, String phoneIsp) {
        if (CollectionUtils.isEmpty(ispList)) {
            return false;
        }
        for (String isp : ispList) {
            if (CommonConstant.DEFAULT_ISP.equalsIgnoreCase(isp) || phoneIsp.contains(isp)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取可用渠道信息列表
     *
     * @param tplData  模版信息
     * @param phoneIsp 手机号码归属地
     * @return
     */
    public static List<ChannelCode> getChannelList(TplData tplData, PhoneIsp phoneIsp) {
        List<ChannelCode> channelCodeList = new ArrayList<>();
        List<TplChannelData> tplChannelDatalist = tplData.getChannelInfoList();
        if (CollectionUtils.isEmpty(tplChannelDatalist)) {
            return Collections.emptyList();
        }

        BaseDataCache dataCache = BaseDataCache.getInstance();
        for (TplChannelData tplChannelData : tplChannelDatalist) {
            List<ChannelAccountDisableData> disableDataList = dataCache.getDisabledList(tplChannelData.getChannelAccountId());
            if (isChannelDisabled(disableDataList, phoneIsp)) {
                //渠道禁用，跳过
                continue;
            }
            //匹配运营商
            if (!isIspMatch(tplChannelData.getIspList(), phoneIsp.getIsp())) {
                continue;
            }
            //匹配地域
            Integer areaFilterType = tplChannelData.getAreaFilterType();
            if (!TplChannelAreaFilterEnum.checkFilterType(areaFilterType)) {
                //地域过滤类型非法，跳过
                log.warn("模版渠道地域过滤类型配置错误：{}", JSON.toJSONString(tplChannelData));
                continue;
            }
            List<AreaData> areaList = tplChannelData.getAreaList();
            if ((TplChannelAreaFilterEnum.CONTAINS.getType().equals(areaFilterType) && isAreaMatch(areaList, phoneIsp))
                    || (TplChannelAreaFilterEnum.NOT_CONTAINS.getType().equals(areaFilterType) && !isAreaMatch(areaList, phoneIsp))) {
                ChannelCode channelCode = ChannelCode.buildChannelCode(tplChannelData);
                channelCode.setMsgContent("");
                channelCodeList.add(channelCode);
            }
        }
        return channelCodeList;
    }

    /**
     * 判断是否命中渠道禁用配置
     *
     * @param disableDataList
     * @param phoneIsp
     * @return
     */
    private static boolean isChannelDisabled(List<ChannelAccountDisableData> disableDataList, PhoneIsp phoneIsp) {
        if (CollectionUtils.isEmpty(disableDataList)) {
            return false;
        }
        for (ChannelAccountDisableData disableData : disableDataList) {
            // 使用新的判断逻辑，支持屏蔽类型
            if (isMatchWithType(disableData, phoneIsp)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否命中短信类型屏蔽配置
     *
     * @param disableDataList
     * @param phoneIsp
     * @return
     */
    public static boolean isSmsTypeDisabled(List<SmsTypeDisableData> disableDataList, PhoneIsp phoneIsp) {
        if (CollectionUtils.isEmpty(disableDataList)) {
            return false;
        }
        for (SmsTypeDisableData item : disableDataList) {
            if (isMatch(item.getStartTime(), item.getEndTime(), item.getAreaList(), item.getIspList(), phoneIsp)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断区域是否匹配
     * 如果为空则返回false
     *
     * @param areaDataList
     * @param phoneIsp
     * @return
     */
    private static boolean isAreaMatch(List<AreaData> areaDataList, PhoneIsp phoneIsp) {
        if (CollectionUtils.isEmpty(areaDataList)) {
            return false;
        }
        for (AreaData areaData : areaDataList) {
            String province = areaData.getProvinceShortName();
            String city = areaData.getCityShortName();
            if (StringUtils.isEmpty(province)) {
                //省份为空，异常配置，跳过
                continue;
            }
            if (CommonConstant.DEFAULT_AREA.equalsIgnoreCase(province)) {
                //匹配全国
                return true;
            }
            if (province.equals(phoneIsp.getProvince())) {
                //省份一致，比较城市
                if (StringUtils.isEmpty(city) || CommonConstant.DEFAULT_AREA.equalsIgnoreCase(city)) {
                    //匹配全省
                    return true;
                } else if (city.equals(phoneIsp.getCity())) {
                    //匹配城市
                    return true;
                }
            }
        }
        return false;
    }

}
