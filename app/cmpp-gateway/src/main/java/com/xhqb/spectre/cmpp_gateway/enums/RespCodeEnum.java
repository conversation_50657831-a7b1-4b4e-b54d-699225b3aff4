package com.xhqb.spectre.cmpp_gateway.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Getter
@AllArgsConstructor
public enum RespCodeEnum {

    /**
     * 请求成功：0
     */
    SUCCESS(0, "请求处理成功", 0),

    /**
     * 前端异常：1000-9999
     */
    PARAM_ERROR(1000, "请求参数异常", 1),

    /**
     * 业务场景异常：10000-19999
     */
    SIGN_EMPTY(10001, "签名为空", 21),
    SIGN_NOT_SUPPORTED(10002, "签名不支持", 21),
    TPL_NOT_FOUND(10003, "未找到匹配的模板", 3),
    TPL_NOT_APPLY(10004, "短信模板未报备", 3),
    SEND_LIMIT(10005, "短信发送限流", 8),
    TPL_PHONE_DISABLED(10006, "短信模板被屏蔽", 9),
    NO_AVAILABLE_CHANNEL(10007, "无可用渠道", 72),
    SMS_TYPE_DISABLED(10008, "短信类型被屏蔽", 9),
    MOBILE_BLACK(10009, "黑名单", 69),

    /**
     * 后端服务异常：20000-29999
     */
    SYSTEM_ERROR(20001, "系统异常", 9);

    private Integer code;
    private String msg;

    /**
     * Cmpp submit response result。result值参考Cmpp标准
     */
    private Integer submitResult;

    private static final Map<Integer, RespCodeEnum> RESULT_MAP = new HashMap<>();

    static {
        for (RespCodeEnum codeEnum : values()) {
            RESULT_MAP.put(codeEnum.getCode(), codeEnum);
        }
    }

    public static RespCodeEnum getByCode(Integer code) {
        return RESULT_MAP.get(code);
    }

    public static Integer getSubmitResult(Integer code) {
        RespCodeEnum codeEnum = getByCode(code);
        return Optional.ofNullable(codeEnum).map(RespCodeEnum::getSubmitResult).orElse(9);
    }
}
