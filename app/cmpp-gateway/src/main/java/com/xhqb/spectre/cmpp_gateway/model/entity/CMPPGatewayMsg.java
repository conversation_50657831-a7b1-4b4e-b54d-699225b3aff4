package com.xhqb.spectre.cmpp_gateway.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@ToString
public class CMPPGatewayMsg {
    private String msgId;
    private String userName;
    private String session;
    private String msgContent;
    private String state;
    private String destId;
    private String destterminalId;
    private String submitTime;
    private String doneTime;

    public CMPPGatewayMsg() {
        this.msgId = "";
        this.userName = "";
        this.session = "";
        this.msgContent = "";
        this.state = "";
        this.destId = "";
        this.destterminalId = "";
        this.submitTime = "";
        this.doneTime = "";
    }
}