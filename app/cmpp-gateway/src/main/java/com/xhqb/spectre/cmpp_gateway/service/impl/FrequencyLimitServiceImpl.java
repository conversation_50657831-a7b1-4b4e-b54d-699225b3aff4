package com.xhqb.spectre.cmpp_gateway.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xhqb.spectre.cmpp_gateway.constant.LimitConstants;
import com.xhqb.spectre.cmpp_gateway.enums.CfgTypeLimitCheckEnum;
import com.xhqb.spectre.cmpp_gateway.enums.LimitCheckResultEnum;
import com.xhqb.spectre.cmpp_gateway.service.FrequencyLimitService;
import com.xhqb.spectre.cmpp_gateway.service.MobileLimitService;
import com.xhqb.spectre.common.dal.dto.AppSendLimitData;
import com.xhqb.spectre.common.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


/**
 * 频率检查
 * <p>
 * 后续可能针对实际需求进行优化
 * 目前频率是以30s、1H、1D、24小时内相同内容、24小时内验证码 作为处理键值的
 */
@Service
@Slf4j
public class FrequencyLimitServiceImpl implements FrequencyLimitService {

    @Resource
    private MobileLimitService mobileLimitService;

    /**
     * 短信内容md5缓存（如果短信内容不带参数，则md5值相同，缓存起来可以减少md5的计算）
     */
    private final Cache<String, String> md5Cache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    /**
     * 限流+白名单
     * 1、 实质应该白名单通过后、不做任何频率限制
     * 2、 白名单未通过的、进行五种条件的频率检查
     * 3、 频率检查通过后，可以发送短信
     * <p>
     * App 数量限制 - windows： 1 days，  key ： appMaxCountDay + appCode， value：smsCount
     * Mobile30s的量：    - windows： 30s，     key :  mobileMaxCountHalfMinute + appCode + phone, value: List.size < send_limit_value
     * Mobile1小时量      - windows： 1h，       key :  mobileMaxCountHour + appCode + phone, value: List.size < send_limit_value
     * Mobile1天          - windows： 1d，       key :  mobileMaxCountDay + appCode + phone, value: List.size < send_limit_value
     * Mobile相同内容:  - windows:  1d,     key :  mobileSameContentMaxCycle + appCode + md5(smsContent) + phone, value: List.size < send_limit_value
     */
    @Override
    public LimitCheckResultEnum checkSendLimitFrequencyFrom(String appCode, AppSendLimitData limitData, String phone, String smsType, String smsContent) {
        CfgTypeLimitCheckEnum cfgTypeLimitCheckEnum = CfgTypeLimitCheckEnum.getByCfgType(limitData.getLimitKey());
        if (Objects.isNull(cfgTypeLimitCheckEnum)) {
            return LimitCheckResultEnum.APP_SEND_LIMIT_PASS;
        }
        return doCheckSendLimitFrequency(appCode, phone, cfgTypeLimitCheckEnum, limitData, smsType, smsContent);
    }

    /**
     * 减去redis中的计数
     *
     * @param appCode
     * @param cfgType
     * @param phone
     * @param smsType
     * @param smsContent
     */
    @Override
    public void subtractSMSFrequencyLimitCount(String appCode, String cfgType, String phone, String smsType, String smsContent) {
        CfgTypeLimitCheckEnum cfgTypeLimitCheckEnum = CfgTypeLimitCheckEnum.getByCfgType(cfgType);
        if (Objects.isNull(cfgTypeLimitCheckEnum)) {
            return;
        }
        switch (cfgTypeLimitCheckEnum) {
            case APP_SEND_LIMIT_MOBILE_MAZX_COUNT_HALF_MINUTE:
                String mobileLimitKey30S = buildLimitCacheKey(LimitConstants.SMS_TIMEOUT_REDIS_KEY30S, appCode, phone);
                mobileLimitService.postSubtractAppFrequencyLimit(mobileLimitKey30S);
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_HOUR:
                String mobileLimitKey1H = buildLimitCacheKey(LimitConstants.SMS_TIMEOUT_REDIS_KEY1H, appCode, phone);
                mobileLimitService.postSubtractAppFrequencyLimit(mobileLimitKey1H);
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_DAY:
                String mobileLimitKey1D = buildLimitCacheKey(LimitConstants.SMS_TIMEOUT_REDIS_KEY1D, appCode, phone);
                mobileLimitService.postSubtractAppFrequencyLimit(mobileLimitKey1D);
                break;
            case APP_SEND_LIMIT_MOBILE_SAME_CONTENT_MAX_CYCLE:
                String mobileSameSMSKey = LimitConstants.SMS_SAME_CONTENT_REDIS_KEY24H + ":" + appCode + ":" + getContentMd5(smsContent) + ":" + phone;
                mobileLimitService.postSubtractAppFrequencyLimit(mobileSameSMSKey);
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_VERIFY:
                if (CommonUtil.isVerifySms(smsType)) {
                    String mobileVerify = buildLimitCacheKey(LimitConstants.VERIFY_TIMEOUT_REDIS_KEY1D, appCode, phone);
                    mobileLimitService.postSubtractAppFrequencyLimit(mobileVerify);
                }
            default:
                break;
        }
    }

    private LimitCheckResultEnum doCheckSendLimitFrequency(String appCode, String phone, CfgTypeLimitCheckEnum cfgType, AppSendLimitData appSendLimit, String smsType, String smsContent) {
        //初始化默认频率检查变量
        LimitCheckResultEnum limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_PASS;
        String limitCheckKey = buildLimitCheckKey(appCode, phone);
        Integer limitValue = Integer.parseInt(appSendLimit.getLimitValue());
        switch (cfgType) {
            case APP_SEND_LIMIT_MOBILE_MAZX_COUNT_HALF_MINUTE:
                if (!mobileLimitService.checkMobileMaxCountHalfMinute(limitCheckKey, limitValue)) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_MAZX_COUNT_HALF_MINUTE;
                }
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_HOUR:
                if (!mobileLimitService.checkMobileMaxCountHour(limitCheckKey, limitValue)) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_HOUR;
                }
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_DAY:
                if (!mobileLimitService.checkMobileMaxCountDay(limitCheckKey, limitValue)) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_DAY;
                }
                break;
            case APP_SEND_LIMIT_MOBILE_SAME_CONTENT_MAX_CYCLE:
                String mobileSameSMSKey = appCode + ":" + getContentMd5(smsContent) + ":" + phone;
                if (!mobileLimitService.checkMobileSameContentMaxCycle(mobileSameSMSKey, limitValue)) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_SAME_CONTENT_MAX_CYCLE;
                }
                break;
            case APP_SEND_LIMIT_MOBILE_MAX_COUNT_VERIFY:
                if (CommonUtil.isVerifySms(smsType) && !mobileLimitService.checkVerifyCountDay(limitCheckKey, limitValue)) {
                    limitCheckResultEnum = LimitCheckResultEnum.APP_SEND_LIMIT_MOBILE_MAX_COUNT_VERIFY;
                }
            default:
                break;
        }
        return limitCheckResultEnum;
    }

    private String buildLimitCheckKey(String appCode, String phone) {
        return appCode + ":" + phone;
    }

    private String buildLimitCacheKey(String prefix, String appCode, String phone) {
        return prefix + ":" + buildLimitCheckKey(appCode, phone);
    }

    /**
     * 获取短信内容的md5值
     *
     * @param smsContent
     * @return
     */
    private String getContentMd5(String smsContent) {
        String md5 = md5Cache.getIfPresent(smsContent);
        if (StringUtils.isNotEmpty(md5)) {
            return md5;
        }
        md5 = DigestUtils.md5DigestAsHex(smsContent.getBytes(StandardCharsets.UTF_8));
        md5Cache.put(smsContent, md5);
        return md5;
    }
}

