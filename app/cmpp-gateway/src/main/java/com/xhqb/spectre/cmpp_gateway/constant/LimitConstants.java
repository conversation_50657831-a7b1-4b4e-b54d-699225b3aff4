package com.xhqb.spectre.cmpp_gateway.constant;

public final class LimitConstants {
    //30s
    public static final String SMS_TIMEOUT_REDIS_KEY30S = "spectre-gateway:limit:30S";
    //1小时
    public static final String SMS_TIMEOUT_REDIS_KEY1H = "spectre-gateway:limit:1H";
    //1天
    public static final String SMS_TIMEOUT_REDIS_KEY1D = "spectre-gateway:limit:1D";
    //相同短信内容
    public static final String SMS_SAME_CONTENT_REDIS_KEY24H = "spectre-gateway:limit:CYCLE";
    // 验证码
    public static final String VERIFY_TIMEOUT_REDIS_KEY1D = "spectre-gateway:limit:verify:1D";

    // 同一手机号码一天最多发送20条验证码
    public static final int DEFAULT_MOBILE_MAX_VERIFY = 20;

    // 同一手机号码30秒最多接收10条短信
    public static final int DEFAULT_MOBILE_MAX_HALF_MINUTE = 10;

    // 同一手机号码1小时最多接收20条短信
    public static final int DEFAULT_MOBILE_MAX_HOUR = 20;

    // 同一手机号码1天最多接收50条短信
    public static final int DEFAULT_MOBILE_MAX_DAY = 50;

    // 同一手机号码一天最多接收50条相同的短信
    public static final int DEFAULT_MOBILE_SAME_MAX_CYCLE = 50;
}
