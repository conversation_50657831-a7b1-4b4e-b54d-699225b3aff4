package com.xhqb.spectre.cmpp_gateway.server;

import com.xhqb.spectre.cmpp_gateway.CustomSpringContextAware;
import com.xhqb.spectre.cmpp_gateway.model.entity.CMPPGatewayMsg;
import com.xhqb.spectre.cmpp_gateway.service.RedisService;
import com.xhqb.spectre.cmpp_gateway.service.SmsService;
import com.xhqb.spectre.cmpp_gateway.util.CmppMessageUtils;
import com.xhqb.spectre.common.msg.Message;
import com.xhqb.spectre.common.protobuf.DeliverProto;
import com.google.protobuf.InvalidProtocolBufferException;
import com.zx.sms.codec.cmpp.msg.CmppDeliverRequestMessage;
import com.zx.sms.common.util.ChannelUtil;
import com.zx.sms.connect.manager.EndpointEntity;
import com.zx.sms.connect.manager.EndpointManager;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.socket.DatagramPacket;
import io.netty.util.concurrent.GenericFutureListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;

import java.util.List;

@Slf4j
public class UdpProtoServerChannelInitializer extends SimpleChannelInboundHandler<DatagramPacket> {

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, DatagramPacket packet) throws Exception {
        CustomSpringContextAware customSpringContextAware = new CustomSpringContextAware();
        ApplicationContext context = customSpringContextAware.getContext();
        RedisService redisService = context.getBean(RedisService.class);
        SmsService smsService = context.getBean(SmsService.class);

        byte[] bytes = new byte[packet.content().readableBytes()];
        packet.content().getBytes(0, bytes);
        Message req = new Message(bytes);
        if (req.getCommandId() == Message.DELIVER_REQUEST) {
            byte[] bodyBuffer = req.getBodyBuffer();

            int code = 0;
            try {
                DeliverProto.Deliver deliver = DeliverProto.Deliver.parseFrom(bodyBuffer);
                String username = deliver.getUsername();
                String msgId = null;
                if(deliver.hasMsgid()) {
                    msgId = deliver.getMsgid();
                }

                log.info("udp read, username: {}, msg id: {}", username, msgId);
                if (StringUtils.isEmpty(msgId)) {
                    List<CMPPGatewayMsg> msgList = redisService.getDeliverUserMsg(username);
                    if (msgList.size() == 0) {
                        log.info("no msg to deliver, username: {}", username);
                    }
                    else {
                        for (int i = 0; i < msgList.size(); i++) {
                            CMPPGatewayMsg msg = msgList.get(i);
                            if (StringUtils.isEmpty(msg.getUserName())) {
                                log.info("redis gateway msg invalid, msg id : {}", msg.getMsgId());
                                continue;
                            }
                            msg.setUserName(username);
                            sendCmppDeliverRequestMessage(msg, username, redisService, smsService);
                        }
                    }
                }
                else {
                    CMPPGatewayMsg msg = redisService.getMsg(msgId);
                    if (StringUtils.isEmpty(msg.getUserName())) {
                        log.info("redis gateway msg invalid, msg id : {}", msg.getMsgId());
                    }
                    else {
                        msg.setUserName(username);
                        sendCmppDeliverRequestMessage(msg, username, redisService, smsService);
                    }
                }
            } catch (InvalidProtocolBufferException e) {
                log.error("InvalidProtocolBufferException", e);
                code = 1;
            }

            Message resp = new Message(Message.DELIVER_RESPONSE, req.getSequenceNo());
            DeliverProto.DeliverResp.Builder builder = DeliverProto.DeliverResp.newBuilder();
            builder.setCode(code).build();
            DeliverProto.DeliverResp deliverResp = builder.build();
            byte[] respBodyBuffer = deliverResp.toByteArray();
            resp.setBodyBuffer(respBodyBuffer);
            DatagramPacket respPacket = new DatagramPacket(Unpooled.copiedBuffer(resp.getBuffer()), packet.sender());
            ctx.writeAndFlush(respPacket);
        } else {
            log.error("unknown command id: {}", req.getCommandId());
        }
    }

    private void sendCmppDeliverRequestMessage(CMPPGatewayMsg msg, String userName, RedisService redisService, SmsService smsService) {

        String msgId = msg.getMsgId();
        CmppDeliverRequestMessage requestMessage = CmppMessageUtils.buildCmppDeliverRequestMessage(msg);

        GenericFutureListener listener = future -> {
            if (!future.isSuccess()) {
                log.warn("send deliver message to {} failure, promise is null, msg id: {}", userName, msgId);

                int msgStatus = 4;
                smsService.sendDeliverMessage2MQ(msg, msgStatus);
            } else {
                log.info("send deliver message to {} succeed, msg id: {}", userName, msgId);
                if (msg.getState().equals("DELIVRD")) {
                    redisService.delUserMsg(userName, msgId);
                }

                int msgStatus = 3;
                smsService.sendDeliverMessage2MQ(msg, msgStatus);
            }
        };

        EndpointEntity e = EndpointManager.INS.getEndpointEntity(userName);
        if (e != null) {
            ChannelFuture promise = ChannelUtil.asyncWriteToEntity(e, requestMessage, listener);
            if (promise == null) {
                log.warn("send deliver message to {} failure, msg id: {}", userName, msgId);

                int msgStatus = 4;
                smsService.sendDeliverMessage2MQ(msg, msgStatus);
            }
        }
        else {
            log.error("no available connections, send deliver message to {} failure, msg id: {}", userName, msgId);
        }
    }
}
