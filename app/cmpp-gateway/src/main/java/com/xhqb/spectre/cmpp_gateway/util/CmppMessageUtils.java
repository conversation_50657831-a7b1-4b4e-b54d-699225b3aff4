package com.xhqb.spectre.cmpp_gateway.util;

import com.xhqb.spectre.cmpp_gateway.model.entity.CMPPGatewayMsg;
import com.zx.sms.codec.cmpp.msg.CmppDeliverRequestMessage;
import com.zx.sms.codec.cmpp.msg.CmppReportRequestMessage;
import com.zx.sms.common.util.DefaultSequenceNumberUtil;
import com.zx.sms.common.util.MsgId;

public class CmppMessageUtils {

    public static CmppDeliverRequestMessage buildCmppDeliverRequestMessage(CMPPGatewayMsg msg) {
        String msgId = msg.getMsgId();
        CmppDeliverRequestMessage requestMessage = new CmppDeliverRequestMessage();
        requestMessage.setMsgId(new MsgId());
        requestMessage.setSrcterminalId(msg.getDestterminalId());
        requestMessage.setDestId(msg.getDestId());
        CmppReportRequestMessage reportRequestMessage = new CmppReportRequestMessage();
        reportRequestMessage.setMsgId(new MsgId(msgId));
        reportRequestMessage.setDestterminalId(msg.getDestterminalId());
        reportRequestMessage.setSubmitTime(msg.getSubmitTime());
        reportRequestMessage.setDoneTime(msg.getDoneTime());
        reportRequestMessage.setStat(msg.getState());
        reportRequestMessage.setSmscSequence(DefaultSequenceNumberUtil.getSequenceNo());
        requestMessage.setReportRequestMessage(reportRequestMessage);
        return requestMessage;
    }
}
