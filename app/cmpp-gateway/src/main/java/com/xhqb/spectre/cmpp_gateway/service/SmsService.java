package com.xhqb.spectre.cmpp_gateway.service;


import com.xhqb.spectre.cmpp_gateway.model.entity.CMPPGatewayMsg;
import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.cmpp_gateway.model.result.BaseResult;
import com.xhqb.spectre.common.mq.DeliverMessageMQ;
import com.xhqb.spectre.common.mq.MessageMQ;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/29 19:16
 * @Description:
 */
public interface SmsService {

    void sendDeliverMessage2MQ(CMPPGatewayMsg msg, int msgStatus);

    Boolean dispatchSMSMessage2QAsync(String messageMQTopic, MessageMQ<BaseBodyMessage> bodyMessage);

    void dispatchSMSDeliverMessage2QAsync(String messageMQTopic, DeliverMessageMQ<BaseBodyMessage> bodyMessage);

    BaseResult<MessageMQ> buildMessageMQ(String mobile, String smsContent, String username);

}
