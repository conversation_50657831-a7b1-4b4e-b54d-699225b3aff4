package com.xhqb.spectre.cmpp_gateway;

import com.xhqb.spectre.cmpp_gateway.job.BaseDataCacheJob;
import com.xhqb.spectre.cmpp_gateway.server.CmppServer;
import com.xhqb.spectre.cmpp_gateway.server.UdpProtoServer;
import com.xhqb.spectre.cmpp_gateway.util.NetUtils;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.annotation.Resource;
import java.util.concurrent.locks.LockSupport;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@MapperScan("com.xhqb.spectre.common.dal.mapper")
@EnableScheduling
@Slf4j
public class CmppGatewayApplication implements CommandLineRunner {

    @Value("${server.cmpp.port}")
    private int cmppPort;

    @Value("${server.udp.port}")
    private int udpPort;

    private String host;

    @Resource
    private BaseDataCacheJob baseDataCacheJob;

    public static void main(String[] args) {
        SpringApplication.run(CmppGatewayApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        host = NetUtils.getHostAddress();
        if (host == null) {
            log.error("get host address failure");
            return;
        }
        //加载配置缓存
        baseDataCacheJob.execute();

        final CmppServer cmppServer = new CmppServer(cmppPort);
        cmppServer.start();
        final UdpProtoServer udpProtoServer = new UdpProtoServer();
        udpProtoServer.run(udpPort);
        LockSupport.park();
    }

    public int getUdpPort() {
        return udpPort;
    }

    public String getHost() {
        return host;
    }

}
