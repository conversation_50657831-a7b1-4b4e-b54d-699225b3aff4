package com.xhqb.spectre.cmpp_gateway.aspect;

import com.google.common.collect.ImmutableList;
import com.xhqb.spectre.cmpp_gateway.annotation.RateLimit;
import com.xhqb.spectre.cmpp_gateway.enums.RateLimitTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.io.Serializable;
import java.lang.reflect.Method;


@Aspect
@Configuration
@Slf4j
public class RateLimitAspect {

    @Resource
    private RedisTemplate<String, Serializable> limitRedisTemplate;

    @Around("execution(* com.xhqb.spectre.cmpp_gateway.service.impl.MobileLimitServiceImpl.check*(..))")
    public Object interceptor(ProceedingJoinPoint pjp) {
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        Method method = signature.getMethod();
        Object[] args = pjp.getArgs();
        if (args.length != 2) {
            return false;
        }
        String key;
        RateLimit limitAnnotation = method.getAnnotation(RateLimit.class);
        RateLimitTypeEnum limitType = limitAnnotation.limitType();
        int limitPeriod = limitAnnotation.period();
        int limitCount = Integer.parseInt(args[1].toString());
        if (limitType == RateLimitTypeEnum.CUSTOMER) {
            key = args[0].toString();
        } else {
            key = StringUtils.upperCase(method.getName());
        }
        ImmutableList<String> keys = ImmutableList.of(StringUtils.joinWith(":", limitAnnotation.prefix(), key));
        try {
            Number count = limitRedisTemplate.execute(RedisLuaScript.arIncr, keys, limitCount, limitPeriod);
            return count != null && count.intValue() <= limitCount;
        } catch (Throwable e) {
            if (e instanceof RuntimeException) {
                throw new RuntimeException(e.getLocalizedMessage());
            }
            throw new RuntimeException("RateLimitAspect server exception");
        }
    }

}
