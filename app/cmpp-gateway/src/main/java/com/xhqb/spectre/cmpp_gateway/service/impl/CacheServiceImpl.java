package com.xhqb.spectre.cmpp_gateway.service.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.cmpp_gateway.cache.BaseDataCache;
import com.xhqb.spectre.cmpp_gateway.cache.TplMappingItem;
import com.xhqb.spectre.cmpp_gateway.service.CacheService;
import com.xhqb.spectre.common.constant.DisableTypeConstant;
import com.xhqb.spectre.common.dal.dto.*;
import com.xhqb.spectre.common.dal.entity.*;
import com.xhqb.spectre.common.dal.mapper.*;
import com.xhqb.spectre.common.dal.query.AppQuery;
import com.xhqb.spectre.common.dal.query.SignQuery;
import com.xhqb.spectre.common.utils.CommonUtil;
import com.xhqb.spectre.common.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/30 15:00
 * @Description:
 */
@Service
public class CacheServiceImpl implements CacheService {

    @Autowired
    private AppMapper appMapper;

    @Autowired
    private TplMapper tplMapper;

    @Autowired
    private SignMapper signMapper;

    @Autowired
    private TplChannelMapper tplChannelMapper;

    @Autowired
    private TplDisableMapper tplDisableMapper;

    @Autowired
    private ChannelAccountDisableMapper accountDisableMapper;

    @Autowired
    private MobileWhiteMapper mobileWhiteMapper;

    @Autowired
    private AppSendLimitMapper appSendLimitMapper;

    @Autowired
    private GatewayTplMappingMapper tplMappingMapper;

    @Autowired
    private GatewayUserMapper gatewayUserMapper;

    @Autowired
    private SmsTypeDisableMapper smsTypeDisableMapper;

    @Autowired
    private MobileBlackMapper mobileBlackMapper;

    /**
     * 刷新业务应用缓存
     */
    @Override
    public void refreshAppInfo() {
        List<AppDO> appDOList = appMapper.selectByQuery(new AppQuery());
        List<AppData> dataList = appDOList.stream().map(this::buildAppData).collect(Collectors.toList());
        BaseDataCache.getInstance().refreshApp(dataList);
    }

    /**
     * 刷新模板信息缓存
     */
    @Override
    public void refreshTplInfo() {
        List<TplDO> tplDOList = tplMapper.selectAllEnabled();
        List<TplData> dataList = tplDOList.stream().map(this::buildTplData).collect(Collectors.toList());
        BaseDataCache.getInstance().refreshTpl(dataList);
    }

    /**
     * 刷新渠道屏蔽信息缓存
     */
    @Override
    public void refreshChannelAccountDisable() {
        List<ChannelAccountDisableDO> accountDisableDOList = accountDisableMapper.selectAll();

        Map<Integer, List<ChannelAccountDisableData>> dataMap = new ConcurrentHashMap<>();
        for (ChannelAccountDisableDO item : accountDisableDOList) {
            if (new Date().after(DateTime.of(item.getMaskEndTime()))) {
                //过滤掉结束时间小于当前时间的数据
                continue;
            }
            Integer channelAccountId = item.getChannelAccountId();
            ChannelAccountDisableData data = buildDisableData(item);
            if (dataMap.containsKey(channelAccountId)) {
                dataMap.get(channelAccountId).add(data);
            } else {
                List<ChannelAccountDisableData> tmpList = new ArrayList<>();
                tmpList.add(data);
                dataMap.put(channelAccountId, tmpList);
            }
        }
        BaseDataCache.getInstance().refreshChannelDisabledMap(dataMap);
    }

    /**
     * 刷新白名单缓存
     */
    @Override
    public void refreshMobileWhite() {
        List<MobileWhiteDO> whiteDOList = mobileWhiteMapper.loadAllWhiteInfo();
        Set<String> whiteSet = whiteDOList.stream().map(this::buildMobileWhiteValue).collect(Collectors.toSet());
        BaseDataCache.getInstance().refreshWhite(whiteSet);
    }

    /**
     * 刷新应用限流缓存
     */
    @Override
    public void refreshAppSendLimit() {
        List<AppSendLimitDO> sendLimitDOList = appSendLimitMapper.selectAllEnabled();
        Map<String, List<AppSendLimitData>> dataMap = new ConcurrentHashMap<>();
        for (AppSendLimitDO item : sendLimitDOList) {
            String appCode = item.getAppCode();
            AppSendLimitData data = AppSendLimitData.builder()
                    .appCode(appCode)
                    .limitKey(item.getSendLimitKey())
                    .limitValue(item.getSendLimitValue())
                    .build();
            if (dataMap.containsKey(appCode)) {
                dataMap.get(appCode).add(data);
            } else {
                List<AppSendLimitData> tmpList = new ArrayList<>();
                tmpList.add(data);
                dataMap.put(appCode, tmpList);
            }
        }
        BaseDataCache.getInstance().refreshAppLimit(dataMap);
    }

    /**
     * 刷新网关模板文案映射缓存
     */
    @Override
    public void refreshTplMapping() {
        List<GatewayTplMappingDO> tplMappingDOList = tplMappingMapper.selectAll();
        Map<Integer, List<TplMappingItem>> map = new ConcurrentHashMap<>();
        for (GatewayTplMappingDO item : tplMappingDOList) {
            Integer tplId = item.getTplId();
            TplMappingItem mappingItem = TplMappingItem.build(item);
            if (map.containsKey(tplId)) {
                map.get(tplId).add(mappingItem);
            } else {
                List<TplMappingItem> tmpList = new ArrayList<>();
                tmpList.add(mappingItem);
                map.put(tplId, tmpList);
            }
        }
        BaseDataCache.getInstance().refreshTplMapping(map);
    }

    /**
     * 刷新网关用户缓存
     */
    @Override
    public void refreshGatewayUser() {
        List<GatewayUserDO> list = gatewayUserMapper.selectAll();
        BaseDataCache.getInstance().refreshGatewayUser(list);
    }

    /**
     * 刷新签名缓存
     */
    @Override
    public void refreshSign() {
        List<SignDO> list = signMapper.selectByQuery(new SignQuery());
        BaseDataCache.getInstance().refreshSign(list);
    }

    /**
     * 刷新短信类型屏蔽缓存
     */
    @Override
    public void refreshSmsTypeDisable() {
        List<SmsTypeDisableDO> smsTypeDisableDOList = smsTypeDisableMapper.selectAll();

        Map<String, List<SmsTypeDisableData>> dataMap = new ConcurrentHashMap<>();
        for (SmsTypeDisableDO item : smsTypeDisableDOList) {
            if (new Date().after(DateUtil.intToDate(item.getEndTime()))) {
                //过滤掉结束时间小于当前时间的数据
                continue;
            }
            String smsTypeCode = item.getSmsTypeCode();
            SmsTypeDisableData data = buildDisableData(item);
            if (dataMap.containsKey(smsTypeCode)) {
                dataMap.get(smsTypeCode).add(data);
            } else {
                List<SmsTypeDisableData> tmpList = new ArrayList<>();
                tmpList.add(data);
                dataMap.put(smsTypeCode, tmpList);
            }
        }
        BaseDataCache.getInstance().refreshSmsTypeDisabledMap(dataMap);
    }

    /**
     * 刷新黑名单缓存
     */
    @Override
    public void refreshBlack() {
        Set<String> allList = new HashSet<>();
        // 分批获取黑名单
        int pageSize = 1000;
        int offset = 0;
        while (true) {
            List<MobileBlackDO> blackList = mobileBlackMapper.selectByPage(offset, pageSize);
            if (blackList.size() > 0) {
                allList.addAll(blackList.stream().map(this::buildBlackCacheValue).collect(Collectors.toSet()));
            }
            if (blackList.size() < pageSize) {
                break;
            }
            offset += pageSize;
        }
        BaseDataCache.getInstance().refreshBlack(allList);
    }

    private String buildBlackCacheValue(MobileBlackDO item) {
        String smsTypeCode = item.getSmsTypeCode();
        String mobile = item.getMobile();
        String appCode = item.getAppCode();

        String value = smsTypeCode + "_" + mobile;
        if (StringUtils.isBlank(appCode)) {
            return value;
        }
        return value + "_" + appCode;
    }

    private AppData buildAppData(AppDO appDO) {
        return AppData.builder()
                .code(appDO.getCode())
                .name(appDO.getName())
                .skey(appDO.getSkey())
                .cbUrl(appDO.getCbUrl())
                .build();
    }

    private TplData buildTplData(TplDO tplDO) {
        //签名
        SignDO signDO = BaseDataCache.getInstance().getSign(tplDO.getSignId());
        if (Objects.isNull(signDO)) {
            signDO = signMapper.selectByPrimaryKey(tplDO.getSignId());
        }
        //渠道信息
        List<TplChannelData> channelList = tplChannelMapper.selectByTplId(tplDO.getId()).stream().map(item -> TplChannelData.builder()
                .channelAccountId(item.getChannelAccountId())
                .channelTplId(item.getChannelTplId())
                .ispList(CommonUtil.strToList(item.getIsps()))
                .areaFilterType(item.getAreaFilterType())
                .areaList(StringUtils.isEmpty(item.getAreas()) ? Collections.emptyList() : JSON.parseArray(item.getAreas(), AreaData.class))
                .weight(item.getWeight())
                .remark(item.getRemark())
                .tplContent(item.getTplContent())
                .build())
                .collect(Collectors.toList());
        //模板屏蔽信息
        List<TplDisableData> disableList = tplDisableMapper.selectByTplId(tplDO.getId()).stream().map(item -> TplDisableData.builder()
                .ispList(CommonUtil.strToList(item.getIsps()))
                .areaList(StringUtils.isEmpty(item.getAreas()) ? Collections.emptyList() : JSON.parseArray(item.getAreas(), AreaData.class))
                .startTime(DateUtil.intToString(item.getStartTime()))
                .endTime(DateUtil.intToString(item.getEndTime()))
                .build())
                .collect(Collectors.toList());

        return TplData.builder()
                .id(tplDO.getId())
                .code(tplDO.getCode())
                .smsTypeCode(tplDO.getSmsTypeCode())
                .appCode(tplDO.getAppCode())
                .signId(tplDO.getSignId())
                .signCode(signDO.getCode())
                .signName(signDO.getName())
                .content(tplDO.getContent())
                .paramCount(CommonUtil.getTplParamCount(tplDO.getContent()))
                .channelInfoList(channelList)
                .disableInfoList(disableList)
                .build();
    }

    private ChannelAccountDisableData buildDisableData(ChannelAccountDisableDO item) {
        return ChannelAccountDisableData.builder()
                .channelAccountId(item.getChannelAccountId())
                .ispList(CommonUtil.strToList(item.getIsps()))
                .areaList(StringUtils.isEmpty(item.getAreas()) ? Collections.emptyList() : JSON.parseArray(item.getAreas(), AreaData.class))
                .startTime(DateTime.of(item.getMaskStartTime()).toString())
                .endTime(DateTime.of(item.getMaskEndTime()).toString())
                .disableType(item.getDisableType() != null ? item.getDisableType() : DisableTypeConstant.DEFAULT_TYPE)
                .periodStartTime(item.getPeriodStartTime())
                .periodEndTime(item.getPeriodEndTime())
                .build();
    }

    private SmsTypeDisableData buildDisableData(SmsTypeDisableDO item) {
        return SmsTypeDisableData.builder()
                .smsTypeCode(item.getSmsTypeCode())
                .ispList(CommonUtil.strToList(item.getIsps()))
                .areaList(StringUtils.isEmpty(item.getAreas()) ? Collections.emptyList() : JSON.parseArray(item.getAreas(), AreaData.class))
                .startTime(DateUtil.intToString(item.getStartTime()))
                .endTime(DateUtil.intToString(item.getEndTime()))
                .build();
    }

    private String buildMobileWhiteValue(MobileWhiteDO mobileWhiteDO) {
        return mobileWhiteDO.getAppCode() + "_" + mobileWhiteDO.getCfgType() + "_" + mobileWhiteDO.getMobile();
    }
}
