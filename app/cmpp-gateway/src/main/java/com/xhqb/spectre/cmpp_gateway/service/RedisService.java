package com.xhqb.spectre.cmpp_gateway.service;

import com.xhqb.spectre.cmpp_gateway.model.entity.CMPPGatewayMsg;
import com.xhqb.spectre.cmpp_gateway.util.NetUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RedisService {

    private final String CMPP_GATEWAY_KEY_PREFIX = "Spectre:CMPPGateway:";
    private final String CMPP_GATEWAY_MSG_KEY_PREFIX = CMPP_GATEWAY_KEY_PREFIX + "Msg:";
    private final String CMPP_GATEWAY_USER_MSG_KEY_PREFIX = CMPP_GATEWAY_KEY_PREFIX + "UserMsg:";
    private final String CMPP_GATEWAY_USER_SEESION_PREFIX = CMPP_GATEWAY_KEY_PREFIX + "Session:";

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Value("${server.udp.port}")
    private int udpPort;

    public void setUserMsg(CMPPGatewayMsg msg) {
        HashMap map = new HashMap();
        String key = CMPP_GATEWAY_MSG_KEY_PREFIX + msg.getMsgId();

        map.put("Session", msg.getSession());
        map.put("UserName", msg.getUserName());
        map.put("MsgContent", msg.getMsgContent());
        map.put("State", msg.getState());
        map.put("DestId", msg.getDestId());
        map.put("DestterminalId", msg.getDestterminalId());
        map.put("SubmitTime", msg.getSubmitTime());
        map.put("DoneTime", msg.getDoneTime());
        stringRedisTemplate.opsForHash().putAll(key, map);
        stringRedisTemplate.expire(key, 3, TimeUnit.DAYS);

        key = CMPP_GATEWAY_USER_MSG_KEY_PREFIX + msg.getUserName();
        map = new HashMap();
        map.put(msg.getMsgId(), "1");
        stringRedisTemplate.opsForHash().putAll(key, map);
        stringRedisTemplate.expire(key, 3, TimeUnit.DAYS);
    }

    public void delUserMsg(String userName, String msgId) {
        String key = CMPP_GATEWAY_MSG_KEY_PREFIX + msgId;
        stringRedisTemplate.delete(key);

        key = CMPP_GATEWAY_USER_MSG_KEY_PREFIX + userName;
        stringRedisTemplate.opsForHash().delete(key, msgId);
    }

    public List<CMPPGatewayMsg> getDeliverUserMsg(String userName) {
        String key = CMPP_GATEWAY_USER_MSG_KEY_PREFIX + userName;
        Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(key);
        List<CMPPGatewayMsg> list = new ArrayList<>();
        for (Object k : entries.keySet()) {
            if (k instanceof String) {
                String msgId = (String) k;
                String v = (String) entries.get(k);
                if (v.equals("2")) {
                    CMPPGatewayMsg msg = getMsg(msgId);
                    if (msg != null) {
                        list.add(msg);
                    }
                    else {
                        stringRedisTemplate.opsForHash().delete(key, k);
                    }
                }
                else {
                    String msgKey = CMPP_GATEWAY_MSG_KEY_PREFIX + msgId;
                    if (!stringRedisTemplate.hasKey(msgKey)) {
                        stringRedisTemplate.opsForHash().delete(key, k);
                    }
                }
            }
        }
        return list;
    }

    public CMPPGatewayMsg getMsg(String msgId) {
        String key = CMPP_GATEWAY_MSG_KEY_PREFIX + msgId;
        Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(key);
        log.info("redis gateway msg: {}", entries);
        if (entries.isEmpty()) {
            return null;
        }

        CMPPGatewayMsg msg = new CMPPGatewayMsg();
        msg.setMsgId(msgId);
        for (Object k : entries.keySet()) {
            if(k instanceof String) {
                if(k.equals("Session")) {
                    String session = (String) entries.get(k);
                    msg.setSession(session != null ? session : "");
                }
                else if (k.equals("UserName")) {
                    String userName = (String) entries.get(k);
                    msg.setUserName(userName != null ? userName : "");
                }
                else if (k.equals("DestId")) {
                    String destId = (String) entries.get(k);
                    msg.setDestId(destId != null ? destId : "");
                }
                else if (k.equals("DestterminalId")) {
                    String destterminalId = (String) entries.get(k);
                    msg.setDestterminalId(destterminalId != null ? destterminalId : "");
                }
                else if (k.equals("SubmitTime")) {
                    String submitTime = (String) entries.get(k);
                    msg.setSubmitTime(submitTime != null ? submitTime : "");
                }
                else if (k.equals("DoneTime")) {
                    String doneTime = (String) entries.get(k);
                    msg.setDoneTime(doneTime != null ? doneTime : "");
                }
                else if (k.equals("State")) {
                    String state = (String) entries.get(k);
                    msg.setState(state != null ? state : "");
                }
            }
        }

        return msg;
    }

    public void setUseSession(String userName) throws Exception {
        String key = CMPP_GATEWAY_USER_SEESION_PREFIX + userName;
        String session = NetUtils.getHostAddress() + ":" + udpPort;
        stringRedisTemplate.opsForSet().add(key, session);
        stringRedisTemplate.expire(key, 2, TimeUnit.MINUTES);
    }

    public Set<String> getUseSession(String userName) throws Exception {
        String key = CMPP_GATEWAY_USER_SEESION_PREFIX + userName;
        return stringRedisTemplate.opsForSet().members(key);
    }

    public void delUseSession(String userName) throws Exception {
        String key = CMPP_GATEWAY_USER_SEESION_PREFIX + userName;
        String session = NetUtils.getHostAddress() + ":" + udpPort;
        stringRedisTemplate.opsForSet().remove(key, session);
    }
}