package com.xhqb.spectre.cmpp_gateway.constant;

//import com.xhqb.kael.lucifer.telemetry.PrometheusCounterMetrics;
//import io.prometheus.client.Counter;

/*
 * @Author: huangyanxiong
 * @Date: 2021/12/16 14:57
 * @Description: Prometheus上报
 */
public final class LuciferConstant {

//    /**
//     * 请求次数
//     */
//    public static final Counter REQUEST_COUNTER = new PrometheusCounterMetrics("cmpp_gw_request_total", "CMPP网关请求次数")
//            .createWithLabels("userName");
//
//    /**
//     * 异常请求次数
//     */
//    public static final Counter INVALID_REQUEST_COUNTER = new PrometheusCounterMetrics("cmpp_gw_invalid_request_total", "CMPP网关异常请求次数")
//            .createWithLabels("userName", "errorCode");
//
//    /**
//     * 发送mq次数
//     */
//    public static final Counter MQ_PRODUCE_COUNTER = new PrometheusCounterMetrics("cmpp_gw_mq_produce_total", "CMPP网关发送MQ次数")
//            .createWithLabels("topicName");
//
//    /**
//     * 推送回执次数
//     */
//    public static final Counter SEND_DELIVER_COUNTER = new PrometheusCounterMetrics("cmpp_gw_send_deliver_total", "CMPP网关推送回执次数")
//            .createWithLabels("userName");
//
//    /**
//     * 推送回执失败次数
//     */
//    public static final Counter SEND_DELIVER_FAILED_COUNTER = new PrometheusCounterMetrics("cmpp_gw_send_deliver_failed_total", "CMPP网关推送回执失败次数")
//            .createWithLabels("userName");
}
