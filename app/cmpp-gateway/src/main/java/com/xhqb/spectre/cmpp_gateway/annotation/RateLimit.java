package com.xhqb.spectre.cmpp_gateway.annotation;

import com.xhqb.spectre.cmpp_gateway.enums.RateLimitTypeEnum;

import java.lang.annotation.*;

/**
 * 限流注解
 */
@Inherited
@Documented
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {

    /**
     * 资源的key
     *
     * @return String
     */
    String key() default "";

    /**
     * Key的prefix
     *
     * @return String
     */
    String prefix() default "";

    /**
     * 给定的时间段
     * 单位秒
     *
     * @return int
     */
    int period();

    /**
     * 最多的访问限制次数
     *
     * @return int
     */
    int count();

    /**
     * 类型
     *
     * @return LimitType
     */
    RateLimitTypeEnum limitType() default RateLimitTypeEnum.CUSTOMER;
}
