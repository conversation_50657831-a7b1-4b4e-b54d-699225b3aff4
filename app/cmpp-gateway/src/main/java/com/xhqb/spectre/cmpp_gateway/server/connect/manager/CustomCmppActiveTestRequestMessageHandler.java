package com.xhqb.spectre.cmpp_gateway.server.connect.manager;

import com.xhqb.spectre.cmpp_gateway.CustomSpringContextAware;
import com.xhqb.spectre.cmpp_gateway.service.RedisService;
import com.zx.sms.codec.cmpp.msg.CmppActiveTestRequestMessage;
import com.zx.sms.handler.cmpp.CmppActiveTestRequestMessageHandler;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

@Slf4j
public class CustomCmppActiveTestRequestMessageHandler extends CmppActiveTestRequestMessageHandler {

    private CustomCMPPServerChildEndpointEntity entity;

    public CustomCmppActiveTestRequestMessageHandler(CustomCMPPServerChildEndpointEntity entity) {
        this.entity = entity;
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, CmppActiveTestRequestMessage e) throws Exception {
        log.info("cmpp active test request, username: {}, seq: {}", entity.getUserName(), e.getSequenceNo());

        CustomSpringContextAware customSpringContextAware = new CustomSpringContextAware();
        ApplicationContext context = customSpringContextAware.getContext();
        RedisService redisService = context.getBean(RedisService.class);
        redisService.setUseSession(entity.getUserName());
        super.channelRead0(ctx, e);
    }

}
