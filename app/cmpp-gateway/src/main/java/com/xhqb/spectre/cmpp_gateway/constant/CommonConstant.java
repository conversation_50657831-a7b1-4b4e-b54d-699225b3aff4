package com.xhqb.spectre.cmpp_gateway.constant;

/**
 * 短信平台公共参数常量
 */
public final class CommonConstant {

    /**
     * 限流白名单配置key
     */
    public static final String WHITE_APP_SEND_LIMIT = "appSendLimit";

    /**
     * 默认运营商
     */
    public static final String DEFAULT_ISP = "default";

    /**
     * 默认地址
     */
    public static final String DEFAULT_AREA = "default";

    /**
     * 默认分片
     */
    public static final int DEFAULT_SLICE_NUMBER = 1;

    /**
     * 默认请求来源，1：http；2：cmpp
     */
    public static final int DEFAULT_REQ_SRC = 2;

    /**
     * 发送类型，1：实时；2：延时
     */
    public static final int SEND_TYPE_CURRENT = 1;
    public static final int SEND_TYPE_DALY = 2;

    /**
     * 模板文案检测，1：开启；0：不开启
     */
    public static final int TPL_CHECK_OPEN = 1;
    public static final int TPL_CHECK_CLOSE = 0;
}
