package com.xhqb.spectre.cmpp_gateway.server.connect.manager;

import com.xhqb.spectre.cmpp_gateway.CustomSpringContextAware;
import com.xhqb.spectre.cmpp_gateway.service.RedisService;
import com.zx.sms.codec.cmpp.msg.CmppTerminateRequestMessage;
import com.zx.sms.handler.cmpp.CmppTerminateRequestMessageHandler;
import io.netty.channel.ChannelHandlerContext;
import org.springframework.context.ApplicationContext;

public class CustomCmppTerminateRequestMessageHandler extends CmppTerminateRequestMessageHandler {

    private CustomCMPPServerChildEndpointEntity entity;

    public CustomCmppTerminateRequestMessageHandler(CustomCMPPServerChildEndpointEntity entity) {
        this.entity = entity;
    }

    @Override
    public void channelRead0(final ChannelHandlerContext ctx, CmppTerminateRequestMessage e) throws Exception {
        CustomSpringContextAware customSpringContextAware = new CustomSpringContextAware();
        ApplicationContext context = customSpringContextAware.getContext();
        RedisService redisService = context.getBean(RedisService.class);
        redisService.delUseSession(entity.getUserName());
        super.channelRead0(ctx, e);
    }
}
