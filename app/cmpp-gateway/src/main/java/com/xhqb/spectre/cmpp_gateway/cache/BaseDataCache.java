package com.xhqb.spectre.cmpp_gateway.cache;

import com.xhqb.spectre.common.dal.dto.*;
import com.xhqb.spectre.common.dal.entity.GatewayUserDO;
import com.xhqb.spectre.common.dal.entity.SignDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/*
 * @Author: huangyanxiong, wangjianzheng
 * @Create: 2021/11/29 16:53
 * @Update: 2021/11/30 10:28
 * @Description:
 */
public final class BaseDataCache {

    private static final BaseDataCache INSTANCE = new BaseDataCache();

    /**
     * 模板文案映射缓存
     */
    private static Map<Integer, List<TplMappingItem>> TPL_MAPPING_MAP = new ConcurrentHashMap<>();
    private static volatile Integer TPL_MAPPING_UPDATE_TIME = null;

    /**
     * 网关用户缓存
     */
    private static Map<String, GatewayUserDO> GATEWAY_USER_MAP = new ConcurrentHashMap<>();
    private static volatile Integer GATEWAY_USER_UPDATE_TIME = null;

    /**
     * 业务应用缓存
     */
    private static Map<String, AppData> APP_MAP = new ConcurrentHashMap<>();
    private static volatile Integer APP_UPDATE_TIME = null;

    /**
     * 模板缓存
     */
    private static Map<String, TplData> TPL_MAP = new ConcurrentHashMap<>();
    private static volatile Integer TPL_UPDATE_TIME = null;

    /**
     * 白名单缓存
     */
    private static Set<String> WHITE_SET = new HashSet<>();
    private static volatile Integer WHITE_UPDATE_TIME = null;

    /**
     * 应用限流缓存
     */
    private static Map<String, List<AppSendLimitData>> APP_LIMIT_MAP = new ConcurrentHashMap<>();
    private static volatile Integer APP_LIMIT_UPDATE_TIME = null;

    /**
     * 渠道账号屏蔽缓存
     */
    private static Map<Integer, List<ChannelAccountDisableData>> CHANNEL_DISABLED_MAP = new ConcurrentHashMap<>();
    private static volatile Integer CHANNEL_DISABLED_UPDATE_TIME = null;

    /**
     * 签名缓存
     */
    private static Map<Integer, SignDO> SIGN_MAP = new ConcurrentHashMap<>();
    private static Map<String, Integer> SIGN_NAME_MAP = new ConcurrentHashMap<>();
    private static volatile Integer SIGN_UPDATE_TIME = null;

    /**
     * 手机号运营商数据
     */
    private static byte[] PHONE_DATA = null;

    /**
     * 短信类型屏蔽缓存
     */
    private static Map<String, List<SmsTypeDisableData>> SMS_TYPE_DISABLED_MAP = new ConcurrentHashMap<>();
    private static volatile Integer SMS_TYPE_DISABLED_UPDATE_TIME = null;

    /**
     * 黑名单
     */
    private static Set<String> BLACK_SET = new HashSet<>();
    private static volatile Integer BLACK_UPDATE_TIME = null;

    private BaseDataCache() {
    }

    public static BaseDataCache getInstance() {
        return INSTANCE;
    }

    /**
     * 获取网关用户信息
     *
     * @param userName
     * @return
     */
    public GatewayUserDO getGatewayUser(String userName) {
        return GATEWAY_USER_MAP.get(userName);
    }

    /**
     * 刷新网关用户映射缓存
     *
     * @param list
     */
    public void refreshGatewayUser(List<GatewayUserDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            GATEWAY_USER_MAP = new ConcurrentHashMap<>();
            return;
        }
        Map<String, GatewayUserDO> tmpMap = new ConcurrentHashMap<>();
        for (GatewayUserDO item : list) {
            String signIds = item.getSignIdList();
            Set<Integer> signIdSet = StringUtils.isNotEmpty(signIds) ?
                    Arrays.stream(signIds.split(",")).map(Integer::valueOf).collect(Collectors.toSet()) : Collections.emptySet();
            item.setSignIdSet(signIdSet);
            tmpMap.put(item.getUserName(), item);
        }
        GATEWAY_USER_MAP = tmpMap;
    }

    /**
     * 获取网关用户变更时间
     *
     * @return
     */
    public Integer getGatewayUserUpdateTime() {
        return GATEWAY_USER_UPDATE_TIME;
    }

    /**
     * 设置网关用户变更时间
     *
     * @param updateTime
     */
    public void setGatewayUserUpdateTime(Integer updateTime) {
        GATEWAY_USER_UPDATE_TIME = updateTime;
    }

    /**
     * 刷新模板文案映射缓存
     *
     * @param map
     */
    public void refreshTplMapping(Map<Integer, List<TplMappingItem>> map) {
        TPL_MAPPING_MAP = map;
    }

    public List<TplMappingItem> getTplMappingList(Integer tplId) {
        return TPL_MAPPING_MAP.get(tplId);
    }

    public Integer getTplMappingUpdateTime() {
        return TPL_MAPPING_UPDATE_TIME;
    }

    public void setTplMappingUpdateTime(Integer updateTime) {
        TPL_MAPPING_UPDATE_TIME = updateTime;
    }

    /**
     * 刷新业务应用缓存
     *
     * @return
     */
    public void refreshApp(List<AppData> list) {
        APP_MAP = Optional.ofNullable(list)
                .map(item -> item.stream().collect(Collectors.toConcurrentMap(AppData::getCode, Function.identity())))
                .orElse(new ConcurrentHashMap<>());
    }

    public AppData getAppInfo(String appCode) {
        return APP_MAP.get(appCode);
    }

    public Integer getAppUpdateTime() {
        return APP_UPDATE_TIME;
    }

    public void setAppUpdateTime(Integer updateTime) {
        APP_UPDATE_TIME = updateTime;
    }

    /**
     * 刷新模板缓存
     *
     * @param list
     */
    public void refreshTpl(List<TplData> list) {
        if (CollectionUtils.isEmpty(list)) {
            TPL_MAP = new ConcurrentHashMap<>();
            return;
        }
        Map<String, TplData> tmpMap = new ConcurrentHashMap<>();
        for (TplData item : list) {
            String cacheKey = buildTplKey(item.getCode(), item.getSignId());
            tmpMap.put(cacheKey, item);
        }
        TPL_MAP = tmpMap;
    }

    public TplData getTplInfo(String tplCode, Integer signId) {
        return TPL_MAP.get(buildTplKey(tplCode, signId));
    }

    private String buildTplKey(String tplCode, Integer signId) {
        return tplCode + ":" + signId;
    }

    public Integer getTplUpdateTime() {
        return TPL_UPDATE_TIME;
    }

    public void setTplUpdateTime(Integer updateTime) {
        TPL_UPDATE_TIME = updateTime;
    }

    /**
     * 刷新白名单缓存
     */
    public void refreshWhite(Set<String> whiteSet) {
        WHITE_SET = whiteSet;
    }

    /**
     * 是否包含白名单
     *
     * @param appCode
     * @param cfgType
     * @param phone
     * @return
     */
    public boolean containsWhite(String appCode, String cfgType, String phone) {
        if (StringUtils.isBlank(appCode) || StringUtils.isBlank(cfgType) || StringUtils.isBlank(phone)) {
            return false;
        }
        String key = appCode + "_" + cfgType + "_" + phone;
        return WHITE_SET.contains(key);
    }

    public Integer getWhiteUpdateTime() {
        return WHITE_UPDATE_TIME;
    }

    public void setWhiteUpdateTime(Integer updateTime) {
        WHITE_UPDATE_TIME = updateTime;
    }

    /**
     * 刷新应用限流
     *
     * @param map
     */
    public void refreshAppLimit(Map<String, List<AppSendLimitData>> map) {
        APP_LIMIT_MAP = map;
    }

    public List<AppSendLimitData> getAppLimitList(String appCode) {
        return APP_LIMIT_MAP.get(appCode);
    }

    public Integer getAppLimitUpdateTime() {
        return APP_LIMIT_UPDATE_TIME;
    }

    public void setAppLimitUpdateTime(Integer updateTime) {
        APP_LIMIT_UPDATE_TIME = updateTime;
    }

    /**
     * 刷新渠道屏蔽信息缓存
     *
     * @param map
     */
    public void refreshChannelDisabledMap(Map<Integer, List<ChannelAccountDisableData>> map) {
        CHANNEL_DISABLED_MAP = map;
    }

    public List<ChannelAccountDisableData> getDisabledList(Integer channelId) {
        return CHANNEL_DISABLED_MAP.get(channelId);
    }

    public Integer getChannelDisabledUpdateTime() {
        return CHANNEL_DISABLED_UPDATE_TIME;
    }

    public void setChannelDisableUpdateTime(Integer updateTime) {
        CHANNEL_DISABLED_UPDATE_TIME = updateTime;
    }

    /**
     * 刷新签名缓存
     */
    public void refreshSign(List<SignDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            SIGN_MAP = new ConcurrentHashMap<>();
            SIGN_NAME_MAP = new ConcurrentHashMap<>();
            return;
        }
        Map<Integer, SignDO> tmpSignMap = new ConcurrentHashMap<>();
        Map<String, Integer> tmpSignNameMap = new ConcurrentHashMap<>();
        for (SignDO signDO : list) {
            tmpSignMap.put(signDO.getId(), signDO);
            tmpSignNameMap.put(signDO.getName(), signDO.getId());
        }
        SIGN_MAP = tmpSignMap;
        SIGN_NAME_MAP = tmpSignNameMap;
    }

    public SignDO getSign(Integer signId) {
        return SIGN_MAP.get(signId);
    }

    public Integer getSignIdByName(String signName) {
        return SIGN_NAME_MAP.get(signName);
    }

    public Integer getSignUpdateTime() {
        return SIGN_UPDATE_TIME;
    }

    public void setSignUpdateTime(Integer updateTime) {
        SIGN_UPDATE_TIME = updateTime;
    }

    /**
     * 设置手机号运营商数据
     *
     * @param data
     */
    public void refreshPhoneData(byte[] data) {
        PHONE_DATA = data;
    }

    public byte[] getPhoneData() {
        return PHONE_DATA;
    }

    /**
     * 刷新短信类型屏蔽信息缓存
     *
     * @param map
     */
    public void refreshSmsTypeDisabledMap(Map<String, List<SmsTypeDisableData>> map) {
        SMS_TYPE_DISABLED_MAP = map;
    }

    public List<SmsTypeDisableData> getDisabledList(String smsTypeCode) {
        return SMS_TYPE_DISABLED_MAP.get(smsTypeCode);
    }

    public Integer getSmsTypeDisabledUpdateTime() {
        return SMS_TYPE_DISABLED_UPDATE_TIME;
    }

    public void setSmsTypeDisableUpdateTime(Integer updateTime) {
        SMS_TYPE_DISABLED_UPDATE_TIME = updateTime;
    }

    /**
     * 刷新黑名单缓存
     *
     * @param blackSet
     */
    public void refreshBlack(Set<String> blackSet) {
        BLACK_SET = blackSet;
    }

    public boolean containsBlack(String smsType, String mobile, String appCode) {
        if (StringUtils.isBlank(smsType) || StringUtils.isBlank(mobile) || CollectionUtils.isEmpty(BLACK_SET)) {
            return false;
        }
        String key = smsType + "_" + mobile;
        String key2 = key + "_" + appCode;
        return BLACK_SET.contains(key) || BLACK_SET.contains(key2);
    }

    public Integer getBlackUpdateTime() {
        return BLACK_UPDATE_TIME;
    }

    public void setBlackUpdateTime(Integer updateTime) {
        BLACK_UPDATE_TIME = updateTime;
    }
}
