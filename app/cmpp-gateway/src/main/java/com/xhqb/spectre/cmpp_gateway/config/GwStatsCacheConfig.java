package com.xhqb.spectre.cmpp_gateway.config;


import com.xhqb.spectre.cmpp_gateway.util.GwStatsCacheUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

@Configuration
public class GwStatsCacheConfig {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Bean
    @ConditionalOnProperty(name = "gw.stats.cache.enabled", havingValue = "true", matchIfMissing = true)
    public GwStatsCacheUtil gwStatsCacheUtil() {
        return new GwStatsCacheUtil(redisTemplate);
    }
}
