package com.xhqb.spectre.cmpp_gateway.server.connect.manager;

import com.xhqb.spectre.cmpp_gateway.server.session.CustomSessionStateManager;
import com.zx.sms.BaseMessage;
import com.zx.sms.connect.manager.EndpointEntity;
import com.zx.sms.connect.manager.cmpp.CMPPServerChildEndpointConnector;
import com.zx.sms.session.AbstractSessionStateManager;
import io.netty.channel.ChannelPipeline;
import io.netty.util.concurrent.Promise;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import java.util.concurrent.ConcurrentMap;

@Slf4j
public class CustomCMPPServerChildEndpointConnector extends CMPPServerChildEndpointConnector {

    public CustomCMPPServerChildEndpointConnector(CustomCMPPServerChildEndpointEntity endpoint) {
        super(endpoint);
    }

    @Override
    protected void doBindHandler(ChannelPipeline pipe, EndpointEntity cmppentity) {
        super.doBindHandler(pipe, cmppentity);

        CustomCMPPServerChildEndpointEntity entity = (CustomCMPPServerChildEndpointEntity)cmppentity;
        pipe.replace("CMPPSubmitLongMessageHandler", "CMPPSubmitLongMessageHandler", new CustomCMPPSubmitLongMessageHandler(entity));
        pipe.replace("CmppActiveTestRequestMessageHandler", "CmppActiveTestRequestMessageHandler", new CustomCmppActiveTestRequestMessageHandler(entity));
        pipe.replace("CmppActiveTestResponseMessageHandler", "CmppActiveTestResponseMessageHandler", new CustomCmppActiveTestResponseMessageHandler(entity));
        pipe.replace("CmppTerminateRequestMessageHandler", "CmppTerminateRequestMessageHandler", new CustomCmppTerminateRequestMessageHandler(entity));
    }

    @Override
    public <T extends BaseMessage> List<Promise<T>> synwrite(List<T> msgs) {
        if(super.fetch() == null) {
            log.error("no available connection");
            return null;
        }

        return super.synwrite(msgs);
    }

    @Override
    protected AbstractSessionStateManager createSessionManager(EndpointEntity entity, ConcurrentMap storeMap, boolean preSend) {
        return new CustomSessionStateManager(entity, storeMap, preSend);
    }
}