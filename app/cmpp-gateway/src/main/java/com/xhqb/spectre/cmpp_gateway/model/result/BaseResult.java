package com.xhqb.spectre.cmpp_gateway.model.result;

import com.xhqb.spectre.cmpp_gateway.enums.RespCodeEnum;
import lombok.Data;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/29 18:53
 * @Description:
 */
@Data
public class BaseResult<T> implements Serializable {

    private static final long serialVersionUID = -5236406721325909409L;

    private Integer code;

    private String msg;

    private T data;

    public BaseResult(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> BaseResult<T> success(T data) {
        return new BaseResult<>(RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMsg(), data);
    }

    public static <T> BaseResult<T> success() {
        return success(null);
    }

    public static <T> BaseResult<T> error(int code, String msg) {
        return new BaseResult<>(code, msg, null);
    }

    public static <T> BaseResult<T> error(RespCodeEnum codeEnum) {
        return new BaseResult<>(codeEnum.getCode(), codeEnum.getMsg(), null);
    }

    public static <T> BaseResult<T> systemError() {
        return error(RespCodeEnum.SYSTEM_ERROR);
    }
}
