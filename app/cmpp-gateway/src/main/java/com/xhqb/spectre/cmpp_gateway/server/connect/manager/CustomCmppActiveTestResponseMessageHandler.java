package com.xhqb.spectre.cmpp_gateway.server.connect.manager;

import com.xhqb.spectre.cmpp_gateway.CustomSpringContextAware;
import com.xhqb.spectre.cmpp_gateway.service.RedisService;
import com.zx.sms.codec.cmpp.msg.CmppActiveTestResponseMessage;
import com.zx.sms.connect.manager.EndpointManager;
import com.zx.sms.handler.cmpp.CmppActiveTestResponseMessageHandler;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

@Slf4j
public class CustomCmppActiveTestResponseMessageHandler extends CmppActiveTestResponseMessageHandler {

    private CustomCMPPServerChildEndpointEntity entity;

    public CustomCmppActiveTestResponseMessageHandler(CustomCMPPServerChildEndpointEntity entity) {
        this.entity = entity;
    }

    @Override
    protected void channelRead0(ChannelHandlerContext paramChannelHandlerContext, CmppActiveTestResponseMessage paramI) throws Exception {
        log.info("cmpp active test response, username: {}, seq: {}", entity.getUserName(), paramI.getSequenceNo());

        CustomSpringContextAware customSpringContextAware = new CustomSpringContextAware();
        ApplicationContext context = customSpringContextAware.getContext();
        RedisService redisService = context.getBean(RedisService.class);
        redisService.setUseSession(entity.getUserName());
        super.channelRead0(paramChannelHandlerContext, paramI);

        EndpointManager.INS.addEndpointEntity(entity);
    }
}
