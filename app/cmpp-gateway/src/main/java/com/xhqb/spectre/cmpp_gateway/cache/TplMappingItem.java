package com.xhqb.spectre.cmpp_gateway.cache;

import com.xhqb.spectre.common.dal.entity.GatewayTplMappingDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/29 16:58
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplMappingItem {

    private static final String TPL_PARAM_SYMBOL = "[*]";

    //模板参数格式包含两种（1：[*]；2：${XXX}）
    private static final String TPL_PARAM_SYMBOL_REGEX = "\\[\\*]";
    private static final String TPL_PARAM_SYMBOL_REGEX2 = "\\$\\{[\\w|\u4e00-\u9fa5|-]+}";
    private static final Pattern TPL_PARAM_SYMBOL_PATTERN2 = Pattern.compile(TPL_PARAM_SYMBOL_REGEX2);

    private static final String TPL_PARAM_SYMBOL_REPLACE = "\\.{1,20}";

    /**
     * 模板ID
     */
    private Integer tplId;

    /**
     * 模板内容
     */
    private String tplContent;

    /**
     * 模板内容是否包含变量参数
     */
    private boolean containsParam;

    /**
     * 模板内容的正则表达式pattern（如果包含参数，则进行预编译正则表达式，否则设置为null）
     */
    private Pattern pattern;

    public static TplMappingItem build(GatewayTplMappingDO item) {
        String content = item.getTplContent();
        if (StringUtils.isEmpty(content) || (!content.contains(TPL_PARAM_SYMBOL) && !TPL_PARAM_SYMBOL_PATTERN2.matcher(content).matches())) {
            return TplMappingItem.builder()
                    .tplId(item.getTplId())
                    .tplContent(item.getTplContent())
                    .containsParam(false)
                    .pattern(null)
                    .build();
        }
        content = content.replaceAll(TPL_PARAM_SYMBOL_REGEX, TPL_PARAM_SYMBOL_REPLACE);
        content = content.replaceAll(TPL_PARAM_SYMBOL_REGEX2, TPL_PARAM_SYMBOL_REPLACE);
        String patternStr = "^" + content + "$";
        Pattern pattern = Pattern.compile(patternStr);
        return TplMappingItem.builder()
                .tplId(item.getTplId())
                .tplContent(item.getTplContent())
                .containsParam(true)
                .pattern(pattern)
                .build();
    }
}
