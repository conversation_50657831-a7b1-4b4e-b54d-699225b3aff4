package com.xhqb.spectre.cmpp_gateway.service.impl;

import com.xhqb.spectre.cmpp_gateway.service.OpTimeService;
import com.xhqb.spectre.common.dal.entity.OpTimeDO;
import com.xhqb.spectre.common.dal.mapper.OpTimeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/9 14:11
 * @Description:
 */
@Service
public class OpTimeServiceImpl implements OpTimeService {

    @Autowired
    private OpTimeMapper opTimeMapper;

    @Override
    public OpTimeDO findByModule(Integer module) {
        return opTimeMapper.selectByModule(module);
    }

    @Override
    public List<OpTimeDO> findAllOpTime() {
        return opTimeMapper.selectAll();
    }
}
