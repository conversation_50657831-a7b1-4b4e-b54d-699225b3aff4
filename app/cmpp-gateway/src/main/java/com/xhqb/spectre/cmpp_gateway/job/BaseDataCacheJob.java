package com.xhqb.spectre.cmpp_gateway.job;

import com.xhqb.spectre.cmpp_gateway.cache.BaseDataCache;
import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.cmpp_gateway.service.CacheService;
import com.xhqb.spectre.cmpp_gateway.service.OpTimeService;
import com.xhqb.spectre.common.dal.entity.OpTimeDO;
import com.xhqb.spectre.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/*
 * @Author: huangyanxiong, wangjianzheng
 * @Create: 2021/11/29 18:04
 * @Update: 2021/11/30 15:03
 * @Description:
 */
@Component
@Slf4j
public class BaseDataCacheJob {

    @Autowired
    private OpTimeService opTimeService;

    @Autowired
    private CacheService cacheService;

    /**
     * 比较数据库时间和系统记录的时间，刷新缓存
     */
    @Scheduled(cron = "${cache.refresh.cron:*/30 * * * * ?}")
    public void execute() {
        //刷新缓存
        refreshCache();
    }

    private void refreshCache() {
        BaseDataCache dataCache = BaseDataCache.getInstance();
        List<OpTimeDO> opTimeDOList = opTimeService.findAllOpTime();
        for (OpTimeDO opTimeDO : opTimeDOList) {
            int module = opTimeDO.getModule().intValue();
            Integer lastUpdateTime = getUpdateTime(module);
            Integer opTime = opTimeDO.getOpTime();
            if (Objects.nonNull(lastUpdateTime) && (lastUpdateTime == -1 || opTime <= lastUpdateTime)) {
                continue;
            }
            switch (module) {
                //业务应用
                case OpLogConstant.MODULE_APP:
                    cacheService.refreshAppInfo();
                    dataCache.setAppUpdateTime(opTime);
                    break;
                //签名
                case OpLogConstant.MODULE_SIGN:
                    cacheService.refreshSign();
                    dataCache.setSignUpdateTime(opTime);
                    break;
                //模板
                case OpLogConstant.MODULE_TPL:
                    cacheService.refreshTplInfo();
                    dataCache.setTplUpdateTime(opTime);
                    break;
                //渠道屏蔽
                case OpLogConstant.MODULE_CHANNEL_ACCOUNT_DISABLE:
                    cacheService.refreshChannelAccountDisable();
                    dataCache.setChannelDisableUpdateTime(opTime);
                    break;
                //网关模板映射
                case OpLogConstant.MODULE_GATEWAY_TPL_MAPPING:
                    cacheService.refreshTplMapping();
                    dataCache.setTplMappingUpdateTime(opTime);
                    break;
                //应用限流
                case OpLogConstant.MODULE_SEND_LIMIT:
                    cacheService.refreshAppSendLimit();
                    dataCache.setAppLimitUpdateTime(opTime);
                    break;
                //白名单
                case OpLogConstant.MODULE_MOBILE_WHITE:
                    cacheService.refreshMobileWhite();
                    dataCache.setWhiteUpdateTime(opTime);
                    break;
                //网关用户
                case OpLogConstant.MODULE_GATEWAY_USER:
                    cacheService.refreshGatewayUser();
                    dataCache.setGatewayUserUpdateTime(opTime);
                    break;
                //短信类型屏蔽
                case OpLogConstant.MODULE_SMS_TYPE_DISABLE:
                    cacheService.refreshSmsTypeDisable();
                    dataCache.setSmsTypeDisableUpdateTime(opTime);
                    break;
                //黑名单
                case OpLogConstant.MODULE_MOBILE_BLACK:
                    cacheService.refreshBlack();
                    dataCache.setBlackUpdateTime(opTime);
                    break;
                default:
                    break;
            }
            log.info("module: {}，缓存更新，此次更新时间：{}，上一次更新时间：{}", module, DateUtil.intToString(opTime), DateUtil.intToString(lastUpdateTime));
        }
    }

    private Integer getUpdateTime(int module) {
        BaseDataCache dataCache = BaseDataCache.getInstance();
        switch (module) {
            case OpLogConstant.MODULE_APP:
                return dataCache.getAppUpdateTime();
            case OpLogConstant.MODULE_SIGN:
                return dataCache.getSignUpdateTime();
            case OpLogConstant.MODULE_TPL:
                return dataCache.getTplUpdateTime();
            case OpLogConstant.MODULE_CHANNEL_ACCOUNT_DISABLE:
                return dataCache.getChannelDisabledUpdateTime();
            case OpLogConstant.MODULE_GATEWAY_TPL_MAPPING:
                return dataCache.getTplMappingUpdateTime();
            case OpLogConstant.MODULE_SEND_LIMIT:
                return dataCache.getAppLimitUpdateTime();
            case OpLogConstant.MODULE_MOBILE_WHITE:
                return dataCache.getWhiteUpdateTime();
            case OpLogConstant.MODULE_GATEWAY_USER:
                return dataCache.getGatewayUserUpdateTime();
            case OpLogConstant.MODULE_SMS_TYPE_DISABLE:
                return dataCache.getSmsTypeDisabledUpdateTime();
            case OpLogConstant.MODULE_MOBILE_BLACK:
                return dataCache.getBlackUpdateTime();
            default:
                //返回-1，表示无需更新
                return -1;
        }
    }
}
