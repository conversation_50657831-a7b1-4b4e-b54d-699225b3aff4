package com.xhqb.spectre.cmpp_gateway.util;

import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

public class GwStatsCacheUtil {
    /**
     * gw级指标缓存key前缀
     */
    public static final String GW_KEY_PREFIX = "spectre:gw_stats:";

    public static final String APP_KEY_PREFIX = "spectre:app_stats:";

    private RedisTemplate<String, Object> redisTemplate;

    private HashOperations<String, String, Object> hashOps;

    public GwStatsCacheUtil(RedisTemplate<String, Object> redisTemplate) {

        this.redisTemplate = redisTemplate;
        hashOps = redisTemplate.opsForHash();
    }

    /**
     * 获取 Redis Key
     *
     * @param prefix  前缀
     * @param groupName 分组名
     * @param username 账户 IDD
     * @param statDate  统计日期
     * @return Redis Key
     */
    private String getRedisKey(String prefix, String groupName, String username, LocalDate statDate) {
        String statDateStr = statDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String key = prefix + groupName + ":" + username + ":" + statDateStr;
        if (Boolean.FALSE.equals(redisTemplate.hasKey(key))) {
            hashOps.put(key, "init", statDateStr);
            setKeyExpiration(key, 7L, TimeUnit.DAYS);
        }
        return key;
    }


    /**
     * 设置键的过期时间
     *
     * @param key     键
     * @param timeout 过期时间
     * @param unit    时间单位
     */
    private void setKeyExpiration(String key, long timeout, TimeUnit unit) {
        redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 增加模板维度的成功数
     *
     * @param prefix  前缀
     * @param groupName 分组名
     * @param username 账户 ID
     * @param statDate  统计日期
     * @param tplCode   模板编码
     * @param errCode   错误码
     * @param increment 增量
     */
    public void increment(String prefix, String groupName, String username, LocalDate statDate, String tplCode, String errCode, int increment) {

        String key = getRedisKey(prefix, groupName, username, statDate);
        hashOps.increment(key, "tpl:" + tplCode + ":code:" + errCode, increment);
    }
}

