package com.xhqb.spectre.cmpp_gateway.util;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;

public class NetUtils {

    public static String getHostAddress() throws Exception {
        Enumeration<NetworkInterface> interfaces  = NetworkInterface.getNetworkInterfaces();
        while (interfaces.hasMoreElements()) {
            NetworkInterface anInterface = (NetworkInterface)interfaces.nextElement();
            if (anInterface.isUp() && !anInterface.isLoopback() && !anInterface.isPointToPoint() && !anInterface.isVirtual()) {
                Enumeration<InetAddress> addresses = anInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    if (address != null && address instanceof Inet4Address) {
                        return address.getHostAddress();
                    }
                }
            }
        }

        return null;
    }
}