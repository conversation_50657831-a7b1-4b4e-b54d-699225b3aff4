package com.xhqb.spectre.cmpp_gateway.service;

public interface MobileLimitService {

    Boolean checkMobileMaxCountHalfMinute(String key, Integer limitValue);

    Boolean checkMobileMaxCountHour(String key, Integer limitValue);

    Boolean checkMobileMaxCountDay(String key, Integer limitValue);

    Boolean checkMobileSameContentMaxCycle(String key, Integer limitValue);

    Boolean checkVerifyCountDay(String key, Integer limitValue);

    void postSubtractAppFrequencyLimit(String appKey);

}
