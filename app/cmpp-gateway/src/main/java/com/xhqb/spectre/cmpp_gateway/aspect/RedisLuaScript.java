package com.xhqb.spectre.cmpp_gateway.aspect;

import org.springframework.data.redis.core.script.DefaultRedisScript;

/**
 * 限流脚本
 *
 * <AUTHOR>
 * @date 2021/10/12
 */
public class RedisLuaScript {
    /**
     * 限流计数+1脚本
     * 如果值大于限制值 直接返回
     * 否则+1 返回
     */
    private static final String arIncrStr = "local c"
            + "\nc = redis.call('get',KEYS[1])"
            + "\nif not c or tonumber(c) < tonumber(ARGV[1]) then"
            + "\nc = redis.call('incr',KEYS[1])"
            + "\nif tonumber(c) == 1 then"
            + "\nredis.call('expire',KEYS[1],ARGV[2])"
            + "\nend"
            + "\nreturn c;"
            + "\nend"
            + "\nif c and tonumber(c) > tonumber(ARGV[1]) then"
            + "\nreturn c;"
            + "\nend";

    /**
     * 增加计数
     */
    public static final DefaultRedisScript<Long> arIncr = new DefaultRedisScript<>(arIncrStr, Long.class);

    /**
     * 限流计数-1脚本
     */
    private static final String arDecrStr = "local count = redis.call('get',KEYS[1]);"
            + "\nif count and tonumber(count)>0 then "
            + "\ncount=redis.call('incrby',KEYS[1],-1);"
            + "\nend;"
            + "\nreturn tonumber(count)";

    /**
     * 减去计数
     */
    public static final DefaultRedisScript<Long> arDecr = new DefaultRedisScript<>(arDecrStr, Long.class);

}
