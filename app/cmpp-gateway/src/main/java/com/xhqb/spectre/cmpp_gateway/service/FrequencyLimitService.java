package com.xhqb.spectre.cmpp_gateway.service;

import com.xhqb.spectre.cmpp_gateway.enums.LimitCheckResultEnum;
import com.xhqb.spectre.common.dal.dto.AppSendLimitData;

public interface FrequencyLimitService {

    /**
     * 限流计数
     * @param appCode
     * @param limitData
     * @param phone
     * @param smsType
     * @return
     */
    LimitCheckResultEnum checkSendLimitFrequencyFrom(String appCode, AppSendLimitData limitData, String phone, String smsType, String smsContent);

    /**
     * 减去redis中频率计数
     * @param appCode
     * @param cfgType
     * @param phone
     * @param smsType
     * @param smsContent
     */
    void subtractSMSFrequencyLimitCount(String appCode, String cfgType, String phone, String smsType, String smsContent);
}
