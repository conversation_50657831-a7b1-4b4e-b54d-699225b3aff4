package com.xhqb.spectre.cmpp_gateway.enums;

/**
 * 频率限制检查
 */
public enum LimitCheckResultEnum {

    APP_SEND_LIMIT_PASS(5100, "pass"),

    APP_SEND_LIMIT_APP_MAX_COUNT_DAY(5101, "appMaxCountDay blocked"),

    APP_SEND_LIMIT_MOBILE_MAZX_COUNT_HALF_MINUTE(5102, "mobileMaxCountHalfMinute blocked"),

    APP_SEND_LIMIT_MOBILE_MAX_COUNT_HOUR(5103, "mobileMaxCountHour blocked"),

    APP_SEND_LIMIT_MOBILE_MAX_COUNT_DAY(5104, "mobileMaxCountDay blocked"),

    APP_SEND_LIMIT_MOBILE_SAME_CONTENT_MAX_CYCLE(5105, "mobileSameContentMaxCycle blocked"),

    APP_SEND_LIMIT_MOBILE_MAX_COUNT_SAME_CONTENT_CYCLE(5106, "mobileMaxCountSameContentCycle blocked"),

    APP_SEND_LIMIT_MOBILE_MAX_COUNT_VERIFY(5106, "mobileMaxCountVerify blocked");


    private Integer statusCode;

    private String statusMsg;

    private LimitCheckResultEnum(Integer statusCode, String statusMsg) {
        this.statusCode = statusCode;
        this.statusMsg = statusMsg;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    private void setStatusCode(Integer code) {
        this.statusCode = code;
    }


    public String getStatusMsg() {
        return statusMsg;
    }


    private void setStatusMsg(String message) {
        this.statusMsg = message;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code
     * @return
     */
    public static LimitCheckResultEnum getByCode(Integer code) {
        LimitCheckResultEnum[] arr = values();
        int length = arr.length;

        for (int i = 0; i < length; ++i) {
            LimitCheckResultEnum statusEnum = arr[i];
            if (statusEnum.getStatusCode().equals(code)) {
                return statusEnum;
            }
        }

        return null;
    }

    public static LimitCheckResultEnum getByStatusMsg(String statusMsg) {
        LimitCheckResultEnum[] arr = values();
        int length = arr.length;

        for (int i = 0; i < length; ++i) {
            LimitCheckResultEnum statusEnum = arr[i];
            if (statusEnum.getStatusMsg().equals(statusMsg)) {
                return statusEnum;
            }
        }

        return null;
    }

}
