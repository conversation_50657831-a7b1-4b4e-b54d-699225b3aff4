package com.xhqb.spectre.cmpp_gateway.server;

import com.zx.sms.connect.manager.EndpointManager;
import com.xhqb.spectre.cmpp_gateway.server.connect.manager.CustomCmppServerEndpointEntity;

public class CmppServer {

    private final CustomCmppServerEndpointEntity server = new CustomCmppServerEndpointEntity();

    private int port;

    public CmppServer(int port) {
        this.port = port;
    }

    public void start() {
        final EndpointManager manager = EndpointManager.INS;

        CustomCmppServerEndpointEntity server = new CustomCmppServerEndpointEntity();
        server.setId("server");
        server.setHost("0.0.0.0");
        server.setPort(this.port);
        server.setValid(true);
        server.setUseSSL(false);

        manager.openEndpoint(server);
    }
}