package com.xhqb.spectre.cmpp_gateway.server.connect.manager;

import com.xhqb.spectre.cmpp_gateway.CmppGatewayApplication;
import com.xhqb.spectre.cmpp_gateway.CustomSpringContextAware;
import com.xhqb.spectre.cmpp_gateway.config.VenusConfig;
import com.xhqb.spectre.cmpp_gateway.enums.RespCodeEnum;
import com.xhqb.spectre.cmpp_gateway.model.entity.CMPPGatewayMsg;
import com.xhqb.spectre.cmpp_gateway.model.result.BaseResult;
import com.xhqb.spectre.cmpp_gateway.service.RedisService;
import com.xhqb.spectre.cmpp_gateway.service.SmsService;
import com.xhqb.spectre.cmpp_gateway.util.GwStatsCacheUtil;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import com.xhqb.spectre.common.mq.BaseBodyMessage;
import com.xhqb.spectre.common.mq.DeliverMessageMQ;
import com.xhqb.spectre.common.mq.MessageMQ;
import com.zx.sms.codec.cmpp.msg.CmppSubmitRequestMessage;
import com.zx.sms.codec.cmpp.msg.CmppSubmitResponseMessage;
import com.zx.sms.common.util.CachedMillisecondClock;
import com.zx.sms.common.util.ChannelUtil;
import com.zx.sms.handler.cmpp.CMPPSubmitLongMessageHandler;
import io.netty.channel.ChannelFuture;
import org.marre.sms.SmsMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class CustomCMPPSubmitLongMessageHandler extends CMPPSubmitLongMessageHandler {

    private CustomCMPPServerChildEndpointEntity entity;

    private static final Logger logger = LoggerFactory.getLogger(CustomCMPPSubmitLongMessageHandler.class);

    public CustomCMPPSubmitLongMessageHandler(CustomCMPPServerChildEndpointEntity entity) {
        super(entity);
        this.entity = entity;
    }

    @Override
    protected void resetMessageContent(CmppSubmitRequestMessage msg, SmsMessage smsMessage) {
        super.resetMessageContent(msg, smsMessage);
        String destterminalId = msg.getDestterminalId()[0];
        String content = msg.getMsgContent();
        logger.info("send cmpp submit request message, account:{}, content:{}, destterminal id: {}, session: {}:{}",
                entity.getId(),
                content, destterminalId, entity.getHost(), entity.getPort());

        CustomSpringContextAware customSpringContextAware = new CustomSpringContextAware();
        ApplicationContext context = customSpringContextAware.getContext();
        VenusConfig venusConfig = context.getBean(VenusConfig.class);
        SmsService smsService = context.getBean(SmsService.class);
        CmppGatewayApplication cmppGatewayApplication = context.getBean(CmppGatewayApplication.class);
        RedisService redisService = context.getBean(RedisService.class);

        CmppSubmitResponseMessage responseMessage = new CmppSubmitResponseMessage(msg.getHeader());
        responseMessage.setRequest(msg);

        String submitTime = String.format("%ty%<tm%<td%<tH%<tM", CachedMillisecondClock.INS.now());
        BaseResult<MessageMQ> mqMessageResult = smsService.buildMessageMQ(destterminalId, content, entity.getUserName());
        if (mqMessageResult.getCode() != 0) {
            logger.info("build message mq failure, content: {}, destterminal id: {}", content, destterminalId);
            Integer result = RespCodeEnum.getSubmitResult(mqMessageResult.getCode());
            responseMessage.setResult(result + 9);
        } else {
            MessageTypeEnum messageTypeEnum = MessageTypeEnum.getByMessageType(mqMessageResult.getData().getSmsCode());
            MessageMQ<BaseBodyMessage> bodyMessage = mqMessageResult.getData();
            bodyMessage.setRequestId(responseMessage.getMsgId().toString());
            String messageMQTopic = null;
            if (messageTypeEnum.equals(MessageTypeEnum.VERIFY)) {
                messageMQTopic = venusConfig.getMqVerifySMSMessage();
            } else if (messageTypeEnum.equals(MessageTypeEnum.COLLECTOR)) {
                messageMQTopic = venusConfig.getMqCollectSMSMessage();
            } else if (messageTypeEnum.equals(MessageTypeEnum.MARKET)
                    || messageTypeEnum.equals(MessageTypeEnum.LIGHT_COLLECTOR)
                    || messageTypeEnum.equals(MessageTypeEnum.SEVERE_COLLECTOR)) {
                messageMQTopic = venusConfig.getMqMarketSMSMessage();
            } else if (messageTypeEnum.equals(MessageTypeEnum.NOTIFY)
                    || messageTypeEnum.equals(MessageTypeEnum.DEBT_SWAP)) {
                messageMQTopic = venusConfig.getMqNotifySMSMessage();
            }

            if (smsService.dispatchSMSMessage2QAsync(messageMQTopic, bodyMessage)) {
                responseMessage.setResult(0);
                CMPPGatewayMsg gatewayMsg = new CMPPGatewayMsg();
                gatewayMsg.setMsgId(responseMessage.getMsgId().toString());
                gatewayMsg.setUserName(entity.getUserName());
                gatewayMsg.setSession(cmppGatewayApplication.getHost() + ":" + cmppGatewayApplication.getUdpPort());
                gatewayMsg.setMsgContent(content);
                gatewayMsg.setDestId(entity.getSpCode());
                gatewayMsg.setDestterminalId(destterminalId);
                gatewayMsg.setSubmitTime(submitTime);
                redisService.setUserMsg(gatewayMsg);
            } else {
                responseMessage.setResult(9);
            }
        }

        DeliverMessageMQ deliverMessageMQ = DeliverMessageMQ.builder()
                .msgId(responseMessage.getMsgId().toString())
                .userName(entity.getUserName())
                .submitTime(submitTime)
                .destTerminalId(destterminalId)
                .smsStatus(String.valueOf(responseMessage.getResult()))
                .messageStatus(1)
                .build();
        BaseResult<DeliverMessageMQ> mqDeliverResult = BaseResult.success(deliverMessageMQ);
        String messageMQTopic = venusConfig.getMqDeliverSMSMessage();
        DeliverMessageMQ<BaseBodyMessage> bodyMessage = mqDeliverResult.getData();
        smsService.dispatchSMSDeliverMessage2QAsync(messageMQTopic, bodyMessage);


        // 统计
        GwStatsCacheUtil gwStatsCacheUtil = context.getBean(GwStatsCacheUtil.class);
        gwStatsCacheUtil.increment(GwStatsCacheUtil.GW_KEY_PREFIX,
                entity.getGroupName(),
                entity.getId(),
                LocalDate.now(),
                mqMessageResult.getCode() == 0 ? mqMessageResult.getData().getTplCode() : "0",
                String.valueOf(mqMessageResult.getCode()),
                1);

        List<CmppSubmitResponseMessage> respList = new ArrayList<>();
        CmppSubmitResponseMessage resp = new CmppSubmitResponseMessage(msg.getHeader().getSequenceId());
        respList.add(resp);
        if (msg.getFragments() != null) {
            for (CmppSubmitRequestMessage frag : msg.getFragments()) {
                CmppSubmitResponseMessage submitResponseMessage = new CmppSubmitResponseMessage(frag.getHeader().getSequenceId());
                respList.add(submitResponseMessage);
            }
        }

        for (CmppSubmitResponseMessage submitResponseMessage : respList) {
            final String msgId = submitResponseMessage.getMsgId().toString();
            final int sequenceId = submitResponseMessage.getHeader().getSequenceId();
            ChannelFuture promise = ChannelUtil.asyncWriteToEntity(entity, submitResponseMessage, future -> {
                if (!future.isSuccess()) {
                    logger.warn("send summit response message to {} failure, msgId={}, seq={}",
                            entity.getUserName(), msgId, sequenceId);
                } else {
                    logger.info("send summit response message to {} succeed, msgId:{}, seq:{}",
                            entity.getUserName(), msgId, sequenceId);
                }
            });

            if (promise == null) {
                logger.error("send summit response message to {} failure, msgId={}, seq={}",
                        entity.getUserName(), msgId, sequenceId);
            }
        }

        try {
            redisService.setUseSession(entity.getUserName());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}