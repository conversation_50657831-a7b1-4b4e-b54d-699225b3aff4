package com.xhqb.spectre.channel.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpException;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

@Slf4j
public class HttpUtil {

    /**
     * 发送Post请求
     *
     * @param requestUrl
     * @param jsonParam
     * @param headerMap
     * @return
     */
    public static String doPost(String requestUrl, String jsonParam, Map<String, String> headerMap) throws HttpException {
        String resultStr = null;
        HttpURLConnection httpUrlConn = null;
        InputStream inputStream = null;
        InputStreamReader inputStreamReader = null;
        BufferedReader bufferedReader = null;
        if (StringUtils.isBlank(requestUrl)) {
            return null;
        }
        try {
            StringBuffer buffer = new StringBuffer();
            // 建立连接
            URL url1 = new URL(requestUrl);
            httpUrlConn = (HttpURLConnection) url1.openConnection();
            httpUrlConn.setConnectTimeout(8000); //8s超时
            httpUrlConn.setDoOutput(true);
            httpUrlConn.setRequestMethod("POST");
            httpUrlConn.setRequestProperty("Accept-Charset", "UTF-8");
            httpUrlConn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            for (String key : headerMap.keySet()) {
                httpUrlConn.setRequestProperty(key, headerMap.get(key));
            }
            String charset = headerMap.get("Accept-Charset");
            byte[] bytesParam = jsonParam.getBytes(StringUtils.defaultString(charset, "utf-8"));
            httpUrlConn.getOutputStream().write(bytesParam); // 输入参数
            if (httpUrlConn.getResponseCode() >= 300) {
                throw new HttpException("HTTP Request is not success, Response code is " + httpUrlConn.getResponseCode()
                        + ", and Response message is " + httpUrlConn.getResponseMessage());
            }
            // 获取输入流
            inputStream = httpUrlConn.getInputStream();
            inputStreamReader = new InputStreamReader(inputStream, StringUtils.defaultString(charset, "utf-8"));
            bufferedReader = new BufferedReader(inputStreamReader);
            // 读取返回结果
            String str = null;
            while ((str = bufferedReader.readLine()) != null) {
                buffer.append(str);
            }
            resultStr = buffer.toString();
            //为统一监控, 此处判断返回报文是否为空, 抛出自定义异常
            if (StringUtils.isEmpty(resultStr)) {

                //todo
                //throw new MsgSenderException(MsgSen进入cmpp短信单条发送流程dResultEnum.NULL_RETURN, "Http请求供应商接口返回报文为空");
            }
            // ServiceCounter.incNormal(requestUrl);

        } catch (HttpException httpException) {
            log.warn("http请求异常", httpException);
            throw httpException;
        } catch (Exception e) {
            log.warn("post请求异常：{}", e.getMessage());
            return null;
        } finally {
            //Metrics数据统计

            try {
                // 释放资源
                if (bufferedReader != null) {
                    bufferedReader.close();
                }
                if (inputStreamReader != null) {
                    inputStreamReader.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
                if (httpUrlConn != null) {
                    httpUrlConn.disconnect();
                }
            } catch (IOException e) {
                log.error("执行http请求后释放资源异常:", e);
            }
        }
        return resultStr;
    }
}
