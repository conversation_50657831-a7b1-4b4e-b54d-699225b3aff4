package com.xhqb.spectre.channel.http.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xhqb.spectre.channel.entity.BaseResult;
import com.xhqb.spectre.channel.util.HttpUtil;
import com.xhqb.spectre.common.dal.entity.ChannelAccount;
import com.xhqb.spectre.channel.entity.SendMessage;
import com.xhqb.spectre.common.dal.entity.HttpRecord;
import com.xhqb.spectre.common.enums.DeliveryStatusEnum;
import com.xhqb.spectre.common.enums.ExchangeProviderEnum;
import com.xhqb.spectre.service.util.CommonRedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Component
@Slf4j
public class ChuanglanChannelhandler extends AbstractChannelHandler {

    private static final Logger logger = LoggerFactory.getLogger(ChuanglanChannelhandler.class);
    private static final String ACCOUNT = "account"; //账号
    private static final String PASS_WORD = "password"; //密码
    private static final String MSG = "msg"; //内容
    private static final String REPORT = "report"; //是否需要回执报告
    private static final String PHONE = "phone"; //手机号码


    private static final String RESULT_SUCCESS_CODE = "0";
    private static final int CHECK_PARAMETER = 0;
    private static final String UUID = "uid";
    private static final String MSGID = "msgId";
    private static final String CODE = "code";
    @Autowired
    private RedisTemplate redisTemplate;

    private String doPost(String requestUrl, String jsonParam) throws HttpException {
        Map<String, String> headerMap = new HashMap();
        headerMap.put("Accept-Charset", "UTF-8");
        headerMap.put("Content-Type", "application/json");
        return HttpUtil.doPost(requestUrl, jsonParam, headerMap);
    }

    @Override
    public String getChannelCode() {
        return ExchangeProviderEnum.CHUANGLAN.getProviderName();
    }

    //sendUrl reportUrl
    @Override
    public BaseResult post(SendMessage message) {
        String resultCode = "";
        try {
            ChannelAccount channelAccount = message.getChannelAccount();
            logger.info("进入创蓝单条发送，参数:{}", message.toString());
            String encContent = URLEncoder.encode(message.getContent(), "UTF-8");
            JSONObject param = new JSONObject();
            JSONObject channelInfo = JSONObject.parseObject(channelAccount.getJsonMapping());
            param.put(ACCOUNT, channelAccount.getKey());
            param.put(PASS_WORD, channelInfo.get("password"));
            param.put(MSG, encContent);
            param.put(PHONE, message.getPhone());
            String chuanglanIsReport = channelInfo.getString("reportUrl");
            param.put(REPORT, chuanglanIsReport);
            String requestData = param.toString();
            logger.info("创蓝 sendSms请求数据:{}", requestData);

            String chuanglanApiUrl = channelInfo.get("sendUrl").toString();
            String resp = this.doPost(chuanglanApiUrl, requestData);
            /*int retryTime = 0;
            while (StringUtils.isBlank(resp) && retryTime <= 5) {
                logger.warn("send http request and return null, retry * {}", retryTime);
                resp = this.doPost(chuanglanApiUrl, requestData);
                retryTime++;
            }*/
            if (StringUtils.isBlank(resp)) {
                throw new Exception("创蓝单条发送无返回");
            }
            logger.info("创蓝 sendSms响应数据:{}", resp);
            JSONObject jsonRs = JSONObject.parseObject(resp);
            resultCode = jsonRs.getString("code");
            if (RESULT_SUCCESS_CODE.equals(resultCode)) {
                return BaseResult.success();
            }
            int count = CommonRedisUtil.increaseFailCount(redisTemplate, ExchangeProviderEnum.CHUANGLAN.getProviderName(), 1);
            ValueOperations<String, Object> valueOperations = redisTemplate.opsForValue();
            valueOperations.set(CommonRedisUtil.PARTNER_FAIL_KEY + ExchangeProviderEnum.CHUANGLAN.getProviderName(), count, 1, TimeUnit.HOURS);
            //TODO 错误码转换
            return BaseResult.systemError();
        } catch (Exception ex) {
            logger.error("创蓝单条发送异常", ex);
            return BaseResult.systemError();
        }

    }

    /**
     * 创蓝回执接口文档店址：https://zz.253.com/api_doc/kai-fa-yin-dao.html
     */
    @Override
    public List<HttpRecord> receiveReport(Map<String, Object> params) {
        logger.info("创蓝回执参数:{}", params.toString());
        HttpRecord httpRecord = HttpRecord.builder()
                .channelMsgId(params.get("msgid").toString())
                .build();

        //运营商时间
        String reportTime = params.get("reportTime").toString();
        String notifyTime = params.get("notifyTime").toString();
        try {
            SimpleDateFormat reportTimeSdf = new SimpleDateFormat("yyMMddHHmm");
            httpRecord.setReportTime(reportTimeSdf.parse(reportTime));
            httpRecord.setRealSendTime(reportTimeSdf.parse(notifyTime));
        } catch (ParseException e) {
            logger.warn("chuanglan date parse error,reportTime:{}", reportTime);
        }
        httpRecord.setMobile(params.get("mobile").toString());
        httpRecord.setCode(params.get("status").toString());
        httpRecord.setDesc(params.get("statusDesc").toString());
        httpRecord.setStatus(getDeliveryStatus(params.get("status").toString()));
        httpRecord.setBillCount(Integer.valueOf((String) params.get("length")));
        httpRecord.setChannelCode(getChannelCode());
        return Lists.newArrayList(httpRecord);
    }

    public Integer getDeliveryStatus(String status) {
        if ("DELIVRD".equals(status)) {
            return DeliveryStatusEnum.SUCCESS.getCode();
        }
        return DeliveryStatusEnum.FAILURE.getCode();
    }
}
