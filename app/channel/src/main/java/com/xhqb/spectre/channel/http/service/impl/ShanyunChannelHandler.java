package com.xhqb.spectre.channel.http.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.channel.entity.BaseResult;
import com.xhqb.spectre.channel.util.HttpUtil;
import com.xhqb.spectre.common.dal.entity.HttpRecord;
import com.xhqb.spectre.common.dal.entity.ChannelAccount;
import com.xhqb.spectre.channel.entity.SendMessage;
import com.xhqb.spectre.common.enums.ExchangeProviderEnum;
import com.xhqb.spectre.common.enums.DeliveryStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * json_mapping
 * password（密钥）,returnUrl（回执返回地址）
 */
@Component
@Slf4j
public class ShanyunChannelHandler extends AbstractChannelHandler {

    @Value("${shanyun.api.send.url:http://apiext.szshanyun.com:8089/receive}")
    private String sendUrl;

    @Override
    public String getChannelCode() {
        return ExchangeProviderEnum.SHANYUN.getProviderName();
    }

    @Override
    public BaseResult post(SendMessage message) {
        try {
            ChannelAccount channelAccount = message.getChannelAccount();
            JSONObject jsonMapping = JSONObject.parseObject(channelAccount.getJsonMapping());

            log.debug("进入闪云单条发送，参数:{}, {}", channelAccount, message);
            String timestamp = String.valueOf(System.currentTimeMillis());

            JSONObject send = new JSONObject();
            send.put("phoneNumber", message.getPhone());
            send.put("outOrderId", message.getOrderId());

            JSONObject businessBody = new JSONObject();
            businessBody.put("sendList", Collections.singletonList(send));
            businessBody.put("content", message.getContent());
            businessBody.put("signatureStr", message.getSignName());
            if (!StringUtils.isEmpty(jsonMapping.get("returnUrl"))) {
                businessBody.put("returnUrl", jsonMapping.get("returnUrl"));
            }

            JSONObject SendRequest = new JSONObject();
            SendRequest.put("businessBody", businessBody);
            SendRequest.put("userAccount", channelAccount.getKey());
            SendRequest.put("timestamp", timestamp);

            SendRequest.put("sign", getSign(businessBody, jsonMapping.getString("password"), timestamp));

            String param = SendRequest.toJSONString();

            log.info("闪云发送请求数据:{}", param);
            String resp = this.doPost(sendUrl, param);
            log.info("闪云 sendSms响应数据:{}", resp);
            if (StringUtils.isEmpty(resp)) {
                throw new Exception("闪云单条发送无返回");
            }

            JSONObject result = JSONObject.parseObject(resp);
            //接口返回数据
            //"message":"操作成功","statusCode":1,"systemOrderId":null}
            if ("1".equals(result.get("statusCode").toString())) {
                return BaseResult.success();
            }

            //TODO 错误码转换
            return BaseResult.systemError();

        } catch (Exception e) {
            log.error("闪云单条发送异常了:{}", e.getMessage());
            return BaseResult.systemError();
        }
    }

    @Override
    public List<HttpRecord> receiveReport(Map<String, Object> params) {
        log.info("解析回执;");

        if (StringUtils.isEmpty(params.get("sendStatus"))) {
            //空数据
            return new ArrayList<>();
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        HttpRecord httpRecord = new HttpRecord();
        httpRecord.setChannelMsgId(params.get("outOrderId").toString());
        httpRecord.setChannelCode(getChannelCode());
        httpRecord.setMobile(params.get("phoneNumber").toString());

        try {
            Date date = sdf.parse(params.get("receiveTime").toString());
            httpRecord.setReportTime(date);
            httpRecord.setRealSendTime(date);
        } catch (ParseException e) {
            log.warn("shanyun date parse error,reportTime:{}", params.get("receiveTime"));
            httpRecord.setReportTime(new Date());
            httpRecord.setRealSendTime(new Date());
        }

        httpRecord.setStatus(getDeliveryStatus(params.get("sendStatus").toString()));
        httpRecord.setCode(params.get("sendStatus").toString());
        httpRecord.setDesc(params.get("message").toString());
        httpRecord.setBillCount(1);

        return Collections.singletonList(httpRecord);
    }

    private String getSign(Map businessBody, String userSecret, String timestamp) {
        String k = JSONObject.toJSONString(businessBody) + userSecret + timestamp;
        try {
            byte[] key = k.getBytes("UTF-8");
            return DigestUtils.md5DigestAsHex(key);
        } catch (UnsupportedEncodingException e) {
            log.error("生产签名失败", k, e);
        }
        return null;
    }

    private String doPost(String requestUrl, String jsonParam) throws HttpException {
        Map<String, String> headerMap = new HashMap();
        headerMap.put("Accept-Charset", "UTF-8");
        headerMap.put("Content-Type", "application/json");
        return HttpUtil.doPost(requestUrl, jsonParam, headerMap);
    }

    @Override
    public Integer getDeliveryStatus(String status) {
        if ("1".equals(status)) {
            return DeliveryStatusEnum.SUCCESS.getCode();
        }
        return DeliveryStatusEnum.FAILURE.getCode();
    }
}
