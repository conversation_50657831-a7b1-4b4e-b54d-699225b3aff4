package com.xhqb.spectre.channel.http.context;

import com.xhqb.spectre.channel.http.service.impl.AbstractChannelHandler;
import com.xhqb.spectre.channel.http.service.IPoster;
import com.xhqb.spectre.channel.http.service.IRecipient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class ChannelContext implements InitializingBean {

    public static ConcurrentHashMap<String, AbstractChannelHandler> handlers = new ConcurrentHashMap<>();

    @Autowired
    private List<AbstractChannelHandler> channelHandlerList;

    public IPoster getPoster(String channelCode) {
        return handlers.get(channelCode);
    }

    public IRecipient getRecipient(String channelCode) {
        return handlers.get(channelCode);
    }

    @Override
    public void afterPropertiesSet() throws BeansException {
        channelHandlerList.forEach(handler -> {
            log.info("载入渠道:{}", handler.getChannelCode());
            handlers.put(handler.getChannelCode(), handler);
        });
        log.info("初始化渠道总数:{}", handlers.size());
    }
}
