package com.xhqb.spectre.channel.http.service.impl;

import com.xhqb.spectre.channel.entity.BaseResult;
import com.xhqb.spectre.channel.entity.SendMessage;
import com.xhqb.spectre.common.dal.entity.HttpRecord;
import com.xhqb.spectre.common.enums.DeliveryStatusEnum;
import com.xhqb.spectre.common.enums.ExchangeProviderEnum;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class MiaoxinChannelHandler extends AbstractChannelHandler {

    private static final Logger logger = LoggerFactory.getLogger(MiaoxinChannelHandler.class);

    private static final String RESULT_SUCCESS_CODE = "0";
    private static final int ALLOW_MAX_BATCH_SIZE = 500;
    private static final String UID = "uid";
    private static final String PASS_WORD = "pwd";
    private static final String MOBILE = "mobile";
    private static final String SRCPHONE = "srcphone";
    private static final String MSG = "msg";
    @Autowired
    private RedisTemplate redisTemplate;


    public static String doPost(Map<String, Object> paramers, String url) {
        return null;
    }

    @Override
    public String getChannelCode() {
        return ExchangeProviderEnum.MIAOXIN.getProviderName();
    }

    //json_mapping sendUrl, batchSendUrl
    @Override
    public BaseResult post(SendMessage message) {
        return BaseResult.success();
    }

    @Override
    public List<HttpRecord> receiveReport(Map<String, Object> params) {
        return null;
    }

    public Integer getDeliveryStatus(String status) {
        if ("0".equals(status)) {
            return DeliveryStatusEnum.SUCCESS.getCode();
        }
        return DeliveryStatusEnum.FAILURE.getCode();
    }
}
