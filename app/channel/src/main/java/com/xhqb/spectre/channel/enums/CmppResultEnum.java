package com.xhqb.spectre.channel.enums;

import java.util.Objects;

public enum CmppResultEnum {

    /**
     * 失败
     */
    FAILED("FAIL", "失败"),

    /**
     * 成功
     */
    SUCCESS("SUCCESS", "成功"),

    /**
     * 从h5loan移过来,之前都用这个
     */
    SUCCESS_RESPONSE("SUCCESS_RESPONSE", "操作成功"),

    /**
     * 系统异常
     */
    EXCEPTION("EXCEPTION", "系统异常"),

    /**
     * 请求参数错误
     */
    ILLEGAL_REQ("ILLEGAL", "请求非法"),
    /**
     * 重复请求
     */
    DUPLICATE_REQ("DUPLICATE", "重复请求"),
    /**
     * 连接超时
     */
    REQ_TIMEOUT("TIMEOUT", "连接超时"),

    /**
     * 未知结果码，出现时，可能是client端未更新到最新的ExcuteResultEnum导致
     */
    UNKNOW("UNKNOW", "请求失败，请稍后重试"),

    CHANNEL_MISS("CHANNEL_MISS", "渠道链接不存在");


    /**********************************************************************************/

    /**
     * 结果码
     */
    private String code;

    /**
     * 结果内容
     */
    private String message;

    /**
     * Constructor.
     *
     * @param code
     * @param message
     */
    CmppResultEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * get enum by resultCode
     *
     * @param resultCode
     * @return
     */
    public static CmppResultEnum getByResultCode(String resultCode) {
        if (Objects.isNull(resultCode)) {
            return null;
        }
        for (CmppResultEnum resultValue : values()) {
            if (resultValue.getCode().equals(resultCode)) {
                return resultValue;
            }
        }
        return UNKNOW; //出现未知的结果码时，可能是client端未更新到最新的ExcuteResultEnum导致
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
