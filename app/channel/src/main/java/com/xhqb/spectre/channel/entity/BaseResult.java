package com.xhqb.spectre.channel.entity;

import lombok.*;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/13 18:44
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseResult implements Serializable {

    private static final long serialVersionUID = 2088112388823662838L;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 响应码
     */
    private String resultCode;

    /**
     * 描述
     */
    private String resultMsg;

    /**
     * 是否需要重试
     *
     * @return
     */
    public boolean needRetrySend() {
        return !success && ResultEnum.RETRY_SEND.getCode().equals(resultCode);
    }

    public boolean isChannelMiss() {
        return !success && ResultEnum.CHANNEL_MISS.getCode().equals(resultCode);
    }

    public boolean isSystemError() {
        return !success && ResultEnum.SYSTEM_ERROR.getCode().equals(resultCode);
    }

    public static BaseResult build(CmppResponse cmppResponse) {
        return BaseResult.builder()
                .success(cmppResponse.isSuccess())
                .resultCode(cmppResponse.getResultCode())
                .resultMsg(cmppResponse.getResultMsg())
                .build();
    }

    public static BaseResult success() {
        return BaseResult.builder()
                .success(true)
                .resultCode(ResultEnum.SUCCESS.getCode())
                .resultMsg(ResultEnum.SUCCESS.getMessage())
                .build();
    }

    public static BaseResult systemError() {
        return BaseResult.builder()
                .success(false)
                .resultCode(ResultEnum.SYSTEM_ERROR.getCode())
                .resultMsg(ResultEnum.SYSTEM_ERROR.getMessage())
                .build();
    }

    public static BaseResult retrySend() {
        return BaseResult.builder()
                .success(false)
                .resultCode(ResultEnum.RETRY_SEND.getCode())
                .resultMsg(ResultEnum.RETRY_SEND.getMessage())
                .build();
    }

    public static BaseResult channelMiss() {
        return BaseResult.builder()
                .success(false)
                .resultCode(ResultEnum.CHANNEL_MISS.getCode())
                .resultMsg(ResultEnum.CHANNEL_MISS.getMessage())
                .build();
    }

    @Getter
    @AllArgsConstructor
    enum ResultEnum {

        /**
         * 成功
         */
        SUCCESS("SUCCESS", "成功"),

        /**
         * 系统异常
         */
        SYSTEM_ERROR("SYSTEM_ERROR", "系统异常"),

        /**
         * 发送重试（此响应码用于发送重试，误删）
         */
        RETRY_SEND("RETRY_SEND", "发送重试"),

        /**
         * 渠道链接不存在
         */
        CHANNEL_MISS("CHANNEL_MISS", "渠道链接不存在");

        /**
         * 结果码
         */
        private String code;

        /**
         * 结果内容
         */
        private String message;
    }
}
