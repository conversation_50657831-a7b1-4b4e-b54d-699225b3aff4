package com.xhqb.spectre.channel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/13 17:46
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmppRequestDTO implements Serializable {

    private static final long serialVersionUID = 6901356821511221266L;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道账号ID
     */
    private String channelId;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 附加内容,不做业务处理,回执时原样返回
     */
    private String addition;

    /**
     * 重发次数
     */
    private Integer resend;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 请求来源 1：http 2：cmpp
     */
    private Integer reqSrc;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * cmpp网关用户名
     */
    private String gatewayUserName;

    /**
     * 订单表名后缀(yyyyMM)[由PosterConsumer类中进行填充]
     */
    private String tableNameSuffix;
}
