package com.xhqb.spectre.channel.http.service.impl;

import com.xhqb.spectre.channel.entity.BaseResult;
import com.xhqb.spectre.channel.entity.SendMessage;
import com.xhqb.spectre.channel.util.HttpUtil;
import com.xhqb.spectre.common.dal.entity.HttpRecord;
import com.xhqb.spectre.common.enums.DeliveryStatusEnum;
import com.xhqb.spectre.common.enums.ExchangeProviderEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpException;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * json_mapping
 * password（密钥）,returnUrl（回执返回地址）
 */
@Component
@Slf4j
public class KaifengChannelHandler extends AbstractChannelHandler {


    @Override
    public String getChannelCode() {
        return ExchangeProviderEnum.KAIFENG.getProviderName();
    }

    @Override
    public BaseResult post(SendMessage message) {
        return BaseResult.success();
    }

    @Override
    public List<HttpRecord> receiveReport(Map<String, Object> params) {
        return null;
    }

    private String doPost(String requestUrl, String jsonParam) throws HttpException {
        Map<String, String> headerMap = new HashMap();
        headerMap.put("Accept-Charset", "UTF-8");
        headerMap.put("Content-Type", "application/json");
        return HttpUtil.doPost(requestUrl, jsonParam, headerMap);
    }

    @Override
    public Integer getDeliveryStatus(String status) {
        if ("DELIVRD".equals(status)) {
            return DeliveryStatusEnum.SUCCESS.getCode();
        }
        return DeliveryStatusEnum.FAILURE.getCode();
    }
}
