package com.xhqb.spectre.channel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/13 17:46
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmppResponse implements Serializable {

    private static final long serialVersionUID = 8380660117152599486L;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 结果枚举
     */
    private String resultCode;

    /**
     * 结果描述
     */
    private String resultMsg;
}
