package com.xhqb.spectre.channel.http.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.channel.entity.BaseResult;
import com.xhqb.spectre.common.dal.entity.HttpRecord;
import com.xhqb.spectre.common.dal.entity.ChannelAccount;
import com.xhqb.spectre.channel.entity.SendMessage;
import com.xhqb.spectre.common.enums.ExchangeProviderEnum;
import com.xhqb.spectre.common.enums.DeliveryStatusEnum;
import com.xhqb.spectre.service.util.CommonRedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.security.MessageDigest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class BoshitongChannelHandler extends AbstractChannelHandler {

    private static final Logger logger = LoggerFactory.getLogger(BoshitongChannelHandler.class);

    private static final String RESULT_SUCCESS_CODE = "0";
    private static final int ALLOW_MAX_BATCH_SIZE = 500;
    private static final String UID = "uid";
    private static final String PASS_WORD = "pwd";
    private static final String MOBILE = "mobile";
    private static final String SRCPHONE = "srcphone";
    private static final String MSG = "msg";
    @Autowired
    private RedisTemplate redisTemplate;

    public static String getMd5Sign(String data) {
        StringBuffer sb = new StringBuffer();
        try {
            MessageDigest md5 = MessageDigest.getInstance("md5");
            byte[] b = data.getBytes();
            byte[] digest = md5.digest(b);
            char[] chars = new char[]{'0', '1', '2', '3', '4', '5',
                    '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

            for (byte bb : digest) {
                sb.append(chars[(bb >> 4) & 15]);
                sb.append(chars[bb & 15]);
            }

        } catch (Exception e) {
            logger.info("Exception:{}", e.getMessage());
        }
        return sb.toString();
    }

    public static String doPost(Map<String, Object> parameters, String url) {
        HttpClient httpClient = new HttpClient();
        httpClient.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, "UTF-8");
        httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(10000);
        PostMethod postMethod = new PostMethod(url);
        List<NameValuePair> nvps = new ArrayList<NameValuePair>();
        Set<String> keySet = parameters.keySet();
        for (String key : keySet) {
            nvps.add(new NameValuePair(key, parameters.get(key).toString()));
        }
        if (nvps != null && nvps.size() > 0) {
            NameValuePair[] nvpInfo = new NameValuePair[nvps.size()];
            for (int i = 0; i < nvps.size(); i++) {
                nvpInfo[i] = nvps.get(i);
            }
            postMethod.setRequestBody(nvpInfo);
        }
        postMethod.addRequestHeader("Content-type", "application/x-www-form-urlencoded");
        postMethod.getParams().setParameter(HttpMethodParams.SO_TIMEOUT, 10000);
        postMethod.getParams().setParameter(HttpMethodParams.RETRY_HANDLER, new DefaultHttpMethodRetryHandler());
        try {
            int statusCode = httpClient.executeMethod(postMethod);
            if (statusCode != HttpStatus.SC_OK) {
                System.out.println("访问出错，出错信息为:" + postMethod.getStatusLine());
            } else {
                return new String(postMethod.getResponseBodyAsString().getBytes("UTF-8"));
            }
        } catch (Exception e) {
            logger.info("Exception:{}", e.getMessage());
        } finally {
            postMethod.releaseConnection();
            httpClient.getHttpConnectionManager().closeIdleConnections(0);
        }
        return null;
    }

    @Override
    public String getChannelCode() {
        return ExchangeProviderEnum.BOSHITONG.getProviderName();
    }

    //json_mapping sendUrl, batchSendUrl
    @Override
    public BaseResult post(SendMessage message) {
        try {
            logger.info("进入博士通单条发送，参数:{}", message.toString());
            ChannelAccount channelAccount = message.getChannelAccount();
            Map<String, Object> parMap = new HashMap<>();
            JSONObject channelInfo = JSONObject.parseObject(channelAccount.getJsonMapping());
            parMap.put(UID, channelInfo.get("uid"));
            parMap.put(PASS_WORD, channelInfo.get("password"));
            parMap.put(MOBILE, message.getPhone());
            parMap.put(SRCPHONE, channelInfo.get("srcphone"));
            parMap.put(MSG, URLEncoder.encode(message.getContent(), "UTF-8"));

            String boshitongSmsUrl = channelInfo.get("sendUrl").toString();
            String resultStr = doPost(parMap, boshitongSmsUrl);
            logger.info("博士通sendSms请求数据:{}", parMap.toString());
            int retryTime = 0;
            while (StringUtils.isBlank(resultStr) && retryTime <= 5) {
                logger.warn("send http request and return null, retry * {}", retryTime);
                resultStr = doPost(parMap, boshitongSmsUrl);
                retryTime++;
            }
            if (StringUtils.isBlank(resultStr)) {
                throw new Exception("博士通http请求返回空");
            }
            logger.info("博士通 sendSms响应数据:{}", resultStr);
            if (resultStr.contains(",")) {
                if (RESULT_SUCCESS_CODE.equals(resultStr.split(",")[0])) {
                    return BaseResult.success();
                }
            }
            int count = CommonRedisUtil.increaseFailCount(redisTemplate, ExchangeProviderEnum.BOSHITONG.getProviderName(), 1);
            ValueOperations<String, Object> valueOperations = redisTemplate.opsForValue();
            valueOperations.set(CommonRedisUtil.PARTNER_FAIL_KEY + ExchangeProviderEnum.BOSHITONG.getProviderName(), count, 1, TimeUnit.HOURS);

            //TODO 错误码转换，http渠道发送状态更新
            return BaseResult.systemError();
        } catch (Exception e) {
            logger.error("博士通单条短信发送异常", e);
            return BaseResult.systemError();
        }

    }

    @Override
    public List<HttpRecord> receiveReport(Map<String, Object> params) {
        JSONArray arr = JSONArray.parseArray(params.get("content").toString());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmss");
        List<HttpRecord> httpRecords = new ArrayList<>();
        for (Integer i = 0; i < arr.size(); i++) {
            if (params.get("state") == null) {
                continue;
            }
            HttpRecord httpRecord = HttpRecord.builder().build();
            httpRecord.setChannelMsgId(arr.getJSONObject(i).get("msgid").toString());
            httpRecord.setMobile(arr.getJSONObject(i).get("dstphone").toString());
            httpRecord.setChannelCode(getChannelCode());
            httpRecord.setStatus(getDeliveryStatus(params.get("state").toString()));
            try {
                Date date = sdf.parse(params.get("logtime").toString());
                httpRecord.setReportTime(date);
                httpRecord.setRealSendTime(date);
            } catch (ParseException e) {
                log.warn("shanyun date parse error,reportTime:{}", params.get("receiveTime"));
            }

            httpRecord.setCode(params.get("state").toString());
            httpRecord.setBillCount(Integer.parseInt(params.get("pk_total").toString()));
            httpRecords.add(httpRecord);
        }
        return httpRecords;
    }

    public Integer getDeliveryStatus(String status) {
        if ("DELIVRD".equals(status)) {
            return DeliveryStatusEnum.SUCCESS.getCode();
        }
        return DeliveryStatusEnum.FAILURE.getCode();
    }
}
