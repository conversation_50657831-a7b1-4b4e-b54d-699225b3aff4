package com.xhqb.spectre.channel.exception;

import com.xhqb.spectre.channel.entity.SendMessage;
import org.springframework.core.NestedRuntimeException;

public class RetrySendException extends NestedRuntimeException {

    private SendMessage sendMessage;

    public RetrySendException(SendMessage sendMessage, String message) {
        super(message);
        this.sendMessage = sendMessage;
    }

    public RetrySendException(SendMessage sendMessage, String message, Throwable cause) {
        super(message, cause);
        this.sendMessage = sendMessage;
    }

    public SendMessage getSendMessage() {
        return sendMessage;
    }
}
