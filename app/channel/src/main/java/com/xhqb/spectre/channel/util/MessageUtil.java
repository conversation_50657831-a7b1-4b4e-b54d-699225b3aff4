package com.xhqb.spectre.channel.util;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
public class MessageUtil {

    public static Integer count(String message) {
        int count = message.length();
        if (count <= 70) {
            return 1;
        }
        BigDecimal length = new BigDecimal(count);
        BigDecimal n = length.divide(new BigDecimal(67), 0, RoundingMode.CEILING);
        return Integer.valueOf(n.toString());
    }
}
