package com.xhqb.spectre.channel.service.impl;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.channel.constant.LuciferConstant;
import com.xhqb.spectre.channel.entity.*;
import com.xhqb.spectre.channel.service.CmppChannelService;
import com.xhqb.spectre.channel.util.HttpUtil;
import io.prometheus.client.Collector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class CmppChannelServiceImpl implements CmppChannelService {

    @Value("${cmpp.xhqb.send.url}")
    private String cmppXhqbSendUrl;

    /**
     * 单条短信发送
     *
     * @param message
     * @return
     */
    @Override
    public BaseResult send(SendMessage message) {
        long start = 0;
        String reqParamStr = "";
        try {
            CmppRequestDTO requestItem = CmppRequestDTO.builder()
                    .tplCode(message.getTplCode())
                    .channelCode(message.getChannelAccount().getChannelCode())
                    .channelId(String.valueOf(message.getChannelAccount().getId()))
                    .mobile(message.getPhone())
                    .content(message.getSignName() + message.getContent())
                    .addition(message.getOrderId())
                    .resend(message.getResend())
                    .smsTypeCode(message.getSmsCode())
                    .reqSrc(message.getReqSrc())
                    .requestId(message.getRequestId())
                    .gatewayUserName(message.getGatewayUserName())
                    .tableNameSuffix(message.getTableNameSuffix())
                    .build();
            CmppRequest request = CmppRequest.builder()
                    .messageDtoList(Collections.singletonList(requestItem))
                    .build();
            reqParamStr = JSON.toJSONString(request);

//            log.info("短信发送请求参数：{}", reqParam);
            //http请求耗时埋点
            start = System.nanoTime();
            String responseStr = this.doPost(cmppXhqbSendUrl, reqParamStr);
            LuciferConstant.DISPATCH_HTTP_REQUEST_TIME.labels(message.getSmsCode()).observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
//            log.info("短信发送响应值：{}，订单号：{}", responseStr, message.getOrderId());

            if (StringUtils.isEmpty(responseStr)) {
                log.warn("短信发送响应值为空，请求参数：{}", reqParamStr);
                return BaseResult.systemError();
            }
            CmppResponse result = JSON.parseObject(responseStr, CmppResponse.class);
            if (!result.isSuccess()) {
                log.warn("短信发送失败，请求参数：{}，响应值：{}", reqParamStr, responseStr);
            }

            return BaseResult.build(result);
        } catch (HttpException e) {
            if (start > 0) {
                LuciferConstant.DISPATCH_HTTP_REQUEST_TIME.labels(message.getSmsCode()).observe((System.nanoTime() - start) / Collector.NANOSECONDS_PER_SECOND);
            }
            log.error("短信发送http异常，请求参数：{}", reqParamStr, e);
            //HttpException的场景，暂时不重试
            return BaseResult.systemError();
        } catch (Exception e) {
            log.error("短信发送异常，请求参数：{}", reqParamStr, e);
            return BaseResult.systemError();
        }
    }

    private String doPost(String requestUrl, String jsonParam) throws HttpException {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Accept-Charset", "UTF-8");
        headerMap.put("Content-Type", "application/json");
        return HttpUtil.doPost(requestUrl, jsonParam, headerMap);
    }
}
