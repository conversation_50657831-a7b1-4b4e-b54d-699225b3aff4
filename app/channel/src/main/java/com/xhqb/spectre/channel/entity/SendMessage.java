package com.xhqb.spectre.channel.entity;

import com.xhqb.spectre.common.dal.entity.ChannelAccount;
import com.xhqb.spectre.common.mq.ChannelCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendMessage {

    private String appCode;

    private String phone;

    private String province;

    private String city;

    private String isp;

    private String smsCode;

    private List<ChannelCode> channelCodeSet;

    private String orderId;

    /**
     * 签名code
     */
    private String signName;

    private Integer sliceId;

    /**
     * 发送时间
     */
    private String sendTime;

    private Integer sendType;

    private String receiveTime;

    /**
     * 模板
     */
    private String tplCode;

    private String content;

    private List<String> paramMap;

    /**
     * 请求来源 1：http 2：cmpp
     */
    private Integer reqSrc;

    private String requestId;

    private Integer batchId;

    private Integer resend;

    /**
     * 手机号状态，-1：未查询；0：空号；1：正常；2：停机；3：库无；4：沉默号；5：风险号；99：未知
     */
    private Long phoneStatus;

    /**
     * cmpp网关用户名
     */
    private String gatewayUserName;

    private ChannelAccount channelAccount;

    /**
     * 业务批次号
     */
    private String bizBatchId;

    /**
     * 是否回调消息中心（0：不回调 1：回调）
     */
    private Integer callMetis;

    /**
     * 订单表名后缀(yyyyMM)[由PosterConsumer类中进行填充]
     */
    private String tableNameSuffix;

}
