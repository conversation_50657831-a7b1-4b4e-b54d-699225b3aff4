package com.xhqb.spectre.channel.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/13 18:16
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmppRequest implements Serializable {

    private static final long serialVersionUID = 6901356821511221266L;

    private List<CmppRequestDTO> messageDtoList;
}
