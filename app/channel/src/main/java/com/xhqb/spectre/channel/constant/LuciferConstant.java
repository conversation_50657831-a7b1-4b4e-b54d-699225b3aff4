package com.xhqb.spectre.channel.constant;

import com.xhqb.kael.lucifer.telemetry.PrometheusHistogramMetrics;
import io.prometheus.client.Histogram;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/18 15:40
 * @Description:
 */
public final class LuciferConstant {

    /**
     * Dispatch分发的http接口响应时间
     */
    public static final Histogram DISPATCH_HTTP_REQUEST_TIME = new PrometheusHistogramMetrics("dispatch_http_request_time", "分发短信的http请求时间")
            .createWithLabels("smsType");
}
