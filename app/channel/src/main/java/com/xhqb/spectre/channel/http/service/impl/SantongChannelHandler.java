package com.xhqb.spectre.channel.http.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.channel.entity.BaseResult;
import com.xhqb.spectre.channel.util.HttpUtil;
import com.xhqb.spectre.common.dal.entity.HttpRecord;
import com.xhqb.spectre.common.dal.entity.ChannelAccount;
import com.xhqb.spectre.channel.entity.SendMessage;
import com.xhqb.spectre.common.enums.ExchangeProviderEnum;
import com.xhqb.spectre.common.enums.DeliveryStatusEnum;
import com.xhqb.spectre.service.util.CommonRedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class SantongChannelHandler extends AbstractChannelHandler {

    private static final Logger logger = LoggerFactory.getLogger(SantongChannelHandler.class);
    private static final String ACCOUNT = "account"; //账号
    private static final String PASS_WORD = "password"; //密码
    private static final String PHONES = "phones"; //手机号
    private static final String CONTENT = "content"; //内容
    private static final String SIGN = "sign"; //签名
    private static final String SUB_CODE = "subcode"; //子码
    private static final String SEND_TIME = "sendtime"; //定时发送时间
    private static final String MSG_ID = "msgid";
    private static final String DATA = "data";
    private static final String SIGN_CONTENT = "signContent";
    private static final String NEW_CONTENT = "content";
    protected static final String KEY_RESULT = "result";
    protected static final String KEY_MSG_ID = "msgid";
    private static final String RESULT_SUCCESS_CODE = "0";
    private static final int CHECK_PARAMETER = 0;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 大汉三通MD5加密
     *
     * @param sourceString
     * @return
     */
    private String md5Encode(String sourceString) {
        String resultString = null;
        try {
            resultString = sourceString;
            MessageDigest md = MessageDigest.getInstance("MD5");
            resultString = byte2hexString(md.digest(resultString.getBytes()));
        } catch (Exception var3) {
            logger.error("MD5Encode occur an Exception: ", var3);
        }
        return resultString;
    }

    private String byte2hexString(byte[] bytes) {
        StringBuffer bf = new StringBuffer(bytes.length * 2);

        for (int i = 0; i < bytes.length; ++i) {
            if ((bytes[i] & 255) < 16) {
                bf.append("0");
            }

            bf.append(Long.toString(bytes[i] & 255, 16));
        }

        return bf.toString();
    }

    @Override
    public String getChannelCode() {
        return ExchangeProviderEnum.SANTONG.getProviderName();
    }

    @Override
    public BaseResult post(SendMessage message) {
        String resultCode = "";
        try {
            logger.info("进入三通单条短信发送，参数：{}", message.toString());
            ChannelAccount channelAccount = message.getChannelAccount();
            JSONObject channelInfo = JSONObject.parseObject(channelAccount.getJsonMapping());
            JSONObject param = new JSONObject();
            String santongApiUrl = channelInfo.getString("sendUrl");
            param.put(ACCOUNT, channelInfo.get("account"));
            param.put(PASS_WORD, md5Encode(channelInfo.getString("password")));
            param.put(PHONES, message.getPhone());
            param.put(CONTENT, message.getContent());
            param.put(SIGN, message.getSignName());
            param.put(SEND_TIME, "");
            String requestData = param.toString();
            logger.info("三通 sendSms请求数据:{}", requestData);
            String resp = HttpUtil.doPost(santongApiUrl, requestData, new HashMap<String, String>());
            int retryTime = 0;
            while (StringUtils.isBlank(resp) && retryTime <= 5) {
                resp = HttpUtil.doPost(santongApiUrl, requestData, new HashMap<String, String>());
                retryTime++;
            }
            if (StringUtils.isEmpty(resp)) {
                throw new Exception("三通单条发送未返回");
            }
            logger.info("三通 sendSms响应数据:{}", resp);
            JSONObject jsonRs = JSONObject.parseObject(resp);
            resultCode = jsonRs.getString(KEY_RESULT);
            if (RESULT_SUCCESS_CODE.equals(resultCode)) {
                return BaseResult.success();
            } else {
                int count = CommonRedisUtil.increaseFailCount(redisTemplate, ExchangeProviderEnum.SANTONG.getProviderName(), 1);
                ValueOperations<String, Object> valueOperations = redisTemplate.opsForValue();
                valueOperations.set(CommonRedisUtil.PARTNER_FAIL_KEY + ExchangeProviderEnum.SANTONG.getProviderName(), count, 1, TimeUnit.HOURS);
                //TODO 错误码转换
                return BaseResult.systemError();
            }
        } catch (Exception ex) {
            logger.error("三通单条发送异常了", ex);
            return BaseResult.systemError();
        }
    }

    @Override
    public List<HttpRecord> receiveReport(Map<String, Object> params) {
        List<HttpRecord> reports = new ArrayList<>();
        try {
            JSONObject body = JSONObject.parseObject((String) params.get("report"));
            logger.info("三通回执参数:{}", body);
            JSONArray arr = body.getJSONArray("reports");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            for (Integer i = 0; i < arr.size(); i++) {
                HttpRecord httpRecord = HttpRecord.builder().build();
                httpRecord.setChannelCode(getChannelCode());
                httpRecord.setChannelMsgId(arr.getJSONObject(i).get("msgId").toString());
                httpRecord.setMobile(arr.getJSONObject(i).get("mobile").toString());
                httpRecord.setCode(arr.getJSONObject(i).get("wgcode").toString());
                httpRecord.setDesc(arr.getJSONObject(i).get("desc").toString());

                httpRecord.setStatus(getDeliveryStatus(arr.getJSONObject(i).get("status").toString()));
                httpRecord.setRealSendTime(simpleDateFormat.parse(arr.getJSONObject(i).get("sendTime").toString()));
                httpRecord.setRealSendTime(simpleDateFormat.parse(arr.getJSONObject(i).get("time").toString()));
                httpRecord.setBillCount(1);
                reports.add(httpRecord);
            }
        } catch (Exception e) {
            log.error("解析失败", e);

        }
        return reports;
    }

    @Override
    public Integer getDeliveryStatus(String status) {
        if ("1".equals(status)) {
            return DeliveryStatusEnum.SUCCESS.getCode();
        }
        return DeliveryStatusEnum.FAILURE.getCode();
    }
}
