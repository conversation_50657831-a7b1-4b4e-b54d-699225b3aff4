package com.xhqb.spectre.logserver.factory.impl;

import com.xhqb.spectre.logserver.factory.ResendMessage;
import com.xhqb.spectre.logserver.factory.ResendMessageProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 验证码短信发送mq
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/22 2:38 下午
 */
@Slf4j
@Component
public class ResendVerifyMessage extends ResendMessage {

    @Value("#{'${kael.mq.producers:}'.split(',')[1]}")
    private String verifyTopic;
    @Value("${logServer.retry.verify.diffSecondTime}")
    private Long verifyDiffTime;
    /**
     * 获取mq
     *
     * @return mq
     */
    @Override
    public ResendMessageProperties getResendMessageProperties() {
//        log.info("验证码短信,verifyDiffTime:{},verifyTopic:{}", verifyDiffTime, verifyTopic);
        return new ResendMessageProperties(verifyDiffTime, verifyTopic);
    }
}
