package com.xhqb.spectre.logserver.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.common.dal.dto.mq.SubmitResqDTO;
import com.xhqb.spectre.logserver.service.SubmitResqService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 处理cmpp接收发送状态的q
 *
 * <AUTHOR>
 * @date 2021/10/07
 */
@Component
@Slf4j
public class SubmitResqConsumer {
    @Resource
    private SubmitResqService submitResqService;

    /**
     * 保存order
     *
     * @param message mq
     */
    @MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[2]}",
            subscriptionType = SubscriptionType.Shared,
            clazz = String.class, receiverQueueSize = 100)
    public void execute(String message) {
        try {
            log.info("接收submit resq; message={}", message);
            SubmitResqDTO submitResqDTO = JSONObject.parseObject(message, SubmitResqDTO.class);
            submitResqService.saveOrder(submitResqDTO);
        } catch (Exception e) {
            log.warn("处理submit记录异常", e);
        }
    }
}
