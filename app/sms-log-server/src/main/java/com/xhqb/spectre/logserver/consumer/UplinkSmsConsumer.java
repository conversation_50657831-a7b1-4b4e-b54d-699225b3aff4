package com.xhqb.spectre.logserver.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.common.dal.dto.mq.CmppDeliverUplinkDTO;
import com.xhqb.spectre.logserver.service.UplinkSmsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/14 11:40 上午
 */
@Component
@Slf4j
public class UplinkSmsConsumer {

    @Resource
    private UplinkSmsService uplinkSmsService;

    /**
     * 更新回执
     *
     * @param message mq
     */
    @MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[3]}",
            subscriptionType = SubscriptionType.Shared,
            clazz = String.class)
    public void execute(String message) {
        try {
            log.info("接收cmpp上行短信; message={}", message);
            CmppDeliverUplinkDTO cmppDeliverUplinkDTO = JSONObject.parseObject(message, CmppDeliverUplinkDTO.class);
            uplinkSmsService.saveUplink(cmppDeliverUplinkDTO);
        } catch (Exception e) {
            log.warn("处理上行短信记录异常", e);
        }
    }
}
