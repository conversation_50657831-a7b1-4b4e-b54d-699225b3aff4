package com.xhqb.spectre.logserver.service.impl;

import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.msg.Message;
import com.xhqb.spectre.common.protobuf.DeliverProto;
import com.xhqb.spectre.common.utils.SequenceNumberUtil;
import com.xhqb.spectre.logserver.cache.GatewayCache;
import com.xhqb.spectre.logserver.dto.CmppRedisDTO;
import com.xhqb.spectre.logserver.enums.GatewayEnum;
import com.xhqb.spectre.logserver.service.GatewayCacheService;
import com.xhqb.spectre.logserver.service.RedisService;
import com.xhqb.spectre.logserver.service.SendToCmppService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetSocketAddress;
import java.text.MessageFormat;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/29 11:43 上午
 */
@Service
@Slf4j
public class SendToCmppServiceImpl implements SendToCmppService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private GatewayCacheService gatewayCacheService;

    @Resource
    private RedisService redisService;

    /**
     * 按天统计的数据缓存（T+1更新，因此可以缓存）

     private final Cache<String, Object> requestIdsCache = CacheBuilder.newBuilder()
     .maximumSize(10000)
     .expireAfterWrite(10, TimeUnit.SECONDS)
     .build();
     */
    /**
     * 用于requestId存放redis的过期时间
     */
    @Value("${logServer.sendToCmpp.requestId.redis.secondsTime}")
    private Long requestIdRedisTime;

    /**
     * 发送redis
     *
     * @param cmppRedisDTO 存入redis的数据
     */
    @Override
    public void saveRedis(CmppRedisDTO cmppRedisDTO) {
        log.info("回推参数 cmppRedisDTO:{}", cmppRedisDTO);
        String msgId = cmppRedisDTO.getRequestId();
        //使用Redis分布式锁，减少重复推送（原因是长短信会拆分成几个deliver消息，订单相同）。无需手动释放锁，等自然过期，在有效期内拦截相同订单。
        String lockKey = MessageFormat.format(RedisKeys.LogServerKeys.SEND_UDP_LOCK_KEY, msgId);
        Boolean getLock = redisService.lock(lockKey, "1", requestIdRedisTime, TimeUnit.SECONDS);
        if (!getLock) {
            //获取锁失败，说明已有相同订单正在执行该逻辑，直接返回
            log.info("获取分布式锁失败，无需发送udp，requestId:{}", msgId);
            return;
        }

        byte[] msg = GatewayEnum.MSG.concat(msgId).getBytes();
        byte[] msgStatus = cmppRedisDTO.getMsgStatus().getBytes();
        String username = cmppRedisDTO.getGatewayUserName();
        if (username == null) {
            log.info("回推失败原因：username is null");
            return;
        }
        // 获取缓存信息中的session
        String session = GatewayCache.getInstance().getSessionByUsername(username);
        if (session == null) {
            // 刷新单个session
            gatewayCacheService.getSingleSession(username);
            session = GatewayCache.getInstance().getSessionByUsername(username);
        }
        // 将短信消息与用户session关联消息保存到redis中
        stringRedisTemplate.executePipelined((RedisCallback<?>) connection -> {
            connection.hashCommands().hSet(msg, GatewayEnum.STATE, cmppRedisDTO.getReportStatus().getBytes());
            connection.hashCommands().hSet(msg, GatewayEnum.SUBMIT_TIME, String.valueOf(cmppRedisDTO.getSubmitTime()).getBytes());
            connection.hashCommands().hSet(msg, GatewayEnum.DONE_TIME, String.valueOf(cmppRedisDTO.getDoneTime()).getBytes());
            if (cmppRedisDTO.getDestterminalId() != null) {
                connection.hashCommands().hSet(msg, GatewayEnum.DESTTERMINAL_ID, cmppRedisDTO.getDestterminalId().getBytes());
            }
            if (cmppRedisDTO.getDestId() != null) {
                connection.hashCommands().hSet(msg, GatewayEnum.DEST_ID, cmppRedisDTO.getDestId().getBytes());
            }
            // 将用户最新消息列表状态保存到redis中
            connection.hashCommands().hSet(GatewayEnum.USER_MSG.concat(username).getBytes(), msgId.getBytes(), msgStatus);
            return null;
        });
//        log.info("状态回推cmpp，修改redis成功");
        sendUdp(session, msgId, username);
    }

    /**
     * 发包
     *
     * @param session   ip和端口，ip:port
     * @param requestId cmpp网关生成的消息id
     * @param username  用户名
     */
    @Override
    public void sendUdp(String session, String requestId, String username) {
        if (StringUtils.isEmpty(session)) {
            log.info("udp发送失败原因：session为空");
            return;
        }

//        log.info("发送udp,session:{},requestId:{},username:{}", session, requestId, username);
        String[] split = session.split(":");
        // 设置发送地址和端口
        InetSocketAddress inetSocketAddress = new InetSocketAddress(split[0], Integer.parseInt(split[1]));

        Message req = new Message(Message.DELIVER_REQUEST, SequenceNumberUtil.getSequenceNo());
        // 设置发送数据格式
        DeliverProto.Deliver deliver = DeliverProto.Deliver.newBuilder()
                .setUsername(username)
                .setMsgid(requestId)
                .build();

        req.setBodyBuffer(deliver.toByteArray());
        byte[] udpMessage = req.getBuffer();

        // 发送数据
        DatagramPacket datagramPacket;
        try (DatagramSocket datagramSocket = new DatagramSocket()) {
            datagramPacket = new DatagramPacket(udpMessage, udpMessage.length, inetSocketAddress);
            datagramSocket.send(datagramPacket);
        } catch (IOException e) {
            log.warn("发送UDP包异常", e);
        }
    }
}
