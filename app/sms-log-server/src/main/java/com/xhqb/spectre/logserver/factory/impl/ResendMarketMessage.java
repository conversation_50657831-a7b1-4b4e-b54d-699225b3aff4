package com.xhqb.spectre.logserver.factory.impl;

import com.xhqb.spectre.logserver.factory.ResendMessage;
import com.xhqb.spectre.logserver.factory.ResendMessageProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 营销短信发送mq
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/22 2:38 下午
 */
@Slf4j
@Component
public class ResendMarketMessage extends ResendMessage {

    @Value("#{'${kael.mq.producers:}'.split(',')[2]}")
    private String marketTopic;
    @Value("${logServer.retry.market.diffSecondTime}")
    private Long marketDiffTime;

    /**
     * 获取mq
     *
     * @return mq
     */
    @Override
    public ResendMessageProperties getResendMessageProperties() {
//        log.info("营销短信,marketDiffTime:{},marketTopic:{}", marketDiffTime, marketTopic);
        return new ResendMessageProperties(marketDiffTime, marketTopic);
    }
}
