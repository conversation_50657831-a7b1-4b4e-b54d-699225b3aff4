package com.xhqb.spectre.logserver.service.impl;

import com.xhqb.spectre.common.dal.dto.mq.CmppDeliverUplinkDTO;
import com.xhqb.spectre.common.dal.entity.SmsUplinkDO;
import com.xhqb.spectre.logserver.service.UplinkSaveBatchService;
import com.xhqb.spectre.logserver.service.UplinkSmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/9/27
 */
@Service
@Slf4j
public class UplinkSmsServiceImpl implements UplinkSmsService {

    @Resource
    private UplinkSaveBatchService uplinkSaveBatchService;

    /**
     * 上行短信回执入库
     *
     * @param cmppDeliverUplinkDTO cmpp上行短信回执记录
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveUplink(CmppDeliverUplinkDTO cmppDeliverUplinkDTO) {
        if (Objects.isNull(cmppDeliverUplinkDTO)) {
            log.info("无数据; cmppDeliverUplinkDTO={}", cmppDeliverUplinkDTO);
            return;
        }
        // 实体类转换
        SmsUplinkDO smsUplinkDO = dtoToDo(cmppDeliverUplinkDTO);
        uplinkSaveBatchService.saveUplinkBatch(smsUplinkDO);
//        log.info("cmpp上行短信回执入库; smsUplinkDO={}", smsUplinkDO);
    }

    /**
     * 实体类转换
     *
     * @param cmppDeliverUplinkDTO 队列消息
     * @return 上行短信回执实体类
     */
    @Override
    public SmsUplinkDO dtoToDo(CmppDeliverUplinkDTO cmppDeliverUplinkDTO) {
        SmsUplinkDO smsUplinkDO = new SmsUplinkDO();
        smsUplinkDO.setChannelMsgId(cmppDeliverUplinkDTO.getChannelMsgId());
        smsUplinkDO.setMobile(cmppDeliverUplinkDTO.getMobile());
        smsUplinkDO.setRecvUplinkTime(cmppDeliverUplinkDTO.getRecvUplinkTime());
        smsUplinkDO.setDestTerminalId(cmppDeliverUplinkDTO.getDestTerminalId());
        smsUplinkDO.setMsgLength(cmppDeliverUplinkDTO.getMsgLength());
        smsUplinkDO.setMsgContent(cmppDeliverUplinkDTO.getMsgContent());
        smsUplinkDO.setChannelAccountId(cmppDeliverUplinkDTO.getChannelAccountId() == null ? 0 : cmppDeliverUplinkDTO.getChannelAccountId());
        smsUplinkDO.setChannelCode(cmppDeliverUplinkDTO.getChannelCode() == null ? "" : cmppDeliverUplinkDTO.getChannelCode());
        smsUplinkDO.setSmsTypeCode(cmppDeliverUplinkDTO.getSmsTypeCode() == null ? "" : cmppDeliverUplinkDTO.getSmsTypeCode());
        return smsUplinkDO;
    }
}
