package com.xhqb.spectre.logserver.configuration;

import com.alibaba.druid.pool.DruidDataSource;
import com.xhqb.kael.boot.autoconfigure.druid.DatasourceConfigUtils;
import com.xhqb.kael.boot.autoconfigure.druid.DefaultConnectionProperties;
import com.xhqb.kael.boot.autoconfigure.druid.DruidConnectionProperties;
import com.xhqb.spectre.common.utils.CommonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.List;

/**
 * spectredb datasource config
 *
 * <AUTHOR>
 * @date 2023/09/14
 */
@Configuration
@MapperScan(basePackages = "com.xhqb.spectre.logserver.dal", sqlSessionTemplateRef = "encryptSpectreSqlSessionTemplate")
@EnableConfigurationProperties(EncryptSpectreDruidProperties.class)
public class EncryptSpectreDataSourceConfig {


    @Bean(name = "encryptSpectreDataSource")
    public DataSource encryptSpectreDataSource(EncryptSpectreDruidProperties encryptSpectreDruidProperties) {
        DruidConnectionProperties defaultProperties = DruidConnectionProperties.withDefault(new DefaultConnectionProperties());
        BeanUtils.copyProperties(encryptSpectreDruidProperties, defaultProperties, CommonUtil.getNullPropertyNames(encryptSpectreDruidProperties));
        DruidDataSource dataSource = DatasourceConfigUtils.createDataSource(defaultProperties);
        dataSource.setName("encrypt-spectredb");
        return dataSource;
    }

    @Bean(name = "encryptSpectreSqlSessionFactory")
    public SqlSessionFactory encryptSpectreSqlSessionFactory(@Qualifier("encryptSpectreDataSource") DataSource dataSource, ObjectProvider<List<Interceptor>> interceptorObjectProvider) throws Exception {
        final SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        //设置加解密插件
        List<Interceptor> interceptorList = interceptorObjectProvider.getIfAvailable();
        if (CollectionUtils.isNotEmpty(interceptorList)) {
            bean.setPlugins(interceptorList.toArray(new Interceptor[0]));
        }
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/mysql/spectredb/*Mapper.xml"));
        return bean.getObject();
    }

    @Bean(name = "encryptSpectreTransactionManager")
    public DataSourceTransactionManager encryptSpectreTransactionManager(@Qualifier("encryptSpectreDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "encryptSpectreSqlSessionTemplate")
    public SqlSessionTemplate encryptSpectreSqlSessionTemplate(@Qualifier("encryptSpectreSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
