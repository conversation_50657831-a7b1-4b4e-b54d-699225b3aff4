package com.xhqb.spectre.logserver.service.impl;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.common.constant.CodeMappingConstants;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.dto.mq.SendSubmitDTO;
import com.xhqb.spectre.common.mq.MetisReceiptMessage;
import com.xhqb.spectre.logserver.dto.RetryDTO;
import com.xhqb.spectre.logserver.service.MetisReceiptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.Objects;

/**
 * 回执消息中心服务
 *
 * @author: cl
 * @date: 2023/09/13
 */
@Slf4j
@Service
public class MetisReceiptServiceImpl implements MetisReceiptService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private KafkaTemplate<String, Object> kafkaTemplate;

    /**
     * 消息中心metis-receipt消费
     */
    @Value("${metis.receipt.msg.topic:metis-receipt-msg-req}")
    private volatile String metisReceiptMsgTopic;

    @Value("${call.metis.enable:true}")
    private volatile boolean callMetisEnable;

    @Override
    public void callMetisWithDeliverSuccess(RetryDTO retryDTO) {
        if (!callMetisEnable) {
            return;
        }

        // 捕获异常，避免影响现有流程
        try {
            long startTime = System.currentTimeMillis();
            Long orderId = retryDTO.getOrderId();
            String orderIdStr = String.valueOf(orderId);
            Integer resend = retryDTO.getResend();
            String orderCacheKey = MessageFormat.format(RedisKeys.DispatcherKeys.ORDER_KEY, orderIdStr, resend);
            String orderStr = stringRedisTemplate.opsForValue().get(orderCacheKey);
            if (orderStr == null) {
                log.info("redis获取数据为空。redisKey:{}, orderId:{}", orderCacheKey, orderId);
                return;
            }

            SendSubmitDTO sendSubmitDTO = JSON.parseObject(orderStr, SendSubmitDTO.class);
            if (Objects.equals(1, sendSubmitDTO.getCallMetis())) {
                MetisReceiptMessage metisReceiptMessage = buildMetisReceiptMessage(retryDTO, sendSubmitDTO);
                metisReceiptMessage.setResult("0");
                metisReceiptMessage.setResultDesc("success");
                metisReceiptMessage.setStage(CodeMappingConstants.TYPE_DELIVER);
                doCallMetis(metisReceiptMessage);
                log.info("回执消息中心完成|orderId:{}|cost:{}", retryDTO.getOrderId(), System.currentTimeMillis() - startTime);
            }


        } catch (Exception e) {
           log.warn("回调消息中心异常|orderId:{}", retryDTO.getOrderId(), e);
        }

    }

    @Override
    public void callMetisWithSubmitOrDeliverFail(RetryDTO retryDTO, SendSubmitDTO sendSubmitDTO) {
        if (!callMetisEnable) {
            return;
        }
        try {

            if (Objects.equals(1, sendSubmitDTO.getCallMetis())) {
                long startTime = System.currentTimeMillis();
                MetisReceiptMessage metisReceiptMessage = buildMetisReceiptMessage(retryDTO, sendSubmitDTO);
                metisReceiptMessage.setResult("1");
                if (Objects.nonNull(retryDTO.getChannelErrCodeDTO())) {
                    metisReceiptMessage.setResultDesc(String.valueOf(retryDTO.getChannelErrCodeDTO().getXhErrCode()));
                }
                metisReceiptMessage.setStage(retryDTO.getType());
                doCallMetis(metisReceiptMessage);
                log.info("回执消息中心完成|orderId:{}|cost:{}", retryDTO.getOrderId(), System.currentTimeMillis() - startTime);
            }


        } catch (Exception e) {
            log.warn("回调消息中心异常|orderId:{}", retryDTO.getOrderId(), e);
        }

    }

    void doCallMetis(final MetisReceiptMessage metisReceiptMessage) {
        // 发送kafka消息
        kafkaTemplate.send(metisReceiptMsgTopic, JSON.toJSONString(metisReceiptMessage));
    }

    private MetisReceiptMessage buildMetisReceiptMessage(final RetryDTO retryDTO, final SendSubmitDTO sendSubmitDTO) {
        MetisReceiptMessage metisReceiptMessage = new MetisReceiptMessage();
        metisReceiptMessage.setRequestId(sendSubmitDTO.getRequestId());
        metisReceiptMessage.setMsgType("0");
        metisReceiptMessage.setMobile(sendSubmitDTO.getMobile());
        metisReceiptMessage.setExt(JSON.toJSONString(retryDTO));
        return metisReceiptMessage;
    }


}
