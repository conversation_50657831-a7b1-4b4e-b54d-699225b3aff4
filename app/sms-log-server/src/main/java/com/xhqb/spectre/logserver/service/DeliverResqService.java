package com.xhqb.spectre.logserver.service;

import com.xhqb.spectre.common.dal.dto.mq.CmppDeliverResqDTO;
import com.xhqb.spectre.common.dal.dto.mq.DeliverResqDTO;
import com.xhqb.spectre.common.dal.entity.HttpRecord;
import com.xhqb.spectre.common.dal.entity.SmsReceiptDO;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/19 10:41 上午
 */
public interface DeliverResqService {

    /**
     * 回执入库
     *
     * @param deliverResqDTO deliverResqDTO
     */
    void updateReceipt(DeliverResqDTO deliverResqDTO);

    /**
     * cmpp回执入库
     *
     * @param cmppRecord CmppDeliverResqDTO
     */
    void save(CmppDeliverResqDTO cmppRecord);

    /**
     * CmppDeliverResqDTO to SendReceiptDO
     *
     * @param bean CmppDeliverResqDTO
     * @return SendReceiptDO
     */
    SmsReceiptDO cmppToReceipt(CmppDeliverResqDTO bean);

}
