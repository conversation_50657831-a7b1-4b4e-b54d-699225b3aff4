package com.xhqb.spectre.logserver.dto;

import com.xhqb.spectre.common.dal.dto.mq.CmppDeliverResqDTO;
import com.xhqb.spectre.common.dal.dto.mq.SubmitResqDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/27 3:30 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RetryDTO implements Serializable {
    private static final long serialVersionUID = 1228932138697627813L;
    /**
     * 重发类型
     */
    private String type;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 错误映射码
     */
    private ChannelErrCodeDTO channelErrCodeDTO;
    /**
     * 渠道返回状态码
     */
    private String channelStatus;
    /**
     * 重发次数
     */
    private Integer resend;
    /**
     * 短信类型
     */
    private String smsTypeCode;
    /**
     * 间隔时间
     */
    private Long subTime;
    /**
     * 消息来源 1：http 2：cmpp
     */
    private Integer reqSrc;
    /**
     * cmpp网关用户名
     */
    private String gatewayUserName;
}
