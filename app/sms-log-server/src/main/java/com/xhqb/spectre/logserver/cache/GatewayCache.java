package com.xhqb.spectre.logserver.cache;

import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

/**
 * codeMapping缓存
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/18 6:39 下午
 */
public class GatewayCache {

    private static final GatewayCache INSTANCE = new GatewayCache();

    private static List<String> USERNAME = new ArrayList<>();
    private static Map<String, List<String>> SESSION = new ConcurrentHashMap<>();

    private static Integer USERNAME_UPDATE_TIME = null;
    private static Integer SESSION_UPDATE_TIME = null;

    private GatewayCache() {
    }

    /**
     * Gets instance.
     *
     * @return the instance
     */
    public static GatewayCache getInstance() {
        return INSTANCE;
    }

    /**
     * 获取缓存用户名
     *
     * @return 用户名列表
     */
    public List<String> getUsername() {
        return USERNAME;
    }

    /**
     * 获取所有用户的ip和端口
     *
     * @return session
     */
    public Map<String, List<String>> getAllSessions() {
        return SESSION;
    }

    /**
     * 根据用户名获取所有ip和端口
     *
     * @param username 用户名
     * @return ip和端口
     */
    public List<String> getAllSessionsByUsername(String username) {
        return SESSION.get(username);
    }

    /**
     * 根据用户名获取其中一个ip和端口
     *
     * @param username 用户名
     * @return ip和端口
     */
    public String getSessionByUsername(String username) {
        List<String> sessionList = SESSION.get(username);
        if (CollectionUtils.isEmpty(sessionList)) {
            return null;
        }
        int index = sessionList.size() > 1 ? ThreadLocalRandom.current().nextInt(sessionList.size()) : 0;

        return sessionList.get(index);
    }

    /**
     * 刷新缓存
     *
     * @param username 用户名
     */
    public void refreshUsername(List<String> username) {
        USERNAME = username;
    }

    /**
     * 刷新缓存
     *
     * @param session session
     */
    public void refreshSession(Map<String, List<String>> session) {
        SESSION = session;
    }

    public void refreshSingleSession(String username, List<String> sessions) {
        SESSION.put(username, sessions);
    }

    /**
     * 获取数据变更时间
     *
     * @return 数据变更时间
     */
    public Integer getUsernameUpdateTime() {
        return USERNAME_UPDATE_TIME;
    }

    /**
     * 设置数据变更时间
     *
     * @param dataUpdateTime 数据变更时间
     */
    public void setUsernameUpdateTime(Integer dataUpdateTime) {
        USERNAME_UPDATE_TIME = dataUpdateTime;
    }

    /**
     * 获取数据变更时间
     *
     * @return 数据变更时间
     */
    public Integer getSessionUpdateTime() {
        return SESSION_UPDATE_TIME;
    }

    /**
     * 设置数据变更时间
     *
     * @param dataUpdateTime 数据变更时间
     */
    public void setSessionUpdateTime(Integer dataUpdateTime) {
        SESSION_UPDATE_TIME = dataUpdateTime;
    }
}
