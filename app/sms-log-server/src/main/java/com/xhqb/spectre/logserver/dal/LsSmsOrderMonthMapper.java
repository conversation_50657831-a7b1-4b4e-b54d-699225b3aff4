package com.xhqb.spectre.logserver.dal;

import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.entity.SmsOrderKey;
import com.xhqb.spectre.common.dal.entity.SmsOrderStatDO;
import com.xhqb.spectre.common.dal.query.ReissueOrderQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LsSmsOrderMonthMapper {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(String tableName, SmsOrderKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int insert(String tableName, SmsOrderDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int insertSelective(String tableName, SmsOrderDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    SmsOrderDO selectByPrimaryKey(String tableName, SmsOrderKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(String tableName, SmsOrderDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(String tableName, SmsOrderDO record);

    SmsOrderDO selectByOrderId(String tableName, Long orderId);

    SmsOrderDO selectByRequestId(String tableName, String requestId);

    List<SmsOrderDO> selectByRequestIds(String tableName, List<String> requestIdList);

    List<SmsOrderDO> selectByRequestIdsAndAppCode(String tableName, List<String> requestIdList, String appCode);

    SmsOrderDO selectByAppCodeAndOutOrderId(@Param("tableName") String tableName, @Param("appCode") String appCode, @Param("outOrderId") String outOrderId);

    int insertOrUpdateBatch(@Param("tableName") String tableName, @Param("list") List<SmsOrderDO> smsOrderDOList);

    int insertOrUpdateBatchSubmitCount(@Param("tableName") String tableName, @Param("list") List<SmsOrderDO> smsOrderDOList);

    int insertOrUpdateBatchReportCount(@Param("tableName") String tableName, @Param("list") List<SmsOrderDO> smsOrderDOList);

    /**
     * 群发测试查询
     * <p>
     * 只查询发送成功的一条记录，用于群发任务提交校验
     *
     * @param tableName
     * @param batchId
     * @return
     */
    SmsOrderDO batchTestQuery(@Param("tableName") String tableName, @Param("batchId") Integer batchId);

    List<SmsOrderStatDO> selectStatByChannel(@Param("tableName") String tableName, @Param("beginTime") Integer beginTime, @Param("endTime") Integer endTime);

    List<SmsOrderStatDO> selectStatByIsp(@Param("tableName") String tableName, @Param("beginTime") Integer beginTime, @Param("endTime") Integer endTime);

    List<SmsOrderStatDO> selectMarketStatByChannel(@Param("tableName") String tableName, @Param("beginTime") Integer beginTime, @Param("endTime") Integer endTime);

    List<SmsOrderStatDO> selectMarketStatByIsp(@Param("tableName") String tableName, @Param("beginTime") Integer beginTime, @Param("endTime") Integer endTime);

    List<SmsOrderDO> selectOrderStatus(@Param("tableName") String tableName, @Param("appCode") String appCode,
                                       @Param("requestId") String requestId,
                                       @Param("tplCode") String tplCode,
                                       @Param("mobiles") List<String> mobiles);

    List<SmsOrderDO> selectByReissueOrderQuery(String tableName, ReissueOrderQuery query);

    Integer countByReissueOrderQuery(String tableName, ReissueOrderQuery query);
}