package com.xhqb.spectre.logserver.factory;

import com.xhqb.spectre.common.enums.MessageTypeEnum;
import com.xhqb.spectre.logserver.factory.impl.ResendCollectorMessage;
import com.xhqb.spectre.logserver.factory.impl.ResendMarketMessage;
import com.xhqb.spectre.logserver.factory.impl.ResendNotifyMessage;
import com.xhqb.spectre.logserver.factory.impl.ResendVerifyMessage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/22 2:34 下午
 */
@Component
public class RetryFactory {
    @Resource
    private ResendNotifyMessage resendNotifyMessage;

    @Resource
    private ResendVerifyMessage resendVerifyMessage;

    @Resource
    private ResendMarketMessage resendMarketMessage;

    @Resource
    private ResendCollectorMessage resendCollectorMessage;

    /**
     * 使用 getResendMessage 方法获取重发短信类型的对象
     *
     * @param messageType 类型
     * @return 类型对象
     */
    public ResendMessage getResendMessage(MessageTypeEnum messageType) {
        if (messageType == null) {
            return null;
        }
        switch (messageType) {
            case NOTIFY:
            case DEBT_SWAP:
                return resendNotifyMessage;
            case VERIFY:
                return resendVerifyMessage;
            case MARKET:
                return resendMarketMessage;
            case COLLECTOR:
            case LIGHT_COLLECTOR:
            case SEVERE_COLLECTOR:
                return resendCollectorMessage;
            default:
                break;
        }
        return null;
    }
}
