package com.xhqb.spectre.logserver.service;

import com.xhqb.spectre.logserver.dto.CmppRedisDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/29 11:39 上午
 */
public interface SendToCmppService {
    /**
     * 发送redis
     *
     * @param cmppRedisDTO 存入redis的数据
     */
    void saveRedis(CmppRedisDTO cmppRedisDTO);

    /**
     * 发包
     *
     * @param session ip和端口，ip:port
     * @param requestId cmpp网关生成的消息id
     * @param username  用户名
     */
    void sendUdp(String session, String requestId, String username);
}
