package com.xhqb.spectre.logserver.dal;

import com.xhqb.spectre.common.dal.entity.TplUsageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LsTplUsageMonthMapper {

    int deleteByPrimaryKey(Long id);

    int insert(TplUsageDO record);

    int insertSelective(TplUsageDO record);

    TplUsageDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TplUsageDO record);

    int updateByPrimaryKey(TplUsageDO record);

    int insertByBatch(@Param("tableName") String tableName, @Param("list") List<TplUsageDO> tplUsageDOList);
}