package com.xhqb.spectre.logserver.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/3 2:24 下午
 */
public interface GatewayCacheService {

    /**
     * 获取所有用户名
     *
     * @return list集合
     */
    List<String> getUsername();

    /**
     * 获取所有用户名的IP和端口
     *
     * @return session
     */
    Map<String, List<String>> getSession();

    /**
     * 刷新单个session
     *
     * @param username 用户名
     * @return session
     */
    List<String> getSingleSession(String username);

}
