package com.xhqb.spectre.logserver.service.batch;

import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.TplUsageDO;
import com.xhqb.spectre.logserver.dal.LsTplUsageMonthMapper;
import com.xhqb.spectre.logserver.service.RedisService;
import com.xhqb.spectre.logserver.service.TplUsageSaveBatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class TplUsageSaveBatchServiceImpl implements TplUsageSaveBatchService {

    private static final List<TplUsageDO> tplUsageDOList = new ArrayList<>();

    @Resource
    private RedisService redisService;

    @Resource
    private LsTplUsageMonthMapper lsTplUsageMonthMapper;

    /**
     * 模板订单表批量写入的分布式锁的超时时间，单位：毫秒
     */
    @Value("${sms.tplUsage.batchSave.lock.time:6000}")
    private Long batchSaveLockTime;

    @Value("${spectre.logServer.batchSaveCount:100}")
    private Integer count;

    @Resource
    private TplUsageShardingService tplUsageShardingService;

    private static final String TPL_USAGE_WRITE_LOCK_KEY = RedisKeys.LogServerKeys.TPL_USAGE_WRITE_LOCK_KEY;

    @Override
    public void insertByBatch(TplUsageDO tplUsageDO) {
        synchronized (tplUsageDOList) {
            tplUsageDOList.add(tplUsageDO);
            if (tplUsageDOList.size() >= count) {
                doInsertByBatch();
            }
        }
    }

    private void doInsertByBatch() {
        if (!lock()) {
            return;
        }
        try {
            log.info("模板调用记录批量入库，size={}", tplUsageDOList.size());

            tplUsageShardingService.tplUsageBySharding(tplUsageDOList,
                    "tplUsage调用记录处理", (tableName, orderList) -> lsTplUsageMonthMapper.insertByBatch(tableName, orderList));

            tplUsageDOList.clear();
        } catch (Exception e) {
            log.warn("OrderSaveBatchServiceImpl insertOrUpdate exception, message: {}", e.getMessage());
        } finally {
            //解锁
            unLock();
        }
    }


    /**
     * 加锁
     *
     * @return
     */
    private boolean lock() {
        Boolean getLock = redisService.lock(TPL_USAGE_WRITE_LOCK_KEY, "1", batchSaveLockTime, TimeUnit.MILLISECONDS);
        return Boolean.TRUE.equals(getLock);
    }

    /**
     * 解锁
     *
     * @return
     */
    private void unLock() {
        redisService.unLock(TPL_USAGE_WRITE_LOCK_KEY);
    }

    /**
     * 定时任务
     */
    @Scheduled(fixedDelayString = "${spectre.logserver.orderScheduled.ScheduledTime}")
    public void orderScheduled() {
        synchronized (tplUsageDOList) {
            if (tplUsageDOList.size() > 0) {
                doInsertByBatch();
            }
        }
    }

    /**
     * Bean销毁前将订单入库
     */
    @PreDestroy
    public void preDestroy() {
        orderScheduled();
    }
}
