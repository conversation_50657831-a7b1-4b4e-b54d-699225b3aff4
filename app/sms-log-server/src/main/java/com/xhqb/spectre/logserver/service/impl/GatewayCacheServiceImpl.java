package com.xhqb.spectre.logserver.service.impl;

import com.xhqb.spectre.common.dal.mapper.GatewayUserMapper;
import com.xhqb.spectre.logserver.cache.GatewayCache;
import com.xhqb.spectre.logserver.enums.GatewayEnum;
import com.xhqb.spectre.logserver.service.GatewayCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/3 2:31 下午
 */
@Service
@Slf4j
public class GatewayCacheServiceImpl implements GatewayCacheService {

    @Resource
    private GatewayUserMapper gatewayUserMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取所有用户名
     *
     * @return list集合
     */
    @Override
    public List<String> getUsername() {
        List<String> user = gatewayUserMapper.selectUserName();
        // 更新缓存
        GatewayCache.getInstance().refreshUsername(user);
        log.info("user:{}", user);
        return user;
    }

    /**
     * 获取所有用户名的IP和端口
     *
     * @return session
     */
    @Override
    public Map<String, List<String>> getSession() {
        List<String> usernames = GatewayCache.getInstance().getUsername();
        if (CollectionUtils.isEmpty(usernames)) {
            log.info("缓存用户名为空");
            return null;
        }
        Map<String, List<String>> session = new ConcurrentHashMap<>();
        // 获取redis数据
        for (String username : usernames) {
            // 拿到redis的key
            String key = GatewayEnum.SESSION.concat(username);
            // 获取redis中的数据
            Set<String> members = stringRedisTemplate.opsForSet().members(key);
            // 将redis中的数据转换并存储到map结构中
            if (CollectionUtils.isNotEmpty(members)) {
                session.put(username, new ArrayList<>(members));
            }
        }
        // 更新缓存
        GatewayCache.getInstance().refreshSession(session);

        return session;
    }

    /**
     * 刷新单个session
     *
     * @param username 用户名
     * @return session
     */
    @Override
    public List<String> getSingleSession(String username) {
        Set<String> sessionSet = stringRedisTemplate.opsForSet().members(GatewayEnum.SESSION.concat(username));
        if (CollectionUtils.isEmpty(sessionSet)) {
            return null;
        }
        // 更新缓存
        List<String> sessionList = new ArrayList<>(sessionSet);
        GatewayCache.getInstance().refreshSingleSession(username, sessionList);

        return sessionList;
    }
}
