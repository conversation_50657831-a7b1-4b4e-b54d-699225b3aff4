package com.xhqb.spectre.logserver.service.impl;

import com.xhqb.spectre.common.dal.dto.mq.GatewayDeliverDTO;
import com.xhqb.spectre.common.dal.entity.GatewayDeliverDO;
import com.xhqb.spectre.logserver.dal.LsGatewayDeliverMapper;
import com.xhqb.spectre.logserver.service.GatewayDeliverService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/3 11:24 上午
 */
@Service
@Slf4j
public class GatewayDeliverServiceImpl implements GatewayDeliverService {
    /**
     * 缓存网关推送记录
     */
    private static final List<GatewayDeliverDO> gatewayDeliverDOList = new ArrayList<>();
    @Value("${spectre.logServer.batchSaveCount}")
    private Integer count;
    @Resource
    private LsGatewayDeliverMapper lsGatewayDeliverMapper;

    /**
     * 保存网关推送数据
     *
     * @param gatewayDeliverDTO 网关推送记录
     */
    @Override
    public void saveGateway(GatewayDeliverDTO gatewayDeliverDTO) {
        if (gatewayDeliverDTO == null) {
            log.info("gatewayDeliverDTO获取数据为空");
            return;
        }
        // 转换实体类
        GatewayDeliverDO gatewayDeliverDO = GatewayDeliverDO.builder()
                .msgId(gatewayDeliverDTO.getMsgId() == null ? "" : gatewayDeliverDTO.getMsgId())
                .userName(gatewayDeliverDTO.getUserName() == null ? "" : gatewayDeliverDTO.getUserName())
                .submitTime(gatewayDeliverDTO.getSubmitTime() == null ? "" : gatewayDeliverDTO.getSubmitTime())
                .doneTime(gatewayDeliverDTO.getDoneTime() == null ? "" : gatewayDeliverDTO.getDoneTime())
                .smsStatus(gatewayDeliverDTO.getSmsStatus() == null ? "" : gatewayDeliverDTO.getSmsStatus())
                .destTerminalId(gatewayDeliverDTO.getDestTerminalId() == null ? "" : gatewayDeliverDTO.getDestTerminalId())
                .messageStatus(gatewayDeliverDTO.getMessageStatus() == null ? 0 : gatewayDeliverDTO.getMessageStatus())
                .deliveredTime(gatewayDeliverDTO.getDeliveredTime() == null ? 0 : gatewayDeliverDTO.getDeliveredTime())
                .build();
        // 批量入库
        saveGatewayBatch(gatewayDeliverDO);
    }

    /**
     * 批量保存
     *
     * @param gatewayDeliverDO
     */
    public void saveGatewayBatch(GatewayDeliverDO gatewayDeliverDO) {
        synchronized (gatewayDeliverDOList) {
            gatewayDeliverDOList.add(gatewayDeliverDO);
            if (gatewayDeliverDOList.size() >= count) {
                // 插入数据库
                insert();
            }
        }
    }

    /**
     * 定时任务
     */
    @Scheduled(fixedDelayString = "${spectre.logserver.orderScheduled.ScheduledTime}")
    public void orderScheduled() {
        synchronized (gatewayDeliverDOList) {
            insert();
        }
    }

    /**
     * 正式批量入库
     */
    public void insert() {
        // 批量插入
        if (gatewayDeliverDOList.size() > 0) {
            try {
                log.info("gatewayDeliverDOList size={}", gatewayDeliverDOList.size());
                lsGatewayDeliverMapper.insertOrUpdateBatch(gatewayDeliverDOList);
                gatewayDeliverDOList.clear();
            } catch (Exception e) {
                log.warn("GatewayDeliverServiceImpl insert exception", e);
            }
        }
    }

    /**
     * Bean销毁前将缓存中数据入库
     */
    @PreDestroy
    public void preDestroy() {
        orderScheduled();
    }
}
