package com.xhqb.spectre.logserver.service;

import com.xhqb.spectre.common.dal.entity.SmsOrderDO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/19 10:54 上午
 */
public interface OrderSaveBatchService {

    /**
     * 保存接收到回执次数的order
     *
     * @param smsOrderDO
     */
    void saveOrUpdateOrderReport(SmsOrderDO smsOrderDO);

    /**
     * 保存接收到订单的order
     *
     * @param smsOrderDO
     */
    void saveOrUpdateOrder(SmsOrderDO smsOrderDO);

    /**
     * 保存接收到submit req次数
     *
     * @param smsOrderDO
     */
    void saveOrUpdateOrderResqCount(SmsOrderDO smsOrderDO);
}
