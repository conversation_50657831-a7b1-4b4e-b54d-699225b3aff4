package com.xhqb.spectre.logserver.service;

import com.xhqb.spectre.common.dal.dto.mq.SendSubmitDTO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/19 10:46 上午
 */
public interface SendSubmitService {
    /**
     * 保存发送结果到数据库中
     *
     * @param submitDTO mq发送结果
     */
    void saveOrder(SendSubmitDTO submitDTO);

    /**
     * 转换
     *
     * @param submitDTO 队列消息
     * @return SmsOrderDO
     */
    SmsOrderDO sendSubmitToOrder(SendSubmitDTO submitDTO);
}
