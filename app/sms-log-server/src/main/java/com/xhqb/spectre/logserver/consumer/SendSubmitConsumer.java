package com.xhqb.spectre.logserver.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.lucifer.telemetry.PrometheusCounterMetrics;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.common.dal.dto.mq.SendSubmitDTO;
import com.xhqb.spectre.logserver.service.SendSubmitService;
import io.prometheus.client.Counter;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class SendSubmitConsumer {

    @Resource
    private SendSubmitService sendSubmitService;

    private static final Counter counter = new PrometheusCounterMetrics("spectre_smslogserver_order_total", "description")
            .createWithLabels("sms_type");

    /**
     * 保存order
     *
     * @param message mq
     */
    @MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[0]}", subscriptionType = SubscriptionType.Key_Shared,
            clazz = String.class, count = 4, receiverQueueSize = 100)
    public void execute(String message) {
        try {
            log.info("接收订单消息; message={}", message);
            SendSubmitDTO bean = JSONObject.parseObject(message, SendSubmitDTO.class);
            // 埋点计数
            counter.labels(bean.getSmsTypeCode()).inc();
            sendSubmitService.saveOrder(bean);
        } catch (Exception e) {
            log.warn("处理订单记录异常", e);
        }
    }
}
