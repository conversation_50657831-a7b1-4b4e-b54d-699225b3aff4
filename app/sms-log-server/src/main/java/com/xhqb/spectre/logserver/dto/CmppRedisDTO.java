package com.xhqb.spectre.logserver.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/29 1:41 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CmppRedisDTO implements Serializable {
    private static final long serialVersionUID = -8080449763952704990L;
    /**
     * 请求ID
     */
    private String requestId;
    /**
     * cmpp网关用户名
     */
    private String gatewayUserName;
    /**
     * 是否接收到回执或者发送发送状态
     */
    private String msgStatus;
    /**
     * 小花回执状态映射码
     */
    private String reportStatus;
    /**
     * 提交时间，格式：YYMMDDHHMM
     */
    private String submitTime;
    /**
     * 完成时间，格式：YYMMDDHHMM
     */
    private String doneTime;
    /**
     * 目的号码，一般4-6位
     */
    private String destId;
    /**
     * 短信目标手机
     */
    private String destterminalId;
}
