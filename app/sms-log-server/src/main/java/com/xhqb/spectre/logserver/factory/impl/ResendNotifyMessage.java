package com.xhqb.spectre.logserver.factory.impl;

import com.xhqb.spectre.logserver.factory.ResendMessage;
import com.xhqb.spectre.logserver.factory.ResendMessageProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 通知短信重试mq
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/22 2:38 下午
 */
@Slf4j
@Component
public class ResendNotifyMessage extends ResendMessage {

    @Value("#{'${kael.mq.producers:}'.split(',')[0]}")
    private String notifyTopic;
    @Value("${logServer.retry.notify.diffSecondTime}")
    private Long notifyDiffTime;

    /**
     * 获取mq
     *
     * @return mq
     */
    @Override
    public ResendMessageProperties getResendMessageProperties() {
//        log.info("通知短信,notifyDiffTime:{},notifyTopic:{}", notifyDiffTime, notifyTopic);
        return new ResendMessageProperties(notifyDiffTime, notifyTopic);
    }
}
