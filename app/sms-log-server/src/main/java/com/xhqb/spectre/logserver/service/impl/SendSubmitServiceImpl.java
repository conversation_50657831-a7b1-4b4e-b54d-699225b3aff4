package com.xhqb.spectre.logserver.service.impl;

import com.xhqb.spectre.common.dal.dto.mq.SendSubmitDTO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.utils.DateUtil;
import com.xhqb.spectre.logserver.service.OrderSaveBatchService;
import com.xhqb.spectre.logserver.service.SendSubmitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 21/10/18
 */
@Service
@Slf4j
public class SendSubmitServiceImpl implements SendSubmitService {

    @Resource
    private OrderSaveBatchService orderSaveBatchService;

    /**
     * 保存发送结果到数据库中
     *
     * @param submitDTO mq发送结果
     */
    @Override
    public void saveOrder(SendSubmitDTO submitDTO) {
        if (submitDTO == null) {
            log.info("获取submitDTO数据为空");
            return;
        }
        // 实体类转换
        SmsOrderDO smsOrderDO = sendSubmitToOrder(submitDTO);
        // 插入数据库
        orderSaveBatchService.saveOrUpdateOrder(smsOrderDO);
    }

    /**
     * 转换
     *
     * @param submitDTO 队列消息
     * @return SmsOrderDO
     */
    @Override
    public SmsOrderDO sendSubmitToOrder(SendSubmitDTO submitDTO) {
        SmsOrderDO smsOrderDO = new SmsOrderDO();
        smsOrderDO.setOrderId(submitDTO.getOrderId());
        smsOrderDO.setRequestId(submitDTO.getRequestId() == null ? "" : submitDTO.getRequestId());
        smsOrderDO.setAppCode(submitDTO.getAppCode() == null ? "" : submitDTO.getAppCode());
        smsOrderDO.setTplCode(submitDTO.getTplCode() == null ? "" : submitDTO.getTplCode());
        smsOrderDO.setSmsTypeCode(submitDTO.getSmsTypeCode() == null ? "" : submitDTO.getSmsTypeCode());
        smsOrderDO.setChannelCode(submitDTO.getChannelCode() == null ? "" : submitDTO.getChannelCode());
        smsOrderDO.setChannelAccountId(submitDTO.getChannelAccountId() == null ? 0 : submitDTO.getChannelAccountId());
        smsOrderDO.setSignName(submitDTO.getSignName() == null ? "" : submitDTO.getSignName());
        smsOrderDO.setMobile(submitDTO.getMobile());
        smsOrderDO.setContent(submitDTO.getContent() == null ? "" : submitDTO.getContent());
        smsOrderDO.setSliceId(submitDTO.getSliceId() == null ? 0 : submitDTO.getSliceId());
        smsOrderDO.setProvinceShortName(submitDTO.getProvinceShortName() == null ? "" : submitDTO.getProvinceShortName());
        smsOrderDO.setCityShortName(submitDTO.getCityShortName() == null ? "" : submitDTO.getCityShortName());
        smsOrderDO.setIspCode(submitDTO.getIspCode() == null ? "" : submitDTO.getIspCode());
        smsOrderDO.setBatchId(submitDTO.getBatchId() == null ? 0 : submitDTO.getBatchId());
        smsOrderDO.setBillCount(submitDTO.getBillCount() == null ? 0 : submitDTO.getBillCount());
        smsOrderDO.setSendType(submitDTO.getSendType() == null ? 0 : submitDTO.getSendType());
        smsOrderDO.setResend(submitDTO.getResend() == null ? 0 : submitDTO.getResend());
        smsOrderDO.setSendTime(submitDTO.getSendTime() == null ? 0 : submitDTO.getSendTime());
        smsOrderDO.setParameter(submitDTO.getParameter() == null ? "" : submitDTO.getParameter());
        smsOrderDO.setReqSrc(submitDTO.getReqSrc() == null ? 1 : submitDTO.getReqSrc());
        smsOrderDO.setPhoneStatus(Objects.isNull(submitDTO.getPhoneStatus()) ? -1L : submitDTO.getPhoneStatus());
//        smsOrderDO.setChannelCodeSet(submitDTO.getChannelCodeSet() == null ? "" : JSON.toJSONString(submitDTO.getChannelCodeSet()));
        smsOrderDO.setBizBatchId(Objects.isNull(submitDTO.getBizBatchId()) ? "" : submitDTO.getBizBatchId());
        smsOrderDO.setCallMetis(Objects.isNull(submitDTO.getCallMetis()) ? 0 : submitDTO.getCallMetis());
        // 订单分表后缀设置
        String tableNameSuffix = submitDTO.getTableNameSuffix();
        if (StringUtils.isBlank(tableNameSuffix)) {
            tableNameSuffix = DateUtil.getTableNameSuffix();
        }
        smsOrderDO.setTableNameSuffix(tableNameSuffix);
        return smsOrderDO;
    }
}
