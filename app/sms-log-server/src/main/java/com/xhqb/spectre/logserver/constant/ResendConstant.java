package com.xhqb.spectre.logserver.constant;

/*
 * @Author: h<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/2/18 15:15
 * @Description:
 */
public final class ResendConstant {

    /**
     * 重发次数，用4个bit标识，第1、3位表示deliver，第2、4位表示submit。例如1011，表示deliver重发2次，submit重发1次
     * <p>
     * 0000（submit重发0次，deliver重发0次）
     */
    public static final int RESEND_0 = 0;

    /**
     * 0001（submit重发1次，deliver重发0次）
     */
    public static final int RESEND_1 = 1;

    /**
     * 0010（submit重发0次，deliver重发1次）
     */
    public static final int RESEND_2 = 2;

    /**
     * 0011（submit重发1次，deliver重发1次）
     */
    public static final int RESEND_3 = 3;

    /**
     * 1010（submit重发0次，deliver重发2次）
     */
    public static final int RESEND_10 = 10;

    /**
     * 1011（submit重发1次，deliver重发2次）
     */
    public static final int RESEND_11 = 11;

    /**
     * 重发标识
     */
    public static final int RETRY_SYMBOL = 1;
}
