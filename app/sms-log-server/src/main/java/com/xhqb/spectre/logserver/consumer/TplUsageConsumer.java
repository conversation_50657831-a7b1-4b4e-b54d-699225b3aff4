package com.xhqb.spectre.logserver.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.lucifer.telemetry.PrometheusCounterMetrics;
import com.xhqb.kael.mq.MQMessage;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.common.dal.dto.mq.SendSubmitDTO;
import com.xhqb.spectre.logserver.service.TplUsageService;
import io.prometheus.client.Counter;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class TplUsageConsumer {

    @Resource
    private TplUsageService tplUsageService;

    private static final Counter counter = new PrometheusCounterMetrics("spectre_smslogserver_tpl_usage_total", "description")
            .createWithLabels("tpl_code");

    /**
     * 保存模板调用记录
     *
     * @param message mq
     */
    @MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[0]}",
            subscriptionType = SubscriptionType.Key_Shared,
            clazz = String.class,
            maxRedeliverCount = 2,
            ackTimeout = 60L,
            receiverQueueSize = 100)
    public void execute(MQMessage<String> message) {
        try {
            log.info("模板调用消息; msgId={}, reCount={}, msg={}", message.getMessageId(),
                    message.getRedeliveryCount(),
                    message.getValue());
            if (message.getRedeliveryCount() == 0) {
                SendSubmitDTO bean = JSONObject.parseObject(message.getValue(), SendSubmitDTO.class);
                // 埋点计数
                counter.labels(bean.getTplCode()).inc();
                tplUsageService.save(bean);
            }
        } catch (Exception e) {
            log.warn("处理模板调用记录异常", e);
        }
    }
}
