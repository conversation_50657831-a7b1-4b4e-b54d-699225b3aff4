package com.xhqb.spectre.logserver.service.impl;

import com.xhqb.spectre.common.dal.entity.CodeMappingDO;
import com.xhqb.spectre.common.dal.mapper.CodeMappingMapper;
import com.xhqb.spectre.logserver.cache.CodeMappingCache;
import com.xhqb.spectre.logserver.dto.ChannelErrCodeDTO;
import com.xhqb.spectre.logserver.service.CodeMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/18 2:32 下午
 */
@Service
@Slf4j
public class CodeMappingServiceImpl implements CodeMappingService {

    @Resource
    CodeMappingMapper codeMappingMapper;

    /**
     * 获取渠道错误码和小花错误码映射map
     *
     * @return map集合
     */
    @Override
    public Map<String, Map<String, Map<String, ChannelErrCodeDTO>>> getCodeMapping() {
        Map<String, Map<String, Map<String, ChannelErrCodeDTO>>> codeMap = new ConcurrentHashMap();
        List<CodeMappingDO> codeMappingDOList = codeMappingMapper.selectAll();
        if (codeMappingDOList == null) {
            return null;
        }
        Map<String, List<CodeMappingDO>> channelCodeMap = codeMappingDOList.stream().collect(Collectors.groupingBy(CodeMappingDO::getChannelCode));
        for (String channelCode : channelCodeMap.keySet()) {
            List<CodeMappingDO> channelCodeList = channelCodeMap.get(channelCode);
            Map<String, List<CodeMappingDO>> typeMap = channelCodeList.stream().collect(Collectors.groupingBy(CodeMappingDO::getType));
            Map<String, Map<String, ChannelErrCodeDTO>> typeMap2 = new HashMap<>();
            for (String type : typeMap.keySet()) {
                List<CodeMappingDO> typeList = typeMap.get(type);
                Map<String, ChannelErrCodeDTO> channelErrCodeMap = new ConcurrentHashMap<>();
                for (CodeMappingDO codeMappingDO : typeList) {
                    channelErrCodeMap.put(codeMappingDO.getChannelErrCode(), new ChannelErrCodeDTO(codeMappingDO.getXhErrCode(), codeMappingDO.getErrorCodeDO().getCodeDesc(), codeMappingDO.getErrorCodeDO().getRetry()));
                }
                typeMap2.put(type, channelErrCodeMap);
            }
            codeMap.put(channelCode, typeMap2);
        }
        // 更新至缓存
        CodeMappingCache.getInstance().refresh(codeMap);
        log.info("codeMap:{}", codeMap);
        return codeMap;
    }
}
