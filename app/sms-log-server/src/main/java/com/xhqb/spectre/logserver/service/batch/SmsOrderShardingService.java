package com.xhqb.spectre.logserver.service.batch;

import com.xhqb.spectre.common.constant.ShardingTableName;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 订单分表服务
 *
 * <AUTHOR>
 * @date 2024/7/24
 */
@Service
@Slf4j
public class SmsOrderShardingService {

    /**
     * 订单分表功能
     *
     * @param smsOrderList     待处理的订单数据
     * @param opName           当前操作的名称
     * @param shardingCallback 分表回调
     */
    public void smsOrderBySharding(List<SmsOrderDO> smsOrderList, String opName, ShardingCallback shardingCallback) {
        try {
            Map<Optional<String>, List<SmsOrderDO>> shardingGroup = smsOrderList.stream().collect(Collectors.groupingBy(x -> Optional.ofNullable(x.getTableNameSuffix())));
            Set<Map.Entry<Optional<String>, List<SmsOrderDO>>> entries = shardingGroup.entrySet();
            for (Map.Entry<Optional<String>, List<SmsOrderDO>> entry : entries) {
                doSmsOrderBySharding(entry, opName, shardingCallback);
            }
        } catch (Exception e) {
            log.error("分表{}处理异常", opName, e);
        }
    }

    /**
     * 订单分表功能
     *
     * @param entry
     */
    private void doSmsOrderBySharding(Map.Entry<Optional<String>, List<SmsOrderDO>> entry, String opName, ShardingCallback shardingCallback) {
        String tableName = null;
        try {
            String suffix = entry.getKey().orElseGet(DateUtil::getTableNameSuffix);
            tableName = ShardingTableName.getSmsOrderName(suffix);
            List<SmsOrderDO> orderList = entry.getValue();
            shardingCallback.sharding(tableName, orderList);
        } catch (Exception e) {
            log.error("订单分表do{}处理失败,tableName ={}", opName, tableName, e);
        }
    }


    interface ShardingCallback {
        /**
         * 分表处理
         *
         * @param tableName
         * @param orderList
         */
        void sharding(String tableName, List<SmsOrderDO> orderList);
    }
}
