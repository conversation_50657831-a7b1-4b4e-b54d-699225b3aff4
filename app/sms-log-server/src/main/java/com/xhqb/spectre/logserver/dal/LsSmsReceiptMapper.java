package com.xhqb.spectre.logserver.dal;

import com.xhqb.spectre.common.dal.entity.SmsReceiptDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface LsSmsReceiptMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_send_receipt
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_send_receipt
     *
     * @mbggenerated
     */
    int insert(SmsReceiptDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_send_receipt
     *
     * @mbggenerated
     */
    int insertSelective(SmsReceiptDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_send_receipt
     *
     * @mbggenerated
     */
    SmsReceiptDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_send_receipt
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SmsReceiptDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_send_receipt
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SmsReceiptDO record);

    int insertBatch(List<SmsReceiptDO> smsReceiptDOList);
}