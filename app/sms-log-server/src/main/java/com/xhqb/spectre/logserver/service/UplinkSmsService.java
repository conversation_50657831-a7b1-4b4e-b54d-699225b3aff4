package com.xhqb.spectre.logserver.service;

import com.xhqb.spectre.common.dal.dto.mq.CmppDeliverUplinkDTO;
import com.xhqb.spectre.common.dal.entity.SmsUplinkDO;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/19 10:51 上午
 */
public interface UplinkSmsService {
    /**
     * 上行短信回执入库
     *
     * @param cmppDeliverUplinkDTO cmpp上行短信回执记录
     */
    @Transactional(rollbackFor = Exception.class)
    void saveUplink(CmppDeliverUplinkDTO cmppDeliverUplinkDTO);

    /**
     * 实体类转换
     *
     * @param cmppDeliverUplinkDTO 队列消息
     * @return 上行短信回执实体类
     */
    SmsUplinkDO dtoToDo(CmppDeliverUplinkDTO cmppDeliverUplinkDTO);
}
