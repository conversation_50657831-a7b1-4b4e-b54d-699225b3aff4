package com.xhqb.spectre.logserver.factory.impl;

import com.xhqb.spectre.logserver.factory.ResendMessage;
import com.xhqb.spectre.logserver.factory.ResendMessageProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 催收短信发送mq
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/22 2:38 下午
 */
@Slf4j
@Component
public class ResendCollectorMessage extends ResendMessage {

    @Value("#{'${kael.mq.producers:}'.split(',')[3]}")
    private String collectorTopic;
    @Value("${logServer.retry.collector.diffSecondTime}")
    private Long collectorDiffTime;

    /**
     * 获取mq
     *
     * @return mq
     */
    @Override
    public ResendMessageProperties getResendMessageProperties() {
//        log.info("催收短信,collectorDiffTime:{},collectorTopic:{}", collectorDiffTime, collectorTopic);
        return new ResendMessageProperties(collectorDiffTime, collectorTopic);
    }
}
