package com.xhqb.spectre.logserver.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.common.dal.dto.mq.DeliverResqDTO;
import com.xhqb.spectre.logserver.service.DeliverResqService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/9/27
 */
@Component
@Slf4j
public class DeliverResqConsumer {

    @Resource
    private DeliverResqService deliverResqService;

    /**
     * 更新回执
     *
     * @param message mq
     */
    @MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[1]}",
            subscriptionType = SubscriptionType.Shared,
            clazz = String.class, receiverQueueSize = 100)
    public void execute(String message) {
        try {
            log.info("接收回执; message={}", message);
            DeliverResqDTO deliverResqDTO = JSONObject.parseObject(message, DeliverResqDTO.class);
            deliverResqService.updateReceipt(deliverResqDTO);
        } catch (Exception e) {
            log.warn("处理deliver记录异常", e);
        }
    }
}
