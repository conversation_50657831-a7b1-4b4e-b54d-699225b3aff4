package com.xhqb.spectre.logserver.service.batch;

import com.xhqb.spectre.common.dal.entity.SubmitResqDO;
import com.xhqb.spectre.common.dal.mapper.SubmitResqMapper;
import com.xhqb.spectre.logserver.service.SubmitResqSaveBatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量保存
 *
 * <AUTHOR>
 * @date 09/29
 */
@Service
@Slf4j
public class SubmitResqSaveBatchServiceImpl implements SubmitResqSaveBatchService {

    @Resource
    private SubmitResqMapper submitResqMapper;

    private static final List<SubmitResqDO> submitResqDOList = new ArrayList<>();

    @Value("${spectre.logServer.batchSaveCount}")
    private Integer count;

    /**
     * 批量保存
     *
     * @param submitResqDO
     */
    @Override
    public void saveSubmitResqBatch(SubmitResqDO submitResqDO) {
        synchronized (submitResqDOList) {
            submitResqDOList.add(submitResqDO);
            if (submitResqDOList.size() >= count) {
                insertSubmitResq();
            }
        }
    }


    /**
     * 批量插入
     */
    @Override
    public void insertSubmitResq() {
        // 批量插入
        if (submitResqDOList.size() > 0) {
            try {
                log.info("提交状态批量入库，size={}", submitResqDOList.size());
                submitResqMapper.insertBatch(submitResqDOList);
                submitResqDOList.clear();
            } catch (Exception e) {
                log.warn("SubmitResqSaveBatchServiceImpl insertSubmitResq exception", e);
            }
        }
    }


    /**
     * 定时任务
     */
    @Scheduled(fixedDelayString = "${spectre.logserver.orderScheduled.ScheduledTime}")
    public void orderScheduled() {
        synchronized (submitResqDOList) {
            insertSubmitResq();
        }
    }

    /**
     * Bean销毁前将订单入库
     */
    @PreDestroy
    public void preDestroy() {
        orderScheduled();
    }
}
