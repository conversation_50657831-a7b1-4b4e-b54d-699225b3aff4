package com.xhqb.spectre.logserver.cache;

import com.xhqb.spectre.common.constant.CodeMappingConstants;
import com.xhqb.spectre.logserver.dto.ChannelErrCodeDTO;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * codeMapping缓存
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/18 6:39 下午
 */
public class CodeMappingCache {

    private static final CodeMappingCache INSTANCE = new CodeMappingCache();

    private static Map<String, Map<String, Map<String, ChannelErrCodeDTO>>> DATA_MAP = new ConcurrentHashMap<>();

    private static Integer DATA_UPDATE_TIME = null;

    /**
     * 兜底策略，存入
     *
     * "sumbit":{
     * "0":[0,"发送成功"],
     * },
     * "deliver":{
     * "DELIVRD":[0,"反馈成功"],
     * }
     */
    static {
        Map<String, ChannelErrCodeDTO> channelErrCodeMapSubmit = new ConcurrentHashMap<>();
        Map<String, ChannelErrCodeDTO> channelErrCodeMapDeliver = new ConcurrentHashMap<>();
        Map<String, Map<String, ChannelErrCodeDTO>> typeMap = new ConcurrentHashMap<>();
        channelErrCodeMapSubmit.put(
                CodeMappingConstants.SUBMIT_CHANNEL_CODE,
                new ChannelErrCodeDTO(
                        CodeMappingConstants.XH_SUCCESS_CODE_DEFAULT,
                        CodeMappingConstants.SUBMIT_XH_DESC_SUCCESS,
                        CodeMappingConstants.ERR_CODE_RETRY));
        typeMap.put(CodeMappingConstants.TYPE_SUBMIT, channelErrCodeMapSubmit);

        channelErrCodeMapDeliver.put(
                CodeMappingConstants.DELIVER_CHANNEL_CODE_SUCCESS,
                new ChannelErrCodeDTO(
                        CodeMappingConstants.XH_SUCCESS_CODE_DEFAULT,
                        CodeMappingConstants.DELIVER_XH_DESC_SUCCESS,
                        CodeMappingConstants.ERR_CODE_RETRY));
        typeMap.put(CodeMappingConstants.TYPE_DELIVER, channelErrCodeMapDeliver);

        DATA_MAP.put(CodeMappingConstants.CHANNEL_CODE_DEFAULT, typeMap);
    }

    private CodeMappingCache() {
    }

    /**
     * Gets instance.
     *
     * @return the instance
     */
    public static CodeMappingCache getInstance() {
        return INSTANCE;
    }

    /**
     * 获取所有缓存数据
     *
     * @return dataMap
     */
    public Map<String, Map<String, Map<String, ChannelErrCodeDTO>>> getDataMap() {
        return DATA_MAP;
    }

    /**
     * 获取channelCode缓存数据
     *
     * @param channelCode channelCode
     * @return channelCodeMap
     */
    public Map<String, Map<String, ChannelErrCodeDTO>> getChannelCodeMap(String channelCode) {
        return DATA_MAP.get(channelCode);
    }

    /**
     * 获取映射关系
     *
     * @param channelCode    渠道码
     * @param type           类型
     * @param channelErrCode 状态码
     * @return ChannelErrCodeDTO
     */
    public ChannelErrCodeDTO getErrCode(String channelCode, String type, String channelErrCode) {
        Map<String, Map<String, ChannelErrCodeDTO>> channelCodeMap = DATA_MAP.get(channelCode);
        if (channelCodeMap == null) {
            return null;
        }
        Map<String, ChannelErrCodeDTO> typeMap = channelCodeMap.get(type);
        if (typeMap == null) {
            return null;
        }
        ChannelErrCodeDTO channelErrCodeDTO = typeMap.get(channelErrCode);
        return channelErrCodeDTO;
    }

    /**
     * 刷新缓存
     *
     * @param codeMap
     */
    public void refresh(Map<String, Map<String, Map<String, ChannelErrCodeDTO>>> codeMap) {
        DATA_MAP = codeMap;
    }

    /**
     * 获取数据变更时间
     *
     * @return
     */
    public Integer getDataUpdateTime() {
        return DATA_UPDATE_TIME;
    }

    /**
     * 设置数据变更时间
     *
     * @param dataUpdateTime
     */
    public void setDataUpdateTime(Integer dataUpdateTime) {
        DATA_UPDATE_TIME = dataUpdateTime;
    }
}
