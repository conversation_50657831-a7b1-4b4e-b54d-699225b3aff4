package com.xhqb.spectre.logserver.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/18 4:02 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChannelErrCodeDTO implements Serializable {
    private static final long serialVersionUID = 1531716525242612786L;

    /**
     * 小花错误码编码
     */
    private Integer xhErrCode;

    /**
     * 错误码描述
     */
    private String codeDesc;

    /**
     * 是否重发 0否 1是
     */
    private Integer retry;
}
