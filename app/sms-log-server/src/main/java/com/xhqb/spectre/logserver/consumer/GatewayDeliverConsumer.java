package com.xhqb.spectre.logserver.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.common.dal.dto.mq.GatewayDeliverDTO;
import com.xhqb.spectre.logserver.service.GatewayDeliverService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/3 11:21 上午
 */
@Component
@Slf4j
public class GatewayDeliverConsumer {
    @Resource
    private GatewayDeliverService gatewayDeliverService;

    /**
     * 更新回执
     *
     * @param message mq
     */
    @MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[5]}",
            subscriptionType = SubscriptionType.Key_Shared,
            clazz = String.class, receiverQueueSize = 100)
    public void execute(String message) {
        try {
            log.info("接收网关推送记录; message={}", message);
            GatewayDeliverDTO gatewayDeliverDTO = JSONObject.parseObject(message, GatewayDeliverDTO.class);
            gatewayDeliverService.saveGateway(gatewayDeliverDTO);
        } catch (Exception e) {
            log.warn("处理网关推送记录异常", e);
        }
    }
}
