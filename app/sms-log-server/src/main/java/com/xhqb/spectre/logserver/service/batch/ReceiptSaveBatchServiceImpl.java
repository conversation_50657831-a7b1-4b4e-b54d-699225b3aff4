package com.xhqb.spectre.logserver.service.batch;

import com.xhqb.spectre.common.dal.entity.SmsReceiptDO;
import com.xhqb.spectre.logserver.dal.LsSmsReceiptMapper;
import com.xhqb.spectre.logserver.service.ReceiptSaveBatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量保存回执
 *
 * <AUTHOR>
 * @date 2021/10/07
 */
@Service
@Slf4j
public class ReceiptSaveBatchServiceImpl implements ReceiptSaveBatchService {

    @Resource
    private LsSmsReceiptMapper lsSmsReceiptMapper;

    private static final List<SmsReceiptDO> smsReceiptDOList = new ArrayList<>();

    @Value("${spectre.logServer.batchSaveCount}")
    private Integer count;

    /**
     * 批量保存
     *
     * @param smsReceiptDO
     */
    @Override
    public void saveReceiptBatch(SmsReceiptDO smsReceiptDO) {
        synchronized (smsReceiptDOList) {
            smsReceiptDOList.add(smsReceiptDO);
            if (smsReceiptDOList.size() >= count) {
                insertReceipt();
            }
        }
    }

    /**
     * 批量插入
     */
    @Override
    public void insertReceipt() {
        // 批量插入
        if (smsReceiptDOList.size() > 0) {
            try {
                log.info("回执状态批量入库，size={}", smsReceiptDOList.size());
                lsSmsReceiptMapper.insertBatch(smsReceiptDOList);
                smsReceiptDOList.clear();
            } catch (Exception e) {
                log.warn("ReceiptSaveBatchServiceImpl insertReceipt exception", e);
            }
        }
    }

    /**
     * 定时任务
     */
    @Scheduled(fixedDelayString = "${spectre.logserver.receiptScheduled.ScheduledTime}")
    public void orderScheduled() {
        synchronized (smsReceiptDOList) {
            insertReceipt();
        }
    }

    /**
     * Bean销毁前将订单入库
     */
    @PreDestroy
    public void preDestroy() {
        orderScheduled();
    }
}
