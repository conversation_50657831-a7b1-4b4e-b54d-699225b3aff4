//package com.xhqb.spectre.logserver.producer;
//
//import com.alibaba.fastjson.JSON;
//import com.xhqb.kael.mq.producer.MQTemplate;
//import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.pulsar.client.api.TypedMessageBuilder;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.concurrent.TimeUnit;
//
///**
// * 更新Order状态消息生产者
// *
// * <AUTHOR>
// * @date 2021/10/6
// */
//@Component
//@Slf4j
//public class ReceiptUpdateOrderProducer {
//    /**
//     * 延迟消息时间 默认5分钟
//     */
//    @Value("${spectre.logServer.receiptUpdateToOrder.delayTimeMinute:5}")
//    private Integer delayTimeMinute;
//    /**
//     * topic -> spectre_receipt_update_to_order
//     */
//    @Value("#{'${kael.mq.producers:}'.split(',')[4]}")
//    private String receiptUpdateToOrderTopic;
//
//    @Resource
//    private MQTemplate<String> mqTemplate;
//
//
//    /**
//     * 发送更新Order reportStatus状态消息
//     *
//     * @param smsOrderDO
//     */
//    public void delaySend(SmsOrderDO smsOrderDO) {
//        String body = JSON.toJSONString(smsOrderDO);
//        TypedMessageBuilder<String> message = mqTemplate.createMessage(receiptUpdateToOrderTopic, body);
//        message.deliverAfter(delayTimeMinute, TimeUnit.MINUTES);
//        try {
//            message.send();
//            log.info("延迟发送更新Order reportStatus状态消息成功, message = {}, delayTimeMinute = {}", body, delayTimeMinute);
//        } catch (Exception e) {
//            log.error("延迟发送更新Order reportStatus状态消息失败, message = {}", body, e);
//            throw new RuntimeException(e);
//        }
//
//    }
//}
