package com.xhqb.spectre.logserver.service.batch;

import com.xhqb.spectre.common.constant.ShardingTableName;
import com.xhqb.spectre.common.dal.entity.TplUsageDO;
import com.xhqb.spectre.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * 模板调用记录分表服务
 *
 */
@Service
@Slf4j
public class TplUsageShardingService {

    /**
     * 模板调用记录分表功能
     *
     * @param tplUsageDOList     待处理的订单数据
     * @param opName           当前操作的名称
     * @param callback 分表回调
     */
    public void tplUsageBySharding(List<TplUsageDO> tplUsageDOList,
                                   String opName,
                                   BiConsumer<String, List<TplUsageDO>> callback) {
        try {
            Map<Optional<String>, List<TplUsageDO>> shardingGroup = tplUsageDOList.stream()
                    .collect(Collectors.groupingBy(x -> Optional.ofNullable(x.getTableNameSuffix())));
            Set<Map.Entry<Optional<String>, List<TplUsageDO>>> entries = shardingGroup.entrySet();
            for (Map.Entry<Optional<String>, List<TplUsageDO>> entry : entries) {
                doTplUsageByShardTpling(entry, opName, callback);
            }
        } catch (Exception e) {
            log.error("分表{}处理异常", opName, e);
        }
    }

    /**
     * 模板调用记录
     *
     * @param entry
     */
    private void doTplUsageByShardTpling(Map.Entry<Optional<String>, List<TplUsageDO>> entry,
                                         String opName,
                                         BiConsumer<String, List<TplUsageDO>> callback) {
        String tableName = null;
        try {
            String suffix = entry.getKey().orElseGet(DateUtil::getTableNameSuffix);
            tableName = ShardingTableName.getTplUsageName(suffix);
            List<TplUsageDO> orderList = entry.getValue();
            callback.accept(tableName, orderList);
        } catch (Exception e) {
            log.error("订单分表do{}处理失败,tableName ={}", opName, tableName, e);
        }
    }
}
