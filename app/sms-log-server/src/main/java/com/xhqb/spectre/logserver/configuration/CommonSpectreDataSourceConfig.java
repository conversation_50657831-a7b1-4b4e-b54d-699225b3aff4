package com.xhqb.spectre.logserver.configuration;

import com.alibaba.druid.pool.DruidDataSource;
import com.xhqb.kael.boot.autoconfigure.druid.DatasourceConfigUtils;
import com.xhqb.kael.boot.autoconfigure.druid.DefaultConnectionProperties;
import com.xhqb.kael.boot.autoconfigure.druid.DruidConnectionProperties;
import com.xhqb.spectre.common.utils.CommonUtil;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * spectredb datasource config
 *
 * <AUTHOR>
 * @date 2023/09/14
 */
@Configuration
@MapperScan(basePackages = "com.xhqb.spectre.common.dal.mapper", sqlSessionTemplateRef = "commonSpectreSqlSessionTemplate")
@EnableConfigurationProperties(CommonSpectreDruidProperties.class)
public class CommonSpectreDataSourceConfig {


    @Bean(name = "commonSpectreDataSource")
    public DataSource commonSpectreDataSource(CommonSpectreDruidProperties commonSpectreDruidProperties) {
        DruidConnectionProperties defaultProperties = DruidConnectionProperties.withDefault(new DefaultConnectionProperties());
        BeanUtils.copyProperties(commonSpectreDruidProperties, defaultProperties, CommonUtil.getNullPropertyNames(commonSpectreDruidProperties));
        DruidDataSource dataSource = DatasourceConfigUtils.createDataSource(defaultProperties);
        dataSource.setName("common-spectredb");
        return dataSource;
    }

    @Primary
    @Bean(name = "commonSpectreSqlSessionFactory")
    public SqlSessionFactory commonSpectreSqlSessionFactory(@Qualifier("commonSpectreDataSource") DataSource dataSource) throws Exception {
        final SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*Mapper.xml"));
        return bean.getObject();
    }

    @Primary
    @Bean(name = "commonSpectreTransactionManager")
    public DataSourceTransactionManager commonSpectreTransactionManager(@Qualifier("commonSpectreDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Primary
    @Bean(name = "commonSpectreSqlSessionTemplate")
    public SqlSessionTemplate spectreSqlSessionTemplate(@Qualifier("commonSpectreSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
