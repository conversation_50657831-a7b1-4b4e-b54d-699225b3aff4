package com.xhqb.spectre.logserver.service.impl;

import com.xhqb.kael.lucifer.telemetry.PrometheusCounterMetrics;
import com.xhqb.spectre.common.constant.CodeMappingConstants;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.dto.mq.SubmitResqDTO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.entity.SubmitResqDO;
import com.xhqb.spectre.common.enums.ProtocolTypeEnum;
import com.xhqb.spectre.common.utils.DateUtil;
import com.xhqb.spectre.logserver.cache.CodeMappingCache;
import com.xhqb.spectre.logserver.dto.ChannelErrCodeDTO;
import com.xhqb.spectre.logserver.dto.CmppRedisDTO;
import com.xhqb.spectre.logserver.dto.RetryDTO;
import com.xhqb.spectre.logserver.enums.GatewayEnum;
import com.xhqb.spectre.logserver.service.*;
import io.prometheus.client.Counter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SubmitResqServiceImpl implements SubmitResqService {

    @Resource
    private OrderSaveBatchService orderSaveBatchService;

    @Resource
    private SubmitResqSaveBatchService submitResqSaveBatchService;

    @Resource
    private RetryService retryService;

    @Resource
    private SendToCmppService sendToCmppService;

    @Resource
    private RedisService redisService;

    /**
     * submit相同结果的分布式锁超时时间
     */
    @Value("${sms.submit.lock.second:30}")
    private Long submitLockTime;

    /**
     * 短信重发的线程池
     */
    @Resource(name = "retryThreadPool")
    private ThreadPoolExecutor retryThreadPool;

    private ChannelErrCodeDTO channelErrCodeDTO = new ChannelErrCodeDTO(
            CodeMappingConstants.XH_CODE_DEFAULT,
            CodeMappingConstants.XH_DESC_DEFAULT,
            CodeMappingConstants.ERR_CODE_RETRY);

    private static final Integer SUCCESS_CODE = 0;

    private static final Counter COUNTER = new PrometheusCounterMetrics("spectre_smslogserver_submit_total", "description")
            .createWithLabels("status", "sms_type", "channel_code");

    /**
     * get方法
     *
     * @return ChannelErrCodeDTO
     */
    public ChannelErrCodeDTO getChannelErrCodeDTO() {
        return channelErrCodeDTO;
    }

    /**
     * set方法
     *
     * @param channelErrCodeDTO 渠道错误码映射实体类
     */
    public void setChannelErrCodeDTO(ChannelErrCodeDTO channelErrCodeDTO) {
        this.channelErrCodeDTO = channelErrCodeDTO;
    }

    /**
     * 保存发送结果到数据库中
     *
     * @param submitResqDTO mq发送结果
     */
    @Override
    public void saveOrder(SubmitResqDTO submitResqDTO) {
        if (submitResqDTO == null) {
            log.info("submitResqDTO获取数据为空");
            return;
        }
        // 埋点计数
        if (SUCCESS_CODE.equals(submitResqDTO.getResult())) {
            COUNTER.labels("success", submitResqDTO.getSmsTypeCode(), submitResqDTO.getChannelCode()).inc();
        } else {
            COUNTER.labels("fail", submitResqDTO.getSmsTypeCode(), submitResqDTO.getChannelCode()).inc();
        }
        //保存submit结果记录
        SubmitResqDO submitResqDO = transferSubmit(submitResqDTO);
        submitResqSaveBatchService.saveSubmitResqBatch(submitResqDO);

        //判断submit结果是否重复，重复则忽略（长短信会拆分成几个相同的submit结果，相同的结果无需多次更新订单表）
        String lockKey = MessageFormat.format(RedisKeys.LogServerKeys.SUBMIT_RESULT_LOCK_KEY, String.valueOf(submitResqDTO.getOrderId()), submitResqDTO.getResend(), submitResqDTO.getResult());
        Boolean getLock = redisService.lock(lockKey, "1", submitLockTime, TimeUnit.SECONDS);
        if (!getLock) {
            //获取锁失败，说明submit结果重复，之前已处理过相同逻辑，直接返回
            return;
        }

        //更新订单表
        SmsOrderDO smsOrderDO = SmsOrderDO.builder()
                .orderId(submitResqDO.getOrderId())
                .resend(submitResqDTO.getResend() != null ? submitResqDTO.getResend() : 0)
                .channelMsgId(submitResqDO.getChannelMsgId())
                .sendStatus(submitResqDO.getStatus())
                .sendCode(submitResqDO.getResult())
                .recvSubmitTime(submitResqDO.getRecvSubmitTime())
                .sendDesc(submitResqDO.getDescription())
                .tableNameSuffix(submitResqDTO.getTableNameSuffix())
                .build();
        orderSaveBatchService.saveOrUpdateOrderResqCount(smsOrderDO);

        //短信重发，提交到线程池处理
        retryThreadPool.execute(() -> retry(submitResqDTO, submitResqDO));
    }

    /**
     * 将接收到的submit队列信息映射到SubmitResqDO实体类中
     *
     * @param submitResqDTO submit队列
     * @return 实体类SubmitResqDO
     */
    @Override
    public SubmitResqDO transferSubmit(SubmitResqDTO submitResqDTO) {
        SubmitResqDO submitResqDO = new SubmitResqDO();
        submitResqDO.setOrderId(submitResqDTO.getOrderId());
        submitResqDO.setChannelMsgId(submitResqDTO.getChannelMsgId());
        submitResqDO.setRecvSubmitTime(new BigDecimal(submitResqDTO.getRecvSubmitTime()).divide(new BigDecimal(1000)).intValue());
        submitResqDO.setResult("");
        submitResqDO.setStatus(getChannelErrCodeDTO().getXhErrCode());
        submitResqDO.setDescription(getChannelErrCodeDTO().getCodeDesc());
        if (submitResqDTO.getChannelCode() != null && submitResqDTO.getResult() != null) {
            submitResqDO.setResult(String.valueOf(submitResqDTO.getResult()));
            ChannelErrCodeDTO channelErrCodeDTO = getSubmitStatus(submitResqDTO.getChannelCode(), String.valueOf(submitResqDTO.getResult()));
            setChannelErrCodeDTO(channelErrCodeDTO);
            submitResqDO.setStatus(channelErrCodeDTO.getXhErrCode());
            submitResqDO.setDescription(channelErrCodeDTO.getCodeDesc());
        }
        return submitResqDO;
    }

    /**
     * 将接收到的submit队列信息映射到order实体类中
     *
     * @param submitResqDO submit队列
     * @return order实体类SmsOrderDO
     */
    @Override
    public SmsOrderDO sendSubmitToOrder(SubmitResqDO submitResqDO) {
        SmsOrderDO smsOrderDO = new SmsOrderDO();
        smsOrderDO.setOrderId(submitResqDO.getOrderId());
        smsOrderDO.setChannelMsgId(submitResqDO.getChannelMsgId());
        smsOrderDO.setSendStatus(submitResqDO.getStatus());
        smsOrderDO.setSendCode(submitResqDO.getResult());
        smsOrderDO.setRecvSubmitTime(submitResqDO.getRecvSubmitTime());
        smsOrderDO.setSendDesc(submitResqDO.getDescription());
        return smsOrderDO;
    }

    /**
     * 关系映射
     *
     * @param channelCode 渠道code
     * @param status      状态码
     * @return 映射关系
     */
    private ChannelErrCodeDTO getSubmitStatus(String channelCode, String status) {
        // 从渠道code中查找
        ChannelErrCodeDTO channelErrCodeDTO = CodeMappingCache.getInstance().getErrCode(channelCode, CodeMappingConstants.TYPE_SUBMIT, status);
        if (channelErrCodeDTO != null) {
            return channelErrCodeDTO;
        }
        // 从默认中查找
        channelErrCodeDTO = CodeMappingCache.getInstance().getErrCode(CodeMappingConstants.CHANNEL_CODE_DEFAULT, CodeMappingConstants.TYPE_SUBMIT, status);
        if (channelErrCodeDTO != null) {
            return channelErrCodeDTO;
        }
        // 返回[404,"未知错误码"]
        return new ChannelErrCodeDTO(CodeMappingConstants.XH_CODE_DEFAULT, CodeMappingConstants.XH_DESC_DEFAULT, CodeMappingConstants.ERR_CODE_RETRY);
    }

    /**
     * 短信重发
     *
     * @param submitResqDTO
     * @param submitResqDO
     */
    private void retry(SubmitResqDTO submitResqDTO, SubmitResqDO submitResqDO) {
        RetryDTO retryDTO = RetryDTO.builder()
                .type(CodeMappingConstants.TYPE_SUBMIT)
                .orderId(submitResqDTO.getOrderId())
                .channelErrCodeDTO(getChannelErrCodeDTO())
                .channelStatus(submitResqDO.getResult())
                .resend(submitResqDTO.getResend())
                .smsTypeCode(submitResqDTO.getSmsTypeCode())
                .reqSrc(submitResqDTO.getReqSrc())
                .gatewayUserName(submitResqDTO.getGatewayUserName())
                .build();
        Boolean isRetry = retryService.retry(retryDTO);
        if (ProtocolTypeEnum.CMPP.getValue().equals(submitResqDTO.getReqSrc()) && isRetry && !CodeMappingConstants.XH_SUCCESS_CODE_DEFAULT.equals(submitResqDO.getStatus())) {
            // 如果没有重发了，并且发送状态不正常，则发送redis
            CmppRedisDTO cmppRedisDTO = CmppRedisDTO.builder()
                    .requestId(submitResqDTO.getRequestId() == null ? "" : submitResqDTO.getRequestId())
                    .gatewayUserName(submitResqDTO.getGatewayUserName() == null ? "" : submitResqDTO.getGatewayUserName())
                    .msgStatus(GatewayEnum.DONE.getMessageStatus())
                    .reportStatus(GatewayEnum.S.concat(submitResqDTO.getResult().toString()))
                    .submitTime(DateUtil.intTimeToStr(submitResqDTO.getRecvSendTime()))
                    .doneTime(DateUtil.intTimeToStr(submitResqDTO.getRecvSendTime()))
                    .build();
            sendToCmppService.saveRedis(cmppRedisDTO);
        }
    }
}
