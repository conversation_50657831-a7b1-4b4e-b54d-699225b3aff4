package com.xhqb.spectre.logserver.service.impl;

import com.xhqb.spectre.common.dal.dto.mq.SendSubmitDTO;
import com.xhqb.spectre.common.dal.entity.TplUsageDO;
import com.xhqb.spectre.logserver.service.TplUsageSaveBatchService;
import com.xhqb.spectre.logserver.service.TplUsageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class TplUsageServiceImpl implements TplUsageService {

    @Autowired
    private TplUsageSaveBatchService tplUsageSaveBatchService;
    @Override
    public void save(SendSubmitDTO submitDTO) {
        if (submitDTO == null) {
            log.info("获取submitDTO数据为空");
            return;
        }
        // 实体类转换
        TplUsageDO tplUsageDO = convert(submitDTO);
        // 插入数据库
        tplUsageSaveBatchService.insertByBatch(tplUsageDO);
    }

    private TplUsageDO convert(SendSubmitDTO submitDTO) {
        TplUsageDO tplUsageDO = new TplUsageDO();
        tplUsageDO.setOrderId(submitDTO.getOrderId());
        tplUsageDO.setAppCode(submitDTO.getAppCode());
        tplUsageDO.setTplCode(submitDTO.getTplCode());
        tplUsageDO.setSmsTypeCode(submitDTO.getSmsTypeCode());
        tplUsageDO.setSignName(submitDTO.getSignName());
        tplUsageDO.setSendTime(submitDTO.getSendTime());
        tplUsageDO.setTableNameSuffix(submitDTO.getTableNameSuffix());
        return tplUsageDO;
    }
}
