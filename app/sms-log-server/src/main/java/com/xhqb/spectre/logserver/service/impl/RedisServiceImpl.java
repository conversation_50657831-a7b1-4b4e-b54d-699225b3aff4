package com.xhqb.spectre.logserver.service.impl;

import com.xhqb.spectre.logserver.service.RedisService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/*
 * @Author: huangyanxiong
 * @Date: 2021/12/31 10:52
 * @Description:
 */
@Service
public class RedisServiceImpl implements RedisService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 加锁
     *
     * @param key
     * @param value
     * @param timeout
     * @param unit
     * @return
     */
    @Override
    public Boolean lock(String key, String value, long timeout, TimeUnit unit) {
        return stringRedisTemplate.opsForValue().setIfAbsent(key, value, timeout, unit);
    }

    /**
     * 解锁
     *
     * @param key
     * @return
     */
    @Override
    public Boolean unLock(String key) {
        return stringRedisTemplate.delete(key);
    }
}
