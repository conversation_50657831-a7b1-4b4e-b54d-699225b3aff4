package com.xhqb.spectre.logserver.service.batch;

import com.xhqb.spectre.common.dal.entity.SmsUplinkDO;
import com.xhqb.spectre.logserver.dal.LsSmsUplinkMapper;
import com.xhqb.spectre.logserver.service.UplinkSaveBatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量保存cmpp上行短信回执
 *
 * <AUTHOR>
 * @date 2021/10/14
 */
@Service
@Slf4j
public class UplinkSaveBatchServiceImpl implements UplinkSaveBatchService {

    @Resource
    private LsSmsUplinkMapper lsSmsUplinkMapper;

    private static final List<SmsUplinkDO> smsUplinkDOList = new ArrayList<>();

    @Value("${spectre.logServer.batchSaveCount}")
    private Integer count;

    /**
     * 批量保存
     *
     * @param smsUplinkDO
     */
    @Override
    public void saveUplinkBatch(SmsUplinkDO smsUplinkDO) {
        synchronized (smsUplinkDOList) {
            smsUplinkDOList.add(smsUplinkDO);
            if (smsUplinkDOList.size() >= count) {
                insertUplink();
            }
        }
    }

    /**
     * 存入数据库操作
     */
    @Override
    public void insertUplink() {
        // 批量插入
        if (smsUplinkDOList.size() > 0) {
            try {
                log.info("上行短信批量入库：size={}", smsUplinkDOList.size());
                lsSmsUplinkMapper.insertBatch(smsUplinkDOList);
                smsUplinkDOList.clear();
            } catch (Exception e) {
                log.warn("UplinkSaveBatchServiceImpl insertUplink exception", e);
            }
        }
    }

    /**
     * 定时任务
     */
    @Scheduled(fixedDelayString = "${spectre.logserver.uplinkScheduled.ScheduledTime}")
    public void orderScheduled() {
        synchronized (smsUplinkDOList) {
            insertUplink();
        }
    }

    /**
     * Bean销毁前将订单入库
     */
    @PreDestroy
    public void preDestroy() {
        orderScheduled();
    }
}
