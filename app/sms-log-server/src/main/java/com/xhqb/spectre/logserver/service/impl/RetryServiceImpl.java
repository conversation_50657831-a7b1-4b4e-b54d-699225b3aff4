package com.xhqb.spectre.logserver.service.impl;

import com.xhqb.spectre.common.constant.CodeMappingConstants;
import com.xhqb.spectre.common.enums.MessageTypeEnum;
import com.xhqb.spectre.logserver.constant.ResendConstant;
import com.xhqb.spectre.logserver.dto.RetryDTO;
import com.xhqb.spectre.logserver.factory.ResendMessage;
import com.xhqb.spectre.logserver.factory.RetryFactory;
import com.xhqb.spectre.logserver.service.MetisReceiptService;
import com.xhqb.spectre.logserver.service.RetryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/10/20
 */
@Service
@Slf4j
public class RetryServiceImpl implements RetryService {

    @Resource
    private RetryFactory retryFactory;

    @Resource
    private MetisReceiptService metisReceiptService;

    @Value("${logServer.retry.failover_anyway}")
    private Integer failOverAnyway;

    /**
     * 重发短信逻辑（submit允许重发一次，deliver允许重发两次）
     *
     * @param retryDTO 重发短信实体类
     * @return 是否不需要重发 是true 否false
     */
    @Override
    public Boolean retry(RetryDTO retryDTO) {
        // 全部重试开关，为0关闭，为1打开，打开后除了成功发送的短信外全部重试。
        // 判断该状态码是否需要重发 retry 是否重发 0不重发 1重发
        if (failOverAnyway == 0 && ResendConstant.RETRY_SYMBOL != retryDTO.getChannelErrCodeDTO().getRetry()) {
            // 不重发原因：该状态码retry设置为不重发。
            return true;
        }

        // 判断是submit还是回执
        int resend = Objects.nonNull(retryDTO.getResend()) ? retryDTO.getResend() : 0;
        if (CodeMappingConstants.TYPE_DELIVER.equals(retryDTO.getType())) {
            if (CodeMappingConstants.DELIVER_CHANNEL_CODE_SUCCESS.equals(retryDTO.getChannelStatus())) {
                // 不重发原因：回执正确接收。
                // 回执消息中心
                // https://www.tapd.cn/67739864/prong/stories/view/1167739864001104458
                metisReceiptService.callMetisWithDeliverSuccess(retryDTO);
                return true;
            }
            if (resend >= ResendConstant.RESEND_10) {
                // 不重发原因：deliver已经重发了两次
                return true;
            }
        } else {
            // 如果submit结果显示0或9999，则不重试
            if (CodeMappingConstants.SUBMIT_CHANNEL_CODE.equals(retryDTO.getChannelStatus())
                    || CodeMappingConstants.SUBMIT_CHANNEL_CODE_9999.equals(retryDTO.getChannelStatus())) {
                // 不重发原因：submit正确接收。
                return true;
            }
            if (resend != ResendConstant.RESEND_0 && resend != ResendConstant.RESEND_2 && resend != ResendConstant.RESEND_10) {
                // 不重发原因：submit只能重发一次
                return true;
            }
        }
        // 根据短信类型选择重发策略
        MessageTypeEnum messageType = MessageTypeEnum.getByMessageType(retryDTO.getSmsTypeCode());
        // 根据类型判断发送到哪个mq
        ResendMessage resendMessage = retryFactory.getResendMessage(messageType);
        if (Objects.isNull(resendMessage)) {
            return true;
        }
        return resendMessage.sendMq(retryDTO);
    }
}
