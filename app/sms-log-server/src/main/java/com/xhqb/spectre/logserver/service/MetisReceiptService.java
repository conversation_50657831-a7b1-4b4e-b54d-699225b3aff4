package com.xhqb.spectre.logserver.service;

import com.xhqb.spectre.common.dal.dto.mq.SendSubmitDTO;
import com.xhqb.spectre.logserver.dto.RetryDTO;

/**
 * 回执消息中心服务
 *
 * @author: cl
 * @date: 2023/09/13
 */
public interface MetisReceiptService {

    /**
     * 回执成功时回调消息中心
     *
     * @param retryDTO 重试信息
     */
    void callMetisWithDeliverSuccess(RetryDTO retryDTO);

    /**
     * 提交失败或回执失败时，回调消息中心(无可用通道)
     *
     * @param retryDTO      重试信息
     * @param sendSubmitDTO 订单信息
     */
    void callMetisWithSubmitOrDeliverFail(RetryDTO retryDTO, SendSubmitDTO sendSubmitDTO);

}
