package com.xhqb.spectre.logserver.factory;

import com.alibaba.fastjson.JSON;
import com.xhqb.kael.mq.producer.MQTemplate;
import com.xhqb.spectre.common.constant.CodeMappingConstants;
import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.dto.mq.SendSubmitDTO;
import com.xhqb.spectre.common.mq.ChannelCode;
import com.xhqb.spectre.common.mq.MessageMQ;
import com.xhqb.spectre.logserver.dto.RetryDTO;
import com.xhqb.spectre.logserver.service.MetisReceiptService;
import com.xhqb.spectre.logserver.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.MessageId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/22 2:36 下午
 */
@Slf4j
@Component
public class ResendMessage {

    @Resource
    private MQTemplate mqTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisService redisService;

    @Resource
    private MetisReceiptService metisReceiptService;

    @Value("${sms.resend.lock.minute:120}")
    private Long resendLockTime;

    /**
     * 获取差值时间
     *
     * @return 差值
     */
    public ResendMessageProperties getResendMessageProperties() {
        return null;
    }

    /**
     * 将重发短信消息体发送到mq中
     *
     * @param retryDTO 重发所需参数
     * @return 是否整个流程走完 是true 否false
     */
    public Boolean sendMq(RetryDTO retryDTO) {
//        log.info("重发参数，retryDTO：{}", retryDTO);
        // 获取值
        Long orderId = retryDTO.getOrderId();
        String orderIdStr = String.valueOf(orderId);
        Integer resend = retryDTO.getResend();
        // 类型为回执重发, 如果时间差值为空 或者大于指定差值 不重发
        if (CodeMappingConstants.TYPE_DELIVER.equals(retryDTO.getType())
                && retryDTO.getSubTime() > getResendMessageProperties().getDiffTime()) {
            log.info("不重发原因：间隔时间过长。间隔时间为{},短信类型为{},orderId:{}", retryDTO.getSubTime(), retryDTO.getSmsTypeCode(), orderId);
            return true;
        }

        //使用Redis分布式锁，防止相同订单重复执行重发逻辑（原因是长短信会拆分成几个deliver消息，或者渠道商推送了几次回执）。无需手动释放锁，等自然过期，在有效期内拦截相同订单。
        String lockKey = MessageFormat.format(RedisKeys.LogServerKeys.RESEND_LOCK_KEY, orderIdStr, resend);
        Boolean getLock = redisService.lock(lockKey, "1", resendLockTime, TimeUnit.MINUTES);
        if (!getLock) {
            //获取锁失败，说明已有相同订单正在执行该逻辑，直接返回
            log.info("不重发原因：获取分布式锁失败，长短信重复。orderId:{}", orderId);
            return false;
        }
        try {
            // 从redis中获取重发短信的参数
            String orderCacheKey = MessageFormat.format(RedisKeys.DispatcherKeys.ORDER_KEY, orderIdStr, resend);
            String orderStr = stringRedisTemplate.opsForValue().get(orderCacheKey);
            if (orderStr == null) {
                log.info("不重发原因：redis获取数据为空。redisKey:{}, orderId:{}", orderCacheKey, orderId);
                return true;
            }
            SendSubmitDTO sendSubmitDTO = JSON.parseObject(orderStr, SendSubmitDTO.class);
            List<ChannelCode> channelCodeSet = sendSubmitDTO.getChannelCodeSet();
            if (channelCodeSet == null || channelCodeSet.size() <= 1) {
                log.info("不重发原因：可选渠道为空或只有一个。orderId:{}, channelCodeSet:{}", orderId, channelCodeSet);
                // 回执消息中心
                // https://www.tapd.cn/67739864/prong/stories/view/1167739864001104458
                metisReceiptService.callMetisWithSubmitOrDeliverFail(retryDTO, sendSubmitDTO);
                return true;
            }


            // 构造MQ消息
            MessageMQ messageMQ = buildMessageMQ(sendSubmitDTO);
            messageMQ.setGatewayUserName(retryDTO.getGatewayUserName());
            // 判断是回执重发还是submit重发
            if (CodeMappingConstants.TYPE_DELIVER.equals(retryDTO.getType())) {
                if (resend < 2) {
                    messageMQ.setResend(resend + 2);
                } else {
                    messageMQ.setResend(resend + 8);
                }
            } else {
                if (resend == 0) {
                    messageMQ.setResend(resend + 1);
                } else {
                    messageMQ.setResend(resend + 4);
                }
            }
            // 发q
            MessageId messageId = mqTemplate.send(getResendMessageProperties().getMessageTopic(), JSON.toJSONString(messageMQ));
            log.info("短信重发发q成功; orderId={},messageId={},messageMQ={}", orderId, messageId, messageMQ);
        } catch (Exception e) {
            log.warn("ResendMessage send exception", e);
        }
        return false;
    }

    /**
     * 类型转换
     *
     * @param sendSubmitDTO orderDTO redis实体类
     * @return 发q实体类
     */
    public MessageMQ buildMessageMQ(SendSubmitDTO sendSubmitDTO) {
        MessageMQ messageMQ = MessageMQ.builder()
                .appCode(sendSubmitDTO.getAppCode())
                .phone(sendSubmitDTO.getMobile())
                .province(sendSubmitDTO.getProvinceShortName())
                .city(sendSubmitDTO.getCityShortName())
                .isp(sendSubmitDTO.getIspCode())
                .smsCode(sendSubmitDTO.getSmsTypeCode())
                .orderId(String.valueOf(sendSubmitDTO.getOrderId()))
                .signName(sendSubmitDTO.getSignName())
                .sliceId(sendSubmitDTO.getSliceId())
                .sendType(sendSubmitDTO.getSendType())
                .receiveTime(String.valueOf(System.currentTimeMillis()))
                .tplCode(sendSubmitDTO.getTplCode())
                .content(sendSubmitDTO.getContent())
                .requestId(sendSubmitDTO.getRequestId())
                .batchId(sendSubmitDTO.getBatchId())
                .reqSrc(sendSubmitDTO.getReqSrc())
                .phoneStatus(sendSubmitDTO.getPhoneStatus())
                .channelCodeSet(sendSubmitDTO.getChannelCodeSet())
                // 解决bizBatchId丢失问题 - 2024-12-10
                .bizBatchId(sendSubmitDTO.getBizBatchId())
                .callMetis(sendSubmitDTO.getCallMetis())
                .build();
        if (!StringUtils.isEmpty(sendSubmitDTO.getParameter())) {
            List<String> list = Arrays.asList(sendSubmitDTO.getParameter().split(","));
            messageMQ.setParamMap(list);
        }
        return messageMQ;
    }


}
