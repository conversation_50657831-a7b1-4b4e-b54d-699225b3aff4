package com.xhqb.spectre.logserver.service;

import com.xhqb.spectre.common.dal.dto.mq.SubmitResqDTO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.entity.SubmitResqDO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/19 10:49 上午
 */
public interface SubmitResqService {

    /**
     * 保存发送结果到数据库中
     *
     * @param submitResqDTO mq发送结果
     */
    void saveOrder(SubmitResqDTO submitResqDTO);

    /**
     * 将接收到的submit队列信息映射到SubmitResqDO实体类中
     *
     * @param submitResqDTO submit队列
     * @return 实体类SubmitResqDO
     */
    SubmitResqDO transferSubmit(SubmitResqDTO submitResqDTO);

    /**
     * 将接收到的submit队列信息映射到order实体类中
     *
     * @param submitResqDO submit队列
     * @return order实体类SmsOrderDO
     */
    SmsOrderDO sendSubmitToOrder(SubmitResqDO submitResqDO);
}
