package com.xhqb.spectre.logserver.configuration;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/24
 */
@Data
@Component
public class VenusConfig {

    /**
     * 订单原始保存开关
     */
    @Value("${order.save.enable:true}")
    private volatile boolean orderSaveEnable;

    /**
     * 订单是否开启分表
     */
    @Value("${order.sharding.enable:true}")
    private volatile boolean orderShardingEnable;
}
