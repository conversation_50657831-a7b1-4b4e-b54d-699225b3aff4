package com.xhqb.spectre.logserver.service.batch;

import com.xhqb.spectre.common.dal.entity.ShortUrlLogDO;
import com.xhqb.spectre.common.dal.mapper.ShortUrlLogMapper;
import com.xhqb.spectre.logserver.service.UrlShortLogSaveBatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/15
 * 批量插入短链日志
 */
@Service
@Slf4j
public class UrlShortLogSaveBatchServiceImpl implements UrlShortLogSaveBatchService {

    @Resource
    private ShortUrlLogMapper shortUrlLogMapper;

    private static final List<ShortUrlLogDO> shortUrlLogDOList = new ArrayList<>();

    /**
     * 缓存最大条数
     */
    @Value("${spectre.logServer.batchSaveCount}")
    private Integer count;

    @Override
    public void saveUrlShortLog(ShortUrlLogDO shortUrlLogDO) {
        synchronized (shortUrlLogDOList) {
            shortUrlLogDOList.add(shortUrlLogDO);
            if (shortUrlLogDOList.size() >= count) {
                insertOrUpdate();
            }
        }
    }

    @Override
    public void insertOrUpdate() {
        // 批量插入代码
        if (shortUrlLogDOList.size() > 0) {
            try {
                log.info("短链日志批量入库，size={}", shortUrlLogDOList.size());
                shortUrlLogMapper.insertBatch(shortUrlLogDOList);
                shortUrlLogDOList.clear();
            } catch (Exception e) {
                log.warn("UrlShortLogSaveBatchServiceImpl insertOrUpdate exception", e);
            }
        }
    }

    /**
     * 定时任务
     */
    @Scheduled(fixedDelayString = "${spectre.logserver.urlShortScheduled.ScheduledTime}")
    public void urlShortScheduled() {
        synchronized (shortUrlLogDOList) {
            insertOrUpdate();
        }
    }

    /**
     * Bean销毁前将订单入库
     */
    @PreDestroy
    public void preDestroy() {
        urlShortScheduled();
    }

}
