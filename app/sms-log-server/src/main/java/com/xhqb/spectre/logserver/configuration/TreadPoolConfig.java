package com.xhqb.spectre.logserver.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/*
 * @Author: huangyanxiong
 * @Date: 2021/12/31 10:21
 * @Description:
 */
@Configuration
public class TreadPoolConfig {

    /**
     * 短信重发的线程池
     *
     * @return
     */
    @Bean(name = "retryThreadPool")
    public ThreadPoolExecutor retryThreadPool() {
        return new ThreadPoolExecutor(
                8,
                16,
                3L,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(2000),
                new CustomizableThreadFactory("retry-pool-"),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

}
