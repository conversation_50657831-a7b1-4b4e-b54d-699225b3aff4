package com.xhqb.spectre.logserver.enums;

/**
 * <AUTHOR>
 * @date 2021/11/29
 */
public enum GatewayEnum {
    /**
     * 无状态
     */
    UNKNOWN("0", "无状态"),
    /**
     * 已提交
     */
    COMMIT("1", "已提交"),
    /**
     * 已送达
     */
    DONE("2", "已送达");
    /**
     * 回执状态
     */
    public static final byte[] STATE = "State".getBytes();
    /**
     * 短信提交时间
     */
    public static final byte[] SUBMIT_TIME = "SubmitTime".getBytes();
    /**
     * 短信完成时间
     */
    public static final byte[] DONE_TIME = "DoneTime".getBytes();
    public static final byte[] DESTTERMINAL_ID = "DestterminalId".getBytes();
    public static final byte[] DEST_ID = "DestId".getBytes();
    public static final String SESSION = "Spectre:CMPPGateway:Session:";
    public static final String MSG = "Spectre:CMPPGateway:Msg:";
    public static final String USER_MSG = "Spectre:CMPPGateway:UserMsg:";
    public static final String GATEWAY = "Gateway:";
    public static final String S = "s:";

    private final String messageStatus;

    private final String description;

    public String getMessageStatus() {
        return messageStatus;
    }

    public String getDescription() {
        return description;
    }

    /**
     * construct.
     *
     * @param messageStatus 状态码
     * @param description 描述
     */
    GatewayEnum(String messageStatus, String description) {
        this.messageStatus = messageStatus;
        this.description = description;
    }
}
