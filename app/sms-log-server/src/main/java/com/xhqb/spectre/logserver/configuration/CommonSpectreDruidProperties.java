package com.xhqb.spectre.logserver.configuration;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * spectredb druid config
 *
 * <AUTHOR>
 * @date 2023/09/14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(prefix = "kael.config.resources.druid.spectre-logserver")
public class CommonSpectreDruidProperties extends DruidProperties {

}

