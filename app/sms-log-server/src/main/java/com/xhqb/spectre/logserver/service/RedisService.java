package com.xhqb.spectre.logserver.service;

import java.util.concurrent.TimeUnit;

/*
 * @Author: h<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/12/31 10:51
 * @Description:
 */
public interface RedisService {

    /**
     * 加锁
     * @param key
     * @param value
     * @param timeout
     * @param unit
     * @return
     */
    Boolean lock(String key, String value, long timeout, TimeUnit unit);

    /**
     * 解锁
     * @param key
     * @return
     */
    Boolean unLock(String key);

}
