package com.xhqb.spectre.logserver.job;

import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.entity.OpTimeDO;
import com.xhqb.spectre.common.dal.mapper.OpTimeMapper;
import com.xhqb.spectre.common.utils.DateUtil;
import com.xhqb.spectre.logserver.cache.CodeMappingCache;
import com.xhqb.spectre.logserver.service.CodeMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 监听opTime是否更改
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/18 6:39 下午
 */
@Component
@Slf4j
public class CodeMappingJob {

    @Resource
    OpTimeMapper opTimeMapper;

    @Resource
    CodeMappingService codeMappingService;

    /**
     * 定时任务
     */
    @Scheduled(fixedDelayString = "${spectre.logserver.CodeMapping.ScheduledTime}")
    public void getCodeMappingScheduled() {
        //根据opTime判断数据是否变更
        OpTimeDO opTimeDO = opTimeMapper.selectByModule(OpLogConstant.MODULE_ERR_CODE_MAPPING);
        if (Objects.isNull(opTimeDO)) {
            return;
        }
        Integer opTime = opTimeDO.getOpTime();
        Integer lastUpdateTime = CodeMappingCache.getInstance().getDataUpdateTime();
        if (Objects.isNull(lastUpdateTime) || opTime > lastUpdateTime) {
            // 更新缓存
            codeMappingService.getCodeMapping();
            // 设置数据更新时间
            CodeMappingCache.getInstance().setDataUpdateTime(opTime);
            log.info("错误码映射缓存更新，此次更新时间：{}，上一次更新时间：{}", DateUtil.intToString(opTime), DateUtil.intToString(lastUpdateTime));
        }
    }
}
