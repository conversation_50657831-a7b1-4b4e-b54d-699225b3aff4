package com.xhqb.spectre.logserver.service.batch;

import com.xhqb.spectre.common.constant.RedisKeys;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.logserver.configuration.VenusConfig;
import com.xhqb.spectre.logserver.dal.LsSmsOrderMapper;
import com.xhqb.spectre.logserver.dal.LsSmsOrderMonthMapper;
import com.xhqb.spectre.logserver.service.OrderSaveBatchService;
import com.xhqb.spectre.logserver.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/26 4:44 下午
 */
@Service
@Slf4j
public class OrderSaveBatchServiceImpl implements OrderSaveBatchService {

    @Resource
    private LsSmsOrderMapper lsSmsOrderMapper;

    private static final List<SmsOrderDO> smsOrderDOList = new ArrayList<>();

    private static final List<SmsOrderDO> smsOrderDOListResq = new ArrayList<>();

    private static final List<SmsOrderDO> smsOrderDOListReport = new ArrayList<>();

    @Resource
    private RedisService redisService;

    /**
     * 短信订单表批量写入的分布式锁的超时时间，单位：毫秒
     */
    @Value("${sms.order.batchSave.lock.time:1000}")
    private Long batchSaveLockTime;

    @Value("${spectre.logServer.batchSaveCount:100}")
    private Integer count;

    private static final String BATCH_SAVE_LOCK_KEY = RedisKeys.LogServerKeys.SMS_ORDER_WRITE_LOCK_KEY;

    @Resource
    private LsSmsOrderMonthMapper lsSmsOrderMonthMapper;
    @Resource
    private VenusConfig venusConfig;
    @Resource
    private SmsOrderShardingService smsOrderShardingService;

    /**
     * 保存接收到回执次数的order
     *
     * @param smsOrderDO
     */
    @Override
    public void saveOrUpdateOrderReport(SmsOrderDO smsOrderDO) {
        synchronized (smsOrderDOListReport) {
            smsOrderDOListReport.add(smsOrderDO);
            if (smsOrderDOListReport.size() >= count) {
                insertOrUpdateOrderReport();
            }
        }
    }

    /**
     * 保存接收到订单的order
     *
     * @param smsOrderDO
     */
    @Override
    public void saveOrUpdateOrder(SmsOrderDO smsOrderDO) {
        synchronized (smsOrderDOList) {
            smsOrderDOList.add(smsOrderDO);
            if (smsOrderDOList.size() >= count) {
                insertOrUpdate();
            }
        }
    }

    /**
     * 保存接收到submit req次数
     *
     * @param smsOrderDO
     */
    @Override
    public void saveOrUpdateOrderResqCount(SmsOrderDO smsOrderDO) {
        synchronized (smsOrderDOListResq) {
            smsOrderDOListResq.add(smsOrderDO);
            if (smsOrderDOListResq.size() >= count) {
                insertOrUpdateOrderResq();
            }
        }
    }

    /**
     * 批量插入 更新reportcount
     */
    private void insertOrUpdateOrderReport() {
        //加锁，获取锁失败时直接返回
        if (!lock()) {
            return;
        }
        try {
            log.info("回执时订单批量修改，size={}", smsOrderDOListReport.size());
            if (venusConfig.isOrderSaveEnable()) {
                // 控制t_sms_order 表的数据回执状态更新
                lsSmsOrderMapper.insertOrUpdateBatchReportCount(smsOrderDOListReport);
            }

            if (venusConfig.isOrderShardingEnable()) {
                // 控制订单分表的发送回执状态数据更新
                smsOrderShardingService.smsOrderBySharding(smsOrderDOListReport, "DeliverResqConsumer发送回执处理", (tableName, orderList) -> lsSmsOrderMonthMapper.insertOrUpdateBatchReportCount(tableName, orderList));
            }

            smsOrderDOListReport.clear();
        } catch (Exception e) {
            log.warn("OrderSaveBatchServiceImpl insertOrUpdateOrderReport exception, message: {}", e.getMessage());
        } finally {
            //解锁
            unLock();
        }
    }

    /**
     * 批量插入
     */
    private void insertOrUpdate() {
        //加锁，获取锁失败时直接返回
        if (!lock()) {
            return;
        }
        try {
            log.info("订单批量入库，size={}", smsOrderDOList.size());

            if (venusConfig.isOrderSaveEnable()) {
                // 控制t_sms_order 表的数据写入
                lsSmsOrderMapper.insertOrUpdateBatch(smsOrderDOList);
            }

            if (venusConfig.isOrderShardingEnable()) {
                // 控制订单分表的数据写入
                smsOrderShardingService.smsOrderBySharding(smsOrderDOList, "SendSubmitConsumer提交订单处理", (tableName, orderList) -> lsSmsOrderMonthMapper.insertOrUpdateBatch(tableName, orderList));
            }

            smsOrderDOList.clear();
        } catch (Exception e) {
            //这里只打印message字段，原因是exception的stack_trace字段太大，会导致写入es失败
            log.warn("OrderSaveBatchServiceImpl insertOrUpdate exception, message: {}", e.getMessage());
        } finally {
            //解锁
            unLock();
        }
    }

    /**
     * 批量插入 更新submitresqcount
     */
    private void insertOrUpdateOrderResq() {
        //加锁，获取锁失败时直接返回
        if (!lock()) {
            return;
        }
        try {
            log.info("提交时订单批量修改，size={}", smsOrderDOListResq.size());
            if (venusConfig.isOrderSaveEnable()) {
                // 控制t_sms_order 表的发送状态更新
                lsSmsOrderMapper.insertOrUpdateBatchSubmitCount(smsOrderDOListResq);
            }

            if (venusConfig.isOrderShardingEnable()) {
                // 控制订单分表的提交回执状态更新
                smsOrderShardingService.smsOrderBySharding(smsOrderDOListResq, "SubmitResqConsumer提交回执处理", (tableName, orderList) -> lsSmsOrderMonthMapper.insertOrUpdateBatchSubmitCount(tableName, orderList));
            }

            smsOrderDOListResq.clear();
        } catch (Exception e) {
            log.warn("OrderSaveBatchServiceImpl insertOrUpdateOrderResq exception, message: {}", e.getMessage());
        } finally {
            //解锁
            unLock();
        }
    }

    /**
     * 加锁
     *
     * @return
     */
    private boolean lock() {
        Boolean getLock = redisService.lock(BATCH_SAVE_LOCK_KEY, "1", batchSaveLockTime, TimeUnit.MILLISECONDS);
        return Boolean.TRUE.equals(getLock);
    }

    /**
     * 解锁
     *
     * @return
     */
    private void unLock() {
        redisService.unLock(BATCH_SAVE_LOCK_KEY);
    }

    /**
     * 定时任务
     */
    @Scheduled(fixedDelayString = "${spectre.logserver.orderScheduled.ScheduledTime}")
    public void orderScheduled() {
        synchronized (smsOrderDOList) {
            if (smsOrderDOList.size() > 0) {
                insertOrUpdate();
            }
        }
        synchronized (smsOrderDOListResq) {
            if (smsOrderDOListResq.size() > 0) {
                insertOrUpdateOrderResq();
            }
        }
        synchronized (smsOrderDOListReport) {
            if (smsOrderDOListReport.size() > 0) {
                insertOrUpdateOrderReport();
            }
        }
    }

    /**
     * Bean销毁前将订单入库
     */
    @PreDestroy
    public void preDestroy() {
        orderScheduled();
    }
}