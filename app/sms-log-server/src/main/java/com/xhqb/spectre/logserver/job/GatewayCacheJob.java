package com.xhqb.spectre.logserver.job;

import com.xhqb.spectre.common.constant.OpLogConstant;
import com.xhqb.spectre.common.dal.entity.OpTimeDO;
import com.xhqb.spectre.common.dal.mapper.OpTimeMapper;
import com.xhqb.spectre.common.utils.DateUtil;
import com.xhqb.spectre.logserver.cache.GatewayCache;
import com.xhqb.spectre.logserver.service.GatewayCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/3 3:23 下午
 */
@Slf4j
@Component
public class GatewayCacheJob {

    @Resource
    OpTimeMapper opTimeMapper;
    @Resource
    GatewayCacheService gatewayCacheService;
    @Value("${spectre.logserver.GatewaySession.SubTime}")
    private Integer gatewaySubTime;

    /**
     * 定时任务
     */
    @Scheduled(fixedDelayString = "${spectre.logserver.GatewayUsername.ScheduledTime}")
    public void getUsernameScheduled() {
        //根据opTime判断数据是否变更
        OpTimeDO opTimeDO = opTimeMapper.selectByModule(OpLogConstant.MODULE_GATEWAY_USER);
        if (Objects.isNull(opTimeDO)) {
            return;
        }
        Integer opTime = opTimeDO.getOpTime();
        Integer lastUpdateTime = GatewayCache.getInstance().getUsernameUpdateTime();
        if (Objects.isNull(lastUpdateTime) || opTime > lastUpdateTime) {
            // 更新缓存
            gatewayCacheService.getUsername();
            // 设置数据更新时间
            GatewayCache.getInstance().setUsernameUpdateTime(opTime);
            log.info("用户缓存更新，此次更新时间：{}，上一次更新时间：{}", DateUtil.intToString(opTime), DateUtil.intToString(lastUpdateTime));
        }
    }

    /**
     * 定时任务
     */
    @Scheduled(fixedDelayString = "${spectre.logserver.GatewaySession.ScheduledTime}")
    public void getSessionScheduled() {
        Integer lastUpdateTime = GatewayCache.getInstance().getSessionUpdateTime();
        Integer nowTime = DateUtil.getNow();
        if (Objects.isNull(lastUpdateTime) || nowTime >= (lastUpdateTime + gatewaySubTime)) {
            gatewayCacheService.getSession();
            // 设置数据更新时间
            GatewayCache.getInstance().setSessionUpdateTime(nowTime);
        }
    }
}
