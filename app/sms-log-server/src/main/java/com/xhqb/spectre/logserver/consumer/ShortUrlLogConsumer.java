package com.xhqb.spectre.logserver.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xhqb.kael.mq.annotation.MQConsumer;
import com.xhqb.spectre.common.dal.entity.ShortUrlLogDO;
import com.xhqb.spectre.common.mq.UrlShortenerLogMQ;
import com.xhqb.spectre.logserver.service.UrlShortLogSaveBatchService;
import com.xhqb.spectre.logserver.utils.BaseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.SubscriptionType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/11/15
 * 保存短链访问日志
 */
@Component
@Slf4j
public class ShortUrlLogConsumer {


    @Resource
    private UrlShortLogSaveBatchService urlShortLogSaveBatchService;

    /**
     * 保存order
     *
     * @param message mq
     */
    @MQConsumer(topic = "#{'${kael.mq.consumers:}'.split(',')[4]}",
            subscriptionType = SubscriptionType.Shared,
            clazz = String.class)
    public void execute(String message) {
        try {
            log.info("接收short url log; message={}", message);
            UrlShortenerLogMQ urlShortenerLogMQ = JSONObject.parseObject(message, UrlShortenerLogMQ.class);
            ShortUrlLogDO shortUrlLogDO = new ShortUrlLogDO();
            BaseUtil.copyProperties(urlShortenerLogMQ, shortUrlLogDO);
            urlShortLogSaveBatchService.saveUrlShortLog(shortUrlLogDO);
        } catch (Exception e) {
            log.warn("处理短链访问记录异常", e);
        }
    }

}
