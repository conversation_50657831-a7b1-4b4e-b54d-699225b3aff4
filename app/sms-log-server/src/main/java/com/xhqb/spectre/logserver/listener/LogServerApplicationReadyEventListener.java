package com.xhqb.spectre.logserver.listener;

import com.xhqb.spectre.logserver.service.CodeMappingService;
import com.xhqb.spectre.logserver.service.GatewayCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/18 1:57 下午
 */
@Component
@Slf4j
public class LogServerApplicationReadyEventListener implements ApplicationListener<ApplicationReadyEvent> {

    @Resource
    CodeMappingService codeMappingService;
    @Resource
    GatewayCacheService gatewayCacheService;

    /**
     * Handle an application event.
     * 跟随LogServerApplication启动
     *
     * @param event the event to respond to
     */
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        // 更新缓存
        codeMappingService.getCodeMapping();
        gatewayCacheService.getUsername();
        gatewayCacheService.getSession();
    }
}
