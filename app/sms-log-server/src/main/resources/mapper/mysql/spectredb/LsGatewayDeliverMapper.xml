<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.logserver.dal.LsGatewayDeliverMapper">
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.GatewayDeliverDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="submit_time" jdbcType="VARCHAR" property="submitTime" />
    <result column="done_time" jdbcType="VARCHAR" property="doneTime" />
    <result column="sms_status" jdbcType="VARCHAR" property="smsStatus" />
    <result column="dest_terminal_id" jdbcType="VARCHAR" property="destTerminalId" />
    <result column="message_status" jdbcType="TINYINT" property="messageStatus" />
    <result column="delivered_time" jdbcType="INTEGER" property="deliveredTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    msg_id, user_name, submit_time, done_time, sms_status, dest_terminal_id, message_status,
    delivered_time, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_gateway_deliver
    where msg_id = #{msgId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_gateway_deliver
    where msg_id = #{msgId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.GatewayDeliverDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_gateway_deliver (msg_id, user_name, submit_time, 
      done_time, sms_status, dest_terminal_id,
      message_status, delivered_time, create_time,
      update_time)
    values (#{msgId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{submitTime,jdbcType=VARCHAR}, 
      #{doneTime,jdbcType=VARCHAR}, #{smsStatus,jdbcType=VARCHAR}, #{destTerminalId,jdbcType=VARCHAR},
      #{messageStatus,jdbcType=TINYINT}, #{deliveredTime,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.GatewayDeliverDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_gateway_deliver
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="msgId != null">
        msg_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
      <if test="doneTime != null">
        done_time,
      </if>
      <if test="smsStatus != null">
        sms_status,
      </if>
      <if test="destTerminalId != null">
        dest_terminal_id,
      </if>
      <if test="messageStatus != null">
        message_status,
      </if>
      <if test="deliveredTime != null">
        delivered_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="msgId != null">
        #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=VARCHAR},
      </if>
      <if test="doneTime != null">
        #{doneTime,jdbcType=VARCHAR},
      </if>
      <if test="smsStatus != null">
        #{smsStatus,jdbcType=VARCHAR},
      </if>
      <if test="destTerminalId != null">
        #{destTerminalId,jdbcType=VARCHAR},
      </if>
      <if test="messageStatus != null">
        #{messageStatus,jdbcType=TINYINT},
      </if>
      <if test="deliveredTime != null">
        #{deliveredTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.GatewayDeliverDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_gateway_deliver
    <set>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        submit_time = #{submitTime,jdbcType=VARCHAR},
      </if>
      <if test="doneTime != null">
        done_time = #{doneTime,jdbcType=VARCHAR},
      </if>
      <if test="smsStatus != null">
        sms_status = #{smsStatus,jdbcType=VARCHAR},
      </if>
      <if test="destTerminalId != null">
        dest_terminal_id = #{destTerminalId,jdbcType=VARCHAR},
      </if>
      <if test="messageStatus != null">
        message_status = #{messageStatus,jdbcType=TINYINT},
      </if>
      <if test="deliveredTime != null">
        delivered_time = #{deliveredTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where msg_id = #{msgId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.GatewayDeliverDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_gateway_deliver
    set user_name = #{userName,jdbcType=VARCHAR},
      submit_time = #{submitTime,jdbcType=VARCHAR},
      done_time = #{doneTime,jdbcType=VARCHAR},
      sms_status = #{smsStatus,jdbcType=VARCHAR},
      dest_terminal_id = #{destTerminalId,jdbcType=VARCHAR},
      message_status = #{messageStatus,jdbcType=TINYINT},
      delivered_time = #{deliveredTime,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where msg_id = #{msgId,jdbcType=VARCHAR}
  </update>
  <insert id="insertOrUpdateBatch">
    insert into t_gateway_deliver(msg_id, user_name, submit_time, done_time, sms_status, dest_terminal_id, message_status, delivered_time)
    values
    <foreach collection="list" item="item" separator=",">
      (
      #{item.msgId,jdbcType=VARCHAR},
      #{item.userName,jdbcType=VARCHAR},
      #{item.submitTime,jdbcType=VARCHAR},
      #{item.doneTime,jdbcType=VARCHAR},
      #{item.smsStatus,jdbcType=VARCHAR},
      #{item.destTerminalId,jdbcType=VARCHAR},
      #{item.messageStatus,jdbcType=TINYINT},
      #{item.deliveredTime,jdbcType=INTEGER}
      )
    </foreach>
    ON DUPLICATE KEY UPDATE
    msg_id = values (msg_id),
    user_name = values (user_name),
    submit_time = values (submit_time),
    done_time = values (done_time),
    sms_status = values (sms_status),
    dest_terminal_id = values (dest_terminal_id),
    message_status = values (message_status),
    delivered_time = values (delivered_time)
  </insert>
</mapper>