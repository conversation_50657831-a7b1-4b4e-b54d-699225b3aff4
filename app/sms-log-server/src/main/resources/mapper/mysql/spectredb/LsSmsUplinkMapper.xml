<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.logserver.dal.LsSmsUplinkMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.SmsUplinkDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="channel_msg_id" jdbcType="VARCHAR" property="channelMsgId"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="recv_uplink_time" jdbcType="INTEGER" property="recvUplinkTime"/>
        <result column="dest_terminal_id" jdbcType="VARCHAR" property="destTerminalId"/>
        <result column="msg_length" jdbcType="INTEGER" property="msgLength"/>
        <result column="msg_content" jdbcType="VARCHAR" property="msgContent"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="channel_account_id" jdbcType="INTEGER" property="channelAccountId"/>
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode"/>
        <result column="sms_type_code" jdbcType="VARCHAR" property="smsTypeCode"/>
        <result column="cid" jdbcType="VARCHAR" property="cid"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, channel_msg_id, mobile, recv_uplink_time, dest_terminal_id, msg_length, msg_content,
        create_time, channel_account_id, channel_code, sms_type_code, cid
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from t_sms_uplink
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from t_sms_uplink
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.SmsUplinkDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_sms_uplink (id, channel_msg_id, mobile,
        recv_uplink_time, dest_terminal_id, msg_length,
        msg_content, create_time, channel_account_id, channel_code, sms_type_code)
        values (#{id,jdbcType=BIGINT}, #{channelMsgId,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR},
        #{recvUplinkTime,jdbcType=INTEGER}, #{destTerminalId,jdbcType=VARCHAR}, #{msgLength,jdbcType=INTEGER},
        #{msgContent,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{channelAccountId,jdbcType=INTEGER},
        #{channelCode,jdbcType=VARCHAR}, #{smsTypeCode,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.SmsUplinkDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_sms_uplink
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="channelMsgId != null">
                channel_msg_id,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="recvUplinkTime != null">
                recv_uplink_time,
            </if>
            <if test="destTerminalId != null">
                dest_terminal_id,
            </if>
            <if test="msgLength != null">
                msg_length,
            </if>
            <if test="msgContent != null">
                msg_content,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="channelAccountId != null">
                channel_account_id,
            </if>
            <if test="channelCode != null">
                channel_code,
            </if>
            <if test="smsTypeCode != null">
                smsType_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="channelMsgId != null">
                #{channelMsgId,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="recvUplinkTime != null">
                #{recvUplinkTime,jdbcType=INTEGER},
            </if>
            <if test="destTerminalId != null">
                #{destTerminalId,jdbcType=VARCHAR},
            </if>
            <if test="msgLength != null">
                #{msgLength,jdbcType=INTEGER},
            </if>
            <if test="msgContent != null">
                #{msgContent,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="channelAccountId != null">
                #{channelAccountId,jdbcType=INTEGER},
            </if>
            <if test="channelCode != null">
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                #{smsTypeCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.SmsUplinkDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_sms_uplink
        <set>
            <if test="channelMsgId != null">
                channel_msg_id = #{channelMsgId,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="recvUplinkTime != null">
                recv_uplink_time = #{recvUplinkTime,jdbcType=INTEGER},
            </if>
            <if test="destTerminalId != null">
                dest_terminal_id = #{destTerminalId,jdbcType=VARCHAR},
            </if>
            <if test="msgLength != null">
                msg_length = #{msgLength,jdbcType=INTEGER},
            </if>
            <if test="msgContent != null">
                msg_content = #{msgContent,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="channelAccountId != null">
                #{channelAccountId,jdbcType=INTEGER},
            </if>
            <if test="channelCode != null">
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="cid != null">
               cid = #{cid,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.SmsUplinkDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_sms_uplink
        set channel_msg_id = #{channelMsgId,jdbcType=VARCHAR},
        mobile = #{mobile,jdbcType=VARCHAR},
        recv_uplink_time = #{recvUplinkTime,jdbcType=INTEGER},
        dest_terminal_id = #{destTerminalId,jdbcType=VARCHAR},
        msg_length = #{msgLength,jdbcType=INTEGER},
        msg_content = #{msgContent,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        channel_account_id = #{channelAccountId,jdbcType=INTEGER},
        channel_code = #{channelCode,jdbcType=VARCHAR},
        sms_type_code = #{smsTypeCode,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.SmsUplinkQuery"
            resultType="java.lang.Integer">
        select count(*)
        from t_sms_uplink
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="orderId != null">
                and channel_msg_id = #{orderId}
            </if>
            <if test="mobile != null">
                and mobile = #{mobile}
            </if>
            <if test="channelAccountId != null">
                and channel_account_id = #{channelAccountId}
            </if>
            <if test="channelCode != null">
                and channel_code = #{channelCode}
            </if>
            <if test="smsTypeCode != null">
                and sms_type_code = #{smsTypeCode}
            </if>
            <if test="msgContent != null">
                and msg_content like concat('%', #{msgContent}, '%')
            </if>
        </where>
    </select>

    <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.SmsUplinkQuery"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_sms_uplink
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="orderId != null">
                and channel_msg_id = #{orderId}
            </if>
            <if test="mobile != null">
                and mobile = #{mobile}
            </if>
            <if test="channelAccountId != null">
                and channel_account_id = #{channelAccountId}
            </if>
            <if test="channelCode != null">
                and channel_code = #{channelCode}
            </if>
            <if test="smsTypeCode != null">
                and sms_type_code = #{smsTypeCode}
            </if>
            <if test="msgContent != null">
                and msg_content like concat('%', #{msgContent}, '%')
            </if>
        </where>
        order by create_time desc
        <if test="pageParameter != null">
            limit #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>

    <insert id="insertBatch">
        insert into t_sms_uplink(
        channel_msg_id, mobile, recv_uplink_time, dest_terminal_id, msg_length, msg_content,
        channel_account_id, channel_code, sms_type_code
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.channelMsgId,jdbcType=VARCHAR},
            #{item.mobile,jdbcType=VARCHAR},
            #{item.recvUplinkTime,jdbcType=INTEGER},
            #{item.destTerminalId,jdbcType=VARCHAR},
            #{item.msgLength,jdbcType=INTEGER},
            #{item.msgContent,jdbcType=VARCHAR},
            #{item.channelAccountId,jdbcType=INTEGER},
            #{item.channelCode,jdbcType=VARCHAR},
            #{item.smsTypeCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="scanUplink" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_sms_uplink
        where id  <![CDATA[ > ]]> #{lastId}
        and cid = ''
        order by id
        limit #{pageSize}
    </select>
    <select id="selectByMarket" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
       from t_sms_uplink
        where id <![CDATA[>]]> #{lastSmsUplinkId}
        and sms_type_code='market'
       order by id
       limit #{pageSize}
    </select>



</mapper>