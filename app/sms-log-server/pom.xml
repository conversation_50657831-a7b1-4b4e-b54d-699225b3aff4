<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>app</artifactId>
        <groupId>com.xhqb.spectre</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>spectre-logserver</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-infra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-mybatis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.zaxxer</groupId>
                    <artifactId>HikariCP</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-druid</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-venus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xhqb</groupId>
            <artifactId>kael-mq-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-sequence</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.13.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.xhqb.spectre</groupId>
            <artifactId>common</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.xhqb.spectre</groupId>-->
<!--            <artifactId>channel</artifactId>-->
<!--            <version>${project.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-starter-integration</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.integration</groupId>-->
<!--                    <artifactId>spring-integration-redis</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!--埋点-->
        <dependency>
            <groupId>com.xhqb.kael</groupId>
            <artifactId>kael-starter-lucifer</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--udp-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-integration</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-ip</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>

        <dependency>
            <groupId>com.xhqb.kael.mybatis</groupId>
            <artifactId>kael-mybatis-plugin-starter</artifactId>
        </dependency>
    </dependencies>

</project>