<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>spectre-parent</artifactId>
        <groupId>com.xhqb.spectre</groupId>
        <version>${revision}</version>
    </parent>

    <artifactId>dalgen</artifactId>
    <version>${revision}</version>

    <dependencies>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>3.2.9.RELEASE</version>
        </dependency>
    </dependencies>
    <properties>
        <target.basedir>${project.basedir}</target.basedir>
        <dal.src.dir>${target.basedir}/src/main/java</dal.src.dir>
        <dal.mapperxml.dir>${target.basedir}/src/main/resources</dal.mapperxml.dir>

        <!--<dal.resource.dir>${target.basedir}/src/main/resources</dal.resource.dir>-->
    </properties>

    <build>
        <plugins>
            <plugin>

                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                    <!--<outputDirectory>${targetProject}</outputDirectory>-->
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>5.1.36</version>
                    </dependency>
                    <!--<dependency>-->
                    <!--<groupId>org.springframework</groupId>-->
                    <!--<artifactId>spring-context</artifactId>-->
                    <!--<version>3.2.9.RELEASE</version>-->
                    <!--</dependency>-->
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>