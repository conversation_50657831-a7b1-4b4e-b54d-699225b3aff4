<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <properties resource="dal.properties"/>
    <context id="DevTables" targetRuntime="MyBatis3">
        <commentGenerator>
            <property name="suppressDate" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="${jdbc.driver}"
                        connectionURL="${jdbc.url}" userId="${jdbc.username}" password="${jdbc.password}">
        </jdbcConnection>


        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.xhqb.spectre.common.dal.entity" targetProject="${dal.src.dir}">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mapper" targetProject="${dal.mapperxml.dir}">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.xhqb.spectre.common.dal.mapper"
                             targetProject="${dal.src.dir}">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!--        <table schema="spectredb" tableName="t_app" domainObjectName="App"-->
        <!--               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false">-->
        <!--            <columnOverride column="is_delete" property="isDelete" javaType="java.lang.Integer"/>-->
        <!--        </table>-->
        <!--        <table schema="spectredb" tableName="t_channel" domainObjectName="Channel"-->
        <!--               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"/>-->
        <!--        <table schema="spectredb" tableName="t_channel_account" domainObjectName="ChannelAccount"-->
        <!--               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"/>-->
        <!--        <table schema="spectredb" tableName="t_sign" domainObjectName="Sign"-->
        <!--               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"/>-->
        <!--        <table schema="spectredb" tableName="t_sign_channel" domainObjectName="SignChannel"-->
        <!--               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"/>-->
        <!--        <table schema="spectredb" tableName="t_sms_type" domainObjectName="SmsType"-->
        <!--               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"/>-->
        <!--        <table schema="spectredb" tableName="t_tpl" domainObjectName="Tpl"-->
        <!--               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"/>-->
<!--        <table schema="spectredb" tableName="t_sms_order" domainObjectName="SmsOrder"-->
<!--               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
<!--               enableUpdateByExample="false"/>-->
        <table schema="spectredb" tableName="t_channel_group_item" domainObjectName="ChannelGroupItem"
               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               enableUpdateByExample="false"/>
        <table schema="spectredb" tableName="t_channel_group" domainObjectName="ChannelGroup"
               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               enableUpdateByExample="false"/>
        <!--        <table schema="spectredb" tableName="t_op_log" domainObjectName="OpLog"-->
        <!--               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"/>-->
        <!--        <table schema="spectredb" tableName="t_batch_task" domainObjectName="BatchTask"-->
        <!--               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"/>-->
        <!--        <table schema="spectredb" tableName="t_batch_task_param" domainObjectName="BatchTaskParam"-->
        <!--               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"/>-->
        <!--        <table schema="spectredb" tableName="t_batch_task_report" domainObjectName="BatchTaskReport"-->
        <!--               enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"/>-->
    </context>
</generatorConfiguration>
