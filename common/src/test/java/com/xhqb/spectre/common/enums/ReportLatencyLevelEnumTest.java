package com.xhqb.spectre.common.enums;

import org.junit.Test;

import static org.junit.Assert.*;

/**
 * ReportLatencyLevelEnum 测试类
 */
public class ReportLatencyLevelEnumTest {

    @Test
    public void testFromCostTime_NegativeValue_ReturnsNull() {
        // 测试负数时间返回null
        assertNull(ReportLatencyLevelEnum.fromCostTime(-1));
        assertNull(ReportLatencyLevelEnum.fromCostTime(-100));
    }

    @Test
    public void testFromCostTime_ZeroToFiveSeconds_ReturnsLevel1() {
        // 测试0-5秒范围返回LEVEL_1 (注意5秒不包含在内)
        assertEquals(ReportLatencyLevelEnum.LEVEL_1, ReportLatencyLevelEnum.fromCostTime(0));
        assertEquals(ReportLatencyLevelEnum.LEVEL_1, ReportLatencyLevelEnum.fromCostTime(2));
        assertEquals(ReportLatencyLevelEnum.LEVEL_1, ReportLatencyLevelEnum.fromCostTime(4));
        // 5秒应该属于LEVEL_2
    }

    @Test
    public void testFromCostTime_FiveToTenSeconds_ReturnsLevel2() {
        // 测试5-10秒范围返回LEVEL_2 (注意10秒不包含在内)
        assertEquals(ReportLatencyLevelEnum.LEVEL_2, ReportLatencyLevelEnum.fromCostTime(5));
        assertEquals(ReportLatencyLevelEnum.LEVEL_2, ReportLatencyLevelEnum.fromCostTime(7));
        assertEquals(ReportLatencyLevelEnum.LEVEL_2, ReportLatencyLevelEnum.fromCostTime(9));
        // 10秒应该属于LEVEL_3
    }

    @Test
    public void testFromCostTime_TenToTwentySeconds_ReturnsLevel3() {
        // 测试10-20秒范围返回LEVEL_3 (注意20秒不包含在内)
        assertEquals(ReportLatencyLevelEnum.LEVEL_3, ReportLatencyLevelEnum.fromCostTime(10));
        assertEquals(ReportLatencyLevelEnum.LEVEL_3, ReportLatencyLevelEnum.fromCostTime(15));
        assertEquals(ReportLatencyLevelEnum.LEVEL_3, ReportLatencyLevelEnum.fromCostTime(19));
        // 20秒应该属于LEVEL_4
    }

    @Test
    public void testFromCostTime_TwentyToThirtySeconds_ReturnsLevel4() {
        // 测试20-30秒范围返回LEVEL_4 (注意30秒不包含在内)
        assertEquals(ReportLatencyLevelEnum.LEVEL_4, ReportLatencyLevelEnum.fromCostTime(20));
        assertEquals(ReportLatencyLevelEnum.LEVEL_4, ReportLatencyLevelEnum.fromCostTime(25));
        assertEquals(ReportLatencyLevelEnum.LEVEL_4, ReportLatencyLevelEnum.fromCostTime(29));
        // 30秒应该属于LEVEL_5
    }

    @Test
    public void testFromCostTime_ThirtyToSixtySeconds_ReturnsLevel5() {
        // 测试30-60秒范围返回LEVEL_5 (注意60秒不包含在内)
        assertEquals(ReportLatencyLevelEnum.LEVEL_5, ReportLatencyLevelEnum.fromCostTime(30));
        assertEquals(ReportLatencyLevelEnum.LEVEL_5, ReportLatencyLevelEnum.fromCostTime(45));
        assertEquals(ReportLatencyLevelEnum.LEVEL_5, ReportLatencyLevelEnum.fromCostTime(59));
        // 60秒应该属于LEVEL_6
    }

    @Test
    public void testFromCostTime_OneToFiveMinutes_ReturnsLevel6() {
        // 测试1-5分钟(60-300秒)范围返回LEVEL_6 (注意300秒不包含在内)
        assertEquals(ReportLatencyLevelEnum.LEVEL_6, ReportLatencyLevelEnum.fromCostTime(60));
        assertEquals(ReportLatencyLevelEnum.LEVEL_6, ReportLatencyLevelEnum.fromCostTime(180));
        assertEquals(ReportLatencyLevelEnum.LEVEL_6, ReportLatencyLevelEnum.fromCostTime(299));
        // 300秒应该属于LEVEL_7
    }

    @Test
    public void testFromCostTime_FiveToThirtyMinutes_ReturnsLevel7() {
        // 测试5-30分钟(300-1800秒)范围返回LEVEL_7 (注意1800秒不包含在内)
        assertEquals(ReportLatencyLevelEnum.LEVEL_7, ReportLatencyLevelEnum.fromCostTime(300));
        assertEquals(ReportLatencyLevelEnum.LEVEL_7, ReportLatencyLevelEnum.fromCostTime(600));
        assertEquals(ReportLatencyLevelEnum.LEVEL_7, ReportLatencyLevelEnum.fromCostTime(1799));
        // 1800秒应该属于LEVEL_8
    }

    @Test
    public void testFromCostTime_ThirtyToSixtyMinutes_ReturnsLevel8() {
        // 测试30-60分钟(1800-3600秒)范围返回LEVEL_8 (注意3600秒不包含在内)
        assertEquals(ReportLatencyLevelEnum.LEVEL_8, ReportLatencyLevelEnum.fromCostTime(1800));
        assertEquals(ReportLatencyLevelEnum.LEVEL_8, ReportLatencyLevelEnum.fromCostTime(2700));
        assertEquals(ReportLatencyLevelEnum.LEVEL_8, ReportLatencyLevelEnum.fromCostTime(3599));
        // 3600秒应该属于LEVEL_9
    }

    @Test
    public void testFromCostTime_MoreThanSixtyMinutes_ReturnsLevel9() {
        // 测试大于60分钟(>3600秒)范围返回LEVEL_9
        assertEquals(ReportLatencyLevelEnum.LEVEL_9, ReportLatencyLevelEnum.fromCostTime(3600));
        assertEquals(ReportLatencyLevelEnum.LEVEL_9, ReportLatencyLevelEnum.fromCostTime(7200));
        assertEquals(ReportLatencyLevelEnum.LEVEL_9, ReportLatencyLevelEnum.fromCostTime(100000));
    }

    @Test
    public void testGetValue() {
        // 测试获取等级值
        assertEquals(1, ReportLatencyLevelEnum.LEVEL_1.getValue());
        assertEquals(2, ReportLatencyLevelEnum.LEVEL_2.getValue());
        assertEquals(3, ReportLatencyLevelEnum.LEVEL_3.getValue());
        assertEquals(4, ReportLatencyLevelEnum.LEVEL_4.getValue());
        assertEquals(5, ReportLatencyLevelEnum.LEVEL_5.getValue());
        assertEquals(6, ReportLatencyLevelEnum.LEVEL_6.getValue());
        assertEquals(7, ReportLatencyLevelEnum.LEVEL_7.getValue());
        assertEquals(8, ReportLatencyLevelEnum.LEVEL_8.getValue());
        assertEquals(9, ReportLatencyLevelEnum.LEVEL_9.getValue());
    }

    @Test
    public void testGetDescription() {
        // 测试获取描述信息
        assertEquals("0~5秒", ReportLatencyLevelEnum.LEVEL_1.getDescription());
        assertEquals("5~10秒", ReportLatencyLevelEnum.LEVEL_2.getDescription());
        assertEquals("10~20秒", ReportLatencyLevelEnum.LEVEL_3.getDescription());
        assertEquals("20~30秒", ReportLatencyLevelEnum.LEVEL_4.getDescription());
        assertEquals("30~60秒", ReportLatencyLevelEnum.LEVEL_5.getDescription());
        assertEquals("1~5分钟", ReportLatencyLevelEnum.LEVEL_6.getDescription());
        assertEquals("5~30分钟", ReportLatencyLevelEnum.LEVEL_7.getDescription());
        assertEquals("30~60分钟", ReportLatencyLevelEnum.LEVEL_8.getDescription());
        assertEquals("大于60分钟", ReportLatencyLevelEnum.LEVEL_9.getDescription());
    }

    @Test
    public void testMatches() {
        // 测试matches方法
        assertTrue(ReportLatencyLevelEnum.LEVEL_1.matches(0));
        assertTrue(ReportLatencyLevelEnum.LEVEL_1.matches(4));
        assertFalse(ReportLatencyLevelEnum.LEVEL_1.matches(5));
        assertFalse(ReportLatencyLevelEnum.LEVEL_1.matches(10));

        assertTrue(ReportLatencyLevelEnum.LEVEL_5.matches(35));
        assertFalse(ReportLatencyLevelEnum.LEVEL_5.matches(29));
        assertFalse(ReportLatencyLevelEnum.LEVEL_5.matches(60));
    }

    @Test
    public void testGetRange() {
        // 测试getRange方法
        assertEquals("0~5秒", ReportLatencyLevelEnum.LEVEL_1.getRange());
        assertEquals("5~10秒", ReportLatencyLevelEnum.LEVEL_2.getRange());
        assertEquals("10~20秒", ReportLatencyLevelEnum.LEVEL_3.getRange());
        assertEquals("20~30秒", ReportLatencyLevelEnum.LEVEL_4.getRange());
        assertEquals("30~60秒", ReportLatencyLevelEnum.LEVEL_5.getRange());
        assertEquals("1~5分钟", ReportLatencyLevelEnum.LEVEL_6.getRange());
        assertEquals("5~30分钟", ReportLatencyLevelEnum.LEVEL_7.getRange());
        assertEquals("30~60分钟", ReportLatencyLevelEnum.LEVEL_8.getRange());
        assertEquals("大于60分钟", ReportLatencyLevelEnum.LEVEL_9.getRange());
    }

    @Test
    public void testFromTimestamps_NullOrZeroValues_ReturnsNull() {
        // 测试null或0值返回null
        assertNull(ReportLatencyLevelEnum.fromTimestamps(null, 100L));
        assertNull(ReportLatencyLevelEnum.fromTimestamps(100L, null));
        assertNull(ReportLatencyLevelEnum.fromTimestamps(0L, 100L));
        assertNull(ReportLatencyLevelEnum.fromTimestamps(100L, 0L));
    }

    @Test
    public void testFromTimestamps_ReportTimeBeforeSubmitTime_ReturnsNull() {
        // 测试回执时间早于提交时间返回null
        assertNull(ReportLatencyLevelEnum.fromTimestamps(100L, 99L));
    }

    @Test
    public void testFromTimestamps_ValidValues_ReturnsCorrectLevel() {
        // 测试有效值返回正确的等级
        assertEquals(ReportLatencyLevelEnum.LEVEL_1, ReportLatencyLevelEnum.fromTimestamps(100L, 104L));
        assertEquals(ReportLatencyLevelEnum.LEVEL_2, ReportLatencyLevelEnum.fromTimestamps(100L, 109L));
        assertEquals(ReportLatencyLevelEnum.LEVEL_3, ReportLatencyLevelEnum.fromTimestamps(100L, 119L));
        assertEquals(ReportLatencyLevelEnum.LEVEL_4, ReportLatencyLevelEnum.fromTimestamps(100L, 129L));
        assertEquals(ReportLatencyLevelEnum.LEVEL_5, ReportLatencyLevelEnum.fromTimestamps(100L, 159L));
        assertEquals(ReportLatencyLevelEnum.LEVEL_6, ReportLatencyLevelEnum.fromTimestamps(100L, 399L));
        assertEquals(ReportLatencyLevelEnum.LEVEL_7, ReportLatencyLevelEnum.fromTimestamps(100L, 1899L));
        assertEquals(ReportLatencyLevelEnum.LEVEL_8, ReportLatencyLevelEnum.fromTimestamps(100L, 3699L));
        assertEquals(ReportLatencyLevelEnum.LEVEL_9, ReportLatencyLevelEnum.fromTimestamps(100L, 37000L));
    }
}