<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.BatchTaskParamMapper">
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.BatchTaskParamDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_id" jdbcType="INTEGER" property="taskId" />
    <result column="send_status" jdbcType="TINYINT" property="sendStatus" />
    <result column="file_md5" jdbcType="VARCHAR" property="fileMd5" />
    <result column="start_offset" property="startOffset" />
    <result column="end_offset" property="endOffset" />
    <result column="is_delete" property="isDelete" jdbcType="TINYINT" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.xhqb.spectre.common.dal.entity.BatchTaskParamDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="param_json_array" jdbcType="LONGVARCHAR" property="paramJsonArray" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, task_id, send_status, file_md5, start_offset, end_offset, is_delete, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    param_json_array
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_batch_task_param
    where id = #{id,jdbcType=INTEGER} and is_delete = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_batch_task_param
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskParamDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_task_param (id, task_id, send_status,
      file_md5, start_offset, end_offset, is_delete, create_time, update_time,
      param_json_array)
    values (#{id,jdbcType=INTEGER}, #{taskId,jdbcType=INTEGER}, #{sendStatus,jdbcType=TINYINT},
      #{fileMd5,jdbcType=VARCHAR}, #{startOffset}, #{endOffset}, #{isDelete,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{paramJsonArray,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskParamDO" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_task_param
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="sendStatus != null">
        send_status,
      </if>
      <if test="fileMd5 != null">
        file_md5,
      </if>
      <if test="startOffset != null">
        start_offset,
      </if>
      <if test="endOffset != null">
        end_offset,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="paramJsonArray != null">
        param_json_array,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=INTEGER},
      </if>
      <if test="sendStatus != null">
        #{sendStatus,jdbcType=TINYINT},
      </if>
      <if test="fileMd5 != null">
        #{fileMd5,jdbcType=VARCHAR},
      </if>
      <if test="startOffset != null">
        #{startOffset},
      </if>
      <if test="endOffset != null">
        #{endOffset},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paramJsonArray != null">
        #{paramJsonArray,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskParamDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_task_param
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=INTEGER},
      </if>
      <if test="sendStatus != null">
        send_status = #{sendStatus,jdbcType=TINYINT},
      </if>
      <if test="fileMd5 != null">
        file_md5 = #{fileMd5,jdbcType=VARCHAR},
      </if>
      <if test="startOffset != null">
        start_offset = #{startOffset},
      </if>
      <if test="endOffset != null">
        end_offset = #{endOffset},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paramJsonArray != null">
        param_json_array = #{paramJsonArray,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskParamDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_task_param
    set task_id = #{taskId,jdbcType=INTEGER},
      send_status = #{sendStatus,jdbcType=TINYINT},
      file_md5 = #{fileMd5,jdbcType=VARCHAR},
      start_offset = #{startOffset},
      end_offset = #{endOffset},
      is_delete = #{isDelete,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      param_json_array = #{paramJsonArray,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskParamDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_task_param
    set task_id = #{taskId,jdbcType=INTEGER},
      send_status = #{sendStatus,jdbcType=TINYINT},
      file_md5 = #{fileMd5,jdbcType=VARCHAR},
      start_offset = #{startOffset},
      end_offset = #{endOffset},
      is_delete = #{isDelete,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
    insert into t_batch_task_param (task_id,file_md5,start_offset,end_offset,param_json_array)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.taskId,jdbcType=INTEGER},#{item.fileMd5,jdbcType=VARCHAR},#{item.startOffset},#{item.endOffset},#{item.paramJsonArray,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>

  <select id="selectByTaskId" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_batch_task_param
    where task_id = #{taskId,jdbcType=INTEGER}  and is_delete = 0
  </select>

  <update id="batchDeleteById">
    update t_batch_task_param
    set is_delete = 1
    where id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>

  <update id="deleteByTaskId">
    update t_batch_task_param
    set is_delete = 1
    where task_id = #{taskId}
  </update>

  <select id="selectByTaskIdAndStatus" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    <if test="needMapping != null">
      ,
      <include refid="Blob_Column_List" />
    </if>
    from t_batch_task_param
    where task_id = #{taskId,jdbcType=INTEGER}
        and send_status = #{sendStatus}
        and is_delete = 0
  </select>

  <select id="selectByIdList"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_batch_task_param
    where id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and is_delete = 0
  </select>

  <update id="updateTaskIdByIdList">
    update t_batch_task_param
    set task_id = #{taskId,jdbcType=INTEGER}
    where id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>

  <update id="updateSendStatusById">
    update t_batch_task_param
    set send_status = #{destStatus}
    where id = #{id} and send_status = #{sourceStatus}
  </update>

  <select id="selectByTaskIdAndMapping"  resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    <if test="needMapping != null">
      ,
      <include refid="Blob_Column_List" />
    </if>
    from t_batch_task_param
    where task_id = #{taskId,jdbcType=INTEGER}
  </select>

  <select id="selectByIdListAndMapping"  resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_batch_task_param
    where id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <update id="batchUpdateTaskIdAndMapping">
    update t_batch_task_param
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="param_json_array =case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          <if test="item.paramJsonArray!=null">
            when id=#{item.id} then #{item.paramJsonArray}
          </if>
        </foreach>
      </trim>
      <trim prefix="task_id =case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          <if test="item.taskId!=null">
            when id=#{item.id} then #{item.taskId}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item.id}
    </foreach>
  </update>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.BatchTaskParamQuery" resultType="java.lang.Integer">
    select count(*)
    from t_batch_task_param
    <where>
      <if test="taskId != null">
        and task_id = #{taskId}
      </if>
      <if test="sendStatus != null">
        and send_status in
        <foreach collection="sendStatus" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="fileMd5 != null">
        and file_md5 = #{fileMd5}
      </if>
      <if test="startOffset != null">
        and start_offset = #{startOffset}
      </if>
      <if test="endOffset != null">
        and end_offset = #{endOffset}
      </if>
      and is_delete = #{isDelete}
      and task_id != 0
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.BatchTaskParamQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_batch_task_param
    <where>
      <if test="taskId != null">
        and task_id = #{taskId}
      </if>
      <if test="sendStatus != null">
        and send_status in
        <foreach collection="sendStatus" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="fileMd5 != null">
        and file_md5 = #{fileMd5}
      </if>
      <if test="startOffset != null">
        and start_offset = #{startOffset}
      </if>
      <if test="endOffset != null">
        and end_offset = #{endOffset}
      </if>
      and is_delete = #{isDelete}
      and task_id != 0
    </where>
    order by update_time desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>

  <select id="fetchAbDataList"  resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_batch_task_param
    where task_id = #{taskId,jdbcType=INTEGER}
    and send_status = 0
    and is_delete = 0
    order by id asc
    limit #{pageSize}
  </select>

</mapper>