<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.BatchTaskReportMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.BatchTaskReportDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="task_id" property="taskId" jdbcType="INTEGER" />
    <result column="task_param_id" property="taskParamId" jdbcType="INTEGER" />
    <result column="cost" property="cost" jdbcType="INTEGER" />
    <result column="total" property="total" jdbcType="INTEGER" />
    <result column="success" property="success" jdbcType="INTEGER" />
    <result column="fail" property="fail" jdbcType="INTEGER" />
    <result column="unknown" property="unknown" jdbcType="INTEGER" />
    <result column="time_stat" property="timeStat" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.xhqb.spectre.common.dal.entity.BatchTaskReportDO" extends="BaseResultMap" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="error_stat" property="errorStat" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, task_id, task_param_id, cost, total, success, fail, unknown, time_stat, create_time,
    update_time
  </sql>
  <sql id="Blob_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    error_stat
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_batch_task_report
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_batch_task_report
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskReportDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_task_report (id, task_id, task_param_id,
    cost, total, success,
    fail, unknown, time_stat,
    create_time, update_time, error_stat
    )
    values (#{id,jdbcType=INTEGER}, #{taskId,jdbcType=INTEGER}, #{taskParamId,jdbcType=INTEGER},
    #{cost,jdbcType=INTEGER}, #{total,jdbcType=INTEGER}, #{success,jdbcType=INTEGER},
    #{fail,jdbcType=INTEGER}, #{unknown,jdbcType=INTEGER}, #{timeStat,jdbcType=VARCHAR},
    #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{errorStat,jdbcType=LONGVARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskReportDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_task_report
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="taskId != null" >
        task_id,
      </if>
      <if test="taskParamId != null" >
        task_param_id,
      </if>
      <if test="cost != null" >
        cost,
      </if>
      <if test="total != null" >
        total,
      </if>
      <if test="success != null" >
        success,
      </if>
      <if test="fail != null" >
        fail,
      </if>
      <if test="unknown != null" >
        unknown,
      </if>
      <if test="timeStat != null" >
        time_stat,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="errorStat != null" >
        error_stat,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="taskId != null" >
        #{taskId,jdbcType=INTEGER},
      </if>
      <if test="taskParamId != null" >
        #{taskParamId,jdbcType=INTEGER},
      </if>
      <if test="cost != null" >
        #{cost,jdbcType=INTEGER},
      </if>
      <if test="total != null" >
        #{total,jdbcType=INTEGER},
      </if>
      <if test="success != null" >
        #{success,jdbcType=INTEGER},
      </if>
      <if test="fail != null" >
        #{fail,jdbcType=INTEGER},
      </if>
      <if test="unknown != null" >
        #{unknown,jdbcType=INTEGER},
      </if>
      <if test="timeStat != null" >
        #{timeStat,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorStat != null" >
        #{errorStat,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskReportDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_task_report
    <set >
      <if test="taskId != null" >
        task_id = #{taskId,jdbcType=INTEGER},
      </if>
      <if test="taskParamId != null" >
        task_param_id = #{taskParamId,jdbcType=INTEGER},
      </if>
      <if test="cost != null" >
        cost = #{cost,jdbcType=INTEGER},
      </if>
      <if test="total != null" >
        total = #{total,jdbcType=INTEGER},
      </if>
      <if test="success != null" >
        success = #{success,jdbcType=INTEGER},
      </if>
      <if test="fail != null" >
        fail = #{fail,jdbcType=INTEGER},
      </if>
      <if test="unknown != null" >
        unknown = #{unknown,jdbcType=INTEGER},
      </if>
      <if test="timeStat != null" >
        time_stat = #{timeStat,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorStat != null" >
        error_stat = #{errorStat,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskReportDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_task_report
    set task_id = #{taskId,jdbcType=INTEGER},
    task_param_id = #{taskParamId,jdbcType=INTEGER},
    cost = #{cost,jdbcType=INTEGER},
    total = #{total,jdbcType=INTEGER},
    success = #{success,jdbcType=INTEGER},
    fail = #{fail,jdbcType=INTEGER},
    unknown = #{unknown,jdbcType=INTEGER},
    time_stat = #{timeStat,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    error_stat = #{errorStat,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskReportDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_task_report
    set task_id = #{taskId,jdbcType=INTEGER},
    task_param_id = #{taskParamId,jdbcType=INTEGER},
    cost = #{cost,jdbcType=INTEGER},
    total = #{total,jdbcType=INTEGER},
    success = #{success,jdbcType=INTEGER},
    fail = #{fail,jdbcType=INTEGER},
    unknown = #{unknown,jdbcType=INTEGER},
    time_stat = #{timeStat,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByTaskIdAndParamId" resultMap="ResultMapWithBLOBs" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_batch_task_report
    <where>
      <if test="taskId != null">
        and task_id = #{taskId}
      </if>
      <if test="taskParamId != null">
        and task_param_id = #{taskParamId}
      </if>
    </where>
    <if test="taskId == null and taskParamId == null">
      limit 100
    </if>
  </select>

  <select id="selectByTaskId" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from t_batch_task_report
    <where>
      task_id = #{taskId}
    </where>
  </select>
</mapper>