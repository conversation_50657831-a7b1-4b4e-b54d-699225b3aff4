<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TestContentTaskRecordMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.test.tool.TestContentTaskRecordDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="mobile_enc" jdbcType="VARCHAR" property="mobile"/>
        <result column="sms_sign_id" jdbcType="INTEGER" property="smsSignId"/>
        <result column="sms_tpl_code" jdbcType="VARCHAR" property="smsTplCode"/>
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="send_status" jdbcType="TINYINT" property="sendStatus"/>
        <result column="report_status" jdbcType="TINYINT" property="reportStatus"/>
        <result column="app_report_status" jdbcType="TINYINT" property="appReportStatus"/>
        <result column="app_report_time" jdbcType="INTEGER" property="appReportTime"/>
        <result column="app_report_extend" jdbcType="VARCHAR" property="appReportExtend"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="tpl_code" jdbcType="VARCHAR" property="tplCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,
        task_id, content, mobile_enc, sms_sign_id, sms_tpl_code, request_id, send_status,
        report_status, app_report_status, app_report_time, app_report_extend, create_time, update_time,
        brand,tpl_code
    </sql>
    <insert id="insertBySelective">
        insert into t_test_content_task_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="content != null">content,</if>
            <if test="mobile != null">mobile_enc,</if>
            <if test="smsSignId != null">sms_sign_id,</if>
            <if test="smsTplCode != null">sms_tpl_code,</if>
            <if test="requestId != null">request_id,</if>
            <if test="sendStatus != null">send_status,</if>
            <if test="reportStatus != null">report_status,</if>
            <if test="appReportStatus != null">app_report_status,</if>
            <if test="appReportTime != null">app_report_time,</if>
            <if test="appReportExtend != null">app_report_extend,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="brand != null">brand,</if>
            <if test="tplCode != null and tplCode != ''">tpl_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="content != null">#{content},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="smsSignId != null">#{smsSignId},</if>
            <if test="smsTplCode != null">#{smsTplCode},</if>
            <if test="requestId != null">#{requestId},</if>
            <if test="sendStatus != null">#{sendStatus},</if>
            <if test="reportStatus != null">#{reportStatus},</if>
            <if test="appReportStatus != null">#{appReportStatus},</if>
            <if test="appReportTime != null">#{appReportTime},</if>
            <if test="appReportExtend != null">#{appReportExtend},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="brand != null">#{brand},</if>
            <if test="tplCode != null and tplCode != ''">#{tplCode},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective">
        update t_test_content_task_record
        <set>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="mobile != null">mobile_enc = #{mobile},</if>
            <if test="smsSignId != null">sms_sign_id = #{smsSignId},</if>
            <if test="smsTplCode != null">sms_tpl_code = #{smsTplCode},</if>
            <if test="requestId != null">request_id = #{requestId},</if>
            <if test="sendStatus != null">send_status = #{sendStatus},</if>
            <if test="reportStatus != null">report_status = #{reportStatus},</if>
            <if test="appReportStatus != null">app_report_status = #{appReportStatus},</if>
            <if test="appReportTime != null">app_report_time = #{appReportTime},</if>
            <if test="appReportExtend != null">app_report_extend = #{appReportExtend},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="brand != null">brand = #{brand},</if>
        </set>
        where id = #{id}
    </update>

    <update id="updateAppReportStatusByTaskId">
        update t_test_content_task_record
        set app_report_status = 2,
            update_time       = now()
        where task_id = #{taskId}
          and app_report_status = 0
          and report_status = 0
          and send_status = 0
    </update>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_content_task_record
        where id = #{id}
    </select>
    <select id="selectByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_content_task_record
        where task_id = #{taskId}
    </select>
    <select id="selectByContent" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_content_task_record
        where mobile_enc = #{mobile}
        and content = #{content}
        and app_report_status = 0
        order by id desc
        limit 1;
    </select>
    <select id="selectByTaskIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        t_test_content_task_record
        where task_id in
        <foreach collection="taskIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="statByTaskIds" resultType="com.xhqb.spectre.common.dal.entity.TestContentStatDO">
        select
        tpl_code as 'tplCode',
        content,
        count(1) as 'sendCount',
        sum(if(report_status = 0, 1, 0)) as 'reportCount',
        sum(if(app_report_status = 1, 1, 0)) as 'reportSuccessCount'
        from t_test_content_task_record
        <where>
            <if test="taskIdList != null and taskIdList.size() > 0">
                and task_id in
                <foreach item="item" collection="taskIdList" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="tplCode != null">
                and tpl_code = #{tplCode}
            </if>
            <if test="startTime != null">
                and create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and create_time &lt;= #{endTime}
            </if>
        </where>
        group by tpl_code, content
    </select>
</mapper>