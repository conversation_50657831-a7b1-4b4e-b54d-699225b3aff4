<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.WxUrlLinkMapper">

    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.WxUrlLinkDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="linkDesc" column="link_desc" jdbcType="VARCHAR"/>
            <result property="shortUrlId" column="short_url_id" jdbcType="INTEGER"/>
            <result property="shortCode" column="short_code" jdbcType="VARCHAR"/>
            <result property="shortUrl" column="short_url" jdbcType="VARCHAR"/>
            <result property="urlLink" column="url_link" jdbcType="VARCHAR"/>
            <result property="appid" column="appid" jdbcType="VARCHAR"/>
            <result property="envVersion" column="env_version" jdbcType="VARCHAR"/>
            <result property="path" column="path" jdbcType="VARCHAR"/>
            <result property="query" column="query" jdbcType="VARCHAR"/>
            <result property="generateDate" column="generate_date" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,link_desc,short_url_id,short_code,
        short_url,url_link,appid,
        env_version,path,query,
        generate_date,status,creator,
        create_time,updater,update_time,
        is_delete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_wx_url_link
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_wx_url_link
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhqb.spectre.common.dal.entity.WxUrlLinkDO" useGeneratedKeys="true">
        insert into t_wx_url_link
        ( id,link_desc,short_url_id,short_code
        ,short_url,url_link,appid
        ,env_version,path,query
        ,generate_date,status,creator
        ,create_time,updater,update_time
        ,is_delete)
        values (#{id,jdbcType=BIGINT},#{linkDesc,jdbcType=VARCHAR},#{shortUrlId,jdbcType=INTEGER},#{shortCode,jdbcType=VARCHAR}
        ,#{shortUrl,jdbcType=VARCHAR},#{urlLink,jdbcType=VARCHAR},#{appid,jdbcType=VARCHAR}
        ,#{envVersion,jdbcType=VARCHAR},#{path,jdbcType=VARCHAR},#{query,jdbcType=VARCHAR}
        ,#{generateDate,jdbcType=TIMESTAMP},#{status,jdbcType=TINYINT},#{creator,jdbcType=VARCHAR}
        ,#{createTime,jdbcType=TIMESTAMP},#{updater,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP}
        ,#{isDelete,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhqb.spectre.common.dal.entity.WxUrlLinkDO" useGeneratedKeys="true">
        insert into t_wx_url_link
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="linkDesc != null">link_desc,</if>
                <if test="shortUrlId != null">short_url_id,</if>
                <if test="shortCode != null">short_code,</if>
                <if test="shortUrl != null">short_url,</if>
                <if test="urlLink != null">url_link,</if>
                <if test="appid != null">appid,</if>
                <if test="envVersion != null">env_version,</if>
                <if test="path != null">path,</if>
                <if test="query != null">query,</if>
                <if test="generateDate != null">generate_date,</if>
                <if test="status != null">status,</if>
                <if test="creator != null">creator,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updater != null">updater,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="isDelete != null">is_delete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="linkDesc != null">#{linkDesc,jdbcType=VARCHAR},</if>
                <if test="shortUrlId != null">#{shortUrlId,jdbcType=INTEGER},</if>
                <if test="shortCode != null">#{shortCode,jdbcType=VARCHAR},</if>
                <if test="shortUrl != null">#{shortUrl,jdbcType=VARCHAR},</if>
                <if test="urlLink != null">#{urlLink,jdbcType=VARCHAR},</if>
                <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
                <if test="envVersion != null">#{envVersion,jdbcType=VARCHAR},</if>
                <if test="path != null">#{path,jdbcType=VARCHAR},</if>
                <if test="query != null">#{query,jdbcType=VARCHAR},</if>
                <if test="generateDate != null">#{generateDate,jdbcType=TIMESTAMP},</if>
                <if test="status != null">#{status,jdbcType=TINYINT},</if>
                <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updater != null">#{updater,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="isDelete != null">#{isDelete,jdbcType=TINYINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.WxUrlLinkDO">
        update t_wx_url_link
        <set>
                <if test="linkDesc != null">
                    link_desc = #{linkDesc,jdbcType=VARCHAR},
                </if>
                <if test="shortUrlId != null">
                    short_url_id = #{shortUrlId,jdbcType=INTEGER},
                </if>
                <if test="shortCode != null">
                    short_code = #{shortCode,jdbcType=VARCHAR},
                </if>
                <if test="shortUrl != null">
                    short_url = #{shortUrl,jdbcType=VARCHAR},
                </if>
                <if test="urlLink != null">
                    url_link = #{urlLink,jdbcType=VARCHAR},
                </if>
                <if test="appid != null">
                    appid = #{appid,jdbcType=VARCHAR},
                </if>
                <if test="envVersion != null">
                    env_version = #{envVersion,jdbcType=VARCHAR},
                </if>
                <if test="path != null">
                    path = #{path,jdbcType=VARCHAR},
                </if>
                <if test="query != null">
                    query = #{query,jdbcType=VARCHAR},
                </if>
                <if test="generateDate != null">
                    generate_date = #{generateDate,jdbcType=TIMESTAMP},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=TINYINT},
                </if>
                <if test="creator != null">
                    creator = #{creator,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updater != null">
                    updater = #{updater,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="isDelete != null">
                    is_delete = #{isDelete,jdbcType=TINYINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.WxUrlLinkDO">
        update t_wx_url_link
        set 
            link_desc =  #{linkDesc,jdbcType=VARCHAR},
            short_url_id =  #{shortUrlId,jdbcType=INTEGER},
            short_code =  #{shortCode,jdbcType=VARCHAR},
            short_url =  #{shortUrl,jdbcType=VARCHAR},
            url_link =  #{urlLink,jdbcType=VARCHAR},
            appid =  #{appid,jdbcType=VARCHAR},
            env_version =  #{envVersion,jdbcType=VARCHAR},
            path =  #{path,jdbcType=VARCHAR},
            query =  #{query,jdbcType=VARCHAR},
            generate_date =  #{generateDate,jdbcType=TIMESTAMP},
            status =  #{status,jdbcType=TINYINT},
            creator =  #{creator,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            updater =  #{updater,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            is_delete =  #{isDelete,jdbcType=TINYINT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.WxUrlLinkQuery"
            resultType="java.lang.Integer">
        select count(*)
        from t_wx_url_link
        <where>
            is_delete = 0
            <if test="id !=null">and id=#{id}</if>
            <if test="shortUrl != null and shortUrl != ''">and short_url like concat('%', #{shortUrl}, '%')</if>
            <if test="urlLink != null and urlLink != ''">and url_link like concat('%', #{urlLink}, '%')</if>
            <if test="status != null">and status = #{status}</if>
            <if test="creator != null and creator != ''">and creator like concat('%', #{creator}, '%')</if>
            <if test="linkDesc !=null and linkDesc!=''">and link_desc like concat('%',#{linkDesc},'%')</if>
        </where>
    </select>

    <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.WxUrlLinkQuery"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_wx_url_link
        <where>
            is_delete = 0
            <if test="id !=null">and id=#{id}</if>
            <if test="shortUrl != null and shortUrl != ''">and short_url like concat('%', #{shortUrl}, '%')</if>
            <if test="urlLink != null and urlLink != ''">and url_link like concat('%', #{urlLink}, '%')</if>
            <if test="status != null">and status = #{status}</if>
            <if test="creator != null and creator != ''">and creator like concat('%', #{creator}, '%')</if>
            <if test="linkDesc !=null and linkDesc!=''">and link_desc like concat('%',#{linkDesc},'%')</if>
        </where>
        order by create_time desc
        <if test="pageParameter != null">
            limit #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>

    <update id="enable">
        update `t_wx_url_link`
        set `status`  = 1,
            `updater` = #{operator}
        where `id` = #{id}
          and `status` = 0
    </update>

    <update id="disable">
        update `t_wx_url_link`
        set `status`  = 0,
            `updater` = #{operator}
        where `id` = #{id}
          and `status` = 1
    </update>

</mapper>
