<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ChannelGroupItemMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ChannelGroupItemDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="group_id" property="groupId" jdbcType="INTEGER" />
    <result column="channel_account_id" property="channelAccountId" jdbcType="INTEGER" />
    <result column="isps" property="isps" jdbcType="VARCHAR" />
    <result column="area_filter_type" property="areaFilterType" jdbcType="TINYINT" />
    <result column="areas" property="areas" jdbcType="VARCHAR" />
    <result column="weight" property="weight" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, group_id, channel_account_id, isps, area_filter_type, areas, weight, remark, 
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_channel_group_item
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_channel_group_item
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.ChannelGroupItemDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_channel_group_item (id, group_id, channel_account_id, 
      isps, area_filter_type, areas, 
      weight, remark, create_time, 
      update_time)
    values (#{id,jdbcType=INTEGER}, #{groupId,jdbcType=INTEGER}, #{channelAccountId,jdbcType=INTEGER}, 
      #{isps,jdbcType=VARCHAR}, #{areaFilterType,jdbcType=TINYINT}, #{areas,jdbcType=VARCHAR}, 
      #{weight,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.ChannelGroupItemDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_channel_group_item
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="groupId != null" >
        group_id,
      </if>
      <if test="channelAccountId != null" >
        channel_account_id,
      </if>
      <if test="isps != null" >
        isps,
      </if>
      <if test="areaFilterType != null" >
        area_filter_type,
      </if>
      <if test="areas != null" >
        areas,
      </if>
      <if test="weight != null" >
        weight,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="groupId != null" >
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="channelAccountId != null" >
        #{channelAccountId,jdbcType=INTEGER},
      </if>
      <if test="isps != null" >
        #{isps,jdbcType=VARCHAR},
      </if>
      <if test="areaFilterType != null" >
        #{areaFilterType,jdbcType=TINYINT},
      </if>
      <if test="areas != null" >
        #{areas,jdbcType=VARCHAR},
      </if>
      <if test="weight != null" >
        #{weight,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.ChannelGroupItemDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_channel_group_item
    <set >
      <if test="groupId != null" >
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="channelAccountId != null" >
        channel_account_id = #{channelAccountId,jdbcType=INTEGER},
      </if>
      <if test="isps != null" >
        isps = #{isps,jdbcType=VARCHAR},
      </if>
      <if test="areaFilterType != null" >
        area_filter_type = #{areaFilterType,jdbcType=TINYINT},
      </if>
      <if test="areas != null" >
        areas = #{areas,jdbcType=VARCHAR},
      </if>
      <if test="weight != null" >
        weight = #{weight,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.ChannelGroupItemDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_channel_group_item
    set group_id = #{groupId,jdbcType=INTEGER},
      channel_account_id = #{channelAccountId,jdbcType=INTEGER},
      isps = #{isps,jdbcType=VARCHAR},
      area_filter_type = #{areaFilterType,jdbcType=TINYINT},
      areas = #{areas,jdbcType=VARCHAR},
      weight = #{weight,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByGroupId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_channel_group_item
    where group_id = #{groupId}
  </select>

  <delete id="deleteByGroupId" parameterType="java.lang.Integer">
    delete from t_channel_group_item where group_id = #{groupId}
  </delete>

  <select id="selectByGroupIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_channel_group_item
    where group_id in
    <foreach item="groupId" collection="groupIdList" open="(" separator="," close=")">
      #{groupId}
    </foreach>
  </select>
</mapper>