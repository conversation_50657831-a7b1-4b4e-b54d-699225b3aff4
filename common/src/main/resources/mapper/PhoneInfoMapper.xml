<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.PhoneInfoMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.PhoneInfoDO" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="pref" property="pref" jdbcType="VARCHAR" />
    <result column="phone" property="phone" jdbcType="VARCHAR" />
    <result column="province" property="province" jdbcType="VARCHAR" />
    <result column="city" property="city" jdbcType="VARCHAR" />
    <result column="isp" property="isp" jdbcType="VARCHAR" />
    <result column="post_code" property="postCode" jdbcType="VARCHAR" />
    <result column="city_code" property="cityCode" jdbcType="VARCHAR" />
    <result column="area_code" property="areaCode" jdbcType="VARCHAR" />
    <result column="isp_code" property="ispCode" jdbcType="INTEGER" />
    <result column="p_jp" property="pJp" jdbcType="VARCHAR" />
    <result column="c_jp" property="cJp" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, pref, phone, province, city, isp, post_code, city_code, area_code, isp_code,
    p_jp, c_jp
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from t_phone_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from t_phone_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.PhoneInfoDO" >
    insert into t_phone_info (id, pref, phone,
      province, city, isp,
      post_code, city_code, area_code,
      isp_code, p_jp, c_jp)
    values (#{id,jdbcType=INTEGER}, #{pref,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR},
      #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{isp,jdbcType=VARCHAR},
      #{postCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{areaCode,jdbcType=VARCHAR},
      #{ispCode,jdbcType=INTEGER}, #{pJp,jdbcType=VARCHAR}, #{cJp,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.PhoneInfoDO" >
    insert into t_phone_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="pref != null" >
        pref,
      </if>
      <if test="phone != null" >
        phone,
      </if>
      <if test="province != null" >
        province,
      </if>
      <if test="city != null" >
        city,
      </if>
      <if test="isp != null" >
        isp,
      </if>
      <if test="postCode != null" >
        post_code,
      </if>
      <if test="cityCode != null" >
        city_code,
      </if>
      <if test="areaCode != null" >
        area_code,
      </if>
      <if test="ispCode != null" >
        isp_code,
      </if>
      <if test="pJp != null" >
        p_jp,
      </if>
      <if test="cJp != null" >
        c_jp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="pref != null" >
        #{pref,jdbcType=VARCHAR},
      </if>
      <if test="phone != null" >
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="isp != null" >
        #{isp,jdbcType=VARCHAR},
      </if>
      <if test="postCode != null" >
        #{postCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null" >
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null" >
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="ispCode != null" >
        #{ispCode,jdbcType=INTEGER},
      </if>
      <if test="pJp != null" >
        #{pJp,jdbcType=VARCHAR},
      </if>
      <if test="cJp != null" >
        #{cJp,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.PhoneInfoDO" >
    update t_phone_info
    <set >
      <if test="pref != null" >
        pref = #{pref,jdbcType=VARCHAR},
      </if>
      <if test="phone != null" >
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="isp != null" >
        isp = #{isp,jdbcType=VARCHAR},
      </if>
      <if test="postCode != null" >
        post_code = #{postCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null" >
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null" >
        area_code = #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="ispCode != null" >
        isp_code = #{ispCode,jdbcType=INTEGER},
      </if>
      <if test="pJp != null" >
        p_jp = #{pJp,jdbcType=VARCHAR},
      </if>
      <if test="cJp != null" >
        c_jp = #{cJp,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.PhoneInfoDO" >
    update t_phone_info
    set pref = #{pref,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      isp = #{isp,jdbcType=VARCHAR},
      post_code = #{postCode,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      area_code = #{areaCode,jdbcType=VARCHAR},
      isp_code = #{ispCode,jdbcType=INTEGER},
      p_jp = #{pJp,jdbcType=VARCHAR},
      c_jp = #{cJp,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>