<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.AreasInfoMapper">
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.AreasInfoDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="level_type" jdbcType="VARCHAR" property="levelType" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="short_name" jdbcType="VARCHAR" property="shortName" />
    <result column="parent_path" jdbcType="VARCHAR" property="parentPath" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="province_short_name" jdbcType="VARCHAR" property="provinceShortName" />
    <result column="city_short_name" jdbcType="VARCHAR" property="cityShortName" />
    <result column="district_short_name" jdbcType="VARCHAR" property="districtShortName" />
    <result column="province_pinyin" jdbcType="VARCHAR" property="provincePinyin" />
    <result column="city_pinyin" jdbcType="VARCHAR" property="cityPinyin" />
    <result column="district_pinyin" jdbcType="VARCHAR" property="districtPinyin" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="zip_code" jdbcType="VARCHAR" property="zipCode" />
    <result column="pinyin" jdbcType="VARCHAR" property="pinyin" />
    <result column="jian_pin" jdbcType="VARCHAR" property="jianPin" />
    <result column="first_char" jdbcType="VARCHAR" property="firstChar" />
    <result column="lng" jdbcType="VARCHAR" property="lng" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
    <result column="remark1" jdbcType="VARCHAR" property="remark1" />
    <result column="remark2" jdbcType="VARCHAR" property="remark2" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, parent_id, level_type, name, short_name, parent_path, province, city, district,
    province_short_name, city_short_name, district_short_name, province_pinyin, city_pinyin,
    district_pinyin, city_code, zip_code, pinyin, jian_pin, first_char, lng, lat, remark1,
    remark2
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from t_areas_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_areas_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.AreasInfoDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_areas_info (id, parent_id, level_type,
      name, short_name, parent_path,
      province, city, district,
      province_short_name, city_short_name, district_short_name,
      province_pinyin, city_pinyin, district_pinyin,
      city_code, zip_code, pinyin,
      jian_pin, first_char, lng,
      lat, remark1, remark2
      )
    values (#{id,jdbcType=VARCHAR}, #{parentId,jdbcType=VARCHAR}, #{levelType,jdbcType=VARCHAR},
      #{name,jdbcType=VARCHAR}, #{shortName,jdbcType=VARCHAR}, #{parentPath,jdbcType=VARCHAR},
      #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{district,jdbcType=VARCHAR},
      #{provinceShortName,jdbcType=VARCHAR}, #{cityShortName,jdbcType=VARCHAR}, #{districtShortName,jdbcType=VARCHAR},
      #{provincePinyin,jdbcType=VARCHAR}, #{cityPinyin,jdbcType=VARCHAR}, #{districtPinyin,jdbcType=VARCHAR},
      #{cityCode,jdbcType=VARCHAR}, #{zipCode,jdbcType=VARCHAR}, #{pinyin,jdbcType=VARCHAR},
      #{jianPin,jdbcType=VARCHAR}, #{firstChar,jdbcType=VARCHAR}, #{lng,jdbcType=VARCHAR},
      #{lat,jdbcType=VARCHAR}, #{remark1,jdbcType=VARCHAR}, #{remark2,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.AreasInfoDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_areas_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="levelType != null">
        level_type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="shortName != null">
        short_name,
      </if>
      <if test="parentPath != null">
        parent_path,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="district != null">
        district,
      </if>
      <if test="provinceShortName != null">
        province_short_name,
      </if>
      <if test="cityShortName != null">
        city_short_name,
      </if>
      <if test="districtShortName != null">
        district_short_name,
      </if>
      <if test="provincePinyin != null">
        province_pinyin,
      </if>
      <if test="cityPinyin != null">
        city_pinyin,
      </if>
      <if test="districtPinyin != null">
        district_pinyin,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="zipCode != null">
        zip_code,
      </if>
      <if test="pinyin != null">
        pinyin,
      </if>
      <if test="jianPin != null">
        jian_pin,
      </if>
      <if test="firstChar != null">
        first_char,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="remark1 != null">
        remark1,
      </if>
      <if test="remark2 != null">
        remark2,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="levelType != null">
        #{levelType,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="shortName != null">
        #{shortName,jdbcType=VARCHAR},
      </if>
      <if test="parentPath != null">
        #{parentPath,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        #{district,jdbcType=VARCHAR},
      </if>
      <if test="provinceShortName != null">
        #{provinceShortName,jdbcType=VARCHAR},
      </if>
      <if test="cityShortName != null">
        #{cityShortName,jdbcType=VARCHAR},
      </if>
      <if test="districtShortName != null">
        #{districtShortName,jdbcType=VARCHAR},
      </if>
      <if test="provincePinyin != null">
        #{provincePinyin,jdbcType=VARCHAR},
      </if>
      <if test="cityPinyin != null">
        #{cityPinyin,jdbcType=VARCHAR},
      </if>
      <if test="districtPinyin != null">
        #{districtPinyin,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="pinyin != null">
        #{pinyin,jdbcType=VARCHAR},
      </if>
      <if test="jianPin != null">
        #{jianPin,jdbcType=VARCHAR},
      </if>
      <if test="firstChar != null">
        #{firstChar,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=VARCHAR},
      </if>
      <if test="remark1 != null">
        #{remark1,jdbcType=VARCHAR},
      </if>
      <if test="remark2 != null">
        #{remark2,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.AreasInfoDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_areas_info
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="levelType != null">
        level_type = #{levelType,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="shortName != null">
        short_name = #{shortName,jdbcType=VARCHAR},
      </if>
      <if test="parentPath != null">
        parent_path = #{parentPath,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        district = #{district,jdbcType=VARCHAR},
      </if>
      <if test="provinceShortName != null">
        province_short_name = #{provinceShortName,jdbcType=VARCHAR},
      </if>
      <if test="cityShortName != null">
        city_short_name = #{cityShortName,jdbcType=VARCHAR},
      </if>
      <if test="districtShortName != null">
        district_short_name = #{districtShortName,jdbcType=VARCHAR},
      </if>
      <if test="provincePinyin != null">
        province_pinyin = #{provincePinyin,jdbcType=VARCHAR},
      </if>
      <if test="cityPinyin != null">
        city_pinyin = #{cityPinyin,jdbcType=VARCHAR},
      </if>
      <if test="districtPinyin != null">
        district_pinyin = #{districtPinyin,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        zip_code = #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="pinyin != null">
        pinyin = #{pinyin,jdbcType=VARCHAR},
      </if>
      <if test="jianPin != null">
        jian_pin = #{jianPin,jdbcType=VARCHAR},
      </if>
      <if test="firstChar != null">
        first_char = #{firstChar,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=VARCHAR},
      </if>
      <if test="remark1 != null">
        remark1 = #{remark1,jdbcType=VARCHAR},
      </if>
      <if test="remark2 != null">
        remark2 = #{remark2,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.AreasInfoDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_areas_info
    set parent_id = #{parentId,jdbcType=VARCHAR},
      level_type = #{levelType,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      short_name = #{shortName,jdbcType=VARCHAR},
      parent_path = #{parentPath,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      district = #{district,jdbcType=VARCHAR},
      province_short_name = #{provinceShortName,jdbcType=VARCHAR},
      city_short_name = #{cityShortName,jdbcType=VARCHAR},
      district_short_name = #{districtShortName,jdbcType=VARCHAR},
      province_pinyin = #{provincePinyin,jdbcType=VARCHAR},
      city_pinyin = #{cityPinyin,jdbcType=VARCHAR},
      district_pinyin = #{districtPinyin,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      zip_code = #{zipCode,jdbcType=VARCHAR},
      pinyin = #{pinyin,jdbcType=VARCHAR},
      jian_pin = #{jianPin,jdbcType=VARCHAR},
      first_char = #{firstChar,jdbcType=VARCHAR},
      lng = #{lng,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=VARCHAR},
      remark1 = #{remark1,jdbcType=VARCHAR},
      remark2 = #{remark2,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="selectProvinceCity" resultMap="BaseResultMap">
    select `id`, `parent_id`, `level_type`, `name`, `province`, `city`, `province_short_name`, `city_short_name`
    from t_areas_info
    where `level_type` in ('0', '1', '2')
  </select>
</mapper>