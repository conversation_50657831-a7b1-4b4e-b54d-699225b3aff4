<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.GatewayUserMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.GatewayUserDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="group_name" property="groupName" jdbcType="VARCHAR" />
    <result column="user_name" property="userName" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="sp_code" property="spCode" jdbcType="VARCHAR" />
    <result column="service_id" property="serviceId" jdbcType="VARCHAR" />
    <result column="msg_src" property="msgSrc" jdbcType="VARCHAR" />
    <result column="tpl_code" property="tplCode" jdbcType="VARCHAR" />
    <result column="sign_id_list" jdbcType="VARCHAR" property="signIdList" />
    <result column="white_ip_list" jdbcType="VARCHAR" property="whiteIpList" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updater" property="updater" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_delete" property="isDelete" jdbcType="TINYINT" />
    <result column="is_check_tpl" jdbcType="TINYINT" property="isCheckTpl" />
  </resultMap>

  <resultMap id="TplResultMap" type="com.xhqb.spectre.common.dal.entity.support.TplDOSupport">
    <result column="tpl_id" property="id" jdbcType="INTEGER" />
    <result column="tpl_code" property="code" jdbcType="VARCHAR" />
    <result column="tpl_title" property="title" jdbcType="VARCHAR" />
    <result column="sign_name" property="signName" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, group_name, user_name, password, sp_code, service_id, msg_src, tpl_code, sign_id_list,
    white_ip_list, creator, create_time, updater, update_time, is_delete, is_check_tpl
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from t_gateway_user
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_gateway_user
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.GatewayUserDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_gateway_user (id, group_name, user_name,
      password, sp_code, service_id,
      msg_src, tpl_code, sign_id_list,
      white_ip_list, creator, create_time,
      updater, update_time, is_delete,
      is_check_tpl)
    values (#{id,jdbcType=INTEGER}, #{groupName,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR},
      #{password,jdbcType=VARCHAR}, #{spCode,jdbcType=VARCHAR}, #{serviceId,jdbcType=VARCHAR},
      #{msgSrc,jdbcType=VARCHAR}, #{tplCode,jdbcType=VARCHAR}, #{signIdList,jdbcType=VARCHAR},
      #{whiteIpList,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT},
      #{isCheckTpl,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.GatewayUserDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_gateway_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="spCode != null">
        sp_code,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="msgSrc != null">
        msg_src,
      </if>
      <if test="tplCode != null">
        tpl_code,
      </if>
      <if test="signIdList != null">
        sign_id_list,
      </if>
      <if test="whiteIpList != null">
        white_ip_list,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="isCheckTpl != null">
        is_check_tpl,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="spCode != null">
        #{spCode,jdbcType=VARCHAR},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=VARCHAR},
      </if>
      <if test="msgSrc != null">
        #{msgSrc,jdbcType=VARCHAR},
      </if>
      <if test="tplCode != null">
        #{tplCode,jdbcType=VARCHAR},
      </if>
      <if test="signIdList != null">
        #{signIdList,jdbcType=VARCHAR},
      </if>
      <if test="whiteIpList != null">
        #{whiteIpList,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="isCheckTpl != null">
        #{isCheckTpl,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.GatewayUserDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_gateway_user
    <set>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="spCode != null">
        sp_code = #{spCode,jdbcType=VARCHAR},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=VARCHAR},
      </if>
      <if test="msgSrc != null">
        msg_src = #{msgSrc,jdbcType=VARCHAR},
      </if>
      <if test="tplCode != null">
        tpl_code = #{tplCode,jdbcType=VARCHAR},
      </if>
      <if test="signIdList != null">
        sign_id_list = #{signIdList,jdbcType=VARCHAR},
      </if>
      <if test="whiteIpList != null">
        white_ip_list = #{whiteIpList,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="isCheckTpl != null">
        is_check_tpl = #{isCheckTpl,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.GatewayUserDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_gateway_user
    set group_name = #{groupName,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      sp_code = #{spCode,jdbcType=VARCHAR},
      service_id = #{serviceId,jdbcType=VARCHAR},
      msg_src = #{msgSrc,jdbcType=VARCHAR},
      tpl_code = #{tplCode,jdbcType=VARCHAR},
      sign_id_list = #{signIdList,jdbcType=VARCHAR},
      white_ip_list = #{whiteIpList,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=TINYINT},
      is_check_tpl = #{isCheckTpl,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.GatewayUserQuery" resultType="java.lang.Integer">
    select count(*)
    from t_gateway_user
    <where>
      <if test="serviceId != null and serviceId != ''">
        and service_id = #{serviceId}
      </if>
      <if test="userName != null and userName != ''">
        and user_name = #{userName}
      </if>
      <if test="tplCode != null and tplCode != ''">
        and tpl_code = #{tplCode}
      </if>
      and is_delete = 0
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.GatewayUserQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_gateway_user
    <where>
      <if test="serviceId != null and serviceId != ''">
        and service_id = #{serviceId}
      </if>
      <if test="userName != null and userName != ''">
        and user_name = #{userName}
      </if>
      <if test="tplCode != null and tplCode != ''">
        and tpl_code = #{tplCode}
      </if>
      and is_delete = 0
    </where>
    order by update_time desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>

  <select id="selectTplList" resultMap="TplResultMap">
      SELECT
          t.id tpl_id,
          t.`code` tpl_code,
          t.title tpl_title,
          s.`name` sign_name
      FROM
          t_tpl t
              INNER JOIN t_sign s ON t.sign_id = s.id
      WHERE
          t.`code` LIKE 'cmpp\_%\_tpl'
         OR t.`code` LIKE 'http\_%\_tpl'
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_gateway_user
    where is_delete = 0
  </select>

  <select id="findByUserName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_gateway_user
    where is_delete = 0 and `user_name` = #{user_name,jdbcType=VARCHAR}
  </select>

  <select id="selectUserName" resultType="java.lang.String">
    select user_name
    from t_gateway_user
    where is_delete = 0
  </select>
</mapper>