<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ShortUrlLogMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ShortUrlLogDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="click_time" property="clickTime" jdbcType="INTEGER"/>
        <result column="short_code" property="shortCode" jdbcType="VARCHAR"/>
        <result column="referrer" property="referrer" jdbcType="VARCHAR"/>
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR"/>
        <result column="platform" property="platform" jdbcType="VARCHAR"/>
        <result column="brand" property="brand" jdbcType="VARCHAR"/>
        <result column="model" property="model" jdbcType="VARCHAR"/>
        <result column="os" property="os" jdbcType="VARCHAR"/>
        <result column="os_version" property="osVersion" jdbcType="VARCHAR"/>
        <result column="ip" property="ip" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="isp" property="isp" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="user_tpl_code" property="userTplCode" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ClickCountResultMap" type="com.xhqb.spectre.common.dal.entity.ShortUrlClickCountDO">
        <result column="short_code" property="shortCode" jdbcType="VARCHAR"/>
        <result column="click_count" property="clickCount" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="ShortUrlLogDataMap" type="com.xhqb.spectre.common.dal.dto.ShortUrlLogData">
        <result column="brand" property="brand" jdbcType="VARCHAR"/>
        <result column="os" property="os" jdbcType="VARCHAR"/>
        <result column="user_tpl_code" property="userTplCode" jdbcType="VARCHAR"/>
        <result column="short_code" property="shortCode" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, click_time, short_code, referrer, user_agent, platform, brand, model, os, os_version,
        ip, province, city, isp, create_time,user_tpl_code
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from t_short_url_log
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="countByQuery" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_short_url_log
        <where>
            <if test="startClickTime!=null and startClickTime!=''">
                and click_time <![CDATA[ >= ]]> UNIX_TIMESTAMP(#{startClickTime})
            </if>
            <if test="endClickTime!=null and endClickTime!=''">
                and click_time  <![CDATA[<=]]> UNIX_TIMESTAMP(#{endClickTime})
            </if>
            <if test="shortUrl!=null and shortUrl!=''">
                and short_code =#{shortUrl}
            </if>
            <if test=" os!=null and  os!=''">
                and os = #{os}
            </if>
        </where>
    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_short_url_log
        <where>
            <if test="startClickTime!=null and startClickTime!=''">
                and click_time <![CDATA[ >=]]> UNIX_TIMESTAMP(#{startClickTime})
            </if>
            <if test="endClickTime!=null and endClickTime!=''">
                and click_time <![CDATA[<=]]> UNIX_TIMESTAMP(#{endClickTime})
            </if>
            <if test="shortUrl!=null and shortUrl!=''">
                and short_code =#{shortUrl}
            </if>
            <if test=" os!=null and  os!=''">
                and os = #{os}
            </if>
        </where>
        order by click_time desc
        <if test="pageParameter!=null">
            limit #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from t_short_url_log
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.ShortUrlLogDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_short_url_log (id, click_time, short_code,
        referrer, user_agent, platform,
        brand, model, os, os_version,
        ip, province, city,
        isp, create_time)
        values (#{id,jdbcType=BIGINT}, #{clickTime,jdbcType=INTEGER}, #{shortCode,jdbcType=VARCHAR},
        #{referrer,jdbcType=VARCHAR}, #{userAgent,jdbcType=VARCHAR}, #{platform,jdbcType=VARCHAR},
        #{brand,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{os,jdbcType=VARCHAR}, #{osVersion,jdbcType=VARCHAR},
        #{ip,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR},
        #{isp,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.ShortUrlLogDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_short_url_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="clickTime != null">
                click_time,
            </if>
            <if test="shortCode != null">
                short_code,
            </if>
            <if test="referrer != null">
                referrer,
            </if>
            <if test="userAgent != null">
                user_agent,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="brand != null">
                brand,
            </if>
            <if test="model != null">
                model,
            </if>
            <if test="os != null">
                os,
            </if>
            <if test="osVersion != null">
                os_version,
            </if>
            <if test="ip != null">
                ip,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="isp != null">
                isp,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="clickTime != null">
                #{clickTime,jdbcType=INTEGER},
            </if>
            <if test="shortCode != null">
                #{shortCode,jdbcType=VARCHAR},
            </if>
            <if test="referrer != null">
                #{referrer,jdbcType=VARCHAR},
            </if>
            <if test="userAgent != null">
                #{userAgent,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=VARCHAR},
            </if>
            <if test="brand != null">
                #{brand,jdbcType=VARCHAR},
            </if>
            <if test="model != null">
                #{model,jdbcType=VARCHAR},
            </if>
            <if test="os != null">
                #{os,jdbcType=VARCHAR},
            </if>
            <if test="osVersion != null">
                #{osVersion,jdbcType=VARCHAR},
            </if>
            <if test="ip != null">
                #{ip,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="isp != null">
                #{isp,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.ShortUrlLogDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_short_url_log
        <set>
            <if test="clickTime != null">
                click_time = #{clickTime,jdbcType=INTEGER},
            </if>
            <if test="shortCode != null">
                short_code = #{shortCode,jdbcType=VARCHAR},
            </if>
            <if test="referrer != null">
                referrer = #{referrer,jdbcType=VARCHAR},
            </if>
            <if test="userAgent != null">
                user_agent = #{userAgent,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="brand != null">
                brand = #{brand,jdbcType=VARCHAR},
            </if>
            <if test="model != null">
                model = #{model,jdbcType=VARCHAR},
            </if>
            <if test="os != null">
                os = #{os,jdbcType=VARCHAR},
            </if>
            <if test="osVersion != null">
                os_version = #{osVersion,jdbcType=VARCHAR},
            </if>
            <if test="ip != null">
                ip = #{ip,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="isp != null">
                isp = #{isp,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.ShortUrlLogDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_short_url_log
        set click_time = #{clickTime,jdbcType=INTEGER},
        short_code = #{shortCode,jdbcType=VARCHAR},
        referrer = #{referrer,jdbcType=VARCHAR},
        user_agent = #{userAgent,jdbcType=VARCHAR},
        platform = #{platform,jdbcType=VARCHAR},
        brand = #{brand,jdbcType=VARCHAR},
        model = #{model,jdbcType=VARCHAR},
        os = #{os,jdbcType=VARCHAR},
        os_version = #{osVersion,jdbcType=VARCHAR},
        ip = #{ip,jdbcType=VARCHAR},
        province = #{province,jdbcType=VARCHAR},
        city = #{city,jdbcType=VARCHAR},
        isp = #{isp,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="insertBatch" parameterType="com.xhqb.spectre.common.dal.entity.ShortUrlLogDO">
        insert into t_short_url_log(
        click_time, short_code, referrer, user_agent, platform, brand, model, os,
        os_version,ip, province,city,isp,user_tpl_code
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.clickTime,jdbcType=INTEGER},
            #{item.shortCode,jdbcType=VARCHAR},
            #{item.referrer,jdbcType=VARCHAR},
            #{item.userAgent,jdbcType=VARCHAR},
            #{item.platform,jdbcType=VARCHAR},
            #{item.brand,jdbcType=VARCHAR},
            #{item.model,jdbcType=VARCHAR},
            #{item.os,jdbcType=VARCHAR},
            #{item.osVersion,jdbcType=VARCHAR},
            #{item.ip,jdbcType=VARCHAR},
            #{item.province,jdbcType=VARCHAR},
            #{item.city,jdbcType=VARCHAR},
            #{item.isp,jdbcType=VARCHAR},
            #{item.userTplCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="selectLastItem" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_short_url_log
        where `create_time` <![CDATA[ >=]]> #{beginTime,jdbcType=TIMESTAMP}
        order by `create_time` desc limit 1
    </select>

    <select id="sumClickCount" resultMap="ClickCountResultMap">
        select short_code, count(*) as click_count
        from t_short_url_log
        where create_time <![CDATA[ >]]> #{beginTime,jdbcType=TIMESTAMP}
        and create_time <![CDATA[ <=]]> #{endTime,jdbcType=TIMESTAMP}
        group by short_code;
    </select>
    <select id="selectByShortCodes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_short_url_log
        where
        create_time <![CDATA[ >= ]]> #{startTime}
        and create_time <![CDATA[ <= ]]> #{endTime}
        and short_code in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        order by create_time desc
    </select>
    <select id="selectByTime" resultMap="ShortUrlLogDataMap">
        select
        brand,os,user_tpl_code,short_code
        from t_short_url_log
        where
        click_time <![CDATA[ >= ]]>  #{startOfDay} and click_time <![CDATA[ <= ]]> #{endOfDay}
    </select>
</mapper>