<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ZbxShortUrlMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ZbxShortUrlDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="short_code" jdbcType="VARCHAR" property="shortCode"/>
        <result column="src_url" jdbcType="VARCHAR" property="srcUrl"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="expired_date" jdbcType="DATE" property="expiredDate"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="click_count" jdbcType="INTEGER" property="clickCount"/>
        <result column="ip_click_count" jdbcType="INTEGER" property="ipClickCount"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, short_code, src_url, description, expired_date, status, click_count, ip_click_count,
        creator, create_time, updater, update_time, is_delete
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from t_zbx_short_url
        where is_delete = 0 and id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from t_zbx_short_url
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.ZbxShortUrlDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_zbx_short_url (id, short_code, src_url,
        description, expired_date, status,
        click_count, ip_click_count, creator,
        create_time, updater, update_time,
        is_delete)
        values (#{id,jdbcType=INTEGER}, #{shortCode,jdbcType=VARCHAR}, #{srcUrl,jdbcType=VARCHAR},
        #{description,jdbcType=VARCHAR}, #{expiredDate,jdbcType=DATE}, #{status,jdbcType=TINYINT},
        #{clickCount,jdbcType=INTEGER}, #{ipClickCount,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDelete,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.ZbxShortUrlDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_zbx_short_url
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="shortCode != null">
                short_code,
            </if>
            <if test="srcUrl != null">
                src_url,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="expiredDate != null">
                expired_date,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="clickCount != null">
                click_count,
            </if>
            <if test="ipClickCount != null">
                ip_click_count,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="shortCode != null">
                #{shortCode,jdbcType=VARCHAR},
            </if>
            <if test="srcUrl != null">
                #{srcUrl,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="expiredDate != null">
                #{expiredDate,jdbcType=DATE},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="clickCount != null">
                #{clickCount,jdbcType=INTEGER},
            </if>
            <if test="ipClickCount != null">
                #{ipClickCount,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.ZbxShortUrlDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_zbx_short_url
        <set>
            <if test="shortCode != null">
                short_code = #{shortCode,jdbcType=VARCHAR},
            </if>
            <if test="srcUrl != null">
                src_url = #{srcUrl,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="expiredDate != null">
                expired_date = #{expiredDate,jdbcType=DATE},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="clickCount != null">
                click_count = #{clickCount,jdbcType=INTEGER},
            </if>
            <if test="ipClickCount != null">
                ip_click_count = #{ipClickCount,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.ZbxShortUrlDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_zbx_short_url
        set short_code = #{shortCode,jdbcType=VARCHAR},
        src_url = #{srcUrl,jdbcType=VARCHAR},
        description = #{description,jdbcType=VARCHAR},
        expired_date = #{expiredDate,jdbcType=DATE},
        status = #{status,jdbcType=TINYINT},
        click_count = #{clickCount,jdbcType=INTEGER},
        ip_click_count = #{ipClickCount,jdbcType=INTEGER},
        creator = #{creator,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater = #{updater,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_delete = #{isDelete,jdbcType=TINYINT}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="enable">
        update `t_zbx_short_url`
        set `status`  = 1,
            `updater` = #{operator}
        where `id` = #{id}
          and `status` = 0
    </update>

    <update id="disable">
        update `t_zbx_short_url`
        set `status`  = 0,
            `updater` = #{operator}
        where `id` = #{id}
          and `status` = 1
    </update>

    <update id="delete">
        update `t_zbx_short_url`
        set `is_delete` = 1,
            `updater`   = #{operator}
        where `id` = #{id}
          and `status` = 0
    </update>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_zbx_short_url where status = 1 and is_delete =0
    </select>

    <select id="selectALlByUpdateTime" resultMap="BaseResultMap" parameterType="java.util.Date">
        select
        <include refid="Base_Column_List"/>
        from t_zbx_short_url where status = 1 and is_delete =0 and update_time > #{updateTime}
    </select>

    <select id="selectByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_zbx_short_url
        where is_delete = 0 and status = 1 and short_code =#{code}
    </select>

    <select id="listByType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_zbx_short_url where status = 1 and `type` = 1 and is_delete =0
    </select>

    <select id="listByUpdateAndType" resultMap="BaseResultMap" parameterType="java.util.Date">
        select
        <include refid="Base_Column_List"/>
        from t_zbx_short_url where status = 1 and `type` = 1  and is_delete =0  order by update_time desc limit 1
    </select>

    <update id="updateClickCount">
        update `t_zbx_short_url`
        set `click_count` = `click_count` + #{newClickCount}
        where `short_code` = #{shortCode}
    </update>
</mapper>