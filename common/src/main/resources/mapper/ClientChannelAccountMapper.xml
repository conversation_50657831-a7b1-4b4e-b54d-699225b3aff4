<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ClientChannelAccountMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ClientChannelAccountDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="service_id" property="serviceId" jdbcType="VARCHAR" />
    <result column="username" property="username" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="is_channel_connect" property="isChannelConnect" jdbcType="TINYINT" />
    <result column="is_valid" property="isValid" jdbcType="TINYINT" />
    <result column="version" property="version" jdbcType="INTEGER" />
    <result column="max_connect" property="maxConnect" jdbcType="INTEGER" />
    <result column="write_limit" property="writeLimit" jdbcType="INTEGER" />
    <result column="read_limit" property="readLimit" jdbcType="INTEGER" />
    <result column="ip_list" property="ipList" jdbcType="VARCHAR" />
    <result column="is_ip_check" property="isIpCheck" jdbcType="TINYINT" />
    <result column="group_name" property="groupName" jdbcType="VARCHAR" />
    <result column="idle_time_sec" property="idleTimeSec" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="updater" property="updater" jdbcType="VARCHAR" />
    <result column="is_delete" property="isDelete" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, service_id, username, password, is_channel_connect, is_valid, version, max_connect, 
    write_limit, read_limit, ip_list, is_ip_check, group_name, idle_time_sec, create_time, 
    creator, update_time, updater, is_delete
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_client_channel_account
    where id = #{id,jdbcType=INTEGER}
    and is_delete = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_client_channel_account
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.ClientChannelAccountDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_client_channel_account (id, service_id, username, 
      password, is_channel_connect, is_valid, 
      version, max_connect, write_limit, 
      read_limit, ip_list, is_ip_check, 
      group_name, idle_time_sec, create_time, 
      creator, update_time, updater, 
      is_delete)
    values (#{id,jdbcType=INTEGER}, #{serviceId,jdbcType=VARCHAR}, #{username,jdbcType=VARCHAR}, 
      #{password,jdbcType=VARCHAR}, #{isChannelConnect,jdbcType=TINYINT}, #{isValid,jdbcType=TINYINT}, 
      #{version,jdbcType=INTEGER}, #{maxConnect,jdbcType=INTEGER}, #{writeLimit,jdbcType=INTEGER}, 
      #{readLimit,jdbcType=INTEGER}, #{ipList,jdbcType=VARCHAR}, #{isIpCheck,jdbcType=TINYINT}, 
      #{groupName,jdbcType=VARCHAR}, #{idleTimeSec,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.ClientChannelAccountDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_client_channel_account
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="serviceId != null" >
        service_id,
      </if>
      <if test="username != null" >
        username,
      </if>
      <if test="password != null" >
        password,
      </if>
      <if test="isChannelConnect != null" >
        is_channel_connect,
      </if>
      <if test="isValid != null" >
        is_valid,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="maxConnect != null" >
        max_connect,
      </if>
      <if test="writeLimit != null" >
        write_limit,
      </if>
      <if test="readLimit != null" >
        read_limit,
      </if>
      <if test="ipList != null" >
        ip_list,
      </if>
      <if test="isIpCheck != null" >
        is_ip_check,
      </if>
      <if test="groupName != null" >
        group_name,
      </if>
      <if test="idleTimeSec != null" >
        idle_time_sec,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updater != null" >
        updater,
      </if>
      <if test="isDelete != null" >
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null" >
        #{serviceId,jdbcType=VARCHAR},
      </if>
      <if test="username != null" >
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null" >
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="isChannelConnect != null" >
        #{isChannelConnect,jdbcType=TINYINT},
      </if>
      <if test="isValid != null" >
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="version != null" >
        #{version,jdbcType=INTEGER},
      </if>
      <if test="maxConnect != null" >
        #{maxConnect,jdbcType=INTEGER},
      </if>
      <if test="writeLimit != null" >
        #{writeLimit,jdbcType=INTEGER},
      </if>
      <if test="readLimit != null" >
        #{readLimit,jdbcType=INTEGER},
      </if>
      <if test="ipList != null" >
        #{ipList,jdbcType=VARCHAR},
      </if>
      <if test="isIpCheck != null" >
        #{isIpCheck,jdbcType=TINYINT},
      </if>
      <if test="groupName != null" >
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="idleTimeSec != null" >
        #{idleTimeSec,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.ClientChannelAccountDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_client_channel_account
    <set >
      <if test="serviceId != null" >
        service_id = #{serviceId,jdbcType=VARCHAR},
      </if>
      <if test="username != null" >
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null" >
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="isChannelConnect != null" >
        is_channel_connect = #{isChannelConnect,jdbcType=TINYINT},
      </if>
      <if test="isValid != null" >
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="maxConnect != null" >
        max_connect = #{maxConnect,jdbcType=INTEGER},
      </if>
      <if test="writeLimit != null" >
        write_limit = #{writeLimit,jdbcType=INTEGER},
      </if>
      <if test="readLimit != null" >
        read_limit = #{readLimit,jdbcType=INTEGER},
      </if>
      <if test="ipList != null" >
        ip_list = #{ipList,jdbcType=VARCHAR},
      </if>
      <if test="isIpCheck != null" >
        is_ip_check = #{isIpCheck,jdbcType=TINYINT},
      </if>
      <if test="groupName != null" >
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="idleTimeSec != null" >
        idle_time_sec = #{idleTimeSec,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.ClientChannelAccountDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_client_channel_account
    set service_id = #{serviceId,jdbcType=VARCHAR},
      username = #{username,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      is_channel_connect = #{isChannelConnect,jdbcType=TINYINT},
      is_valid = #{isValid,jdbcType=TINYINT},
      version = #{version,jdbcType=INTEGER},
      max_connect = #{maxConnect,jdbcType=INTEGER},
      write_limit = #{writeLimit,jdbcType=INTEGER},
      read_limit = #{readLimit,jdbcType=INTEGER},
      ip_list = #{ipList,jdbcType=VARCHAR},
      is_ip_check = #{isIpCheck,jdbcType=TINYINT},
      group_name = #{groupName,jdbcType=VARCHAR},
      idle_time_sec = #{idleTimeSec,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.ClientChannelAccountQuery" resultType="java.lang.Integer">
    select count(1)
    from t_client_channel_account
    <where>
      <if test="serviceId != null">
        and service_id = #{serviceId}
      </if>
      <if test="username != null">
        and username = #{username}
      </if>
      and is_delete = 0
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.ClientChannelAccountQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_client_channel_account
    <where>
      <if test="serviceId != null">
        and service_id = #{serviceId}
      </if>
      <if test="username != null">
        and username = #{username}
      </if>
      and is_delete = 0
    </where>
    order by update_time desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>
</mapper>