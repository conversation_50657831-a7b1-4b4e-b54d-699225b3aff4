<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TestTaskMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.test.TestTaskDO">
        <id column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="tpl_id" property="tplId" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="is_delete" property="isDelete" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        task_id
        ,tpl_id,status,create_time,update_time,name,is_delete,remark
    </sql>
    <insert id="insertBySelective" useGeneratedKeys="true" keyProperty="taskId">
        insert into t_test_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                task_id,
            </if>
            <if test="tplId != null">
                tpl_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="tplId != null">
                #{tplId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateStatusByTaskId">
        update t_test_task
        set status = 2
        where task_id = #{taskId}
    </update>
    <update id="updateDeleteTagByTaskId">
        update t_test_task
        set is_delete = 1
        where task_id = #{taskId}
    </update>
    <update id="updateByTaskIdAndStatus">
        update t_test_task
        set status = #{status}
        where task_id = #{taskId}
    </update>
    <select id="selectByTplId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_task
        where tpl_id = #{tplId} and is_delete = 0
        order by update_time desc
    </select>
    <select id="countByQuery" resultType="java.lang.Integer">
        select count(*)
        from t_test_task
        <where>
            is_delete = 0
            <if test="query.tplId != null">
                and tpl_id = #{query.tplId}
            </if>
            <if test="query.taskId != null">
                and task_id = #{query.taskId}
            </if>
            <if test="query.name != null">
                and name like concat('%',#{query.name},'%')
            </if>
        </where>
    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_task
        <where>
            is_delete = 0
            <if test="query.tplId != null">
                and tpl_id = #{query.tplId}
            </if>
            <if test="query.taskId != null">
                and task_id = #{query.taskId}
            </if>
            <if test="query.name != null">
                and name like concat('%',#{query.name},'%')
            </if>
        </where>
        order by update_time desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset},#{query.pageParameter.pageSize}
        </if>
    </select>
    <select id="selectByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_task
        where task_id = #{taskId} limit 1
    </select>
    <select id="selectByTplIdAndTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_task
        where tpl_id = #{tplId} and is_delete = 0 and create_time &gt;= #{startTime} and create_time &lt;= #{endTime}
    </select>
    <select id="selectByTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_task
        where is_delete = 0 and create_time &gt;= #{startTime} and create_time &lt;= #{endTime}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_task
        where is_delete = 0
    </select>

</mapper>
