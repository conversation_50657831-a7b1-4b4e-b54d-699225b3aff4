<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TestContentTaskMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.test.tool.TestContentTaskDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="brand_config" jdbcType="VARCHAR" property="brandConfig"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="task_status" jdbcType="TINYINT" property="taskStatus"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="check_status" jdbcType="TINYINT" property="checkStatus"/>
        <result column="submit_time" jdbcType="INTEGER" property="submitTime"/>
        <result column="complete_time" jdbcType="INTEGER" property="completeTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , task_id, name, content, brand_config, remark, task_status, creator, create_time, updater,
          update_time,check_status,submit_time,complete_time
    </sql>

    <insert id="insertBySelective" useGeneratedKeys="true" keyProperty="id">
        insert into t_test_content_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="name != null">name,</if>
            <if test="content != null">content,</if>
            <if test="brandConfig != null">brand_config,</if>
            <if test="remark != null">remark,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="creator != null">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null">updater,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="completeTime != null">complete_time,</if>
            <if test="submitTime != null">submit_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="name != null">#{name},</if>
            <if test="content != null">#{content},</if>
            <if test="brandConfig != null">#{brandConfig},</if>
            <if test="remark != null">#{remark},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="completeTime != null">#{completeTime},</if>
            <if test="submitTime != null">#{submitTime},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective">
        update t_test_content_task
        <set>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="content != null">content = #{content},</if>
            <if test="brandConfig != null">brand_config = #{brandConfig},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
        </set>
        where id = #{id}
    </update>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_content_task
        where id = #{id}
    </select>

    <select id="countByQuery" resultType="java.lang.Integer">
        select count(1)
        from t_test_content_task
        <where>
            <if test="query.name != null">
                and name like concat('%', #{query.name}, '%')
            </if>
            <if test="query.content != null">
                and content like concat('%', #{query.content}, '%')
            </if>
            <if test="query.creator != null">
                and creator like concat('%', #{query.creator}, '%')
            </if>
            <if test="query.taskStatus != null">
                and task_status = #{query.taskStatus}
            </if>
            <if test="query.taskIdList != null and query.taskIdList.size() > 0">
                and task_id in
                <foreach item="item" collection="query.taskIdList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.likeTplCode != null">
                and tpl_code like concat('%', #{query.likeTplCode}, '%')
            </if>
            <if test="query.startTime != null">
                and create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and create_time &lt;= #{query.endTime}
            </if>
        </where>
    </select>

    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_content_task
        <where>
            <if test="query.name != null">
                and name like concat('%', #{query.name}, '%')
            </if>
            <if test="query.content != null">
                and content like concat('%', #{query.content}, '%')
            </if>
            <if test="query.creator != null">
                and creator like concat('%', #{query.creator}, '%')
            </if>
            <if test="query.taskStatus != null">
                and task_status = #{query.taskStatus}
            </if>
            <if test="query.taskIdList != null and query.taskIdList.size() > 0">
                and task_id in
                <foreach item="item" collection="query.taskIdList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.likeTplCode != null">
                and tpl_code like concat('%', #{query.likeTplCode}, '%')
            </if>
            <if test="query.startTime != null">
                and create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and create_time &lt;= #{query.endTime}
            </if>
        </where>
        order by create_time desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset}, #{query.pageParameter.pageSize}
        </if>
    </select>

    <select id="selectByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_content_task
        where task_id = #{taskId} limit 1
    </select>
    <select id="selectByTaskStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_content_task
        where task_status = #{taskStatus} and update_time > #{startTime} and update_time <![CDATA[ < ]]>  #{endTime}
    </select>

    <select id="countUnionDataByQuery" resultType="java.lang.Integer">
        select count(1)
        from t_test_content_task as l
        left join t_auto_test_bind as r
        on l.task_id collate utf8mb4_0900_ai_ci = r.test_content_task_id
        <where>
            <if test="query.taskIdList != null and query.taskIdList.size() > 0">
                and l.task_id in
                <foreach item="item" collection="query.taskIdList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.likeTplCode != null">
                and r.tpl_codes like concat('%', #{query.likeTplCode}, '%')
            </if>
            <if test="query.startTime != null">
                and l.create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and l.create_time &lt;= #{query.endTime}
            </if>
        </where>
    </select>

    <select id="selectUnionDataByQuery" resultMap="BaseResultMap">
        select
        l.*
        from t_test_content_task as l
        left join t_auto_test_bind as r
        on l.task_id collate utf8mb4_0900_ai_ci = r.test_content_task_id
        <where>
            <if test="query.taskIdList != null and query.taskIdList.size() > 0">
                and l.task_id in
                <foreach item="item" collection="query.taskIdList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.likeTplCode != null">
                and r.tpl_codes like concat('%', #{query.likeTplCode}, '%')
            </if>
            <if test="query.startTime != null">
                and l.create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and l.create_time &lt;= #{query.endTime}
            </if>
        </where>
        order by create_time desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset}, #{query.pageParameter.pageSize}
        </if>
    </select>
</mapper>