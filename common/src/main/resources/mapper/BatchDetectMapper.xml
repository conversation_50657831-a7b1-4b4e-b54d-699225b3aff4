<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.BatchDetectMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.BatchDetectDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="task_id" property="taskId" jdbcType="INTEGER" />
    <result column="content" property="content" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="file_md5" property="fileMd5" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, task_id, content, type, remark, file_md5, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from t_batch_detect
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_batch_detect
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.BatchDetectDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_detect (id, task_id, content,
    type, remark, file_md5,
    create_time, update_time)
    values (#{id,jdbcType=INTEGER}, #{taskId,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR},
    #{type,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{fileMd5,jdbcType=VARCHAR},
    #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchDetectDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_detect
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="taskId != null" >
        task_id,
      </if>
      <if test="content != null" >
        content,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="fileMd5 != null" >
        file_md5,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="taskId != null" >
        #{taskId,jdbcType=INTEGER},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=TINYINT},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fileMd5 != null" >
        #{fileMd5,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchDetectDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_detect
    <set >
      <if test="taskId != null" >
        task_id = #{taskId,jdbcType=INTEGER},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fileMd5 != null" >
        file_md5 = #{fileMd5,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.BatchDetectDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_detect
    set task_id = #{taskId,jdbcType=INTEGER},
    content = #{content,jdbcType=VARCHAR},
    type = #{type,jdbcType=TINYINT},
    remark = #{remark,jdbcType=VARCHAR},
    file_md5 = #{fileMd5,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
    insert into t_batch_detect (content,
    type, remark, file_md5)
    values
    <foreach collection="list" item="item" separator=",">
      (IFNULL(#{item.content},""),#{item.type},#{item.remark},#{item.fileMd5})
    </foreach>
  </insert>

  <update id="updateTaskIdByIdList">
    update t_batch_detect
    set task_id = #{taskId,jdbcType=INTEGER}
    where id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and task_id = 0
  </update>

  <select id="selectIdByFileMd5" resultType="int" >
    select
    id
    from t_batch_detect
    where file_md5 = #{fileMd5}
  </select>

  <select id="exportQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_batch_detect
    where task_id = #{taskId}
      <if test="type != null">
        and type = #{type}
      </if>
      and id <![CDATA[ > ]]> #{lastId}
    order by id asc limit #{pageSize}
  </select>

  <select id="exportCount"  resultType="java.lang.Integer">
    select count(1)
    from t_batch_detect
    where task_id = #{taskId}
    <if test="type != null">
      and type = #{type}
    </if>
  </select>
</mapper>