<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TestTplMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.test.TestTplDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="channel_account_id" property="channelAccountId" jdbcType="INTEGER"/>
        <result column="sms_tpl_id" property="smsTplId" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="params" property="params" jdbcType="VARCHAR"/>
        <result column="check_times" property="checkTimes" jdbcType="INTEGER"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="mobiles" property="mobiles" jdbcType="VARCHAR"/>
        <result column="mobile_count" property="mobileCount" jdbcType="INTEGER"/>
        <result column="type_weight" property="typeWeight" jdbcType="VARCHAR"/>
        <result column="max_times" property="maxTimes" jdbcType="INTEGER"/>
        <result column="time_period" property="timePeriod" jdbcType="VARCHAR"/>
        <result column="notify_account" property="notifyAccount" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR"/>
        <result column="sender_level" property="senderLevel" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,channel_account_id,params,check_times,source,mobiles,mobile_count,type_weight,max_times
        ,time_period,notify_account,status,remark,create_time,update_time,creator,updater,sms_tpl_id,name,sender_level,sms_type_code
    </sql>
    <insert id="insertBySelective" useGeneratedKeys="true" keyProperty="id">
        insert into t_test_tpl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="channelAccountId != null">
                channel_account_id,
            </if>
            <if test="params != null">
                params,
            </if>
            <if test="checkTimes != null">
                check_times,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="mobiles != null">
                mobiles,
            </if>
            <if test="mobileCount != null">
                mobile_count,
            </if>
            <if test="maxTimes != null">
                max_times,
            </if>
            <if test="timePeriod != null">
                time_period,
            </if>
            <if test="notifyAccount != null">
                notify_account,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="smsTplId != null">
                sms_tpl_id,
            </if>
            <if test="typeWeight != null">
                type_weight,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="smsTypeCode != null">
                sms_type_code,
            </if>
            <if test="senderLevel != null">
                sender_level,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="channelAccountId != null">
                #{channelAccountId,jdbcType=INTEGER},
            </if>
            <if test="params != null">
                #{params,jdbcType=VARCHAR},
            </if>
            <if test="checkTimes != null">
                #{checkTimes,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                #{source,jdbcType=INTEGER},
            </if>
            <if test="mobiles != null">
                #{mobiles,jdbcType=VARCHAR},
            </if>
            <if test="mobileCount != null">
                #{mobileCount,jdbcType=INTEGER},
            </if>
            <if test="maxTimes != null">
                #{maxTimes,jdbcType=INTEGER},
            </if>
            <if test="timePeriod != null">
                #{timePeriod,jdbcType=VARCHAR},
            </if>
            <if test="notifyAccount != null">
                #{notifyAccount,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="smsTplId != null">
                #{smsTplId,jdbcType=INTEGER},
            </if>
            <if test="typeWeight != null">
                #{typeWeight,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="senderLevel != null">
                #{senderLevel,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective">
        update t_test_tpl
        <set>
            <if test="channelAccountId != null">
                channel_account_id = #{channelAccountId,jdbcType=INTEGER},
            </if>
            <if test="params != null">
                params = #{params,jdbcType=VARCHAR},
            </if>
            <if test="checkTimes != null">
                check_times = #{checkTimes,jdbcType=INTEGER},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=INTEGER},
            </if>
            <if test="mobiles != null">
                mobiles = #{mobiles,jdbcType=VARCHAR},
            </if>
            <if test="mobileCount != null">
                mobile_count = #{mobileCount,jdbcType=INTEGER},
            </if>
            <if test="maxTimes != null">
                max_times = #{maxTimes,jdbcType=INTEGER},
            </if>
            <if test="timePeriod != null">
                time_period = #{timePeriod,jdbcType=VARCHAR},
            </if>
            <if test="notifyAccount != null">
                notify_account = #{notifyAccount,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="smsTplId != null">
                sms_tpl_id = #{smsTplId,jdbcType=INTEGER},
            </if>
            <if test="typeWeight != null">
                type_weight = #{typeWeight,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="senderLevel != null">
                sender_level = #{senderLevel,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="countByQuery" resultType="java.lang.Integer">
        select count(*)
        from t_test_tpl
        <where>
            is_delete = 0
            <if test="query.status != null">
                and status = #{query.status}
            </if>
            <if test="query.smsTplId != null">
                and sms_tpl_id = #{query.smsTplId}
            </if>
            <if test="query.channelAccountId != null">
                and channel_account_id = #{query.channelAccountId}
            </if>
            <if test="query.source != null">
                and source = #{query.source}
            </if>
            <if test="query.id != null">
                and id = #{query.id}
            </if>
        </where>
    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_tpl
        <where>
            is_delete = 0
            <if test="query.status != null">
                and status = #{query.status}
            </if>
            <if test="query.smsTplId != null">
                and sms_tpl_id = #{query.smsTplId}
            </if>
            <if test="query.channelAccountId != null">
                and channel_account_id = #{query.channelAccountId}
            </if>
            <if test="query.source != null">
                and source = #{query.source}
            </if>
            <if test="query.id != null">
                and id = #{query.id}
            </if>
        </where>
        order by update_time desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset},#{query.pageParameter.pageSize}
        </if>
    </select>
    <select id="selectByChannelAccountIdAndSmsTplId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_tpl
        where channel_account_id = #{channelAccountId}
        and sms_tpl_id = #{smsTplId} and is_delete = 0
        limit 1
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_tpl
        where id = #{id,jdbcType=INTEGER} and is_delete = 0
    </select>
    <select id="selectByTplIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_tpl
        where id in
        <foreach collection="tplIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_delete = 0 and `source` =#{source}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_tpl
        where is_delete = 0 and status = 1
    </select>
    <select id="selectByTplId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_tpl
        where sms_tpl_id = #{tplId} and is_delete = 0
    </select>


</mapper>
