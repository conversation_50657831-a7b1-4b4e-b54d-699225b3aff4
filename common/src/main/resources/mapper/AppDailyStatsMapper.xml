<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.AppDailyStatsMapper">

    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.AppDailyStatsDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="statDate" column="stat_date" jdbcType="DATE"/>
            <result property="accountCategory" column="account_category" jdbcType="TINYINT"/>
            <result property="accountId" column="account_id" jdbcType="INTEGER"/>
            <result property="tplCode" column="tpl_code" jdbcType="VARCHAR"/>
            <result property="tplMappingId" column="tpl_mapping_id" jdbcType="INTEGER"/>
            <result property="totalRequests" column="total_requests" jdbcType="INTEGER"/>
            <result property="errCode" column="err_code" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,stat_date,account_category,
        account_id,tpl_code,tpl_mapping_id,
        total_requests,err_code,status,
        create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_app_daily_stats
        where  id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_app_daily_stats
        where  id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhqb.spectre.common.dal.entity.AppDailyStatsDO" useGeneratedKeys="true">
        insert into t_app_daily_stats
        ( id,stat_date,account_category
        ,account_id,tpl_code,tpl_mapping_id
        ,total_requests,err_code,status
        ,create_time,update_time)
        values (#{id,jdbcType=BIGINT},#{statDate,jdbcType=DATE},#{accountCategory,jdbcType=TINYINT}
        ,#{accountId,jdbcType=INTEGER},#{tplCode,jdbcType=VARCHAR},#{tplMappingId,jdbcType=INTEGER}
        ,#{totalRequests,jdbcType=INTEGER},#{errCode,jdbcType=VARCHAR},#{status,jdbcType=TINYINT}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhqb.spectre.common.dal.entity.AppDailyStatsDO" useGeneratedKeys="true">
        insert into t_app_daily_stats
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="statDate != null">stat_date,</if>
                <if test="accountCategory != null">account_category,</if>
                <if test="accountId != null">account_id,</if>
                <if test="tplCode != null">tpl_code,</if>
                <if test="tplMappingId != null">tpl_mapping_id,</if>
                <if test="totalRequests != null">total_requests,</if>
                <if test="errCode != null">err_code,</if>
                <if test="status != null">status,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="statDate != null">#{statDate,jdbcType=DATE},</if>
                <if test="accountCategory != null">#{accountCategory,jdbcType=TINYINT},</if>
                <if test="accountId != null">#{accountId,jdbcType=INTEGER},</if>
                <if test="tplCode != null">#{tplCode,jdbcType=VARCHAR},</if>
                <if test="tplMappingId != null">#{tplMappingId,jdbcType=INTEGER},</if>
                <if test="totalRequests != null">#{totalRequests,jdbcType=INTEGER},</if>
                <if test="errCode != null">#{errCode,jdbcType=VARCHAR},</if>
                <if test="status != null">#{status,jdbcType=TINYINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.AppDailyStatsDO">
        update t_app_daily_stats
        <set>
                <if test="statDate != null">
                    stat_date = #{statDate,jdbcType=DATE},
                </if>
                <if test="accountCategory != null">
                    account_category = #{accountCategory,jdbcType=TINYINT},
                </if>
                <if test="accountId != null">
                    account_id = #{accountId,jdbcType=INTEGER},
                </if>
                <if test="tplCode != null">
                    tpl_code = #{tplCode,jdbcType=VARCHAR},
                </if>
                <if test="tplMappingId != null">
                    tpl_mapping_id = #{tplMappingId,jdbcType=INTEGER},
                </if>
                <if test="totalRequests != null">
                    total_requests = #{totalRequests,jdbcType=INTEGER},
                </if>
                <if test="errCode != null">
                    err_code = #{errCode,jdbcType=VARCHAR},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=TINYINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.AppDailyStatsDO">
        update t_app_daily_stats
        set 
            stat_date =  #{statDate,jdbcType=DATE},
            account_category =  #{accountCategory,jdbcType=TINYINT},
            account_id =  #{accountId,jdbcType=INTEGER},
            tpl_code =  #{tplCode,jdbcType=VARCHAR},
            tpl_mapping_id =  #{tplMappingId,jdbcType=INTEGER},
            total_requests =  #{totalRequests,jdbcType=INTEGER},
            err_code =  #{errCode,jdbcType=VARCHAR},
            status =  #{status,jdbcType=TINYINT},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT}
    </update>

    <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.AppDailyStatsQuery" resultType="java.lang.Integer">
        select count(*)
        from t_app_daily_stats
        <where>
            stat_date BETWEEN #{startDate, jdbcType=DATE} AND #{endDate, jdbcType=DATE}
            <if test="accountId != null">
                AND account_id = #{accountId}
                AND account_category = #{accountCategory}
            </if>
        </where>
    </select>

    <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.AppDailyStatsQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_app_daily_stats
        <where>
            stat_date BETWEEN #{startDate, jdbcType=DATE} AND #{endDate, jdbcType=DATE}
            <if test="accountId != null">
                AND account_id = #{accountId}
                AND account_category = #{accountCategory}
            </if>
        </where>
        order by update_time desc
        <if test="pageParameter != null">
            limit #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>

    <insert id="saveOrUpdate" parameterType="com.xhqb.spectre.common.dal.entity.AppDailyStatsDO">
        insert into t_app_daily_stats(
        stat_date, account_category, account_id, tpl_code, tpl_mapping_id, err_code, total_requests, status
        )
        values (
           #{statDate,jdbcType=DATE},
           #{accountCategory,jdbcType=TINYINT},
           #{accountId,jdbcType=INTEGER},
           #{tplCode,jdbcType=VARCHAR},
           #{tplMappingId,jdbcType=INTEGER},
           #{errCode,jdbcType=VARCHAR},
           #{totalRequests,jdbcType=INTEGER},
           #{status,jdbcType=TINYINT}
               )
        ON DUPLICATE KEY UPDATE
        total_requests = values (total_requests)
    </insert>
</mapper>
