<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ChannelAccountDisableMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ChannelAccountDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="channel_account_id" property="channelAccountId" jdbcType="INTEGER" />
    <result column="isps" property="isps" jdbcType="VARCHAR" />
    <result column="areas" property="areas" jdbcType="VARCHAR" />
    <result column="mask_start_time" property="maskStartTime"  />
    <result column="mask_end_time" property="maskEndTime" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updater" property="updater" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_delete" property="isDelete" jdbcType="TINYINT" />
    <result column="disable_type" property="disableType" jdbcType="INTEGER" />
    <result column="period_start_time" property="periodStartTime" jdbcType="VARCHAR" />
    <result column="period_end_time" property="periodEndTime" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <association property="channelAccountDO" javaType="com.xhqb.spectre.common.dal.entity.ChannelAccountDO">
      <result column="channel_code" property="channelCode"/>
      <result column="sms_type_code" property="smsTypeCode"/>
      <result column="name" property="name"/>
      <result column="status" property="status"/>
      <association property="channelDO" javaType="com.xhqb.spectre.common.dal.entity.ChannelDO">
        <result column="channel_name" property="name"/>
      </association>
    </association>
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, channel_account_id, isps, areas, creator, create_time,
    updater, update_time, is_delete, mask_start_time, mask_end_time,
    disable_type, period_start_time, period_end_time, remark
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_channel_account_disable
    where is_delete = 0 and id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_channel_account_disable
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.ChannelAccountDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_channel_account_disable (id, channel_account_id, isps,
      areas, creator, create_time, updater,
      update_time, is_delete, mask_start_time, mask_end_time,
      disable_type, period_start_time, period_end_time, remark)
    values (#{id,jdbcType=INTEGER}, #{channelAccountId,jdbcType=INTEGER}, #{isps,jdbcType=VARCHAR},
      #{areas,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT}, #{maskStartTime}, #{maskEndTime},
      #{disableType,jdbcType=INTEGER}, #{periodStartTime,jdbcType=VARCHAR}, #{periodEndTime,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.ChannelAccountDisableDO" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_channel_account_disable
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="channelAccountId != null" >
        channel_account_id,
      </if>
      <if test="isps != null" >
        isps,
      </if>
      <if test="areas != null" >
        areas,
      </if>
      <if test="maskStartTime != null" >
        mask_start_time,
      </if>
      <if test="maskEndTime != null" >
        mask_end_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updater != null" >
        updater,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="isDelete != null" >
        is_delete,
      </if>
      <if test="disableType != null" >
        disable_type,
      </if>
      <if test="periodStartTime != null" >
        period_start_time,
      </if>
      <if test="periodEndTime != null" >
        period_end_time,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="channelAccountId != null" >
        #{channelAccountId,jdbcType=INTEGER},
      </if>
      <if test="isps != null" >
        #{isps,jdbcType=VARCHAR},
      </if>
      <if test="areas != null" >
        #{areas,jdbcType=VARCHAR},
      </if>
      <if test="maskStartTime != null" >
        #{maskStartTime},
      </if>
      <if test="maskEndTime != null" >
        #{maskEndTime},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="disableType != null" >
        #{disableType,jdbcType=INTEGER},
      </if>
      <if test="periodStartTime != null" >
        #{periodStartTime,jdbcType=VARCHAR},
      </if>
      <if test="periodEndTime != null" >
        #{periodEndTime,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.ChannelAccountDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_channel_account_disable
    <set >
      <if test="channelAccountId != null" >
        channel_account_id = #{channelAccountId,jdbcType=INTEGER},
      </if>
      <if test="isps != null" >
        isps = #{isps,jdbcType=VARCHAR},
      </if>
      <if test="areas != null" >
        areas = #{areas,jdbcType=VARCHAR},
      </if>
      <if test="maskStartTime != null" >
        mask_start_time = #{maskStartTime},
      </if>
      <if test="maskEndTime != null" >
        mask_end_time = #{maskEndTime},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="disableType != null" >
        disable_type = #{disableType,jdbcType=INTEGER},
      </if>
      <if test="periodStartTime != null" >
        period_start_time = #{periodStartTime,jdbcType=VARCHAR},
      </if>
      <if test="periodEndTime != null" >
        period_end_time = #{periodEndTime,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.ChannelAccountDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_channel_account_disable
    set channel_account_id = #{channelAccountId,jdbcType=INTEGER},
      isps = #{isps,jdbcType=VARCHAR},
      areas = #{areas,jdbcType=VARCHAR},
      mask_start_time = #{maskStartTime},
      mask_end_time = #{maskEndTime},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=TINYINT},
      disable_type = #{disableType,jdbcType=INTEGER},
      period_start_time = #{periodStartTime,jdbcType=VARCHAR},
      period_end_time = #{periodEndTime,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.ChannelAccountDisableQuery" resultType="java.lang.Integer">
    select count(*)
    from t_channel_account_disable m
    left join t_channel_account n on m.channel_account_id = n.id
    left join t_channel t on n.channel_code = t.code
    <where>
      m.is_delete = 0 and t.is_delete = 0
      <if test="channelName != null and channelName != ''">and t.name like concat('%', #{channelName}, '%')</if>
      <if test="channelAccountName != null and channelAccountName != ''">and n.name like concat('%', #{channelAccountName}, '%')</if>
      <if test="channelAccountStatus != null and channelAccountStatus != -1">and n.status = #{channelAccountStatus}</if>
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.ChannelAccountDisableQuery" resultMap="BaseResultMap">
    select m.id, m.channel_account_id, m.isps, m.areas, m.mask_start_time, m.mask_end_time, m.creator, m.create_time, m.updater, m.update_time,
           m.disable_type, m.period_start_time, m.period_end_time, m.remark,
           n.channel_code, n.sms_type_code, n.name, n.status, t.name as 'channel_name'
    from t_channel_account_disable m
    left join t_channel_account n on m.channel_account_id = n.id
    left join t_channel t on n.channel_code = t.code
    <where>
      m.is_delete = 0 and t.is_delete = 0
      <if test="channelName != null and channelName != ''">and t.name like concat('%', #{channelName}, '%')</if>
      <if test="channelAccountName != null and channelAccountName != ''">and n.name like concat('%', #{channelAccountName}, '%')</if>
      <if test="channelAccountStatus != null and channelAccountStatus != -1">and n.status = #{channelAccountStatus}</if>
    </where>
    order by m.update_time desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>

  <select id="selectByAccountId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_channel_account_disable
    where is_delete = 0 and channel_account_id = #{channelAccountId,jdbcType=INTEGER}
  </select>

  <update id="delete">
    update `t_channel_account_disable`
    set `is_delete` = 1, `updater` = #{operator}
    where `id` = #{id}
  </update>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_channel_account_disable
    where is_delete = 0
  </select>
</mapper>