<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.GatewayTplMappingMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.GatewayTplMappingDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="tpl_id" property="tplId" jdbcType="INTEGER" />
    <result column="tpl_content" property="tplContent" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updater" property="updater" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_delete" property="isDelete" jdbcType="TINYINT" />
    <result column="tpl_code" property="tplCode" jdbcType="VARCHAR" />
    <result column="sign_name" property="signName" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, tpl_id, tpl_content, creator, create_time, updater, update_time, is_delete
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_gateway_tpl_mapping
    where is_delete = 0 and id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_gateway_tpl_mapping
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.GatewayTplMappingDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_gateway_tpl_mapping (id, tpl_id, tpl_content, 
      creator, create_time, updater, 
      update_time, is_delete)
    values (#{id,jdbcType=INTEGER}, #{tplId,jdbcType=INTEGER}, #{tplContent,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.GatewayTplMappingDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_gateway_tpl_mapping
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="tplId != null" >
        tpl_id,
      </if>
      <if test="tplContent != null" >
        tpl_content,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updater != null" >
        updater,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="isDelete != null" >
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="tplId != null" >
        #{tplId,jdbcType=INTEGER},
      </if>
      <if test="tplContent != null" >
        #{tplContent,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.GatewayTplMappingDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_gateway_tpl_mapping
    <set >
      <if test="tplId != null" >
        tpl_id = #{tplId,jdbcType=INTEGER},
      </if>
      <if test="tplContent != null" >
        tpl_content = #{tplContent,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.GatewayTplMappingDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_gateway_tpl_mapping
    set tpl_id = #{tplId,jdbcType=INTEGER},
      tpl_content = #{tplContent,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.GatewayTplMappingQuery" resultType="java.lang.Integer">
    select count(*)
    from t_gateway_tpl_mapping as m
    left join t_tpl n on m.tpl_id = n.id
    left join t_sign t on n.sign_id = t.id
    <where>
      m.is_delete = 0
      <if test="tplId != null">and m.tpl_id = #{tplId}</if>
      <if test="tplCode != null and tplCode != ''">and n.code = #{tplCode}</if>
      <if test="tplContent != null and tplContent != ''">and m.tpl_content like concat('%', #{tplContent}, '%')</if>
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.GatewayTplMappingQuery" resultMap="BaseResultMap">
    select
    m.id, m.tpl_id, m.tpl_content, m.creator, m.create_time, m.updater, m.update_time, n.code as tpl_code, t.name as sign_name
    from t_gateway_tpl_mapping as m
    left join t_tpl n on m.tpl_id = n.id
    left join t_sign t on n.sign_id = t.id
    <where>
      m.is_delete = 0
      <if test="tplId != null">and m.tpl_id = #{tplId}</if>
      <if test="tplCode != null and tplCode != ''">and n.code = #{tplCode}</if>
      <if test="tplContent != null and tplContent != ''">and m.tpl_content like concat('%', #{tplContent}, '%')</if>
    </where>
    order by m.update_time desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>

  <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    m.id, m.tpl_id, m.tpl_content, m.creator, m.create_time, m.updater, m.update_time, n.code as tpl_code, t.name as sign_name
    from t_gateway_tpl_mapping as m
    left join t_tpl n on m.tpl_id = n.id
    left join t_sign t on n.sign_id = t.id
    where m.is_delete = 0 and m.id = #{id}
  </select>

  <update id="delete">
    update `t_gateway_tpl_mapping`
    set `is_delete` = 1, `updater` = #{operator}
    where `id` = #{id}
  </update>

  <select id="selectByTplIdAndContent" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_gateway_tpl_mapping
    where is_delete = 0 and tpl_id = #{tplId} and tpl_content = #{tplContent}
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_gateway_tpl_mapping
    where is_delete = 0
  </select>
</mapper>