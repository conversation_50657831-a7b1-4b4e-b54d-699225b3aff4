<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.PhoneNumberStatusMapper">

    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.PhoneNumberStatusDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="mobile" column="mobile_enc" jdbcType="VARCHAR"/>
        <result property="lastTime" column="last_time" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="carrier" column="carrier" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,mobile_enc,mobile_hash,
        last_time,status,carrier,
        create_time,
        update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_phone_number_status
        where id = #{id,jdbcType=BIGINT}
    </select>


    <select id="selectByMobiles" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_phone_number_status
        where mobile_enc IN
        <foreach item="mobile" index="index" collection="mobiles" open="(" separator="," close=")">
            #{mobile}
        </foreach>
        and last_time > #{timestamp, jdbcType=INTEGER}
    </select>

    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.PhoneNumberStatusDO">
        insert into t_phone_number_status (`id`, `mobile_enc`,
                                           `last_time`, `status`, `carrier`,
                                           `create_time`, `update_time`)
        values (#{id,jdbcType=BIGINT},
                #{mobile,jdbcType=VARCHAR},
                #{lastTime,jdbcType=INTEGER},
                #{status,jdbcType=TINYINT},
                #{carrier,jdbcType=TINYINT},
                #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.PhoneNumberStatusDO">
        insert into t_phone_number_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="mobile != null">
                mobile_enc,
            </if>
            <if test="lastTime != null">
                last_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="carrier != null">
                carrier,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="update_time != null">
                update_time,
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.PhoneNumberStatusDO">
        update t_phone_number_status
        <set>
            <if test="mobile != null">
                mobile_enc = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="mobileHash != null">
                mobile_hash = #{mobileHash,jdbcType=VARCHAR},
            </if>
        </set>
    </update>

    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.PhoneNumberStatusDO">
        update t_phone_number_status
        set mobile_enc  = #{mobile,jdbcType=VARCHAR},
            mobile_hash = #{mobileHash,jdbcType=VARCHAR}
        WHERE id = #{id}
    </update>

    <select id="selectByMobile" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_phone_number_status
        where mobile_enc = #{mobile,jdbcType=VARCHAR}
    </select>
    <select id="selectEmptyNumbers" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_phone_number_status
        where status = 4
            limit #{emptyCount}
    </select>


    <insert id="saveOrUpdateBatch" parameterType="com.xhqb.spectre.common.dal.entity.PhoneNumberStatusDO">
        insert into t_phone_number_status(
        mobile_enc, last_time, status, carrier
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.mobile,jdbcType=VARCHAR},
            #{item.lastTime,jdbcType=INTEGER},
            #{item.status,jdbcType=TINYINT},
            #{item.carrier,jdbcType=TINYINT}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        mobile_enc = values (mobile_enc),
        last_time = values (last_time),
        status = values (status),
        carrier = values (carrier)
    </insert>
</mapper>
