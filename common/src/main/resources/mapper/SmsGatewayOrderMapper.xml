<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.cmpp_gateway.dao.SmsGatewayOrderMapper" >
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.SmsGatewayOrderDO" >
        <!--x
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="request_id" jdbcType="VARCHAR" property="requestId" />
        <result column="channel_msg_id" jdbcType="VARCHAR" property="channelMsgId" />
        <result column="channel_account_id" jdbcType="INTEGER" property="channelAccountId" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="content" jdbcType="VARCHAR" property="content" />
        <result column="send_time" jdbcType="INTEGER" property="sendTime" />
        <result column="send_status" jdbcType="INTEGER" property="sendStatus" />
        <result column="recv_submit_time" jdbcType="INTEGER" property="recvSubmitTime" />
        <result column="report_time" jdbcType="INTEGER" property="reportTime" />
        <result column="report_status" jdbcType="INTEGER" property="reportStatus" />
        <result column="report_desc" jdbcType="VARCHAR" property="reportDesc" />
        <result column="submit_time" jdbcType="INTEGER" property="submitTime" />
        <result column="done_time" jdbcType="INTEGER" property="doneTime" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, request_id, channel_msg_id, channel_account_id, mobile, content, send_time, send_status,
        recv_submit_time, report_time, report_status, report_desc, submit_time, done_time
    </sql>
    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.SmsGatewayOrderDO" >
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_sms_gateway_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="requestId != null">
                request_id,
            </if>
            <if test="channelMsgId != null">
                channel_msg_id,
            </if>
            <if test="channelAccountId != null">
                channel_account_id,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="sendTime != null">
                send_time,
            </if>
            <if test="sendStatus != null">
                send_status,
            </if>
            <if test="recvSubmitTime != null">
                recv_submit_time,
            </if>
            <if test="reportTime != null">
                report_time,
            </if>
            <if test="reportStatus != null">
                report_status,
            </if>
            <if test="reportDesc != null">
                report_desc,
            </if>
            <if test="submitTime != null">
                submit_time,
            </if>
            <if test="doneTime != null">
                done_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="requestId != null">
                #{requestId,jdbcType=VARCHAR},
            </if>
            <if test="channelMsgId != null">
                #{channelMsgId,jdbcType=VARCHAR},
            </if>
            <if test="channelAccountId != null">
                #{channelAccountId,jdbcType=INTEGER},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="sendTime != null">
                #{sendTime,jdbcType=INTEGER},
            </if>
            <if test="sendStatus != null">
                #{sendStatus,jdbcType=INTEGER},
            </if>
            <if test="recvSubmitTime != null">
                #{recvSubmitTime,jdbcType=INTEGER},
            </if>
            <if test="reportTime != null">
                #{reportTime,jdbcType=INTEGER},
            </if>
            <if test="reportStatus != null">
                #{reportStatus,jdbcType=INTEGER},
            </if>
            <if test="reportDesc != null">
                #{reportDesc,jdbcType=VARCHAR},
            </if>
            <if test="submitTime != null">
                #{submitTime,jdbcType=INTEGER},
            </if>
            <if test="doneTime != null">
                #{doneTime,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByServerMsgId" parameterType="com.xhqb.spectre.common.dal.entity.SmsGatewayOrderDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_sms_gateway_order
        <set>
            <if test="requestId != null">
                request_id = #{requestId,jdbcType=VARCHAR},
            </if>
            <if test="channelMsgId != null">
                channel_msg_id = #{channelMsgId,jdbcType=VARCHAR},
            </if>
            <if test="channelAccountId != null">
                channel_account_id = #{channelAccountId,jdbcType=INTEGER},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="sendTime != null">
                send_time = #{sendTime,jdbcType=INTEGER},
            </if>
            <if test="sendStatus != null">
                send_status = #{sendStatus,jdbcType=INTEGER},
            </if>
            <if test="recvSubmitTime != null">
                recv_submit_time = #{recvSubmitTime,jdbcType=INTEGER},
            </if>
            <if test="reportTime != null">
                report_time = #{reportTime,jdbcType=INTEGER},
            </if>
            <if test="reportStatus != null">
                report_status = #{reportStatus,jdbcType=INTEGER},
            </if>
            <if test="reportDesc != null">
                report_desc = #{reportDesc,jdbcType=VARCHAR},
            </if>
            <if test="submitTime != null">
                submit_time = #{submitTime,jdbcType=INTEGER},
            </if>
            <if test="doneTime != null">
                done_time = #{doneTime,jdbcType=INTEGER},
            </if>
        </set>
        where request_id = #{requestId,jdbcType=VARCHAR}
    </update>
</mapper>