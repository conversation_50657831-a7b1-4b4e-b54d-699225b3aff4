<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ParamsMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ParamsDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="value" property="value" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>

    </resultMap>
    <sql id="Base_Column_List">
        id
        , code, name, remark, creator, create_time, updater, update_time, is_delete,value,type
    </sql>
    <insert id="insertSelective">
        insert into t_test_param
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="value != null">
                value,
            </if>
            <if test="type != null">
                type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="value != null">
                #{value,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateDeleteTagByIdList">
        update t_test_param
        set is_delete = 1
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateDeleteTagByCodeList">
        update t_test_param
        set is_delete = 1
        where code in
        <foreach collection="codeList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </update>
    <select id="countCodeByQuery" resultType="java.lang.Integer">
        select count(*)
        from t_test_param
        <where>
            is_delete = 0
            <if test="query.name != null">
                and name like concat('%',#{query.name},'%')
            </if>
            <if test="query.code != null">
                and code = #{query.code}
            </if>
        </where>
        group by code
    </select>
    <select id="selectCodeByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_param
        <where>
            is_delete = 0
            <if test="query.name != null">
                and name like concat('%',#{query.name},'%')
            </if>
            <if test="query.code != null">
                and code = #{query.code}
            </if>
        </where>
        order by update_time desc
        group by code
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset}, #{query.pageParameter.pageSize}
        </if>
    </select>
    <select id="selectByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_param
        where is_delete = 0
        and code = #{code}
        order by create_time desc
        limit 1
    </select>
    <select id="selectGroupByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_param
        where is_delete = 0
        group by code
    </select>
    <select id="selectByBaseInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_param
        where is_delete = 0
        and code = #{code}
        and name = #{name}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_param
        where is_delete = 0
    </select>
    <select id="countValueByQuery" resultType="java.lang.Integer">
        select count(*)
        from t_test_param
        <where>
            is_delete = 0
            <if test="query.code != null">
                and code = #{query.code}
            </if>
            <if test="query.value != null">
                and value = #{query.value}
            </if>
        </where>
    </select>


</mapper>
