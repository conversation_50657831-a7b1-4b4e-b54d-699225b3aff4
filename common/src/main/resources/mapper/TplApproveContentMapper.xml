<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TplApproveContentMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.oa.TplApproveContent">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="flow_id" property="flowId" jdbcType="VARCHAR"/>
        <result column="content_id" property="contentId" jdbcType="VARCHAR"/>
        <result column="tpl_code" property="tplCode" jdbcType="VARCHAR"/>
        <result column="sign_id" property="signId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , flow_id, content_id, tpl_code, create_time, update_time,sign_id
    </sql>
    <insert id="insertSelective">
        insert into t_tpl_approve_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="flowId != null">
                flow_id,
            </if>
            <if test="contentId != null">
                content_id,
            </if>
            <if test="tplCode != null">
                tpl_code,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="signId != null">
                sign_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="flowId != null">
                #{flowId,jdbcType=VARCHAR},
            </if>
            <if test="contentId != null">
                #{contentId,jdbcType=VARCHAR},
            </if>
            <if test="tplCode != null">
                #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="signId != null">
                #{signId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="selectByFlowIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_approve_content
        where flow_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id asc
    </select>
    <select id="selectByContentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_approve_content
        where content_id = #{contentId} limit 1
    </select>


</mapper>
