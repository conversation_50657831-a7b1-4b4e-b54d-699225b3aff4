<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.BatchTaskMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.BatchTaskDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="app_code" property="appCode" jdbcType="VARCHAR" />
    <result column="tpl_id" property="tplId" jdbcType="INTEGER" />
    <result column="sign_id" property="signId" jdbcType="INTEGER" />
    <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR" />
    <result column="content" property="content" jdbcType="VARCHAR" />
    <result column="user_id_type" property="userIdType" jdbcType="TINYINT" />
    <result column="send_type" property="sendType" jdbcType="TINYINT" />
    <result column="send_time" property="sendTime" />
    <result column="file_info_list" property="fileInfoList" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="total_count" property="totalCount" jdbcType="INTEGER" />
    <result column="sent_count" property="sentCount" jdbcType="INTEGER" />
    <result column="real_send_count" property="realSendCount" jdbcType="INTEGER" />
    <result column="send_start_time" property="sendStartTime" />
    <result column="send_end_time" property="sendEndTime" />
    <result column="order_id" property="orderId" jdbcType="VARCHAR" />
    <result column="version" property="version" jdbcType="INTEGER" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updater" property="updater" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_delete" property="isDelete" jdbcType="TINYINT" />
    <result column="tpl_type" property="tplType" />
    <result column="bigdata_id" property="bigdataId" />
    <result column="project_desc" property="projectDesc" />
    <result column="user_group" property="userGroup" />
    <result column="limit_rate" property="limitRate" />
    <association property="sign" javaType="com.xhqb.spectre.common.dal.entity.SignDO">
      <id column="sign_id" property="id"/>
      <result column="sign_name" property="name"/>
    </association>
  </resultMap>

  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, app_code, tpl_id, sign_id, sms_type_code, content, user_id_type, send_type, send_time, 
    file_info_list, remark, status, total_count, sent_count, real_send_count, send_start_time, send_end_time,
    order_id, version, creator, create_time, updater, update_time, is_delete, tpl_type, bigdata_id, project_desc, user_group, limit_rate
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_batch_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_batch_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_task (id, app_code, tpl_id, 
      sign_id, sms_type_code, content, 
      user_id_type, send_type, send_time, 
      file_info_list, remark, status, 
      total_count, sent_count, real_send_count, send_start_time,
      send_end_time, order_id, version, 
      creator, create_time, updater, 
      update_time, is_delete, tpl_type, bigdata_id, project_desc, user_group, limit_rate)
    values (#{id,jdbcType=INTEGER}, #{appCode,jdbcType=VARCHAR}, #{tplId,jdbcType=INTEGER}, 
      #{signId,jdbcType=INTEGER}, #{smsTypeCode,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, 
      #{userIdType,jdbcType=TINYINT}, #{sendType,jdbcType=TINYINT}, #{sendTime},
      #{fileInfoList,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{totalCount,jdbcType=INTEGER}, #{sentCount,jdbcType=INTEGER}, #{realSendCount,jdbcType=INTEGER}, #{sendStartTime},
      #{sendEndTime}, #{orderId,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER},
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT}, #{tplType}, #{bigdataId}, #{projectDesc}, #{userGroup}, #{limitRate})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskDO" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_task
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="appCode != null" >
        app_code,
      </if>
      <if test="tplId != null" >
        tpl_id,
      </if>
      <if test="signId != null" >
        sign_id,
      </if>
      <if test="smsTypeCode != null" >
        sms_type_code,
      </if>
      <if test="content != null" >
        content,
      </if>
      <if test="userIdType != null" >
        user_id_type,
      </if>
      <if test="sendType != null" >
        send_type,
      </if>
      <if test="sendTime != null" >
        send_time,
      </if>
      <if test="fileInfoList != null" >
        file_info_list,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="totalCount != null" >
        total_count,
      </if>
      <if test="sentCount != null" >
        sent_count,
      </if>
      <if test="realSendCount != null" >
        real_send_count,
      </if>
      <if test="sendStartTime != null" >
        send_start_time,
      </if>
      <if test="sendEndTime != null" >
        send_end_time,
      </if>
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updater != null" >
        updater,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="isDelete != null" >
        is_delete,
      </if>
      <if test="tplType != null" >
        tpl_type,
      </if>
      <if test="bigdataId != null" >
        bigdata_id,
      </if>
      <if test="projectDesc != null" >
        project_desc,
      </if>
      <if test="userGroup != null" >
        user_group,
      </if>
      <if test="limitRate != null" >
        limit_rate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="appCode != null" >
        #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="tplId != null" >
        #{tplId,jdbcType=INTEGER},
      </if>
      <if test="signId != null" >
        #{signId,jdbcType=INTEGER},
      </if>
      <if test="smsTypeCode != null" >
        #{smsTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="userIdType != null" >
        #{userIdType,jdbcType=TINYINT},
      </if>
      <if test="sendType != null" >
        #{sendType,jdbcType=TINYINT},
      </if>
      <if test="sendTime != null" >
        #{sendTime},
      </if>
      <if test="fileInfoList != null" >
        #{fileInfoList,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="totalCount != null" >
        #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="sentCount != null" >
        #{sentCount,jdbcType=INTEGER},
      </if>
      <if test="realSendCount != null" >
        #{realSendCount,jdbcType=INTEGER},
      </if>
      <if test="sendStartTime != null" >
        #{sendStartTime},
      </if>
      <if test="sendEndTime != null" >
        #{sendEndTime},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="version != null" >
        #{version,jdbcType=INTEGER},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="tplType != null" >
        #{tplType},
      </if>
      <if test="bigdataId != null" >
        #{bigdataId},
      </if>
      <if test="projectDesc != null" >
        #{projectDesc},
      </if>
      <if test="userGroup != null" >
        #{userGroup},
      </if>
      <if test="limitRate != null" >
        #{limitRate},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_task
    <set >
      <if test="appCode != null" >
        app_code = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="tplId != null" >
        tpl_id = #{tplId,jdbcType=INTEGER},
      </if>
      <if test="signId != null" >
        sign_id = #{signId,jdbcType=INTEGER},
      </if>
      <if test="smsTypeCode != null" >
        sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="userIdType != null" >
        user_id_type = #{userIdType,jdbcType=TINYINT},
      </if>
      <if test="sendType != null" >
        send_type = #{sendType,jdbcType=TINYINT},
      </if>
      <if test="sendTime != null" >
        send_time = #{sendTime},
      </if>
      <if test="fileInfoList != null" >
        file_info_list = #{fileInfoList,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="totalCount != null" >
        total_count = #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="sentCount != null" >
        sent_count = #{sentCount,jdbcType=INTEGER},
      </if>
      <if test="realSendCount != null" >
        real_send_count = #{realSendCount,jdbcType=INTEGER},
      </if>
      <if test="sendStartTime != null" >
        send_start_time = #{sendStartTime},
      </if>
      <if test="sendEndTime != null" >
        send_end_time = #{sendEndTime},
      </if>
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="tplType != null" >
        tpl_type = #{tplType},
      </if>
      <if test="bigdataId != null" >
        bigdata_id = #{bigdataId},
      </if>
      <if test="projectDesc != null" >
        project_desc = #{projectDesc},
      </if>
      <if test="userGroup != null" >
        user_group = #{userGroup},
      </if>
      <if test="limitRate != null" >
        limit_rate = #{limitRate},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_task
    set app_code = #{appCode,jdbcType=VARCHAR},
      tpl_id = #{tplId,jdbcType=INTEGER},
      sign_id = #{signId,jdbcType=INTEGER},
      sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      user_id_type = #{userIdType,jdbcType=TINYINT},
      send_type = #{sendType,jdbcType=TINYINT},
      send_time = #{sendTime},
      file_info_list = #{fileInfoList,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      total_count = #{totalCount,jdbcType=INTEGER},
      sent_count = #{sentCount,jdbcType=INTEGER},
      real_send_count = #{realSendCount,jdbcType=INTEGER},
      send_start_time = #{sendStartTime},
      send_end_time = #{sendEndTime},
      order_id = #{orderId,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=TINYINT},
      tpl_type = #{tplType},
      bigdata_id = #{bigdataId},
      project_desc = #{projectDesc},
      user_group = #{userGroup},
      limit_rate = #{limitRate}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.BatchTaskQuery" resultType="java.lang.Integer">
    select count(*)
    from t_batch_task bt join t_sign sg
    on bt.sign_id = sg.id
    <where>
      <if test="taskId != null">
        and bt.id = #{taskId}
      </if>
      <if test="tplId != null">
        and bt.tpl_id = #{tplId}
      </if>
      <if test="smsTypeCode != null">
        and bt.sms_type_code = #{smsTypeCode}
      </if>
      <if test="signName != null">
        and sg.name = #{signName}
      </if>
      <if test="status != null">
        and bt.status = #{status}
      </if>
      <if test="content != null">
        and bt.content like concat('%', #{content}, '%')
      </if>
      and bt.is_delete=0
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.BatchTaskQuery" resultMap="BaseResultMap">
    select
    bt.id, bt.app_code, bt.tpl_id, bt.sign_id, bt.sms_type_code, bt.content, bt.user_id_type, bt.send_type, bt.send_time,
    bt.file_info_list, bt.remark, bt.status, bt.total_count, bt.sent_count, bt.real_send_count, bt.send_start_time, bt.send_end_time,
    bt.order_id, bt.version, bt.creator, bt.create_time, bt.updater, bt.update_time, bt.bigdata_id, bt.project_desc, bt.user_group,
    sg.name as 'sign_name'
    from t_batch_task bt join t_sign sg
    on bt.sign_id = sg.id
    <where>
      <if test="taskId != null">
        and bt.id = #{taskId}
      </if>
      <if test="tplId != null">
        and bt.tpl_id = #{tplId}
      </if>
      <if test="smsTypeCode != null">
        and bt.sms_type_code = #{smsTypeCode}
      </if>
      <if test="signName != null">
        and sg.name = #{signName}
      </if>
      <if test="status != null">
        and bt.status = #{status}
      </if>
      <if test="content != null">
        and bt.content like concat('%', #{content}, '%')
      </if>
      and bt.is_delete=0
    </where>
    order by bt.update_time desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>

  <update id="updateByPrimaryKeyWithVersion" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskDO" >
    update t_batch_task
    <set >
      <if test="appCode != null" >
        app_code = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="tplId != null" >
        tpl_id = #{tplId,jdbcType=INTEGER},
      </if>
      <if test="signId != null" >
        sign_id = #{signId,jdbcType=INTEGER},
      </if>
      <if test="smsTypeCode != null" >
        sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="userIdType != null" >
        user_id_type = #{userIdType,jdbcType=TINYINT},
      </if>
      <if test="sendType != null" >
        send_type = #{sendType,jdbcType=TINYINT},
      </if>
      <if test="sendTime != null" >
        send_time = #{sendTime},
      </if>
      <if test="fileInfoList != null" >
        file_info_list = #{fileInfoList,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="totalCount != null" >
        total_count = #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="sentCount != null" >
        sent_count = #{sentCount,jdbcType=INTEGER},
      </if>
      <if test="realSendCount != null" >
        real_send_count = #{realSendCount,jdbcType=INTEGER},
      </if>
      <if test="sendStartTime != null" >
        send_start_time = #{sendStartTime},
      </if>
      <if test="sendEndTime != null" >
        send_end_time = #{sendEndTime},
      </if>
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="projectDesc != null" >
        project_desc = #{projectDesc},
      </if>
      <if test="userGroup != null" >
        user_group = #{userGroup},
      </if>
      <if test="limitRate != null" >
        limit_rate = #{limitRate},
      </if>
      version = version+1,
    </set>
    where id = #{id,jdbcType=INTEGER} and version = #{version,jdbcType=INTEGER}
  </update>

  <update id="updateSentCountByIdWithVersion" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskDO" >
    update t_batch_task
    <set >
      <if test="appCode != null" >
        app_code = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="tplId != null" >
        tpl_id = #{tplId,jdbcType=INTEGER},
      </if>
      <if test="signId != null" >
        sign_id = #{signId,jdbcType=INTEGER},
      </if>
      <if test="smsTypeCode != null" >
        sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="userIdType != null" >
        user_id_type = #{userIdType,jdbcType=TINYINT},
      </if>
      <if test="sendType != null" >
        send_type = #{sendType,jdbcType=TINYINT},
      </if>
      <if test="sendTime != null" >
        send_time = #{sendTime},
      </if>
      <if test="fileInfoList != null" >
        file_info_list = #{fileInfoList,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="totalCount != null" >
        total_count = #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="sentCount != null" >
        sent_count = sent_count + #{sentCount,jdbcType=INTEGER},
      </if>
      <if test="realSendCount != null" >
        real_send_count = real_send_count + #{realSendCount,jdbcType=INTEGER},
      </if>
      <if test="sendStartTime != null" >
        send_start_time = #{sendStartTime},
      </if>
      <if test="sendEndTime != null" >
        send_end_time = #{sendEndTime},
      </if>
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      version = version+1,
    </set>
    where id = #{id,jdbcType=INTEGER} and version = #{version,jdbcType=INTEGER}
  </update>

  <update id="updateByStatusWithId">
    update t_batch_task
    <set >
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="sendStartTime != null" >
        send_start_time = #{sendStartTime},
      </if>
      version = version+1,
    </set>
    where id = #{id,jdbcType=INTEGER} and status = #{queryStatus}
  </update>

  <select id="selectByIdList" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from t_batch_task
    where id in
    <foreach collection="idList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

</mapper>