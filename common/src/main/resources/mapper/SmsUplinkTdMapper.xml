<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.SmsUplinkTdMapper">

    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.SmsUplinkTdDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderId" column="order_id" jdbcType="BIGINT"/>
            <result property="resend" column="resend" jdbcType="INTEGER"/>
            <result property="mobileEnc" column="mobile_enc" jdbcType="VARCHAR"/>
            <result property="mobileHash" column="mobile_hash" jdbcType="VARCHAR"/>
            <result property="sendTime" column="send_time" jdbcType="INTEGER"/>
            <result property="replyTime" column="reply_time" jdbcType="INTEGER"/>
            <result property="replyContent" column="reply_content" jdbcType="VARCHAR"/>
            <result property="channelCode" column="channel_code" jdbcType="VARCHAR"/>
            <result property="channelAccountId" column="channel_account_id" jdbcType="INTEGER"/>
            <result property="replyType" column="reply_type" jdbcType="TINYINT"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="smsTypeCode" column="sms_type_code" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,resend,
        mobile_enc,mobile_hash,send_time,
        reply_time,reply_content,channel_code,
        channel_account_id,reply_type,mobile,
        sms_type_code,create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_sms_uplink_td
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_sms_uplink_td
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhqb.spectre.common.dal.entity.SmsUplinkTdDO" useGeneratedKeys="true">
        insert into t_sms_uplink_td
        ( id,order_id,resend
        ,mobile_enc,mobile_hash,send_time
        ,reply_time,reply_content,channel_code
        ,channel_account_id,reply_type,mobile
        ,sms_type_code,create_time,update_time
        )
        values (#{id,jdbcType=BIGINT},#{orderId,jdbcType=BIGINT},#{resend,jdbcType=INTEGER}
        ,#{mobileEnc,jdbcType=VARCHAR},#{mobileHash,jdbcType=VARCHAR},#{sendTime,jdbcType=INTEGER}
        ,#{replyTime,jdbcType=INTEGER},#{replyContent,jdbcType=VARCHAR},#{channelCode,jdbcType=VARCHAR}
        ,#{channelAccountId,jdbcType=INTEGER},#{replyType,jdbcType=TINYINT},#{mobile,jdbcType=VARCHAR}
        ,#{smsTypeCode,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhqb.spectre.common.dal.entity.SmsUplinkTdDO" useGeneratedKeys="true">
        insert into t_sms_uplink_td
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="orderId != null">order_id,</if>
                <if test="resend != null">resend,</if>
                <if test="mobileEnc != null">mobile_enc,</if>
                <if test="mobileHash != null">mobile_hash,</if>
                <if test="sendTime != null">send_time,</if>
                <if test="replyTime != null">reply_time,</if>
                <if test="replyContent != null">reply_content,</if>
                <if test="channelCode != null">channel_code,</if>
                <if test="channelAccountId != null">channel_account_id,</if>
                <if test="replyType != null">reply_type,</if>
                <if test="mobile != null">mobile,</if>
                <if test="smsTypeCode != null">sms_type_code,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="orderId != null">#{orderId,jdbcType=BIGINT},</if>
                <if test="resend != null">#{resend,jdbcType=INTEGER},</if>
                <if test="mobileEnc != null">#{mobileEnc,jdbcType=VARCHAR},</if>
                <if test="mobileHash != null">#{mobileHash,jdbcType=VARCHAR},</if>
                <if test="sendTime != null">#{sendTime,jdbcType=INTEGER},</if>
                <if test="replyTime != null">#{replyTime,jdbcType=INTEGER},</if>
                <if test="replyContent != null">#{replyContent,jdbcType=VARCHAR},</if>
                <if test="channelCode != null">#{channelCode,jdbcType=VARCHAR},</if>
                <if test="channelAccountId != null">#{channelAccountId,jdbcType=INTEGER},</if>
                <if test="replyType != null">#{replyType,jdbcType=TINYINT},</if>
                <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
                <if test="smsTypeCode != null">#{smsTypeCode,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.SmsUplinkTdDO">
        update t_sms_uplink_td
        <set>
                <if test="orderId != null">
                    order_id = #{orderId,jdbcType=BIGINT},
                </if>
                <if test="resend != null">
                    resend = #{resend,jdbcType=INTEGER},
                </if>
                <if test="mobileEnc != null">
                    mobile_enc = #{mobileEnc,jdbcType=VARCHAR},
                </if>
                <if test="mobileHash != null">
                    mobile_hash = #{mobileHash,jdbcType=VARCHAR},
                </if>
                <if test="sendTime != null">
                    send_time = #{sendTime,jdbcType=INTEGER},
                </if>
                <if test="replyTime != null">
                    reply_time = #{replyTime,jdbcType=INTEGER},
                </if>
                <if test="replyContent != null">
                    reply_content = #{replyContent,jdbcType=VARCHAR},
                </if>
                <if test="channelCode != null">
                    channel_code = #{channelCode,jdbcType=VARCHAR},
                </if>
                <if test="channelAccountId != null">
                    channel_account_id = #{channelAccountId,jdbcType=INTEGER},
                </if>
                <if test="replyType != null">
                    reply_type = #{replyType,jdbcType=TINYINT},
                </if>
                <if test="mobile != null">
                    mobile = #{mobile,jdbcType=VARCHAR},
                </if>
                <if test="smsTypeCode != null">
                    sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.SmsUplinkTdDO">
        update t_sms_uplink_td
        set 
            order_id =  #{orderId,jdbcType=BIGINT},
            resend =  #{resend,jdbcType=INTEGER},
            mobile_enc =  #{mobileEnc,jdbcType=VARCHAR},
            mobile_hash =  #{mobileHash,jdbcType=VARCHAR},
            send_time =  #{sendTime,jdbcType=INTEGER},
            reply_time =  #{replyTime,jdbcType=INTEGER},
            reply_content =  #{replyContent,jdbcType=VARCHAR},
            channel_code =  #{channelCode,jdbcType=VARCHAR},
            channel_account_id =  #{channelAccountId,jdbcType=INTEGER},
            reply_type =  #{replyType,jdbcType=TINYINT},
            mobile =  #{mobile,jdbcType=VARCHAR},
            sms_type_code =  #{smsTypeCode,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <insert id="insertBatch">
        insert into t_sms_uplink_td(
        order_id, resend, mobile_enc, send_time, reply_time,
        reply_content, channel_code, channel_account_id, reply_type, sms_type_code
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.orderId,jdbcType=BIGINT},
                #{item.resend,jdbcType=INTEGER},
                #{item.mobile,jdbcType=VARCHAR},
                #{item.sendTime,jdbcType=INTEGER},
                #{item.replyTime,jdbcType=INTEGER},
                #{item.replyContent,jdbcType=VARCHAR},
                #{item.channelCode,jdbcType=VARCHAR},
                #{item.channelAccountId,jdbcType=INTEGER},
                #{item.replyType,jdbcType=TINYINT},
                #{item.smsTypeCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>
