<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.SmsOrderMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.SmsOrderDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="order_id" jdbcType="BIGINT" property="orderId"/>
        <id column="resend" jdbcType="INTEGER" property="resend"/>
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="channel_msg_id" jdbcType="VARCHAR" property="channelMsgId"/>
        <result column="app_code" jdbcType="VARCHAR" property="appCode"/>
        <result column="tpl_code" jdbcType="VARCHAR" property="tplCode"/>
        <result column="sms_type_code" jdbcType="VARCHAR" property="smsTypeCode"/>
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode"/>
        <result column="channel_account_id" jdbcType="INTEGER" property="channelAccountId"/>
        <result column="sign_name" jdbcType="VARCHAR" property="signName"/>
        <result column="mobile_enc" jdbcType="VARCHAR" property="mobile"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="slice_id" jdbcType="INTEGER" property="sliceId"/>
        <result column="province_short_name" jdbcType="VARCHAR" property="provinceShortName"/>
        <result column="city_short_name" jdbcType="VARCHAR" property="cityShortName"/>
        <result column="isp_code" jdbcType="VARCHAR" property="ispCode"/>
        <result column="batch_id" jdbcType="INTEGER" property="batchId"/>
        <result column="bill_count" jdbcType="TINYINT" property="billCount"/>
        <result column="send_type" jdbcType="TINYINT" property="sendType"/>
        <result column="req_src" jdbcType="TINYINT" property="reqSrc"/>
        <result column="send_time" jdbcType="INTEGER" property="sendTime"/>
        <result column="send_status" jdbcType="INTEGER" property="sendStatus"/>
        <result column="send_code" jdbcType="VARCHAR" property="sendCode"/>
        <result column="send_desc" jdbcType="VARCHAR" property="sendDesc"/>
        <result column="recv_submit_time" jdbcType="INTEGER" property="recvSubmitTime"/>
        <result column="report_time" jdbcType="INTEGER" property="reportTime"/>
        <result column="report_status" jdbcType="INTEGER" property="reportStatus"/>
        <result column="report_code" jdbcType="VARCHAR" property="reportCode"/>
        <result column="report_desc" jdbcType="VARCHAR" property="reportDesc"/>
        <result column="submit_time" jdbcType="INTEGER" property="submitTime"/>
        <result column="done_time" jdbcType="INTEGER" property="doneTime"/>
        <result column="parameter" jdbcType="VARCHAR" property="parameter"/>
        <result column="channel_code_set" jdbcType="VARCHAR" property="channelCodeSet"/>
        <result column="submit_resq_count" jdbcType="TINYINT" property="submitResqCount"/>
        <result column="report_count" jdbcType="TINYINT" property="reportCount"/>
        <result column="phone_status" jdbcType="BIGINT" property="phoneStatus"/>
        <result column="biz_batch_id" jdbcType="VARCHAR" property="bizBatchId"/>
        <result column="call_metis" jdbcType="INTEGER" property="callMetis"/>
        <result column="cas_version" jdbcType="INTEGER" property="casVersion"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="SmsStatResultMap" type="com.xhqb.spectre.common.dal.entity.SmsOrderStatDO">
        <result column="sms_type_code" jdbcType="VARCHAR" property="smsTypeCode"/>
        <result column="channel_account_id" jdbcType="INTEGER" property="channelAccountId"/>
        <result column="isp_code" jdbcType="VARCHAR" property="isp"/>
        <result column="total_count" jdbcType="INTEGER" property="totalCount"/>
        <result column="success_count" jdbcType="INTEGER" property="successCount"/>
        <result column="failed_count" jdbcType="INTEGER" property="failedCount"/>
        <result column="unknown_count" jdbcType="INTEGER" property="unknownCount"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        order_id, resend, request_id, channel_msg_id, app_code, tpl_code, sms_type_code,
        channel_code, channel_account_id, sign_name, mobile_enc, content, slice_id, province_short_name,
        city_short_name, isp_code, batch_id, bill_count, send_type, req_src, send_time, send_status,
        send_code, send_desc, recv_submit_time, report_time, report_status, report_code,
        report_desc, submit_time, done_time, parameter, channel_code_set, submit_resq_count,
        report_count, phone_status,biz_batch_id,call_metis, cas_version, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.SmsOrderKey"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from t_sms_order
        where order_id = #{orderId,jdbcType=BIGINT}
        and resend = #{resend,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.SmsOrderKey">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from t_sms_order
        where order_id = #{orderId,jdbcType=BIGINT}
        and resend = #{resend,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.SmsOrderDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_sms_order (order_id, resend, request_id,
        channel_msg_id, app_code, tpl_code,
        sms_type_code, channel_code, channel_account_id,
        sign_name, mobile_enc, content,
        slice_id, province_short_name, city_short_name,
        isp_code, batch_id, bill_count,
        send_type, req_src, send_time,
        send_status, send_code, send_desc,
        recv_submit_time, report_time, report_status,
        report_code, report_desc, submit_time,
        done_time, parameter, channel_code_set,
        submit_resq_count, report_count, phone_status,
        biz_batch_id,call_metis,
        cas_version, create_time, update_time
        )
        values (#{orderId,jdbcType=BIGINT}, #{resend,jdbcType=INTEGER}, #{requestId,jdbcType=VARCHAR},
        #{channelMsgId,jdbcType=VARCHAR}, #{appCode,jdbcType=VARCHAR}, #{tplCode,jdbcType=VARCHAR},
        #{smsTypeCode,jdbcType=VARCHAR}, #{channelCode,jdbcType=VARCHAR}, #{channelAccountId,jdbcType=INTEGER},
        #{signName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR},
        #{sliceId,jdbcType=INTEGER}, #{provinceShortName,jdbcType=VARCHAR}, #{cityShortName,jdbcType=VARCHAR},
        #{ispCode,jdbcType=VARCHAR}, #{batchId,jdbcType=INTEGER}, #{billCount,jdbcType=TINYINT},
        #{sendType,jdbcType=TINYINT}, #{reqSrc,jdbcType=TINYINT}, #{sendTime,jdbcType=INTEGER},
        #{sendStatus,jdbcType=INTEGER}, #{sendCode,jdbcType=VARCHAR}, #{sendDesc,jdbcType=VARCHAR},
        #{recvSubmitTime,jdbcType=INTEGER}, #{reportTime,jdbcType=INTEGER}, #{reportStatus,jdbcType=INTEGER},
        #{reportCode,jdbcType=VARCHAR}, #{reportDesc,jdbcType=VARCHAR}, #{submitTime,jdbcType=INTEGER},
        #{doneTime,jdbcType=INTEGER}, #{parameter,jdbcType=VARCHAR}, #{channelCodeSet,jdbcType=VARCHAR},
        #{submitResqCount,jdbcType=TINYINT}, #{reportCount,jdbcType=TINYINT}, #{phoneStatus,jdbcType=BIGINT},
        #{bizBatchId,jdbcType=VARCHAR}, #{callMetis,jdbcType=INTEGER},
        #{casVersion,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.SmsOrderDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_sms_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                order_id,
            </if>
            <if test="resend != null">
                resend,
            </if>
            <if test="requestId != null">
                request_id,
            </if>
            <if test="channelMsgId != null">
                channel_msg_id,
            </if>
            <if test="appCode != null">
                app_code,
            </if>
            <if test="tplCode != null">
                tpl_code,
            </if>
            <if test="smsTypeCode != null">
                sms_type_code,
            </if>
            <if test="channelCode != null">
                channel_code,
            </if>
            <if test="channelAccountId != null">
                channel_account_id,
            </if>
            <if test="signName != null">
                sign_name,
            </if>
            <if test="mobile != null">
                mobile_enc,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="sliceId != null">
                slice_id,
            </if>
            <if test="provinceShortName != null">
                province_short_name,
            </if>
            <if test="cityShortName != null">
                city_short_name,
            </if>
            <if test="ispCode != null">
                isp_code,
            </if>
            <if test="batchId != null">
                batch_id,
            </if>
            <if test="billCount != null">
                bill_count,
            </if>
            <if test="sendType != null">
                send_type,
            </if>
            <if test="reqSrc != null">
                req_src,
            </if>
            <if test="sendTime != null">
                send_time,
            </if>
            <if test="sendStatus != null">
                send_status,
            </if>
            <if test="sendCode != null">
                send_code,
            </if>
            <if test="sendDesc != null">
                send_desc,
            </if>
            <if test="recvSubmitTime != null">
                recv_submit_time,
            </if>
            <if test="reportTime != null">
                report_time,
            </if>
            <if test="reportStatus != null">
                report_status,
            </if>
            <if test="reportCode != null">
                report_code,
            </if>
            <if test="reportDesc != null">
                report_desc,
            </if>
            <if test="submitTime != null">
                submit_time,
            </if>
            <if test="doneTime != null">
                done_time,
            </if>
            <if test="parameter != null">
                parameter,
            </if>
            <if test="channelCodeSet != null">
                channel_code_set,
            </if>
            <if test="submitResqCount != null">
                submit_resq_count,
            </if>
            <if test="reportCount != null">
                report_count,
            </if>
            <if test="phoneStatus != null">
                phone_status,
            </if>
            <if test="bizBatchId != null">
                biz_batch_id,
            </if>
            <if test="callMetis != null">
                call_metis,
            </if>
            <if test="casVersion != null">
                cas_version,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="resend != null">
                #{resend,jdbcType=INTEGER},
            </if>
            <if test="requestId != null">
                #{requestId,jdbcType=VARCHAR},
            </if>
            <if test="channelMsgId != null">
                #{channelMsgId,jdbcType=VARCHAR},
            </if>
            <if test="appCode != null">
                #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="tplCode != null">
                #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null">
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="channelAccountId != null">
                #{channelAccountId,jdbcType=INTEGER},
            </if>
            <if test="signName != null">
                #{signName,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="sliceId != null">
                #{sliceId,jdbcType=INTEGER},
            </if>
            <if test="provinceShortName != null">
                #{provinceShortName,jdbcType=VARCHAR},
            </if>
            <if test="cityShortName != null">
                #{cityShortName,jdbcType=VARCHAR},
            </if>
            <if test="ispCode != null">
                #{ispCode,jdbcType=VARCHAR},
            </if>
            <if test="batchId != null">
                #{batchId,jdbcType=INTEGER},
            </if>
            <if test="billCount != null">
                #{billCount,jdbcType=TINYINT},
            </if>
            <if test="sendType != null">
                #{sendType,jdbcType=TINYINT},
            </if>
            <if test="reqSrc != null">
                #{reqSrc,jdbcType=TINYINT},
            </if>
            <if test="sendTime != null">
                #{sendTime,jdbcType=INTEGER},
            </if>
            <if test="sendStatus != null">
                #{sendStatus,jdbcType=INTEGER},
            </if>
            <if test="sendCode != null">
                #{sendCode,jdbcType=VARCHAR},
            </if>
            <if test="sendDesc != null">
                #{sendDesc,jdbcType=VARCHAR},
            </if>
            <if test="recvSubmitTime != null">
                #{recvSubmitTime,jdbcType=INTEGER},
            </if>
            <if test="reportTime != null">
                #{reportTime,jdbcType=INTEGER},
            </if>
            <if test="reportStatus != null">
                #{reportStatus,jdbcType=INTEGER},
            </if>
            <if test="reportCode != null">
                #{reportCode,jdbcType=VARCHAR},
            </if>
            <if test="reportDesc != null">
                #{reportDesc,jdbcType=VARCHAR},
            </if>
            <if test="submitTime != null">
                #{submitTime,jdbcType=INTEGER},
            </if>
            <if test="doneTime != null">
                #{doneTime,jdbcType=INTEGER},
            </if>
            <if test="parameter != null">
                #{parameter,jdbcType=VARCHAR},
            </if>
            <if test="channelCodeSet != null">
                #{channelCodeSet,jdbcType=VARCHAR},
            </if>
            <if test="submitResqCount != null">
                #{submitResqCount,jdbcType=TINYINT},
            </if>
            <if test="reportCount != null">
                #{reportCount,jdbcType=TINYINT},
            </if>
            <if test="phoneStatus != null">
                #{phoneStatus,jdbcType=BIGINT},
            </if>
            <if test="bizBatchId != null">
                #{bizBatchId,jdbcType=VARCHAR},
            </if>
            <if test="callMetis != null">
                #{callMetis,jdbcType=INTEGER},
            </if>
            <if test="casVersion != null">
                #{casVersion,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.SmsOrderDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_sms_order
        <set>
            <if test="requestId != null">
                request_id = #{requestId,jdbcType=VARCHAR},
            </if>
            <if test="channelMsgId != null">
                channel_msg_id = #{channelMsgId,jdbcType=VARCHAR},
            </if>
            <if test="appCode != null">
                app_code = #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="tplCode != null">
                tpl_code = #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null">
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="channelAccountId != null">
                channel_account_id = #{channelAccountId,jdbcType=INTEGER},
            </if>
            <if test="signName != null">
                sign_name = #{signName,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile_enc = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="sliceId != null">
                slice_id = #{sliceId,jdbcType=INTEGER},
            </if>
            <if test="provinceShortName != null">
                province_short_name = #{provinceShortName,jdbcType=VARCHAR},
            </if>
            <if test="cityShortName != null">
                city_short_name = #{cityShortName,jdbcType=VARCHAR},
            </if>
            <if test="ispCode != null">
                isp_code = #{ispCode,jdbcType=VARCHAR},
            </if>
            <if test="batchId != null">
                batch_id = #{batchId,jdbcType=INTEGER},
            </if>
            <if test="billCount != null">
                bill_count = #{billCount,jdbcType=TINYINT},
            </if>
            <if test="sendType != null">
                send_type = #{sendType,jdbcType=TINYINT},
            </if>
            <if test="reqSrc != null">
                req_src = #{reqSrc,jdbcType=TINYINT},
            </if>
            <if test="sendTime != null">
                send_time = #{sendTime,jdbcType=INTEGER},
            </if>
            <if test="sendStatus != null">
                send_status = #{sendStatus,jdbcType=INTEGER},
            </if>
            <if test="sendCode != null">
                send_code = #{sendCode,jdbcType=VARCHAR},
            </if>
            <if test="sendDesc != null">
                send_desc = #{sendDesc,jdbcType=VARCHAR},
            </if>
            <if test="recvSubmitTime != null">
                recv_submit_time = #{recvSubmitTime,jdbcType=INTEGER},
            </if>
            <if test="reportTime != null">
                report_time = #{reportTime,jdbcType=INTEGER},
            </if>
            <if test="reportStatus != null">
                report_status = #{reportStatus,jdbcType=INTEGER},
            </if>
            <if test="reportCode != null">
                report_code = #{reportCode,jdbcType=VARCHAR},
            </if>
            <if test="reportDesc != null">
                report_desc = #{reportDesc,jdbcType=VARCHAR},
            </if>
            <if test="submitTime != null">
                submit_time = #{submitTime,jdbcType=INTEGER},
            </if>
            <if test="doneTime != null">
                done_time = #{doneTime,jdbcType=INTEGER},
            </if>
            <if test="parameter != null">
                parameter = #{parameter,jdbcType=VARCHAR},
            </if>
            <if test="channelCodeSet != null">
                channel_code_set = #{channelCodeSet,jdbcType=VARCHAR},
            </if>
            <if test="submitResqCount != null">
                submit_resq_count = #{submitResqCount,jdbcType=TINYINT},
            </if>
            <if test="reportCount != null">
                report_count = #{reportCount,jdbcType=TINYINT},
            </if>
            <if test="phoneStatus != null">
                phone_status = #{phoneStatus,jdbcType=BIGINT},
            </if>
            <if test="bizBatchId != null">
                biz_batch_id = #{bizBatchId ,jdbcType=VARCHAR},
            </if>
            <if test="callMetis != null">
                call_metis = #{callMetis,jdbcType=INTEGER},
            </if>
            <if test="casVersion != null">
                cas_version = #{casVersion,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where order_id = #{orderId,jdbcType=BIGINT}
        and resend = #{resend,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.SmsOrderDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_sms_order
        set request_id = #{requestId,jdbcType=VARCHAR},
        channel_msg_id = #{channelMsgId,jdbcType=VARCHAR},
        app_code = #{appCode,jdbcType=VARCHAR},
        tpl_code = #{tplCode,jdbcType=VARCHAR},
        sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
        channel_code = #{channelCode,jdbcType=VARCHAR},
        channel_account_id = #{channelAccountId,jdbcType=INTEGER},
        sign_name = #{signName,jdbcType=VARCHAR},
        mobile_enc = #{mobile,jdbcType=VARCHAR},
        content = #{content,jdbcType=VARCHAR},
        slice_id = #{sliceId,jdbcType=INTEGER},
        province_short_name = #{provinceShortName,jdbcType=VARCHAR},
        city_short_name = #{cityShortName,jdbcType=VARCHAR},
        isp_code = #{ispCode,jdbcType=VARCHAR},
        batch_id = #{batchId,jdbcType=INTEGER},
        bill_count = #{billCount,jdbcType=TINYINT},
        send_type = #{sendType,jdbcType=TINYINT},
        req_src = #{reqSrc,jdbcType=TINYINT},
        send_time = #{sendTime,jdbcType=INTEGER},
        send_status = #{sendStatus,jdbcType=INTEGER},
        send_code = #{sendCode,jdbcType=VARCHAR},
        send_desc = #{sendDesc,jdbcType=VARCHAR},
        recv_submit_time = #{recvSubmitTime,jdbcType=INTEGER},
        report_time = #{reportTime,jdbcType=INTEGER},
        report_status = #{reportStatus,jdbcType=INTEGER},
        report_code = #{reportCode,jdbcType=VARCHAR},
        report_desc = #{reportDesc,jdbcType=VARCHAR},
        submit_time = #{submitTime,jdbcType=INTEGER},
        done_time = #{doneTime,jdbcType=INTEGER},
        parameter = #{parameter,jdbcType=VARCHAR},
        channel_code_set = #{channelCodeSet,jdbcType=VARCHAR},
        submit_resq_count = #{submitResqCount,jdbcType=TINYINT},
        report_count = #{reportCount,jdbcType=TINYINT},
        phone_status = #{phoneStatus,jdbcType=BIGINT},
        biz_batch_id = #{bizBatchId,jdbcType=VARCHAR},
        call_metis = #{callMetis,jdbcType=INTEGER},
        cas_version = #{casVersion,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where order_id = #{orderId,jdbcType=BIGINT}
        and resend = #{resend,jdbcType=INTEGER}
    </update>

    <select id="selectByAppCodeAndOutOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_sms_order
        where app_code = #{appCode,jdbcType=VARCHAR} and request_id = #{outOrderId, jdbcType=VARCHAR}
    </select>

    <select id="selectByOrderId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_sms_order
        where order_id = #{orderId, jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="selectByRequestId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_sms_order
        where request_id = #{requestId, jdbcType=VARCHAR}
        order by create_time desc limit 1
    </select>

    <select id="selectByRequestIds" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_sms_order
        where request_id in
        <foreach collection="list" item="requestId" index="index" open="(" close=")" separator=",">
            #{requestId, jdbcType=VARCHAR}
        </foreach>
    </select>

    <insert id="insertOrUpdateBatch" parameterType="com.xhqb.spectre.common.dal.entity.SmsOrderDO">
        insert into t_sms_order(
        order_id, req_src, request_id, app_code, tpl_code, sms_type_code, channel_code, channel_account_id, sign_name,
        mobile_enc, content, slice_id, province_short_name, city_short_name, isp_code, batch_id, bill_count,
        send_type, resend, send_time, parameter, phone_status,biz_batch_id,call_metis, cas_version
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.orderId,jdbcType=BIGINT},
            #{item.reqSrc,jdbcType=TINYINT},
            #{item.requestId,jdbcType=VARCHAR},
            #{item.appCode,jdbcType=VARCHAR},
            #{item.tplCode,jdbcType=VARCHAR},
            #{item.smsTypeCode,jdbcType=VARCHAR},
            #{item.channelCode,jdbcType=VARCHAR},
            #{item.channelAccountId,jdbcType=INTEGER},
            #{item.signName,jdbcType=VARCHAR},
            #{item.mobile,jdbcType=VARCHAR},
            #{item.content,jdbcType=VARCHAR},
            #{item.sliceId,jdbcType=INTEGER},
            #{item.provinceShortName,jdbcType=VARCHAR},
            #{item.cityShortName,jdbcType=VARCHAR},
            #{item.ispCode,jdbcType=VARCHAR},
            #{item.batchId,jdbcType=INTEGER},
            #{item.billCount,jdbcType=TINYINT},
            #{item.sendType,jdbcType=TINYINT},
            #{item.resend,jdbcType=INTEGER},
            #{item.sendTime,jdbcType=INTEGER},
            #{item.parameter,jdbcType=VARCHAR},
            #{item.phoneStatus,jdbcType=BIGINT},
            #{item.bizBatchId,jdbcType=VARCHAR},
            #{item.callMetis,jdbcType=INTEGER},
            1
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        request_id = values (request_id),
        req_src = values (req_src),
        app_code = values (app_code),
        tpl_code = values (tpl_code),
        sms_type_code = values (sms_type_code),
        channel_code = values (channel_code),
        channel_account_id = values (channel_account_id),
        sign_name = values (sign_name),
        mobile_enc = values (mobile_enc),
        content = values (content),
        slice_id = values (slice_id),
        province_short_name = values (province_short_name),
        city_short_name = values (city_short_name),
        isp_code = values (isp_code),
        batch_id = values (batch_id),
        bill_count = values (bill_count),
        send_type = values (send_type),
        send_time = values (send_time),
        parameter = values (parameter),
        phone_status = values (phone_status),
        biz_batch_id = values (biz_batch_id),
        call_metis = values (call_metis),
        cas_version = cas_version + 1
    </insert>

    <insert id="insertOrUpdateBatchSubmitCount" parameterType="com.xhqb.spectre.common.dal.entity.SmsOrderDO">
        insert into t_sms_order(
        order_id, resend, channel_msg_id, send_status, send_code, send_desc, recv_submit_time,
        submit_resq_count,cas_version
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.orderId,jdbcType=BIGINT},
            #{item.resend,jdbcType=INTEGER},
            #{item.channelMsgId,jdbcType=VARCHAR},
            #{item.sendStatus,jdbcType=INTEGER},
            #{item.sendCode,jdbcType=VARCHAR},
            #{item.sendDesc,jdbcType=VARCHAR},
            #{item.recvSubmitTime,jdbcType=INTEGER},
            1,
            1
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        channel_msg_id = values (channel_msg_id),
        send_status = values (send_status),
        send_code = values (send_code),
        send_desc = values (send_desc),
        recv_submit_time = values (recv_submit_time),
        submit_resq_count = submit_resq_count + 1,
        cas_version = cas_version + 1
    </insert>

    <insert id="insertOrUpdateBatchReportCount" parameterType="com.xhqb.spectre.common.dal.entity.SmsOrderDO">
        insert into t_sms_order(
        order_id, resend, report_time, report_status, report_code, report_desc, submit_time, done_time,
        report_count,cas_version
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.orderId,jdbcType=BIGINT},
            #{item.resend,jdbcType=INTEGER},
            #{item.reportTime,jdbcType=INTEGER},
            #{item.reportStatus,jdbcType=INTEGER},
            #{item.reportCode,jdbcType=VARCHAR},
            #{item.reportDesc,jdbcType=VARCHAR},
            #{item.submitTime,jdbcType=INTEGER},
            #{item.doneTime,jdbcType=INTEGER},
            1,
            1
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        report_time = values (report_time),
        report_status = values (report_status),
        report_code = values (report_code),
        report_desc = values (report_desc),
        submit_time = values (submit_time),
        done_time = values (done_time),
        report_count = report_count + 1,
        cas_version = cas_version + 1
    </insert>

    <select id="batchTestQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_sms_order
        where batch_id = #{batchId} and request_id like 'batch_%'
        and send_status = 0
        limit 1
    </select>

    <select id="selectByRequestIdsAndAppCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_sms_order
        where app_code = #{appCode} and request_id in
        <foreach collection="requestIdList" item="requestId" index="index" open="(" close=")" separator=",">
            #{requestId, jdbcType=VARCHAR}
        </foreach>
        and send_time <![CDATA[ >= ]]> #{nDaysAgoEarliestTimestamp}
        and send_time <![CDATA[ < ]]> #{currentDayLatestTimestamp}
    </select>

    <select id="selectStatByChannel" resultMap="SmsStatResultMap">
        select sms_type_code,
               channel_account_id,
               count(*)                                                                 as total_count,
               sum(case when report_status = 0 then 1 else 0 end)                       as success_count,
               sum(case when report_status <![CDATA[ > ]]> 0 or send_status <![CDATA[ > ]]> 0 then 1 else 0 end)    as failed_count,
               sum(case when report_status = -1 and send_status <![CDATA[ <= ]]> 0 then 1 else 0 end) as unknown_count
        from t_sms_order
        where send_time <![CDATA[ >= ]]> #{beginTime}
          and send_time <![CDATA[ < ]]> #{endTime}
          and sms_type_code <![CDATA[ <> ]]> ''
        group by channel_account_id;
    </select>

    <select id="selectStatByIsp" resultMap="SmsStatResultMap">
        select sms_type_code,
               channel_account_id,
               isp_code,
               count(*)                                                                 as total_count,
               sum(case when report_status = 0 then 1 else 0 end)                       as success_count,
               sum(case when report_status <![CDATA[ > ]]> 0 or send_status <![CDATA[ > ]]> 0 then 1 else 0 end)    as failed_count,
               sum(case when report_status = -1 and send_status <![CDATA[ <= ]]> 0 then 1 else 0 end) as unknown_count
        from t_sms_order
        where send_time <![CDATA[ >= ]]> #{beginTime}
          and send_time <![CDATA[ < ]]> #{endTime}
          and sms_type_code <![CDATA[ <> ]]> ''
        group by channel_account_id, isp_code;
    </select>

    <select id="selectMarketStatByChannel" resultMap="SmsStatResultMap">
        select sms_type_code,
               channel_account_id,
               count(*)                                                                 as total_count,
               sum(case when report_status = 0 then 1 else 0 end)                       as success_count,
               sum(case when report_status <![CDATA[ > ]]> 0 or send_status <![CDATA[ > ]]> 0 then 1 else 0 end)    as failed_count,
               sum(case when report_status = -1 and send_status <![CDATA[ <= ]]> 0 then 1 else 0 end) as unknown_count
        from t_sms_order
        where send_time <![CDATA[ >= ]]> #{beginTime}
          and send_time <![CDATA[ < ]]> #{endTime}
          and sms_type_code = 'market'
        group by channel_account_id;
    </select>

    <select id="selectMarketStatByIsp" resultMap="SmsStatResultMap">
        select sms_type_code,
               channel_account_id,
               isp_code,
               count(*)                                                                 as total_count,
               sum(case when report_status = 0 then 1 else 0 end)                       as success_count,
               sum(case when report_status <![CDATA[ > ]]> 0 or send_status <![CDATA[ > ]]> 0 then 1 else 0 end)    as failed_count,
               sum(case when report_status = -1 and send_status <![CDATA[ <= ]]> 0 then 1 else 0 end) as unknown_count
        from t_sms_order
        where send_time <![CDATA[ >= ]]> #{beginTime}
          and send_time <![CDATA[ < ]]> #{endTime}
          and sms_type_code = 'market'
        group by channel_account_id, isp_code;
    </select>

    <select id="selectOrderStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_sms_order
        <where>
            <if test="requestId != null and requestId != ''">and request_id = #{requestId}</if>
            <if test="appCode != null and appCode != ''">and app_code = #{appCode}</if>
            <if test="tplCode != null and tplCode != ''">and tpl_code = #{tplCode}</if>
            and mobile_enc in
            <foreach collection="mobiles" item="mobile" index="index" open="(" close=")" separator=",">
                #{mobile, jdbcType=VARCHAR}
            </foreach>
            and send_time <![CDATA[ >= ]]> #{nDaysAgoEarliestTimestamp}
            and send_time <![CDATA[ < ]]> #{currentDayLatestTimestamp}
        </where>
    </select>
    <select id="selectByReissueOrderQuery" resultMap="BaseResultMap"
            parameterType="com.xhqb.spectre.common.dal.query.ReissueOrderQuery">
        select
        <include refid="Base_Column_List"/>
        from t_sms_order
        where mobile_enc =#{query.mobile}
        and sms_type_code !='verify'
        and send_time <![CDATA[ >= ]]> #{nDaysAgoEarliestTimestamp}
        and send_time <![CDATA[ < ]]> #{currentDayLatestTimestamp}
        order by send_time desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset}, #{query.pageParameter.pageSize}
        </if>
    </select>

    <select id="countByReissueOrderQuery" resultType="java.lang.Integer"
            parameterType="com.xhqb.spectre.common.dal.query.ReissueOrderQuery">
        select count(*)
        from t_sms_order
        where mobile_enc = #{query.mobile}
          and sms_type_code !='verify'
          and send_time <![CDATA[ >= ]]> #{nDaysAgoEarliestTimestamp}
          and send_time <![CDATA[ < ]]> #{currentDayLatestTimestamp}
    </select>

    <select id="selectQueryTestStats" resultType="com.xhqb.spectre.common.dal.dto.result.QueryTestStatResult">
        select count(distinct order_id) as 'totalCount', sum(case report_status when 0 then 1 else 0 end) as 'successCount'
        from t_sms_order
        where send_status = 0
          and send_time <![CDATA[ >= ]]> #{sendStartTime}
          and send_time <![CDATA[ <= ]]> #{sendEndTime}
          and sms_type_code = #{smsTypeCode}
          and channel_account_id = #{channelAccountId}
    </select>

    <select id="selectDebtSmsReportOrderStats"
            resultType="com.xhqb.spectre.common.dal.dto.result.DebtSmsReportOrderResult">
        select
        count(distinct order_id) as 'orderAmount',
        sum(CASE WHEN send_status = 0 THEN 1 ELSE 0 END) as 'successAmount',
        tpl_code as 'tplCode',
        biz_batch_id as 'bizBatchId'
        from t_sms_order
        where send_time <![CDATA[ >= ]]> #{beginSendTime}
        and send_time  <![CDATA[ <= ]]> #{endSendTime}
        and tpl_code in
        <foreach collection="debtSmsReportList" item="debtSmsReport" index="index" open="(" close=")" separator=",">
            #{debtSmsReport.tplCode}
        </foreach>
        and biz_batch_id in
        <foreach collection="debtSmsReportList" item="debtSmsReport" index="index" open="(" close=")" separator=",">
            #{debtSmsReport.bizBatchId}
        </foreach>
        group by tpl_code, biz_batch_id
    </select>
    <select id="selectByTplCodeAndDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_sms_order
        where tpl_code = #{tplCode}
        and send_time >= #{startTime}
        and send_time <![CDATA[<=]]> #{endTime}
        limit #{count}
    </select>
</mapper>