<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.UserShortUrlDOMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.UserShortUrlDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="short_url" jdbcType="VARCHAR" property="shortUrl"/>
        <result column="short_code" jdbcType="VARCHAR" property="shortCode"/>
        <result column="batch_id" jdbcType="VARCHAR" property="batchId"/>
        <result column="cid" jdbcType="VARCHAR" property="cid"/>
        <result column="mobile_enc" jdbcType="VARCHAR" property="mobile"/>
        <result column="long_url" jdbcType="VARCHAR" property="longUrl"/>
        <result column="tpl_code" jdbcType="VARCHAR" property="tplCode"/>
        <result column="expired_date" jdbcType="VARCHAR" property="expiredDate"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,short_url,short_code,batch_id,cid,mobile_enc,long_url,tpl_code,expired_date,create_time,
            update_time,is_delete,status
    </sql>

    <sql id="ChannelCode_Column_List">
       short_code,mobile_enc,long_url
    </sql>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xhqb.spectre.common.dal.entity.UserShortUrlDO" useGeneratedKeys="true">
        insert into t_user_short_url
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="tplCode != null">
                tpl_code,
            </if>
            <if test="shortUrl != null">
                short_url,
            </if>
            <if test="longUrl != null">
                long_url,
            </if>
            <if test="expiredDate != null">
                expired_date,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="cid != null">
                cid,
            </if>
            <if test="mobile != null">
                mobile_enc,
            </if>
            <if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="tplCode != null">
                #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="shortUrl != null">
                #{shortUrl,jdbcType=VARCHAR},
            </if>
            <if test="longUrl != null">
                #{longUrl,jdbcType=VARCHAR},
            </if>
            <if test="expiredDate != null">
                #{expiredDate,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="cid != null">
                #{cid,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="batchId != null">
                #{batchId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert">
        insert into t_user_short_url
        (batch_id,tpl_code,short_url,long_url,expired_date,short_code,mobile_enc,cid)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.batchId,jdbcType=VARCHAR},
            #{item.tplCode,jdbcType=VARCHAR},
            #{item.shortUrl,jdbcType=VARCHAR},
            #{item.longUrl,jdbcType=VARCHAR},
            #{item.expiredDate,jdbcType=VARCHAR},
            #{item.shortCode,jdbcType=VARCHAR},
            #{item.mobile,jdbcType=VARCHAR},
            #{item.cid,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <select id="loadUserShortUrlList" resultMap="BaseResultMap">
        select id, short_code
        from t_user_short_url
        where id <![CDATA[ > ]]> #{lastId}
          and create_time <![CDATA[ >= ]]> #{selectedDate}
        order by id asc
            limit #{pageSize}
    </select>
    <select id="selectByLimitTime" resultType="com.xhqb.spectre.common.dal.entity.UserShortUrlDO">
        select short_code as shortCode
        from t_user_short_url
        where is_delete = 0
          and status = 0
          and create_time <![CDATA[ < ]]> #{limitTime}
        order by id desc
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_user_short_url
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="countByQuery" resultType="java.lang.Integer">
        select count(*)
        from t_user_short_url
        where is_delete = 0 and status =0
        <if test="query.tplCode != null">
            and tpl_code = #{query.tplCode,jdbcType=VARCHAR}
        </if>
        <if test="query.shortCode != null">
            and short_code = #{query.shortCode,jdbcType=VARCHAR}
        </if>
        <if test="query.mobile != null">
            and mobile_enc = #{query.mobile,jdbcType=VARCHAR}
        </if>
        <if test="query.startDate != null">
            and create_time <![CDATA[ >= ]]> #{query.startDate,jdbcType=VARCHAR}
        </if>
        <if test="query.endDate != null">
            and create_time <![CDATA[ <= ]]> #{query.endDate,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_user_short_url
        where is_delete = 0 and status =0
        <if test="query.tplCode != null">
            and tpl_code = #{query.tplCode,jdbcType=VARCHAR}
        </if>
        <if test="query.shortCode != null">
            and short_code = #{query.shortCode,jdbcType=VARCHAR}
        </if>
        <if test="query.mobile != null">
            and mobile_enc = #{query.mobile,jdbcType=VARCHAR}
        </if>
        <if test="query.startDate != null">
            and create_time <![CDATA[ >= ]]> #{query.startDate,jdbcType=VARCHAR}
        </if>
        <if test="query.endDate != null">
            and create_time <![CDATA[ <= ]]> #{query.endDate,jdbcType=VARCHAR}
        </if>
        order by create_time desc
        <if test="query.pageParameter!= null">
            limit #{query.pageParameter.offset}, #{query.pageParameter.pageSize}
        </if>
    </select>

    <select id="selectByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_user_short_url
        where is_delete = 0 and status =0 and short_code=#{code}
        order by create_time desc limit 1
    </select>
    <select id="selectByShortCodes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_user_short_url
        where is_delete = 0 and status =0 and short_code in
        <foreach collection="list" item="code" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </select>
    <select id="selectByMobile" resultMap="BaseResultMap">
        select
        <include refid="ChannelCode_Column_List"/>
        from t_user_short_url
        where is_delete = 0 and status =0
        and create_time <![CDATA[ >= ]]> #{startTime}
        and create_time <![CDATA[ <= ]]> #{endTime}
        and mobile_enc = #{mobile}
        order by create_time desc
    </select>
</mapper>