<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.WxUrlLinkFlowMapper">

    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.WxUrlLinkFlowDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="wxUrlLinkId" column="wx_url_link_id" jdbcType="BIGINT"/>
            <result property="urlLink" column="url_link" jdbcType="VARCHAR"/>
            <result property="generateDate" column="generate_date" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,wx_url_link_id,url_link,
        generate_date,create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_wx_url_link_flow
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_wx_url_link_flow
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhqb.spectre.common.dal.entity.WxUrlLinkFlowDO" useGeneratedKeys="true">
        insert into t_wx_url_link_flow
        ( id,wx_url_link_id,url_link
        ,generate_date,create_time,update_time
        )
        values (#{id,jdbcType=BIGINT},#{wxUrlLinkId,jdbcType=INTEGER},#{urlLink,jdbcType=VARCHAR}
        ,#{generateDate,jdbcType=TIMESTAMP},#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhqb.spectre.common.dal.entity.WxUrlLinkFlowDO" useGeneratedKeys="true">
        insert into t_wx_url_link_flow
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="wxUrlLinkId != null">wx_url_link_id,</if>
                <if test="urlLink != null">url_link,</if>
                <if test="generateDate != null">generate_date,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="wxUrlLinkId != null">#{wxUrlLinkId,jdbcType=BIGINT},</if>
                <if test="urlLink != null">#{urlLink,jdbcType=VARCHAR},</if>
                <if test="generateDate != null">#{generateDate,jdbcType=TIMESTAMP},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.WxUrlLinkFlowDO">
        update t_wx_url_link_flow
        <set>
                <if test="wxUrlLinkId != null">
                    wx_url_link_id = #{wxUrlLinkId,jdbcType=BIGINT},
                </if>
                <if test="urlLink != null">
                    url_link = #{urlLink,jdbcType=VARCHAR},
                </if>
                <if test="generateDate != null">
                    generate_date = #{generateDate,jdbcType=TIMESTAMP},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.WxUrlLinkFlowDO">
        update t_wx_url_link_flow
        set 
            wx_url_link_id =  #{wxUrlLinkId,jdbcType=BIGINT},
            url_link =  #{urlLink,jdbcType=VARCHAR},
            generate_date =  #{generateDate,jdbcType=TIMESTAMP},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
