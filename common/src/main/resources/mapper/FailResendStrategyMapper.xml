<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.FailResendStrategyMapper">

    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.FailResendStrategyDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="strategyId" column="strategy_id" jdbcType="VARCHAR"/>
        <result property="strategyName" column="strategy_name" jdbcType="VARCHAR"/>
        <result property="timePeriod" column="time_period" jdbcType="VARCHAR"/>
        <result property="originalTplCodes" column="original_tpl_codes" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , strategy_id, strategy_name, time_period,
          original_tpl_codes, is_delete, remark,
          creator, updater, status, create_time, update_time
    </sql>
    <insert id="insertSelective">
        INSERT INTO t_fail_resend_strategy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">
                strategy_id,
            </if>
            <if test="strategyName != null">
                strategy_name,
            </if>
            <if test="timePeriod != null">
                time_period,
            </if>
            <if test="originalTplCodes != null">
                original_tpl_codes,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">
                #{strategyId,jdbcType=VARCHAR},
            </if>
            <if test="strategyName != null">
                #{strategyName,jdbcType=VARCHAR},
            </if>
            <if test="timePeriod != null">
                #{timePeriod,jdbcType=VARCHAR},
            </if>
            <if test="originalTplCodes != null">
                #{originalTplCodes,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective">
        UPDATE t_fail_resend_strategy
        <set>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=VARCHAR},
            </if>
            <if test="strategyName != null">
                strategy_name = #{strategyName,jdbcType=VARCHAR},
            </if>
            <if test="timePeriod != null">
                time_period = #{timePeriod,jdbcType=VARCHAR},
            </if>
            <if test="originalTplCodes != null">
                original_tpl_codes = #{originalTplCodes,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateDeleteTagByStrategyId">
        update t_fail_resend_strategy
        set is_delete = 1
        where strategy_id = #{strategyId,jdbcType=BIGINT}
    </update>
    <select id="countByQuery" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM t_fail_resend_strategy
        <where>
            and is_delete = 0
            <if test="query.strategyId != null and query.strategyId != ''">
                AND strategy_id = #{query.strategyId}
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.creator != null and query.creator != ''">
                AND creator like concat('%',#{query.creator},'%')
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND create_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND create_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.tplCode != null and query.tplCode != ''">
                AND original_tpl_codes like concat('%',#{query.tplCode},'%')
            </if>
        </where>
    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_fail_resend_strategy
        <where>
            and is_delete = 0
            <if test="query.strategyId != null and query.strategyId != ''">
                AND strategy_id = #{query.strategyId}
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.creator != null and query.creator != ''">
                AND creator like concat('%',#{query.creator},'%')
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND create_time <![CDATA[>=]]> #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND create_time <![CDATA[<=]]> #{query.endTime}
            </if>
            <if test="query.tplCode != null and query.tplCode != ''">
                AND original_tpl_codes like concat('%',#{query.tplCode},'%')
            </if>
        </where>
        order by update_time desc
        <if test="query != null and query.pageParameter != null">
            limit #{query.pageParameter.offset}, #{query.pageParameter.pageSize}
        </if>
    </select>
    <select id="selectByStrategyId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_fail_resend_strategy
        WHERE strategy_id = #{strategyId}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_fail_resend_strategy
        WHERE is_delete = 0
    </select>

    <select id="selectAllEnabled" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_fail_resend_strategy
        WHERE status = 0 AND is_delete = 0
        ORDER BY create_time DESC
    </select>

    <select id="selectByTplCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_fail_resend_strategy
        WHERE status = 0 AND is_delete = 0
        AND FIND_IN_SET(#{tplCode}, original_tpl_codes) > 0
        ORDER BY create_time DESC
    </select>

</mapper>
