<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.MobileWhiteMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.MobileWhiteDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="app_code" property="appCode" jdbcType="VARCHAR"/>
        <result column="cfg_type" property="cfgType" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, app_code, cfg_type, mobile, creator, create_time, updater, update_time,
        is_delete, description
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from t_mobile_white
        where is_delete = 0 and id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from t_mobile_white
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.MobileWhiteDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_mobile_white (id, app_code,
        cfg_type, mobile, creator,
        create_time, updater, update_time,
        is_delete, description)
        values (#{id,jdbcType=INTEGER}, #{appCode,jdbcType=VARCHAR},
        #{cfgType,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDelete,jdbcType=TINYINT}, #{description,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.MobileWhiteDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_mobile_white
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="appCode != null">
                app_code,
            </if>
            <if test="cfgType != null">
                cfg_type,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="description != null">
                description,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="appCode != null">
                #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="cfgType != null">
                #{cfgType,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.MobileWhiteDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_mobile_white
        <set>
            <if test="appCode != null">
                app_code = #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="cfgType != null">
                cfg_type = #{cfgType,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.MobileWhiteDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_mobile_white
        set app_code = #{appCode,jdbcType=VARCHAR},
        cfg_type = #{cfgType,jdbcType=VARCHAR},
        mobile = #{mobile,jdbcType=VARCHAR},
        creator = #{creator,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater = #{updater,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_delete = #{isDelete,jdbcType=TINYINT},
        description = #{description,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectOne" parameterType="com.xhqb.spectre.common.dal.entity.MobileWhiteDO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_mobile_white
        where is_delete = 0 and cfg_type = #{cfgType,jdbcType=VARCHAR}
              and app_code =#{appCode,jdbcType=VARCHAR} and mobile = #{mobile,jdbcType=VARCHAR}
    </select>

    <select id="loadAllWhiteInfo" parameterType="com.xhqb.spectre.common.dal.entity.MobileWhiteDO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_mobile_white where is_delete = 0
    </select>

    <select id="selectList" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_mobile_white where is_delete = 0 and app_code = #{appCode, jdbcType=VARCHAR}
    </select>

    <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.MobileWhiteQuery" resultType="java.lang.Integer">
        select count(*)
        from t_mobile_white
        <where>
            is_delete = 0
            <if test="appCode != null and appCode != ''">and app_code = #{appCode}</if>
            <if test="cfgType != null and cfgType != ''">and `cfg_type` = #{cfgType}</if>
            <if test="mobile != null and mobile != ''">and mobile = #{mobile}</if>
        </where>
    </select>

    <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.MobileWhiteQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_mobile_white
        <where>
            is_delete = 0
            <if test="appCode != null and appCode != ''">and app_code = #{appCode}</if>
            <if test="cfgType != null and cfgType != ''">and `cfg_type` = #{cfgType}</if>
            <if test="mobile != null and mobile != ''">and mobile = #{mobile}</if>
        </where>
        order by update_time desc
        <if test="pageParameter != null">
            limit #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>

    <update id="delete">
        update `t_mobile_white`
        set `is_delete` = 1, `updater` = #{operator}
        where `id` = #{id}
    </update>

    <update id="deleteByIdList">
        update t_mobile_white
        set is_delete = 1, updater = #{operator}
        where id in
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="deleteByAppCode">
        update t_mobile_white
        set is_delete = 1, updater = #{operator}
        where app_code = #{appCode}
    </update>
</mapper>