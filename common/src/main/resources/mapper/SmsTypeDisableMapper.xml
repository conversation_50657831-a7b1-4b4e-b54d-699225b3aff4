<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.SmsTypeDisableMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.SmsTypeDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR" />
    <result column="isps" property="isps" jdbcType="VARCHAR" />
    <result column="areas" property="areas" jdbcType="VARCHAR" />
    <result column="start_time" property="startTime" jdbcType="INTEGER" />
    <result column="end_time" property="endTime" jdbcType="INTEGER" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updater" property="updater" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_delete" property="isDelete" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, sms_type_code, isps, areas, start_time, end_time, creator, create_time, updater, 
    update_time, is_delete
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_sms_type_disable
    where is_delete = 0 and id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_sms_type_disable
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.SmsTypeDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_sms_type_disable (id, sms_type_code, isps, 
      areas, start_time, end_time, 
      creator, create_time, updater, 
      update_time, is_delete)
    values (#{id,jdbcType=INTEGER}, #{smsTypeCode,jdbcType=VARCHAR}, #{isps,jdbcType=VARCHAR}, 
      #{areas,jdbcType=VARCHAR}, #{startTime,jdbcType=INTEGER}, #{endTime,jdbcType=INTEGER}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.SmsTypeDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_sms_type_disable
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="smsTypeCode != null" >
        sms_type_code,
      </if>
      <if test="isps != null" >
        isps,
      </if>
      <if test="areas != null" >
        areas,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updater != null" >
        updater,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="isDelete != null" >
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="smsTypeCode != null" >
        #{smsTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="isps != null" >
        #{isps,jdbcType=VARCHAR},
      </if>
      <if test="areas != null" >
        #{areas,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=INTEGER},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.SmsTypeDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_sms_type_disable
    <set >
      <if test="smsTypeCode != null" >
        sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="isps != null" >
        isps = #{isps,jdbcType=VARCHAR},
      </if>
      <if test="areas != null" >
        areas = #{areas,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=INTEGER},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.SmsTypeDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_sms_type_disable
    set sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
      isps = #{isps,jdbcType=VARCHAR},
      areas = #{areas,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=INTEGER},
      end_time = #{endTime,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.SmsTypeDisableQuery" resultType="java.lang.Integer">
    select count(*)
    from t_sms_type_disable
    <where>
      is_delete = 0
      <if test="smsTypeCode != null and smsTypeCode != ''">and sms_type_code = #{smsTypeCode}</if>
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.SmsTypeDisableQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_sms_type_disable
    <where>
      is_delete = 0
      <if test="smsTypeCode != null and smsTypeCode != ''">and sms_type_code = #{smsTypeCode}</if>
    </where>
    order by update_time desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>

  <update id="deleteByIdList">
    update t_sms_type_disable
    set is_delete = 1, updater = #{operator}
    where id in
    <foreach item="id" collection="idList" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_sms_type_disable
    where is_delete = 0
  </select>
</mapper>