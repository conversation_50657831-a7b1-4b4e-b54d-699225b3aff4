<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TplMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.TplDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="sms_type_code" jdbcType="VARCHAR" property="smsTypeCode"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="sign_id" jdbcType="INTEGER" property="signId"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="app_code" jdbcType="VARCHAR" property="appCode"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="approve_status" jdbcType="INTEGER" property="approveStatus"/>
        <result column="report_id" jdbcType="VARCHAR" property="reportId"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="notify_send_status" jdbcType="TINYINT" property="notifySendStatus"/>
        <result column="tag" jdbcType="TINYINT" property="tag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, code, sms_type_code, title, sign_id, content, app_code, status, remark,
        creator, create_time, updater, update_time, is_delete,approve_status,report_id,`source`,notify_send_status,
        tag
    </sql>
    <sql id="Partial_Column_List">
        id, code, title, status
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from t_tpl
        where is_delete = 0 and id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from t_tpl
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.TplDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_tpl (id, code, sms_type_code,
        title, sign_id, content,
        app_code, status,
        remark, creator,
        create_time, updater, update_time,
        is_delete,approve_status,report_id,`source`,notify_send_status,tag)
        values (#{id,jdbcType=INTEGER}, #{code,jdbcType=VARCHAR}, #{smsTypeCode,jdbcType=VARCHAR},
        #{title,jdbcType=VARCHAR}, #{signId,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR},
        #{appCode,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
        #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP},
        #{isDelete,jdbcType=TINYINT},#{approveStatus,jdbcType=INTEGER},
        #{reportId,jdbcType=VARCHAR},#{source,jdbcType=VARCHAR},
        #{notifySendStatus,jdbcType=TINYINT},
        #{tag})
    </insert>
    <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.TplDO" useGeneratedKeys="true"
            keyProperty="id">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_tpl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="smsTypeCode != null">
                sms_type_code,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="signId != null">
                sign_id,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="appCode != null">
                app_code,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="approveStatus != null">
                approve_status,
            </if>
            <if test="reportId != null">
                report_id,
            </if>
            <if test="source != null">
                `source`,
            </if>
            <if test="notifySendStatus != null">
                notify_send_status,
            </if>
            <if test="tag != null">
                tag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="signId != null">
                #{signId,jdbcType=INTEGER},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="appCode != null">
                #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="approveStatus != null">
                #{approveStatus,jdbcType=INTEGER},
            </if>
            <if test="reportId != null">
                #{reportId,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source,jdbcType=VARCHAR},
            </if>
            <if test="notifySendStatus != null">
                #{notifySendStatus,jdbcType=TINYINT},
            </if>
            <if test="tag != null">
                #{tag,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.TplDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_tpl
        <set>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="signId != null">
                sign_id = #{signId,jdbcType=INTEGER},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="appCode != null">
                app_code = #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="approveStatus != null">
                approve_status = #{approveStatus,jdbcType=INTEGER},
            </if>
            <if test="reportId != null">
                report_id = #{reportId,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                `source` = #{source,jdbcType=VARCHAR},
            </if>
            <if test="notifySendStatus != null">
                notify_send_status = #{notifySendStatus,jdbcType=TINYINT},
            </if>
            <if test="tag != null">
                tag = #{tag,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.TplDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_tpl
        set code = #{code,jdbcType=VARCHAR},
        sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
        title = #{title,jdbcType=VARCHAR},
        sign_id = #{signId,jdbcType=INTEGER},
        content = #{content,jdbcType=VARCHAR},
        app_code = #{appCode,jdbcType=VARCHAR},
        status = #{status,jdbcType=TINYINT},
        remark = #{remark,jdbcType=VARCHAR},
        creator = #{creator,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater = #{updater,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_delete = #{isDelete,jdbcType=TINYINT},
        approve_status = #{approveStatus,jdbcType=INTEGER},
        report_id = #{reportId,jdbcType=VARCHAR},
        `source` = #{source,jdbcType=VARCHAR},
        notify_send_status = #{notifySendStatus,jdbcType=TINYINT},
        tag = #{tag,jdbcType=TINYINT}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.TplQuery" resultType="java.lang.Integer">
        select count(*)
        from t_tpl as m
        <if test="channelAccountId != null">
            join t_tpl_channel as n on m.id = n.tpl_id
        </if>
        <where>
            m.is_delete = 0
            <if test="id != null">and m.id = #{id}</if>
            <if test="code != null and code != ''">and m.code like concat('%', #{code}, '%')</if>
            <if test="title != null and title != ''">and m.title like concat('%', #{title}, '%')</if>
            <if test="status != null">and m.status = #{status}</if>
            <if test="approveStatus != null">and m.approve_status = #{approveStatus}</if>
            <if test="signId != null">and m.sign_id = #{signId}</if>
            <if test="smsTypeCode != null and smsTypeCode != ''">and m.sms_type_code = #{smsTypeCode}</if>
            <if test="content != null and content != ''">and m.content like concat('%', #{content}, '%')</if>
            <if test="creator != null and creator != ''">and m.creator like concat('%', #{creator}, '%')</if>
            <if test="channelAccountId != null">and n.channel_account_id = #{channelAccountId}</if>
            <if test="tplCodeList != null and tplCodeList.size() > 0">
                AND m.code IN
                <foreach collection="tplCodeList" item="tplCode" open="(" separator="," close=")">
                    #{tplCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.TplQuery" resultMap="BaseResultMap">
        select m.id, m.code, m.sms_type_code, m.title, m.sign_id, m.content, m.app_code, m.status, m.remark, m.creator,
        m.create_time, m.updater, m.update_time, m.is_delete, m.approve_status,
        m.report_id,m.notify_send_status,m.source, m.tag
        from t_tpl as m
        <if test="channelAccountId != null">
            join t_tpl_channel as n on m.id = n.tpl_id
        </if>
        <where>
            m.is_delete = 0
            <if test="id != null">and m.id = #{id}</if>
            <if test="code != null and code != ''">and m.code like concat('%', #{code}, '%')</if>
            <if test="title != null and title != ''">and m.title like concat('%', #{title}, '%')</if>
            <if test="status != null">and m.status = #{status}</if>
            <if test="approveStatus != null">and m.approve_status = #{approveStatus}</if>
            <if test="signId != null">and m.sign_id = #{signId}</if>
            <if test="smsTypeCode != null and smsTypeCode != ''">and m.sms_type_code = #{smsTypeCode}</if>
            <if test="source != null and source != ''">and m.source = #{source}</if>
            <if test="content != null and content != ''">and m.content like concat('%', #{content}, '%')</if>
            <if test="creator != null and creator != ''">and m.creator like concat('%', #{creator}, '%')</if>
            <if test="channelAccountId != null">and n.channel_account_id = #{channelAccountId}</if>
            <if test="tplCodeList != null and tplCodeList.size() > 0">
                AND m.code IN
                <foreach collection="tplCodeList" item="tplCode" open="(" separator="," close=")">
                    #{tplCode}
                </foreach>
            </if>
        </where>
        order by m.update_time desc
        <if test="pageParameter != null">
            limit #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>

    <select id="selectByCodeAndSign" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl
        where is_delete = 0 and code = #{code} and sign_id = #{signId}
        limit 1
    </select>

    <update id="enable">
        update `t_tpl`
        set `status`  = 1,
            `updater` = #{operator}
        where `id` = #{id}
          and `status` = 0
    </update>

    <update id="disable">
        update `t_tpl`
        set `status`  = 0,
            `updater` = #{operator}
        where `id` = #{id}
          and `status` = 1
    </update>

    <update id="delete">
        update `t_tpl`
        set `is_delete` = 1,
            `updater`   = #{operator}
        where `id` = #{id}
          and `status` = 0
    </update>
    <update id="updateApproveStatusByCodeAndSignId">
        update `t_tpl`
        <set>
            <if test="approveStatus != null">`approve_status` = #{approveStatus},</if>
            <if test="updater != null">`updater` = #{updater},</if>
            `update_time` = now()
        </set>
        where `code` = #{code} and `sign_id` = #{signId}
    </update>
    <update id="updateContentByCodeAndSignId">
        update `t_tpl`
        set `content` = #{content}
        where `code` = #{code}
          and `sign_id` = #{signId}
    </update>
    <update id="updateByContentId">
        update `t_tpl`
        set `content`        = #{content},
            `approve_status` = #{approveStatus},
            `updater`        = 'SYS',
            `update_time`    = now()
        where `report_id` = #{reportId}
    </update>
    <update id="updateNotifyStatus">
        update `t_tpl`
        set `notify_send_status` = #{notifyStatus},
            `updater`            = 'SYS',
            `update_time`        = now()
        where `id` = #{id}
    </update>
    <update id="updateNotifySendStatusByReportIds">
        update `t_tpl`
        set `notify_send_status` = #{notifySendStatus},
        `update_time` = now()
        where `report_id` in
        <foreach collection="reportIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectEnum" resultMap="BaseResultMap">
        select
        <include refid="Partial_Column_List"/>
        from t_tpl
        <where>
            is_delete = 0
            <if test="status != null and status != -1">and status = #{status}</if>
            <if test="code != null and code != ''">and code like concat('%', #{code}, '%')</if>
            <if test="title != null and title != ''">and title like concat('%', #{title}, '%')</if>
            <if test="signId != null">and sign_id = #{signId}</if>
        </where>
    </select>

    <select id="selectAllEnabled" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl
        where is_delete = 0 and status = 1
    </select>

    <select id="selectByAppCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl
        where is_delete = 0 and app_code = #{appCode}
    </select>

    <select id="selectByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl
        <where>
            id in
            <foreach collection="idList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and is_delete = 0
        </where>
    </select>

    <select id="autoMatchTpl" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl
        where app_code = #{appCode}
        and sign_id = #{signId}
        and sms_type_code = #{smsTypeCode}
        and content = #{content}
        and is_delete = 0
    </select>

    <select id="selectByActive" parameterType="com.xhqb.spectre.common.dal.query.TplQuery" resultMap="BaseResultMap">
        select m.id, m.code, m.sms_type_code, m.title, m.sign_id, m.content, m.app_code, m.status, m.remark, m.creator,
        m.create_time, m.updater, m.update_time, m.is_delete
        from t_tpl as m
        <if test="channelAccountId != null">
            join t_tpl_channel as n on m.id = n.tpl_id
        </if>
        <where>
            m.is_delete = 0
            <if test="id != null">and m.id = #{id}</if>
            <if test="code != null and code != ''">and m.code like concat('%', #{code}, '%')</if>
            <if test="title != null and title != ''">and m.title like concat('%', #{title}, '%')</if>
            <if test="status != null">and m.status = #{status}</if>
            <if test="signId != null">and m.sign_id = #{signId}</if>
            <if test="smsTypeCode != null and smsTypeCode != ''">and m.sms_type_code = #{smsTypeCode}</if>
            <if test="content != null and content != ''">and m.content like concat('%', #{content}, '%')</if>
            <if test="creator != null and creator != ''">and m.creator like concat('%', #{creator}, '%')</if>
            <if test="channelAccountId != null">and n.channel_account_id = #{channelAccountId}</if>
            and m.is_active=1
        </where>
        order by m.update_time desc
        <if test="pageParameter != null">
            limit #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>
    <select id="selectByIdListAndTplCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl
        where is_delete = 0 and id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="tplCode != null and tplCode != ''">
            and code = #{tplCode}
        </if>
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl
        where is_delete = 0 and status = 1
    </select>
    <select id="selectByReportId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl
        where is_delete = 0 and report_id = #{reportId} limit 1
    </select>
    <select id="selectEffectiveNotify" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl
        where is_delete = 0 and status = #{status} and source = #{source} and notify_send_status = #{notifySendStatus}
    </select>

    <select id="selectByCodeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl
        <where>
            code in
            <foreach collection="codeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and is_delete = 0
        </where>
    </select>
</mapper>