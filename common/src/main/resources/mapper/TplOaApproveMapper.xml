<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TplOaApproveMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.oa.TplOaApprove">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="flow_type_id" property="flowTypeId" jdbcType="VARCHAR"/>
        <result column="flow_id" property="flowId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR"/>
        <result column="scene_code" property="sceneCode" jdbcType="VARCHAR"/>
        <result column="original_content" property="originalContent" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , flow_type_id, flow_id, sms_type_code, scene_code, original_content, status, creator, create_time, updater, update_time,remark,title,user_id
    </sql>
    <insert id="insertSelective">
        insert into t_tpl_oa_approve
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flowTypeId != null">
                flow_type_id,
            </if>
            <if test="flowId != null">
                flow_id,
            </if>
            <if test="smsTypeCode != null">
                sms_type_code,
            </if>
            <if test="sceneCode != null">
                scene_code,
            </if>
            <if test="originalContent != null">
                original_content,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="userId != null">
                user_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flowTypeId != null">
                #{flowTypeId,jdbcType=VARCHAR},
            </if>
            <if test="flowId != null">
                #{flowId,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="sceneCode != null">
                #{sceneCode,jdbcType=VARCHAR},
            </if>
            <if test="originalContent != null">
                #{originalContent,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
        </trim>

    </insert>
    <update id="updateStatusByFlowId">
        update t_tpl_oa_approve
        set status = #{status,jdbcType=INTEGER}
        where flow_id = #{flowId,jdbcType=VARCHAR}
          and status = 1
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_oa_approve
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectByStatusList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_oa_approve
        where status in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_oa_approve
        <where>
            <if test="query.smsTypeCode != null">
                and sms_type_code = #{query.smsTypeCode,jdbcType=VARCHAR}
            </if>
            <if test="query.creator != null">
                and creator = #{query.creator,jdbcType=VARCHAR}
            </if>
            <if test="query.sceneCode != null">
                and scene_code = #{query.sceneCode,jdbcType=VARCHAR}
            </if>
            <if test="query.approveStatus != null">
                and status = #{query.approveStatus,jdbcType=INTEGER}
            </if>
            <if test="query.startTime != null">
                and create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and create_time &lt;= #{query.endTime}
            </if>
        </where>
        order by update_time desc
        <if test="query != null and query.pageParameter != null">
            limit #{query.pageParameter.offset},#{query.pageParameter.pageSize}
        </if>
    </select>
    <select id="countByQuery" resultType="java.lang.Integer">
        select count(*)
        from t_tpl_oa_approve
        <where>
            <if test="query.smsTypeCode != null">
                and sms_type_code = #{query.smsTypeCode,jdbcType=VARCHAR}
            </if>
            <if test="query.creator != null">
                and creator = #{query.creator,jdbcType=VARCHAR}
            </if>
            <if test="query.sceneCode != null">
                and scene_code = #{query.sceneCode,jdbcType=VARCHAR}
            </if>
            <if test="query.approveStatus != null">
                and status = #{query.approveStatus,jdbcType=INTEGER}
            </if>
            <if test="query.startTime != null">
                and create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and create_time &lt;= #{query.endTime}
            </if>
        </where>
    </select>
    <select id="selectByFlowId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_oa_approve
        where flow_id = #{flowId,jdbcType=VARCHAR} limit 1
    </select>


</mapper>
