<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.BlackSourceMapper">
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.BlackSourceDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="code" jdbcType="VARCHAR" property="code"/>
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, name, description, code, status, creator, create_time, updater, update_time, is_delete
  </sql>
  <sql id="Partial_Column_List">
    id, name, code, status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_black_source
    where is_delete = 0 and id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_black_source
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.BlackSourceDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_black_source (id, name, description, code,
      status, creator, create_time, 
      updater, update_time, is_delete
      )
    values (#{id,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR},
      #{status,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.BlackSourceDO" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_black_source
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.BlackSourceDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_black_source
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.BlackSourceDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_black_source
    set name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectEnum" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Partial_Column_List" />
    from t_black_source
    <where>
      is_delete = 0
      <if test="status != null and status != -1">and status = #{status}</if>
    </where>
  </select>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.BlackSourceQuery" resultType="java.lang.Integer">
    select count(*)
    from t_black_source
    <where>
      is_delete = 0
      <if test="name != null and name != ''">and name like concat('%', #{name}, '%')</if>
      <if test="code != null">and code = #{code}</if>
      <if test="status != null">and status = #{status}</if>
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.BlackSourceQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_black_source
    <where>
      is_delete = 0
      <if test="name != null and name != ''">and name like concat('%', #{name}, '%')</if>
      <if test="code != null">and code = #{code}</if>
      <if test="status != null">and status = #{status}</if>
    </where>
    order by update_time desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>

  <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_black_source
    where is_delete = 0 and name = #{name}
    limit 1
  </select>

  <select id="selectByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_black_source
    where is_delete = 0 and code = #{code}
    limit 1
  </select>

  <update id="enable">
    update `t_black_source`
    set `status` = 1, `updater` = #{operator}
    where `id` = #{id} and `status` = 0
  </update>

  <update id="disable">
    update `t_black_source`
    set `status` = 0, `updater` = #{operator}
    where `id` = #{id} and `status` = 1
  </update>

  <update id="delete">
    update `t_black_source`
    set `is_delete` = 1, `updater` = #{operator}
    where `id` = #{id} and `status` = 0
  </update>
</mapper>