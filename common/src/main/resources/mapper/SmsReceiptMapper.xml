<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.SmsReceiptMapper">
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.SmsReceiptDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="channel_account_id" jdbcType="INTEGER" property="channelAccountId" />
    <result column="channel_code" jdbcType="VARCHAR" property="channelCode" />
    <result column="tpl_code" jdbcType="VARCHAR" property="tplCode" />
    <result column="channel_msg_id" jdbcType="VARCHAR" property="channelMsgId" />
    <result column="mobile_enc" jdbcType="VARCHAR" property="mobile" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="report_status" jdbcType="VARCHAR" property="reportStatus" />
    <result column="report_desc" jdbcType="VARCHAR" property="reportDesc" />
    <result column="submit_time" jdbcType="INTEGER" property="submitTime" />
    <result column="recv_report_time" jdbcType="INTEGER" property="recvReportTime" />
    <result column="done_time" jdbcType="INTEGER" property="doneTime" />
    <result column="dest_terminal_id" jdbcType="VARCHAR" property="destTerminalId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, order_id, channel_account_id, channel_code, tpl_code, channel_msg_id, mobile_enc,
    status, report_status, report_desc, submit_time, recv_report_time, done_time, dest_terminal_id,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from t_sms_receipt
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_sms_receipt
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.SmsReceiptDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_sms_receipt (id, order_id, channel_account_id,
      channel_code, tpl_code, channel_msg_id,
      mobile_enc, status, report_status,
      report_desc, submit_time, recv_report_time,
      done_time, dest_terminal_id, create_time
      )
    values (#{id,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{channelAccountId,jdbcType=INTEGER},
      #{channelCode,jdbcType=VARCHAR}, #{tplCode,jdbcType=VARCHAR}, #{channelMsgId,jdbcType=VARCHAR},
      #{mobile,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{reportStatus,jdbcType=VARCHAR},
      #{reportDesc,jdbcType=VARCHAR}, #{submitTime,jdbcType=INTEGER}, #{recvReportTime,jdbcType=INTEGER},
      #{doneTime,jdbcType=INTEGER}, #{destTerminalId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.SmsReceiptDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_sms_receipt
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="channelAccountId != null">
        channel_account_id,
      </if>
      <if test="channelCode != null">
        channel_code,
      </if>
      <if test="tplCode != null">
        tpl_code,
      </if>
      <if test="channelMsgId != null">
        channel_msg_id,
      </if>
      <if test="mobile != null">
        mobile_enc,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="reportStatus != null">
        report_status,
      </if>
      <if test="reportDesc != null">
        report_desc,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
      <if test="recvReportTime != null">
        recv_report_time,
      </if>
      <if test="doneTime != null">
        done_time,
      </if>
      <if test="destTerminalId != null">
        dest_terminal_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="channelAccountId != null">
        #{channelAccountId,jdbcType=INTEGER},
      </if>
      <if test="channelCode != null">
        #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="tplCode != null">
        #{tplCode,jdbcType=VARCHAR},
      </if>
      <if test="channelMsgId != null">
        #{channelMsgId,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="reportStatus != null">
        #{reportStatus,jdbcType=VARCHAR},
      </if>
      <if test="reportDesc != null">
        #{reportDesc,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=INTEGER},
      </if>
      <if test="recvReportTime != null">
        #{recvReportTime,jdbcType=INTEGER},
      </if>
      <if test="doneTime != null">
        #{doneTime,jdbcType=INTEGER},
      </if>
      <if test="destTerminalId != null">
        #{destTerminalId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.SmsReceiptDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_sms_receipt
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="channelAccountId != null">
        channel_account_id = #{channelAccountId,jdbcType=INTEGER},
      </if>
      <if test="channelCode != null">
        channel_code = #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="tplCode != null">
        tpl_code = #{tplCode,jdbcType=VARCHAR},
      </if>
      <if test="channelMsgId != null">
        channel_msg_id = #{channelMsgId,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile_enc = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="reportStatus != null">
        report_status = #{reportStatus,jdbcType=VARCHAR},
      </if>
      <if test="reportDesc != null">
        report_desc = #{reportDesc,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        submit_time = #{submitTime,jdbcType=INTEGER},
      </if>
      <if test="recvReportTime != null">
        recv_report_time = #{recvReportTime,jdbcType=INTEGER},
      </if>
      <if test="doneTime != null">
        done_time = #{doneTime,jdbcType=INTEGER},
      </if>
      <if test="destTerminalId != null">
        dest_terminal_id = #{destTerminalId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.SmsReceiptDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_sms_receipt
    set order_id = #{orderId,jdbcType=BIGINT},
      channel_account_id = #{channelAccountId,jdbcType=INTEGER},
      channel_code = #{channelCode,jdbcType=VARCHAR},
      tpl_code = #{tplCode,jdbcType=VARCHAR},
      channel_msg_id = #{channelMsgId,jdbcType=VARCHAR},
      mobile_enc = #{mobile,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      report_status = #{reportStatus,jdbcType=VARCHAR},
      report_desc = #{reportDesc,jdbcType=VARCHAR},
      submit_time = #{submitTime,jdbcType=INTEGER},
      recv_report_time = #{recvReportTime,jdbcType=INTEGER},
      done_time = #{doneTime,jdbcType=INTEGER},
      dest_terminal_id = #{destTerminalId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="insertBatch" parameterType="com.xhqb.spectre.common.dal.entity.SmsReceiptDO">
    insert into t_sms_receipt(
    order_id, channel_msg_id, tpl_code, channel_code, channel_account_id, mobile_enc,
    recv_report_time, status, report_status, report_desc, submit_time, done_time
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      #{item.orderId,jdbcType=BIGINT},
      #{item.channelMsgId,jdbcType=INTEGER},
      #{item.tplCode,jdbcType=VARCHAR},
      #{item.channelCode,jdbcType=VARCHAR},
      #{item.channelAccountId,jdbcType=VARCHAR},
      #{item.mobile,jdbcType=VARCHAR},
      #{item.recvReportTime,jdbcType=INTEGER},
      #{item.status,jdbcType=INTEGER},
      #{item.reportStatus,jdbcType=TINYINT},
      #{item.reportDesc,jdbcType=VARCHAR},
      #{item.submitTime,jdbcType=INTEGER},
      #{item.doneTime,jdbcType=INTEGER}
      )
    </foreach>
  </insert>
</mapper>