<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ParamsValueMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ParamsValueDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="value" property="value" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>

    </resultMap>
    <sql id="Base_Column_List">
        id
        , code, value, creator, create_time, updater, update_time, is_delete
    </sql>
    <insert id="insertSelective">
        insert into t_test_param_value
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="value != null">
                value,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                #{value,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateDeleteTagByCodeList">
        update t_test_param_value
        set is_delete = 1
        where code in
        <foreach collection="codeList" item="code" separator="," open="(" close=")">
            #{code}
        </foreach>
        and is_delete = 0
    </update>
    <update id="updateDeleteTagByIdList">
        update t_test_param_value
        set is_delete = 1
        where id in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        and is_delete = 0
    </update>
    <select id="countByQuery" resultType="java.lang.Integer">
        select count(*)
        from t_test_param_value
        <where>
            is_delete = 0
            <if test="query.codes != null">
                and code in
                <foreach collection="query.codes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.value != null">
                and value = #{query.value}
            </if>
        </where>
    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_param_value
        <where>
            is_delete = 0
            <if test="query.codes != null">
                and code in
                <foreach collection="query.codes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.value != null">
                and value = #{query.value}
            </if>
        </where>
        order by update_time desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset},#{query.pageParameter.pageSize}
        </if>

    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_param_value
        where is_delete = 0
    </select>
    <select id="selectByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_param_value
        where code = #{code} and is_delete = 0
    </select>


</mapper>
