<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.CmppRecordMapper" >
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.CmppRecord" >
        <!--
        resultMap
        -->
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="order_id" property="orderId" jdbcType="BIGINT" />
        <result column="mobile_phone" property="mobilePhone" jdbcType="VARCHAR" />
        <result column="pk_total" property="pkTotal" jdbcType="INTEGER" />
        <result column="pk_number" property="pkNumber" jdbcType="INTEGER" />
        <result column="submit_seq_id" property="submitSeqId" jdbcType="INTEGER" />
        <result column="partner_platform" property="partnerPlatform" jdbcType="VARCHAR" />
        <result column="platform_status" property="platformStatus" jdbcType="VARCHAR" />
        <result column="platform_time" property="platformTime" jdbcType="VARCHAR" />
        <result column="report_msg_id" property="reportMsgId" jdbcType="VARCHAR" />
        <result column="report_status" property="reportStatus" jdbcType="VARCHAR" />
        <result column="report_time" property="reportTime" jdbcType="VARCHAR" />
        <result column="record_status" property="recordStatus" jdbcType="INTEGER" />
        <result column="addition" property="addition" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="creator" property="creator" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modifier" property="modifier" jdbcType="VARCHAR" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    </resultMap>
    <sql id="Base_Column_List" >
        id, order_id, mobile_phone, pk_total, pk_number, submit_seq_id, partner_platform, platform_status,
        platform_time, report_msg_id, report_status, report_time, record_status, addition,
        status
    </sql>

    <select id="findSubmitCoupledByMsgIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_cmpp_record
        where record_status=3 and report_msg_id in
        <if test="list != null and list.size > 0">
            <foreach collection="list" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="batchInsert" parameterType="com.xhqb.spectre.common.dal.entity.CmppRecord">
        insert into t_cmpp_record (id, order_id, mobile_phone, pk_total, pk_number, submit_seq_id,
        partner_platform, platform_status, platform_time, report_msg_id, report_status, report_time,
        record_status, addition, status, creator, create_time, modifier, modify_time) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id,jdbcType=VARCHAR}, #{item.orderId,jdbcType=BIGINT}, #{item.mobilePhone,jdbcType=VARCHAR},
            #{item.pkTotal,jdbcType=INTEGER}, #{item.pkNumber,jdbcType=INTEGER}, #{item.submitSeqId,jdbcType=INTEGER},
            #{item.partnerPlatform,jdbcType=VARCHAR}, #{item.platformStatus,jdbcType=VARCHAR}, #{item.platformTime,jdbcType=VARCHAR},
            #{item.reportMsgId,jdbcType=VARCHAR}, #{item.reportStatus,jdbcType=VARCHAR}, #{item.reportTime,jdbcType=VARCHAR},
            #{item.recordStatus,jdbcType=INTEGER}, #{item.addition,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR},
            #{item.creator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR},
            #{item.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <update id="updateDeliverInfoById" parameterType="com.xhqb.spectre.common.dal.entity.CmppRecord" >
        update t_cmpp_record
        <set >
            <if test="reportStatus != null" >
                report_status = #{reportStatus,jdbcType=VARCHAR},
            </if>
            <if test="reportTime != null" >
                report_time = #{reportTime,jdbcType=VARCHAR},
            </if>
            <if test="recordStatus != null" >
                record_status = #{recordStatus,jdbcType=INTEGER},
            </if>
            <if test="modifier != null" >
                modifier = #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>