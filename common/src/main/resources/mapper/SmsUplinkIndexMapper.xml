<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.SmsUplinkIndexMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.SmsUplinkIndexDo">
      <id column="id" property="id"/>
        <result property="lastSmsUplinkId" column="sms_uplink_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <update id="updateUplinkLastId">
        update  t_sms_uplink_index set sms_uplink_id=#{id} where id=1
    </update>
    <select id="selectSmsUplinkId" resultType="java.lang.Long">
        SELECT sms_uplink_id from t_sms_uplink_index where id=1
    </select>


</mapper>