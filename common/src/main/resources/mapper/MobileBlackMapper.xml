<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.MobileBlackMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.MobileBlackDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="cid" property="cid" jdbcType="VARCHAR"/>
        <result column="mobile_enc" property="mobile" jdbcType="VARCHAR"/>
        <result column="source" property="source" jdbcType="VARCHAR"/>
        <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="add_type" property="addType" jdbcType="TINYINT"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="app_code" property="appCode" jdbcType="VARCHAR"/>
        <result column="expired_time" property="expiredTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, cid, mobile_enc, source, sms_type_code, description, add_type, creator, create_time,
        updater, update_time, is_delete, app_code,expired_time
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from t_mobile_black
        where is_delete = 0 and id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from t_mobile_black
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.MobileBlackDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_mobile_black (id, cid, mobile_enc,
        source, sms_type_code, description,
        add_type, creator, create_time,
        updater, update_time, is_delete,expired_time
        )
        values (#{id,jdbcType=INTEGER}, #{cid,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR},
        #{source,jdbcType=VARCHAR}, #{smsTypeCode,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
        #{addType,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDelete,jdbcType=TINYINT},#{expiredTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.MobileBlackDO"
            useGeneratedKeys="true" keyProperty="id">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_mobile_black
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="cid != null">
                cid,
            </if>
            <if test="mobile != null">
                mobile_enc,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="smsTypeCode != null">
                sms_type_code,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="addType != null">
                add_type,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="appCode != null and appCode !=''">
                app_code,
            </if>
            <if test="expiredTime != null">
                expired_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="cid != null">
                #{cid,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="addType != null">
                #{addType,jdbcType=TINYINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="appCode != null and appCode !=''">
                #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="expiredTime != null">
                #{expiredTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch">
        insert into t_mobile_black (cid, mobile_enc, source, sms_type_code, description,add_type, creator, updater)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.cid}, #{item.mobile}, #{item.source}, #{item.smsTypeCode}, #{item.description}, #{item.addType},
            #{item.creator}, #{item.updater})
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.MobileBlackDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_mobile_black
        <set>
            <if test="cid != null">
                cid = #{cid,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile_enc = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="addType != null">
                add_type = #{addType,jdbcType=TINYINT},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if  test="expiredTime != null ">
                expired_time = #{expiredTime}
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.MobileBlackDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_mobile_black
        set cid = #{cid,jdbcType=VARCHAR},
        mobile_enc = #{mobile,jdbcType=VARCHAR},
        source = #{source,jdbcType=VARCHAR},
        sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
        description = #{description,jdbcType=VARCHAR},
        add_type = #{addType,jdbcType=TINYINT},
        creator = #{creator,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater = #{updater,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_delete = #{isDelete,jdbcType=TINYINT}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.MobileBlackQuery"
            resultType="java.lang.Integer">
        select count(*)
        from t_mobile_black
        <where>
            is_delete = 0
            <if test="cid != null and cid != ''">and cid = #{cid}</if>
            <if test="mobile != null and mobile != ''">and mobile_enc = #{mobile}</if>
            <if test="source != null and source != ''">and `source` = #{source}</if>
            <if test="appCode != null and appCode != ''">and `app_code` = #{appCode}</if>
        </where>
    </select>

    <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.MobileBlackQuery"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_mobile_black
        <where>
            is_delete = 0
            <if test="cid != null and cid != ''">and cid = #{cid}</if>
            <if test="mobile != null and mobile != ''">and mobile_enc = #{mobile}</if>
            <if test="source != null and source != ''">and `source` = #{source}</if>
            <if test="appCode != null and appCode != ''">and `app_code` = #{appCode}</if>
        </where>
        order by update_time desc
        <if test="pageParameter != null">
            limit #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>

    <select id="selectByPage" resultMap="BaseResultMap">
        select `sms_type_code`, `mobile_enc`, app_code
        from t_mobile_black
        where is_delete = 0
            limit #{offset}
            , #{pageSize}
    </select>

    <update id="delete">
        update `t_mobile_black`
        set `is_delete` = 1,
            `updater`   = #{operator}
        where `id` = #{id}
    </update>

    <select id="selectByMobileAndSmsType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_mobile_black
        where is_delete = 0 and mobile_enc = #{mobile} and sms_type_code = #{smsTypeCode}
        limit 1
    </select>

    <select id="selectByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_mobile_black
        where is_delete = 0 and id in
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectByMarketAndPhone" resultType="java.lang.String">
        SELECT
        mobile_enc
        FROM
        t_mobile_black
        WHERE
        sms_type_code = 'market'
        AND is_delete =0
        AND mobile IN
        <foreach collection="mobileList" item="mobile" open="(" close=")" separator=",">
            #{mobile}
        </foreach>
    </select>
    <select id="selectExpiredDataByExpiredTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_mobile_black
        where is_delete = 0 and expired_time &lt; now()
    </select>


    <update id="deleteByIdList">
        update t_mobile_black
        set is_delete = 1, updater = #{operator}
        where id in
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectBlackListAfterId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_mobile_black
        where
        id > #{lastId}
        <if test="updateTime != null">
            <![CDATA[ and update_time >= #{updateTime, jdbcType=TIMESTAMP} ]]>
        </if>
        order by id
        limit #{pageSize}
    </select>
</mapper>
