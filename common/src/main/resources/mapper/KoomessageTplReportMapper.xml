<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.KoomessageTplReportMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.KoomessageTplReportDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="tpl_id" property="tplId" jdbcType="VARCHAR" />
    <result column="tpl_name" property="tplName" jdbcType="VARCHAR" />
    <result column="report_date" property="reportDate" jdbcType="DATE" />
    <result column="resolving_times" property="resolvingTimes" jdbcType="INTEGER" />
    <result column="expose_uv" property="exposeUv" jdbcType="BIGINT" />
    <result column="expose_pv" property="exposePv" jdbcType="BIGINT" />
    <result column="click_uv" property="clickUv" jdbcType="BIGINT" />
    <result column="click_pv" property="clickPv" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="is_delete" property="isDelete" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, tpl_id, tpl_name, report_date, resolving_times, expose_uv, expose_pv, click_uv, 
    click_pv, create_time, update_time, is_delete
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_koomessage_tpl_report
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_koomessage_tpl_report
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.KoomessageTplReportDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_koomessage_tpl_report (id, tpl_id, tpl_name, 
      report_date, resolving_times, expose_uv, 
      expose_pv, click_uv, click_pv, 
      create_time, update_time, is_delete
      )
    values (#{id,jdbcType=BIGINT}, #{tplId,jdbcType=VARCHAR}, #{tplName,jdbcType=VARCHAR},
      #{reportDate,jdbcType=DATE}, #{resolvingTimes,jdbcType=INTEGER}, #{exposeUv,jdbcType=BIGINT},
      #{exposePv,jdbcType=BIGINT}, #{clickUv,jdbcType=BIGINT}, #{clickPv,jdbcType=BIGINT},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.KoomessageTplReportDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_koomessage_tpl_report
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="tplId != null" >
        tpl_id,
      </if>
      <if test="tplName != null" >
        tpl_name,
      </if>
      <if test="reportDate != null" >
        report_date,
      </if>
      <if test="resolvingTimes != null" >
        resolving_times,
      </if>
      <if test="exposeUv != null" >
        expose_uv,
      </if>
      <if test="exposePv != null" >
        expose_pv,
      </if>
      <if test="clickUv != null" >
        click_uv,
      </if>
      <if test="clickPv != null" >
        click_pv,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="isDelete != null" >
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tplId != null" >
        #{tplId,jdbcType=VARCHAR},
      </if>
      <if test="tplName != null" >
        #{tplName,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null" >
        #{reportDate,jdbcType=DATE},
      </if>
      <if test="resolvingTimes != null" >
        #{resolvingTimes,jdbcType=INTEGER},
      </if>
      <if test="exposeUv != null" >
        #{exposeUv,jdbcType=BIGINT},
      </if>
      <if test="exposePv != null" >
        #{exposePv,jdbcType=BIGINT},
      </if>
      <if test="clickUv != null" >
        #{clickUv,jdbcType=BIGINT},
      </if>
      <if test="clickPv != null" >
        #{clickPv,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.KoomessageTplReportDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_koomessage_tpl_report
    <set >
      <if test="tplId != null" >
        tpl_id = #{tplId,jdbcType=VARCHAR},
      </if>
      <if test="tplName != null" >
        tpl_name = #{tplName,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null" >
        report_date = #{reportDate,jdbcType=DATE},
      </if>
      <if test="resolvingTimes != null" >
        resolving_times = #{resolvingTimes,jdbcType=INTEGER},
      </if>
      <if test="exposeUv != null" >
        expose_uv = #{exposeUv,jdbcType=BIGINT},
      </if>
      <if test="exposePv != null" >
        expose_pv = #{exposePv,jdbcType=BIGINT},
      </if>
      <if test="clickUv != null" >
        click_uv = #{clickUv,jdbcType=BIGINT},
      </if>
      <if test="clickPv != null" >
        click_pv = #{clickPv,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null" >
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.KoomessageTplReportDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_koomessage_tpl_report
    set tpl_id = #{tplId,jdbcType=VARCHAR},
      tpl_name = #{tplName,jdbcType=VARCHAR},
      report_date = #{reportDate,jdbcType=DATE},
      resolving_times = #{resolvingTimes,jdbcType=INTEGER},
      expose_uv = #{exposeUv,jdbcType=BIGINT},
      expose_pv = #{exposePv,jdbcType=BIGINT},
      click_uv = #{clickUv,jdbcType=BIGINT},
      click_pv = #{clickPv,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByReportDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_koomessage_tpl_report
    where
    tpl_id = #{tplId, jdbcType=VARCHAR}
    and report_date = #{reportDate, jdbcType=DATE}
    limit 1;
  </select>
</mapper>