<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TestMobileMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.test.TestMobileDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="mobile_enc" property="mobile" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="expired_time" property="expiredTime" jdbcType="TIMESTAMP"/>
        <result column="detect_time" property="detectTime" jdbcType="TIMESTAMP"/>
        <result column="param" property="param" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,mobile_enc,type,remark,create_time,update_time,creator,updater,status,expired_time,detect_time,param
    </sql>
    <insert id="insertBySelective">
        insert into t_test_mobile
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="mobile != null">
                mobile_enc,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="expiredTime != null">
                expired_time,
            </if>
            <if test="detectTime != null">
                detect_time,
            </if>
            <if test="param != null">
                param,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="expiredTime != null">
                #{expiredTime,jdbcType=TIMESTAMP},
            </if>
            <if test="detectTime != null">
                #{detectTime,jdbcType=TIMESTAMP},
            </if>
            <if test="param != null">
                #{param,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateDeleteByIdList">
        update t_test_mobile
        set is_delete = 1
        where id in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        and is_delete = 0
    </update>
    <update id="updateDeleteByMobileList">
        update t_test_mobile
        set is_delete = 1
        where is_delete = 0 and mobile_enc in
        <foreach collection="mobiles" item="mobile" separator="," open="(" close=")">
            #{mobile}
        </foreach>
    </update>
    <update id="updateDetectTimeByMobileList">
        update t_test_mobile
        set detect_time = now(),expired_time = #{expiryDate}
        where is_delete = 0 and mobile_enc in
        <foreach collection="mobiles" item="mobile" separator="," open="(" close=")">
            #{mobile}
        </foreach>
    </update>
    <update id="updateStatusByMobileList">
        update t_test_mobile
        set status = 0
        where is_delete = 0 and mobile_enc in
        <foreach collection="mobiles" item="mobile" separator="," open="(" close=")">
            #{mobile}
        </foreach>
    </update>
    <update id="updateByPrimaryKeySelective">
        update t_test_mobile
        <set>
            <if test="mobile != null">
                mobile_enc = #{mobile},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updater != null">
                updater = #{updater},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="expiredTime != null">
                expired_time = #{expiredTime},
            </if>
            <if test="param != null">
                param = #{param},
            </if>
        </set>
        where id = #{id}
    </update>
    <select id="countByQuery" resultType="java.lang.Integer">
        select count(*)
        from t_test_mobile
        <where>
            is_delete = 0
            <if test="query.mobile != null">
                and mobile_enc = #{query.mobile}
            </if>
            <if test="query.type != null">
                and type = #{query.type}
            </if>
            <if test="query.status != null">
                and status = #{query.status}
            </if>
        </where>
    </select>
    <select id="listByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_mobile
        <where>
            is_delete = 0
            <if test="query.mobile != null">
                and mobile_enc = #{query.mobile}
            </if>
            <if test="query.type != null">
                and type = #{query.type}
            </if>
            <if test="query.status != null">
                and status = #{query.status}
            </if>
        </where>
        order by update_time desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset},#{query.pageParameter.pageSize}
        </if>
    </select>
    <select id="selectByMobiles" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_mobile
        where is_delete = 0
        and mobile_enc in
        <foreach collection="mobiles" item="mobile" open="(" separator="," close=")">
            #{mobile}
        </foreach>
    </select>
    <select id="countAll" resultType="java.lang.Integer">
        select count(*)
        from t_test_mobile
        where is_delete = 0
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_mobile
        where is_delete = 0 and status = 1 and expired_time >= now()
    </select>
    <select id="selectInvalid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_mobile
        where is_delete = 0 and status = 1 and expired_time &lt; now()
    </select>
    <select id="selectByUpdateTimeLimit" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_mobile
        where is_delete = 0
        order by update_time desc
        limit 1
    </select>


</mapper>
