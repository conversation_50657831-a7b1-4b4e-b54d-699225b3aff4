<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.OpLogMapper">
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.OpLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="sys_name" property="sysName" jdbcType="VARCHAR" />
    <result column="op_type" property="opType" />
    <result column="req_method" property="reqMethod" jdbcType="VARCHAR" />
    <result column="req_uri" property="reqUri" jdbcType="VARCHAR" />
    <result column="action_name" property="actionName" jdbcType="VARCHAR" />
    <result column="req_param" property="reqParam" jdbcType="VARCHAR" />
    <result column="req_ip" property="reqIp" jdbcType="VARCHAR" />
    <result column="operator" property="operator" jdbcType="VARCHAR" />
    <result column="op_time" property="opTime" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, sys_name, op_type, req_method, req_uri, action_name, req_param, req_ip, operator, op_time,
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from t_op_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_op_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.OpLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_op_log (id, sys_name, op_type, req_method,
    req_uri, action_name, req_param,
    req_ip, operator, op_time,
    create_time, update_time)
    values (#{id,jdbcType=INTEGER}, #{sysName,jdbcType=VARCHAR}, #{opType}, #{reqMethod,jdbcType=VARCHAR},
    #{reqUri,jdbcType=VARCHAR}, #{actionName,jdbcType=VARCHAR}, #{reqParam,jdbcType=VARCHAR},
    #{reqIp,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{opTime,jdbcType=INTEGER},
    #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.OpLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_op_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="sysName != null" >
        sys_name,
      </if>
      <if test="opType != null" >
        op_type,
      </if>
      <if test="reqMethod != null" >
        req_method,
      </if>
      <if test="reqUri != null" >
        req_uri,
      </if>
      <if test="actionName != null" >
        action_name,
      </if>
      <if test="reqParam != null" >
        req_param,
      </if>
      <if test="reqIp != null" >
        req_ip,
      </if>
      <if test="operator != null" >
        operator,
      </if>
      <if test="opTime != null" >
        op_time,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="sysName != null" >
        #{sysName,jdbcType=VARCHAR},
      </if>
      <if test="opType != null" >
        #{opType},
      </if>
      <if test="reqMethod != null" >
        #{reqMethod,jdbcType=VARCHAR},
      </if>
      <if test="reqUri != null" >
        #{reqUri,jdbcType=VARCHAR},
      </if>
      <if test="actionName != null" >
        #{actionName,jdbcType=VARCHAR},
      </if>
      <if test="reqParam != null" >
        #{reqParam,jdbcType=VARCHAR},
      </if>
      <if test="reqIp != null" >
        #{reqIp,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="opTime != null" >
        #{opTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.OpLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_op_log
    <set >
      <if test="sysName != null" >
        sys_name = #{sysName,jdbcType=VARCHAR},
      </if>
      <if test="opType != null" >
        op_type = #{opType},
      </if>
      <if test="reqMethod != null" >
        req_method = #{reqMethod,jdbcType=VARCHAR},
      </if>
      <if test="reqUri != null" >
        req_uri = #{reqUri,jdbcType=VARCHAR},
      </if>
      <if test="actionName != null" >
        action_name = #{actionName,jdbcType=VARCHAR},
      </if>
      <if test="reqParam != null" >
        req_param = #{reqParam,jdbcType=VARCHAR},
      </if>
      <if test="reqIp != null" >
        req_ip = #{reqIp,jdbcType=VARCHAR},
      </if>
      <if test="operator != null" >
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="opTime != null" >
        op_time = #{opTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.OpLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_op_log
    set sys_name = #{sysName,jdbcType=VARCHAR},
    op_type = #{opType},
    req_method = #{reqMethod,jdbcType=VARCHAR},
    req_uri = #{reqUri,jdbcType=VARCHAR},
    action_name = #{actionName,jdbcType=VARCHAR},
    req_param = #{reqParam,jdbcType=VARCHAR},
    req_ip = #{reqIp,jdbcType=VARCHAR},
    operator = #{operator,jdbcType=VARCHAR},
    op_time = #{opTime,jdbcType=INTEGER},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>