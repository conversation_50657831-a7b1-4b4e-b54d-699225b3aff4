<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.BatchTestLogMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.BatchTestLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="task_id" property="taskId" jdbcType="INTEGER" />
    <result column="req_content" property="reqContent" jdbcType="VARCHAR" />
    <result column="resp_content" property="respContent" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="updater" property="updater" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, task_id, req_content, resp_content, status, create_time, creator, update_time, 
    updater
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_batch_test_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_batch_test_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.BatchTestLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_test_log (id, task_id, req_content, 
      resp_content, status, create_time, 
      creator, update_time, updater
      )
    values (#{id,jdbcType=INTEGER}, #{taskId,jdbcType=INTEGER}, #{reqContent,jdbcType=VARCHAR}, 
      #{respContent,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchTestLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_test_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="taskId != null" >
        task_id,
      </if>
      <if test="reqContent != null" >
        req_content,
      </if>
      <if test="respContent != null" >
        resp_content,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updater != null" >
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="taskId != null" >
        #{taskId,jdbcType=INTEGER},
      </if>
      <if test="reqContent != null" >
        #{reqContent,jdbcType=VARCHAR},
      </if>
      <if test="respContent != null" >
        #{respContent,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchTestLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_test_log
    <set >
      <if test="taskId != null" >
        task_id = #{taskId,jdbcType=INTEGER},
      </if>
      <if test="reqContent != null" >
        req_content = #{reqContent,jdbcType=VARCHAR},
      </if>
      <if test="respContent != null" >
        resp_content = #{respContent,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.BatchTestLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_test_log
    set task_id = #{taskId,jdbcType=INTEGER},
      req_content = #{reqContent,jdbcType=VARCHAR},
      resp_content = #{respContent,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="queryTestLog" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from t_batch_test_log
    where task_id = #{taskId}
    order by id desc limit 10
  </select>
</mapper>