<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.BrandMappingMapper">
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.BrandMappingDO">
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="brand" property="brand" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List">
    id, brand, type, code, name, create_time, update_time
  </sql>
    <insert id="insertSelective">
      insert into t_brand_mapping
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="brand != null">
          brand,
        </if>
        <if test="type != null">
            type,
        </if>
        <if test="code != null">
          code,
        </if>
        <if test="name != null">
          name,
        </if>
        <if test="createTime != null">
          create_time,
        </if>
        <if test="updateTime != null">
          update_time,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="brand != null">
          #{brand,jdbcType=VARCHAR},
        </if>
        <if test="type != null">
          #{type,jdbcType=VARCHAR},
        </if>
        <if test="code != null">
          #{code,jdbcType=VARCHAR},
        </if>
        <if test="name != null">
          #{name,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null">
          #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="updateTime != null">
          #{updateTime,jdbcType=TIMESTAMP},
        </if>
      </trim>
    </insert>
    <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_brand_mapping
  </select>
</mapper>
