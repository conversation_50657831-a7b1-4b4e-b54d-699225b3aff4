<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TplContentMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.oa.TplContent">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="content_id" property="contentId" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="original_content" property="originalContent" jdbcType="VARCHAR"/>
        <result column="approved_content" property="approvedContent" jdbcType="VARCHAR"/>
        <result column="sign_id" property="signId" jdbcType="VARCHAR"/>
        <result column="tpl_code" property="tplCode" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="scene_code" property="sceneCode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="tag" jdbcType="TINYINT" property="tag"/>
        <result column="param_type" property="paramType" jdbcType="VARCHAR"/>
        <result column="param_len" property="paramLen" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , content_id, `type`, status, original_content, approved_content, sign_id, tpl_code, creator
          ,updater, content, sms_type_code, channel_code, scene_code, create_time, update_time, tag,param_type,param_len
    </sql>
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into t_tpl_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="contentId != null">
                content_id,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="originalContent != null">
                original_content,
            </if>
            <if test="approvedContent != null">
                approved_content,
            </if>
            <if test="signId != null">
                sign_id,
            </if>
            <if test="tplCode != null">
                tpl_code,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="smsTypeCode != null">
                sms_type_code,
            </if>
            <if test="channelCode != null">
                channel_code,
            </if>
            <if test="sceneCode != null">
                scene_code,
            </if>
            <if test="tag != null">
                tag,
            </if>
            <if test="paramType != null">
                param_type,
            </if>
            <if test="paramLen != null">
                param_len,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="contentId != null">
                #{contentId,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="originalContent != null">
                #{originalContent,jdbcType=VARCHAR},
            </if>
            <if test="approvedContent != null">
                #{approvedContent,jdbcType=VARCHAR},
            </if>
            <if test="signId != null">
                #{signId,jdbcType=VARCHAR},
            </if>
            <if test="tplCode != null">
                #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null">
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="sceneCode != null">
                #{sceneCode,jdbcType=VARCHAR},
            </if>
            <if test="tag != null">
                #{tag,jdbcType=TINYINT},
            </if>
            <if test="paramType != null">
                #{paramType,jdbcType=VARCHAR},
            </if>
            <if test="paramLen != null">
                #{paramLen,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByContentId">
        update t_tpl_content
        set approved_content = #{content},
            status           = #{status}
        where content_id = #{contentId}
    </update>
    <update id="updateByPrimaryKeySelective">
        update t_tpl_content
        <set>
            <if test="contentId != null">
                content_id = #{contentId,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="originalContent != null">
                original_content = #{originalContent,jdbcType=VARCHAR},
            </if>
            <if test="approvedContent != null">
                approved_content = #{approvedContent,jdbcType=VARCHAR},
            </if>
            <if test="signId != null">
                sign_id = #{signId,jdbcType=VARCHAR},
            </if>
            <if test="tplCode != null">
                tpl_code = #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null">
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="tag != null">
                tag = #{tag,jdbcType=TINYINT},
            </if>
            <if test="paramType != null">
                param_type = #{paramType,jdbcType=VARCHAR},
            </if>
            <if test="paramLen != null">
                param_len = #{paramLen,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateDeleteTagByContentId">
        update t_tpl_content
        set is_delete = #{deleteTag}
        where content_id = #{contentId}
    </update>
    <select id="selectByContentIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_content
        where is_delete = 0 and content_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_content
        <where>
            and is_delete = 0
            <if test="query.smsTypeCode != null">
                and sms_type_code = #{query.smsTypeCode}
            </if>
            <if test="query.sceneCode != null">
                and scene_code = #{query.sceneCode}
            </if>
            <if test="query.status != null">
                and status = #{query.status}
            </if>
            <if test="query.contentType != null">
                and `type` = #{query.contentType}
            </if>
            <if test="query.creator != null">
                and creator like concat('%',#{query.creator},'%')
            </if>
            <if test="query.startTime != null">
                and create_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and create_time &lt;= #{query.endTime}
            </if>
            <if test="query.content != null">
                and (original_content like concat('%',#{query.content},'%') or approved_content like
                concat('%',#{query.content},'%'))
            </if>
            <if test="query.signId != null">
                and sign_id = #{query.signId}
            </if>
            <if test="query.contentId != null">
                and content_id = #{query.contentId}
            </if>
            <if test="query.channelCode != null">
                and channel_code like concat('%',#{query.channelCode},'%')
            </if>
            <if test="query.tplCodeList != null and query.tplCodeList.size() > 0">
                and tpl_code in
                <foreach item="item" collection="query.tplCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by update_time desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset},#{query.pageParameter.pageSize}
        </if>
    </select>
    <select id="countByQuery" resultType="java.lang.Integer">
        select count(*)
        from t_tpl_content
        <where>
            and is_delete = 0
            <if test="query.smsTypeCode != null">
                and sms_type_code = #{query.smsTypeCode}
            </if>
            <if test="query.sceneCode != null">
                and scene_code = #{query.sceneCode}
            </if>
            <if test="query.status != null">
                and status = #{query.status}
            </if>
            <if test="query.contentType != null">
                and `type` = #{query.contentType}
            </if>
            <if test="query.creator != null">
                and creator like concat('%',#{query.creator},'%')
            </if>
            <if test="query.startTime != null">
                and create_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and create_time &lt;= #{query.endTime}
            </if>
            <if test="query.content != null">
                and (original_content like concat('%',#{query.content},'%') or approved_content like
                concat('%',#{query.content},'%'))
            </if>
            <if test="query.signId != null">
                and sign_id = #{query.signId}
            </if>
            <if test="query.contentId != null">
                and content_id = #{query.contentId}
            </if>
            <if test="query.channelCode != null">
                and channel_code like concat('%',#{query.channelCode},'%')
            </if>
            <if test="query.tplCodeList != null and query.tplCodeList.size() > 0">
                and tpl_code in
                <foreach item="item" collection="query.tplCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectByStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_content
        where is_delete = 0 and status = #{status} and `type` = 'IN'
        and create_time &gt;= #{startDate} and create_time &lt;= #{endDate}
    </select>
    <select id="selectByContentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_content
        where content_id = #{contentId}
        and is_delete = 0
    </select>

    <select id="selectByTplCodeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_content
        where is_delete = 0 and tpl_code in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectTplCodesByCreator" resultType="java.lang.String">
        select DISTINCT tpl_code
        from t_tpl_content
        where is_delete = 0 and creator = #{creator}
        and tpl_code is not null and tpl_code != ''
    </select>
</mapper>
