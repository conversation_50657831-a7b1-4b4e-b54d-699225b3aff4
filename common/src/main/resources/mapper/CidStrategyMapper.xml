<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.CidStrategyMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.CidStrategyDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="strategy_value" property="strategyValue" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updater" property="updater" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="strategy_group" property="strategyGroup" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, strategy_value, type, status, description, creator, create_time, updater, update_time, strategy_group
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from t_cid_strategy
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_cid_strategy
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.CidStrategyDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_cid_strategy (id, strategy_value, type,
    status, description, creator,
    create_time, updater, update_time, strategy_group
    )
    values (#{id,jdbcType=INTEGER}, #{strategyValue,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR},
    #{status,jdbcType=TINYINT}, #{description,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR},
    #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{strategyGroup}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.CidStrategyDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_cid_strategy
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="strategyValue != null" >
        strategy_value,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updater != null" >
        updater,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="strategyGroup != null" >
        strategy_group,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="strategyValue != null" >
        #{strategyValue,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="strategyGroup != null" >
        #{strategyGroup},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.CidStrategyDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_cid_strategy
    <set >
      <if test="strategyValue != null" >
        strategy_value = #{strategyValue,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="strategyGroup != null" >
        strategy_group = #{strategyGroup},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.CidStrategyDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_cid_strategy
    set strategy_value = #{strategyValue,jdbcType=VARCHAR},
    type = #{type,jdbcType=VARCHAR},
    status = #{status,jdbcType=TINYINT},
    description = #{description,jdbcType=VARCHAR},
    creator = #{creator,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    updater = #{updater,jdbcType=VARCHAR},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    strategy_group = #{strategyGroup}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByGroup" resultMap="BaseResultMap" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from t_cid_strategy
    where strategy_group = #{strategyGroup}
  </select>

  <update id="resetStatusByGroup">
    update t_cid_strategy
    set status = 0
    <!-- 添加个where条件吧 不加where条件看起来心慌 -->
    where id in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and strategy_group = #{strategyGroup}
  </update>

  <select id="selectByType" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from t_cid_strategy
    where type = #{type}
  </select>

  <select id="selectAll" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from t_cid_strategy
  </select>

  <update id="initStrategyGroup">
    update t_cid_strategy
    set strategy_group = 'userCheckGroup'
    where strategy_group = ''
  </update>
</mapper>