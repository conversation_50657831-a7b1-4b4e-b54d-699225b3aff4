<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TplUsageMapper">

    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.TplUsageDO">
            <id property="orderId" column="order_id" jdbcType="BIGINT"/>
            <result property="appCode" column="app_code" jdbcType="VARCHAR"/>
            <result property="tplCode" column="tpl_code" jdbcType="VARCHAR"/>
            <result property="smsTypeCode" column="sms_type_code" jdbcType="VARCHAR"/>
            <result property="signName" column="sign_name" jdbcType="VARCHAR"/>
            <result property="sendTime" column="send_time" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        order_id,app_code,tpl_code,
        sms_type_code,sign_name,send_time,
        create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_tpl_usage
        where  order_id = #{orderId,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_tpl_usage
        where  order_id = #{orderId,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="order_id" keyProperty="orderId" parameterType="com.xhqb.spectre.common.dal.entity.TplUsageDO" useGeneratedKeys="true">
        insert into t_tpl_usage
        ( order_id,app_code,tpl_code
        ,sms_type_code,sign_name,send_time
        ,create_time,update_time)
        values (#{orderId,jdbcType=BIGINT},#{appCode,jdbcType=VARCHAR},#{tplCode,jdbcType=VARCHAR}
        ,#{smsTypeCode,jdbcType=VARCHAR},#{signName,jdbcType=VARCHAR},#{sendTime,jdbcType=INTEGER}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="order_id" keyProperty="orderId" parameterType="com.xhqb.spectre.common.dal.entity.TplUsageDO" useGeneratedKeys="true">
        insert into t_tpl_usage
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="orderId != null">order_id,</if>
                <if test="appCode != null">app_code,</if>
                <if test="tplCode != null">tpl_code,</if>
                <if test="smsTypeCode != null">sms_type_code,</if>
                <if test="signName != null">sign_name,</if>
                <if test="sendTime != null">send_time,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="orderId != null">#{orderId,jdbcType=BIGINT},</if>
                <if test="appCode != null">#{appCode,jdbcType=VARCHAR},</if>
                <if test="tplCode != null">#{tplCode,jdbcType=VARCHAR},</if>
                <if test="smsTypeCode != null">#{smsTypeCode,jdbcType=VARCHAR},</if>
                <if test="signName != null">#{signName,jdbcType=VARCHAR},</if>
                <if test="sendTime != null">#{sendTime,jdbcType=INTEGER},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.TplUsageDO">
        update t_tpl_usage
        <set>
                <if test="appCode != null">
                    app_code = #{appCode,jdbcType=VARCHAR},
                </if>
                <if test="tplCode != null">
                    tpl_code = #{tplCode,jdbcType=VARCHAR},
                </if>
                <if test="smsTypeCode != null">
                    sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
                </if>
                <if test="signName != null">
                    sign_name = #{signName,jdbcType=VARCHAR},
                </if>
                <if test="sendTime != null">
                    send_time = #{sendTime,jdbcType=INTEGER},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   order_id = #{orderId,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.TplUsageDO">
        update t_tpl_usage
        set 
            app_code =  #{appCode,jdbcType=VARCHAR},
            tpl_code =  #{tplCode,jdbcType=VARCHAR},
            sms_type_code =  #{smsTypeCode,jdbcType=VARCHAR},
            sign_name =  #{signName,jdbcType=VARCHAR},
            send_time =  #{sendTime,jdbcType=INTEGER},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   order_id = #{orderId,jdbcType=BIGINT} 
    </update>

    <select id="findTplCodesBySendTime" resultType="string">
        SELECT DISTINCT tpl_code
        FROM t_tpl_usage
        WHERE send_time <![CDATA[ >= ]]> #{start}
          AND send_time <![CDATA[ <= ]]> #{end}
    </select>
</mapper>
