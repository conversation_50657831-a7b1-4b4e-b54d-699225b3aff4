<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ChannelAccountMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ChannelAccountDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode"/>
        <result column="sms_type_code" jdbcType="VARCHAR" property="smsTypeCode"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="key" jdbcType="VARCHAR" property="key"/>
        <result column="json_mapping" jdbcType="VARCHAR" property="jsonMapping"/>
        <result column="price" jdbcType="INTEGER" property="price"/>
        <result column="protocol" jdbcType="TINYINT" property="protocol"/>
        <result column="isps" property="isps" jdbcType="VARCHAR" />
        <result column="area_filter_type" property="areaFilterType" jdbcType="TINYINT" />
        <result column="areas" property="areas" jdbcType="VARCHAR" />
        <result column="weight" jdbcType="INTEGER" property="weight"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_tpl_free_audit" jdbcType="TINYINT" property="isTplFreeAudit"/>
        <result column="sign_ids" jdbcType="VARCHAR" property="signIds"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <association property="channelDO" javaType="com.xhqb.spectre.common.dal.entity.ChannelDO">
            <id column="channel_id" property="id"/>
            <result column="channel_name" property="name"/>
        </association>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, channel_code, sms_type_code, `name`, `key`, json_mapping, price, protocol, isps, area_filter_type, areas, weight,
        status, is_tpl_free_audit, sign_ids, remark, creator, create_time, updater, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from t_channel_account
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from t_channel_account
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.ChannelAccountDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_channel_account (id, channel_code, sms_type_code,
        `name`, `key`, json_mapping,
        price, protocol, isps,
        area_filter_type, areas,weight,
        status, is_tpl_free_audit, sign_ids, remark,
        creator, create_time, updater,
        update_time)
        values (#{id,jdbcType=INTEGER}, #{channelCode,jdbcType=VARCHAR}, #{smsTypeCode,jdbcType=VARCHAR},
        #{name,jdbcType=VARCHAR}, #{key,jdbcType=VARCHAR}, #{jsonMapping,jdbcType=VARCHAR},
        #{price,jdbcType=INTEGER}, #{protocol,jdbcType=TINYINT},#{isps,jdbcType=VARCHAR},
        #{areaFilterType,jdbcType=TINYINT}, #{areas,jdbcType=VARCHAR},  #{weight,jdbcType=INTEGER},
        #{status,jdbcType=TINYINT}, #{isTplFreeAudit,jdbcType=TINYINT}, #{signIds,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.ChannelAccountDO" useGeneratedKeys="true" keyProperty="id">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_channel_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="channelCode != null">
                channel_code,
            </if>
            <if test="smsTypeCode != null">
                sms_type_code,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="key != null">
                `key`,
            </if>
            <if test="jsonMapping != null">
                json_mapping,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="protocol != null">
                protocol,
            </if>
            <if test="isps != null">
                isps,
            </if>
            <if test="areaFilterType != null">
                area_filter_type,
            </if>
            <if test="areas != null">
                areas,
            </if>
            <if test="weight != null">
                weight,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="isTplFreeAudit != null">
                is_tpl_free_audit,
            </if>
            <if test="signIds != null">
                sign_ids,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="channelCode != null">
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="key != null">
                #{key,jdbcType=VARCHAR},
            </if>
            <if test="jsonMapping != null">
                #{jsonMapping,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=INTEGER},
            </if>
            <if test="protocol != null">
                #{protocol,jdbcType=TINYINT},
            </if>
            <if test="isps != null">
                #{isps,jdbcType=VARCHAR},
            </if>
            <if test="areaFilterType != null">
                #{areaFilterType,jdbcType=TINYINT},
            </if>
            <if test="areas != null">
                #{areas,jdbcType=VARCHAR},
            </if>
            <if test="weight != null">
                #{weight,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="isTplFreeAudit != null">
                #{isTplFreeAudit,jdbcType=TINYINT},
            </if>
            <if test="signIds != null">
                #{signIds,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.ChannelAccountDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_channel_account
        <set>
            <if test="channelCode != null">
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="smsTypeCode != null">
                sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="key != null">
                `key` = #{key,jdbcType=VARCHAR},
            </if>
            <if test="jsonMapping != null">
                json_mapping = #{jsonMapping,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=INTEGER},
            </if>
            <if test="protocol != null">
                protocol = #{protocol,jdbcType=TINYINT},
            </if>
            <if test="isps != null">
                isps = #{isps,jdbcType=VARCHAR},
            </if>
            <if test="areaFilterType != null">
                area_filter_type = #{areaFilterType,jdbcType=TINYINT},
            </if>
            <if test="areas != null">
                areas = #{areas,jdbcType=VARCHAR},
            </if>
            <if test="weight != null">
                weight = #{weight,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="isTplFreeAudit != null">
                is_tpl_free_audit = #{isTplFreeAudit,jdbcType=TINYINT},
            </if>
            <if test="signIds != null">
                sign_ids = #{signIds,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.ChannelAccountDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_channel_account
        set channel_code = #{channelCode,jdbcType=VARCHAR},
        sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
        name = #{name,jdbcType=VARCHAR},
        key = #{key,jdbcType=VARCHAR},
        json_mapping = #{jsonMapping,jdbcType=VARCHAR},
        price = #{price,jdbcType=INTEGER},
        protocol = #{protocol,jdbcType=TINYINT},
        isps = #{isps,jdbcType=VARCHAR},
        area_filter_type = #{areaFilterType,jdbcType=TINYINT},
        areas = #{areas,jdbcType=VARCHAR},
        weight = #{weight,jdbcType=INTEGER},
        status = #{status,jdbcType=TINYINT},
        is_tpl_free_audit = #{isTplFreeAudit,jdbcType=TINYINT},
        sign_ids = #{signIds,jdbcType=VARCHAR}
        remark = #{remark,jdbcType=VARCHAR},
        creator = #{creator,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater = #{updater,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectEnum" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select m.`id`, m.`channel_code`, m.`sms_type_code`, m.`name`, m.`status`, n.`name` as 'channel_name'
        from t_channel_account m left join t_channel n on m.channel_code = n.code
        <where>
            n.is_delete = 0
            <if test="status != null and status != -1">and m.status = #{status}</if>
        </where>
    </select>

    <select id="queryPartnerByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        t_channel_account
        where 1=1
        <if test="partnerName !=null and partnerName !=''">
            and channel_code = #{partnerName, jdbcType=VARCHAR}
        </if>
        <if test="messageType !=null and messageType !=''">
            and sms_type_code = #{messageType,jdbcType=VARCHAR}
        </if>
        and status = '1'
        limit 1
    </select>

    <select id="selectByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_channel_account
        where id in
        <foreach item="id" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.ChannelAccountQuery" resultType="java.lang.Integer">
        select count(*)
        from t_channel_account ca
        <where>
            <if test="channelName != null">
                and ca.`name` like concat('%', #{channelName}, '%')
            </if>
            <if test="smsTypeCode != null">
                and ca.sms_type_code = #{smsTypeCode}
            </if>
            <if test="status != null">
                and ca.status = #{status}
            </if>
            <if test="key != null">
                and ca.`key` = #{key}
            </if>
            <if test="protocol != null">
                and ca.protocol = #{protocol}
            </if>
            <if test="signId != null">
                and find_in_set(#{signId}, ca.sign_ids)
            </if>
        </where>
    </select>

    <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.ChannelAccountQuery" resultMap="BaseResultMap">
        select
        ca.id, ca.channel_code, ca.sms_type_code, ca.`name`, ca.`key`, ca.json_mapping, ca.price, ca.protocol, ca.isps, ca.area_filter_type, ca.areas, ca.weight,
        ca.status, ca.is_tpl_free_audit, ca.sign_ids, ca.remark, ca.creator, ca.create_time, ca.updater, ca.update_time
        from t_channel_account ca
        <where>
            <if test="id != null">
                and ca.id = #{id}
            </if>
            <if test="channelName != null">
                and ca.`name` like concat('%', #{channelName}, '%')
            </if>
            <if test="smsTypeCode != null">
                and ca.sms_type_code = #{smsTypeCode}
            </if>
            <if test="status != null">
                and ca.status = #{status}
            </if>
            <if test="key != null">
                and ca.`key` = #{key}
            </if>
            <if test="protocol != null">
                and ca.protocol = #{protocol}
            </if>
            <if test="signId != null">
                and find_in_set(#{signId}, ca.sign_ids)
            </if>
        </where>
        order by ca.update_time desc
        <if test="pageParameter != null">
            limit #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>

    <select id="refreshCacheQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_channel_account
        where id <![CDATA[ > ]]> #{id}
        order by id asc limit #{pageSize}
    </select>

    <select id="selectAllEnabled" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_channel_account
        where status = 1
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_channel_account
    </select>
</mapper>