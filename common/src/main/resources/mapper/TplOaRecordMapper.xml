<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TplOpRecordMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.TplOpRecordDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="sign_id" property="signId" jdbcType="INTEGER"/>
        <result column="business_line_id" property="businessLineId" jdbcType="INTEGER"/>
        <result column="market_scene_id" property="marketSceneId" jdbcType="INTEGER"/>
        <result column="channel_info" property="channelInfo" jdbcType="VARCHAR"/>
        <result column="disable_info" property="disableInfo" jdbcType="VARCHAR"/>
        <result column="app_code" property="appCode" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="operate_time" property="operateTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , `type`, title, status, sign_id, business_line_id, market_scene_id, channel_info, disable_info
        , app_code, content, operator, operate_time, create_time, update_time,sms_type_code,code
    </sql>
    <insert id="insertSelective">
        insert into t_tpl_op_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="signId != null">
                sign_id,
            </if>
            <if test="businessLineId != null">
                business_line_id,
            </if>
            <if test="marketSceneId != null">
                market_scene_id,
            </if>
            <if test="channelInfo != null">
                channel_info,
            </if>
            <if test="disableInfo != null">
                disable_info,
            </if>
            <if test="appCode != null">
                app_code,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="operateTime != null">
                operate_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="smsTypeCode != null">
                sms_type_code,
            </if>
            <if test="code != null">
                code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="signId != null">
                #{signId,jdbcType=INTEGER},
            </if>
            <if test="businessLineId != null">
                #{businessLineId,jdbcType=INTEGER},
            </if>
            <if test="marketSceneId != null">
                #{marketSceneId,jdbcType=INTEGER},
            </if>
            <if test="channelInfo != null">
                #{channelInfo,jdbcType=VARCHAR},
            </if>
            <if test="disableInfo != null">
                #{disableInfo,jdbcType=VARCHAR},
            </if>
            <if test="appCode != null">
                #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operateTime != null">
                #{operateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="smsTypeCode != null">
                #{smsTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByQuery" resultType="java.lang.Integer">
        select count(*)
        from t_tpl_op_record
        <where>
            <if test="query.signId != null">
                and sign_id = #{query.signId,jdbcType=INTEGER}
            </if>
            <if test="query.code != null">
                and code = #{query.code,jdbcType=VARCHAR}
            </if>
            <if test="query.smsTypeCode != null">
                and sms_type_code = #{query.smsTypeCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_op_record
        <where>
            <if test="query.signId != null">
                and sign_id = #{query.signId,jdbcType=INTEGER}
            </if>
            <if test="query.code != null">
                and code = #{query.code,jdbcType=VARCHAR}
            </if>
            <if test="query.smsTypeCode != null">
                and sms_type_code = #{query.smsTypeCode,jdbcType=VARCHAR}
            </if>
        </where>
        order by update_time desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset}, #{query.pageParameter.pageSize}
        </if>

    </select>
    <select id="selectByQueryOrderById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_op_record
        <where>
            <if test="query.signId != null">
                and sign_id = #{query.signId,jdbcType=INTEGER}
            </if>
            <if test="query.code != null">
                and code = #{query.code,jdbcType=VARCHAR}
            </if>
            <if test="query.smsTypeCode != null">
                and sms_type_code = #{query.smsTypeCode,jdbcType=VARCHAR}
            </if>
            <if test="query.id != null">
                and id &gt; #{query.id,jdbcType=INTEGER}
            </if>
            order by id asc limit 1
        </where>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_tpl_op_record
        where id = #{id,jdbcType=INTEGER}
    </select>


</mapper>
