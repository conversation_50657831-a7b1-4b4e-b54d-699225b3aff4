<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.BatchTaskBigdataMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.BatchTaskBigdataDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="app_code" property="appCode" jdbcType="VARCHAR" />
    <result column="tpl_id" property="tplId" jdbcType="INTEGER" />
    <result column="file_url" property="fileUrl" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="send_time" property="sendTime" jdbcType="INTEGER" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updater" property="updater" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="task_no" property="taskNo" jdbcType="VARCHAR" />
    <result column="task_id" property="taskId" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="project_desc" property="projectDesc" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, app_code, tpl_id, file_url, status, send_time, description, creator, create_time,
    updater, update_time, task_no, task_id, remark, project_desc
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from t_batch_task_bigdata
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_batch_task_bigdata
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskBigdataDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_task_bigdata (id, app_code, tpl_id,
    file_url, status, send_time,
    description, creator, create_time,
    updater, update_time, task_no,
    task_id, remark, project_desc)
    values (#{id,jdbcType=INTEGER}, #{appCode,jdbcType=VARCHAR}, #{tplId,jdbcType=INTEGER},
    #{fileUrl,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{sendTime,jdbcType=INTEGER},
    #{description,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{taskNo,jdbcType=VARCHAR},
    #{taskId,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{projectDesc})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskBigdataDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_task_bigdata
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="appCode != null" >
        app_code,
      </if>
      <if test="tplId != null" >
        tpl_id,
      </if>
      <if test="fileUrl != null" >
        file_url,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="sendTime != null" >
        send_time,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updater != null" >
        updater,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="taskNo != null" >
        task_no,
      </if>
      <if test="taskId != null" >
        task_id,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="projectDesc != null" >
        project_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="appCode != null" >
        #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="tplId != null" >
        #{tplId,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null" >
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="sendTime != null" >
        #{sendTime,jdbcType=INTEGER},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskNo != null" >
        #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null" >
        #{taskId,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="projectDesc != null" >
        #{projectDesc},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskBigdataDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_task_bigdata
    <set >
      <if test="appCode != null" >
        app_code = #{appCode,jdbcType=VARCHAR},
      </if>
      <if test="tplId != null" >
        tpl_id = #{tplId,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null" >
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="sendTime != null" >
        send_time = #{sendTime,jdbcType=INTEGER},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskNo != null" >
        task_no = #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null" >
        task_id = #{taskId,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="projectDesc != null" >
        project_desc = #{projectDesc},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskBigdataDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_task_bigdata
    set app_code = #{appCode,jdbcType=VARCHAR},
    tpl_id = #{tplId,jdbcType=INTEGER},
    file_url = #{fileUrl,jdbcType=VARCHAR},
    status = #{status,jdbcType=TINYINT},
    send_time = #{sendTime,jdbcType=INTEGER},
    description = #{description,jdbcType=VARCHAR},
    creator = #{creator,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    updater = #{updater,jdbcType=VARCHAR},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    task_no = #{taskNo,jdbcType=VARCHAR},
    task_id = #{taskId,jdbcType=INTEGER},
    remark = #{remark,jdbcType=VARCHAR},
    project_desc = #{projectDesc}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.BatchTaskBigdataQuery" resultType="java.lang.Integer">
    select count(1)
    from t_batch_task_bigdata
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="appCode != null">
        and app_code = #{appCode}
      </if>
      <if test="tplId != null">
        and tpl_id = #{tplId}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="taskId != null">
        and task_id = #{taskId}
      </if>
      <if test="fileUrl != null">
        and file_url like concat('%', #{fileUrl}, '%')
      </if>
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.BatchTaskBigdataQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_batch_task_bigdata
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="appCode != null">
        and app_code = #{appCode}
      </if>
      <if test="tplId != null">
        and tpl_id = #{tplId}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="taskId != null">
        and task_id = #{taskId}
      </if>
      <if test="fileUrl != null">
        and file_url like concat('%', #{fileUrl}, '%')
      </if>
    </where>
    order by update_time desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>

  <select id="selectEarliestOneByStatus" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from t_batch_task_bigdata
    where id <![CDATA[ > ]]> #{lastId}
        and create_time <![CDATA[ <= ]]> now()
        and status = #{status}
    order by id asc limit 1
  </select>

  <select id="jobScan" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from t_batch_task_bigdata
    where id <![CDATA[ > ]]> #{lastId}
    and status = #{status}
    order by id asc
    limit #{pageSize}
  </select>

</mapper>