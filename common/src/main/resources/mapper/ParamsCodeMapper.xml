<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ParamsCodeMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ParamsCodeDO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>

    </resultMap>
    <sql id="Base_Column_List">
        id
        , code, name, type, creator, create_time, updater, update_time, is_delete
    </sql>
    <insert id="insertSelective">
        insert into t_test_param_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateDeleteTagByCodeList">
        update t_test_param_code
        set is_delete = 1
        where code in
        <foreach collection="codeList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        and is_delete = 0
    </update>
    <select id="countByQuery" resultType="java.lang.Integer">
        select count(*)
        from t_test_param_code
        <where>
            is_delete = 0
            <if test="query.name != null">
                and name like concat('%',#{query.name},'%')
            </if>
            <if test="query.codes != null">
                and code in
                <foreach collection="query.codes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_param_code
        <where>
            is_delete = 0
            <if test="query.name != null">
                and name like concat('%',#{query.name},'%')
            </if>
            <if test="query.codes != null">
                and code in
                <foreach collection="query.codes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by update_time desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset},#{query.pageParameter.pageSize}
        </if>
    </select>
    <select id="selectByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_param_code
        where is_delete = 0
        and code = #{code}
        limit 1
    </select>
    <select id="selectByType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_param_code
        where is_delete = 0
        and type = #{type}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_param_code
        where is_delete = 0

    </select>


</mapper>
