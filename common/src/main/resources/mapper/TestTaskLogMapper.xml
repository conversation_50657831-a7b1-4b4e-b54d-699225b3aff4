<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TestTaskLogMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.test.TestTaskLogDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="channel_account_id" property="channelAccountId" jdbcType="INTEGER"/>
        <result column="tpl_id" property="tplId" jdbcType="BIGINT"/>
        <result column="mobile_enc" property="mobile" jdbcType="VARCHAR"/>
        <result column="params" property="params" jdbcType="VARCHAR"/>
        <result column="send_status" property="sendStatus" jdbcType="INTEGER"/>
        <result column="call_status" property="callStatus" jdbcType="INTEGER"/>
        <result column="call_reason" property="callReason" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,task_id,channel_account_id,tpl_id,params,send_status,call_status,call_reason,create_time,update_time,mobile_enc
    </sql>
    <insert id="insertBySelective">
        insert into t_test_task_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="channelAccountId != null">
                channel_account_id,
            </if>
            <if test="tplId != null">
                tpl_id,
            </if>
            <if test="mobile != null">
                mobile_enc,
            </if>
            <if test="params != null">
                params,
            </if>
            <if test="sendStatus != null">
                send_status,
            </if>
            <if test="callStatus != null">
                call_status,
            </if>
            <if test="callReason != null">
                call_reason,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>

        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="channelAccountId != null">
                #{channelAccountId,jdbcType=INTEGER},
            </if>
            <if test="tplId != null">
                #{tplId,jdbcType=BIGINT},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="params != null">
                #{params,jdbcType=VARCHAR},
            </if>
            <if test="sendStatus != null">
                #{sendStatus,jdbcType=INTEGER},
            </if>
            <if test="callStatus!= null">
                #{callStatus,jdbcType=INTEGER},
            </if>
            <if test="callReason != null">
                #{callReason,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateSendStatus">
        update t_test_task_record
        set send_status = #{sendStatus} , update_time = now()
        where id = #{id}
    </update>
    <update id="updateByTaskIdAndMobile">
        update t_test_task_record
        set call_status = #{callStatus},
            call_reason = #{callReason},
            update_time = now()
        where task_id = #{taskId}
          and mobile_enc = #{mobile}
    </update>

    <select id="countByQuery" resultType="java.lang.Integer">
        select count(*)
        from t_test_task_record
        <where>
            <if test="query.taskId != null and query.taskId != ''">
                and task_id = #{query.taskId}
            </if>
            <if test="query.tplId != null and query.tplId != ''">
                and tpl_id = #{query.tplId}
            </if>
            <if test="query.mobile != null and query.mobile != ''">
                and mobile_enc = #{query.mobile}
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                and create_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                and create_time &lt;= #{query.endTime}
            </if>
        </where>
    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_task_record
        <where>
            <if test="query.taskId != null and query.taskId != ''">
                and task_id = #{query.taskId}
            </if>
            <if test="query.tplId != null and query.tplId != ''">
                and tpl_id = #{query.tplId}
            </if>
            <if test="query.mobile != null and query.mobile!= ''">
                and mobile_enc = #{query.mobile}
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                and create_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                and create_time &lt;= #{query.endTime}
            </if>
        </where>
        order by update_time desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset},#{query.pageParameter.pageSize}
        </if>
    </select>
    <select id="selectByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_task_record
        where task_id = #{taskId}
    </select>
    <select id="selectByTaskIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_task_record
        where task_id in
        <foreach collection="taskIdList" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>
    <select id="selectByTaskIdListAndCallStatus"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_task_record
        where task_id in
        <foreach collection="taskIdList" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        and call_status = #{callStatus}
    </select>

</mapper>
