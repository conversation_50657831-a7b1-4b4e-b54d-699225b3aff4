<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ChannelAccountLogMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ChannelAccountLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="channel_code" property="channelCode" jdbcType="VARCHAR" />
    <result column="sms_type_code" property="smsTypeCode" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="key" property="key" jdbcType="VARCHAR" />
    <result column="json_mapping" property="jsonMapping" jdbcType="VARCHAR" />
    <result column="price" property="price" jdbcType="INTEGER" />
    <result column="protocol" property="protocol" jdbcType="TINYINT" />
    <result column="isps" property="isps" jdbcType="VARCHAR" />
    <result column="area_filter_type" property="areaFilterType" jdbcType="TINYINT" />
    <result column="areas" property="areas" jdbcType="VARCHAR" />
    <result column="weight" property="weight" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="is_tpl_free_audit" property="isTplFreeAudit" jdbcType="TINYINT" />
    <result column="sign_ids" property="signIds" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updater" property="updater" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="channel_account_id" property="channelAccountId" jdbcType="INTEGER" />
    <result column="op_type" property="opType" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, channel_code, sms_type_code, name, key, json_mapping, price, protocol, isps, 
    area_filter_type, areas, weight, status, is_tpl_free_audit, sign_ids, remark, creator, 
    create_time, updater, update_time, channel_account_id, op_type
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_channel_account_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_channel_account_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.ChannelAccountLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_channel_account_log (id, channel_code, sms_type_code, 
      name, key, json_mapping, 
      price, protocol, isps, 
      area_filter_type, areas, weight, 
      status, is_tpl_free_audit, sign_ids, 
      remark, creator, create_time, 
      updater, update_time, channel_account_id, 
      op_type)
    values (#{id,jdbcType=INTEGER}, #{channelCode,jdbcType=VARCHAR}, #{smsTypeCode,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{key,jdbcType=VARCHAR}, #{jsonMapping,jdbcType=VARCHAR}, 
      #{price,jdbcType=INTEGER}, #{protocol,jdbcType=TINYINT}, #{isps,jdbcType=VARCHAR}, 
      #{areaFilterType,jdbcType=TINYINT}, #{areas,jdbcType=VARCHAR}, #{weight,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{isTplFreeAudit,jdbcType=TINYINT}, #{signIds,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{channelAccountId,jdbcType=INTEGER}, 
      #{opType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.ChannelAccountLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_channel_account_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="channelCode != null" >
        channel_code,
      </if>
      <if test="smsTypeCode != null" >
        sms_type_code,
      </if>
      <if test="name != null" >
        `name`,
      </if>
      <if test="key != null" >
        `key`,
      </if>
      <if test="jsonMapping != null" >
        json_mapping,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="protocol != null" >
        protocol,
      </if>
      <if test="isps != null" >
        isps,
      </if>
      <if test="areaFilterType != null" >
        area_filter_type,
      </if>
      <if test="areas != null" >
        areas,
      </if>
      <if test="weight != null" >
        weight,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="isTplFreeAudit != null" >
        is_tpl_free_audit,
      </if>
      <if test="signIds != null" >
        sign_ids,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updater != null" >
        updater,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="channelAccountId != null" >
        channel_account_id,
      </if>
      <if test="opType != null" >
        op_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="channelCode != null" >
        #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="smsTypeCode != null" >
        #{smsTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="key != null" >
        #{key,jdbcType=VARCHAR},
      </if>
      <if test="jsonMapping != null" >
        #{jsonMapping,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        #{price,jdbcType=INTEGER},
      </if>
      <if test="protocol != null" >
        #{protocol,jdbcType=TINYINT},
      </if>
      <if test="isps != null" >
        #{isps,jdbcType=VARCHAR},
      </if>
      <if test="areaFilterType != null" >
        #{areaFilterType,jdbcType=TINYINT},
      </if>
      <if test="areas != null" >
        #{areas,jdbcType=VARCHAR},
      </if>
      <if test="weight != null" >
        #{weight,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isTplFreeAudit != null" >
        #{isTplFreeAudit,jdbcType=TINYINT},
      </if>
      <if test="signIds != null" >
        #{signIds,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="channelAccountId != null" >
        #{channelAccountId,jdbcType=INTEGER},
      </if>
      <if test="opType != null" >
        #{opType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.ChannelAccountLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_channel_account_log
    <set >
      <if test="channelCode != null" >
        channel_code = #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="smsTypeCode != null" >
        sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="key != null" >
        key = #{key,jdbcType=VARCHAR},
      </if>
      <if test="jsonMapping != null" >
        json_mapping = #{jsonMapping,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=INTEGER},
      </if>
      <if test="protocol != null" >
        protocol = #{protocol,jdbcType=TINYINT},
      </if>
      <if test="isps != null" >
        isps = #{isps,jdbcType=VARCHAR},
      </if>
      <if test="areaFilterType != null" >
        area_filter_type = #{areaFilterType,jdbcType=TINYINT},
      </if>
      <if test="areas != null" >
        areas = #{areas,jdbcType=VARCHAR},
      </if>
      <if test="weight != null" >
        weight = #{weight,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isTplFreeAudit != null" >
        is_tpl_free_audit = #{isTplFreeAudit,jdbcType=TINYINT},
      </if>
      <if test="signIds != null" >
        sign_ids = #{signIds,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="channelAccountId != null" >
        channel_account_id = #{channelAccountId,jdbcType=INTEGER},
      </if>
      <if test="opType != null" >
        op_type = #{opType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.ChannelAccountLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_channel_account_log
    set channel_code = #{channelCode,jdbcType=VARCHAR},
      sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      key = #{key,jdbcType=VARCHAR},
      json_mapping = #{jsonMapping,jdbcType=VARCHAR},
      price = #{price,jdbcType=INTEGER},
      protocol = #{protocol,jdbcType=TINYINT},
      isps = #{isps,jdbcType=VARCHAR},
      area_filter_type = #{areaFilterType,jdbcType=TINYINT},
      areas = #{areas,jdbcType=VARCHAR},
      weight = #{weight,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      is_tpl_free_audit = #{isTplFreeAudit,jdbcType=TINYINT},
      sign_ids = #{signIds,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      channel_account_id = #{channelAccountId,jdbcType=INTEGER},
      op_type = #{opType,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectCount"  resultType="java.lang.Integer">
    select count(*)
    from t_channel_account_log
  </select>
</mapper>