<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.AutoTestTaskMapper">

    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.AutoTestTaskDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="bindTpl" column="bind_tpl" jdbcType="VARCHAR"/>
        <result property="testFrequency" column="test_frequency" jdbcType="VARCHAR"/>
        <result property="testCycle" column="test_cycle" jdbcType="VARCHAR"/>
        <result property="workDay" column="work_day" jdbcType="INTEGER"/>
        <result property="enable" column="enable" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Select_List">
        <trim suffixOverrides=",">
            id,
            name,
            type,
            bind_tpl,
            test_frequency,
            test_cycle,
            work_day,
            enable,
            remark,
            is_delete,
            create_time,
            update_time,
            creator,
            updater,
        </trim>
    </sql>

    <insert id="insert">
        insert into t_auto_test_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="entity.id != null">
                id,
            </if>
            <if test="entity.name != null">
                name,
            </if>
            <if test="entity.type != null">
                type,
            </if>
            <if test="entity.bindTpl != null">
                bind_tpl,
            </if>
            <if test="entity.testFrequency != null">
                test_frequency,
            </if>
            <if test="entity.testCycle != null">
                test_cycle,
            </if>
            <if test="entity.workDay != null">
                work_day,
            </if>
            <if test="entity.enable != null">
                enable,
            </if>
            <if test="entity.remark != null">
                remark,
            </if>
            <if test="entity.isDelete != null">
                is_delete,
            </if>
            <if test="entity.createTime != null">
                create_time,
            </if>
            <if test="entity.updateTime != null">
                update_time,
            </if>
            <if test="entity.creator != null">
                creator,
            </if>
            <if test="entity.updater != null">
                updater,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="entity.id != null">
                #{entity.id},
            </if>
            <if test="entity.name != null">
                #{entity.name},
            </if>
            <if test="entity.type != null">
                #{entity.type},
            </if>
            <if test="entity.bindTpl != null">
                #{entity.bindTpl},
            </if>
            <if test="entity.testFrequency != null">
                #{entity.testFrequency},
            </if>
            <if test="entity.testCycle != null">
                #{entity.testCycle},
            </if>
            <if test="entity.workDay != null">
                #{entity.workDay},
            </if>
            <if test="entity.enable != null">
                #{entity.enable},
            </if>
            <if test="entity.remark != null">
                #{entity.remark},
            </if>
            <if test="entity.isDelete != null">
                #{entity.isDelete},
            </if>
            <if test="entity.createTime != null">
                #{entity.createTime},
            </if>
            <if test="entity.updateTime != null">
                #{entity.updateTime},
            </if>
            <if test="entity.creator != null">
                #{entity.creator},
            </if>
            <if test="entity.updater != null">
                #{entity.updater},
            </if>
        </trim>
    </insert>

    <update id="updateById">
        update t_auto_test_task
        <trim prefix="set" suffixOverrides=",">
            <if test="entity.name != null">
                name = #{entity.name},
            </if>
            <if test="entity.type != null">
                type = #{entity.type},
            </if>
            <if test="entity.bindTpl != null">
                bind_tpl = #{entity.bindTpl},
            </if>
            <if test="entity.testFrequency != null">
                test_frequency = #{entity.testFrequency},
            </if>
            <if test="entity.testCycle != null">
                test_cycle = #{entity.testCycle},
            </if>
            <if test="entity.workDay != null">
                work_day = #{entity.workDay},
            </if>
            <if test="entity.enable != null">
                enable = #{entity.enable},
            </if>
            <if test="entity.remark != null">
                remark = #{entity.remark},
            </if>
            <if test="entity.isDelete != null">
                is_delete = #{entity.isDelete},
            </if>
            <if test="entity.createTime != null">
                create_time = #{entity.createTime},
            </if>
            <if test="entity.updateTime != null">
                update_time = #{entity.updateTime},
            </if>
            <if test="entity.creator != null">
                creator = #{entity.creator},
            </if>
            <if test="entity.updater != null">
                updater = #{entity.updater},
            </if>
        </trim>
        where id = #{entity.id}
    </update>

    <select id="countByQuery" resultType="java.lang.Integer">
        select count(*)
        from t_auto_test_task
        <where>
            <if test="query.isDelete != null">
                and is_delete = #{query.isDelete}
            </if>
            <if test="query.likeName != null">
                and name like concat('%', #{query.likeName}, '%')
            </if>
            <if test="query.type != null">
                and type = #{query.type}
            </if>
            <if test="query.enable != null">
                and enable = #{query.enable}
            </if>
        </where>
        <if test="query != null and query.pageParameter != null">
            limit #{query.pageParameter.offset}, #{query.pageParameter.pageSize}
        </if>
    </select>

    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Select_List"/>
        from t_auto_test_task
        <where>
            <if test="query.isDelete != null">
                and is_delete = #{query.isDelete}
            </if>
            <if test="query.likeName != null">
                and name like concat('%', #{query.likeName}, '%')
            </if>
            <if test="query.type != null">
                and type = #{query.type}
            </if>
            <if test="query.enable != null">
                and enable = #{query.enable}
            </if>
        </where>
        order by update_time desc
        <if test="query != null and query.pageParameter != null">
            limit #{query.pageParameter.offset}, #{query.pageParameter.pageSize}
        </if>
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Select_List"/>
        from t_auto_test_task
        where id = #{id}
    </select>

</mapper>