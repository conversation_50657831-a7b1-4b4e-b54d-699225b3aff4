<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.CodeMappingMapper">
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.CodeMappingDO">
    <id column="channel_code" jdbcType="VARCHAR" property="channelCode" />
    <id column="type" jdbcType="VARCHAR" property="type" />
    <id column="channel_err_code" jdbcType="VARCHAR" property="channelErrCode" />
    <result column="xh_err_code" jdbcType="INTEGER" property="xhErrCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <association property="errorCodeDO" javaType="com.xhqb.spectre.common.dal.entity.ErrorCodeDO">
      <id column="type" jdbcType="VARCHAR" property="type" />
      <id column="xh_err_code" jdbcType="INTEGER" property="xhErrCode" />
      <result column="code_desc" jdbcType="VARCHAR" property="codeDesc" />
      <result column="retry" jdbcType="TINYINT" property="retry" />
      <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
      <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </association>
  </resultMap>
  <sql id="Base_Column_List">
    channel_code, `type`, channel_err_code, xh_err_code, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_code_mapping
    where channel_code = #{channelCode,jdbcType=VARCHAR}
      and `type` = #{type,jdbcType=VARCHAR}
      and channel_err_code = #{channelErrCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey">
    delete from t_code_mapping
    where channel_code = #{channelCode,jdbcType=VARCHAR}
      and `type` = #{type,jdbcType=VARCHAR}
      and channel_err_code = #{channelErrCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.CodeMappingDO">
    insert into t_code_mapping (channel_code, `type`, channel_err_code, 
      xh_err_code, create_time, update_time
      )
    values (#{channelCode,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{channelErrCode,jdbcType=VARCHAR}, 
      #{xhErrCode,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.CodeMappingDO">
    insert into t_code_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="channelCode != null">
        channel_code,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="channelErrCode != null">
        channel_err_code,
      </if>
      <if test="xhErrCode != null">
        xh_err_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="channelCode != null">
        #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="channelErrCode != null">
        #{channelErrCode,jdbcType=VARCHAR},
      </if>
      <if test="xhErrCode != null">
        #{xhErrCode,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.CodeMappingDO">
    update t_code_mapping
    <set>
      <if test="xhErrCode != null">
        xh_err_code = #{xhErrCode,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where channel_code = #{channelCode,jdbcType=VARCHAR}
      and `type` = #{type,jdbcType=VARCHAR}
      and channel_err_code = #{channelErrCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.CodeMappingDO">
    update t_code_mapping
    set xh_err_code = #{xhErrCode,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where channel_code = #{channelCode,jdbcType=VARCHAR}
      and `type` = #{type,jdbcType=VARCHAR}
      and channel_err_code = #{channelErrCode,jdbcType=VARCHAR}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select channel_code, m.`type`, m.channel_err_code, m.xh_err_code, code_desc, retry
    from t_code_mapping m
    left join t_error_code c
    on c.type = m.type
    and c.xh_err_code = m.xh_err_code
  </select>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.CodeMappingQuery" resultType="java.lang.Integer">
    select count(1)
    from t_code_mapping tcm left join t_error_code tec on(tcm.`type` = tec.`type` and tcm.xh_err_code = tec.xh_err_code)
    <where>
      <if test="channelCode != null">
        and tcm.channel_code = #{channelCode}
      </if>
      <if test="channelErrCode != null">
        and tcm.channel_err_code = #{channelErrCode}
      </if>
      <if test="xhErrCode != null">
        and tcm.xh_err_code = #{xhErrCode}
      </if>
      <if test="type != null">
        and tcm.`type` = #{type}
      </if>
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.CodeMappingQuery" resultMap="BaseResultMap">
    select
    tcm.channel_code, tcm.`type`, tcm.channel_err_code, tcm.xh_err_code, tcm.create_time, tcm.update_time,
    tec.code_desc
    from t_code_mapping tcm left join t_error_code tec on(tcm.`type` = tec.`type` and tcm.xh_err_code = tec.xh_err_code)
    <where>
      <if test="channelCode != null">
        and tcm.channel_code = #{channelCode}
      </if>
      <if test="channelErrCode != null">
        and tcm.channel_err_code = #{channelErrCode}
      </if>
      <if test="xhErrCode != null">
        and tcm.xh_err_code = #{xhErrCode}
      </if>
      <if test="type != null">
        and tcm.`type` = #{type}
      </if>
    </where>
    order by tcm.update_time desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>

</mapper>