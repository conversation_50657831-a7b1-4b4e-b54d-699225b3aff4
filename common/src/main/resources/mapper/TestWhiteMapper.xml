<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TestWhiteMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.test.tool.TestWhiteDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="mobile_enc" jdbcType="VARCHAR" property="mobile"/>
        <result column="owner" jdbcType="VARCHAR" property="owner"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="os_version" jdbcType="VARCHAR" property="osVersion"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, mobile_enc, owner, brand, os_version, status, creator, create_time, updater, update_time,
        is_delete,remark
    </sql>
    <insert id="insertBySelective" useGeneratedKeys="true" keyProperty="id">
        insert into t_test_white
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mobile != null">mobile_enc,</if>
            <if test="owner != null">owner,</if>
            <if test="brand != null">brand,</if>
            <if test="osVersion != null">os_version,</if>
            <if test="status != null">status,</if>
            <if test="creator != null">creator,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updater != null">updater,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mobile != null">#{mobile},</if>
            <if test="owner != null">#{owner},</if>
            <if test="brand != null">#{brand},</if>
            <if test="osVersion != null">#{osVersion},</if>
            <if test="status != null">#{status},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective">
        update t_test_white
        <set>
            <if test="mobile != null">mobile_enc = #{mobile},</if>
            <if test="owner != null">owner = #{owner},</if>
            <if test="brand != null">brand = #{brand},</if>
            <if test="osVersion != null">os_version = #{osVersion},</if>
            <if test="status != null">status = #{status},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="remark != null">remark = #{remark},</if>
        </set>
        where id = #{id}
    </update>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_white
        where id = #{id}
    </select>
    <select id="countByQuery" resultType="java.lang.Integer">
        select count(1)
        from t_test_white
        <where>
            and is_delete = 0
            <if test="query.mobile != null">
                and mobile_enc = #{query.mobile}
            </if>
            <if test="query.brand != null">
                and brand = #{query.brand}
            </if>
        </where>
    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_white
        <where>
            and is_delete = 0
            <if test="query.mobile != null">
                and mobile_enc = #{query.mobile}
            </if>
            <if test="query.brand != null">
                and brand = #{query.brand}
            </if>
        </where>
        order by update_time desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset}, #{query.pageParameter.pageSize}
        </if>
    </select>
    <select id="selectByMobile" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_white
        where mobile_enc = #{mobile} and is_delete = 0
    </select>

    <select id="countAllByBrand" resultType="java.util.Map">
        select brand, count(1) as count
        from t_test_white
        where is_delete = 0
        and status = 1
        group by brand
    </select>
    <select id="selectByBrand" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_test_white
        where brand = #{brand} and is_delete = 0 and status = 1
    </select>

    <select id="selectByBrandInfo" resultType="com.xhqb.spectre.common.dal.entity.BrandInfoDO">
        select brand,
               count(1) as count
        from t_test_white
        where is_delete = 0
          and status = 1
        group by brand
    </select>
</mapper>