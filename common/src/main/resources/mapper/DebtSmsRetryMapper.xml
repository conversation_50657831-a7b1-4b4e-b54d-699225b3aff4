<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.DebtSmsRetryMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.DebtSmsRetryDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tpl_code" jdbcType="VARCHAR" property="tplCode"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="retry_times" jdbcType="INTEGER" property="retryTimes"/>
        <result column="start_time" jdbcType="INTEGER" property="startTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.xhqb.spectre.common.dal.entity.DebtSmsRetryDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <result column="message_json" jdbcType="LONGVARCHAR" property="messageJson"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, tpl_code, order_id, status, retry_times, start_time, remark, create_time, update_time,
        is_delete
    </sql>
    <sql id="Blob_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        message_json
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from t_debt_sms_retry
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from t_debt_sms_retry
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.DebtSmsRetryDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_debt_sms_retry (id, tpl_code, order_id,
        status, retry_times, start_time,
        remark, create_time, update_time,
        is_delete, message_json)
        values (#{id,jdbcType=BIGINT}, #{tplCode,jdbcType=VARCHAR}, #{orderId,jdbcType=BIGINT},
        #{status,jdbcType=TINYINT}, #{retryTimes,jdbcType=INTEGER}, #{startTime,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDelete,jdbcType=TINYINT}, #{messageJson,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.DebtSmsRetryDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_debt_sms_retry
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="tplCode != null">
                tpl_code,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="retryTimes != null">
                retry_times,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="messageJson != null">
                message_json,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="tplCode != null">
                #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="retryTimes != null">
                #{retryTimes,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="messageJson != null">
                #{messageJson,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.DebtSmsRetryDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_debt_sms_retry
        <set>
            <if test="tplCode != null">
                tpl_code = #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="retryTimes != null">
                retry_times = #{retryTimes,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="messageJson != null">
                message_json = #{messageJson,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.xhqb.spectre.common.dal.entity.DebtSmsRetryDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_debt_sms_retry
        set tpl_code = #{tplCode,jdbcType=VARCHAR},
        order_id = #{orderId,jdbcType=BIGINT},
        status = #{status,jdbcType=TINYINT},
        retry_times = #{retryTimes,jdbcType=INTEGER},
        start_time = #{startTime,jdbcType=INTEGER},
        remark = #{remark,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_delete = #{isDelete,jdbcType=TINYINT},
        message_json = #{messageJson,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.DebtSmsRetryDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_debt_sms_retry
        set tpl_code = #{tplCode,jdbcType=VARCHAR},
        order_id = #{orderId,jdbcType=BIGINT},
        status = #{status,jdbcType=TINYINT},
        retry_times = #{retryTimes,jdbcType=INTEGER},
        start_time = #{startTime,jdbcType=INTEGER},
        remark = #{remark,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_delete = #{isDelete,jdbcType=TINYINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByOrderIdWithoutMessage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_debt_sms_retry
        where order_id = #{orderId}
        limit 1
    </select>

    <select id="selectDebtSmsRetryJob" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from t_debt_sms_retry
        where id > #{lastId}
        and status =0
        and start_time<![CDATA[ >= ]]> #{beginStartTime}
        and start_time<![CDATA[ <= ]]> #{endStartTime}
        order by id asc
        limit #{pageSize}
    </select>
</mapper>