<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.BatchTaskLogMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.BatchTaskLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="task_id" property="taskId" jdbcType="INTEGER" />
    <result column="task_param_id" property="taskParamId" jdbcType="INTEGER" />
    <result column="cid" property="cid" jdbcType="VARCHAR" />
    <result column="mobile" property="mobile" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="param" property="param" jdbcType="VARCHAR" />
    <result column="err_code" property="errCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, task_id, task_param_id, cid, mobile, status, description, create_time, update_time,
    param, err_code
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from t_batch_task_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_batch_task_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_task_log (id, task_id, task_param_id,
    cid, mobile, status,
    description, create_time, update_time,
    param, err_code)
    values (#{id,jdbcType=INTEGER}, #{taskId,jdbcType=INTEGER}, #{taskParamId,jdbcType=INTEGER},
    #{cid,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
    #{description,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
    #{param,jdbcType=VARCHAR}, #{errCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_batch_task_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="taskId != null" >
        task_id,
      </if>
      <if test="taskParamId != null" >
        task_param_id,
      </if>
      <if test="cid != null" >
        cid,
      </if>
      <if test="mobile != null" >
        mobile,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="param != null" >
        param,
      </if>
      <if test="errCode != null" >
        err_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="taskId != null" >
        #{taskId,jdbcType=INTEGER},
      </if>
      <if test="taskParamId != null" >
        #{taskParamId,jdbcType=INTEGER},
      </if>
      <if test="cid != null" >
        #{cid,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="param != null" >
        #{param,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null" >
        #{errCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_task_log
    <set >
      <if test="taskId != null" >
        task_id = #{taskId,jdbcType=INTEGER},
      </if>
      <if test="taskParamId != null" >
        task_param_id = #{taskParamId,jdbcType=INTEGER},
      </if>
      <if test="cid != null" >
        cid = #{cid,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="param != null" >
        param = #{param,jdbcType=VARCHAR},
      </if>
      <if test="errCode != null" >
        err_code = #{errCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.BatchTaskLogDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_batch_task_log
    set task_id = #{taskId,jdbcType=INTEGER},
    task_param_id = #{taskParamId,jdbcType=INTEGER},
    cid = #{cid,jdbcType=VARCHAR},
    mobile = #{mobile,jdbcType=VARCHAR},
    status = #{status,jdbcType=TINYINT},
    description = #{description,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    param = #{param,jdbcType=VARCHAR},
    err_code = #{errCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert">
    insert into t_batch_task_log (task_id, task_param_id,cid, mobile, status, description, param, err_code)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.taskId,jdbcType=INTEGER}, #{item.taskParamId,jdbcType=INTEGER},
     #{item.cid,jdbcType=VARCHAR}, #{item.mobile,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT},
      #{item.description,jdbcType=VARCHAR}, #{item.param}, #{item.errCode})
    </foreach>
  </insert>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.BatchTaskLogQuery" resultType="java.lang.Integer">
    select count(*)
    from t_batch_task_log
    <where>
      <if test="taskId != null">
        and task_id = #{taskId}
      </if>
      <if test="taskParamId != null">
        and task_param_id = #{taskParamId}
      </if>
      <if test="cid != null">
        and cid = #{cid}
      </if>
      <if test="mobile != null">
        and mobile = #{mobile}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.BatchTaskLogQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_batch_task_log
    <where>
      <if test="taskId != null">
        and task_id = #{taskId}
      </if>
      <if test="taskParamId != null">
        and task_param_id = #{taskParamId}
      </if>
      <if test="cid != null">
        and cid = #{cid}
      </if>
      <if test="mobile != null">
        and mobile = #{mobile}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
    </where>
    order by id desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>

  <select id="selectByTaskId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_batch_task_log
    where task_id = #{taskId}
  </select>

  <select id="exportQuery" resultMap="BaseResultMap">
    select
    id,param,description
    from t_batch_task_log
    where task_id = #{taskId}
    and status = 0
    and id <![CDATA[ > ]]> #{lastId}
    order by id asc limit #{pageSize}
  </select>

  <select id="exportCount"  resultType="java.lang.Integer">
    select count(1)
    from t_batch_task_log
    where task_id = #{taskId}
      and status = 0
  </select>

</mapper>