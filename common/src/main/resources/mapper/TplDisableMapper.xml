<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TplDisableMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.TplDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="tpl_id" property="tplId" jdbcType="INTEGER" />
    <result column="isps" property="isps" jdbcType="VARCHAR" />
    <result column="areas" property="areas" jdbcType="VARCHAR" />
    <result column="start_time" property="startTime" />
    <result column="end_time" property="endTime" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, tpl_id, isps, areas, start_time, end_time, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_tpl_disable
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_tpl_disable
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.TplDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_tpl_disable (id, tpl_id, isps, 
      areas, start_time, end_time, 
      create_time, update_time)
    values (#{id,jdbcType=INTEGER}, #{tplId,jdbcType=INTEGER}, #{isps,jdbcType=VARCHAR}, 
      #{areas,jdbcType=VARCHAR}, #{startTime}, #{endTime}, 
      #{createTime}, #{updateTime})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.TplDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_tpl_disable
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="tplId != null" >
        tpl_id,
      </if>
      <if test="isps != null" >
        isps,
      </if>
      <if test="areas != null" >
        areas,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="tplId != null" >
        #{tplId,jdbcType=INTEGER},
      </if>
      <if test="isps != null" >
        #{isps,jdbcType=VARCHAR},
      </if>
      <if test="areas != null" >
        #{areas,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        #{startTime},
      </if>
      <if test="endTime != null" >
        #{endTime},
      </if>
      <if test="createTime != null" >
        #{createTime},
      </if>
      <if test="updateTime != null" >
        #{updateTime},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.TplDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_tpl_disable
    <set >
      <if test="tplId != null" >
        tpl_id = #{tplId,jdbcType=INTEGER},
      </if>
      <if test="isps != null" >
        isps = #{isps,jdbcType=VARCHAR},
      </if>
      <if test="areas != null" >
        areas = #{areas,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.TplDisableDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_tpl_disable
    set tpl_id = #{tplId,jdbcType=INTEGER},
      isps = #{isps,jdbcType=VARCHAR},
      areas = #{areas,jdbcType=VARCHAR},
      start_time = #{startTime},
      end_time = #{endTime},
      create_time = #{createTime},
      update_time = #{updateTime}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByTplId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_tpl_disable
    where tpl_id = #{tplId}
  </select>

  <delete id="deleteByTplId" parameterType="java.lang.Integer">
    delete from t_tpl_disable where tpl_id = #{tplId}
  </delete>
</mapper>