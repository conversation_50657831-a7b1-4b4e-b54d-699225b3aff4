<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.SignChannelMapper">
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.SignChannelDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="sign_id" jdbcType="INTEGER" property="signId" />
    <result column="channel_account_id" jdbcType="INTEGER" property="channelAccountId" />
    <result column="channel_sign_id" jdbcType="VARCHAR" property="channelSignId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, sign_id, channel_account_id, channel_sign_id, status, remark, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from t_sign_channel
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_sign_channel
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.SignChannelDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_sign_channel (id, sign_id, channel_account_id,
      channel_sign_id, status, remark,
      create_time, update_time)
    values (#{id,jdbcType=INTEGER}, #{signId,jdbcType=INTEGER}, #{channelAccountId,jdbcType=INTEGER},
      #{channelSignId,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.SignChannelDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_sign_channel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="signId != null">
        sign_id,
      </if>
      <if test="channelAccountId != null">
        channel_account_id,
      </if>
      <if test="channelSignId != null">
        channel_sign_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="signId != null">
        #{signId,jdbcType=INTEGER},
      </if>
      <if test="channelAccountId != null">
        #{channelAccountId,jdbcType=INTEGER},
      </if>
      <if test="channelSignId != null">
        #{channelSignId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.SignChannelDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_sign_channel
    <set>
      <if test="signId != null">
        sign_id = #{signId,jdbcType=INTEGER},
      </if>
      <if test="channelAccountId != null">
        channel_account_id = #{channelAccountId,jdbcType=INTEGER},
      </if>
      <if test="channelSignId != null">
        channel_sign_id = #{channelSignId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.SignChannelDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_sign_channel
    set sign_id = #{signId,jdbcType=INTEGER},
      channel_account_id = #{channelAccountId,jdbcType=INTEGER},
      channel_sign_id = #{channelSignId,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectBySignId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_sign_channel
    where sign_id = #{signId,jdbcType=INTEGER}
  </select>

  <delete id="deleteBySignId" parameterType="java.lang.Integer">
    delete from t_sign_channel where sign_id = #{signId,jdbcType=INTEGER}
  </delete>
</mapper>