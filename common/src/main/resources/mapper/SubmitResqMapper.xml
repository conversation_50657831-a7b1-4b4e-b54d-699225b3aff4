<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.SubmitResqMapper">
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.SubmitResqDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel_msg_id" jdbcType="VARCHAR" property="channelMsgId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="recv_submit_time" jdbcType="INTEGER" property="recvSubmitTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, channel_msg_id, order_id, status, result, description, recv_submit_time, create_time
  </sql>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.SubmitResqDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_submit_resq (id, channel_msg_Id, order_Id,
      status, result, description,
      recv_submit_time, create_time)
    values (#{id,jdbcType=BIGINT}, #{channelMsgId,jdbcType=VARCHAR}, #{orderId,jdbcType=BIGINT},
      #{status,jdbcType=INTEGER}, #{result,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
      #{recvSubmitTime,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.SubmitResqDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_submit_resq
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="channelMsgId != null">
        channel_msg_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="recvSubmitTime != null">
        recv_submit_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="channelMsgId != null">
        #{channelMsgId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="recvSubmitTime != null">
        #{recvSubmitTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="com.xhqb.spectre.common.dal.entity.SubmitResqDO">
    insert into t_submit_resq (
    order_Id, channel_msg_Id, status, result, recv_submit_time, description
    )
    values
    <foreach collection="list" item="item" separator=",">
    (
    #{item.orderId,jdbcType=BIGINT},
    #{item.channelMsgId,jdbcType=VARCHAR},
    #{item.status,jdbcType=INTEGER},
    #{item.result,jdbcType=VARCHAR},
    #{item.recvSubmitTime,jdbcType=INTEGER},
    #{item.description,jdbcType=VARCHAR}
    )
    </foreach>
  </insert>

    <select id="selectByOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_submit_resq
        where order_id = #{orderId} limit 1
    </select>
</mapper>