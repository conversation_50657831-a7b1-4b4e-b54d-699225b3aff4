<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.AutoTestBindMapper">

    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.AutoTestBindDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="autoTestTaskId" column="auto_test_task_id" jdbcType="BIGINT"/>
        <result property="testContentTaskId" column="test_content_task_id" jdbcType="BIGINT"/>
        <result property="tplCodes" column="tpl_codes" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Select_List">
        <trim suffixOverrides=",">
            id,
            auto_test_task_id,
            test_content_task_id,
            tpl_codes,
            create_time,
        </trim>
    </sql>

    <insert id="insert">
        insert into t_auto_test_bind
        <trim prefix="(" suffix=")" suffixOverrides=",">
            auto_test_task_id,
            test_content_task_id,
            tpl_codes,
            create_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{entity.autoTestTaskId},
            #{entity.testContentTaskId},
            #{entity.tplCodes},
            #{entity.createTime},
        </trim>
    </insert>

    <select id="selectByAutoTestTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Select_List"/>
        from t_auto_test_bind
        where auto_test_task_id = #{autoTestTaskId}
    </select>

    <select id="countByAutoTestTaskId" resultType="java.lang.Integer">
        select count(*)
        from t_auto_test_bind
        where auto_test_task_id = #{autoTestTaskId}
    </select>

    <select id="selectLastDateByAutoTestTaskId" resultType="java.util.Date">
        select create_time
        from t_auto_test_bind
        where auto_test_task_id = #{autoTestTaskId}
        order by create_time desc limit 1
    </select>

    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Select_List"/>
        from t_auto_test_bind
        <where>
            <if test="query.startTime != null">
                and create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and create_time &lt;= #{query.endTime}
            </if>
            <if test="query.inAutoTestTaskIdList != null and query.inAutoTestTaskIdList.size() > 0">
                and auto_test_task_id in
                <foreach item="item" collection="query.inAutoTestTaskIdList" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectLastDate" resultType="java.util.Date">
        select create_time
        from t_auto_test_bind
        order by create_time desc limit 1
    </select>

</mapper>