<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ErrorCodeMapper">
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ErrorCodeDO">
    <id column="type" jdbcType="VARCHAR" property="type" />
    <id column="xh_err_code" jdbcType="INTEGER" property="xhErrCode" />
    <result column="code_desc" jdbcType="VARCHAR" property="codeDesc" />
    <result column="retry" jdbcType="TINYINT" property="retry" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    `type`, xh_err_code, code_desc, retry, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_error_code
    where `type` = #{type,jdbcType=VARCHAR}
      and xh_err_code = #{xhErrCode,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey">
    delete from t_error_code
    where `type` = #{type,jdbcType=VARCHAR}
      and xh_err_code = #{xhErrCode,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.ErrorCodeDO">
    insert into t_error_code (`type`, xh_err_code, code_desc, retry,
      create_time, update_time)
    values (#{type,jdbcType=VARCHAR}, #{xhErrCode,jdbcType=INTEGER}, #{codeDesc,jdbcType=VARCHAR}, #{retry,jdbcType=TINYINT},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.ErrorCodeDO">
    insert into t_error_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        `type`,
      </if>
      <if test="xhErrCode != null">
        xh_err_code,
      </if>
      <if test="codeDesc != null">
        code_desc,
      </if>
      <if test="retry != null">
        retry,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="xhErrCode != null">
        #{xhErrCode,jdbcType=INTEGER},
      </if>
      <if test="codeDesc != null">
        #{codeDesc,jdbcType=VARCHAR},
      </if>
      <if test="retry != null">
        #{retry,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.ErrorCodeDO">
    update t_error_code
    <set>
      <if test="codeDesc != null">
        code_desc = #{codeDesc,jdbcType=VARCHAR},
      </if>
      <if test="retry != null">
        retry = #{retry,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `type` = #{type,jdbcType=VARCHAR}
      and xh_err_code = #{xhErrCode,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.ErrorCodeDO">
    update t_error_code
    set code_desc = #{codeDesc,jdbcType=VARCHAR},
      retry = #{retry,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where `type` = #{type,jdbcType=VARCHAR}
      and xh_err_code = #{xhErrCode,jdbcType=INTEGER}
  </update>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.ErrorCodeQuery" resultType="java.lang.Integer">
    select count(*)
    from t_error_code
    <where>
      <if test="type != null">
        and `type` = #{type}
      </if>
      <if test="xhErrCode != null">
        and xh_err_code = #{xhErrCode}
      </if>
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.ErrorCodeQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_error_code
    <where>
      <if test="type != null">
        and `type` = #{type}
      </if>
      <if test="xhErrCode != null">
        and xh_err_code = #{xhErrCode}
      </if>
    </where>
    order by update_time desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>

  <select id="selectMaxCodeByType" resultType="java.lang.Integer">
    select max(xh_err_code)
    from t_error_code
    <where>
      `type` = #{type}
       and xh_err_code != 9999
    </where>
  </select>

</mapper>