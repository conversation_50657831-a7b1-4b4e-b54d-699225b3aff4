<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.SmsDayStatisMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.SmsDayStatisDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="date" property="date" jdbcType="DATE" />
    <id column="code" property="code" jdbcType="VARCHAR" />
    <result column="send_total" property="sendTotal" jdbcType="BIGINT" />
    <result column="billing_total" property="billingTotal" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    date, code, send_total, billing_total, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.xhqb.spectre.common.dal.entity.SmsDayStatisKey" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_sms_day_statis
    where date = #{date,jdbcType=DATE}
      and code = #{code,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.SmsDayStatisKey" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_sms_day_statis
    where date = #{date,jdbcType=DATE}
      and code = #{code,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.SmsDayStatisDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_sms_day_statis (date, code, send_total, 
      billing_total, create_time, update_time
      )
    values (#{date,jdbcType=DATE}, #{code,jdbcType=VARCHAR}, #{sendTotal,jdbcType=BIGINT}, 
      #{billingTotal,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.SmsDayStatisDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_sms_day_statis
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="date != null" >
        date,
      </if>
      <if test="code != null" >
        code,
      </if>
      <if test="sendTotal != null" >
        send_total,
      </if>
      <if test="billingTotal != null" >
        billing_total,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="date != null" >
        #{date,jdbcType=DATE},
      </if>
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="sendTotal != null" >
        #{sendTotal,jdbcType=BIGINT},
      </if>
      <if test="billingTotal != null" >
        #{billingTotal,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.SmsDayStatisDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_sms_day_statis
    <set >
      <if test="sendTotal != null" >
        send_total = #{sendTotal,jdbcType=BIGINT},
      </if>
      <if test="billingTotal != null" >
        billing_total = #{billingTotal,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where date = #{date,jdbcType=DATE}
      and code = #{code,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.SmsDayStatisDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_sms_day_statis
    set send_total = #{sendTotal,jdbcType=BIGINT},
      billing_total = #{billingTotal,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where date = #{date,jdbcType=DATE}
      and code = #{code,jdbcType=VARCHAR}
  </update>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.SmsDayStatisQuery" resultType="java.lang.Integer">
    select count(*)
    from t_sms_day_statis
    <where>
      <if test="date != null">
        and `date` = #{date}
      </if>
      <if test="codeList != null">
        and code in
        <foreach collection="codeList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.SmsDayStatisQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_sms_day_statis
    <where>
      <if test="date != null">
        and `date` = #{date}
      </if>
      <if test="codeList != null">
        and code in
        <foreach collection="codeList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    order by update_time desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>
</mapper>