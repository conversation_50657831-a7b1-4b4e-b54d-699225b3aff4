<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ShortUrlTplDOMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ShortUrlTplDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="long_url" jdbcType="VARCHAR" property="longUrl"/>
        <result column="short_code" jdbcType="VARCHAR" property="shortCode"/>
        <result column="tpl_code" jdbcType="VARCHAR" property="tplCode"/>
        <result column="expired_date" jdbcType="VARCHAR" property="expiredDate"/>
        <result column="click_count" jdbcType="INTEGER" property="clickCount"/>
        <result column="ip_click_count" jdbcType="INTEGER" property="ipClickCount"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="is_encrypt" jdbcType="TINYINT" property="isEncrypt"/>
        <result column="biz_id" jdbcType="VARCHAR" property="bizId"/>
        <result column="scenario" jdbcType="VARCHAR" property="scenario"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , `name`, tpl_code, long_url,short_code, expired_date, click_count, ip_click_count,
        creator, create_time, updater, update_time, is_delete, description,is_encrypt,biz_id,scenario
    </sql>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xhqb.spectre.common.dal.entity.ShortUrlTplDO"
            useGeneratedKeys="true">
        insert into t_short_url_tpl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="longUrl != null">
                long_url,
            </if>
            <if test="shortCode != null">
                short_code,
            </if>
            <if test="tplCode != null">
                tpl_code,
            </if>
            <if test="expiredDate != null">
                expired_date,
            </if>
            <if test="clickCount != null">
                click_count,
            </if>
            <if test="ipClickCount != null">
                ip_click_count,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="isEncrypt != null">
                is_encrypt,
            </if>
            <if test="bizId != null">
                biz_id,
            </if>
            <if test="scenario != null">
                scenario,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="longUrl != null">
                #{longUrl,jdbcType=VARCHAR},
            </if>
            <if test="shortCode != null">
                #{shortCode,jdbcType=VARCHAR},
            </if>
            <if test="tplCode != null">
                #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="expiredDate != null">
                #{expiredDate,jdbcType=VARCHAR},
            </if>
            <if test="clickCount != null">
                #{clickCount,jdbcType=INTEGER},
            </if>
            <if test="ipClickCount != null">
                #{ipClickCount,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="isEncrypt != null">
                #{isEncrypt,jdbcType=TINYINT},
            </if>
            <if test="bizId != null">
                #{bizId,jdbcType=VARCHAR},
            </if>
            <if test="scenario != null">
                #{scenario,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective">
        update t_short_url_tpl
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="longUrl != null">
                long_url = #{longUrl,jdbcType=VARCHAR},
            </if>
            <if test="shortCode != null">
                short_code = #{shortCode,jdbcType=VARCHAR},
            </if>
            <if test="tplCode != null">
                tpl_code = #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="expiredDate != null">
                expired_date = #{expiredDate,jdbcType=VARCHAR},
            </if>
            <if test="clickCount != null">
                click_count = #{clickCount,jdbcType=INTEGER},
            </if>
            <if test="ipClickCount != null">
                ip_click_count = #{ipClickCount,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="isEncrypt != null">
                is_encrypt = #{isEncrypt,jdbcType=TINYINT},
            </if>
            <if test="bizId != null">
                biz_id = #{bizId,jdbcType=VARCHAR},
            </if>
            <if test="scenario != null">
                scenario = #{scenario,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateDeleteTagById">
        update t_short_url_tpl
        set is_delete = 1
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateClickCountByTplCode">
        update t_short_url_tpl
        set click_count = `click_count` + #{nowClickCount}
        where tpl_code = #{tplCode}
    </update>
    <select id="selectByTplCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_short_url_tpl
        where is_delete = 0 and tpl_code = #{tplCode}
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_short_url_tpl
        where id = #{id} and is_delete = 0
    </select>
    <select id="countByQuery" resultType="java.lang.Integer">
        select count(1)
        from t_short_url_tpl
        <where>
            is_delete = 0
            <if test="tplCode != null and tplCode != ''">and tpl_code like concat('%', #{tplCode}, '%')</if>
            <if test="name != null and name != ''">and name like concat('%', #{name}, '%')</if>
        </where>
    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_short_url_tpl
        <where>
            is_delete = 0
            <if test="tplCode != null and tplCode != ''">and tpl_code like concat('%', #{tplCode}, '%')</if>
            <if test="name != null and name != ''">and name like concat('%', #{name}, '%')</if>
        </where>
        order by create_time desc
        <if test="pageParameter != null">
            limit #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>
    <select id="selectByCurDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_short_url_tpl
        where is_delete = 0 and `expired_date` <![CDATA[ >= ]]> now()
    </select>
    <select id="selectByBizId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_short_url_tpl
        where is_delete = 0 and biz_id = #{bizId} limit 1
    </select>
</mapper>