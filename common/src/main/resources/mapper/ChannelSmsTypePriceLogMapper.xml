<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ChannelSmsTypePriceLogMapper">

    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ChannelSmsTypePriceLogDO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="channelCode" column="channel_code" jdbcType="VARCHAR"/>
            <result property="smsTypeCode" column="sms_type_code" jdbcType="VARCHAR"/>
            <result property="effectiveTime" column="effective_time" jdbcType="TIMESTAMP"/>
            <result property="ineffectiveTime" column="ineffective_time" jdbcType="TIMESTAMP"/>
            <result property="perPrice" column="per_price" jdbcType="INTEGER"/>
            <result property="curPrice" column="cur_price" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="signName" column="sign_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,channel_code,sms_type_code,
        effective_time,ineffective_time,per_price,
        cur_price,creator,create_time,
        updater, update_time, sign_name
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_channel_sms_type_price_log
        where  id = #{id,jdbcType=INTEGER} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from t_channel_sms_type_price_log
        where  id = #{id,jdbcType=INTEGER} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhqb.spectre.common.dal.entity.ChannelSmsTypePriceLogDO" useGeneratedKeys="true">
        insert into t_channel_sms_type_price_log
        ( id,channel_code,sms_type_code
        ,effective_time,ineffective_time,per_price
        ,cur_price,creator,create_time
        ,updater,update_time,sign_name)
        values (#{id,jdbcType=INTEGER},#{channelCode,jdbcType=VARCHAR},#{smsTypeCode,jdbcType=VARCHAR}
        ,#{effectiveTime,jdbcType=TIMESTAMP},#{ineffectiveTime,jdbcType=TIMESTAMP},#{perPrice,jdbcType=INTEGER}
        ,#{curPrice,jdbcType=INTEGER},#{creator,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}
        ,#{updater,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP},#{signName,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhqb.spectre.common.dal.entity.ChannelSmsTypePriceLogDO" useGeneratedKeys="true">
        insert into t_channel_sms_type_price_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="channelCode != null">channel_code,</if>
                <if test="smsTypeCode != null">sms_type_code,</if>
                <if test="effectiveTime != null">effective_time,</if>
                <if test="ineffectiveTime != null">ineffective_time,</if>
                <if test="perPrice != null">per_price,</if>
                <if test="curPrice != null">cur_price,</if>
                <if test="creator != null">creator,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updater != null">updater,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="signName != null">sign_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="channelCode != null">#{channelCode,jdbcType=VARCHAR},</if>
                <if test="smsTypeCode != null">#{smsTypeCode,jdbcType=VARCHAR},</if>
                <if test="effectiveTime != null">#{effectiveTime,jdbcType=TIMESTAMP},</if>
                <if test="ineffectiveTime != null">#{ineffectiveTime,jdbcType=TIMESTAMP},</if>
                <if test="perPrice != null">#{perPrice,jdbcType=INTEGER},</if>
                <if test="curPrice != null">#{curPrice,jdbcType=INTEGER},</if>
                <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updater != null">#{updater,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="signName != null">#{signName,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.ChannelSmsTypePriceLogDO">
        update t_channel_sms_type_price_log
        <set>
                <if test="channelCode != null">
                    channel_code = #{channelCode,jdbcType=VARCHAR},
                </if>
                <if test="smsTypeCode != null">
                    sms_type_code = #{smsTypeCode,jdbcType=VARCHAR},
                </if>
                <if test="effectiveTime != null">
                    effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
                </if>
                <if test="ineffectiveTime != null">
                    ineffective_time = #{ineffectiveTime,jdbcType=TIMESTAMP},
                </if>
                <if test="perPrice != null">
                    per_price = #{perPrice,jdbcType=INTEGER},
                </if>
                <if test="curPrice != null">
                    cur_price = #{curPrice,jdbcType=INTEGER},
                </if>
                <if test="creator != null">
                    creator = #{creator,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updater != null">
                    updater = #{updater,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="signName != null">
                    sign_name = #{signName,jdbcType=VARCHAR},
                </if>
        </set>
        where   id = #{id,jdbcType=INTEGER} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.ChannelSmsTypePriceLogDO">
        update t_channel_sms_type_price_log
        set 
            channel_code =  #{channelCode,jdbcType=VARCHAR},
            sms_type_code =  #{smsTypeCode,jdbcType=VARCHAR},
            effective_time =  #{effectiveTime,jdbcType=TIMESTAMP},
            ineffective_time =  #{ineffectiveTime,jdbcType=TIMESTAMP},
            per_price =  #{perPrice,jdbcType=INTEGER},
            cur_price =  #{curPrice,jdbcType=INTEGER},
            creator =  #{creator,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            updater =  #{updater,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            sign_name =  #{signName,jdbcType=VARCHAR}
        where  id = #{id,jdbcType=INTEGER}
    </update>

    <select id="getLatestPrices" resultMap="BaseResultMap">
        SELECT
            t.id AS id,
            t.channel_code AS channel_code,
            t.sms_type_code AS sms_type_code,
            t.effective_time AS effective_time,
            t.ineffective_time AS ineffective_time,
            t.per_price AS per_price,
            t.cur_price AS cur_price,
            t.creator AS creator,
            t.create_time AS create_time,
            t.updater AS updater,
            t.update_time AS update_time,
            t.sign_name AS sign_name
        FROM (
        SELECT *,
        ROW_NUMBER() OVER (PARTITION BY channel_code, sms_type_code, sign_name ORDER BY effective_time DESC) AS rn
        FROM t_channel_sms_type_price_log
        ) t
        WHERE t.rn = 1;
    </select>

    <select id="getLatestPricesByDefault" resultMap="BaseResultMap">
        SELECT
            t.id AS id,
            t.channel_code AS channel_code,
            t.sms_type_code AS sms_type_code,
            t.effective_time AS effective_time,
            t.ineffective_time AS ineffective_time,
            t.per_price AS per_price,
            t.cur_price AS cur_price,
            t.creator AS creator,
            t.create_time AS create_time,
            t.updater AS updater,
            t.update_time AS update_time,
            t.sign_name AS sign_name
        FROM (
                 SELECT *,
                        ROW_NUMBER() OVER (PARTITION BY channel_code, sms_type_code, sign_name ORDER BY effective_time DESC) AS rn
                 FROM t_channel_sms_type_price_log
             ) t
        WHERE t.rn = 1 and t.sign_name = 'default';
    </select>

    <select id="getLatestPricesBySign" resultMap="BaseResultMap">
        SELECT
            t.id AS id,
            t.channel_code AS channel_code,
            t.sms_type_code AS sms_type_code,
            t.effective_time AS effective_time,
            t.ineffective_time AS ineffective_time,
            t.per_price AS per_price,
            t.cur_price AS cur_price,
            t.creator AS creator,
            t.create_time AS create_time,
            t.updater AS updater,
            t.update_time AS update_time,
            t.sign_name AS sign_name
        FROM (
                 SELECT *,
                        ROW_NUMBER() OVER (PARTITION BY channel_code, sms_type_code, sign_name ORDER BY effective_time DESC) AS rn
                 FROM t_channel_sms_type_price_log
             ) t
        WHERE t.rn = 1 and t.sign_name NOT IN ('default', '');
    </select>

    <select id="getLatestPriceByChannelAndType" resultMap="BaseResultMap">

        SELECT
        <include refid="Base_Column_List" />
        FROM t_channel_sms_type_price_log
        WHERE channel_code = #{channelCode}
        AND sms_type_code = #{smsTypeCode}
        AND effective_time <![CDATA[<=]]> NOW()
        AND ineffective_time <![CDATA[>]]> NOW()
        ORDER BY effective_time DESC
        LIMIT 1;

    </select>
</mapper>
