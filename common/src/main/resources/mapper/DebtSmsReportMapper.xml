<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.DebtSmsReportMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.DebtSmsReportDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="report_date" jdbcType="TIMESTAMP" property="reportDate"/>
        <result column="tpl_code" jdbcType="VARCHAR" property="tplCode"/>
        <result column="biz_batch_id" jdbcType="VARCHAR" property="bizBatchId"/>
        <result column="start_time" jdbcType="INTEGER" property="startTime"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="alert_times" jdbcType="INTEGER" property="alertTimes"/>
        <result column="submit_amount" jdbcType="INTEGER" property="submitAmount"/>
        <result column="valid_amount" jdbcType="INTEGER" property="validAmount"/>
        <result column="order_amount" jdbcType="INTEGER" property="orderAmount"/>
        <result column="success_amount" jdbcType="INTEGER" property="successAmount"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, report_date, tpl_code, biz_batch_id, start_time, status,alert_times, submit_amount, valid_amount,
        order_amount,
        success_amount, remark,create_time, update_time, is_delete
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from t_debt_sms_report
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from t_debt_sms_report
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.DebtSmsReportDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_debt_sms_report (id, report_date, tpl_code,
        biz_batch_id, start_time, status,alert_times, submit_amount,
        valid_amount, order_amount, success_amount,remark,
        create_time, update_time, is_delete
        )
        values (#{id,jdbcType=BIGINT}, #{reportDate,jdbcType=TIMESTAMP}, #{tplCode,jdbcType=VARCHAR},
        #{bizBatchId,jdbcType=VARCHAR}, #{startTime,jdbcType=INTEGER},
        #{status,jdbcType=TINYINT},#{alertTimes,jdbcType=INTEGER},
        #{submitAmount,jdbcType=INTEGER},
        #{validAmount,jdbcType=INTEGER}, #{orderAmount,jdbcType=INTEGER}, #{successAmount,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.DebtSmsReportDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_debt_sms_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="reportDate != null">
                report_date,
            </if>
            <if test="tplCode != null">
                tpl_code,
            </if>
            <if test="bizBatchId != null">
                biz_batch_id,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="alertTimes != null">
                alert_times,
            </if>
            <if test="submitAmount != null">
                submit_amount,
            </if>
            <if test="validAmount != null">
                valid_amount,
            </if>
            <if test="orderAmount != null">
                order_amount,
            </if>
            <if test="successAmount != null">
                success_amount,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="reportDate != null">
                #{reportDate,jdbcType=TIMESTAMP},
            </if>
            <if test="tplCode != null">
                #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="bizBatchId != null">
                #{bizBatchId,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="alertTimes != null">
                #{alertTimes,jdbcType=INTEGER},
            </if>
            <if test="submitAmount != null">
                #{submitAmount,jdbcType=INTEGER},
            </if>
            <if test="validAmount != null">
                #{validAmount,jdbcType=INTEGER},
            </if>
            <if test="orderAmount != null">
                #{orderAmount,jdbcType=INTEGER},
            </if>
            <if test="successAmount != null">
                #{successAmount,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.DebtSmsReportDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_debt_sms_report
        <set>
            <if test="reportDate != null">
                report_date = #{reportDate,jdbcType=TIMESTAMP},
            </if>
            <if test="tplCode != null">
                tpl_code = #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="bizBatchId != null">
                biz_batch_id = #{bizBatchId,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="alertTimes != null">
                alert_times = #{alertTimes,jdbcType=INTEGER},
            </if>
            <if test="submitAmount != null">
                submit_amount = #{submitAmount,jdbcType=INTEGER},
            </if>
            <if test="validAmount != null">
                valid_amount = #{validAmount,jdbcType=INTEGER},
            </if>
            <if test="orderAmount != null">
                order_amount = #{orderAmount,jdbcType=INTEGER},
            </if>
            <if test="successAmount != null">
                success_amount = #{successAmount,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.DebtSmsReportDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_debt_sms_report
        set report_date = #{reportDate,jdbcType=TIMESTAMP},
        tpl_code = #{tplCode,jdbcType=VARCHAR},
        biz_batch_id = #{bizBatchId,jdbcType=VARCHAR},
        start_time = #{startTime,jdbcType=INTEGER},
        status = #{status,jdbcType=TINYINT},
        alert_times = #{alertTimes,jdbcType=INTEGER},
        submit_amount = #{submitAmount,jdbcType=INTEGER},
        valid_amount = #{validAmount,jdbcType=INTEGER},
        order_amount = #{orderAmount,jdbcType=INTEGER},
        success_amount = #{successAmount,jdbcType=INTEGER},
        remark = #{remark,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_delete = #{isDelete,jdbcType=TINYINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectDebtSmsReportJob" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_debt_sms_report
        where id > #{lastId}
        and status = 0
        and start_time<![CDATA[ >= ]]> #{beginStartTime}
        and start_time<![CDATA[ <= ]]> #{endStartTime}
        order by id asc
        limit #{pageSize}
    </select>
</mapper>