<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.ShortUrlBrandStatMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.ShortUrlBrandStatDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="stat_date" jdbcType="DATE" property="statDate"/>
        <result column="user_tpl_code" jdbcType="VARCHAR" property="userTplCode"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="pv" jdbcType="INTEGER" property="pv"/>
        <result column="uv" jdbcType="INTEGER" property="uv"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, stat_date, user_tpl_code, brand, pv, uv, create_time, update_time,is_delete
    </sql>
    <insert id="insertBatch">
        insert into t_short_url_brand_stat (stat_date, user_tpl_code, brand, pv, uv, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.statDate,jdbcType=DATE},
            #{item.userTplCode,jdbcType=VARCHAR},
            #{item.brand,jdbcType=VARCHAR},
            #{item.pv,jdbcType=INTEGER},
            #{item.uv,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    <update id="updateDeleteTagByTime">
        update t_short_url_brand_stat
        set is_delete = 1 , update_time = now()
        where stat_date = #{statDate}
    </update>
    <select id="countByQuery" resultType="java.lang.Integer">
        select count(1) from t_short_url_brand_stat
        <where>
            and is_delete = 0
            <if test="query.brands != null and query.brands!=''">
                and brand in
                <foreach collection="query.brands.split(',')" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.unContainsBrands != null and query.unContainsBrands!=''">
                <foreach collection="query.unContainsBrands.split(',')" item="item" separator=" ">
                    and brand != #{item}
                </foreach>
            </if>
            <if test="query.shortCodes != null and query.shortCodes!=''">
                and user_tpl_code in
                <foreach collection="query.shortCodes.split(',')" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.startTime != null and query.startTime!=''">
                and stat_date >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime!=''">
                and stat_date &lt;= #{query.endTime}
            </if>
        </where>

    </select>
    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_short_url_brand_stat
        <where>
            and is_delete = 0
            <if test="query.brands != null and query.brands!=''">
                and brand in
                <foreach collection="query.brands.split(',')" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.unContainsBrands != null and query.unContainsBrands!=''">
                <foreach collection="query.unContainsBrands.split(',')" item="item" separator=" ">
                    and brand != #{item}
                </foreach>
            </if>
            <if test="query.shortCodes != null and query.shortCodes!=''">
                and user_tpl_code in
                <foreach collection="query.shortCodes.split(',')" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.startTime != null and query.startTime!=''">
                and stat_date >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime!=''">
                and stat_date &lt;= #{query.endTime}
            </if>
        </where>
        order by stat_date desc
        <if test="query.pageParameter != null">
            limit #{query.pageParameter.offset},#{query.pageParameter.pageSize}
        </if>
    </select>
    <select id="selectByTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_short_url_brand_stat
        where is_delete = 0
        and stat_date = #{statDate}
        order by brand
    </select>

</mapper>