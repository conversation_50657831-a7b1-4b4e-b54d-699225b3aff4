<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.SmsHisStatisOldMapper" >
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.SmsHisStatisDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="code" property="code" jdbcType="VARCHAR" />
    <result column="his_send_total" property="hisSendTotal" jdbcType="BIGINT" />
    <result column="his_billing_total" property="hisBillingTotal" jdbcType="BIGINT" />
    <result column="lastmonth_send_total" property="lastmonthSendTotal" jdbcType="BIGINT" />
    <result column="lastmonth_billing_total" property="lastmonthBillingTotal" jdbcType="BIGINT" />
    <result column="curmonth_send_total" property="curmonthSendTotal" jdbcType="BIGINT" />
    <result column="curmonth_billing_total" property="curmonthBillingTotal" jdbcType="BIGINT" />
    <result column="lastday_send_total" property="lastdaySendTotal" jdbcType="BIGINT" />
    <result column="lastday_billing_total" property="lastdayBillingTotal" jdbcType="BIGINT" />
    <result column="today_send_total" property="todaySendTotal" jdbcType="BIGINT" />
    <result column="today_billing_total" property="todayBillingTotal" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    code, his_send_total, his_billing_total, lastmonth_send_total, lastmonth_billing_total, 
    curmonth_send_total, curmonth_billing_total, lastday_send_total, lastday_billing_total, 
    today_send_total, today_billing_total, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_sms_his_statis
    where code = #{code,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_sms_his_statis
    where code = #{code,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.SmsHisStatisDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_sms_his_statis (code, his_send_total, his_billing_total, 
      lastmonth_send_total, lastmonth_billing_total, 
      curmonth_send_total, curmonth_billing_total, 
      lastday_send_total, lastday_billing_total, today_send_total, 
      today_billing_total, create_time, update_time
      )
    values (#{code,jdbcType=VARCHAR}, #{hisSendTotal,jdbcType=BIGINT}, #{hisBillingTotal,jdbcType=BIGINT}, 
      #{lastmonthSendTotal,jdbcType=BIGINT}, #{lastmonthBillingTotal,jdbcType=BIGINT}, 
      #{curmonthSendTotal,jdbcType=BIGINT}, #{curmonthBillingTotal,jdbcType=BIGINT}, 
      #{lastdaySendTotal,jdbcType=BIGINT}, #{lastdayBillingTotal,jdbcType=BIGINT}, #{todaySendTotal,jdbcType=BIGINT}, 
      #{todayBillingTotal,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.SmsHisStatisDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_sms_his_statis
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="code != null" >
        code,
      </if>
      <if test="hisSendTotal != null" >
        his_send_total,
      </if>
      <if test="hisBillingTotal != null" >
        his_billing_total,
      </if>
      <if test="lastmonthSendTotal != null" >
        lastmonth_send_total,
      </if>
      <if test="lastmonthBillingTotal != null" >
        lastmonth_billing_total,
      </if>
      <if test="curmonthSendTotal != null" >
        curmonth_send_total,
      </if>
      <if test="curmonthBillingTotal != null" >
        curmonth_billing_total,
      </if>
      <if test="lastdaySendTotal != null" >
        lastday_send_total,
      </if>
      <if test="lastdayBillingTotal != null" >
        lastday_billing_total,
      </if>
      <if test="todaySendTotal != null" >
        today_send_total,
      </if>
      <if test="todayBillingTotal != null" >
        today_billing_total,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="hisSendTotal != null" >
        #{hisSendTotal,jdbcType=BIGINT},
      </if>
      <if test="hisBillingTotal != null" >
        #{hisBillingTotal,jdbcType=BIGINT},
      </if>
      <if test="lastmonthSendTotal != null" >
        #{lastmonthSendTotal,jdbcType=BIGINT},
      </if>
      <if test="lastmonthBillingTotal != null" >
        #{lastmonthBillingTotal,jdbcType=BIGINT},
      </if>
      <if test="curmonthSendTotal != null" >
        #{curmonthSendTotal,jdbcType=BIGINT},
      </if>
      <if test="curmonthBillingTotal != null" >
        #{curmonthBillingTotal,jdbcType=BIGINT},
      </if>
      <if test="lastdaySendTotal != null" >
        #{lastdaySendTotal,jdbcType=BIGINT},
      </if>
      <if test="lastdayBillingTotal != null" >
        #{lastdayBillingTotal,jdbcType=BIGINT},
      </if>
      <if test="todaySendTotal != null" >
        #{todaySendTotal,jdbcType=BIGINT},
      </if>
      <if test="todayBillingTotal != null" >
        #{todayBillingTotal,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.SmsHisStatisDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_sms_his_statis
    <set >
      <if test="hisSendTotal != null" >
        his_send_total = #{hisSendTotal,jdbcType=BIGINT},
      </if>
      <if test="hisBillingTotal != null" >
        his_billing_total = #{hisBillingTotal,jdbcType=BIGINT},
      </if>
      <if test="lastmonthSendTotal != null" >
        lastmonth_send_total = #{lastmonthSendTotal,jdbcType=BIGINT},
      </if>
      <if test="lastmonthBillingTotal != null" >
        lastmonth_billing_total = #{lastmonthBillingTotal,jdbcType=BIGINT},
      </if>
      <if test="curmonthSendTotal != null" >
        curmonth_send_total = #{curmonthSendTotal,jdbcType=BIGINT},
      </if>
      <if test="curmonthBillingTotal != null" >
        curmonth_billing_total = #{curmonthBillingTotal,jdbcType=BIGINT},
      </if>
      <if test="lastdaySendTotal != null" >
        lastday_send_total = #{lastdaySendTotal,jdbcType=BIGINT},
      </if>
      <if test="lastdayBillingTotal != null" >
        lastday_billing_total = #{lastdayBillingTotal,jdbcType=BIGINT},
      </if>
      <if test="todaySendTotal != null" >
        today_send_total = #{todaySendTotal,jdbcType=BIGINT},
      </if>
      <if test="todayBillingTotal != null" >
        today_billing_total = #{todayBillingTotal,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where code = #{code,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.SmsHisStatisDO" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_sms_his_statis
    set his_send_total = #{hisSendTotal,jdbcType=BIGINT},
      his_billing_total = #{hisBillingTotal,jdbcType=BIGINT},
      lastmonth_send_total = #{lastmonthSendTotal,jdbcType=BIGINT},
      lastmonth_billing_total = #{lastmonthBillingTotal,jdbcType=BIGINT},
      curmonth_send_total = #{curmonthSendTotal,jdbcType=BIGINT},
      curmonth_billing_total = #{curmonthBillingTotal,jdbcType=BIGINT},
      lastday_send_total = #{lastdaySendTotal,jdbcType=BIGINT},
      lastday_billing_total = #{lastdayBillingTotal,jdbcType=BIGINT},
      today_send_total = #{todaySendTotal,jdbcType=BIGINT},
      today_billing_total = #{todayBillingTotal,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where code = #{code,jdbcType=VARCHAR}
  </update>

  <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.SmsHisStatisQuery" resultType="java.lang.Integer">
    select count(*)
    from t_sms_his_statis
    <where>
      <if test="codeList != null">
        code in
        <foreach collection="codeList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.SmsHisStatisQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_sms_his_statis
    <where>
      <if test="codeList != null">
        code in
        <foreach collection="codeList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    order by update_time desc
    <if test="pageParameter != null">
      limit #{pageParameter.offset}, #{pageParameter.pageSize}
    </if>
  </select>
</mapper>