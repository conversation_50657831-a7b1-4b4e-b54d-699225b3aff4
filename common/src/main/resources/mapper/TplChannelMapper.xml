<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.TplChannelMapper">
  <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.TplChannelDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="tpl_id" jdbcType="INTEGER" property="tplId" />
    <result column="channel_account_id" jdbcType="INTEGER" property="channelAccountId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="channel_tpl_id" jdbcType="VARCHAR" property="channelTplId" />
    <result column="isps" jdbcType="VARCHAR" property="isps" />
    <result column="area_filter_type" jdbcType="TINYINT" property="areaFilterType" />
    <result column="areas" jdbcType="VARCHAR" property="areas" />
    <result column="weight" jdbcType="INTEGER" property="weight" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="tpl_content" jdbcType="VARCHAR" property="tplContent" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <resultMap id="BaseResultMapSupport" extends="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.support.TplChannelDOSupport">
    <result column="channel_account_name" jdbcType="VARCHAR" property="channelAccountName" />
  </resultMap>

  <resultMap id="TopTplChannelResultMap" extends="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.TopTplChannelDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="tpl_id" jdbcType="INTEGER" property="tplId" />
    <result column="channel_account_id" jdbcType="INTEGER" property="channelAccountId" />
    <result column="weight" jdbcType="INTEGER" property="weight" />
    <result column="channel_code" jdbcType="VARCHAR" property="channelCode" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, tpl_id, channel_account_id, status, channel_tpl_id, isps, area_filter_type, areas, 
    weight, remark, tpl_content, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_tpl_channel
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_tpl_channel
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.TplChannelDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_tpl_channel (id, tpl_id, channel_account_id, 
      status, channel_tpl_id, isps, 
      area_filter_type, areas, weight,
      remark, tpl_content, create_time,
      update_time)
    values (#{id,jdbcType=INTEGER}, #{tplId,jdbcType=INTEGER}, #{channelAccountId,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{channelTplId,jdbcType=VARCHAR}, #{isps,jdbcType=VARCHAR}, 
      #{areaFilterType,jdbcType=TINYINT}, #{areas,jdbcType=VARCHAR}, #{weight,jdbcType=INTEGER},
      #{remark,jdbcType=VARCHAR}, #{tplContent,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.TplChannelDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into t_tpl_channel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tplId != null">
        tpl_id,
      </if>
      <if test="channelAccountId != null">
        channel_account_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="channelTplId != null">
        channel_tpl_id,
      </if>
      <if test="isps != null">
        isps,
      </if>
      <if test="areaFilterType != null">
        area_filter_type,
      </if>
      <if test="areas != null">
        areas,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="tplContent != null">
        tpl_content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="tplId != null">
        #{tplId,jdbcType=INTEGER},
      </if>
      <if test="channelAccountId != null">
        #{channelAccountId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="channelTplId != null">
        #{channelTplId,jdbcType=VARCHAR},
      </if>
      <if test="isps != null">
        #{isps,jdbcType=VARCHAR},
      </if>
      <if test="areaFilterType != null">
        #{areaFilterType,jdbcType=TINYINT},
      </if>
      <if test="areas != null">
        #{areas,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tplContent != null">
        #{tplContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.TplChannelDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_tpl_channel
    <set>
      <if test="tplId != null">
        tpl_id = #{tplId,jdbcType=INTEGER},
      </if>
      <if test="channelAccountId != null">
        channel_account_id = #{channelAccountId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="channelTplId != null">
        channel_tpl_id = #{channelTplId,jdbcType=VARCHAR},
      </if>
      <if test="isps != null">
        isps = #{isps,jdbcType=VARCHAR},
      </if>
      <if test="areaFilterType != null">
        area_filter_type = #{areaFilterType,jdbcType=TINYINT},
      </if>
      <if test="areas != null">
        areas = #{areas,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tplContent != null">
        tpl_content = #{tplContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.TplChannelDO">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_tpl_channel
    set tpl_id = #{tplId,jdbcType=INTEGER},
      channel_account_id = #{channelAccountId,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      channel_tpl_id = #{channelTplId,jdbcType=VARCHAR},
      isps = #{isps,jdbcType=VARCHAR},
      area_filter_type = #{areaFilterType,jdbcType=TINYINT},
      areas = #{areas,jdbcType=VARCHAR},
      weight = #{weight,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      tpl_content = #{tplContent,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByTplId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_tpl_channel
    where tpl_id = #{tplId}
  </select>

  <delete id="deleteByTplId" parameterType="java.lang.Integer">
    delete from t_tpl_channel where tpl_id = #{tplId}
  </delete>

  <select id="selectByChannelAccountId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_tpl_channel
    where channel_account_id = #{channelAccountId}
  </select>

  <select id="selectSupportByTplId" parameterType="java.lang.Integer" resultMap="BaseResultMapSupport">
    select ttc.*,tca.`name` as 'channel_account_name'
    from t_tpl_channel ttc
    left join t_channel_account tca on ttc.channel_account_id = tca.id
    where  ttc.tpl_id = #{tplId}
  </select>

    <insert id="saveByTplChannelDOList">
        insert into t_tpl_channel ( id, tpl_id, channel_account_id, isps, area_filter_type, areas,
        weight, remark, tpl_content) values
        <foreach collection="tplChannelDOList" separator="," item="item">
            ( #{item.id}, #{item.tplId}, #{item.channelAccountId},
            #{item.isps},
            #{item.areaFilterType}, #{item.areas}, #{item.weight}, #{item.remark}, #{item.tplContent})
        </foreach>
    </insert>

    <delete id="deleteByTplIdList">
        delete from t_tpl_channel where tpl_id in
        <foreach collection="tplIdList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByCover">
      delete from t_tpl_channel
      where tpl_id in
      <foreach collection="tplIdList" item="tplId" separator="," open="(" close=")">
        #{tplId}
      </foreach>
      and channel_account_id in
      <foreach collection="channelAccountIdList" item="channelAccountId" separator="," open="(" close=")">
        #{channelAccountId}
      </foreach>
    </delete>

  <delete id="updateWeightByPrimaryKey" parameterType="java.lang.Integer">
    update t_tpl_channel
    set weight = #{weight,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <select id="selectByTplIdList" resultMap="TopTplChannelResultMap">
    select a.channel_code as channel_code,
           c.channel_account_id as channel_account_id,
           c.tpl_id as tpl_id,
           sum(c.weight) as weight
    from t_tpl_channel as c
        left join t_channel_account as a
            on a.id = c.channel_account_id
    where c.tpl_id in
    <foreach collection="tplIdList" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
    group by c.tpl_id, a.channel_code
  </select>
</mapper>