<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.FailResendRuleMapper">

    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.FailResendRuleDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="strategyId" column="strategy_id" jdbcType="VARCHAR"/>
        <result property="ruleId" column="rule_id" jdbcType="VARCHAR"/>
        <result property="sceneType" column="scene_type" jdbcType="VARCHAR"/>
        <result property="sceneValue" column="scene_value" jdbcType="VARCHAR"/>
        <result property="ruleType" column="rule_type" jdbcType="VARCHAR"/>
        <result property="ruleValue" column="rule_value" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , strategy_id, rule_id, scene_type,
          scene_value, rule_type, rule_value,
          is_delete, create_time, update_time
    </sql>
    <insert id="insertSelective">
        INSERT INTO t_fail_resend_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">
                strategy_id,
            </if>
            <if test="ruleId != null">
                rule_id,
            </if>
            <if test="sceneType != null">
                scene_type,
            </if>
            <if test="sceneValue != null">
                scene_value,
            </if>
            <if test="ruleType != null">
                rule_type,
            </if>
            <if test="ruleValue != null">
                rule_value,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">
                #{strategyId},
            </if>
            <if test="ruleId != null">
                #{ruleId},
            </if>
            <if test="sceneType != null">
                #{sceneType},
            </if>
            <if test="sceneValue != null">
                #{sceneValue},
            </if>
            <if test="ruleType != null">
                #{ruleType},
            </if>
            <if test="ruleValue != null">
                #{ruleValue},
            </if>
            <if test="isDelete != null">
                #{isDelete},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>
    <update id="updateDeleteTagByStrategyId">
        UPDATE t_fail_resend_rule
        SET is_delete = 1
        WHERE strategy_id = #{strategyId}
    </update>
    <select id="selectByStrategyId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_fail_resend_rule
        WHERE strategy_id = #{strategyId} and is_delete = 0
    </select>

    <select id="selectAllEnabled" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_fail_resend_rule
        WHERE is_delete = 0
        ORDER BY strategy_id
    </select>

</mapper>
