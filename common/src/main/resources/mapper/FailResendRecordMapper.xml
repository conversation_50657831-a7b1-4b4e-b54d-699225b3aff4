<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhqb.spectre.common.dal.mapper.FailResendRecordMapper">

    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.FailResendRecordDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="originalOrderId" column="original_order_id" jdbcType="VARCHAR"/>
        <result property="originalTplCode" column="original_tpl_code" jdbcType="VARCHAR"/>
        <result property="originalParameter" column="original_parameter" jdbcType="VARCHAR"/>
        <result property="originalReportCode" column="original_report_code" jdbcType="VARCHAR"/>
        <result property="originalSignName" column="original_sign_name" jdbcType="VARCHAR"/>
        <result property="ispCode" column="isp_code" jdbcType="VARCHAR"/>
        <result property="tplCode" column="tpl_code" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile_enc" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="INTEGER"/>
        <result property="strategyId" column="strategy_id" jdbcType="VARCHAR"/>
        <result property="ruleId" column="rule_id" jdbcType="VARCHAR"/>
        <result property="requestId" column="request_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , original_order_id, original_tpl_code, original_parameter,
          original_report_code, original_sign_name, isp_code, strategy_id,
          tpl_code, mobile_enc, start_time, rule_id, request_id,
          status, create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.FailResendRecordDO">
        INSERT IGNORE INTO t_fail_resend_record (original_order_id, original_tpl_code, original_parameter,
                                                 original_report_code, original_sign_name, isp_code,
                                                 strategy_id, tpl_code, mobile_enc, start_time,
                                                 rule_id, request_id, status, create_time, update_time)
        VALUES (#{originalOrderId}, #{originalTplCode}, #{originalParameter},
                #{originalReportCode}, #{originalSignName}, #{ispCode},
                #{strategyId}, #{tplCode}, #{mobile}, #{startTime},
                #{ruleId}, #{requestId}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_fail_resend_record
        WHERE id = #{id}
    </select>

    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.FailResendRecordDO">
        UPDATE t_fail_resend_record
        SET original_order_id = #{originalOrderId},
            original_tpl_code = #{originalTplCode},
            original_parameter = #{originalParameter},
            original_report_code = #{originalReportCode},
            original_sign_name = #{originalSignName},
            isp_code = #{ispCode},
            strategy_id = #{strategyId},
            rule_id = #{ruleId},
            tpl_code = #{tplCode},
            mobile_enc = #{mobile},
            start_time = #{startTime},
            request_id = #{requestId},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="selectByOriginalOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_fail_resend_record
        WHERE original_order_id = #{originalOrderId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_fail_resend_record
        WHERE status = #{status}
        ORDER BY create_time DESC
    </select>

    <select id="selectByStatusList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_fail_resend_record
        WHERE status IN
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
        ORDER BY create_time DESC
    </select>

    <select id="selectByStatusWithLimit" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_fail_resend_record
        WHERE status = #{status}
        ORDER BY create_time ASC
        LIMIT #{limit}
    </select>

    <select id="selectPendingRecordsAfterDate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_fail_resend_record
        WHERE status = #{status}
        AND create_time >= #{afterDate}
        ORDER BY create_time ASC
        LIMIT #{limit}
    </select>

    <update id="updateStatusById">
        UPDATE t_fail_resend_record
        SET status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <update id="updateStatusByIdWithCondition">
        UPDATE t_fail_resend_record
        SET status = #{newStatus},
            update_time = #{updateTime}
        WHERE id = #{id}
        AND status = #{oldStatus}
    </update>

</mapper>
