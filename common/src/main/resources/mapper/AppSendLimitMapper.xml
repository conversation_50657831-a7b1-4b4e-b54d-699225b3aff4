<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xhqb.spectre.common.dal.mapper.AppSendLimitMapper">
    <resultMap id="BaseResultMap" type="com.xhqb.spectre.common.dal.entity.AppSendLimitDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="app_code" property="appCode" jdbcType="VARCHAR"/>
        <result column="send_limit_key" property="sendLimitKey" jdbcType="VARCHAR"/>
        <result column="send_limit_value" property="sendLimitValue" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, app_code, send_limit_key, send_limit_value, status, create_time, creator, updater,
        update_time, is_delete
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from t_app_send_limit
        where is_delete = 0 and id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from t_app_send_limit
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.xhqb.spectre.common.dal.entity.AppSendLimitDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_app_send_limit (id, app_code, send_limit_key,
        send_limit_value, status, create_time,
        creator, updater, update_time,
        is_delete)
        values (#{id,jdbcType=INTEGER}, #{appCode,jdbcType=VARCHAR}, #{sendLimitKey,jdbcType=VARCHAR},
        #{sendLimitValue,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP},
        #{creator,jdbcType=VARCHAR}, #{updater,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDelete,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" parameterType="com.xhqb.spectre.common.dal.entity.AppSendLimitDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into t_app_send_limit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="appCode != null">
                app_code,
            </if>
            <if test="sendLimitKey != null">
                send_limit_key,
            </if>
            <if test="sendLimitValue != null">
                send_limit_value,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="appCode != null">
                #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="sendLimitKey != null">
                #{sendLimitKey,jdbcType=VARCHAR},
            </if>
            <if test="sendLimitValue != null">
                #{sendLimitValue,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xhqb.spectre.common.dal.entity.AppSendLimitDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_app_send_limit
        <set>
            <if test="appCode != null">
                app_code = #{appCode,jdbcType=VARCHAR},
            </if>
            <if test="sendLimitKey != null">
                send_limit_key = #{sendLimitKey,jdbcType=VARCHAR},
            </if>
            <if test="sendLimitValue != null">
                send_limit_value = #{sendLimitValue,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xhqb.spectre.common.dal.entity.AppSendLimitDO">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update t_app_send_limit
        set app_code = #{appCode,jdbcType=VARCHAR},
        send_limit_key = #{sendLimitKey,jdbcType=VARCHAR},
        send_limit_value = #{sendLimitValue,jdbcType=VARCHAR},
        status = #{status,jdbcType=TINYINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        creator = #{creator,jdbcType=VARCHAR},
        updater = #{updater,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_delete = #{isDelete,jdbcType=TINYINT}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectOne" parameterType="com.xhqb.spectre.common.dal.entity.AppSendLimitDO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_app_send_limit
        where app_code = #{appCode,jdbcType=VARCHAR} and send_limit_key = #{sendLimitKey,jdbcType=VARCHAR} and status =
        #{status,jdbcType=TINYINT}
    </select>

    <select id="selectList" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from t_app_send_limit
        where is_delete = 0 and status = 1 and app_code = #{appCode,jdbcType=VARCHAR}
    </select>

    <select id="selectAllEnabled" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_app_send_limit
        where is_delete = 0 and status = 1
    </select>

    <select id="selectByAppCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from t_app_send_limit
        where is_delete = 0 and app_code = #{appCode,jdbcType=VARCHAR}
    </select>

    <select id="selectByAppCodeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_app_send_limit
        where is_delete = 0 and app_code in
        <foreach item="appCode" collection="list" open="(" separator="," close=")">
            #{appCode}
        </foreach>
    </select>

    <select id="countByQuery" parameterType="com.xhqb.spectre.common.dal.query.AppSendLimitQuery" resultType="java.lang.Integer">
        select count(distinct app_code)
        from t_app_send_limit
        <where>
            is_delete = 0
            <if test="appCode != null and appCode != ''">and app_code = #{appCode}</if>
            <if test="status != null and status != -1">and status = #{status}</if>
        </where>
    </select>

    <select id="selectByQuery" parameterType="com.xhqb.spectre.common.dal.query.AppSendLimitQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_app_send_limit
        <where>
            is_delete = 0
            <if test="appCode != null and appCode != ''">and app_code = #{appCode}</if>
            <if test="status != null and status != -1">and status = #{status}</if>
        </where>
        group by app_code
        order by update_time desc
        <if test="pageParameter != null">
            limit #{pageParameter.offset}, #{pageParameter.pageSize}
        </if>
    </select>

    <update id="enable">
        update `t_app_send_limit`
        set `status` = 1, `updater` = #{operator}
        where `app_code` = #{appCode} and `status` = 0
    </update>

    <update id="disable">
        update `t_app_send_limit`
        set `status` = 0, `updater` = #{operator}
        where `app_code` = #{appCode} and `status` = 1
    </update>

    <update id="delete">
        update `t_app_send_limit`
        set `is_delete` = 1, `updater` = #{operator}
        where `app_code` = #{appCode} and `status` = 0
    </update>
</mapper>