package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 补发状态枚举
 */
@Getter
@AllArgsConstructor
public enum FailResendStatusEnum {

    PENDING(0, "待补发", "PENDING"),
    PROCESSING(1, "补发中", "PROCESSING"),
    SUCCESS(2, "补发成功", "SUCCESS"),
    FAILED(3, "补发失败", "FAILED");
    private final Integer code;

    private final String description;

    private final String name;

    private static final Map<Integer, FailResendStatusEnum> CODE_MAP = 
            Arrays.stream(values()).collect(Collectors.toMap(FailResendStatusEnum::getCode, Function.identity()));
    

    private static final Map<String, FailResendStatusEnum> NAME_MAP = 
            Arrays.stream(values()).collect(Collectors.toMap(FailResendStatusEnum::getName, Function.identity()));
    
    /**
     * 根据状态码获取枚举
     * @param code 状态码
     * @return 对应的枚举，如果不存在返回null
     */
    public static FailResendStatusEnum getByCode(Integer code) {
        return CODE_MAP.get(code);
    }
    
    /**
     * 根据名称获取枚举
     * @param name 名称
     * @return 对应的枚举，如果不存在返回null
     */
    public static FailResendStatusEnum getByName(String name) {
        return NAME_MAP.get(name);
    }
    
    /**
     * 判断状态码是否有效
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return CODE_MAP.containsKey(code);
    }
    
    /**
     * 判断是否为终态状态
     * 终态状态：补发成功、补发失败
     * @return 是否为终态
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED;
    }
    
    /**
     * 判断是否为处理中状态
     * 处理中状态：待补发、补发中
     * @return 是否为处理中
     */
    public boolean isProcessingStatus() {
        return this == PENDING || this == PROCESSING;
    }
    
    /**
     * 判断是否为成功状态
     * @return 是否为成功状态
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
    
    /**
     * 判断是否为失败状态
     * @return 是否为失败状态
     */
    public boolean isFailed() {
        return this == FAILED;
    }
    
    /**
     * 判断是否可以重试
     * 只有失败状态可以重试
     * @return 是否可以重试
     */
    public boolean canRetry() {
        return this == FAILED;
    }

    public boolean canCancel() {
        return this == PENDING || this == PROCESSING;
    }


    /**
     * 获取所有处理中状态
     * @return 处理中状态数组
     */
    public static FailResendStatusEnum[] getProcessingStatuses() {
        return new FailResendStatusEnum[]{PENDING, PROCESSING};
    }
    
    /**
     * 状态转换是否合法
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @return 是否合法
     */
    public static boolean isValidTransition(FailResendStatusEnum fromStatus, FailResendStatusEnum toStatus) {
        if (fromStatus == null || toStatus == null) {
            return false;
        }
        
        switch (fromStatus) {
            case PENDING:
                return toStatus == PROCESSING || toStatus == FAILED;
            case PROCESSING:
                return toStatus == SUCCESS || toStatus == FAILED;
            case SUCCESS:
            case FAILED:
                return false;
            default:
                return false;
        }
    }

    
    @Override
    public String toString() {
        return String.format("%s(%d)", description, code);
    }
}
