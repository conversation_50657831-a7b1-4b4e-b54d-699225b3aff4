package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/9/17
 */
@Getter
@AllArgsConstructor
public enum SendTypeEnum {

    IMMEDIATE(1, "立即发送"),
    DELAYED(2, "定时发送");

    private final Integer type;
    private final String description;

    /**
     * 检查发送类型是否合法
     *
     * @param type
     * @return
     */
    public static boolean checkSendType(Integer type) {
        return Arrays.stream(SendTypeEnum.values()).anyMatch(s -> Objects.equals(s.getType(), type));
    }
}
