package com.xhqb.spectre.common.dal.entity;

import java.io.Serializable;
import java.util.Date;

public class ClientChannelAccountDO implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.service_id
     *
     * @mbggenerated
     */
    private String serviceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.username
     *
     * @mbggenerated
     */
    private String username;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.password
     *
     * @mbggenerated
     */
    private String password;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.is_channel_connect
     *
     * @mbggenerated
     */
    private Byte isChannelConnect;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.is_valid
     *
     * @mbggenerated
     */
    private Byte isValid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.version
     *
     * @mbggenerated
     */
    private Integer version;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.max_connect
     *
     * @mbggenerated
     */
    private Integer maxConnect;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.write_limit
     *
     * @mbggenerated
     */
    private Integer writeLimit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.read_limit
     *
     * @mbggenerated
     */
    private Integer readLimit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.ip_list
     *
     * @mbggenerated
     */
    private String ipList;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.is_ip_check
     *
     * @mbggenerated
     */
    private Byte isIpCheck;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.group_name
     *
     * @mbggenerated
     */
    private String groupName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.idle_time_sec
     *
     * @mbggenerated
     */
    private Integer idleTimeSec;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.creator
     *
     * @mbggenerated
     */
    private String creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.updater
     *
     * @mbggenerated
     */
    private String updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_client_channel_account.is_delete
     *
     * @mbggenerated
     */
    private Byte isDelete;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.id
     *
     * @return the value of t_client_channel_account.id
     *
     * @mbggenerated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.id
     *
     * @param id the value for t_client_channel_account.id
     *
     * @mbggenerated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.service_id
     *
     * @return the value of t_client_channel_account.service_id
     *
     * @mbggenerated
     */
    public String getServiceId() {
        return serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.service_id
     *
     * @param serviceId the value for t_client_channel_account.service_id
     *
     * @mbggenerated
     */
    public void setServiceId(String serviceId) {
        this.serviceId = serviceId == null ? null : serviceId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.username
     *
     * @return the value of t_client_channel_account.username
     *
     * @mbggenerated
     */
    public String getUsername() {
        return username;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.username
     *
     * @param username the value for t_client_channel_account.username
     *
     * @mbggenerated
     */
    public void setUsername(String username) {
        this.username = username == null ? null : username.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.password
     *
     * @return the value of t_client_channel_account.password
     *
     * @mbggenerated
     */
    public String getPassword() {
        return password;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.password
     *
     * @param password the value for t_client_channel_account.password
     *
     * @mbggenerated
     */
    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.is_channel_connect
     *
     * @return the value of t_client_channel_account.is_channel_connect
     *
     * @mbggenerated
     */
    public Byte getIsChannelConnect() {
        return isChannelConnect;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.is_channel_connect
     *
     * @param isChannelConnect the value for t_client_channel_account.is_channel_connect
     *
     * @mbggenerated
     */
    public void setIsChannelConnect(Byte isChannelConnect) {
        this.isChannelConnect = isChannelConnect;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.is_valid
     *
     * @return the value of t_client_channel_account.is_valid
     *
     * @mbggenerated
     */
    public Byte getIsValid() {
        return isValid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.is_valid
     *
     * @param isValid the value for t_client_channel_account.is_valid
     *
     * @mbggenerated
     */
    public void setIsValid(Byte isValid) {
        this.isValid = isValid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.version
     *
     * @return the value of t_client_channel_account.version
     *
     * @mbggenerated
     */
    public Integer getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.version
     *
     * @param version the value for t_client_channel_account.version
     *
     * @mbggenerated
     */
    public void setVersion(Integer version) {
        this.version = version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.max_connect
     *
     * @return the value of t_client_channel_account.max_connect
     *
     * @mbggenerated
     */
    public Integer getMaxConnect() {
        return maxConnect;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.max_connect
     *
     * @param maxConnect the value for t_client_channel_account.max_connect
     *
     * @mbggenerated
     */
    public void setMaxConnect(Integer maxConnect) {
        this.maxConnect = maxConnect;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.write_limit
     *
     * @return the value of t_client_channel_account.write_limit
     *
     * @mbggenerated
     */
    public Integer getWriteLimit() {
        return writeLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.write_limit
     *
     * @param writeLimit the value for t_client_channel_account.write_limit
     *
     * @mbggenerated
     */
    public void setWriteLimit(Integer writeLimit) {
        this.writeLimit = writeLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.read_limit
     *
     * @return the value of t_client_channel_account.read_limit
     *
     * @mbggenerated
     */
    public Integer getReadLimit() {
        return readLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.read_limit
     *
     * @param readLimit the value for t_client_channel_account.read_limit
     *
     * @mbggenerated
     */
    public void setReadLimit(Integer readLimit) {
        this.readLimit = readLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.ip_list
     *
     * @return the value of t_client_channel_account.ip_list
     *
     * @mbggenerated
     */
    public String getIpList() {
        return ipList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.ip_list
     *
     * @param ipList the value for t_client_channel_account.ip_list
     *
     * @mbggenerated
     */
    public void setIpList(String ipList) {
        this.ipList = ipList == null ? null : ipList.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.is_ip_check
     *
     * @return the value of t_client_channel_account.is_ip_check
     *
     * @mbggenerated
     */
    public Byte getIsIpCheck() {
        return isIpCheck;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.is_ip_check
     *
     * @param isIpCheck the value for t_client_channel_account.is_ip_check
     *
     * @mbggenerated
     */
    public void setIsIpCheck(Byte isIpCheck) {
        this.isIpCheck = isIpCheck;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.group_name
     *
     * @return the value of t_client_channel_account.group_name
     *
     * @mbggenerated
     */
    public String getGroupName() {
        return groupName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.group_name
     *
     * @param groupName the value for t_client_channel_account.group_name
     *
     * @mbggenerated
     */
    public void setGroupName(String groupName) {
        this.groupName = groupName == null ? null : groupName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.idle_time_sec
     *
     * @return the value of t_client_channel_account.idle_time_sec
     *
     * @mbggenerated
     */
    public Integer getIdleTimeSec() {
        return idleTimeSec;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.idle_time_sec
     *
     * @param idleTimeSec the value for t_client_channel_account.idle_time_sec
     *
     * @mbggenerated
     */
    public void setIdleTimeSec(Integer idleTimeSec) {
        this.idleTimeSec = idleTimeSec;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.create_time
     *
     * @return the value of t_client_channel_account.create_time
     *
     * @mbggenerated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.create_time
     *
     * @param createTime the value for t_client_channel_account.create_time
     *
     * @mbggenerated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.creator
     *
     * @return the value of t_client_channel_account.creator
     *
     * @mbggenerated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.creator
     *
     * @param creator the value for t_client_channel_account.creator
     *
     * @mbggenerated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.update_time
     *
     * @return the value of t_client_channel_account.update_time
     *
     * @mbggenerated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.update_time
     *
     * @param updateTime the value for t_client_channel_account.update_time
     *
     * @mbggenerated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.updater
     *
     * @return the value of t_client_channel_account.updater
     *
     * @mbggenerated
     */
    public String getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.updater
     *
     * @param updater the value for t_client_channel_account.updater
     *
     * @mbggenerated
     */
    public void setUpdater(String updater) {
        this.updater = updater == null ? null : updater.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_client_channel_account.is_delete
     *
     * @return the value of t_client_channel_account.is_delete
     *
     * @mbggenerated
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_client_channel_account.is_delete
     *
     * @param isDelete the value for t_client_channel_account.is_delete
     *
     * @mbggenerated
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }
}