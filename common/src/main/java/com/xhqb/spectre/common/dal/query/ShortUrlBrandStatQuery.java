package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShortUrlBrandStatQuery implements Serializable {

    private static final long serialVersionUID = -1679168624349441362L;

    /**
     * 品牌 多个英文逗号,隔开
     */
    private String brands;

    /**
     * 不包含的品牌 多个英文逗号隔开
     */
    private String unContainsBrands;

    /**
     * 短链编码 多个英文逗号,隔开
     */
    private String shortCodes;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 分页参数
     * @ignore
     */
    private PageParameter pageParameter;
}
