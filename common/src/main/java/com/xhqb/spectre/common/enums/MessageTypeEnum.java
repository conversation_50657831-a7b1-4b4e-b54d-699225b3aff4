package com.xhqb.spectre.common.enums;

import java.util.Arrays;
import java.util.Objects;

public enum MessageTypeEnum {

    /**
     * 验证码
     */
    VERIFY("verify", "验证码"),

    /**
     * 催收, 普通还款通知
     */
    COLLECTOR("collector", "还款通知"),

    /**
     * 轻催, 逾期还款通知
     */
    LIGHT_COLLECTOR("light_collector", "轻催"),

    /**
     * 重催, 严重逾期还款通知
     */
    SEVERE_COLLECTOR("severe_collector", "重催"),

    /**
     * 营销
     */
    MARKET("market", "营销"),

    /**
     * 通知
     */
    NOTIFY("notify", "通知"),

    /**
     * 债转
     */
    DEBT_SWAP("debt_swap", "债转"),

    /**
     * UNKNOWN
     */
    UNKNOWN("unknown", "未知");


    private String messageType;

    private String description;

    /**
     * 重发0次
     */
    public static final Integer RESEND_ZERO = 0;
    /**
     * 重发1次
     */
    public static final Integer RESEND_ONE = 1;

    /**
     * 重发标识
     */
    public static final Integer RETRY = 1;

    public String getMessageType() {
        return messageType;
    }

    public String getDescription() {
        return description;
    }


    /**
     * contructor.
     *
     * @param messageType
     */
    MessageTypeEnum(String messageType, String description) {
        this.messageType = messageType;
        this.description = description;
    }


    /**
     * 通过枚举<code>providerName</code>获得枚举
     *
     * @param messageType
     * @return
     */
    public static MessageTypeEnum getByMessageType(String messageType) {
        for (MessageTypeEnum resultValue : values()) {
            if (resultValue.getMessageType().equals(messageType)) {
                return resultValue;
            }
        }
        return UNKNOWN;
    }

    /**
     * 判断短信类型是否合法
     *
     * @param smsTypeCode
     * @return
     */
    public static boolean contains(String smsTypeCode) {
        return Arrays.stream(MessageTypeEnum.values()).anyMatch(item -> item.getMessageType().equals(smsTypeCode));
    }


    /**
     * 当前类型是否是营销
     *
     * @param smsTypeCode
     * @return
     */
    public static boolean isMarket(String smsTypeCode) {
        MessageTypeEnum targetType = getByMessageType(smsTypeCode);
        return Objects.equals(MessageTypeEnum.MARKET, targetType);
    }
}
