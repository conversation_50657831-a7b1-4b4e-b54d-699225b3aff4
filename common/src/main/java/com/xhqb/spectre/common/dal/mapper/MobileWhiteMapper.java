package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.MobileWhiteDO;
import com.xhqb.spectre.common.dal.query.MobileWhiteQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MobileWhiteMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_mobile_white
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_mobile_white
     *
     * @mbggenerated
     */
    int insert(MobileWhiteDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_mobile_white
     *
     * @mbggenerated
     */
    int insertSelective(MobileWhiteDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_mobile_white
     *
     * @mbggenerated
     */
    MobileWhiteDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_mobile_white
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(MobileWhiteDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_mobile_white
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(MobileWhiteDO record);

    MobileWhiteDO selectOne(MobileWhiteDO record);

    List<MobileWhiteDO> loadAllWhiteInfo();

    List<MobileWhiteDO> selectList(String appCode);

    Integer countByQuery(MobileWhiteQuery mobileWhiteQuery);

    List<MobileWhiteDO> selectByQuery(MobileWhiteQuery mobileWhiteQuery);

    void delete(@Param("id") Integer id, @Param("operator") String operator);

    void deleteByIdList(@Param("idList") List<Integer> idList, @Param("operator") String operator);

    void deleteByAppCode(@Param("appCode") String appCode, @Param("operator") String operator);
}