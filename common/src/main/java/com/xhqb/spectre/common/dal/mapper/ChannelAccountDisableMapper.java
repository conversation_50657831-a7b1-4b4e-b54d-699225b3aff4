package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ChannelAccountDisableDO;
import com.xhqb.spectre.common.dal.query.ChannelAccountDisableQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ChannelAccountDisableMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account_disable
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account_disable
     *
     * @mbggenerated
     */
    int insert(ChannelAccountDisableDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account_disable
     *
     * @mbggenerated
     */
    int insertSelective(ChannelAccountDisableDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account_disable
     *
     * @mbggenerated
     */
    ChannelAccountDisableDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account_disable
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(ChannelAccountDisableDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account_disable
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(ChannelAccountDisableDO record);

    Integer countByQuery(ChannelAccountDisableQuery accountDisableQuery);

    List<ChannelAccountDisableDO> selectByQuery(ChannelAccountDisableQuery accountDisableQuery);

    List<ChannelAccountDisableDO> selectByAccountId(Integer channelAccountId);

    void delete(@Param("id") Integer id, @Param("operator") String operator);

    List<ChannelAccountDisableDO> selectAll();
}