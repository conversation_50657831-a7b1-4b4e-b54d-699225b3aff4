package com.xhqb.spectre.common.strategy.impl;

import com.xhqb.spectre.common.constant.GenericConstants;
import com.xhqb.spectre.common.dal.entity.FailResendRuleDO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dto.FailResendSceneContextDTO;
import com.xhqb.spectre.common.strategy.FailResendSceneStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 运营商场景策略实现
 * scene_type = 0
 */
@Slf4j
public class CarrierSceneStrategy implements FailResendSceneStrategy {

    public static final String SCENE_TYPE_CARRIER = "0";
    
    @Override
    public String getSceneType() {
        return SCENE_TYPE_CARRIER;
    }
    
    @Override
    public boolean isRuleMatched(FailResendRuleDO rule, FailResendSceneContextDTO context) {

        if (!SCENE_TYPE_CARRIER.equals(rule.getSceneType())) {
            return false;
        }
        SmsOrderDO smsOrderDO = context.getSmsOrderDO();
        String sceneValue = rule.getSceneValue();
        String carrierCode = smsOrderDO.getIspCode();
        
        if (StringUtils.isBlank(sceneValue)) {
            log.debug("运营配置值为空, 通过: sceneValue={}, carrierCode={}", sceneValue, carrierCode);
            return true;
        }

        if (StringUtils.isBlank(carrierCode)) {
            log.debug("用户运营商为空, 不通过: sceneValue={}, carrierCode={}", sceneValue, carrierCode);
            return false;
        }

        Set<String> carrierCodes = Arrays.stream(sceneValue.split(GenericConstants.COMMA))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        
        boolean matched = carrierCodes.contains(carrierCode);
        
        log.debug("运营商场景匹配结果: ruleId={}, sceneValue={}, carrierCode={}, matched={}", 
                rule.getRuleId(), sceneValue, carrierCode, matched);
        
        return matched;
    }
}
