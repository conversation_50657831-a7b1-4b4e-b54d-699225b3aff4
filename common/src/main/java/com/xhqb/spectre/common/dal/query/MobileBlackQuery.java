package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/26 14:18
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MobileBlackQuery implements Serializable {

    private static final long serialVersionUID = -1679168624349441362L;

    /**
     * 用户CID
     */
    private String cid;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 来源
     */
    private String source;

    /**
     * 应用编码
     */
    private String appCode;

    private PageParameter pageParameter;
}
