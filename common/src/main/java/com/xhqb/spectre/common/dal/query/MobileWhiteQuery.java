package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 17:14
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MobileWhiteQuery implements Serializable {

    private static final long serialVersionUID = 1353383888774662488L;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 限流规则类型
     */
    private String cfgType;

    /**
     * 手机号
     */
    private String mobile;

    private PageParameter pageParameter;
}
