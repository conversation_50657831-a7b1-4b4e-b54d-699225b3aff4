package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/22 15:47
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelAccountDisableQuery implements Serializable {

    private static final long serialVersionUID = -5405094893782453827L;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道账号名称
     */
    private String channelAccountName;

    /**
     * 渠道账号状态，0：无效，1：有效
     */
    private Integer channelAccountStatus;

    /**
     * 分页参数
     */
    private PageParameter pageParameter;
}
