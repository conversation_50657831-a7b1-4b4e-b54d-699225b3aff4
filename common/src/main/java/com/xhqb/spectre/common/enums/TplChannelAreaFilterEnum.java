package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum TplChannelAreaFilterEnum {

    CONTAINS(1, "包含"),
    NOT_CONTAINS(2, "不包含");

    private final Integer type;
    private final String description;

    /**
     * 检查发送类型是否合法
     *
     * @param type
     * @return
     */
    public static boolean checkFilterType(Integer type) {
        return Arrays.stream(SendTypeEnum.values()).anyMatch(s -> Objects.equals(s.getType(), type));
    }

}
