package com.xhqb.spectre.common.dal.entity;

import lombok.Data;

import java.util.Date;

@Data
public class BrandMappingDO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 类型（0：设备型号 1：浏览器）
     */
    private Integer type;

    /**
     * 编码（设备型号、浏览器 code）
     */
    private String code;

    /**
     * 名称（设备型号、浏览器 name）
     */
    private String name;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
