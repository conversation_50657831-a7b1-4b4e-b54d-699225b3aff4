package com.xhqb.spectre.common.dal.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * dal基础信息类
 * @author: xiaoxiaoxiang
 * @date: 2018/11/15 14:50
 */
@ToString
@Getter
@Setter
public abstract class BaseEntity {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 数据状态,有效-enable,无效-disable
     */
    private String status;

    /**
     * 数据版本号
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 获取生成主键的key
     *
     * @return
     */
    public abstract String getSeqName();
}

