package com.xhqb.spectre.common.dal.entity.oa;

import lombok.Data;

import java.util.Date;

@Data
public class TplApproveContent {

    /**
     * 主键
     */
    private Long id;

    /**
     *  流程 ID
     */
    private String flowId;

    /**
     * 内容 ID
     */
    private String contentId;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 签名 id
     */
    private String signId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
