package com.xhqb.spectre.common.dal.entity;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
public class DebtSmsRetryDO {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_retry.id
     *
     * @mbggenerated
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_retry.tpl_code
     *
     * @mbggenerated
     */
    private String tplCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_retry.order_id
     *
     * @mbggenerated
     */
    private Long orderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_retry.status
     *
     * @mbggenerated
     */
    private Byte status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_retry.retry_times
     *
     * @mbggenerated
     */
    private Integer retryTimes;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_retry.start_time
     *
     * @mbggenerated
     */
    private Integer startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_retry.remark
     *
     * @mbggenerated
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_retry.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_retry.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_retry.is_delete
     *
     * @mbggenerated
     */
    private Byte isDelete;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_retry.message_json
     *
     * @mbggenerated
     */
    private String messageJson;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_retry.id
     *
     * @return the value of t_debt_sms_retry.id
     * @mbggenerated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_retry.id
     *
     * @param id the value for t_debt_sms_retry.id
     * @mbggenerated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_retry.tpl_code
     *
     * @return the value of t_debt_sms_retry.tpl_code
     * @mbggenerated
     */
    public String getTplCode() {
        return tplCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_retry.tpl_code
     *
     * @param tplCode the value for t_debt_sms_retry.tpl_code
     * @mbggenerated
     */
    public void setTplCode(String tplCode) {
        this.tplCode = tplCode == null ? null : tplCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_retry.order_id
     *
     * @return the value of t_debt_sms_retry.order_id
     * @mbggenerated
     */
    public Long getOrderId() {
        return orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_retry.order_id
     *
     * @param orderId the value for t_debt_sms_retry.order_id
     * @mbggenerated
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_retry.status
     *
     * @return the value of t_debt_sms_retry.status
     * @mbggenerated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_retry.status
     *
     * @param status the value for t_debt_sms_retry.status
     * @mbggenerated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_retry.retry_times
     *
     * @return the value of t_debt_sms_retry.retry_times
     * @mbggenerated
     */
    public Integer getRetryTimes() {
        return retryTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_retry.retry_times
     *
     * @param retryTimes the value for t_debt_sms_retry.retry_times
     * @mbggenerated
     */
    public void setRetryTimes(Integer retryTimes) {
        this.retryTimes = retryTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_retry.start_time
     *
     * @return the value of t_debt_sms_retry.start_time
     * @mbggenerated
     */
    public Integer getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_retry.start_time
     *
     * @param startTime the value for t_debt_sms_retry.start_time
     * @mbggenerated
     */
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_retry.remark
     *
     * @return the value of t_debt_sms_retry.remark
     * @mbggenerated
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_retry.remark
     *
     * @param remark the value for t_debt_sms_retry.remark
     * @mbggenerated
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_retry.create_time
     *
     * @return the value of t_debt_sms_retry.create_time
     * @mbggenerated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_retry.create_time
     *
     * @param createTime the value for t_debt_sms_retry.create_time
     * @mbggenerated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_retry.update_time
     *
     * @return the value of t_debt_sms_retry.update_time
     * @mbggenerated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_retry.update_time
     *
     * @param updateTime the value for t_debt_sms_retry.update_time
     * @mbggenerated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_retry.is_delete
     *
     * @return the value of t_debt_sms_retry.is_delete
     * @mbggenerated
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_retry.is_delete
     *
     * @param isDelete the value for t_debt_sms_retry.is_delete
     * @mbggenerated
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_retry.message_json
     *
     * @return the value of t_debt_sms_retry.message_json
     * @mbggenerated
     */
    public String getMessageJson() {
        return messageJson;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_retry.message_json
     *
     * @param messageJson the value for t_debt_sms_retry.message_json
     * @mbggenerated
     */
    public void setMessageJson(String messageJson) {
        this.messageJson = messageJson == null ? null : messageJson.trim();
    }
}