package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.DebtSmsReportDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
public interface DebtSmsReportMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_debt_sms_report
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_debt_sms_report
     *
     * @mbggenerated
     */
    int insert(DebtSmsReportDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_debt_sms_report
     *
     * @mbggenerated
     */
    int insertSelective(DebtSmsReportDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_debt_sms_report
     *
     * @mbggenerated
     */
    DebtSmsReportDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_debt_sms_report
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(DebtSmsReportDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_debt_sms_report
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(DebtSmsReportDO record);

    /**
     * 债转短信报表任务
     *
     * @return
     */
    List<DebtSmsReportDO> selectDebtSmsReportJob(@Param("beginStartTime") Integer beginStartTime, @Param("endStartTime") Integer endStartTime, @Param("pageSize") Integer pageSize, @Param("lastId") Long lastId);
}