package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ZbxShortUrlDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface ZbxShortUrlMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url
     *
     * @mbggenerated
     */
    int insert(ZbxShortUrlDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url
     *
     * @mbggenerated
     */
    int insertSelective(ZbxShortUrlDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url
     *
     * @mbggenerated
     */
    ZbxShortUrlDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(ZbxShortUrlDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(ZbxShortUrlDO record);

    void enable(@Param("id") Integer id, @Param("operator") String operator);

    void disable(@Param("id") Integer id, @Param("operator") String operator);

    void delete(@Param("id") Integer id, @Param("operator") String operator);

    /**
     * 查询所有有效并且未删除的数据
     *
     * @return
     */
    List<ZbxShortUrlDO> selectAll();

    /**
     * 查询所有大于updateTime的数据
     *
     * @param updateTime
     * @return
     */
    List<ZbxShortUrlDO> selectALlByUpdateTime(Date updateTime);

    /**
     * 根据短链编码查询数据
     *
     * @param code 短链编码
     * @return
     */
    ZbxShortUrlDO selectByCode(@Param("code") String code);

    /**
     * 只查询后台管理创建的数据
     */
    List<ZbxShortUrlDO> listByType();

    /**
     * 查询最后更新的为后台类型的短链数据
     */
    ZbxShortUrlDO listByUpdateAndType();

    /**
     * 更新点击次数
     *
     * @param shortCode     短信短链编码
     * @param newClickCount 新增的点击次数
     * @return
     */
    int updateClickCount(@Param("shortCode") String shortCode, @Param("newClickCount") Integer newClickCount);
}