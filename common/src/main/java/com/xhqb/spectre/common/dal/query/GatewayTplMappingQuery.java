package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/26 17:23
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GatewayTplMappingQuery implements Serializable {

    private static final long serialVersionUID = -1258881080972032155L;

    /**
     * 模板ID
     */
    private Integer tplId;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 模板文案
     */
    private String tplContent;

    private PageParameter pageParameter;
}
