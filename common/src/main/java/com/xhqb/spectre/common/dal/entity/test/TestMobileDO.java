package com.xhqb.spectre.common.dal.entity.test;

import lombok.Data;

import java.util.Date;

/**
 * 测试库
 */
@Data
public class TestMobileDO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 手机号类型
     */
    private Integer type;

    /**
     * 删除标识 0:未删除 1:删除
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer status;

    /**
     * 检测时间
     */
    private Date  detectTime;

    /**
     *  失效时间
     */
    private Date  expiredTime;

    /**
     * 基础变量值json:{"name":"阳先生","amount":200}
     */
    private String param;
}

