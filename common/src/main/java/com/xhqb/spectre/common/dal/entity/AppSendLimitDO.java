package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppSendLimitDO implements Serializable {

    private static final long serialVersionUID = 4238716231895318426L;
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_app_send_limit.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_app_send_limit.app_code
     *
     * @mbggenerated
     */
    private String appCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_app_send_limit.send_limit_key
     *
     * @mbggenerated
     */
    private String sendLimitKey;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_app_send_limit.send_limit_value
     *
     * @mbggenerated
     */
    private String sendLimitValue;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_app_send_limit.status
     *
     * @mbggenerated
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_app_send_limit.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_app_send_limit.creator
     *
     * @mbggenerated
     */
    private String creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_app_send_limit.updater
     *
     * @mbggenerated
     */
    private String updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_app_send_limit.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_app_send_limit.is_delete
     *
     * @mbggenerated
     */
    private Byte isDelete;
}