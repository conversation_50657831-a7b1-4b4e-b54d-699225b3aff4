package com.xhqb.spectre.common.utils;

import java.time.LocalDate;
import java.util.*;

public class ShardingUtils {
    private static final String LOGIC_TABLE_NAME = "t_receipt";

    private ShardingUtils() {
    }
    public static String getTableName(LocalDate nowDate) {
        int year = nowDate.getYear();
        int month = nowDate.getMonthValue();
        return LOGIC_TABLE_NAME + "_"+ year + (month < 10 ? "0" + month : month);
    }

    public static String getTableName() {
        return getTableName(LocalDate.now());
    }

    /**
     * 按月分表
     *
     * @param  tableName
     * @param time
     * @return
     */
    public static String getTableNameByTime(String tableName, long time) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(time);
        int year = c.get(Calendar.YEAR);
        int month = c.get(Calendar.MONTH) + 1;
        return tableName + "_" + year + (month < 10 ? "0" + month : month);
    }

    public static List<String> getTableNameByTimeRange(String tableName, Long startTime, Long endTime) {
        if (endTime == null || Long.valueOf(0).equals(endTime)) {
            return Collections.singletonList(getTableNameByTime(tableName, startTime));
        }
        Date startMonthBegin =  DateUtil.getMonthBegin(new Date(startTime));
        Date endMonthBegin = DateUtil.getMonthBegin(new Date(endTime));
        List<String> result = new ArrayList<>(32);
        while (startMonthBegin.compareTo(endMonthBegin) <= 0) {
            result.add(getTableNameByTime(tableName, startMonthBegin.getTime()));
            startMonthBegin = DateUtil.getMonthBegin(startMonthBegin, 1);
        }
        return result;

    }
}
