package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.OpTimeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OpTimeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_op_time
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_op_time
     *
     * @mbggenerated
     */
    int insert(OpTimeDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_op_time
     *
     * @mbggenerated
     */
    int insertSelective(OpTimeDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_op_time
     *
     * @mbggenerated
     */
    OpTimeDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_op_time
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(OpTimeDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_op_time
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(OpTimeDO record);

    OpTimeDO selectByModule(Integer module);

    void updateOpTime(@Param("module") Integer module, @Param("opTime") Integer opTime);

    List<OpTimeDO> selectAll();
}