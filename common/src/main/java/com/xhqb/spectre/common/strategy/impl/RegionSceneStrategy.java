package com.xhqb.spectre.common.strategy.impl;

import com.xhqb.spectre.common.dal.entity.FailResendRuleDO;
import com.xhqb.spectre.common.dto.FailResendSceneContextDTO;
import com.xhqb.spectre.common.strategy.FailResendSceneStrategy;
import lombok.extern.slf4j.Slf4j;

/**
 * 地区场景策略实现
 * scene_type = 2
 */
@Slf4j
public class RegionSceneStrategy implements FailResendSceneStrategy {

    /**
     * 地区场景类型
     */
    public static final String SCENE_TYPE_REGION = "2";

    @Override
    public String getSceneType() {
        return SCENE_TYPE_REGION;
    }

    @Override
    public boolean isRuleMatched(FailResendRuleDO rule, FailResendSceneContextDTO context) {
        // 检查场景类型是否匹配
        if (!SCENE_TYPE_REGION.equals(rule.getSceneType())) {
            return false;
        }
        return false;
    }

}
