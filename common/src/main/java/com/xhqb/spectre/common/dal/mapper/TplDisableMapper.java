package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.TplDisableDO;

import java.util.List;

public interface TplDisableMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl_disable_info
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl_disable_info
     *
     * @mbggenerated
     */
    int insert(TplDisableDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl_disable_info
     *
     * @mbggenerated
     */
    int insertSelective(TplDisableDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl_disable_info
     *
     * @mbggenerated
     */
    TplDisableDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl_disable_info
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(TplDisableDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl_disable_info
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(TplDisableDO record);

    void deleteByTplId(Integer tplId);

    List<TplDisableDO>  selectByTplId(Integer tplId);
}