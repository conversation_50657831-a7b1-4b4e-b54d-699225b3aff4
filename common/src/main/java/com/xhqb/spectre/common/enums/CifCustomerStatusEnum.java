package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * CIF 用户状态枚举
 *
 * <AUTHOR>
 * @date 2021/9/27
 */
@Getter
@AllArgsConstructor
public enum CifCustomerStatusEnum {
    /**
     * 注册开户
     */
    OPEN("OPEN", "已开户"),
    /**
     * 客户激活
     */
    ACTIVE("ACTIVE", "已激活"),
    /**
     * 客户激活
     */
    FROZEN("FROZEN", "冻结"),
    /**
     * 客户销户
     */
    CLOSE("CLOSE", "关户");

    private String code;
    private String description;

    /**
     * 状态是否有效哦
     *
     * @param status
     * @return
     */
    public static boolean isValid(String status) {
        CifCustomerStatusEnum item = getStatus(status);
        if (Objects.isNull(item)) {
            return false;
        }

        return StringUtils.equalsAnyIgnoreCase(status, OPEN.code, ACTIVE.code);
    }

    /**
     * 获取到用户状态
     *
     * @param status
     * @return
     */
    public static CifCustomerStatusEnum getStatus(String status) {
        for (CifCustomerStatusEnum item : values()) {
            if (StringUtils.equals(item.getCode(), status)) {
                return item;
            }
        }
        return null;
    }

}