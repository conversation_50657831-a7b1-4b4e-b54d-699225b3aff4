package com.xhqb.spectre.common.utils;


import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SmsContentUtil {

    public static class SplitResult {
        // 签名
        private final String signName;
        // 内容
        private final String content;

        public SplitResult(String signName, String content) {
            this.signName = signName;
            this.content = content;
        }

        public String getSignName() {
            return signName;
        }

        public String getContent() {
            return content;
        }
    }

    public static SplitResult split(String input) {
        if (input == null || input.isEmpty()) {
            return new SplitResult("", "");
        }


        Pattern pattern = Pattern.compile("^(【.*?】)(.*)$", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            String signName = matcher.group(1);
            String content = matcher.group(2).trim();
            return new SplitResult(signName, content);
        } else {
            return new SplitResult("", input.trim());
        }
    }
}
