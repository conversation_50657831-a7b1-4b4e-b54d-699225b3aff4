package com.xhqb.spectre.common.dal.entity;

import lombok.Data;

import java.util.Date;

/**
 * 自动测试任务 DO
 *
 * <AUTHOR>
 * @date 2025-07-09 11:23:28
 */
@Data
public class AutoTestTaskDO {

    /**
     * id
     */
    private Long id;
    /**
     * 名称
     */
    private String name;

    /**
     * 任务类型，0:TOP模板，1:自定义模板
     */
    private String type;
    /**
     * 绑定的模板，当type = 1 时，value格式：10（1-100的数字）；当type = 2 时，value格式：tplName1,tolName2（英文逗号分割）
     */
    private String bindTpl;
    /**
     * 测试频率，取值：1天1次、2天1次、3天1次
     */
    private String testFrequency;
    /**
     * 测试周期，例如：2025-07-09,2025-07-09（英文逗号分割）
     */
    private String testCycle;
    /**
     * 仅工作日，0：自然日；1：工作日
     */
    private Integer workDay;
    /**
     * 是否启用，0：禁用；1：启用
     */
    private Integer enable;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否删除，0：未删除；1：已删除
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 更新人
     */
    private String updater;


    //*** 非数据库字段 ***//

    /**
     * 执行次数
     */
    private Integer invokeCount;
}
