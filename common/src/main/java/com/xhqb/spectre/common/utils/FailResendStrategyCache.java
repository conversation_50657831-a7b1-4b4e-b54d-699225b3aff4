package com.xhqb.spectre.common.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.common.constant.GenericConstants;
import com.xhqb.spectre.common.dal.entity.FailResendRuleDO;
import com.xhqb.spectre.common.dal.entity.FailResendStrategyDO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dto.FailResendMatchResultDTO;
import com.xhqb.spectre.common.dto.FailResendRuleConfigDTO;
import com.xhqb.spectre.common.dto.FailResendSceneContextDTO;
import com.xhqb.spectre.common.strategy.FailResendSceneStrategy;
import com.xhqb.spectre.common.strategy.FailResendSceneStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 补发策略缓存工具类
 * 实现策略数据的缓存和匹配逻辑
 */
@Slf4j
public class FailResendStrategyCache {
    
    /**
     * 策略缓存 key: tplCode, value: 策略列表
     */
    private static final Map<String, List<FailResendStrategyDO>> STRATEGY_CACHE = new ConcurrentHashMap<>();
    
    /**
     * 规则缓存 key: strategyId, value: 规则列表
     */
    private static final Map<String, List<FailResendRuleDO>> RULE_CACHE = new ConcurrentHashMap();


    private static FailResendSceneStrategyFactory strategyFactory;
    

    public static final String RULE_TYPE_SINGLE = "0";    // 单模板发送
    public static final String RULE_TYPE_MULTIPLE = "1";  // 多模板分流发送

    public static void initCache(List<FailResendStrategyDO> strategies, List<FailResendRuleDO> rules,
                                FailResendSceneStrategyFactory factory) {
        log.info("开始初始化补发策略缓存, 策略数量: {}, 规则数量: {}", strategies.size(), rules.size());

        strategyFactory = factory;

        STRATEGY_CACHE.clear();
        RULE_CACHE.clear();

        for (FailResendStrategyDO strategy : strategies) {
            if (StringUtils.isNotBlank(strategy.getOriginalTplCodes())) {
                String[] tplCodes = strategy.getOriginalTplCodes().split(GenericConstants.COMMA);
                for (String tplCode : tplCodes) {
                    tplCode = tplCode.trim();
                    STRATEGY_CACHE.computeIfAbsent(tplCode, k -> new ArrayList<>()).add(strategy);
                }
            }
        }

        for (FailResendRuleDO rule : rules) {
            RULE_CACHE.computeIfAbsent(rule.getStrategyId(), k -> new ArrayList<>()).add(rule);
        }

        log.info("补发策略缓存初始化完成, {}", getCacheStats());
    }

    /**
     * 模板是否包含在补发策略
     * @param tplCode
     * @return
     */
    public static boolean isTplInStrategy(String tplCode) {
        List<FailResendStrategyDO> strategies = STRATEGY_CACHE.get(tplCode);
        if (strategies == null || strategies.isEmpty()) {
            log.debug("模板不在策略配置, tplCode={}", tplCode);
            return false;
        }
        return true;
    }

    /**
     * 根据模板编码和场景信息匹配补发策略
     * @return 匹配结果
     */
    public static FailResendMatchResultDTO matchStrategy(SmsOrderDO smsOrderDO) {
        log.debug("开始匹配补发策略, smsOrderDO: {}", smsOrderDO);

        if (strategyFactory == null) {
            log.error("策略工厂未初始化，无法进行策略匹配");
            return FailResendMatchResultDTO.builder().matched(false).build();
        }

        List<FailResendStrategyDO> strategies = STRATEGY_CACHE.get(smsOrderDO.getTplCode());
        if (strategies == null || strategies.isEmpty()) {
            log.debug("未找到模板 {} 对应的补发策略", smsOrderDO.getTplCode());
            return FailResendMatchResultDTO.builder().matched(false).build();
        }

        FailResendSceneContextDTO context = FailResendSceneContextDTO.builder()
                .smsOrderDO(smsOrderDO)
                .build();

        for (FailResendStrategyDO strategy : strategies) {
            if (!isInTimePeriod(strategy.getTimePeriod())) {
                log.debug("策略 {} 不在有效时间段内，跳过", strategy.getStrategyId());
                continue;
            }
            
            List<FailResendRuleDO> rules = RULE_CACHE.get(strategy.getStrategyId());
            if (rules == null || rules.isEmpty()) {
                continue;
            }

            for (FailResendRuleDO rule : rules) {
                if (isRuleMatchedWithStrategy(rule, context)) {

                    FailResendRuleConfigDTO config = selectRuleConfig(rule);
                    if (config != null) {
                        log.debug("匹配到补发策略: strategyId={}, ruleId={}, sceneType={}, tplCode={}, signCode={}",
                                strategy.getStrategyId(), rule.getRuleId(), rule.getSceneType(),
                                config.getTplCode(), config.getSignCode());

                        return FailResendMatchResultDTO.builder()
                                .matched(true)
                                .strategyId(strategy.getStrategyId())
                                .ruleId(rule.getRuleId())
                                .tplCode(config.getTplCode())
                                .signCode(config.getSignCode())
                                .build();
                    }
                }
            }
        }

        log.debug("未找到匹配的补发规则, tplCode: {}", smsOrderDO.getTplCode());
        return FailResendMatchResultDTO.builder().matched(false).build();
    }
    
    /**
     * 是否在时间段内
     * @param timePeriod 格式为 "09:12:21-19:21:10"
     * @return 是否在时间段内
     */
    private static boolean isInTimePeriod(String timePeriod) {
        if (StringUtils.isBlank(timePeriod)) {
            // 如果没有设置时间段，默认返回true，表示不限制时间
            return true;
        }
        
        try {
            String[] timeParts = timePeriod.split("-");
            if (timeParts.length != 2) {
                log.warn("时间段格式不正确: {}", timePeriod);
                return false;
            }
            
            LocalTime startTime = LocalTime.parse(timeParts[0], DateTimeFormatter.ofPattern("HH:mm:ss"));
            LocalTime endTime = LocalTime.parse(timeParts[1], DateTimeFormatter.ofPattern("HH:mm:ss"));
            LocalTime now = LocalTime.now();

            if (startTime.isBefore(endTime)) {
                return !now.isBefore(startTime) && now.isBefore(endTime);
            } else {
                return !now.isBefore(startTime) || now.isBefore(endTime);
            }
        } catch (DateTimeParseException e) {
            log.warn("解析时间段失败: {}", timePeriod, e);
            return true;
        }
    }
    
    /**
     * 使用策略模式检查规则是否匹配
     * @param rule 补发规则
     * @param context 场景上下文
     * @return 是否匹配
     */
    private static boolean isRuleMatchedWithStrategy(FailResendRuleDO rule, FailResendSceneContextDTO context) {
        String sceneType = rule.getSceneType();

        FailResendSceneStrategy strategy = strategyFactory.getStrategy(sceneType);
        if (strategy == null) {
            log.warn("未找到场景类型对应的策略实现: sceneType={}, ruleId={}", sceneType, rule.getRuleId());
            return false;
        }

        return strategy.isRuleMatched(rule, context);
    }
    
    /**
     * 根据规则类型选择配置
     */
    private static FailResendRuleConfigDTO selectRuleConfig(FailResendRuleDO rule) {
        String ruleValue = rule.getRuleValue();
        if (StringUtils.isBlank(ruleValue)) {
            return null;
        }
        
        try {
            JSONArray configArray = JSONArray.parseArray(ruleValue);
            if (configArray == null || configArray.isEmpty()) {
                return null;
            }
            
            List<FailResendRuleConfigDTO> configs = new ArrayList<>();
            for (int i = 0; i < configArray.size(); i++) {
                JSONObject configJson = configArray.getJSONObject(i);
                FailResendRuleConfigDTO config = FailResendRuleConfigDTO.builder()
                        .tplCode(configJson.getString("tpl_code"))
                        .signCode(configJson.getString("sign_code"))
                        .weight(configJson.getInteger("weight"))
                        .build();
                configs.add(config);
            }
            
            // 根据规则类型选择
            if (RULE_TYPE_SINGLE.equals(rule.getRuleType())) {
                // 单模板返回第一个
                return configs.get(0);
            } else if (RULE_TYPE_MULTIPLE.equals(rule.getRuleType())) {
                // 多模板分流发送，根据权重随机选择
                return selectByWeight(configs);
            }
            
        } catch (Exception e) {
            log.error("解析规则配置失败, ruleId: {}, ruleValue: {}", rule.getRuleId(), ruleValue, e);
        }
        
        return null;
    }
    
    /**
     * 根据权重选择规则
     */
    private static FailResendRuleConfigDTO selectByWeight(List<FailResendRuleConfigDTO> configs) {
        if (configs == null || configs.isEmpty()) {
            return null;
        }
        
        if (configs.size() == 1) {
            return configs.get(0);
        }
        
        // 计算总权重
        int totalWeight = configs.stream()
                .mapToInt(config -> config.getWeight() != null ? config.getWeight() : 0)
                .sum();
        
        if (totalWeight <= 0) {
            // 如果总权重为0，随机选择一个
            return configs.get(new Random().nextInt(configs.size()));
        }
        
        // 根据权重随机选择
        Random random = new Random();
        int randomWeight = random.nextInt(totalWeight);
        int currentWeight = 0;
        
        for (FailResendRuleConfigDTO config : configs) {
            currentWeight += (config.getWeight() != null ? config.getWeight() : 0);
            if (randomWeight < currentWeight) {
                return config;
            }
        }

        return configs.get(configs.size() - 1);
    }
    
    /**
     * 清空缓存
     */
    public static void clearCache() {
        STRATEGY_CACHE.clear();
        RULE_CACHE.clear();
        log.info("补发策略缓存已清空");
    }
    
    /**
     * 获取策略缓存大小
     */
    public static int getStrategyCacheSize() {
        return STRATEGY_CACHE.size();
    }
    
    /**
     * 获取规则缓存大小
     */
    public static int getRuleCacheSize() {
        return RULE_CACHE.size();
    }
    
    /**
     * 获取缓存统计信息，包含策略ID及该策略下的规则ID列表
     */
    public static String getCacheStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("策略缓存大小: ").append(STRATEGY_CACHE.size())
             .append(", 规则缓存大小: ").append(RULE_CACHE.size())
             .append("\n策略详情:\n");
        
        for (Map.Entry<String, List<FailResendStrategyDO>> entry : STRATEGY_CACHE.entrySet()) {
            String tplCode = entry.getKey();
            List<FailResendStrategyDO> strategies = entry.getValue();
            
            stats.append("  模板[").append(tplCode).append("]: ");
            
            String strategyInfo = strategies.stream()
                .map(strategy -> {
                    String strategyId = strategy.getStrategyId();
                    List<FailResendRuleDO> rules = RULE_CACHE.get(strategyId);
                    String ruleIds = (rules != null) ? 
                        rules.stream().map(FailResendRuleDO::getRuleId).collect(Collectors.joining(",")) : 
                        "";
                    return strategyId + "[" + ruleIds + "]";
                })
                .collect(Collectors.joining(", "));
                
            stats.append(strategyInfo).append("\n");
        }
        
        return stats.toString();
    }
}