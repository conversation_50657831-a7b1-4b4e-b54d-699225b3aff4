package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.oa.TplContent;
import com.xhqb.spectre.common.dal.query.OaReportQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TplContentMapper {

    List<TplContent> selectByContentIdList(@Param("list") List<String> contentIdList);

    int insertSelective(TplContent record);

    int updateByContentId(@Param("contentId") String contentId,
                          @Param("content") String content,
                          @Param("status") Integer status);

    List<TplContent> selectByQuery(@Param("query") OaReportQuery oaReportQuery);

    Integer countByQuery(@Param("query")OaReportQuery oaReportQuery);

    Integer updateByPrimaryKeySelective(TplContent tplContent);

    Integer updateDeleteTagByContentId(@Param("contentId") String contentId,
                                       @Param("deleteTag") int deleteTag);

    List<TplContent> selectByStatus(@Param("status") int status,
                                    @Param("startDate") Date start,
                                    @Param("endDate") Date end);

    /**
     * 根据模板编号批量查询
     * @param tplCodeList
     * @return
     */
    List<TplContent> selectByTplCodeList(@Param("list") List<String> tplCodeList);

    /**
     * 根据创建者获取模板编码列表
     * @param creator 创建者
     * @return 模板编码列表
     */
    List<String> selectTplCodesByCreator(@Param("creator") String creator);

    TplContent selectByContentId(String reportId);
}
