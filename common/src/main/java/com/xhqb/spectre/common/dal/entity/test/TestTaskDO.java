package com.xhqb.spectre.common.dal.entity.test;

import lombok.Data;

import java.util.Date;

/**
 * 测试任务
 */
@Data
public class TestTaskDO {

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 测试模版id
     */
    private Long tplId;

    /**
     * 任务状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
