package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.AreasInfoDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AreasInfoMapper {

    int deleteByPrimaryKey(String id);

    int insert(AreasInfoDO record);

    int insertSelective(AreasInfoDO record);

    AreasInfoDO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(AreasInfoDO record);

    int updateByPrimaryKey(AreasInfoDO record);

    List<AreasInfoDO> selectProvinceCity();
}