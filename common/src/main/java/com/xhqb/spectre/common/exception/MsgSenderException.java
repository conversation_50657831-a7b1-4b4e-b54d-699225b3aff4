package com.xhqb.spectre.common.exception;

import com.xhqb.spectre.common.enums.MsgSendResultEnum;

public class MsgSenderException extends RuntimeException{
    private MsgSendResultEnum code = MsgSendResultEnum.SYS_FAILURES;

    private String message = "";

    /**
     * 以结果码为参数构造异常
     *
     * @param code 结果码
     */
    public MsgSenderException(MsgSendResultEnum code) {
        super();
        this.code = code;
        this.message = code.getMessage();
    }

    /**
     * constructor
     * @param message
     */
    public MsgSenderException(String message) {        //用来创建指定参数对象
        this.message = message;
    }

    /**
     * 以结果码和msg为构造异常
     *
     * @param code 结果码
     * @param msg  消息
     */
    public MsgSenderException(MsgSendResultEnum code, String msg) {
        this.message = msg;
        this.code = code;
    }

    /**
     * 以结果码和Throwable为参数构造函数
     *
     * @param code 结果码
     * @param e    Throwable
     */
    public MsgSenderException(MsgSendResultEnum code, Throwable e) {
        super(e);
        this.code = code;
    }

    public MsgSendResultEnum getCode() {
        return code;
    }

    public void setCode(MsgSendResultEnum code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }


}
