package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ProjectDescDO;
import com.xhqb.spectre.common.dal.query.ProjectDescQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProjectDescMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int insert(ProjectDescDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int insertSelective(ProjectDescDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    ProjectDescDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(ProjectDescDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(ProjectDescDO record);

    List<ProjectDescDO> selectEnum(Integer status);

    Integer countByQuery(ProjectDescQuery projectDescQuery);

    List<ProjectDescDO> selectByQuery(ProjectDescQuery projectDescQuery);

    ProjectDescDO selectByDescription(String name);

    void enable(@Param("id") Integer id, @Param("operator") String operator);

    void disable(@Param("id") Integer id, @Param("operator") String operator);

    void delete(@Param("id") Integer id, @Param("operator") String operator);
}