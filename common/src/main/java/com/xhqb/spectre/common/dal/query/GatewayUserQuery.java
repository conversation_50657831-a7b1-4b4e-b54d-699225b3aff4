package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 网关账号管理(王建政)
 *
 * <AUTHOR>
 * @date 2021/11/8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GatewayUserQuery implements Serializable {

    private static final long serialVersionUID = 6247016157047126098L;

    /**
     * 服务代码
     */
    private String serviceId;

    /**
     * 账号
     */
    private String userName;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 分页参数
     */
    private PageParameter pageParameter;
}
