package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/29
 */
@AllArgsConstructor
@Getter

public enum DebtSmsRetryStatusEnum {

    NOT_FINISHED((byte) 0, "未完成"),
    FINISHED((byte) 1, "已完成"),
    LIMIT_REACHED((byte) 2, "重试次数已满");


    private byte status;
    private String desc;


    public static boolean isLimitReached(Byte status) {
        return Objects.equals(LIMIT_REACHED.getStatus(), status);
    }

    public static boolean isFinished(Byte status) {
        return Objects.equals(FINISHED.getStatus(), status);
    }
}
