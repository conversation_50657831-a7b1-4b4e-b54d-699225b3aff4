package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ChannelSmsTypePriceLogDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_channel_sms_type_price_log(渠道签名单价调整日志表)】的数据库操作Mapper
* @createDate 2025-07-11 17:19:08
* @Entity generator.entity.ChannelSmsTypePriceLog
*/
public interface ChannelSmsTypePriceLogMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ChannelSmsTypePriceLogDO record);

    int insertSelective(ChannelSmsTypePriceLogDO record);

    ChannelSmsTypePriceLogDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ChannelSmsTypePriceLogDO record);

    int updateByPrimaryKey(ChannelSmsTypePriceLogDO record);

    List<ChannelSmsTypePriceLogDO> getLatestPrices();

    List<ChannelSmsTypePriceLogDO> getLatestPricesByDefault();

    List<ChannelSmsTypePriceLogDO> getLatestPricesBySign();

    ChannelSmsTypePriceLogDO getLatestPriceByChannelAndType(@Param("channelCode") String channelCode,
                                                            @Param("smsTypeCode") String smsTypeCode);
}
