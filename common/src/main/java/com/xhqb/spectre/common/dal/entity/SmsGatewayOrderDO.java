package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class SmsGatewayOrderDO {
    private Long id;
    private String requestId;
    private String channelMsgId;
    private String channelAccountId;
    private String mobile;
    private String content;
    private Long sendStatus;
    private Long sendTime;
    private Long recvSubmitTime;
    private Long reportTime;
    private Long reportStatus;
    private String reportDesc;
    private Long submitTime;
    private Long doneTime;
}