package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 映射码
 *
 * <AUTHOR>
 * @date 2021/10/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CodeMappingQuery implements Serializable {

    /**
     * 渠道code
     */
    private String channelCode;
    /**
     * 渠道错误码编码
     */
    private String channelErrCode;
    /**
     * 错误码编码
     */
    private Integer xhErrCode;

    /**
     * 错误码类型 submit->短信发送 deliver->短信回执
     */
    private String type;
    /**
     * 分页参数
     */
    private PageParameter pageParameter;
}
