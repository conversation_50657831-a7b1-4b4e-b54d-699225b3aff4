package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.test.TestTplDO;
import com.xhqb.spectre.common.dal.query.ChannelTestTplQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TestTplMapper {
    Integer countByQuery(@Param("query") ChannelTestTplQuery channelTestTplQuery);

    List<TestTplDO> selectByQuery(@Param("query") ChannelTestTplQuery channelTestTplQuery);

    TestTplDO selectByChannelAccountIdAndSmsTplId(@Param("channelAccountId") Integer channelAccountId, @Param("smsTplId")Integer smsTplId);

    int insertBySelective(TestTplDO testTplDO);

    TestTplDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TestTplDO testTplDO);

    List<TestTplDO> selectByTplIdList(@Param("tplIdList") List<Long> tplIdList,@Param("source") Integer source);

    List<TestTplDO> selectAll();

    TestTplDO selectByTplId(Integer tplId);
}
