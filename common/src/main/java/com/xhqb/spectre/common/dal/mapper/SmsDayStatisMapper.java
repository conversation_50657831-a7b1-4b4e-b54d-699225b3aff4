package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.SmsDayStatisDO;
import com.xhqb.spectre.common.dal.entity.SmsDayStatisKey;
import com.xhqb.spectre.common.dal.query.SmsDayStatisQuery;

import java.util.List;

public interface SmsDayStatisMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_day_statis
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(SmsDayStatisKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_day_statis
     *
     * @mbggenerated
     */
    int insert(SmsDayStatisDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_day_statis
     *
     * @mbggenerated
     */
    int insertSelective(SmsDayStatisDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_day_statis
     *
     * @mbggenerated
     */
    SmsDayStatisDO selectByPrimaryKey(SmsDayStatisKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_day_statis
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SmsDayStatisDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_day_statis
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SmsDayStatisDO record);

    /**
     * 查询短信发送量日概况列表总页数
     *
     * @param smsDayStatisQuery
     * @return
     */
    Integer countByQuery(SmsDayStatisQuery smsDayStatisQuery);

    /**
     * 查询短信发送量日概况列表数据
     *
     * @param smsDayStatisQuery
     * @return
     */
    List<SmsDayStatisDO> selectByQuery(SmsDayStatisQuery smsDayStatisQuery);
}