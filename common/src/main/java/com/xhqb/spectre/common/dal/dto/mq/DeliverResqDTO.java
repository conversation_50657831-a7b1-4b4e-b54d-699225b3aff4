package com.xhqb.spectre.common.dal.dto.mq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class DeliverResqDTO implements Serializable {

    private static final long serialVersionUID = 160163635161065102L;

    private CmppDeliverResqDTO cmppDeliverResqDTO;

    private String type;
}

