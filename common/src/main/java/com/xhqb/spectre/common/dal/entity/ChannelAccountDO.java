package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelAccountDO implements Serializable {

    private static final long serialVersionUID = -5082383062018781368L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.channel_code
     *
     * @mbggenerated
     */
    private String channelCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.sms_type_code
     *
     * @mbggenerated
     */
    private String smsTypeCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.name
     *
     * @mbggenerated
     */
    private String name;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.key
     *
     * @mbggenerated
     */
    private String key;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.json_mapping
     *
     * @mbggenerated
     */
    private String jsonMapping;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.price
     *
     * @mbggenerated
     */
    private Integer price;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.protocol
     *
     * @mbggenerated
     */
    private Integer protocol;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.isps
     *
     * @mbggenerated
     */
    private String isps;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.area_filter_type
     *
     * @mbggenerated
     */
    private Integer areaFilterType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.areas
     *
     * @mbggenerated
     */
    private String areas;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.weight
     *
     * @mbggenerated
     */
    private Integer weight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.status
     *
     * @mbggenerated
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.is_tpl_free_audit
     *
     * @mbggenerated
     */
    private Integer isTplFreeAudit;

    /**
     * 支持的签名列表，以英文逗号分隔
     */
    private String signIds;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.remark
     *
     * @mbggenerated
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.creator
     *
     * @mbggenerated
     */
    private String creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.updater
     *
     * @mbggenerated
     */
    private String updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;


    /**
     * 当前渠道账号所属的渠道信息
     */
    private ChannelDO channelDO;
}