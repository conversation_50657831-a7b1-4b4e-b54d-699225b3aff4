package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 群发任务参数状态
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Getter
@AllArgsConstructor
public enum BatchTaskParamStatusEnum {

    UN_SEND(0, "未发送"),
    SENDING(1, "发送中"),
    SENT(2, "已发送");

    private final Integer code;
    private final String description;


    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return
     */
    public static BatchTaskParamStatusEnum getByCode(Integer code) {
        for (BatchTaskParamStatusEnum status : values()) {
            if (Objects.equals(status.getCode(), code)) {
                return status;
            }
        }
        return null;
    }
}
