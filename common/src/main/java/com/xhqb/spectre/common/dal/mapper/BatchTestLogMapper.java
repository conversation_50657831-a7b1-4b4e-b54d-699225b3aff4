package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.BatchTestLogDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 群发测试发送日志
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
public interface BatchTestLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_test_log
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_test_log
     *
     * @mbggenerated
     */
    int insert(BatchTestLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_test_log
     *
     * @mbggenerated
     */
    int insertSelective(BatchTestLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_test_log
     *
     * @mbggenerated
     */
    BatchTestLogDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_test_log
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(BatchTestLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_test_log
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(BatchTestLogDO record);

    /**
     * 根据群发id查询群发测试信息
     * <p>
     * 只查询最近的10条日志记录
     *
     * @param taskId
     * @return
     */
    List<BatchTestLogDO> queryTestLog(@Param("taskId") Integer taskId);
}