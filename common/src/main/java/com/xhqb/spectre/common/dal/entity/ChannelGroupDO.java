package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelGroupDO implements Serializable {

    private static final long serialVersionUID = -6045100294324178574L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group.name
     *
     * @mbggenerated
     */
    private String name;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group.description
     *
     * @mbggenerated
     */
    private String description;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group.sms_type_code
     *
     * @mbggenerated
     */
    private String smsTypeCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group.sign_id
     *
     * @mbggenerated
     */
    private Integer signId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group.creator
     *
     * @mbggenerated
     */
    private String creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group.updater
     *
     * @mbggenerated
     */
    private String updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group.is_delete
     *
     * @mbggenerated
     */
    private Integer isDelete;
}