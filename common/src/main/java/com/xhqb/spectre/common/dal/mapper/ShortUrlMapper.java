package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ShortUrlDO;
import com.xhqb.spectre.common.dal.query.ShortUrlQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface ShortUrlMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url
     *
     * @mbggenerated
     */
    int insert(ShortUrlDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url
     *
     * @mbggenerated
     */
    int insertSelective(ShortUrlDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url
     *
     * @mbggenerated
     */
    ShortUrlDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(ShortUrlDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(ShortUrlDO record);

    Integer countByQuery(ShortUrlQuery shortUrlQuery);

    List<ShortUrlDO> selectByQuery(ShortUrlQuery shortUrlQuery);

    void enable(@Param("id") Integer id, @Param("operator") String operator);

    void disable(@Param("id") Integer id, @Param("operator") String operator);

    void delete(@Param("id") Integer id, @Param("operator") String operator);

    /**
     * 查询所有有效并且未删除的数据
     *
     * @return
     */
    List<ShortUrlDO> selectAll();

    /**
     * 查询所有大于updateTime的数据
     *
     * @param updateTime
     * @return
     */
    List<ShortUrlDO> selectALlByUpdateTime(Date updateTime);

    /**
     * 根据短链编码查询数据
     *
     * @param code 短链编码
     * @return
     */
    ShortUrlDO selectByCode(@Param("code") String code);

    /**
     * 只查询后台管理创建的数据
     */
    List<ShortUrlDO> listByType();

    /**
     * 查询最后更新的为后台类型的短链数据
     */
    ShortUrlDO listByUpdateAndType();

    /**
     * 更新点击次数
     *
     * @param shortCode     短信短链编码
     * @param newClickCount 新增的点击次数
     * @return
     */
    int updateClickCount(@Param("shortCode") String shortCode, @Param("newClickCount") Integer newClickCount);
}