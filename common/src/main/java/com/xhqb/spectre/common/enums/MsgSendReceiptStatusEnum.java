package com.xhqb.spectre.common.enums;

public enum MsgSendReceiptStatusEnum {

    /**
     * 成功
     */
    SUCCESS("0000", "成功"),

    /**
     * 供应商返回失败
     */
    PARTNER_PLATFORM_FAIL("0001", "供应商返回失败"),

    /**
     * 运营商返回失败
     */
    OPERATOR_PLATFORM_FAIL("0002", "运营商返回失败");




    private String code;

    private String desc;

    public String getCode() {
        return code;
    }


    public String getCodeDesc() {
        return desc;
    }

    /**
     * constructor
     *
     * @param code
     * @param desc
     */
    MsgSendReceiptStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
