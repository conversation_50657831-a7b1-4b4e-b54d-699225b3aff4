package com.xhqb.spectre.common.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Data
public class SmsUplinkDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_uplink.id
     *
     * @mbggenerated
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_uplink.channel_msg_id
     *
     * @mbggenerated
     */
    private String channelMsgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_uplink.mobile
     *
     * @mbggenerated
     */
    private String mobile;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_uplink.recv_uplink_time
     *
     * @mbggenerated
     */
    private Integer recvUplinkTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_uplink.dest_terminal_id
     *
     * @mbggenerated
     */
    private String destTerminalId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_uplink.msg_length
     *
     * @mbggenerated
     */
    private Integer msgLength;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_uplink.msg_content
     *
     * @mbggenerated
     */
    private String msgContent;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_uplink.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * 渠道账号id
     */
    private Integer channelAccountId;

    /**
     * 渠道code
     */
    private String channelCode;

    /**
     * 短信类型
     */
    private String smsTypeCode;

    /**
     * CID
     */
    private String cid;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SmsUplinkDO that = (SmsUplinkDO) o;
        return mobile.equals(that.mobile) && smsTypeCode.equals(that.smsTypeCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(mobile, smsTypeCode);
    }
}