package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/10/13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsUplinkQuery implements Serializable {

    /**
     * 上行短信主键
     */
    private Integer id;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 渠道账号id
     */
    private Integer channelAccountId;

    /**
     * 渠道code
     */
    private String channelCode;

    /**
     * 短信类型
     */
    private String smsTypeCode;

    /**
     * 上行短信内容
     */
    private String msgContent;

    /**
     * 分页参数
     */
    private PageParameter pageParameter;

}
