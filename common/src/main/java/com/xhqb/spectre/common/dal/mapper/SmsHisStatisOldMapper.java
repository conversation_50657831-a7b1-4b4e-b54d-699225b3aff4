package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.SmsHisStatisDO;
import com.xhqb.spectre.common.dal.query.SmsHisStatisQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SmsHisStatisOldMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_his_statis
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(String code);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_his_statis
     *
     * @mbggenerated
     */
    int insert(SmsHisStatisDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_his_statis
     *
     * @mbggenerated
     */
    int insertSelective(SmsHisStatisDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_his_statis
     *
     * @mbggenerated
     */
    SmsHisStatisDO selectByPrimaryKey(String code);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_his_statis
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SmsHisStatisDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_his_statis
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SmsHisStatisDO record);

    /**
     * 查询短信历史发送量统计列表总页数
     *
     * @param smsHisStatisQuery
     * @return
     */
    Integer countByQuery(SmsHisStatisQuery smsHisStatisQuery);

    /**
     * 查询短信历史发送量统计列表数据
     *
     * @param smsHisStatisQuery
     * @return
     */
    List<SmsHisStatisDO> selectByQuery(SmsHisStatisQuery smsHisStatisQuery);
}