package com.xhqb.spectre.common.dal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/17 14:33
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplData implements Serializable {

    private static final long serialVersionUID = -53140095136900759L;

    /**
     * 模板ID
     */
    private Integer id;

    /**
     * 模板编码
     */
    private String code;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 签名ID
     */
    private Integer signId;

    /**
     * 签名code
     */
    private String signCode;

    /**
     * 签名名称
     */
    private String signName;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 参数个数
     */
    private Integer paramCount;

    /**
     * 渠道信息列表
     */
    private List<TplChannelData> channelInfoList;

    /**
     * 模板屏蔽信息列表
     */
    private List<TplDisableData> disableInfoList;

    private Integer tag;
}
