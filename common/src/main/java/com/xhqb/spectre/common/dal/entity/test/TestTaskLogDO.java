package com.xhqb.spectre.common.dal.entity.test;

import lombok.Data;

import java.util.Date;

@Data
public class TestTaskLogDO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 渠道账号id
     */
    private Integer channelAccountId;

    /**
     * 模板id
     */
    private Long tplId;

    /**
     * 参数信息
     */
    private String params;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 发送状态
     */
    private Integer sendStatus;

    /**
     * 回执状态
     */
    private Integer callStatus;

    /**
     * 原因 3-执行失败
     */
    private String callReason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}

