package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.SmsUplinkTdDO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_sms_uplink_td(短信退订记录)】的数据库操作Mapper
* @createDate 2025-04-22 15:25:51
* @Entity generator.entity.SmsUplinkTd
*/
public interface SmsUplinkTdMapper {

    int deleteByPrimaryKey(Long id);

    int insert(SmsUplinkTdDO record);

    int insertSelective(SmsUplinkTdDO record);

    SmsUplinkTdDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SmsUplinkTdDO record);

    int updateByPrimaryKey(SmsUplinkTdDO record);

    int insertBatch(List<SmsUplinkTdDO> smsUplinkTdDOList);
}
