package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.CidStrategyDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/26
 */
public interface CidStrategyMapper {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cid_strategy
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cid_strategy
     *
     * @mbggenerated
     */
    int insert(CidStrategyDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cid_strategy
     *
     * @mbggenerated
     */
    int insertSelective(CidStrategyDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cid_strategy
     *
     * @mbggenerated
     */
    CidStrategyDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cid_strategy
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(CidStrategyDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_cid_strategy
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(CidStrategyDO record);

    /**
     * 根据分组查询所有cid策略数据
     *
     * @param strategyGroup
     * @return
     */
    List<CidStrategyDO> selectByGroup(@Param("strategyGroup") String strategyGroup);

    /**
     * 重置所有的状态都为0
     *
     * @param strategyGroup
     * @param list
     */
    void resetStatusByGroup(@Param("strategyGroup") String strategyGroup, @Param("list") List<Integer> list);

    /**
     * 根据类型查询cid策略数据
     *
     * @param type
     * @return
     */
    List<CidStrategyDO> selectByType(@Param("type") String type);

    /**
     * 查询所有cid策略数据
     *
     * @return
     */
    List<CidStrategyDO> selectAll();

    /**
     * 初始化策略分组
     */
    void initStrategyGroup();
}
