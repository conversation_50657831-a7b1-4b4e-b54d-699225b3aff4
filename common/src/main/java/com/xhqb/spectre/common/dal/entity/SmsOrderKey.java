package com.xhqb.spectre.common.dal.entity;

import java.io.Serializable;

public class SmsOrderKey implements Serializable {

    private static final long serialVersionUID = 8684147734441968476L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.order_id
     *
     * @mbggenerated
     */
    private Long orderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.resend
     *
     * @mbggenerated
     */
    private Integer resend;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_order.order_id
     *
     * @return the value of t_sms_order.order_id
     *
     * @mbggenerated
     */
    public Long getOrderId() {
        return orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_order.order_id
     *
     * @param orderId the value for t_sms_order.order_id
     *
     * @mbggenerated
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_order.resend
     *
     * @return the value of t_sms_order.resend
     *
     * @mbggenerated
     */
    public Integer getResend() {
        return resend;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_order.resend
     *
     * @param resend the value for t_sms_order.resend
     *
     * @mbggenerated
     */
    public void setResend(Integer resend) {
        this.resend = resend;
    }
}