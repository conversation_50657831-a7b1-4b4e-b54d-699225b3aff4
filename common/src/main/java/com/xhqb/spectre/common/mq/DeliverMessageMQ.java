package com.xhqb.spectre.common.mq;

import lombok.*;
import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DeliverMessageMQ<T> implements Serializable {

    private static final long serialVersionUID = 4644095841491531230L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.msg_id
     *
     * @mbggenerated
     */
    private String msgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.user_name
     *
     * @mbggenerated
     */
    private String userName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.submit_time
     *
     * @mbggenerated
     */
    private String submitTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.done_time
     *
     * @mbggenerated
     */
    private String doneTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.sms_status
     *
     * @mbggenerated
     */
    private String smsStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.dest_terminal_id
     *
     * @mbggenerated
     */
    private String destTerminalId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.message_status
     *
     * @mbggenerated
     */
    private Integer messageStatus;

    private Integer deliveredTime;

}
