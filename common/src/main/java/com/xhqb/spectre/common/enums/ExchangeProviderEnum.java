package com.xhqb.spectre.common.enums;

public enum ExchangeProviderEnum {

    /**
     * 三通
     */
    SANTONG("santong", "三通"),

    /**
     * 博世通
     */
    BOSHITONG("boshitong", "博世通"),

    /**
     * 创蓝
     */
    CHUANGLAN("chuanglan", "创蓝"),

    /**
     * 玄武
     */
    XUANWU("xuanwu","玄武"),
    /**
     * 秒信
     */
    MIAOXIN("miaoXin", "秒信"),

    TENCENT("tencent", "腾讯"),

    /**
     * ZHIXIN
     */
    ZHIXIN("zhixin", "智信"),

    SHANYUN("shanyun", "闪云"),

    KAIFENG("kaifeng", "凯风"),

    /**
     * IOS
     */
    MOCK("mock", "模拟"),

    /**
     * IOS
     */
    NOOP("noop", "模拟"),

    CMPP("cmpp", "cmpp"),

    /**
     * UNKNOWN
     */
    UNKNOWN("unknown", "未知");


    private String providerName;

    private String description;

    public String getProviderName() {
        return providerName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * contructor.
     *
     * @param providerName
     */
    ExchangeProviderEnum(String providerName, String description) {
        this.providerName = providerName;
        this.description = description;
    }

    /**
     * 通过枚举<code>providerName</code>获得枚举
     *
     * @param providerName
     * @return
     */
    public static ExchangeProviderEnum getByProviderName(String providerName) {
        for (ExchangeProviderEnum resultValue : values()) {
            if (resultValue.getProviderName().equals(providerName)) {
                return resultValue;
            }
        }
        return UNKNOWN;
    }
}
