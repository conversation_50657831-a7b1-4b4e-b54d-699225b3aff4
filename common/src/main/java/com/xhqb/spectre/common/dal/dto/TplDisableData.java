package com.xhqb.spectre.common.dal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/21 14:42
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplDisableData implements Serializable {

    private static final long serialVersionUID = -8385082311238685748L;

    /**
     * 运营商列表
     */
    private List<String> ispList;

    /**
     * 地域列表
     */
    private List<AreaData> areaList;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;
}
