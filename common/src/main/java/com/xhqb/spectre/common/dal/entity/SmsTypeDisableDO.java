package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsTypeDisableDO implements Serializable {

    private static final long serialVersionUID = -4749677833470267531L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_type_disable.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_type_disable.sms_type_code
     *
     * @mbggenerated
     */
    private String smsTypeCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_type_disable.isps
     *
     * @mbggenerated
     */
    private String isps;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_type_disable.areas
     *
     * @mbggenerated
     */
    private String areas;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_type_disable.start_time
     *
     * @mbggenerated
     */
    private Integer startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_type_disable.end_time
     *
     * @mbggenerated
     */
    private Integer endTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_type_disable.creator
     *
     * @mbggenerated
     */
    private String creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_type_disable.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_type_disable.updater
     *
     * @mbggenerated
     */
    private String updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_type_disable.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_type_disable.is_delete
     *
     * @mbggenerated
     */
    private Integer isDelete;
}