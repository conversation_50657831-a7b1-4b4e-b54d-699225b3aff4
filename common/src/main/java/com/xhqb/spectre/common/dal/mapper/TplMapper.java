package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.TplDO;
import com.xhqb.spectre.common.dal.query.TplQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface TplMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl
     *
     * @mbggenerated
     */
    int insert(TplDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl
     *
     * @mbggenerated
     */
    int insertSelective(TplDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl
     *
     * @mbggenerated
     */
    TplDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(TplDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(TplDO record);

    Integer countByQuery(TplQuery tplQuery);

    List<TplDO> selectByQuery(TplQuery tplQuery);

    TplDO selectByCodeAndSign(@Param("code") String code, @Param("signId") Integer signId);

    void enable(@Param("id") Integer id, @Param("operator") String operator);

    void disable(@Param("id") Integer id, @Param("operator") String operator);

    void delete(@Param("id") Integer id, @Param("operator") String operator);

    List<TplDO> selectEnum(@Param("code") String code, @Param("title") String title, @Param("status") Integer status, @Param("signId") Integer signId);

    List<TplDO> selectAllEnabled();

    List<TplDO> selectByAppCode(String appCode);

    List<TplDO> selectByIdList(@Param("idList") List<Integer> idList);

    /**
     * 根据短信类型+app编码+签名 自动匹配模板信息
     *
     * @param smsTypeCode
     * @param appCode
     * @param signId
     * @param content
     * @return
     */
    List<TplDO> autoMatchTpl(@Param("smsTypeCode") String smsTypeCode, @Param("appCode") String appCode, @Param("signId") Integer signId, @Param("content") String content);

    /**
     * 查询活跃模版
     *
     * @return
     */
    List<TplDO> selectByActive(TplQuery tplQuery);

    List<TplDO> selectByIdListAndTplCode(@Param("idList") List<Integer> smsTplIds, @Param("tplCode") String tplCode);

    List<TplDO> selectAll();

    int updateApproveStatusByCodeAndSignId(@Param("code") String tplCode, @Param("signId") String signId, @Param("approveStatus") int approveStatus,@Param("updater") String updater);

    int updateContentByCodeAndSignId(@Param("code") String tplCode, @Param("signId") String signId,@Param("content") String content);

    TplDO selectByReportId(@Param("reportId") String reportId);

    List<TplDO> selectEffectiveNotify(@Param("source")String source,@Param("notifySendStatus") int notifySendStatus,@Param("status") int status);

    int updateByContentId(@Param("reportId")String reportId, @Param("content")String content,  @Param("approveStatus") int approveStatus);

    int updateNotifyStatus(@Param("id")Integer id, @Param("notifyStatus")int notifyStatus);

    int updateNotifySendStatusByReportIds(@Param("reportIds")List<String> keyRepordIdList,@Param("notifySendStatus") int notifySendStatus);

    List<TplDO> selectByCodeList(@Param("codeList") List<String> codeList);
}