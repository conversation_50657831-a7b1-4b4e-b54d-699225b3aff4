package com.xhqb.spectre.common.dal.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class AutoTestOverrideQuery {

    /**
     * 自动测试任务 ID
     */
    private Long autoTestTaskId;

    /**
     * 模板编码，模糊查询
     */
    private String likeTplCode;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     *
     * @ignore 分页参数
     */
    private PageParameter pageParameter;
}
