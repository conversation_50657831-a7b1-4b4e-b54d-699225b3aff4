package com.xhqb.spectre.common.validation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class PathValidator implements ConstraintValidator<ValidPath, String> {

    private static final String PATH_REGEX = "^/[^/].*"; // / 开头且非空的路径

    @Override
    public void initialize(ValidPath constraintAnnotation) {

    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.isEmpty()) {
            return true;
        }
        return value.matches(PATH_REGEX);
    }
}
