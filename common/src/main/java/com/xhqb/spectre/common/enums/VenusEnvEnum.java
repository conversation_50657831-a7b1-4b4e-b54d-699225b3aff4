package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/*
 * @Author: huangyanxiong
 * @Date: 2021/12/9 10:39
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum VenusEnvEnum {

    DEV("dev", "开发"),
    TEST("test", "测试"),
    UAT("uat", "预生产"),
    PROD("prod", "生产");

    private String env;
    private String description;

    /**
     * Venus配置中心对应环境（dev、test、uat、prod）
     */
    private static final String VENUS_ENV = System.getProperty("venus.env");

    public static boolean isDev() {
        return DEV.getEnv().equalsIgnoreCase(VENUS_ENV);
    }

    public static boolean isTest() {
        return TEST.getEnv().equalsIgnoreCase(VENUS_ENV);
    }

    public static boolean isUat() {
        return UAT.getEnv().equalsIgnoreCase(VENUS_ENV);
    }

    public static boolean isProd() {
        return PROD.getEnv().equalsIgnoreCase(VENUS_ENV);
    }

    /**
     * 是否生产环境
     *
     * @return
     */
    public static boolean isProduction() {
        return isUat() || isProd();
    }

    public static boolean isDevAndTest() {
        return isDev() || isTest();
    }
}
