package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 短信 oa 报备查询
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplOaReportQuery implements Serializable {

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 报备人
     */
    private String creator;

    /**
     * 开始时间 yyyy-MM-dd HH:mm:ss
     */
    private String startTime;

    /**
     * 结束时间 yyyy-MM-dd HH:mm:ss
     */
    private String endTime;

    /**
     * 状态 0：初始化,1：审批中,2:拒绝, 3:通过
     */
    private Integer approveStatus;

    /**
     * 分页参数
     *
     * @ignore
     */
    private PageParameter pageParameter;
}
