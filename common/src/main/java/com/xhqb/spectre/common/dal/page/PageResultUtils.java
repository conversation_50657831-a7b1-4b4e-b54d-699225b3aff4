package com.xhqb.spectre.common.dal.page;

import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;

/**
 * The type Page result utils.
 */
public class PageResultUtils {

    /**
     * Result common pager.
     *
     * @param <T>           the type parameter
     * @param countSupplier the count supplier
     * @param listSupplier  the list supplier
     * @return the common pager
     */
    public static <T> CommonPager<T> result(final Supplier<Integer> countSupplier, final Supplier<List<T>> listSupplier) {
        Integer count = countSupplier.get();
        if (count != null && count > 0) {
            return new CommonPager<>(count, listSupplier.get());
        }
        return new CommonPager<>(0, Collections.emptyList());
    }
}
