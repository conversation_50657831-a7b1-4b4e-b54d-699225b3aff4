package com.xhqb.spectre.common.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * @TableName t_phone_number_status
 */
@TableName(value ="t_phone_number_status")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PhoneNumberStatusDO implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 手机号密文
     */
    @TableField("mobile_enc")
    private String mobile;

    /**
     * 状态最后查询时间
     */
    private Integer lastTime;

    /**
     * 手机号状态
     */
    private Integer status;

    /**
     * 归属运营商
     */
    private Integer carrier;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}