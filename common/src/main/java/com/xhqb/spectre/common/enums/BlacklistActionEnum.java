package com.xhqb.spectre.common.enums;

public enum BlacklistActionEnum {

    ADD("ADD", "加入黑名单"),
    REMOVE("REMOVE", "退出黑名单");

    private final String code;
    private final String name;

    BlacklistActionEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static BlacklistActionEnum getByCode(String code) {
        for (BlacklistActionEnum action : values()) {
            if (action.getCode().equals(code)) {
                return action;
            }
        }
        return null;
    }
}
