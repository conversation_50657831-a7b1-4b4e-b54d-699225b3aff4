package com.xhqb.spectre.common.dal.entity;

import lombok.Data;

import java.util.Date;

/**
 * 失败补发规则
 */
@Data
public class FailResendRuleDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 策略id
     */
    private String strategyId;

    /**
     * 规则 ID
     */
    private String ruleId;

    /**
     * 补发场景:(0:运营商,1:渠道,2:地区)
     */
    private String sceneType;

    /**
     * 补发场景值(多个值用用英文逗号隔开)
     */
    private String sceneValue;

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 规则配置信息:[{"tpl_code":"","sign_code":"","weight":""}]
     */
    private String ruleValue;

    /**
     * 是否删除：0=未删除，1=已删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
