package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.MobileBlackDO;
import com.xhqb.spectre.common.dal.query.MobileBlackQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Mapper
public interface MobileBlackMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_mobile_black
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_mobile_black
     *
     * @mbggenerated
     */
    int insert(MobileBlackDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_mobile_black
     *
     * @mbggenerated
     */
    int insertSelective(MobileBlackDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_mobile_black
     *
     * @mbggenerated
     */
    MobileBlackDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_mobile_black
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(MobileBlackDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_mobile_black
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(MobileBlackDO record);

    Integer countByQuery(MobileBlackQuery mobileBlackQuery);

    List<MobileBlackDO> selectByQuery(MobileBlackQuery mobileBlackQuery);

    List<MobileBlackDO> selectByPage(int offset, int pageSize);

    void delete(@Param("id") Integer id, @Param("operator") String operator);

    MobileBlackDO selectByMobileAndSmsType(@Param("mobile") String mobile, @Param("smsTypeCode") String smsTypeCode);

    List<MobileBlackDO> selectByIdList(@Param("idList") List<Integer> idList);

    void deleteByIdList(@Param("idList") List<Integer> idList, @Param("operator") String operator);

    /**
     * 批量插入
     * @param mobileBlackDOList
     */
    void insertBatch(@Param("list") List<MobileBlackDO> mobileBlackDOList);

    /**
     * 通过手机和营销查询
     *
     * @param mobileList
     * @return
     */
    List<String> selectByMarketAndPhone(@Param("mobileList") List<String> mobileList);

    List<MobileBlackDO> selectExpiredDataByExpiredTime();

    /**
     * id分页
     * @param lastId 主键
     * @param updateTime 更新时间
     * @param pageSize 每次获取的条数
     */
    List<MobileBlackDO> selectBlackListAfterId(@Param("lastId") int lastId,
                                               @Param("updateTime") Date updateTime,
                                               @Param("pageSize") int pageSize);
}