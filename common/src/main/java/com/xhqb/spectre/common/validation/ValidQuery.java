package com.xhqb.spectre.common.validation;

import com.nimbusds.jose.Payload;

import javax.validation.Constraint;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = QueryValidator.class) // 指定校验器类
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidQuery {

    String message() default ""; // 默认错误消息

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
