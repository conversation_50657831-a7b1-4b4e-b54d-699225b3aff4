package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ChannelGroupDO;
import com.xhqb.spectre.common.dal.query.ChannelGroupQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ChannelGroupMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_group
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_group
     *
     * @mbggenerated
     */
    int insert(ChannelGroupDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_group
     *
     * @mbggenerated
     */
    int insertSelective(ChannelGroupDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_group
     *
     * @mbggenerated
     */
    ChannelGroupDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_group
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(ChannelGroupDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_group
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(ChannelGroupDO record);

    Integer countByQuery(ChannelGroupQuery query);

    List<ChannelGroupDO> selectByQuery(ChannelGroupQuery query);

    void delete(@Param("id") Integer id, @Param("operator") String operator);

    void deleteByIdList(@Param("idList") List<Integer> idList, @Param("operator") String operator);
}