package com.xhqb.spectre.common.enums;

public enum BlacklistTypeEnum {

    TD_UPLINK("td_uplink", "退订上行"),
    CUSTOMER_APPEAL("customer_appeal", "用户投诉"),
    ADMIN_OPERATION("admin_operation", "管理员操作"),
    ;

    private final String code;
    private final String name;

    BlacklistTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static BlacklistTypeEnum getByCode(String code) {
        for (BlacklistTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
