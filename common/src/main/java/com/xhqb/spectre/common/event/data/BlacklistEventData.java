package com.xhqb.spectre.common.event.data;

import com.xhqb.spectre.common.enums.BlacklistActionEnum;
import com.xhqb.spectre.common.enums.BlacklistTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BlacklistEventData implements Serializable {

    private static final long serialVersionUID = 1L;

    private String mobile;
    private BlacklistActionEnum action;
    private BlacklistTypeEnum blacklistType;
    private String smsType;
    private String reason;
    private LocalDateTime operateTime;
}
