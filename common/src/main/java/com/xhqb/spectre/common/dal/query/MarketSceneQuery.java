package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.Data;

import java.io.Serializable;

/**
 * 营销场景查询
 *
 * <AUTHOR>
 * @date 2022/9/26
 */
@Data
public class MarketSceneQuery implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 营销场景名称
     */
    private String name;
    /**
     * 营销场景状态 0:无效 1:有效
     */
    private Integer status;
    /**
     * 分页参数
     *
     * @ignore
     */
    private PageParameter pageParameter;
}
