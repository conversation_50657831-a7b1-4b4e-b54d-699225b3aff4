package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskReportDO implements Serializable {

    private static final long serialVersionUID = -8550872904204521788L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_report.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_report.task_id
     *
     * @mbggenerated
     */
    private Integer taskId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_report.task_id
     *
     * @mbggenerated
     */
    private Integer taskParamId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_report.cost
     *
     * @mbggenerated
     */
    private Integer cost;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_report.total
     *
     * @mbggenerated
     */
    private Integer total;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_report.success
     *
     * @mbggenerated
     */
    private Integer success;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_report.fail
     *
     * @mbggenerated
     */
    private Integer fail;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_report.unknown
     *
     * @mbggenerated
     */
    private Integer unknown;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_report.time_stat
     *
     * @mbggenerated
     */
    private String timeStat;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_report.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_report.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_report.error_stat
     *
     * @mbggenerated
     */
    private String errorStat;

}