package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.dto.result.QueryTestStatResult;
import com.xhqb.spectre.common.dal.dto.result.DebtSmsReportOrderResult;
import com.xhqb.spectre.common.dal.entity.DebtSmsReportDO;
import com.xhqb.spectre.common.dal.entity.SmsOrderDO;
import com.xhqb.spectre.common.dal.entity.SmsOrderKey;
import com.xhqb.spectre.common.dal.entity.SmsOrderStatDO;
import com.xhqb.spectre.common.dal.query.ReissueOrderQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface SmsOrderMapper {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(SmsOrder<PERSON><PERSON> key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int insert(SmsOrderDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int insertSelective(SmsOrderDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    SmsOrderDO selectByPrimaryKey(SmsOrderKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SmsOrderDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_order
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SmsOrderDO record);

    SmsOrderDO selectByOrderId(Long orderId);

    SmsOrderDO selectByRequestId(String requestId);

    List<SmsOrderDO> selectByRequestIds(List<String> requestIdList);

    List<SmsOrderDO> selectByRequestIdsAndAppCode(@Param("requestIdList") List<String> requestIdList,
                                                  @Param("appCode") String appCode,
                                                  @Param("currentDayLatestTimestamp") long currentDayLatestTimestamp,
                                                  @Param("nDaysAgoEarliestTimestamp") long nDaysAgoEarliestTimestamp);

    SmsOrderDO selectByAppCodeAndOutOrderId(@Param("appCode") String appCode, @Param("outOrderId") String outOrderId);

    int insertOrUpdateBatch(List<SmsOrderDO> smsOrderDOList);

    int insertOrUpdateBatchSubmitCount(List<SmsOrderDO> smsOrderDOList);

    int insertOrUpdateBatchReportCount(List<SmsOrderDO> smsOrderDOList);

    /**
     * 群发测试查询
     * <p>
     * 只查询发送成功的一条记录，用于群发任务提交校验
     *
     * @param batchId
     * @return
     */
    SmsOrderDO batchTestQuery(@Param("batchId") Integer batchId);

    List<SmsOrderStatDO> selectStatByChannel(@Param("beginTime") Integer beginTime, @Param("endTime") Integer endTime);

    List<SmsOrderStatDO> selectStatByIsp(@Param("beginTime") Integer beginTime, @Param("endTime") Integer endTime);

    List<SmsOrderStatDO> selectMarketStatByChannel(@Param("beginTime") Integer beginTime, @Param("endTime") Integer endTime);

    List<SmsOrderStatDO> selectMarketStatByIsp(@Param("beginTime") Integer beginTime, @Param("endTime") Integer endTime);

    List<SmsOrderDO> selectOrderStatus(@Param("appCode") String appCode,
                                       @Param("requestId") String requestId,
                                       @Param("tplCode") String tplCode,
                                       @Param("mobiles") List<String> mobiles,
                                       @Param("currentDayLatestTimestamp") long currentDayLatestTimestamp,
                                       @Param("nDaysAgoEarliestTimestamp") long nDaysAgoEarliestTimestamp);

    List<SmsOrderDO> selectByReissueOrderQuery(@Param("query") ReissueOrderQuery query,
                                               @Param("currentDayLatestTimestamp") long currentDayLatestTimestamp,
                                               @Param("nDaysAgoEarliestTimestamp") long nDaysAgoEarliestTimestamp);

    Integer countByReissueOrderQuery(@Param("query") ReissueOrderQuery query,
                                     @Param("currentDayLatestTimestamp") long currentDayLatestTimestamp,
                                     @Param("nDaysAgoEarliestTimestamp") long nDaysAgoEarliestTimestamp);

    QueryTestStatResult selectQueryTestStats(@Param("sendStartTime") Long sendStartTime, @Param("sendEndTime") Long sendEndTime,
                                             @Param("smsTypeCode") String smsTypeCode, @Param("channelAccountId") Integer channelAccountId);

    /**
     * 查询债转短信报表订单的数据统计
     *
     * @param beginSendTime
     * @param endSendTime
     * @param debtSmsReportList
     * @return
     */
    List<DebtSmsReportOrderResult> selectDebtSmsReportOrderStats(@Param("beginSendTime") Long beginSendTime, @Param("endSendTime") Long endSendTime,
                                                                 @Param("debtSmsReportList") List<DebtSmsReportDO> debtSmsReportList);

    List<SmsOrderDO> selectByTplCodeAndDate(@Param("tplCode") String tplCode, @Param("startTime") long startTime, @Param("endTime") long endTime,@Param("count") Integer count);
}