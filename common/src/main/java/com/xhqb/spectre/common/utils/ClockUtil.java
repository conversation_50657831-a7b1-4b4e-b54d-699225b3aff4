package com.xhqb.spectre.common.utils;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

@Slf4j
public enum ClockUtil {
    /**
     *
     */
    INS;

    /**
     * 当前时间
     */
    private volatile long now = 0;

    private ClockUtil() {
        this.now = System.currentTimeMillis();
        start();
    }

    private void start() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("clockupdater-pool-%d").setDaemon(true).build();
        ScheduledExecutorService scheduler = new ScheduledThreadPoolExecutor(
            1, namedThreadFactory);
        scheduler.scheduleWithFixedDelay(() -> {
            now = System.currentTimeMillis();
        }, 1L, 1L, TimeUnit.SECONDS);
    }

    public long now() {
        return now;
    }

    public int tick() {
        return (int) (now / 1000);
    }
}
