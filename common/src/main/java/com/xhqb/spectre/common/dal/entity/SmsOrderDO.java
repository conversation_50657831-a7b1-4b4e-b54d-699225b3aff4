package com.xhqb.spectre.common.dal.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsOrderDO implements Serializable {

    private static final long serialVersionUID = 3029721464929273980L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.order_id
     *
     * @mbggenerated
     */
    private Long orderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.request_id
     *
     * @mbggenerated
     */
    private String requestId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.channel_msg_id
     *
     * @mbggenerated
     */
    private String channelMsgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.app_code
     *
     * @mbggenerated
     */
    private String appCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.tpl_code
     *
     * @mbggenerated
     */
    private String tplCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.sms_type_code
     *
     * @mbggenerated
     */
    private String smsTypeCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.channel_code
     *
     * @mbggenerated
     */
    private String channelCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.channel_account_id
     *
     * @mbggenerated
     */
    private Integer channelAccountId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.sign_name
     *
     * @mbggenerated
     */
    private String signName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.mobile
     *
     * @mbggenerated
     */
    private String mobile;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.content
     *
     * @mbggenerated
     */
    private String content;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.slice_id
     *
     * @mbggenerated
     */
    private Integer sliceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.province_short_name
     *
     * @mbggenerated
     */
    private String provinceShortName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.city_short_name
     *
     * @mbggenerated
     */
    private String cityShortName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.isp_code
     *
     * @mbggenerated
     */
    private String ispCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.batch_id
     *
     * @mbggenerated
     */
    private Integer batchId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.bill_count
     *
     * @mbggenerated
     */
    private Integer billCount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.send_type
     *
     * @mbggenerated
     */
    private Integer sendType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.req_src
     *
     * @mbggenerated
     */
    private Integer reqSrc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.resend
     *
     * @mbggenerated
     */
    private Integer resend;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.send_time
     *
     * @mbggenerated
     */
    private Integer sendTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.send_status
     *
     * @mbggenerated
     */
    private Integer sendStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.send_code
     *
     * @mbggenerated
     */
    private String sendCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.send_desc
     *
     * @mbggenerated
     */
    private String sendDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.recv_submit_time
     *
     * @mbggenerated
     */
    private Integer recvSubmitTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.report_time
     *
     * @mbggenerated
     */
    private Integer reportTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.report_status
     *
     * @mbggenerated
     */
    private Integer reportStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.report_code
     *
     * @mbggenerated
     */
    private String reportCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.report_desc
     *
     * @mbggenerated
     */
    private String reportDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.submit_time
     *
     * @mbggenerated
     */
    private Integer submitTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.done_time
     *
     * @mbggenerated
     */
    private Integer doneTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.parameter
     *
     * @mbggenerated
     */
    private String parameter;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.channel_code_set
     *
     * @mbggenerated
     */
    private String channelCodeSet;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.submit_resq_count
     *
     * @mbggenerated
     */
    private Integer submitResqCount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.report_count
     *
     * @mbggenerated
     */
    private Integer reportCount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.phone_status
     *
     * @mbggenerated
     */
    private Long phoneStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.cas_version
     *
     * @mbggenerated
     */
    private Integer casVersion;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_order.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * 业务批次号
     */
    private String bizBatchId;

    /**
     * 是否回调消息中心（0：不回调 1：回调）
     */
    private Integer callMetis;

    /**
     * 回执耗时（秒）
     */
    private Integer reportCostTime;

    /**
     * 回执耗时等级
     */
    private Integer reportCostLevel;

    /**
     * 订单表名后缀(yyyyMM)[由PosterConsumer类中进行填充]
     * 该字段在表中不存在， 只是用于消息透传
     */
    @TableField(exist = false)
    private String tableNameSuffix;

}