package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2022/8/26 18:20
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsOrderStatDO implements Serializable {

    private static final long serialVersionUID = -2243515205389028264L;

    private String smsTypeCode;

    private Integer channelAccountId;

    private String isp;

    private Integer totalCount;

    private Integer successCount;

    private Integer failedCount;

    private Integer unknownCount;
}
