package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelAccountDisableDO implements Serializable {

    private static final long serialVersionUID = -4633794066057007548L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account_disable.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account_disable.channel_account_id
     *
     * @mbggenerated
     */
    private Integer channelAccountId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account_disable.isps
     *
     * @mbggenerated
     */
    private String isps;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account_disable.areas
     *
     * @mbggenerated
     */
    private String areas;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account_disable.creator
     *
     * @mbggenerated
     */
    private String creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account_disable.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account_disable.updater
     *
     * @mbggenerated
     */
    private String updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account_disable.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_account_disable.is_delete
     *
     * @mbggenerated
     */
    private Integer isDelete;

    /**
     * 屏蔽开始时间
     */
    private Long maskStartTime;

    /**
     * 屏蔽结束时间
     */
    private Long maskEndTime;

    /**
     * 屏蔽类型：1-整个时间段屏蔽（原功能），2-固定时间段屏蔽（新功能）
     */
    private Integer disableType;

    /**
     * 时间段开始时间（格式：HH:mm，如 10:30）
     */
    private String periodStartTime;

    /**
     * 时间段结束时间（格式：HH:mm，如 12:00）
     */
    private String periodEndTime;

    /**
     * 备注信息（屏蔽原因等）
     */
    private String remark;

    /**
     * 渠道账号信息
     */
    private ChannelAccountDO channelAccountDO;
}