package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ShortUrlBrandStatDO;
import com.xhqb.spectre.common.dal.query.ShortUrlBrandStatQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Mapper
public interface ShortUrlBrandStatMapper {

    int insertBatch(@Param("list") List<ShortUrlBrandStatDO> list);

    Integer countByQuery(@Param("query") ShortUrlBrandStatQuery shortUrlBrandStatQuery);

    List<ShortUrlBrandStatDO> selectByQuery(@Param("query") ShortUrlBrandStatQuery shortUrlBrandStatQuery);

    List<ShortUrlBrandStatDO> selectByTime(@Param("statDate")Date statDate);

    int updateDeleteTagByTime(@Param("statDate")Date statDate);
}
