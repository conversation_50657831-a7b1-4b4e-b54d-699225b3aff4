package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 短信发送量日概况
 * <p>
 * 按时间段和code查询，code支持多选
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsDayStatisQuery implements Serializable {

    /**
     * 统计日期
     */
    private String date;
    /**
     * 类型编码列表
     */
    private List<String> codeList;
    /**
     * 分页参数
     */
    private PageParameter pageParameter;

}
