package com.xhqb.spectre.common.dal.mapper;


import com.xhqb.spectre.common.dal.entity.CmppRecord;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CmppRecordMapper {

    /**
     * 批量插入
     * @param list
     * @return
     */
    int batchInsert(List<CmppRecord> list);

    /**
     * 更新deliver消息
     * @param record
     * @return
     */
    int updateDeliverInfoById(CmppRecord record);

    /**
     * 根据msgIds查询submitCoupled状态的消息记录
     * @param msgIds
     * @return
     */
    List<CmppRecord> findSubmitCoupledByMsgIds(List<String> msgIds);
}