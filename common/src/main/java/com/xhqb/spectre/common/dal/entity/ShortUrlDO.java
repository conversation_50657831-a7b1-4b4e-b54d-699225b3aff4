package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShortUrlDO implements Serializable {

    private static final long serialVersionUID = 6989323861890291961L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url.short_code
     *
     * @mbggenerated
     */
    private String shortCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url.src_url
     *
     * @mbggenerated
     */
    private String srcUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url.description
     *
     * @mbggenerated
     */
    private String description;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url.expired_date
     *
     * @mbggenerated
     */
    private Date expiredDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url.status
     *
     * @mbggenerated
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url.click_count
     *
     * @mbggenerated
     */
    private Integer clickCount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url.ip_click_count
     *
     * @mbggenerated
     */
    private Integer ipClickCount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url.creator
     *
     * @mbggenerated
     */
    private String creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url.updater
     *
     * @mbggenerated
     */
    private String updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url.is_delete
     *
     * @mbggenerated
     */
    private Integer isDelete;

    /**
     * 创建类型 1 人工  2 接口
     */
    private Integer type;

    public boolean isEnabled() {
        return status.equals(1);
    }

    public boolean isDisabled() {
        return status.equals(0);
    }
}