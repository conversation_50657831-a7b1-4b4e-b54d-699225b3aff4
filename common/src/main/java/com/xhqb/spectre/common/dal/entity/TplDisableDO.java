package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplDisableDO {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_disable_info.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_disable_info.tpl_id
     *
     * @mbggenerated
     */
    private Integer tplId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_disable_info.isps
     *
     * @mbggenerated
     */
    private String isps;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_disable_info.areas
     *
     * @mbggenerated
     */
    private String areas;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_disable_info.start_time
     *
     * @mbggenerated
     */
    private Integer startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_disable_info.end_time
     *
     * @mbggenerated
     */
    private Integer endTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_disable_info.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_disable_info.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;
}