package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ErrorCodeDO;
import com.xhqb.spectre.common.dal.query.ErrorCodeQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ErrorCodeMapper {
    int deleteByPrimaryKey(@Param("type") String type, @Param("xhErrCode") Integer xhErrCode);

    int insert(ErrorCodeDO record);

    int insertSelective(ErrorCodeDO record);

    ErrorCodeDO selectByPrimaryKey(@Param("type") String type, @Param("xhErrCode") Integer xhErrCode);

    int updateByPrimaryKeySelective(ErrorCodeDO record);

    int updateByPrimaryKey(ErrorCodeDO record);

    /**
     * 分页查询错误码列表总页数
     *
     * @param errorCodeQuery
     * @return
     */
    Integer countByQuery(ErrorCodeQuery errorCodeQuery);

    /**
     * 分页查询错误码列表数据
     *
     * @param errorCodeQuery
     * @return
     */
    List<ErrorCodeDO> selectByQuery(ErrorCodeQuery errorCodeQuery);

    /**
     * 根据类型获取最大的错误码 不包括9999
     *
     * @param type
     * @return
     */
    Integer selectMaxCodeByType(@Param("type") String type);

}