/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xhqb.spectre.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * DateUtils.
 */
@Slf4j
public class DateUtil {

    public static final String DATE_FORMAT_DATETIME = "yyyy-MM-dd HH:mm:ss";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DATE_FORMAT_DATETIME);

    /**
     * yyMMddHHmm 格式化
     */
    private static final ThreadLocal<SimpleDateFormat> DATE_FORMATTER = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyMMddHHmm"));
    /**
     * yyyy-MM-dd 格式化
     */
    private static final ThreadLocal<SimpleDateFormat> SMALL_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));
    /**
     * yyyy-MM-dd HH:mm:ss 格式化
     */
    private static final ThreadLocal<SimpleDateFormat> FULL_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    /**
     * yyyy-MM-dd HH:00:00 格式化
     */
    private static final ThreadLocal<SimpleDateFormat> DATE_HOUR_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:00:00"));

    /**
     * yyyy-MM 格式化
     */
    private static final ThreadLocal<SimpleDateFormat> MONTH_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM"));

    /**
     * yyyyMM格式化
     */
    private static final DateTimeFormatter YYYY_MM__FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");

    /**
     * parseLocalDateTime.
     * out put format:yyyy-MM-dd HH:mm:ss
     *
     * @param dataTime date String
     * @return yyyy -MM-dd HH:mm:ss
     * @see LocalDateTime
     */
    public static LocalDateTime parseLocalDateTime(final String dataTime) {
        return LocalDateTime.parse(dataTime, DateTimeFormatter.ofPattern(DATE_FORMAT_DATETIME));
    }

    /**
     * Parse local date time local date time.
     *
     * @param dataTime          the data time
     * @param dateTimeFormatter the date time formatter
     * @return the local date time
     */
    public static LocalDateTime parseLocalDateTime(final String dataTime, final String dateTimeFormatter) {
        return LocalDateTime.parse(dataTime, DateTimeFormatter.ofPattern(dateTimeFormatter));
    }

    /**
     * acquireMinutesBetween.
     *
     * @param start this is start date.
     * @param end   this is start date.
     * @return The number of days between start and end, if end is after start, returns a positive number, otherwise returns a negative number
     */
    public static long acquireMinutesBetween(final LocalDateTime start, final LocalDateTime end) {
        return start.until(end, ChronoUnit.MINUTES);
    }

    /**
     * Acquire millis between long.
     *
     * @param start the start
     * @param end   the end
     * @return the long
     */
    public static long acquireMillisBetween(final LocalDateTime start, final LocalDateTime end) {
        return start.until(end, ChronoUnit.MILLIS);
    }

    /**
     * Format local date time from timestamp local date time.
     *
     * @param timestamp the timestamp
     * @return the local date time
     */
    public static LocalDateTime formatLocalDateTimeFromTimestamp(final Long timestamp) {
        return LocalDateTime.ofEpochSecond(timestamp / 1000, 0, ZoneOffset.ofHours(8));
    }

    /**
     * Format local date time from timestamp by system time zone.
     *
     * @param timestamp the timestamp
     * @return the local date time
     */
    public static LocalDateTime formatLocalDateTimeFromTimestampBySystemTimezone(final Long timestamp) {
        return LocalDateTime.ofEpochSecond(timestamp / 1000, 0, OffsetDateTime.now().getOffset());
    }

    /**
     * Format local date time to string.
     * use default pattern yyyy-MM-dd HH:mm:ss
     *
     * @param localDateTime the localDateTime
     * @return the format string
     */
    public static String localDateTimeToString(final LocalDateTime localDateTime) {
        return DATE_TIME_FORMATTER.format(localDateTime);
    }

    /**
     * Format local date time to string.
     *
     * @param localDateTime the localDateTime
     * @param pattern       formatter pattern
     * @return the format string
     */
    public static String localDateTimeToString(final LocalDateTime localDateTime, final String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(formatter);
    }

    public static String dateToString(Date date) {
        return FULL_FORMAT.get().format(date);
    }

    public static Date stringToDate(String dateTimeStr) {
        try {
            return FULL_FORMAT.get().parse(dateTimeStr);
        } catch (ParseException e) {
            log.warn("stringToDate ParseException", e);
            return null;
        }
    }

    public static Date hourToDate(String hour) {
        try {
            return DATE_HOUR_FORMAT.get().parse(hour);
        } catch (ParseException e) {
            log.warn("hourToDate ParseException", e);
            return null;
        }
    }

    /**
     * int 时间搓转日期
     *
     * @param timestamp
     * @return
     */
    public static Date intToDate(Integer timestamp) {
        return Objects.isNull(timestamp) || timestamp <= 0 ? null : new Date((long) timestamp * 1000);
    }

    /**
     * 日期转date
     *
     * @param date
     * @return
     */
    public static Integer dateToInt(Date date) {
        return Objects.isNull(date) ? 0 : (int) (date.getTime() / 1000);
    }

    /**
     * 时间戳转换为时间格式
     *
     * @param time
     * @return
     */
    public static String intToString(Integer time) {
        if (Objects.isNull(time) || time == 0) {
            return "";
        }
        return FULL_FORMAT.get().format(new Date((long) time * 1000));
    }

    /**
     * 时间格式转换为时间戳
     *
     * @param dateTime
     * @return
     */
    public static Integer stringToInt(String dateTime) {
        Date date = stringToDate(dateTime);
        if (Objects.isNull(date)) {
            return 0;
        }
        return (int) (date.getTime() / 1000);
    }

    /**
     * 获取到当前时间
     *
     * @return 返回秒
     */
    public static Integer getNow() {
        return (int) (System.currentTimeMillis() / 1000);
    }

    /**
     * 将(yyMMddHHmm（YY为年的后两位00-99，MM：01-12，DD：01-31，HH：00-23，MM：00-59）)格式时间转换成时间戳
     *
     * @param reportTime yyMMddHHmm
     * @return 时间戳
     */
    public static Integer strToIntTime(String reportTime) {
        if (Objects.isNull(reportTime)) {
            return null;
        }
        try {
            DateFormat DATE_FORMATTER = new SimpleDateFormat("yyMMddHHmm");
            Date date = DATE_FORMATTER.parse(reportTime);
            return (int) (date.getTime() / 1000);
        } catch (ParseException e) {
            log.warn("strToIntTime ParseException", e);
            return null;
        }
    }

    /**
     * 将(yyMMddHHmm（YY为年的后两位00-99，MM：01-12，DD：01-31，HH：00-23，MM：00-59）)格式时间转换成时间戳
     *
     * @param reportTime yyMMddHHmm
     * @return 时间戳
     */
    public static Long strToLongTime(String reportTime) {
        if (Objects.isNull(reportTime)) {
            return null;
        }
        try {
            Date date = DATE_FORMATTER.get().parse(reportTime);
            return (date.getTime() / 1000);
        } catch (ParseException e) {
            log.warn("strToIntTime ParseException", e);
            return null;
        }
    }

    /**
     * 将long类型时间戳转换成(yyMMddHHmm（YY为年的后两位00-99，MM：01-12，DD：01-31，HH：00-23，MM：00-59）)格式时间
     *
     * @param time long类型时间戳
     * @return yyMMddHHmm
     */
    public static String longTimeToStr(Long time) {
        if (Objects.isNull(time)) {
            return null;
        }
        return DATE_FORMATTER.get().format(new Date(time));
    }

    /**
     * 将int类型时间戳转换成(yyMMddHHmm（YY为年的后两位00-99，MM：01-12，DD：01-31，HH：00-23，MM：00-59）)格式时间
     *
     * @param time int类型时间戳
     * @return yyMMddHHmm
     */
    public static String intTimeToStr(Integer time) {
        if (Objects.isNull(time)) {
            return null;
        }
        return DATE_FORMATTER.get().format(new Date((long) time * 1000));
    }

    /**
     * 获取分表后缀日期
     *
     * @return
     */
    public static String getTableNameSuffix() {
        return YYYY_MM__FORMATTER.format(LocalDate.now());
    }

    public static Date getMonthBegin(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    /**
     * 计算当前日期往前/后推移offsetMonth个月的月份起始时间 如：2019-11-01 00:00:00
     *
     * @return
     */
    public static Date getMonthBegin(Date date, Integer offsetMonths) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.add(Calendar.MONTH, offsetMonths);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }
}
