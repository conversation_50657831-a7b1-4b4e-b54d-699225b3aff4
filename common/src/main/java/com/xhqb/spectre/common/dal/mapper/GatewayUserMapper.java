package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.GatewayUserDO;
import com.xhqb.spectre.common.dal.entity.support.TplDOSupport;
import com.xhqb.spectre.common.dal.query.GatewayUserQuery;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface GatewayUserMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_user
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_user
     *
     * @mbggenerated
     */
    int insert(GatewayUserDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_user
     *
     * @mbggenerated
     */
    int insertSelective(GatewayUserDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_user
     *
     * @mbggenerated
     */
    GatewayUserDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_user
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(GatewayUserDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_user
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(GatewayUserDO record);

    /**
     * 分页查询网关账号总页数
     *
     * @param gatewayUserQuery
     * @return
     */
    Integer countByQuery(GatewayUserQuery gatewayUserQuery);

    /**
     * 分页查询网关账号列表数据
     *
     * @param gatewayUserQuery
     * @return
     */
    List<GatewayUserDO> selectByQuery(GatewayUserQuery gatewayUserQuery);

    List<TplDOSupport> selectTplList();

    List<GatewayUserDO> selectAll();

    /**
     * 获取所有用户名
     *
     * @return 用户名集合
     */
    List<String> selectUserName();



}