package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 群发短信查询参数
 *
 * <AUTHOR>
 * @date 2021/9/17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskQuery implements Serializable {
    /**
     * 批次号
     */
    private Integer taskId;
    /**
     * 模板ID
     */
    private Integer tplId;
    /**
     * 短信类型 verify->验证码 notify->通知 market->营销 collector->催收
     */
    private String smsTypeCode;
    /**
     * 签名
     */
    private String signName;
    /**
     * 内容
     */
    private String content;
    /**
     * 状态 0：待提交；1：已提交；2：发送中；3：已发送；9：已取消
     */
    private Integer status;
    /**
     * 分页参数
     */
    private PageParameter pageParameter;
}
