package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/11/11 16:04
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShortUrlQuery implements Serializable {

    private static final long serialVersionUID = 2013221653090489608L;

    /**
     * 源URL
     */
    private String srcUrl;

    /**
     * 短链地址
     */
    private String shortUrl;

    /**
     * 状态，0：无效，1：有效
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 过期日期查询，开始日期
     */
    private String startDate;

    /**
     * 过期日期查询，结束日期
     */
    private String endDate;
    /**
     * 短链id
     */
    private String id;
    /**
     * 短链描述
     */
    private String description;
    /**
     * 分页参数
     */
    private PageParameter pageParameter;
}
