package com.xhqb.spectre.common.dal.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 模板调用流水记录
 * @TableName t_tpl_usage
 */
@Data
public class TplUsageDO implements Serializable {
    /**
     * 平台订单id
     */
    private Long orderId;

    /**
     * 应用Id
     */
    private String appCode;

    /**
     * 短信模板id
     */
    private String tplCode;

    /**
     * 短信类型id
     */
    private String smsTypeCode;

    /**
     * 签名
     */
    private String signName;

    /**
     * 调用渠道接口时间
     */
    private Integer sendTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 订单表名后缀(yyyyMM)[由PosterConsumer类中进行填充]
     * 该字段在表中不存在， 只是用于消息透传
     */
    @TableField(exist = false)
    private String tableNameSuffix;
}