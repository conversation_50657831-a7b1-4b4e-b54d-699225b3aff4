package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReachRateQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 接通率类型 appCode: 应用接通率 tplCode: 模板接通率 channelCode: 渠道接通率 provinceShortName ：省份接通率
     */
    private String reachRateType;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 短信类型Code
     */
    private String smsTypeCode;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 省份编码
     */
    private String provinceShortName;

    /**
     * 运营商编码
     */
    private String ispCode;

    /**
     * 开始时间
     */
    @NotNull(message = "startTime不能为空")
    private String startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "endTime不能为空")
    private String endTime;

    /**
     * 分页参数
     *
     *  @ignore
     */
    private PageParameter pageParameter;

    /**
     * 分组信息
     *
     * @ignore
     */
    private String groupByInfo;

    /**
     * 使用的tableName
     */
    private String tableName = "t_sms_platform_send_stat";

}
