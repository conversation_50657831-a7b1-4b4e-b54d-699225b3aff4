package com.xhqb.spectre.common.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 短信退订记录
 * @TableName t_sms_uplink_td
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SmsUplinkTdDO implements Serializable {
    /**
     * 
     */
    private Long id;

    /**
     * 平台订单id
     */
    private Long orderId;

    /**
     * 重发次数
     */
    private Integer resend;

    /**
     * 手机号密文
     */
    private String mobileEnc;

    /**
     * 手机号hash
     */
    private String mobileHash;

    /**
     * 发送时间
     */
    private Integer sendTime;

    /**
     * 回复时间
     */
    private Integer replyTime;

    /**
     * 回复内容。上行时为上行内容；状态回执时，为渠道状态码
     */
    private String replyContent;

    /**
     * 渠道CODE
     */
    private String channelCode;

    /**
     * 渠道账号ID
     */
    private Integer channelAccountId;

    /**
     * 回复内容。0：上行，1：状态
     */
    private Integer replyType;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 短信类型
     */
    private String smsTypeCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}