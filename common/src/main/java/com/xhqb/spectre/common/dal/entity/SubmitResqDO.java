package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class SubmitResqDO implements Serializable {
    private static final long serialVersionUID = 3088503221599930896L;
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submit_resq.id
     *
     * @mbggenerated
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submit_resq.channel_msg_Id
     *
     * @mbggenerated
     */
    private String channelMsgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submit_resq.order_Id
     *
     * @mbggenerated
     */
    private Long orderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submit_resq.status
     *
     * @mbggenerated
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submit_resq.result
     *
     * @mbggenerated
     */
    private String result;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submit_resq.description
     *
     * @mbggenerated
     */
    private String description;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submit_resq.recv_submit_time
     *
     * @mbggenerated
     */
    private Integer recvSubmitTime;
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_submit_resq.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

}