package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.FailResendRuleDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FailResendRuleMapper {

    int insertSelective(FailResendRuleDO ruleDO);

    int updateDeleteTagByStrategyId(@Param("strategyId") String strategyId);

    List<FailResendRuleDO> selectByStrategyId(@Param("strategyId") String strategyId);

    List<FailResendRuleDO> selectAllEnabled();
}
