package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.SmsUplinkDO;
import com.xhqb.spectre.common.dal.query.SmsUplinkQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SmsUplinkMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SmsUplinkDO record);

    int insertSelective(SmsUplinkDO record);

    SmsUplinkDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SmsUplinkDO record);

    int updateByPrimaryKey(SmsUplinkDO record);

    /**
     * 分页查询上行短信列表总页数
     *
     * @param smsUplinkQuery
     * @return
     */
    Integer countByQuery(SmsUplinkQuery smsUplinkQuery);

    /**
     * 分页查询上行短信列表数据
     *
     * @param smsUplinkQuery
     * @return
     */
    List<SmsUplinkDO> selectByQuery(SmsUplinkQuery smsUplinkQuery);

    /**
     * 批量插入
     *
     * @param smsUplinkDOList
     */
    int insertBatch(List<SmsUplinkDO> smsUplinkDOList);

    /**
     * 上行短信扫描
     *
     * @param lastId
     * @param pageSize
     * @return
     */
    List<SmsUplinkDO> scanUplink(@Param("lastId") Long lastId,@Param("pageSize") Integer pageSize);

    /**
     * 查询指定大小的上行短信记录
     * @param lastSmsUplinkId 上一次id
     * @param pageSize 大小
     * @return
     */
    List<SmsUplinkDO> selectByMarket(@Param("lastSmsUplinkId") Long lastSmsUplinkId, @Param("pageSize") int pageSize);


}