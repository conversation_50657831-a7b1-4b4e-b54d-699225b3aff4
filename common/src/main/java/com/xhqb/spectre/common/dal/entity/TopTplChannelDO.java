package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TopTplChannelDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private Integer tplId;

    private Integer channelAccountId;

    private String channelCode;

    private Integer weight;

}