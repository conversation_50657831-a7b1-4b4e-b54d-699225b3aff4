package com.xhqb.spectre.common.dal.entity.test.tool;

import lombok.Data;

import java.util.Date;

/**
 * 测试白名单表的数据对象
 */
@Data
public class TestWhiteDO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 手机号加密字段
     */
    private String mobile;

    /**
     * 手机号拥有者
     */
    private String owner;

    /**
     * 手机品牌
     */
    private String brand;

    /**
     * 系统版本
     */
    private String osVersion;

    /**
     * 状态（0：停用 1：启用）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 删除标志
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String remark;
}
