package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskBigdataDO implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 业务应用CODE
     */
    private String appCode;

    /**
     * 模板ID
     */
    private Integer tplId;

    /**
     * 文件url地址
     */
    private String fileUrl;

    /**
     * 状态 ，0：待处理；1：已处理；2：处理失败；3：处理中 ；4：已废弃(删除) ；5：已超时(taskNo查询一直处于处理中)
     */
    private Integer status;
    /**
     * 设定发送时间(unix时间戳,单位秒),设置为0表示立即发送
     */
    private Integer sendTime;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 任务编号，由批量任务手动写入
     */
    private String taskNo;
    /**
     * 群发任务批次号，由批次任务写入
     */
    private Integer taskId;

    /**
     * 大数据写入的标记
     */
    private String remark;

    /**
     * 项目用途
     */
    private String projectDesc;
}