package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.TplUsageDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【t_tpl_usage(模板调用流水记录)】的数据库操作Mapper
* @createDate 2025-06-10 15:46:07
* @Entity generator.entity.TplUsage
*/
public interface TplUsageMapper {

    int deleteByPrimaryKey(Long id);

    int insert(TplUsageDO record);

    int insertSelective(TplUsageDO record);

    TplUsageDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TplUsageDO record);

    int updateByPrimaryKey(TplUsageDO record);
    Set<String> findTplCodesBySendTime(@Param("start") Integer start, @Param("end") Integer end);
}
