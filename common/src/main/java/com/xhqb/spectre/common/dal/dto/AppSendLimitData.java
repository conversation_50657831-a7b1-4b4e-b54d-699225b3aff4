package com.xhqb.spectre.common.dal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/26 15:47
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppSendLimitData implements Serializable {

    private static final long serialVersionUID = -4004241434172057224L;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 参数key
     */
    private String limitKey;

    /**
     * 参数value
     */
    private String limitValue;
}
