package com.xhqb.spectre.common.mq;

import com.xhqb.spectre.common.dal.entity.ChannelAccount;
import lombok.*;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class MessageMQ<T> implements Serializable {

    private static final long serialVersionUID = -2232825622984469083L;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 运营商
     */
    private String isp;

    /**
     * 短信类型
     */
    private String smsCode;

    /**
     * 渠道信息
     */
    private List<ChannelCode> channelCodeSet;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 签名名称
     */
    private String signName;

    /**
     * 分片ID
     */
    private Integer sliceId;

    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 发送类型，1：实时；2：延时
     */
    private Integer sendType;

    /**
     * 发送MQ的时间
     */
    private String receiveTime;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 参数列表
     */
    private List<String> paramMap;

    /**
     * 请求来源，1：http；2：cmpp
     */
    private Integer reqSrc;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 批次ID
     */
    private Integer batchId;

    /**
     * 手机号码状态
     */
    private Long phoneStatus;

    /**
     * cmpp网关用户名
     */
    private String gatewayUserName;

    /**
     * 重发次数
     */
    private Integer resend;

    /**
     * 渠道
     */
    private ChannelAccount channelAccount;

    /**
     * 业务批次号
     */
    private String bizBatchId;

    /**
     * 是否回调消息中心（0：不回调 1：回调）
     */
    private Integer callMetis;
}
