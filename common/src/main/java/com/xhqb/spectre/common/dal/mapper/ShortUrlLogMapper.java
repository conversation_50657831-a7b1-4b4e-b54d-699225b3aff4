package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.dto.ShortUrlLogData;
import com.xhqb.spectre.common.dal.entity.ShortUrlClickCountDO;
import com.xhqb.spectre.common.dal.entity.ShortUrlLogDO;
import com.xhqb.spectre.common.dal.query.ShortUrlLogQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface ShortUrlLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url_log
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url_log
     *
     * @mbggenerated
     */
    int insert(ShortUrlLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url_log
     *
     * @mbggenerated
     */
    int insertSelective(ShortUrlLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url_log
     *
     * @mbggenerated
     */
    ShortUrlLogDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url_log
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(ShortUrlLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url_log
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(ShortUrlLogDO record);

    void insertBatch(List<ShortUrlLogDO> list);

    /**
     * 根据条件查询总条记录数
     *
     * @param shortUrlLogQuery
     * @return
     */
    Integer countByQuery(ShortUrlLogQuery shortUrlLogQuery);

    /**
     * 根据条件查询分页数据
     *
     * @param query
     * @return
     */
    List<ShortUrlLogDO> selectByQuery(ShortUrlLogQuery query);

    /**
     * 查询最新的一条记录
     *
     * @param beginTime
     * @return
     */
    ShortUrlLogDO selectLastItem(@Param("beginTime") Date beginTime);

    /**
     * 统计一段时间内的短链点击次数（根据短链编码聚合）
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    List<ShortUrlClickCountDO> sumClickCount(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 根据短链code和时间查询记录
     * @param shortCodeList 短链code
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 列表
     */
    List<ShortUrlLogDO> selectByShortCodes(@Param("list") List<String> shortCodeList, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<ShortUrlLogData> selectByTime(@Param("startOfDay")long startOfDay,@Param("endOfDay") long endOfDay);
}