package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShortUrlUpdateIndexDO implements Serializable {

    private static final long serialVersionUID = -5234804560012955774L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_update_index.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_update_index.last_update_time
     *
     * @mbggenerated
     */
    private Date lastUpdateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_update_index.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_update_index.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;
}