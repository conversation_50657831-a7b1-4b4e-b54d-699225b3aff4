package com.xhqb.spectre.common.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * t_code_mapping
 * <AUTHOR>
@Data
public class CodeMappingDO extends CodeMappingDOKey implements Serializable {
    /**
     * 渠道code
     */
    private String channelCode;

    /**
     * sumbit或devliver
     */
    private String type;

    /**
     * 渠道错误码编码
     */
    private String channelErrCode;

    /**
     * xh错误码编码
     */
    private Integer xhErrCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 错误码
     */
    private ErrorCodeDO errorCodeDO;

    private static final long serialVersionUID = 1L;
}