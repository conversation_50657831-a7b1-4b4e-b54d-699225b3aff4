package com.xhqb.spectre.common.dal.entity;

import java.util.Date;

public class SmsHisStatisDO {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.code
     *
     * @mbggenerated
     */
    private String code;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.his_send_total
     *
     * @mbggenerated
     */
    private Long hisSendTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.his_billing_total
     *
     * @mbggenerated
     */
    private Long hisBillingTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.lastmonth_send_total
     *
     * @mbggenerated
     */
    private Long lastmonthSendTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.lastmonth_billing_total
     *
     * @mbggenerated
     */
    private Long lastmonthBillingTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.curmonth_send_total
     *
     * @mbggenerated
     */
    private Long curmonthSendTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.curmonth_billing_total
     *
     * @mbggenerated
     */
    private Long curmonthBillingTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.lastday_send_total
     *
     * @mbggenerated
     */
    private Long lastdaySendTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.lastday_billing_total
     *
     * @mbggenerated
     */
    private Long lastdayBillingTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.today_send_total
     *
     * @mbggenerated
     */
    private Long todaySendTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.today_billing_total
     *
     * @mbggenerated
     */
    private Long todayBillingTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_his_statis.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_his_statis.code
     *
     * @return the value of t_sms_his_statis.code
     *
     * @mbggenerated
     */
    public String getCode() {
        return code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_his_statis.code
     *
     * @param code the value for t_sms_his_statis.code
     *
     * @mbggenerated
     */
    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_his_statis.his_send_total
     *
     * @return the value of t_sms_his_statis.his_send_total
     *
     * @mbggenerated
     */
    public Long getHisSendTotal() {
        return hisSendTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_his_statis.his_send_total
     *
     * @param hisSendTotal the value for t_sms_his_statis.his_send_total
     *
     * @mbggenerated
     */
    public void setHisSendTotal(Long hisSendTotal) {
        this.hisSendTotal = hisSendTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_his_statis.his_billing_total
     *
     * @return the value of t_sms_his_statis.his_billing_total
     *
     * @mbggenerated
     */
    public Long getHisBillingTotal() {
        return hisBillingTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_his_statis.his_billing_total
     *
     * @param hisBillingTotal the value for t_sms_his_statis.his_billing_total
     *
     * @mbggenerated
     */
    public void setHisBillingTotal(Long hisBillingTotal) {
        this.hisBillingTotal = hisBillingTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_his_statis.lastmonth_send_total
     *
     * @return the value of t_sms_his_statis.lastmonth_send_total
     *
     * @mbggenerated
     */
    public Long getLastmonthSendTotal() {
        return lastmonthSendTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_his_statis.lastmonth_send_total
     *
     * @param lastmonthSendTotal the value for t_sms_his_statis.lastmonth_send_total
     *
     * @mbggenerated
     */
    public void setLastmonthSendTotal(Long lastmonthSendTotal) {
        this.lastmonthSendTotal = lastmonthSendTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_his_statis.lastmonth_billing_total
     *
     * @return the value of t_sms_his_statis.lastmonth_billing_total
     *
     * @mbggenerated
     */
    public Long getLastmonthBillingTotal() {
        return lastmonthBillingTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_his_statis.lastmonth_billing_total
     *
     * @param lastmonthBillingTotal the value for t_sms_his_statis.lastmonth_billing_total
     *
     * @mbggenerated
     */
    public void setLastmonthBillingTotal(Long lastmonthBillingTotal) {
        this.lastmonthBillingTotal = lastmonthBillingTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_his_statis.curmonth_send_total
     *
     * @return the value of t_sms_his_statis.curmonth_send_total
     *
     * @mbggenerated
     */
    public Long getCurmonthSendTotal() {
        return curmonthSendTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_his_statis.curmonth_send_total
     *
     * @param curmonthSendTotal the value for t_sms_his_statis.curmonth_send_total
     *
     * @mbggenerated
     */
    public void setCurmonthSendTotal(Long curmonthSendTotal) {
        this.curmonthSendTotal = curmonthSendTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_his_statis.curmonth_billing_total
     *
     * @return the value of t_sms_his_statis.curmonth_billing_total
     *
     * @mbggenerated
     */
    public Long getCurmonthBillingTotal() {
        return curmonthBillingTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_his_statis.curmonth_billing_total
     *
     * @param curmonthBillingTotal the value for t_sms_his_statis.curmonth_billing_total
     *
     * @mbggenerated
     */
    public void setCurmonthBillingTotal(Long curmonthBillingTotal) {
        this.curmonthBillingTotal = curmonthBillingTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_his_statis.lastday_send_total
     *
     * @return the value of t_sms_his_statis.lastday_send_total
     *
     * @mbggenerated
     */
    public Long getLastdaySendTotal() {
        return lastdaySendTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_his_statis.lastday_send_total
     *
     * @param lastdaySendTotal the value for t_sms_his_statis.lastday_send_total
     *
     * @mbggenerated
     */
    public void setLastdaySendTotal(Long lastdaySendTotal) {
        this.lastdaySendTotal = lastdaySendTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_his_statis.lastday_billing_total
     *
     * @return the value of t_sms_his_statis.lastday_billing_total
     *
     * @mbggenerated
     */
    public Long getLastdayBillingTotal() {
        return lastdayBillingTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_his_statis.lastday_billing_total
     *
     * @param lastdayBillingTotal the value for t_sms_his_statis.lastday_billing_total
     *
     * @mbggenerated
     */
    public void setLastdayBillingTotal(Long lastdayBillingTotal) {
        this.lastdayBillingTotal = lastdayBillingTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_his_statis.today_send_total
     *
     * @return the value of t_sms_his_statis.today_send_total
     *
     * @mbggenerated
     */
    public Long getTodaySendTotal() {
        return todaySendTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_his_statis.today_send_total
     *
     * @param todaySendTotal the value for t_sms_his_statis.today_send_total
     *
     * @mbggenerated
     */
    public void setTodaySendTotal(Long todaySendTotal) {
        this.todaySendTotal = todaySendTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_his_statis.today_billing_total
     *
     * @return the value of t_sms_his_statis.today_billing_total
     *
     * @mbggenerated
     */
    public Long getTodayBillingTotal() {
        return todayBillingTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_his_statis.today_billing_total
     *
     * @param todayBillingTotal the value for t_sms_his_statis.today_billing_total
     *
     * @mbggenerated
     */
    public void setTodayBillingTotal(Long todayBillingTotal) {
        this.todayBillingTotal = todayBillingTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_his_statis.create_time
     *
     * @return the value of t_sms_his_statis.create_time
     *
     * @mbggenerated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_his_statis.create_time
     *
     * @param createTime the value for t_sms_his_statis.create_time
     *
     * @mbggenerated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_his_statis.update_time
     *
     * @return the value of t_sms_his_statis.update_time
     *
     * @mbggenerated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_his_statis.update_time
     *
     * @param updateTime the value for t_sms_his_statis.update_time
     *
     * @mbggenerated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}