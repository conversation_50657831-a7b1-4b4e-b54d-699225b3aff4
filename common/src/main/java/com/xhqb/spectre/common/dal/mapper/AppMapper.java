package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.AppDO;
import com.xhqb.spectre.common.dal.query.AppQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AppMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_app
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_app
     *
     * @mbggenerated
     */
    int insert(AppDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_app
     *
     * @mbggenerated
     */
    int insertSelective(AppDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_app
     *
     * @mbggenerated
     */
    AppDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_app
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(AppDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_app
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(AppDO record);

    List<AppDO> selectEnum();

    AppDO selectByCode(String code);

    Integer countByQuery(AppQuery appQuery);

    List<AppDO> selectByQuery(AppQuery appQuery);

    void delete(@Param("id") Integer id, @Param("operator") String operator);

    List<AppDO> selectAll();
}