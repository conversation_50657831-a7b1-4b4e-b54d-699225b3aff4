package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GatewayTplMappingDO implements Serializable {

    private static final long serialVersionUID = -3931073164334633767L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_tpl_mapping.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_tpl_mapping.tpl_id
     *
     * @mbggenerated
     */
    private Integer tplId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_tpl_mapping.tpl_content
     *
     * @mbggenerated
     */
    private String tplContent;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_tpl_mapping.creator
     *
     * @mbggenerated
     */
    private String creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_tpl_mapping.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_tpl_mapping.updater
     *
     * @mbggenerated
     */
    private String updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_tpl_mapping.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_tpl_mapping.is_delete
     *
     * @mbggenerated
     */
    private Integer isDelete;

    private String tplCode;

    private String signName;
}