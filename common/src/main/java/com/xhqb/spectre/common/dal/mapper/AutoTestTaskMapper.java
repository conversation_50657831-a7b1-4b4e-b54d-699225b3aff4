package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.AutoTestTaskDO;
import com.xhqb.spectre.common.dal.query.AutoTestTaskQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AutoTestTaskMapper {

    Integer countByQuery(@Param("query") AutoTestTaskQuery query);

    List<AutoTestTaskDO> selectByQuery(@Param("query") AutoTestTaskQuery query);

    AutoTestTaskDO selectById(@Param("id") Long id);

    Integer insert(@Param("entity") AutoTestTaskDO entity);

    Integer updateById(@Param("entity") AutoTestTaskDO entity);
}
