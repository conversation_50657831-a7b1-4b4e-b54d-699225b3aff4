package com.xhqb.spectre.common.dal.entity;

import lombok.Data;

import java.util.Date;

/**
 * 失败补发策略
 */
@Data
public class FailResendStrategyDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 策略id
     */
    private String strategyId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 执行时间段 09:12:21-19:21:10
     */
    private String timePeriod;

    /**
     * 原始模板编码 多个用英文逗号隔开
     */
    private String originalTplCodes;

    /**
     * 状态 0=启用，1=停用
     */
    private Integer status;

    /**
     * 是否删除：0=未删除，1=已删除
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
