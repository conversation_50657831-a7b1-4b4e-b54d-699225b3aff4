package com.xhqb.spectre.common.dal.query;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@Data
public class TestStatQuery implements Serializable {

    /**
     * 统计时间(yyyy-MM-dd)
     */
    private String statDate;

    /**
     * 语音类型 market、notify等
     */
    private String smsTypeCode;

    /**
     * 渠道账号Id
     */
    private Integer channelAccountId;
}
