package com.xhqb.spectre.common.dal.dto.mq;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * cmpp 消息记录
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmppDeliverResqDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long orderId;

    private Integer channelAccountId;

    private String channelCode;

    private String mobile;

    private String channelMsgId;

    private String tplCode;

    private String destTerminalId;
    /**
     * yyMMddHHmm
     */
    private String submitTime;
    /**
     * 10位时间戳
     */
    private Long recvReportTime;
    /**
     * yyMMddHHmm
     */
    private String doneTime;

    private String reportStatus;
    /**
     * 业务调用方传入的附加参数(不做处理,结果回执时原样返回)
     */
    private String addition;

    /**
     * 重发次数
     */
    private Integer resend;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;
    /**
     * 10位时间戳
     */
    private Integer recvSendTime;

    private Integer reqSrc;

    private String gatewayUserName;

    private String requestId;

    private String destId; //目的号码

    /**
     * 订单表名后缀(yyyyMM)[由PosterConsumer类中进行填充]
     */
    private String tableNameSuffix;
}