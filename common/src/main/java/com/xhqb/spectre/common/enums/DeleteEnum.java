package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 删除标志，0：未删除；1：已删除
 *
 * <AUTHOR>
 * @date 2021/10/22
 */
@Getter
@AllArgsConstructor
public enum DeleteEnum {
    NORMAL(0, "未删除"),
    DELETED(1, "已删除");

    private final Integer code;
    private final String description;

    public static boolean isDeleted(Integer code) {
        return Objects.equals(DeleteEnum.DELETED.getCode(), code);
    }
}
