package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: huangyanxiong
 * @Date: 2023/12/4 14:26
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShortUrlClickCountDO implements Serializable {

    private static final long serialVersionUID = -6904639927230695537L;

    /**
     * 短信编码
     */
    private String shortCode;

    /**
     * 点击次数
     */
    private Integer clickCount;
}
