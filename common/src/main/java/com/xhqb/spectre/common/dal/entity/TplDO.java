package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplDO implements Serializable {

    private static final long serialVersionUID = 104482830274038082L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.code
     *
     * @mbggenerated
     */
    private String code;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.sms_type_code
     *
     * @mbggenerated
     */
    private String smsTypeCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.title
     *
     * @mbggenerated
     */
    private String title;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.sign_id
     *
     * @mbggenerated
     */
    private Integer signId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.content
     *
     * @mbggenerated
     */
    private String content;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.app_code
     *
     * @mbggenerated
     */
    private String appCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.status
     *
     * @mbggenerated
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.remark
     *
     * @mbggenerated
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.creator
     *
     * @mbggenerated
     */
    private String creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.updater
     *
     * @mbggenerated
     */
    private String updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl.is_delete
     *
     * @mbggenerated
     */
    private Integer isDelete;

    /**
     * 审批状态
     */
    private Integer approveStatus;

    /**
     * 报备 Id（内容 id）
     */
    private String reportId;

    /**
     * 来源 手工创建 create、报备同步 sync
     */
    private String source;

    /**
     * 通知发送状态 0：未发送，1：已发送
     */
    private Integer notifySendStatus;


    /**
     * 模板标签
     * 默认:0; 营销类通知:1;
     */
    private Integer tag;
}