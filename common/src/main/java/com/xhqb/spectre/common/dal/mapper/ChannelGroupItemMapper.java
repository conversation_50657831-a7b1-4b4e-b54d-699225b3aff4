package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ChannelGroupItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ChannelGroupItemMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_group_item
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_group_item
     *
     * @mbggenerated
     */
    int insert(ChannelGroupItemDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_group_item
     *
     * @mbggenerated
     */
    int insertSelective(ChannelGroupItemDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_group_item
     *
     * @mbggenerated
     */
    ChannelGroupItemDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_group_item
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(ChannelGroupItemDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_group_item
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(ChannelGroupItemDO record);

    List<ChannelGroupItemDO> selectByGroupId(Integer groupId);

    void deleteByGroupId(Integer groupId);

    List<ChannelGroupItemDO> selectByGroupIdList(@Param("groupIdList") List<Integer> groupIdList);
}