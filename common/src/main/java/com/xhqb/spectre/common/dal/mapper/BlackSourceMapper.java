package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.BlackSourceDO;
import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.query.BlackSourceQuery;
import com.xhqb.spectre.common.dal.query.SignQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BlackSourceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int insert(BlackSourceDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int insertSelective(BlackSourceDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    BlackSourceDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(BlackSourceDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(BlackSourceDO record);

    List<BlackSourceDO> selectEnum(Integer status);

    Integer countByQuery(BlackSourceQuery blackSourceQuery);

    List<BlackSourceDO> selectByQuery(BlackSourceQuery blackSourceQuery);

    BlackSourceDO selectByName(String name);

    BlackSourceDO selectByCode(String code);

    void enable(@Param("id") Integer id, @Param("operator") String operator);

    void disable(@Param("id") Integer id, @Param("operator") String operator);

    void delete(@Param("id") Integer id, @Param("operator") String operator);
}