package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.SignChannelDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SignChannelMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign_channel
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign_channel
     *
     * @mbggenerated
     */
    int insert(SignChannelDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign_channel
     *
     * @mbggenerated
     */
    int insertSelective(SignChannelDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign_channel
     *
     * @mbggenerated
     */
    SignChannelDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign_channel
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SignChannelDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign_channel
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SignChannelDO record);

    List<SignChannelDO> selectBySignId(Integer signId);

    void deleteBySignId(Integer signId);
}