package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.BatchTaskBigdataDO;
import com.xhqb.spectre.common.dal.query.BatchTaskBigdataQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BatchTaskBigdataMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_bigdata
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_bigdata
     *
     * @mbggenerated
     */
    int insert(BatchTaskBigdataDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_bigdata
     *
     * @mbggenerated
     */
    int insertSelective(BatchTaskBigdataDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_bigdata
     *
     * @mbggenerated
     */
    BatchTaskBigdataDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_bigdata
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(BatchTaskBigdataDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_bigdata
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(BatchTaskBigdataDO record);

    /**
     * 分页查询大数据群发任务列表总页数
     *
     * @param batchTaskBigdataQuery
     * @return
     */
    Integer countByQuery(BatchTaskBigdataQuery batchTaskBigdataQuery);

    /**
     * 分页查询大数据群发任务列表数据
     *
     * @param batchTaskBigdataQuery
     * @return
     */
    List<BatchTaskBigdataDO> selectByQuery(BatchTaskBigdataQuery batchTaskBigdataQuery);

    /**
     * 根据状态查询最早创建的大数据任务
     *
     * @param lastId
     * @param status
     * @return
     */
    BatchTaskBigdataDO selectEarliestOneByStatus(@Param("lastId") Integer lastId,@Param("status") Integer status);

    /**
     * 任务扫描
     *
     * @param lastId
     * @param pageSize
     * @param status
     * @return
     */
    List<BatchTaskBigdataDO> jobScan(@Param("lastId") Integer lastId, @Param("pageSize") Integer pageSize, @Param("status") Integer status);
}