package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ParamsDO;
import com.xhqb.spectre.common.dal.entity.ParamsValueDO;
import com.xhqb.spectre.common.dal.query.ParamsQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ParamsValueMapper {
    int updateDeleteTagByCodeList(@Param("codeList") List<String> codeList);

    Integer countByQuery(@Param("query") ParamsQuery paramsQuery);

     List<ParamsValueDO> selectByQuery(@Param("query")ParamsQuery paramsQuery);

    int insertSelective(ParamsValueDO paramsValueDO);

    int updateDeleteTagByIdList(@Param("idList") List<Integer> idList);

    List<ParamsValueDO> selectAll();

    List<ParamsValueDO> selectByCode(String code);
}
