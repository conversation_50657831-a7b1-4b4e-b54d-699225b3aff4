package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.oa.TplApproveContent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TplApproveContentMapper {

    List<TplApproveContent> selectByFlowIdList(@Param("list") List<String> flowIdList);

    int insertSelective(TplApproveContent record);

    TplApproveContent selectByContentId(String contentId);
}
