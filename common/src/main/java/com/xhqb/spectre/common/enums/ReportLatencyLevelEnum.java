package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 短信回执耗时等级
 * <p>
 * 回执耗时等级分类：
 * <ul>
 *     <li>1: 0~5s</li>
 *     <li>2: 5~10s</li>
 *     <li>3: 10~20s</li>
 *     <li>4: 20~30s</li>
 *     <li>5: 30~60s</li>
 *     <li>6: 1-5min (60~300s)</li>
 *     <li>7: 5-30min (300~1800s)</li>
 *     <li>8: 30-60min (1800~3600s)</li>
 *     <li>9: >60min (>3600s)</li>
 * </ul>
 */
@Getter
@AllArgsConstructor
public enum ReportLatencyLevelEnum {

    LEVEL_1(1, "0~5秒", 0, 5),
    LEVEL_2(2, "5~10秒", 5, 10),
    LEVEL_3(3, "10~20秒", 10, 20),
    LEVEL_4(4, "20~30秒", 20, 30),
    LEVEL_5(5, "30~60秒", 30, 60),
    LEVEL_6(6, "1~5分钟", 60, 300),
    LEVEL_7(7, "5~30分钟", 300, 1800),
    LEVEL_8(8, "30~60分钟", 1800, 3600),
    LEVEL_9(9, "大于60分钟", 3600, Long.MAX_VALUE);

    /**
     * 等级编号
     */
    private final int level;

    /**
     * 描述
     */
    private final String description;

    /**
     * 最小耗时（包含）
     */
    private final long minSeconds;

    /**
     * 最大耗时（不包含）
     */
    private final long maxSeconds;

    /**
     * 根据耗时（秒）获取对应的等级枚举
     *
     * @param costSeconds 耗时（秒），必须 >= 0
     * @return 对应的等级枚举，若 costSeconds < 0 则返回 null
     */
    public static ReportLatencyLevelEnum fromCostTime(long costSeconds) {
        if (costSeconds < 0) {
            return null;
        }
        for (ReportLatencyLevelEnum level : values()) {
            if (costSeconds >= level.minSeconds && costSeconds < level.maxSeconds) {
                return level;
            }
        }
        return LEVEL_9;
    }

    /**
     * 根据短信提交时间和回执时间计算耗时等级
     *
     * @param submitTime 提交时间戳（秒），如 System.currentTimeMillis() / 1000
     * @param reportTime 回执时间戳（秒）
     * @return 耗时等级枚举，若任一时间为 null 或 0，或 reportTime < submitTime，则视为未完成，返回 null
     */
    public static ReportLatencyLevelEnum fromTimestamps(Long submitTime, Long reportTime) {
        if (submitTime == null || reportTime == null || submitTime == 0 || reportTime == 0 || reportTime < submitTime) {
            return null;
        }
        long costSeconds = reportTime - submitTime;
        return fromCostTime(costSeconds);
    }

    /**
     * 获取耗时范围字符串，例如 "0~5秒"
     *
     * @return 范围描述
     */
    public String getRange() {
        return description;
    }

    /**
     * 判断指定耗时（秒）是否属于此等级
     *
     * @param costSeconds 耗时（秒）
     * @return 是否匹配
     */
    public boolean matches(long costSeconds) {
        return costSeconds >= minSeconds && costSeconds < maxSeconds;
    }

    /**
     * 获取等级对应的整数值（用于数据库存储或传输）
     *
     * @return 等级编号
     */
    public int getValue() {
        return this.level;
    }
}