package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ChannelDO;
import com.xhqb.spectre.common.dal.query.ChannelQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ChannelMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel
     *
     * @mbggenerated
     */
    int insert(ChannelDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel
     *
     * @mbggenerated
     */
    int insertSelective(ChannelDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel
     *
     * @mbggenerated
     */
    ChannelDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(ChannelDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(ChannelDO record);

    List<ChannelDO> selectEnum();

    /**
     * 根据渠道编码查询渠道信息
     * 会过滤掉is_delete=1的数据
     *
     * @param code
     * @return
     */
    ChannelDO selectByCode(@Param("code") String code);

    Integer countByQuery(ChannelQuery channelQuery);

    List<ChannelDO> selectByQuery(ChannelQuery channelQuery);

    List<ChannelDO> selectAll();
}