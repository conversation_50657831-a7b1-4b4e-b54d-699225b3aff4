package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OpTimeDO implements Serializable {

    private static final long serialVersionUID = -7712738213781394652L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_op_time.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_op_time.module
     *
     * @mbggenerated
     */
    private Byte module;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_op_time.op_time
     *
     * @mbggenerated
     */
    private Integer opTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_op_time.version
     *
     * @mbggenerated
     */
    private Integer version;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_op_time.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_op_time.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;
}