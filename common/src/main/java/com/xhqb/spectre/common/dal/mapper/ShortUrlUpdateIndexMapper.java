package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ShortUrlUpdateIndexDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ShortUrlUpdateIndexMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url_update_index
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url_update_index
     *
     * @mbggenerated
     */
    int insert(ShortUrlUpdateIndexDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url_update_index
     *
     * @mbggenerated
     */
    int insertSelective(ShortUrlUpdateIndexDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url_update_index
     *
     * @mbggenerated
     */
    ShortUrlUpdateIndexDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url_update_index
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(ShortUrlUpdateIndexDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_short_url_update_index
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(ShortUrlUpdateIndexDO record);

    ShortUrlUpdateIndexDO selectOne();
}