package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OpLogDO implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 所属系统，例如spectre-admin
     */
    private String sysName;
    /**
     * 操作类型 1->查询 2->新增 3->修改 4->删除
     */
    private Integer opType;

    /**
     * 请求方法
     */
    private String reqMethod;

    /**
     * 请求URI
     */
    private String reqUri;

    /**
     * 请求action的方法名称
     */
    private String actionName;

    /**
     * 请求参数
     */
    private String reqParam;

    /**
     * 请求IP
     */
    private String reqIp;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private Integer opTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}