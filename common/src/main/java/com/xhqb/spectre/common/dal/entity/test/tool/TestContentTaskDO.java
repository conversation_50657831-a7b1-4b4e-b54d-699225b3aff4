package com.xhqb.spectre.common.dal.entity.test.tool;

import lombok.Data;

import java.util.Date;

/**
 * 文案测试主表的数据对象
 */
@Data
public class TestContentTaskDO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 测试任务ID
     */
    private String taskId;

    /**
     * 测试名称
     */
    private String name;

    /**
     * 测试短信文案
     */
    private String content;

    /**
     * 手机号和品牌配置
     */
    private String brandConfig;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态（0：初始华 1：执行中 2：成功 3：失败 4：取消）
     */
    private Integer taskStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 检测状态
     */
    private Integer checkStatus;

    /**
     * 提交时间
     */
    private Integer submitTime;

    /**
     * 完成时间
     */
    private Integer completeTime;
}
