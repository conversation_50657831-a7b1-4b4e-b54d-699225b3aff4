package com.xhqb.spectre.common.dal.dto.mq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/12/2 17:45
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class GatewayDeliverDTO implements Serializable {

    private static final long serialVersionUID = 2416959569236235649L;

    /**
     * 网关消息ID
     */
    private String msgId;

    /**
     * 网关用户名
     */
    private String userName;

    /**
     * 提交时间，格式：YYMMDDHHMM
     */
    private String submitTime;

    /**
     * 完成时间，格式：YYMMDDHHMM
     */
    private String doneTime;

    /**
     * 短信状态
     */
    private String smsStatus;

    /**
     * 目标手机
     */
    private String destTerminalId;

    /**
     * 消息状态，1：已提交；2：提交失败；3：已送达；4：送达失败
     */
    private Integer messageStatus;

    /**
     * 推送回执的时间
     */
    private Integer deliveredTime;
}
