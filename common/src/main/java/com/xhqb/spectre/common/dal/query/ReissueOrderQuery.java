package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReissueOrderQuery implements Serializable {

    private static final long serialVersionUID = -1679168624349441362L;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    /**
     * 页数
     */
    @NotNull(message = "页数不能为空")
    private Integer pageNum;

    /**
     * 页大小
     */
    @NotNull(message = "页大小不能为空")
    private Integer pageSize;

    private String appCode;

    private PageParameter pageParameter;
}
