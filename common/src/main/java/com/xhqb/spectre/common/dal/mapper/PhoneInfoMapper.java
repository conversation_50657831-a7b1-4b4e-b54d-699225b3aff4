package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.PhoneInfoDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PhoneInfoMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(PhoneInfoDO record);

    int insertSelective(PhoneInfoDO record);

    PhoneInfoDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PhoneInfoDO record);

    int updateByPrimaryKey(PhoneInfoDO record);
}