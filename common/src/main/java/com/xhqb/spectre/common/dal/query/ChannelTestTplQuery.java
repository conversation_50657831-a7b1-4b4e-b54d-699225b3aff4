package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelTestTplQuery implements Serializable {

    /**
     * 渠道测试模板id
     */
    private Integer id;

    /**
     * 渠道账号
     */
    private String channelAccountId;

    /**
     * 号码来源
     */
    private Integer source;

    /**
     * 启用状态
     */
    private Integer status;

    /**
     * 短信模版id
     */
    private Integer smsTplId;

    /**
     * tplCode
     */
    private String tplCode;

    /**
     * 分页参数
     */
    private PageParameter pageParameter;
}
