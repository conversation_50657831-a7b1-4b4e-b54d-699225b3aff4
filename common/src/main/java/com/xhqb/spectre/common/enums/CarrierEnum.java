package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 运营商枚举
 */
@Getter
@AllArgsConstructor
public enum CarrierEnum {

    /**
     * 移动
     */
    MOBILE(1, "CMCC", "中国移动"),
    /**
     * 联通
     */
    UNICOM(2, "CUCC", "中国联通"),
    /**
     * 电信
     */
    TELECOM(3, "CTCC", "中国电信"),
    /**
     * 虚拟运营商
     */
    VIRTUAL(4, "VIRTUAL", "虚拟运营商"),

    CBN(5, "CBN", "中国广电"),

    UNKNOWN(9, "UNKNOWN", "未知"),
    ;

    // 运营商id
    private int id;

    // 运营商code
    private String code;

    // 运营商名称
    private String name;

    /**
     * 根据运营商code获取运营商枚举
     *
     * @param carrier 运营商code
     * @return 运营商枚举
     */
    public static CarrierEnum getCarrier(String carrier) {
        if (carrier == null) {
            return CarrierEnum.UNKNOWN;
        }
        return Arrays.stream(CarrierEnum.values())
                .filter(carrierEnum -> carrierEnum.getCode().equals(carrier))
                .findFirst()
                .orElse(CarrierEnum.UNKNOWN);
    }

    public static String getCode(Integer id) {
        return Arrays.stream(CarrierEnum.values())
                .filter(carrierEnum -> carrierEnum.getId() == id)
                .findFirst()
                .orElse(CarrierEnum.UNKNOWN).getCode();
    }
}

