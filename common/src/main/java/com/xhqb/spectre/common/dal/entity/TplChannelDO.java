package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplChannelDO implements Serializable {

    private static final long serialVersionUID = -7536901339331876439L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_channel.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_channel.tpl_id
     *
     * @mbggenerated
     */
    private Integer tplId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_channel.channel_account_id
     *
     * @mbggenerated
     */
    private Integer channelAccountId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_channel.status
     *
     * @mbggenerated
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_channel.channel_tpl_id
     *
     * @mbggenerated
     */
    private String channelTplId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_channel.isps
     *
     * @mbggenerated
     */
    private String isps;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_channel.area_filter_type
     *
     * @mbggenerated
     */
    private Integer areaFilterType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_channel.areas
     *
     * @mbggenerated
     */
    private String areas;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_channel.weight
     *
     * @mbggenerated
     */
    private Integer weight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_channel.remark
     *
     * @mbggenerated
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_channel.tpl_content
     *
     * @mbggenerated
     */
    private String tplContent;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_channel.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_tpl_channel.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;
}