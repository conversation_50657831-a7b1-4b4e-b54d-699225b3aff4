package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/10/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskBigdataQuery implements Serializable {

    /**
     * 批次ID
     */
    private Integer id;
    /**
     * 业务应用CODE
     */
    private String appCode;
    /**
     * 模板ID
     */
    private Integer tplId;
    /**
     * 文件地址(模糊查询)
     */
    private String fileUrl;

    /**
     * 状态 ，0：待处理；1：已处理；2：处理失败；3：处理中 ；4：已废弃
     */
    private Integer status;
    /**
     * 群发任务ID
     */
    private Integer taskId;

    /**
     * 分页参数
     */
    private PageParameter pageParameter;
}
