package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MultipleCompareQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 模板编码(多个用英文逗号隔开) 不传该参数 请传''
     */
    private String tplCode;

    /**
     * 短信类型Code
     */
    private String smsTypeCode;

    /**
     * 渠道编码(多个用英文逗号隔开) 不传该参数 请传''
     */
    private String channelCode;

    /**
     * 省份编码
     */
    private String provinceShortName;

    /**
     * 运营商编码
     */
    private String ispCode;

    /**
     * 开始时间
     */
    @NotNull(message = "startTime不能为空")
    private String startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "endTime不能为空")
    private String endTime;

    /**
     * 模板编码list
     *
     * @ignore
     */
    private List<String> tplCodes;


    /**
     * 渠道编码list
     *
     * @ignore
     */
    private List<String> channelCodes;

    /**
     * 分页参数
     *
     *  @ignore
     */
    private PageParameter pageParameter;

    /**
     * 分组信息
     *
     * @ignore
     */
    private String groupByInfo;

    /**
     * 使用的tableName
     */
    private String tableName = "t_sms_platform_send_stat";

}
