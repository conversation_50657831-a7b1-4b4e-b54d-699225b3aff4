package com.xhqb.spectre.common.dal.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * 应用每日统计请求参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppDailyStatsQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;
    /**
     * 结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 账户ID（可选）
     */
    private Integer accountId;
    /**
     * 账户类别（可选）
     */
    private Integer accountCategory;

    private PageParameter pageParameter;
}
