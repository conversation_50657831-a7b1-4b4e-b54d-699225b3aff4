package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.SignDO;
import com.xhqb.spectre.common.dal.query.SignQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SignMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int insert(SignDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int insertSelective(SignDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    SignDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SignDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sign
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SignDO record);

    List<SignDO> selectEnum(Integer status);

    Integer countByQuery(SignQuery signQuery);

    List<SignDO> selectByQuery(SignQuery signQuery);

    SignDO selectByName(String name);

    SignDO selectByCode(String code);

    void enable(@Param("id") Integer id, @Param("operator") String operator);

    void disable(@Param("id") Integer id, @Param("operator") String operator);

    void delete(@Param("id") Integer id, @Param("operator") String operator);

    List<SignDO> selectAll();

    List<SignDO> selectByIds(@Param("signIds") List<Integer> signIds);
}