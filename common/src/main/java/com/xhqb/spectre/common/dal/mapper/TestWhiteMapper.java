package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.BrandInfoDO;
import com.xhqb.spectre.common.dal.entity.test.tool.TestWhiteDO;
import com.xhqb.spectre.common.dal.query.TestWhiteQuery;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface TestWhiteMapper {

    // select by id
    TestWhiteDO selectByPrimaryKey(Integer id);

    // insert
    int insertBySelective(TestWhiteDO record);

    int updateByPrimaryKeySelective(TestWhiteDO record);

    Integer countByQuery(@Param("query") TestWhiteQuery testWhiteQuery);

    List<TestWhiteDO> selectByQuery(@Param("query") TestWhiteQuery testWhiteQuery);

    TestWhiteDO selectByMobile(@Param("mobile") String mobile);

    List<TestWhiteDO> selectByBrand(@Param("brand") String brand);

    @MapKey("brand")
    Map<String, Integer> countAllByBrand();

    List<BrandInfoDO> selectByBrandInfo();

}
