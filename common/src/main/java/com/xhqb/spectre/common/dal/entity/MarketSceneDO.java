package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 营销场景
 *
 * <AUTHOR>
 * @date 2022/9/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MarketSceneDO {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 状态，0：无效，1：有效
     */
    private Integer status;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志，0：未删除；1：已删除
     */
    private Integer isDelete;

}