package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.OpLogDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OpLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_op_log
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_op_log
     *
     * @mbggenerated
     */
    int insert(OpLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_op_log
     *
     * @mbggenerated
     */
    int insertSelective(OpLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_op_log
     *
     * @mbggenerated
     */
    OpLogDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_op_log
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(OpLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_op_log
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(OpLogDO record);
}