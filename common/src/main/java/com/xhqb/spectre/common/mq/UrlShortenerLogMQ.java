package com.xhqb.spectre.common.mq;

import com.xhqb.spectre.common.dal.entity.ShortUrlLogDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/11/12
 */
@SuperBuilder
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class UrlShortenerLogMQ implements Serializable {

    private static final long serialVersionUID = -1041395308200030462L;

    private Integer clickTime;
    private String shortCode;
    private String referrer;
    private String platform;
    private String brand;
    private String model;
    private String os;
    private String osVersion;
    private String ip;
    private String province;
    private String city;
    private String isp;
    private String userAgent;
    private String userTplCode;

    public static UrlShortenerLogMQ buildUrlShortenerLog(ShortUrlLogDO shortUrlLogDO) {
        return Optional.ofNullable(shortUrlLogDO).map(item -> UrlShortenerLogMQ.builder()
                .clickTime(shortUrlLogDO.getClickTime())
                .shortCode(shortUrlLogDO.getShortCode())
                .referrer(shortUrlLogDO.getReferrer())
                .platform(shortUrlLogDO.getPlatform())
                .brand(shortUrlLogDO.getBrand())
                .model(shortUrlLogDO.getModel())
                .os(shortUrlLogDO.getOs())
                .osVersion(shortUrlLogDO.getOsVersion())
                .ip(shortUrlLogDO.getIp())
                .province(shortUrlLogDO.getProvince())
                .city(shortUrlLogDO.getCity())
                .isp(shortUrlLogDO.getIsp())
                .userAgent(shortUrlLogDO.getUserAgent())
                .userTplCode(shortUrlLogDO.getUserTplCode())
                .build()).orElse(null);
    }

}
