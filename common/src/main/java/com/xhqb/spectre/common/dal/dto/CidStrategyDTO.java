package com.xhqb.spectre.common.dal.dto;

import com.xhqb.spectre.common.dal.entity.CidStrategyDO;
import lombok.Data;

import java.io.Serializable;

/**
 * Cid校验策略表
 *
 * <AUTHOR>
 * @date 2021/11/26
 */
@Data
public class CidStrategyDTO implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 策略值
     */
    private String strategyValue;

    /**
     * 类型 0->用户状态 1->用户完件
     */
    private String type;

    /**
     * 状态 0->未使用(未选中) 1->使用(已选中)
     */
    private Integer status;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 策略分组
     */
    private String strategyGroup;

    /**
     * 转换成DO
     *
     * @return
     */
    public CidStrategyDO toDO() {
        return CidStrategyDO.builder()
                // 主键
                .id(this.id)
                // 策略值
                .strategyValue(this.strategyValue)
                // 类型 0->用户状态 1->用户完件
                .type(this.type)
                // 状态 0->未使用(未选中) 1->使用(已选中)
                .status(this.status)
                // 描述信息
                .description(this.description)
                // 策略分组
                .strategyGroup(this.strategyGroup)
                .build();
    }

}
