package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ShortUrlTplDO;
import com.xhqb.spectre.common.dal.query.ShortUrlTplQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ShortUrlTplDOMapper {
    ShortUrlTplDO selectByTplCode(String tplCode);

    int insertSelective(ShortUrlTplDO addModel);

    ShortUrlTplDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ShortUrlTplDO updateModel);

    int updateDeleteTagById(Integer id);

    Integer countByQuery(ShortUrlTplQuery shortUrlTplQuery);

    List<ShortUrlTplDO> selectByQuery(ShortUrlTplQuery shortUrlTplQuery);

    List<ShortUrlTplDO> selectByCurDate();

    int updateClickCountByTplCode(@Param("tplCode") String tplCode, @Param("nowClickCount") int nowClickCount);

    ShortUrlTplDO selectByBizId(@Param("bizId") String bizId);
}
