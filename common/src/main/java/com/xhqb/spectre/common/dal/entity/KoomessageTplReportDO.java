package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KoomessageTplReportDO {

    private Long id;

    /**
     * 智能信息模板ID
     */
    private String tplId;

    private String tplName;

    /**
     * 日期
     */
    private Date reportDate;

    /**
     * 实际已解析数
     */
    private Integer resolvingTimes;

    /**
     * 消息曝光数
     */
    private Long exposeUv;

    /**
     * 消息曝光次数
     */
    private Long exposePv;

    /**
     * 消息点击数
     */
    private Long clickUv;

    /**
     * 消息点击次数
     */
    private Long clickPv;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     * 0未删除
     */
    private Boolean isDelete;
}