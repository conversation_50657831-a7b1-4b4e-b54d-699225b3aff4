package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.FailResendRecordDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface FailResendRecordMapper {

    int insert(FailResendRecordDO record);

    FailResendRecordDO selectByPrimaryKey(@Param("id") Long id);

    int updateByPrimaryKey(FailResendRecordDO record);

    FailResendRecordDO selectByOriginalOrderId(@Param("originalOrderId") String originalOrderId);

    List<FailResendRecordDO> selectByStatus(@Param("status") Integer status);

    List<FailResendRecordDO> selectByStatusList(@Param("statusList") List<Integer> statusList);


    List<FailResendRecordDO> selectByStatusWithLimit(@Param("status") Integer status, @Param("limit") Integer limit);

    List<FailResendRecordDO> selectPendingRecordsAfterDate(@Param("status") Integer status,
                                                           @Param("afterDate") Date afterDate,
                                                           @Param("limit") Integer limit);

    int updateStatusById(@Param("id") Long id,
                        @Param("status") Integer status,
                        @Param("updateTime") Date updateTime);

    int updateStatusByIdWithCondition(@Param("id") Long id,
                                     @Param("oldStatus") Integer oldStatus,
                                     @Param("newStatus") Integer newStatus,
                                     @Param("updateTime") Date updateTime);
}
