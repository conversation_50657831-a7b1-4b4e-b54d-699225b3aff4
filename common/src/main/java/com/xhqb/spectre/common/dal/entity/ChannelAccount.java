package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelAccount {

    Integer id;
    String channelCode;
    String smsTypeCode;
    String name;
    String key;
    String jsonMapping;
    Integer price;
    Integer protocol;
    Integer weight;
    Integer status;
    Integer isTplFreeAudit;

    public static ChannelAccount buildChannelAccount(ChannelAccountDO item) {
        return ChannelAccount.builder()
            .id(item.getId())
            .channelCode(item.getChannelCode())
            .smsTypeCode(item.getSmsTypeCode())
            .name(item.getName())
            .key(item.getKey())
            .jsonMapping(item.getJsonMapping())
            .price(item.getPrice())
            .protocol(item.getProtocol())
            .weight(item.getWeight())
            .status(item.getStatus())
            .isTplFreeAudit(item.getIsTplFreeAudit())
            .build();
    }
}