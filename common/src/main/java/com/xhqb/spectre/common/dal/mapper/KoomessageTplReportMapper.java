package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.KoomessageTplReportDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface KoomessageTplReportMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_koomessage_tpl_report
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_koomessage_tpl_report
     *
     * @mbggenerated
     */
    int insert(KoomessageTplReportDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_koomessage_tpl_report
     *
     * @mbggenerated
     */
    int insertSelective(KoomessageTplReportDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_koomessage_tpl_report
     *
     * @mbggenerated
     */
    KoomessageTplReportDO selectByPrimaryKey(Integer id);

    KoomessageTplReportDO selectByReportDate(@Param("tplId") String tplId, @Param("reportDate") Date reportDate);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_koomessage_tpl_report
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(KoomessageTplReportDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_koomessage_tpl_report
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(KoomessageTplReportDO record);
}