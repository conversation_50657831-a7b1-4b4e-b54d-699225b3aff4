package com.xhqb.spectre.common.dal.entity;

import com.xhqb.spectre.common.constant.EntitySeqNameConstants;
import com.xhqb.spectre.common.constant.GenericConstants;
import com.xhqb.spectre.common.enums.StatusEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * cmpp 消息记录
 */
@ToString(callSuper = true)
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class CmppRecord extends BaseEntity {

    /**
     * 业务id,用来和sms服务关联
     */
    private Long orderId;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 短信拆分后总数量
     */
    private Integer pkTotal;

    /**
     * 短信拆分后第几条
     */
    private Integer pkNumber;

    /**
     * cmpp协议中submit事件请求的序列号id
     */
    private Integer submitSeqId;

    /**
     * 供应商
     */
    private String partnerPlatform;

    /**
     * 供应商返回状态
     */
    private String platformStatus;

    /**
     * 供应商返回状态时间(YYMMDDHHMM（YY为年的后两位00-99，MM：01-12，DD：01-31，HH：00-23，MM：00-59）)
     */
    private String platformTime;

    /**
     * 短信发送报告msgId
     */
    private String reportMsgId;

    /**
     * 运营商回执状态
     */
    private String reportStatus;

    /**
     * 运营商回执时间(YYMMDDHHMM（YY为年的后两位00-99，MM：01-12，DD：01-31，HH：00-23，MM：00-59）)
     */
    private String reportTime;

    /**
     * 消息记录状态(同linux权限设计, {@link })
     */
    private Integer recordStatus;


    /**
     * 业务调用方传入的附加参数(不做处理,结果回执时原样返回)
     */
    private String addition;

    @Override
    public String getSeqName() {
        return EntitySeqNameConstants.CMPP_RECORD_SEQ_NAME;
    }

    public void init(String id) {
        Date now = new Date();
        super.setId(id);
        super.setStatus(StatusEnum.ENABLE.getCode());
        super.setVersion(1);
        super.setCreateTime(now);
        super.setCreator(GenericConstants.SYS_CREATOR);
        super.setModifyTime(now);
        super.setModifier(GenericConstants.SYS_MODIFIER);
    }
}