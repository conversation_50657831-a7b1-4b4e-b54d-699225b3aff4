package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ChannelAccountLogDO;

public interface ChannelAccountLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account_log
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account_log
     *
     * @mbggenerated
     */
    int insert(ChannelAccountLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account_log
     *
     * @mbggenerated
     */
    int insertSelective(ChannelAccountLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account_log
     *
     * @mbggenerated
     */
    ChannelAccountLogDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account_log
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(ChannelAccountLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account_log
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(ChannelAccountLogDO record);

    /**
     * 查询数量
     *
     * @return
     */
    int selectCount();
}