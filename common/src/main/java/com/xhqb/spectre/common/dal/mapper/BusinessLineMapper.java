package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.BusinessLineDO;
import com.xhqb.spectre.common.dal.query.BusinessLineQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BusinessLineMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_business_line
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_business_line
     *
     * @mbggenerated
     */
    int insert(BusinessLineDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_business_line
     *
     * @mbggenerated
     */
    int insertSelective(BusinessLineDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_business_line
     *
     * @mbggenerated
     */
    BusinessLineDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_business_line
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(BusinessLineDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_business_line
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(BusinessLineDO record);

    /**
     * 查询所有的业务线信息信息
     *
     * @param status 状态，0：无效，1：有效 , 为空查所有
     * @return
     */
    List<BusinessLineDO> listAll(Integer status);

    /**
     * 业务线查询总数量
     *
     * @param businessLineQuery
     * @return
     */
    Integer countByQuery(BusinessLineQuery businessLineQuery);

    /**
     * 业务线查询列表
     *
     * @param businessLineQuery
     * @return
     */
    List<BusinessLineDO> selectByQuery(BusinessLineQuery businessLineQuery);

    /**
     * 删除业务线与模板关联信息
     *
     * @param tplId
     * @param creator
     */
    void deleteBusinessLineTpl(@Param("tplId") Integer tplId, @Param("creator") String creator);

    /**
     * 保存业务线与模板关联信息
     *
     * @param tplId
     * @param businessLineId
     * @param creator
     */
    void insertBusinessLineTpl(@Param("tplId") Integer tplId, @Param("businessLineId") Integer businessLineId, @Param("creator") String creator);

    /**
     * 根据模板ID查询关联的业务线ID列表
     *
     * @param tplId
     * @return
     */
    List<Integer> selectBusinessLineIdByTplId(@Param("tplId") Integer tplId);

    BusinessLineDO selectByName(@Param("name") String businessLineName);

}