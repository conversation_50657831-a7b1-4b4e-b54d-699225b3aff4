package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.BatchTaskParamDO;
import com.xhqb.spectre.common.dal.query.BatchTaskParamQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BatchTaskParamMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_param
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_param
     *
     * @mbggenerated
     */
    int insert(BatchTaskParamDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_param
     *
     * @mbggenerated
     */
    int insertSelective(BatchTaskParamDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_param
     *
     * @mbggenerated
     */
    BatchTaskParamDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_param
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(BatchTaskParamDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_param
     *
     * @mbggenerated
     */
    int updateByPrimaryKeyWithBLOBs(BatchTaskParamDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_param
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(BatchTaskParamDO record);

    /**
     * 批量插入
     *
     * @param list
     */
    void batchInsert(@Param("list") List<BatchTaskParamDO> list);

    /**
     * 根据群发任务ID查询所有的任务参数列表
     *
     * @param taskId
     * @return
     */
    List<BatchTaskParamDO> selectByTaskId(@Param("taskId") Integer taskId);

    /**
     * 根据ID批量删除参数信息
     *
     * @param list
     * @return
     */
    int batchDeleteById(@Param("list") List<Integer> list);

    /**
     * 根据群发任务ID删除分片数据
     *
     * @param taskId
     * @return
     */
    int deleteByTaskId(@Param("taskId") Integer taskId);

    /**
     * 根据群发任务ID+状态查询所有的任务参数列表
     *
     * @param taskId
     * @param sendStatus
     * @param needMapping 为空则不查询param_json字段
     * @return
     */
    List<BatchTaskParamDO> selectByTaskIdAndStatus(@Param("taskId") Integer taskId, @Param("sendStatus") Integer sendStatus, @Param("needMapping") Integer needMapping);

    /**
     * 根据ID查询所有的任务参数列表
     *
     * @param list
     * @return
     */
    List<BatchTaskParamDO> selectByIdList(@Param("list") List<Integer> list);

    /**
     * 根据id更新taskId
     *
     * @param list
     * @param taskId
     * @return
     */
    int updateTaskIdByIdList(@Param("list") List<Integer> list, @Param("taskId") Integer taskId);

    /**
     * 更新发送状态
     *
     * @param id
     * @param destStatus   最终写入库的状态
     * @param sourceStatus 当前认为数据的状态
     * @return
     */
    int updateSendStatusById(@Param("id") Integer id, @Param("destStatus") Integer destStatus, @Param("sourceStatus") Integer sourceStatus);

    /**
     * 根据群发任务ID查询所有的任务参数列表 并根据needMapping控制是否需要参数数据信息
     * <p>
     * 包括被删除的数据一并被查询出来
     *
     * @param taskId
     * @param needMapping
     * @return
     */
    List<BatchTaskParamDO> selectByTaskIdAndMapping(@Param("taskId") Integer taskId, @Param("needMapping") Integer needMapping);

    /**
     * 根据ID查询所有的任务参数列表
     *
     * @param list
     * @return
     */
    List<BatchTaskParamDO> selectByIdListAndMapping(@Param("list") List<Integer> list);


    /**
     * 批量更新taskId和mapping参数
     *
     * @param list
     */
    void batchUpdateTaskIdAndMapping(@Param("list") List<BatchTaskParamDO> list);


    /**
     * 分页查询群发参数列表总页数
     *
     * @param batchTaskParamQuery
     * @return
     */
    Integer countByQuery(BatchTaskParamQuery batchTaskParamQuery);

    /**
     * 分页查询群发参数列表数据
     *
     * @param batchTaskParamQuery
     * @return
     */
    List<BatchTaskParamDO> selectByQuery(BatchTaskParamQuery batchTaskParamQuery);

    /**
     * 灰度数据提取
     *
     * @param taskId
     * @param pageSize
     * @return
     */
    List<BatchTaskParamDO> fetchAbDataList(@Param("taskId") Integer taskId, @Param("pageSize") Integer pageSize);

}