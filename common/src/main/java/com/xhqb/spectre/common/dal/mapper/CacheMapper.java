package com.xhqb.spectre.common.dal.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;

import java.util.Date;

/**
 * 缓存mapper
 * <p>
 * 提供查询缓存的语句，与数据库表没有直接关联
 */
@Mapper
public interface CacheMapper {

    /**
     * 查询表最新更新的一条记录
     *
     * @param tableName  表名称
     * @param updateTime 更新时间字段 如(update_time)
     * @return
     */
    @Select({"select ${updateTime} from ${tableName} order by ${updateTime} desc limit 1"})
    @ResultType(Date.class)
    Date lastUpdateTime(@Param("tableName") String tableName, @Param("updateTime") String updateTime);

}
