package com.xhqb.spectre.common.dal.entity;

import java.io.Serializable;
import lombok.Data;

/**
 * t_code_mapping
 * <AUTHOR>
@Data
public class CodeMappingDOKey implements Serializable {
    /**
     * 渠道code
     */
    private String channelCode;

    /**
     * sumbit或devliver
     */
    private String type;

    /**
     * 渠道错误码编码
     */
    private String channelErrCode;

    private static final long serialVersionUID = 1L;
}