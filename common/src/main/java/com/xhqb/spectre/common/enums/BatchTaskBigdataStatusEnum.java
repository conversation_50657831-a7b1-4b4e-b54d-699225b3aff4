package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 大数据群发任务状态
 * <p>
 * 0 -> 待处理
 * 1 -> 已处理
 * 2 -> 处理失败
 * 3 -> 处理中
 * 4 -> 已废弃(删除)
 * 5 -> 已超时(taskNo查询一直处于处理中)
 *
 * <AUTHOR>
 * @date 2021/10/28
 */
@Getter
@AllArgsConstructor
public enum BatchTaskBigdataStatusEnum {

    UN_PROCESS(0, "待处理"),
    PROCESSED(1, "已处理"),
    FAIL(2, "处理失败"),
    PROCESSING(3, "处理中"),
    DISCARD(4, "已废弃"),
    TIMEOUT(5, "已超时");

    private final Integer code;
    private final String description;
}
