package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.SmsTypeDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SmsTypeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_type
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_type
     *
     * @mbggenerated
     */
    int insert(SmsTypeDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_type
     *
     * @mbggenerated
     */
    int insertSelective(SmsTypeDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_type
     *
     * @mbggenerated
     */
    SmsTypeDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_type
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SmsTypeDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_type
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SmsTypeDO record);
}