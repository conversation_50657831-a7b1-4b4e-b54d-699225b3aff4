package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.oa.TplOaApprove;
import com.xhqb.spectre.common.dal.query.TplOaReportQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TplOaApproveMapper {

    TplOaApprove selectByPrimaryKey(Long id);

    int insertSelective(TplOaApprove record);

    List<TplOaApprove> selectByStatusList(@Param("list") List<Integer> statusList);

    List<TplOaApprove> selectByQuery(@Param("query") TplOaReportQuery query);

    int countByQuery(@Param("query")TplOaReportQuery query);

    int updateStatusByFlowId(@Param("flowId")String flowId,@Param("status") int status);

    TplOaApprove selectByFlowId(String flowId);
}
