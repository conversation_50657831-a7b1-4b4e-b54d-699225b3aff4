package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/23 17:36
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsOrderQuery implements Serializable {

    private static final long serialVersionUID = -1668033278426724450L;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 运营商
     */
    private String isp;

    /**
     * 批次ID
     */
    private Integer batchId;

    /**
     * 发送状态，0：成功，-1：未知，1：失败
     */
    private Integer sendStatus;

    /**
     * 回执状态，0：成功，-1：未知，1：失败
     */
    private Integer reportStatus;

    /**
     * 创建开始时间
     */
    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    /**
     * 创建结束时间
     */
    @NotBlank(message = "结束时间不能为空")
    private String endTime;

    //timestamp

    /**
     * 发送开始时间
     */

    private long startTimestamp;

    /**
     * 发送结束时间
     */
    private long endTimestamp;

    /**
     * 渠道msgid
     */
    private String channelMsgId;

    private PageParameter pageParameter;

    /**
     * 签名
     */
    private String signName;

    /**
     * 请求 id
     */
    private String requestId;

    /**
     * cid
     */
    private String cid;
}
