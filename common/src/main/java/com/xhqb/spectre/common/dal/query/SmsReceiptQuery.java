package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/30 15:18
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsReceiptQuery implements Serializable {

    private static final long serialVersionUID = 3776936226733009065L;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道账号ID
     */
    private Integer channelAccountId;

    /**
     * 回执状态，0：成功，-1：未知，其余失败
     */
    private Integer status;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    private PageParameter pageParameter;
}
