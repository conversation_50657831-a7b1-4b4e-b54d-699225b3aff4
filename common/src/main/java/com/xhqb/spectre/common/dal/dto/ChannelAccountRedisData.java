package com.xhqb.spectre.common.dal.dto;

import com.xhqb.spectre.common.dal.entity.ChannelAccountDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 渠道账户redis缓存
 *
 * <AUTHOR>
 * @date 2021/9/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelAccountRedisData implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 短信类型编码
     */
    private String smsTypeCode;
    /**
     * 账号名称
     */
    private String name;
    /**
     * 渠道账号
     */
    private String key;
    /**
     * 渠道账号json配置参数
     */
    private String jsonMapping;
    /**
     * 费率，千分位存储
     */
    private Integer price;
    /**
     * 协议，1：http；2：cmpp
     */
    private Integer protocol;
    /**
     * 权重(综合分)
     */
    private Integer weight;
    /**
     * 状态，0：无效，1：有效
     */
    private Integer status;
    /**
     * 是否免模板审核，1：是；0：否
     */
    private Integer isTplFreeAudit;
    /**
     * 支持的签名ID列表
     */
    private String signIds;

    /**
     * 查询数据详情展现
     *
     * @param channelAccountDO
     * @return
     */
    public static ChannelAccountRedisData buildRedisInfo(ChannelAccountDO channelAccountDO) {
        return ChannelAccountRedisData.builder()
                // 主键
                .id(channelAccountDO.getId())
                // 渠道编码
                .channelCode(channelAccountDO.getChannelCode())
                // 短信类型编码
                .smsTypeCode(channelAccountDO.getSmsTypeCode())
                // 账号名称
                .name(channelAccountDO.getName())
                // 渠道账号
                .key(channelAccountDO.getKey())
                // 渠道账号json配置参数
                .jsonMapping(channelAccountDO.getJsonMapping())
                // 费率，千分位存储
                .price(channelAccountDO.getPrice())
                // 协议，1：http；2：cmpp
                .protocol(channelAccountDO.getProtocol())
                // 权重
                .weight(channelAccountDO.getWeight())
                // 状态，0：无效，1：有效
                .status(channelAccountDO.getStatus())
                // 是否免模板审核，1：是；0：否
                .isTplFreeAudit(channelAccountDO.getIsTplFreeAudit())
                // 支持的签名ID列表
                .signIds(channelAccountDO.getSignIds())
                .build();
    }
}
