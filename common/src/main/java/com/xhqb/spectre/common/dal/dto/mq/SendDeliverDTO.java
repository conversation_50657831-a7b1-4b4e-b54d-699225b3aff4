package com.xhqb.spectre.common.dal.dto.mq;

import com.xhqb.spectre.common.dal.entity.CmppRecord;
import com.xhqb.spectre.common.dal.entity.HttpRecord;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SendDeliverDTO implements Serializable {

    private static final long serialVersionUID = 4155939602737835986L;

    String type;

    HttpRecord httpRecord;

    CmppRecord cmppRecord;
}
