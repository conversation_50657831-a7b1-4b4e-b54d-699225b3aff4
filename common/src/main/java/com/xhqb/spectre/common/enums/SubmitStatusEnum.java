package com.xhqb.spectre.common.enums;

public enum SubmitStatusEnum {

    NEW(0, "未收到"),
    SUCCESS(1, "成功"),
    FAILURE(2, "失败");

    private Integer code;
    private String description;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private SubmitStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static SubmitStatusEnum getByCode(Integer code) {
        for (SubmitStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
