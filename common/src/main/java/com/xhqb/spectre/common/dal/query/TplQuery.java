package com.xhqb.spectre.common.dal.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/15 18:00
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String code;

    private String title;

    private Integer status;

    private Integer signId;

    private String smsTypeCode;

    private String content;

    private String creator;

    private Integer channelAccountId;

    private PageParameter pageParameter;

    private String appCode;

    private Integer approveStatus;

    private String source;

    /**
     * 模板调用开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date callStartTime;

    /**
     * 模板调用结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date callEndTime;

    private Set<String> tplCodeList;
}
