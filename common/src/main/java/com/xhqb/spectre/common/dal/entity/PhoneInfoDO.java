package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PhoneInfoDO implements Serializable {

    private static final long serialVersionUID = 2373744920509197707L;

    private Integer id;

    private String pref;

    private String phone;

    private String province;

    private String city;

    private String isp;

    private String postCode;

    private String cityCode;

    private String areaCode;

    private Integer ispCode;

    private String pJp;

    private String cJp;

}