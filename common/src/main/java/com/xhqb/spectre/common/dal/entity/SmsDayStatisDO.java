package com.xhqb.spectre.common.dal.entity;

import java.util.Date;

public class SmsDayStatisDO extends SmsDayStatisKey {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_day_statis.send_total
     *
     * @mbggenerated
     */
    private Long sendTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_day_statis.billing_total
     *
     * @mbggenerated
     */
    private Long billingTotal;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_day_statis.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_day_statis.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_day_statis.send_total
     *
     * @return the value of t_sms_day_statis.send_total
     *
     * @mbggenerated
     */
    public Long getSendTotal() {
        return sendTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_day_statis.send_total
     *
     * @param sendTotal the value for t_sms_day_statis.send_total
     *
     * @mbggenerated
     */
    public void setSendTotal(Long sendTotal) {
        this.sendTotal = sendTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_day_statis.billing_total
     *
     * @return the value of t_sms_day_statis.billing_total
     *
     * @mbggenerated
     */
    public Long getBillingTotal() {
        return billingTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_day_statis.billing_total
     *
     * @param billingTotal the value for t_sms_day_statis.billing_total
     *
     * @mbggenerated
     */
    public void setBillingTotal(Long billingTotal) {
        this.billingTotal = billingTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_day_statis.create_time
     *
     * @return the value of t_sms_day_statis.create_time
     *
     * @mbggenerated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_day_statis.create_time
     *
     * @param createTime the value for t_sms_day_statis.create_time
     *
     * @mbggenerated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_day_statis.update_time
     *
     * @return the value of t_sms_day_statis.update_time
     *
     * @mbggenerated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_day_statis.update_time
     *
     * @param updateTime the value for t_sms_day_statis.update_time
     *
     * @mbggenerated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}