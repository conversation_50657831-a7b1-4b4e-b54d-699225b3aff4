package com.xhqb.spectre.common.dal.entity;

import lombok.Data;
import java.util.Date;

/**
 * 短链访问品牌统计
 */
@Data
public class ShortUrlBrandStatDO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 统计时间
     */
    private Date statDate;

    /**
     * 用户短链模板编码
     */
    private String userTplCode;

    /**
     * 手机品牌
     */
    private String brand;

    /**
     * 点击次数
     */
    private Long pv;

    /**
     * 独立用户数量
     */
    private Long uv;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;
}
