package com.xhqb.spectre.common.mq;

import com.xhqb.spectre.common.dal.dto.TplChannelData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.Optional;

/**
 * 渠道信息
 */
@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelCode {

    private String channelTplId;

    private String channelAccountId;

    private String msgContent;

    private Integer weight;

    public static ChannelCode buildChannelCode(TplChannelData tplChannelData) {
        return Optional.ofNullable(tplChannelData).map(item -> {
            ChannelCode code = ChannelCode.builder()
                .channelAccountId(String.valueOf(tplChannelData.getChannelAccountId()))
                .channelTplId(tplChannelData.getChannelTplId())
                .weight(tplChannelData.getWeight())
                .build();
            return code;
        }).orElse(null);
    }

}
