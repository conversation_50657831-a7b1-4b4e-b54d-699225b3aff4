package com.xhqb.spectre.common.dal.entity;

import lombok.Data;

import java.util.Date;

@Data
public class TplOpRecordDO {
    /**
     * 主键 id
     */
    private Integer id;

    /**
     * 操作类型 编辑 update ｜状态扭转 turn ｜创建 create
     */
    private String type;

    /**
     * 模板名称
     */
    private String title;

    /**
     * 状态 0: 无效 1: 有效
     */
    private Integer status;

    /**
     * 签名 id
     */
    private Integer signId;

    /**
     * 业务线 id
     */
    private Integer businessLineId;

    /**
     * 营销场景 id
     */
    private Integer marketSceneId;

    /**
     * 渠道报备信息
     */
    private String channelInfo;

    /**
     * 渠道屏蔽
     */
    private String disableInfo;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 内容
     */
    private String content;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 模板编码
     */
    private String code;


}
