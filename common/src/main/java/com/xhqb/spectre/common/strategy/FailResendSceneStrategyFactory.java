package com.xhqb.spectre.common.strategy;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 补发场景策略工厂
 */
@Slf4j
public class FailResendSceneStrategyFactory {

    /**
     * 策略映射表 key: sceneType, value: strategy
     */
    private final Map<String, FailResendSceneStrategy> strategyMap = new ConcurrentHashMap<>();

    /**
     * 初始化策略工厂
     * @param sceneStrategies 策略列表
     */
    public void initStrategies(List<FailResendSceneStrategy> sceneStrategies) {
        // 清空现有策略
        strategyMap.clear();

        // 初始化策略映射
        for (FailResendSceneStrategy strategy : sceneStrategies) {
            String sceneType = strategy.getSceneType();
            strategyMap.put(sceneType, strategy);
            log.info("注册补发场景策略: sceneType={}, strategy={}", sceneType, strategy.getClass().getSimpleName());
        }

        log.info("补发场景策略工厂初始化完成，共注册{}个策略", strategyMap.size());
    }

    /**
     * 根据场景类型获取策略
     * @param sceneType 场景类型
     * @return 策略实现
     */
    public FailResendSceneStrategy getStrategy(String sceneType) {
        FailResendSceneStrategy strategy = strategyMap.get(sceneType);
        if (strategy == null) {
            log.warn("未找到场景类型对应的策略: sceneType={}", sceneType);
        }
        return strategy;
    }

    /**
     * 获取所有支持的场景类型
     * @return 场景类型集合
     */
    public java.util.Set<String> getSupportedSceneTypes() {
        return strategyMap.keySet();
    }
}
