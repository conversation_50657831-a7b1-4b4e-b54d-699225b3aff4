package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.BatchTaskDO;
import com.xhqb.spectre.common.dal.query.BatchTaskQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BatchTaskMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task
     *
     * @mbggenerated
     */
    int insert(BatchTaskDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task
     *
     * @mbggenerated
     */
    int insertSelective(BatchTaskDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task
     *
     * @mbggenerated
     */
    BatchTaskDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(BatchTaskDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(BatchTaskDO record);

    /**
     * 分页查询群发短信列表总页数
     *
     * @param batchTaskQuery
     * @return
     */
    Integer countByQuery(BatchTaskQuery batchTaskQuery);

    /**
     * 分页查询群发短信列表数据
     *
     * @param batchTaskQuery
     * @return
     */
    List<BatchTaskDO> selectByQuery(BatchTaskQuery batchTaskQuery);

    /**
     * 每次更新时版本号随之更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeyWithVersion(BatchTaskDO record);

    /**
     * 根据id+版本更新发送数量
     *
     * @param record
     * @return
     */
    int updateSentCountByIdWithVersion(BatchTaskDO record);


    /**
     * 根据id+ status 更新
     *
     * @param id            主键
     * @param status        状态
     * @param sendStartTime 发送时间
     * @param queryStatus   更新之前的状态
     * @return
     */
    int updateByStatusWithId(Integer id, Integer status, Integer sendStartTime, Integer queryStatus);

    /**
     * 根据ID批量查询群发任务
     *
     * @param idList
     * @return
     */
    List<BatchTaskDO> selectByIdList(@Param("idList") List<Integer> idList);
}