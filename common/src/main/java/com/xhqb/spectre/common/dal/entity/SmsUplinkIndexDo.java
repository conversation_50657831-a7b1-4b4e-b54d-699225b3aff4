package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/8/19 16:09
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class SmsUplinkIndexDo implements Serializable {
    private static final long serialVersionUID = -1986743981208535793L;
    /**
     * id
     */
    private Integer id;
    /**
     * 上行短信最后id
     */
    private Long lastSmsUplinkId;

    private Date createTime;

    private Date updateTime;

}
