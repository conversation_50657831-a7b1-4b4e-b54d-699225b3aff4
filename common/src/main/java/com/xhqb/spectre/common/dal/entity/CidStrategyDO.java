package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Cid校验策略表
 *
 * <AUTHOR>
 * @date 2021/11/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CidStrategyDO implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 策略值
     */
    private String strategyValue;

    /**
     * 类型 0->用户状态 1->用户完件
     */
    private String type;

    /**
     * 状态 0->未使用(未选中) 1->使用(已选中)
     */
    private Integer status;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 策略分组
     */
    private String strategyGroup;

}
