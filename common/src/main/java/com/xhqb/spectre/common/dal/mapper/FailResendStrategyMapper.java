package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.FailResendStrategyDO;
import com.xhqb.spectre.common.dal.query.FailResendQuery;
import org.apache.ibatis.annotations.Param;

import java.util.Arrays;
import java.util.List;

/**
 * 失败补发策略:【t_fail_resend_strategy】
 */
public interface FailResendStrategyMapper {
    Integer countByQuery(@Param("query") FailResendQuery failResendQuery);

    List<FailResendStrategyDO> selectByQuery(@Param("query") FailResendQuery failResendQuery);

    int insertSelective(FailResendStrategyDO strategyDO);

    FailResendStrategyDO selectByStrategyId(String strategyId);

    int updateByPrimaryKeySelective(FailResendStrategyDO updateStrategyDO);

    int updateDeleteTagByStrategyId(String strategyId);

    List<FailResendStrategyDO> selectAll();

    List<FailResendStrategyDO> selectAllEnabled();

    List<FailResendStrategyDO> selectByTplCode(@Param("tplCode") String tplCode);

}
