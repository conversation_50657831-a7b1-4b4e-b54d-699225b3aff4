package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShortUrlLogDO implements Serializable {

    private static final long serialVersionUID = 406301715442213462L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.id
     *
     * @mbggenerated
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.click_time
     *
     * @mbggenerated
     */
    private Integer clickTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.short_code
     *
     * @mbggenerated
     */
    private String shortCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.referrer
     *
     * @mbggenerated
     */
    private String referrer;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.user_agent
     *
     * @mbggenerated
     */
    private String userAgent;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.platform
     *
     * @mbggenerated
     */
    private String platform;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.brand
     *
     * @mbggenerated
     */
    private String brand;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.model
     *
     * @mbggenerated
     */
    private String model;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.os
     *
     * @mbggenerated
     */
    private String os;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.os_version
     *
     * @mbggenerated
     */
    private String osVersion;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.ip
     *
     * @mbggenerated
     */
    private String ip;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.province
     *
     * @mbggenerated
     */
    private String province;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.city
     *
     * @mbggenerated
     */
    private String city;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.isp
     *
     * @mbggenerated
     */
    private String isp;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_short_url_log.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    private String userTplCode;
}