package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户短链模版
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShortUrlTplDO {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 模版名称
     */
    private String name;

    /**
     * 短链模版编码
     */
    private String tplCode;

    /**
     * 长链
     */
    private String longUrl;

    /**
     * 短链
     */
    private String shortCode;

    /**
     * 过期时间
     */
    private String expiredDate;

    /**
     * 点击次数
     */
    private Integer clickCount;

    /**
     * ip点击次数
     */
    private Integer ipClickCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 删除标志
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String description;

    /**
     * 手机号是否加密(0:不加密,1:加密)
     */
    private Integer isEncrypt;

    /**
     * 业务 id
     */
    private String bizId;


    /**
     * 标志 formal, test
     */
    private String scenario;
}
