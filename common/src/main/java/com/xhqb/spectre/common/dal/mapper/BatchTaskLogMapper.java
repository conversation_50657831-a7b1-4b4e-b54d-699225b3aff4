package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.BatchTaskLogDO;
import com.xhqb.spectre.common.dal.query.BatchTaskLogQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BatchTaskLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_log
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_log
     *
     * @mbggenerated
     */
    int insert(BatchTaskLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_log
     *
     * @mbggenerated
     */
    int insertSelective(BatchTaskLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_log
     *
     * @mbggenerated
     */
    BatchTaskLogDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_log
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(BatchTaskLogDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_log
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(BatchTaskLogDO record);

    /**
     * 批量保存群发日志流水信息
     *
     * @param list
     */
    void batchInsert(@Param("list") List<BatchTaskLogDO> list);

    /**
     * 分页查询群发日志总页数
     *
     * @param batchTaskLogQuery
     * @return
     */
    Integer countByQuery(BatchTaskLogQuery batchTaskLogQuery);

    /**
     * 分页查询群发日志记录
     *
     * @param batchTaskLogQuery
     * @return
     */
    List<BatchTaskLogDO> selectByQuery(BatchTaskLogQuery batchTaskLogQuery);

    /**
     * 根据群发任务ID查询日志列表
     *
     * @param taskId
     * @return
     */
    List<BatchTaskLogDO> selectByTaskId(@Param("taskId") Integer taskId);

    /**
     * 导出失败的数据
     *
     * @param taskId
     * @param lastId
     * @param pageSize
     * @return
     */
    List<BatchTaskLogDO> exportQuery(@Param("taskId") Integer taskId, @Param("lastId") Integer lastId, @Param("pageSize") Integer pageSize);

    /**
     * 导出失败数据数量
     *
     * @param taskId
     * @return
     */
    Integer exportCount(@Param("taskId") Integer taskId);
}