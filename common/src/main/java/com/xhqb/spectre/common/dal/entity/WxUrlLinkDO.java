package com.xhqb.spectre.common.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 小程序短链表
 * @TableName t_wx_url_link
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WxUrlLinkDO implements Serializable {
    /**
     * 唯一标识
     */
    private Long id;

    /**
     * 使用场景
     */
    private String linkDesc;

    /**
     * 短链id
     */
    private Integer shortUrlId;

    /**
     * 短链编码
     */
    private String shortCode;

    /**
     * xh短链地址
     */
    private String shortUrl;

    /**
     * 小程序链接
     */
    private String urlLink;

    /**
     * 小程序
     */
    private String appid;

    /**
     * 小程序环境变量
     */
    private String envVersion;

    /**
     * 进入小程序的路径
     */
    private String path;

    /**
     * 进入小程序的query参数
     */
    private String query;

    /**
     * 最后生成链接的日期
     */
    private Date generateDate;

    /**
     * 状态（0: 停用, 1: 启用）
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志，0：未删除；1：已删除
     */
    private Integer isDelete;

    private static final long serialVersionUID = 1L;

    public boolean isEnabled() {
        return status.equals(1);
    }

    public boolean isDisabled() {
        return status.equals(0);
    }
}