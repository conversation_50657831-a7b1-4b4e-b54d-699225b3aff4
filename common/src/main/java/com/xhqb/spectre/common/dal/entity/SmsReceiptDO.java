package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsReceiptDO {

    public static final String SEQ_NAME = "SendReceiptSequenceId";

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.id
     *
     * @mbggenerated
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.order_id
     *
     * @mbggenerated
     */
    private Long orderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.channel_account_id
     *
     * @mbggenerated
     */
    private Integer channelAccountId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.channel_code
     *
     * @mbggenerated
     */
    private String channelCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.tpl_code
     *
     * @mbggenerated
     */
    private String tplCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.channel_msg_id
     *
     * @mbggenerated
     */
    private String channelMsgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.mobile
     *
     * @mbggenerated
     */
    private String mobile;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.status
     *
     * @mbggenerated
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.report_status
     *
     * @mbggenerated
     */
    private String reportStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.report_desc
     *
     * @mbggenerated
     */
    private String reportDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.submit_time
     *
     * @mbggenerated
     */
    private Integer submitTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.recv_report_time
     *
     * @mbggenerated
     */
    private Integer recvReportTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.done_time
     *
     * @mbggenerated
     */
    private Integer doneTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.dest_terminal_id
     *
     * @mbggenerated
     */
    private String destTerminalId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_receipt.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

}