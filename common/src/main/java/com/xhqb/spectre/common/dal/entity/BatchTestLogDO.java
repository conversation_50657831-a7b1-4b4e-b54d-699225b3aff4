package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 群发测试发送日志
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTestLogDO implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 批次号
     */
    private Integer taskId;

    /**
     * 请求内容
     */
    private String reqContent;

    /**
     * 响应内容
     */
    private String respContent;

    /**
     * 测试请求状态 0->失败 1->成功
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private String updater;
}