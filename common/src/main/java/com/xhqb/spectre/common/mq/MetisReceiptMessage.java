package com.xhqb.spectre.common.mq;

import lombok.Data;

import java.io.Serializable;

/**
 * 消息中心回执信息
 *
 * @author: cl
 * @date: 2023/09/13
 */
@Data
public class MetisReceiptMessage implements Serializable {

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 阶段,取值：submit 、deliver
     */
    private String stage;

    /**
     * 触达类型
     */
    private String msgType;

    /**
     * 回执结果(0:成功 1:失败)
     */
    private String result;

    /**
     * 回执结果描述
     */
    private String resultDesc;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户ID
     */
    private String cid;

    /**
     * 扩展字段
     */
    private String ext;
}
