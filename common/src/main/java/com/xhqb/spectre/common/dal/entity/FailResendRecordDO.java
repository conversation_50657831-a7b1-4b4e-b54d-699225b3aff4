package com.xhqb.spectre.common.dal.entity;

import lombok.Data;

import java.util.Date;

@Data
public class FailResendRecordDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 原始订单ID
     */
    private String originalOrderId;

    /**
     * 原始模板编码
     */
    private String originalTplCode;

    /**
     * 原始信息参数
     */
    private String originalParameter;

    /**
     * 原始回执状态码
     */
    private String originalReportCode;

    /**
     * 原始签名
     */
    private String originalSignName;

    /**
     * 运营商编码
     */
    private String ispCode;

    /**
     * 策略 ID
     */
    private String strategyId;

    /**
     * 补发模板编码
     */
    private String tplCode;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 开始补发时间
     */
    private Integer startTime;

    /**
     * 规则 ID
     */
    private String ruleId;

    /**
     * 请求 ID
     */
    private String requestId;

    /**
     * 补发状态：0=待补发，1=补发中，2=补发成功，3=补发失败
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
