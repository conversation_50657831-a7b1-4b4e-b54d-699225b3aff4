package com.xhqb.spectre.common.dal.mapper;


import com.xhqb.spectre.common.dal.entity.SubmitResqDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SubmitResqMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submit_resq
     *
     * @mbggenerated
     */
    int insert(SubmitResqDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_submit_resq
     *
     * @mbggenerated
     */
    int insertSelective(SubmitResqDO record);

    int insertBatch(List<SubmitResqDO> submitResqDOList);

    /**
     * 根据orderId查询提交响应信息
     *
     * @param orderId
     * @return
     */
    SubmitResqDO selectByOrderId(@Param("orderId") Long orderId);

}