package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 群发任务流水日志表
 *
 * <AUTHOR>
 * @date 2021/12/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskLogDO implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 群发任务ID
     */
    private Integer taskId;

    /**
     * 群发任务分片ID
     */
    private Integer taskParamId;

    /**
     * cid信息
     */
    private String cid;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 发送状态 0->失败 1->成功
     */
    private Integer status;

    /**
     * 发送描述信息
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 参数信息
     */
    private String param;

    /**
     * 失败编码
     */
    private String errCode;

}