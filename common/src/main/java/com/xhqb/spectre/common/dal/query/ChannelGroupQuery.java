package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.Data;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2022/4/11 10:51
 * @Description:
 */
@Data
public class ChannelGroupQuery implements Serializable {

    private static final long serialVersionUID = 5702121449793418397L;

    /**
     * 渠道组ID
     */
    private Integer id;

    /**
     * 渠道组名称
     */
    private String name;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 签名ID
     */
    private Integer signId;

    private PageParameter pageParameter;
}
