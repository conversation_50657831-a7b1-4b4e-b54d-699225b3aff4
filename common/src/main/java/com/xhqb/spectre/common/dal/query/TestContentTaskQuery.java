package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TestContentTaskQuery {

    /**
     * 短信文案
     */
    private String content;

    /**
     * 测试名称
     */
    private String name;

    /**
     * 状态
     */
    private Integer taskStatus;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 分页参数
     *
     * @ignore
     */
    private PageParameter pageParameter;

    /**
     * taskID 列表
     */
    private List<String> taskIdList;

    /**
     * 模版编码，模糊查询；这个字段文案测试不用，自动测试任务用
     */
    private String likeTplCode;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;
}
