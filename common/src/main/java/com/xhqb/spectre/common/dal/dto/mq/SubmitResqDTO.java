package com.xhqb.spectre.common.dal.dto.mq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class SubmitResqDTO implements Serializable {

    private static final long serialVersionUID = 219213027970572458L;

    private String channelMsgId;

    private String channelCode;

    private Long orderId;

    private Integer result;

    /**
     * 重发次数
     */
    private Integer resend;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;
    /**
     * 13位时间戳
     */
    private Long recvSubmitTime;
    /**
     * 10位时间戳
     */
    private Integer recvSendTime;

    private Integer reqSrc;

    private String gatewayUserName;

    private String requestId;

    /**
     * 订单表名后缀(yyyyMM)[由PosterConsumer类中进行填充]
     */
    private String tableNameSuffix;
}
