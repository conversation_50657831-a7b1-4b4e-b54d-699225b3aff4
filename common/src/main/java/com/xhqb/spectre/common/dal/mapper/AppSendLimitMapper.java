package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.AppSendLimitDO;
import com.xhqb.spectre.common.dal.query.AppSendLimitQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AppSendLimitMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_app_send_limit
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_app_send_limit
     *
     * @mbggenerated
     */
    int insert(AppSendLimitDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_app_send_limit
     *
     * @mbggenerated
     */
    int insertSelective(AppSendLimitDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_app_send_limit
     *
     * @mbggenerated
     */
    AppSendLimitDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_app_send_limit
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(AppSendLimitDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_app_send_limit
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(AppSendLimitDO record);

    AppSendLimitDO selectOne(AppSendLimitDO record);

    List<AppSendLimitDO> selectList(String appCode);

    List<AppSendLimitDO> selectAllEnabled();

    List<AppSendLimitDO> selectByAppCode(String appCode);

    Integer countByQuery(AppSendLimitQuery appSendLimitQuery);

    List<AppSendLimitDO> selectByQuery(AppSendLimitQuery appSendLimitQuery);

    List<AppSendLimitDO> selectByAppCodeList(@Param("list") List<String> appCodeList);

    void enable(@Param("appCode") String appCode, @Param("operator") String operator);

    void disable(@Param("appCode") String appCode, @Param("operator") String operator);

    void delete(@Param("appCode") String appCode, @Param("operator") String operator);
}