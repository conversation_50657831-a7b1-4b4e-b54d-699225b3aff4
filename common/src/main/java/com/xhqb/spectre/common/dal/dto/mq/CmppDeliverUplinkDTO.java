package com.xhqb.spectre.common.dal.dto.mq;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * cmpp上行短信回执记录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/14 11:31 上午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmppDeliverUplinkDTO {

    /**
     * 回执消息ID
     */
    private String channelMsgId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 接收到的回执时间
     */
    private Integer recvUplinkTime;

    /**
     * 目标终端
     */
    private String destTerminalId;

    /**
     * 消息长度
     */
    private Integer msgLength;

    /**
     * 用户回复短信消息内容
     */
    private String msgContent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 渠道账号id
     */
    private Integer channelAccountId;

    /**
     * 渠道code
     */
    private String channelCode;

    /**
     * 短信类型
     */
    private String smsTypeCode;

}
