package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.WxUrlLinkFlowDO;

/**
* <AUTHOR>
* @description 针对表【t_wx_url_link_flow(小程序短链流水表)】的数据库操作Mapper
* @createDate 2025-02-20 14:26:15
* @Entity generator.entity.WxUrlLinkFlow
*/
public interface WxUrlLinkFlowMapper {

    int deleteByPrimaryKey(Long id);

    int insert(WxUrlLinkFlowDO record);

    int insertSelective(WxUrlLinkFlowDO record);

    WxUrlLinkFlowDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WxUrlLinkFlowDO record);

    int updateByPrimaryKey(WxUrlLinkFlowDO record);

}
