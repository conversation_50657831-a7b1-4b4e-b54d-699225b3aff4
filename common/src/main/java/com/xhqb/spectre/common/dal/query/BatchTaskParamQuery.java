package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 群发参数查询
 *
 * <AUTHOR>
 * @date 2021/10/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskParamQuery implements Serializable {

    /**
     * 任务ID
     */
    private Integer taskId;
    /**
     * 发送状态 0->未发送 1->发送中 2->已发送
     */
    private List<Integer> sendStatus;
    /**
     * 文件md5值
     */
    private String fileMd5;
    /**
     * 文件分片起始位置
     */
    private Integer startOffset;
    /**
     * 文件结束起始位置
     */
    private Integer endOffset;
    /**
     * 是否删除 0->未删除 1->删除
     */
    private Integer isDelete;

    /**
     * 分页参数
     */
    private PageParameter pageParameter;

}
