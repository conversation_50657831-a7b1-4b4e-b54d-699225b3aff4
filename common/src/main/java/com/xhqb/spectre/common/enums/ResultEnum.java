package com.xhqb.spectre.common.enums;

public enum ResultEnum {

    SUCCUSS_RESPONSE("SUCCUSS", "查询成功"),

    QUERY_FRUSTRATED("QUERY_FRUSTRATED", "查询失败"),

    SAVE_SUCCUSS("SAVE_SUCCUSS", "保存成功"),

    OPERATION_SUCCUSS("OPERATION_SUCCUSS", "操作成功"),

    OPERATION_FAILUER("OPERATION_FAILUER", "操作失败"),

    DELETE_SUCCUSS("DELETE_SUCCUSS", "删除成功"),

    PARAMETER_LACK("PARAMETER_LACK", "参数缺失"),

    PARAMETER_ERROR("PARAMETER_ERROR", "参数错误"),

    SYSTEM_FAILUER("SYSTEMFAIL", "系统错误"),

    FAILURE_RESPONSE("FAILURE", "保存失败"),

    TIME_BEYOND("TIME_BEYOND", "开始时间超出限制"),

    DATA_EMPTY("DATA_EMPTY", "未查询到数据"),

    REPEAT_REQ("REPEAT_REQ", "重复请求"),

    HTTP_FAILUER("9999", "发送异常"),

    HTTP_FAILUER_NULL("9999-null", "发送没有返回数据"),

    RESULT_STATUS_SUCCESS("0", "发送成功"),

    DELETE_STATUS_SUCCESS("DELETE_STATUS_SUCCESS", "删除成功"),

    UNKNOWN_STATUS("UNKNOWN_STATUS", "未知状态"),

    PARTNER_EXIST("PARTNER_EXIST", "供应商已存在");

    private String code;
    private String description;
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private ResultEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    public static ResultEnum getByCode(String code) {
        for (ResultEnum cardServiceResult : values()) {
            if (cardServiceResult.getCode().equals(code)) {
                return cardServiceResult;
            }
        }
        return null;
    }
}
