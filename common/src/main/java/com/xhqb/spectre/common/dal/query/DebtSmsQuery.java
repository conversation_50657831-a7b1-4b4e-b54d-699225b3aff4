package com.xhqb.spectre.common.dal.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.Set;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DebtSmsQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ck合同号
     */
    @NotEmpty(message = "合同列表不能为空")
    @Size(max = 1000, message = "合同数量超过限制")
    private Set<String> contractNos;

}
