package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.Data;

import java.io.Serializable;

/**
 * 业务线查询
 *
 * <AUTHOR>
 * @date 2022/9/23
 */
@Data
public class BusinessLineQuery implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 业务线名称
     */
    private String name;
    /**
     * 业务线状态 0:无效 1:有效
     */
    private Integer status;
    /**
     * 分页参数
     *
     * @ignore
     */
    private PageParameter pageParameter;
}
