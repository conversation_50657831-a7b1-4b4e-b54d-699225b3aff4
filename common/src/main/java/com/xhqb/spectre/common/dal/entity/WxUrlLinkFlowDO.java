package com.xhqb.spectre.common.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 小程序短链流水表
 * @TableName t_wx_url_link_flow
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WxUrlLinkFlowDO implements Serializable {
    /**
     * 唯一标识
     */
    private Long id;

    /**
     * 关联短链表ID
     */
    private Long wxUrlLinkId;

    /**
     * 访问小程序的短链地址
     */
    private String urlLink;

    /**
     * 最后生成链接的日期
     */
    private Date generateDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}