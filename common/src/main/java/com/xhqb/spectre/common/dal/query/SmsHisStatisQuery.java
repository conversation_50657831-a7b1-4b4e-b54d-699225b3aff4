package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 短信历史发送量统计表
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsHisStatisQuery implements Serializable {

    /**
     * 类型编码列表
     */
    private List<String> codeList;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 分页参数
     */
    private PageParameter pageParameter;
}
