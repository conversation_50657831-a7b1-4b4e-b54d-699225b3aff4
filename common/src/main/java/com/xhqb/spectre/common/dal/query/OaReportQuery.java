package com.xhqb.spectre.common.dal.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OaReportQuery {

    /**
     * 短信类型
     */
    private String smsTypeCode;

    /**
     * 使用场景
     */
    private String sceneCode;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;


    /**
     * 签名 iD
     */
    private Integer signId;

    /**
     * 内容 ID (报备 ID)
     */
    private String contentId;

    /**
     * 渠道简称
     */
    private String channelCode;

    /**
     *  内容类型
     * @ignore
     */
    private String contentType;

    /**
     * 分页参数
     * @ignore
     */
    private PageParameter pageParameter;

    /**
     * 模板调用开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date callStartTime;

    /**
     * 模板调用结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date callEndTime;

    private Set<String> tplCodeList;
}
