package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.DebtSmsRetryDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
public interface DebtSmsRetryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_debt_sms_retry
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_debt_sms_retry
     *
     * @mbggenerated
     */
    int insert(DebtSmsRetryDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_debt_sms_retry
     *
     * @mbggenerated
     */
    int insertSelective(DebtSmsRetryDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_debt_sms_retry
     *
     * @mbggenerated
     */
    DebtSmsRetryDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_debt_sms_retry
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(DebtSmsRetryDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_debt_sms_retry
     *
     * @mbggenerated
     */
    int updateByPrimaryKeyWithBLOBs(DebtSmsRetryDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_debt_sms_retry
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(DebtSmsRetryDO record);

    /**
     * 根据orderId查询债转短信重试记录
     *
     * @param orderId
     * @return
     */
    DebtSmsRetryDO selectByOrderIdWithoutMessage(@Param("orderId") Long orderId);

    /**
     * 查询债转短信重试任务
     *
     * @param beginStartTime
     * @param endStartTime
     * @param pageSize
     * @param lastId
     * @return
     */
    List<DebtSmsRetryDO> selectDebtSmsRetryJob(@Param("beginStartTime") Integer beginStartTime, @Param("endStartTime") Integer endStartTime, @Param("pageSize") Integer pageSize, @Param("lastId") Long lastId);
}