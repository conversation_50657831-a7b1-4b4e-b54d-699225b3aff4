package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.CodeMappingDO;
import com.xhqb.spectre.common.dal.query.CodeMappingQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CodeMappingMapper {
    int deleteByPrimaryKey(@Param("channelCode") String channelCode, @Param("type") String type, @Param("channelErrCode") String channelErrCode);

    int insert(CodeMappingDO record);

    int insertSelective(CodeMappingDO record);

    CodeMappingDO selectByPrimaryKey(@Param("channelCode") String channelCode, @Param("type") String type, @Param("channelErrCode") String channelErrCode);

    int updateByPrimaryKeySelective(CodeMappingDO record);

    int updateByPrimaryKey(CodeMappingDO record);

    /**
     * 联表查询所有
     *
     * @return 集合
     */
    List<CodeMappingDO> selectAll();

    /**
     * 分页查询映射码列表总页数
     *
     * @param codeMappingQuery
     * @return
     */
    Integer countByQuery(CodeMappingQuery codeMappingQuery);

    /**
     * 分页查询映射码列表数据
     *
     * @param codeMappingQuery
     * @return
     */
    List<CodeMappingDO> selectByQuery(CodeMappingQuery codeMappingQuery);

}