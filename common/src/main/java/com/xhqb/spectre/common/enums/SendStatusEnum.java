package com.xhqb.spectre.common.enums;

public enum SendStatusEnum {

    NEW(0, "未发送"),
    SEND_SUCCESS(1, "已发送"),
    SEND_FAILURE(2, "发送失败");

    private Integer code;
    private String description;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private SendStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static SendStatusEnum getByCode(Integer code) {
        for (SendStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
