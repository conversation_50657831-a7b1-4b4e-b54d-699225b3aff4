package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 群发上传检测结果
 *
 * <AUTHOR>
 * @date 2022/2/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchDetectDO {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 群发任务ID
     */
    private Integer taskId;
    /**
     * 群发上传的数据(cid/mobile)
     */
    private String content;

    /**
     * 检测类型 0->无效CID 1->空号类型 2->停机类型 3->参数缺失 4->数据重复
     */
    private Integer type;

    /**
     * 检测描述信息
     */
    private String remark;

    /**
     * 群发任务文件md5值
     */
    private String fileMd5;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}