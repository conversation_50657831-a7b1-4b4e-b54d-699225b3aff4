package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/18 11:02
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SignQuery implements Serializable {

    private static final long serialVersionUID = 5627673880592384174L;

    private String name;

    private Integer status;
    /**
     * 签名编码
     */
    private String code;

    private PageParameter pageParameter;
}
