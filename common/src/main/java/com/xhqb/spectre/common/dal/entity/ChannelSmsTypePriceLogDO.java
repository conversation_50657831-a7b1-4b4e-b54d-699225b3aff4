package com.xhqb.spectre.common.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 渠道签名单价调整日志表
 * @TableName t_channel_sms_type_price_log
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChannelSmsTypePriceLogDO implements Serializable {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 短信类型id
     */
    private String smsTypeCode;

    /**
     * 生效时间
     */
    private Date effectiveTime;

    /**
     * 失效时间
     */
    private Date ineffectiveTime;

    /**
     * 调整前单价,千分位存储
     */
    private Integer perPrice;

    /**
     * 调整后单价,千分位存储
     */
    private Integer curPrice;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 签名
     */
    private String signName;

    private static final long serialVersionUID = 1L;
}