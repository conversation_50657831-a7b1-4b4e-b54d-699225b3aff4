package com.xhqb.spectre.common.dal.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DailyStats {
    private String accountId; // 应用ID
    private String date; // 日期

    private List<TemplateStats> templateStatsList = new ArrayList<>(); // 模板维度统计

    public DailyStats(String accountId, String date) {
        this.accountId = accountId;
        this.date = date;
    }

    // 内部类，表示模板维度的统计信息
    @Data
    public static class TemplateStats {
        private String tplCode; // 模板
        private String errCode; // 错误码
        private int requests; // 模板请求数
    }

    public void addTemplateStats(String tplCode, String errCode, int requests) {
        TemplateStats templateStats = new TemplateStats();
        templateStats.setTplCode(tplCode);
        templateStats.setErrCode(errCode);
        templateStats.setRequests(requests);
        this.templateStatsList.add(templateStats);
    }
}
