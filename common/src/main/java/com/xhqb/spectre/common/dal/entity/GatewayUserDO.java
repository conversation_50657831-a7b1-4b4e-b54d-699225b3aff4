package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 网关账号管理(王建政)
 *
 * <AUTHOR>
 * @date 2021/11/8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GatewayUserDO implements Serializable {

    private static final long serialVersionUID = 1013915696050372627L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.group_name
     *
     * @mbggenerated
     */
    private String groupName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.user_name
     *
     * @mbggenerated
     */
    private String userName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.password
     *
     * @mbggenerated
     */
    private String password;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.sp_code
     *
     * @mbggenerated
     */
    private String spCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.service_id
     *
     * @mbggenerated
     */
    private String serviceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.msg_src
     *
     * @mbggenerated
     */
    private String msgSrc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.tpl_code
     *
     * @mbggenerated
     */
    private String tplCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.sign_id_list
     *
     * @mbggenerated
     */
    private String signIdList;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.white_ip_list
     *
     * @mbggenerated
     */
    private String whiteIpList;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.creator
     *
     * @mbggenerated
     */
    private String creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.updater
     *
     * @mbggenerated
     */
    private String updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.is_delete
     *
     * @mbggenerated
     */
    private Integer isDelete;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_user.is_check_tpl
     *
     * @mbggenerated
     */
    private Integer isCheckTpl;

    private Set<Integer> signIdSet;

}