package com.xhqb.spectre.common.dal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/22 19:45
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelAccountDisableData implements Serializable {

    private static final long serialVersionUID = 3601389877034085865L;

    /**
     * 渠道账号ID
     */
    private Integer channelAccountId;

    /**
     * 运营商列表
     */
    private List<String> ispList;

    /**
     * 地域列表
     */
    private List<AreaData> areaList;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 屏蔽类型：1-整个时间段屏蔽（原功能），2-固定时间段屏蔽（新功能）
     */
    private Integer disableType;

    /**
     * 时间段开始时间（格式：HH:mm，如 10:30）
     */
    private String periodStartTime;

    /**
     * 时间段结束时间（格式：HH:mm，如 12:00）
     */
    private String periodEndTime;
}
