package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/10/1 10:25
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppSendLimitQuery implements Serializable {

    private static final long serialVersionUID = -5822216073714788293L;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 状态，0：无效；1：有效
     */
    private Integer status;

    private PageParameter pageParameter;
}
