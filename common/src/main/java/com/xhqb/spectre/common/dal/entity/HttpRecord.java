package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HttpRecord {
    String channelMsgId;
    String channelCode;
    String mobile;
    String code; //返回code
    String desc; //返回描述
    Integer billCount; //计费条数
    Integer status;
    Date reportTime;
    Date realSendTime;
}
