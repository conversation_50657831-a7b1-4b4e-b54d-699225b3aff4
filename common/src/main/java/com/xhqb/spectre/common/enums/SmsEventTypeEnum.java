package com.xhqb.spectre.common.enums;

public enum SmsEventTypeEnum {

    BLACKLIST("BLACKLIST", "黑名单事件"),
    UNSUBSCRIBE("UNSUBSCRIBE", "退订事件"),
    RECEIPT_STATUS("RECEIPT_STATUS", "回执状态事件");

    private final String code;
    private final String name;

    SmsEventTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static SmsEventTypeEnum getByCode(String code) {
        for (SmsEventTypeEnum eventType : values()) {
            if (eventType.getCode().equals(code)) {
                return eventType;
            }
        }
        return null;
    }
}
