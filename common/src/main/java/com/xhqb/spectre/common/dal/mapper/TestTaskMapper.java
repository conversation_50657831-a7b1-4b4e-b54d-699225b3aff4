package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.test.TestTaskDO;
import com.xhqb.spectre.common.dal.query.ChannelTestTaskQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TestTaskMapper {

    int insertBySelective(TestTaskDO taskDO);

    List<TestTaskDO> selectByTplId(Long tplId);

    Integer countByQuery(@Param("query") ChannelTestTaskQuery channelTestTaskQuery);

    List<TestTaskDO> selectByQuery(@Param("query") ChannelTestTaskQuery channelTestTaskQuery);

    TestTaskDO selectByTaskId(String taskId);

    int updateStatusByTaskId(String taskId);

    int updateDeleteTagByTaskId(String taskId);


    List<TestTaskDO> selectByTplIdAndTime(@Param("tplId") Long tplId,@Param("startTime") Date startTime, @Param("endTime")Date endTime);

    int updateByTaskIdAndStatus(@Param("taskId") String taskId, @Param("status") int updateStatus);

    List<TestTaskDO> selectByTime(@Param("startTime")Date startDate, @Param("endTime")Date currentDate);

    List<TestTaskDO> selectAll();

}
