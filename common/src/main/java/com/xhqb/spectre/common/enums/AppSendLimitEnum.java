package com.xhqb.spectre.common.enums;


import java.util.Arrays;

public enum AppSendLimitEnum {

    MOBILE_MAX_COUNT_HALF_MINUTE("mobileMaxCountHalfMinute", "同一个手机号30秒内发送短信条数上限数量"),
    MOBILE_MAX_COUNT_HOUR("mobileMaxCountHour", "同一个手机号1小时内发送短信条数上限数量"),
    MOBILE_MAX_COUNT_DAY("mobileMaxCountDay", "同一个手机号1天内发送短信条数上限数量"),
    MOBILE_SAME_CONTENT_MAX_CYCLE("mobileSameContentMaxCycle", "相同内容短信对同一个手机号发送上限数量"),
    MOBILE_MAX_COUNT_VERIFY("mobileMaxCountVerify", "同一手机号1天内发送验证码短信条数上限数量");

    private String code;

    private String desc;


    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    AppSendLimitEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean contains(String limitKey) {
        return Arrays.stream(AppSendLimitEnum.values()).anyMatch(item -> item.getCode().equals(limitKey));
    }
}
