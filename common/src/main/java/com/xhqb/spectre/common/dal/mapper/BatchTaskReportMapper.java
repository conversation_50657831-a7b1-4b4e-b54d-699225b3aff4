package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.BatchTaskReportDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BatchTaskReportMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_report
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer taskId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_report
     *
     * @mbggenerated
     */
    int insert(BatchTaskReportDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_report
     *
     * @mbggenerated
     */
    int insertSelective(BatchTaskReportDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_report
     *
     * @mbggenerated
     */
    BatchTaskReportDO selectByPrimaryKey(Integer taskId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_report
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(BatchTaskReportDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_report
     *
     * @mbggenerated
     */
    int updateByPrimaryKeyWithBLOBs(BatchTaskReportDO record);


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_task_report
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(BatchTaskReportDO record);

    /**
     * 根据任务批次号和参数ID进行查询
     *
     * @param taskId
     * @param taskParamId
     * @return
     */
    List<BatchTaskReportDO> selectByTaskIdAndParamId(@Param("taskId") Integer taskId, @Param("taskParamId") Integer taskParamId);

    /**
     * 根据taskId查询报表信息
     *
     * @param taskId
     * @return
     */
    List<BatchTaskReportDO> selectByTaskId(@Param("taskId") Integer taskId);
}