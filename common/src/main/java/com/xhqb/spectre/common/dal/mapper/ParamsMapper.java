package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ParamsDO;
import com.xhqb.spectre.common.dal.query.ParamsQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ParamsMapper {
    Integer countCodeByQuery(@Param("query") ParamsQuery paramsQuery);

    List<ParamsDO> selectCodeByQuery(@Param("query") ParamsQuery paramsQuery);

    ParamsDO selectByCode(String code);

    int insertSelective(ParamsDO paramsDO);

    int updateDeleteTagByIdList(@Param("idList") List<Integer> idList);

    List<ParamsDO> selectGroupByCode();

    List<ParamsDO> selectByBaseInfo(@Param("name") String name, @Param("code") String code);

    List<ParamsDO> selectAll();

    int updateDeleteTagByCodeList(@Param("codeList") List<String> codeList);

    Integer countValueByQuery(@Param("query")  ParamsQuery paramsQuery);
}
