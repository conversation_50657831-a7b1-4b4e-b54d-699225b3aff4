package com.xhqb.spectre.common.dal.dto.mq;

import com.xhqb.spectre.common.mq.ChannelCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SendSubmitDTO implements Serializable {

    private static final long serialVersionUID = 4003273722324402225L;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道账号ID
     */
    private Integer channelAccountId;

    /**
     * 签名名称
     */
    private String signName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 处理分片ID
     */
    private Integer sliceId;

    /**
     * 省
     */
    private String provinceShortName;

    /**
     * 市
     */
    private String cityShortName;

    /**
     * 运营商
     */
    private String ispCode;

    /**
     * 计费条数
     */
    private Integer billCount;

    /**
     * 发送类型 1-立即发送，2-定时
     */
    private Integer sendType;

    /**
     * 重发次数
     */
    private Integer resend;

    /**
     * 调用渠道接口时间
     */
    private Integer sendTime;

    /**
     * 信息参数
     */
    private String parameter;

    /**
     * 批次ID
     */
    private Integer batchId;

    /**
     * 请求来源 1：http 2：cmpp
     */
    private Integer reqSrc;

    /**
     * 手机号状态，-1：未查询；0：空号；1：正常；2：停机；3：库无；4：沉默号；5：风险号；99：未知
     */
    private Long phoneStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 可选渠道列表
     */
    private List<ChannelCode> channelCodeSet;

    /**
     * 业务批次号
     */
    private String bizBatchId;

    /**
     * 是否回调消息中心（0：不回调 1：回调）
     */
    private Integer callMetis;

    /**
     * 订单表名后缀(yyyyMM)[由PosterConsumer类中进行填充]
     */
    private String tableNameSuffix;
}
