package com.xhqb.spectre.common.dal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/17 14:41
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplChannelData implements Serializable {

    private static final long serialVersionUID = 6734522261472351615L;

    /**
     * 渠道账号ID
     */
    private Integer channelAccountId;

    /**
     * 渠道侧的模板ID
     */
    private String channelTplId;

    /**
     * 运营商列表
     */
    private List<String> ispList;

    /**
     * 地域过滤类型，1：包含；2：不包含
     */
    private Integer areaFilterType;

    /**
     * 地域列表
     */
    private List<AreaData> areaList;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 备注
     */
    private String remark;

    /**
     * 渠道个性化模版内容
     */
    private String tplContent;
}
