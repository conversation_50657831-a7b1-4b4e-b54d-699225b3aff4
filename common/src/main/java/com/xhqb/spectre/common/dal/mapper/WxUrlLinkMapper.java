package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ShortUrlDO;
import com.xhqb.spectre.common.dal.entity.WxUrlLinkDO;
import com.xhqb.spectre.common.dal.query.ShortUrlQuery;
import com.xhqb.spectre.common.dal.query.WxUrlLinkQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_wx_url_link(小程序短链表)】的数据库操作Mapper
* @createDate 2025-02-20 14:26:15
* @Entity generator.entity.WxUrlLink
*/
public interface WxUrlLinkMapper {

    int deleteByPrimaryKey(Long id);

    int insert(WxUrlLinkDO record);

    int insertSelective(WxUrlLinkDO record);

    WxUrlLinkDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WxUrlLinkDO record);

    int updateByPrimaryKey(WxUrlLinkDO record);

    Integer countByQuery(WxUrlLinkQuery wxUrlLinkQuery);

    List<WxUrlLinkDO> selectByQuery(WxUrlLinkQuery wxUrlLinkQuery);

    void enable(@Param("id") Long id, @Param("operator") String operator);

    void disable(@Param("id") Long id, @Param("operator") String operator);
}
