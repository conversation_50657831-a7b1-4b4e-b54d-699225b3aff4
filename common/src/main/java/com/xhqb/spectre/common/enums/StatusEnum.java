package com.xhqb.spectre.common.enums;

/**
 * 数据状态
 * 可能使用不止一个业务，所以独立
 * Created by zwz on 2017/12/5.
 */
public enum StatusEnum {

    ENABLE("enable","有效"),
    DISABLE("disable","失效");

    private String code;
    private String description;
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    private StatusEnum(String code, String description){
        this.code=code;
        this.description=description;
    }
    /**
     * 通过枚举<code>code</code>获得枚举
     * @param code
     * @return
     */
    public static StatusEnum getByCode(String code){
        for(StatusEnum status:values()){
            if(status.getCode().equals(code)){
                return status;
            }
        }
        return null;
    }
}
