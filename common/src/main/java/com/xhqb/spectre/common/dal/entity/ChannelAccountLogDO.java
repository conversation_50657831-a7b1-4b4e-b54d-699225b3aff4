package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelAccountLogDO implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 账号名称
     */
    private String name;

    /**
     * 渠道账号
     */
    private String key;

    /**
     * 渠道账号json配置参数
     */
    private String jsonMapping;

    /**
     * 费率，千分位存储
     */
    private Integer price;

    /**
     * 协议，1：http；2：cmpp
     */
    private Integer protocol;

    /**
     * 支持的运营商，多个以逗号分隔
     */
    private String isps;

    /**
     * 地域过滤类型，1：包含；2：不包含
     */
    private Integer areaFilterType;

    /**
     * 地域列表，json数组结构
     */
    private String areas;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 状态，0：无效，1：有效
     */
    private Integer status;

    /**
     * 是否免模板审核，1：是；0：否
     */
    private Integer isTplFreeAudit;

    /**
     * 支持的签名ID列表，以英文逗号分隔
     */
    private String signIds;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 渠道账号表id
     */
    private Integer channelAccountId;

    /**
     * 操作类型 1->新增 2->修改 3->删除 4->启用 5->禁用
     */
    private Integer opType;

}