package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/8/8 11:27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class ShortUrlLogQuery implements Serializable {
    private static final long serialVersionUID = -431711659435262354L;
    /**
     * 开始点击时间
     */
    private String startClickTime;
    /**
     * 结束点击时间
     */
    private String endClickTime;
    /**
     * 短链路径
     */
    private String shortUrl;
    /**
     * 系统名称
     */
    private String os;
    /**
     * 分页参数
     */
    private PageParameter pageParameter;


}
