package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskDO implements Serializable {

    private Integer id;

    private String appCode;

    private Integer tplId;

    private Integer signId;

    private String smsTypeCode;

    private String content;

    private Integer userIdType;

    private Integer sendType;

    private Integer sendTime;

    private String fileInfoList;

    private String remark;

    private Integer status;

    private Integer totalCount;

    private Integer sentCount;

    private Integer sendStartTime;

    private Integer sendEndTime;

    private String orderId;

    private Integer version;

    private String creator;

    private Date createTime;

    private String updater;

    private Date updateTime;
    /**
     * 关联的签名信息
     */
    private SignDO sign;

    /**
     * 是否删除 删除标志，0：未删除；1：已删除
     */
    private Integer isDelete;

    /**
     * 实际发送量
     */
    private Integer realSendCount;

    /**
     * 模板类型 0->[*]占位符  1->${xx}名称占位符
     */
    private Integer tplType;

    /**
     * 大数据任务ID
     */
    private Integer bigdataId;
    /**
     * 项目用途
     */
    private String projectDesc;

    /**
     * 用户分组
     */
    private String userGroup;

    /**
     * 分发速率，0表示不限速，正数表示每秒处理的号码数
     */
    private Integer limitRate;

}