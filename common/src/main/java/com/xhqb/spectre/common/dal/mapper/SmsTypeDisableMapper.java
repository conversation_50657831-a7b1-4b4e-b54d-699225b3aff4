package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.SmsTypeDisableDO;
import com.xhqb.spectre.common.dal.query.SmsTypeDisableQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SmsTypeDisableMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_type_disable
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_type_disable
     *
     * @mbggenerated
     */
    int insert(SmsTypeDisableDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_type_disable
     *
     * @mbggenerated
     */
    int insertSelective(SmsTypeDisableDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_type_disable
     *
     * @mbggenerated
     */
    SmsTypeDisableDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_type_disable
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SmsTypeDisableDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_sms_type_disable
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SmsTypeDisableDO record);

    Integer countByQuery(SmsTypeDisableQuery query);

    List<SmsTypeDisableDO> selectByQuery(SmsTypeDisableQuery query);

    void deleteByIdList(@Param("idList") List<Integer> idList, @Param("operator") String operator);

    List<SmsTypeDisableDO> selectAll();
}