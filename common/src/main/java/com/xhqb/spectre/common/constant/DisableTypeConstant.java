package com.xhqb.spectre.common.constant;

/**
 * 屏蔽类型常量
 *
 */
public class DisableTypeConstant {

    /**
     * 屏蔽类型1：整个时间段屏蔽
     */
    public static final Integer TYPE_FULL_PERIOD = 1;

    /**
     * 屏蔽类型2：固定时间段屏蔽
     * 在屏蔽开始时间和结束时间范围内，额外判断新增时间段的开始时间和结束时间
     * 在屏蔽时间范围内再去判断是否在范围内固定的时间段内
     * 例如：屏蔽开始时间和结束时间设置为2025-07-01 00:00:00到2025-07-20 00:00:00
     * 时间段的开始时间和结束时间为10:30到12:00
     * 那么只屏蔽时间段内1个半小时时间范围
     */
    public static final Integer TYPE_PERIOD_TIME = 2;

    /**
     * 默认屏蔽类型
     */
    public static final Integer DEFAULT_TYPE = TYPE_FULL_PERIOD;

    /**
     * 获取屏蔽类型描述
     * 
     * @param type 屏蔽类型
     * @return 描述
     */
    public static String getTypeDescription(Integer type) {
        if (type == null) {
            return "未知类型";
        }
        switch (type) {
            case 1:
                return "整个时间段屏蔽";
            case 2:
                return "固定时间段屏蔽";
            default:
                return "未知类型";
        }
    }

    /**
     * 验证屏蔽类型是否有效
     * 
     * @param type 屏蔽类型
     * @return 是否有效
     */
    public static boolean isValidType(Integer type) {
        return type != null && (type.equals(TYPE_FULL_PERIOD) || type.equals(TYPE_PERIOD_TIME));
    }
}
