package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WxUrlLinkQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 短链id
     */
    private String id;

    /**
     * 短链地址
     */
    private String shortUrl;

    /**
     * 小程序短链地址
     */
    private String urlLink;

    /**
     * 状态，0：无效，1：有效
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 使用场景描述
     */
    private String linkDesc;
    /**
     * 分页参数
     */
    private PageParameter pageParameter;
}
