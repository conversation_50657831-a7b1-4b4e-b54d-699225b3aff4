package com.xhqb.spectre.common.utils;

import com.xhqb.spectre.common.enums.MessageTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.UnknownHostException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtil {

    private static final Pattern TPL_PARAM_PATTERN = Pattern.compile("\\[\\*]");

    public static InetAddress getInetAddress() throws UnknownHostException {
        try {
            InetAddress candidateAddress = null;
            for (Enumeration ifs = NetworkInterface.getNetworkInterfaces(); ifs.hasMoreElements(); ) {
                NetworkInterface networkInterface = (NetworkInterface) ifs.nextElement();
                for (Enumeration addresses = networkInterface.getInetAddresses(); addresses.hasMoreElements(); ) {
                    InetAddress inetAddress = (InetAddress) addresses.nextElement();
                    if (!inetAddress.isLoopbackAddress()) {
                        if (inetAddress.isSiteLocalAddress()) {
                            return inetAddress;
                        } else if (candidateAddress == null) {
                            candidateAddress = inetAddress;
                        }
                    }
                }
            }
            if (candidateAddress != null) {
                return candidateAddress;
            }
            InetAddress jdkSuppliedAddress = InetAddress.getLocalHost();
            if (jdkSuppliedAddress == null) {
                throw new UnknownHostException();
            }
            return jdkSuppliedAddress;
        } catch (Exception e) {
            UnknownHostException unknownHostException = new UnknownHostException(
                "Failed to determine LAN address: " + e);
            unknownHostException.initCause(e);
            throw unknownHostException;
        }
    }

    /**
     * string转为List结构
     *
     * @param isps 逗号分隔的字符串
     * @return
     */
    public static List<String> strToList(String isps) {
        return StringUtils.isEmpty(isps) ? Collections.emptyList() : Arrays.asList(isps.split(","));
    }

    /**
     * 获取短信模板参数个数
     *
     * @param tplContent
     * @return
     */
    public static Integer getTplParamCount(String tplContent) {
        Integer count = 0;
        Matcher matcher = TPL_PARAM_PATTERN.matcher(tplContent);
        while (matcher.find()) {
            count++;
        }
        return count;
    }

    /**
     * 是否验证码短信
     *
     * @param smsType
     * @return
     */
    public static boolean isVerifySms(String smsType) {
        return MessageTypeEnum.VERIFY.getMessageType().equalsIgnoreCase(smsType);
    }

    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();
        Set<String> emptyNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    /**
     * 空数字直接转换成0
     *
     * @param num
     * @return
     */
    public static int nullToZero(Integer num) {
        if (Objects.isNull(num)) {
            return 0;
        }
        return num;
    }

}
