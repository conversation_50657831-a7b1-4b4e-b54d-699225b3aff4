package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ParamsCodeDO;
import com.xhqb.spectre.common.dal.entity.ParamsDO;
import com.xhqb.spectre.common.dal.query.ParamsQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ParamsCodeMapper {
    Integer countByQuery(@Param("query") ParamsQuery paramsQuery);

   List<ParamsCodeDO> selectByQuery(@Param("query") ParamsQuery paramsQuery);

    ParamsCodeDO selectByCode(String code);

    int insertSelective(ParamsCodeDO paramsCodeDO);

    int  updateDeleteTagByCodeList(@Param("codeList") List<String> codeList);

    List<ParamsCodeDO> selectByType(String type);

    List<ParamsCodeDO> selectAll();

}
