package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AreasInfoDO implements Serializable {

    private static final long serialVersionUID = -1071084939109447881L;

    private String id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.parent_id
     *
     * @mbggenerated
     */
    private String parentId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.level_type
     *
     * @mbggenerated
     */
    private String levelType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.name
     *
     * @mbggenerated
     */
    private String name;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.short_name
     *
     * @mbggenerated
     */
    private String shortName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.parent_path
     *
     * @mbggenerated
     */
    private String parentPath;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.province
     *
     * @mbggenerated
     */
    private String province;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.city
     *
     * @mbggenerated
     */
    private String city;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.district
     *
     * @mbggenerated
     */
    private String district;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.province_short_name
     *
     * @mbggenerated
     */
    private String provinceShortName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.city_short_name
     *
     * @mbggenerated
     */
    private String cityShortName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.district_short_name
     *
     * @mbggenerated
     */
    private String districtShortName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.province_pinyin
     *
     * @mbggenerated
     */
    private String provincePinyin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.city_pinyin
     *
     * @mbggenerated
     */
    private String cityPinyin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.district_pinyin
     *
     * @mbggenerated
     */
    private String districtPinyin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.city_code
     *
     * @mbggenerated
     */
    private String cityCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.zip_code
     *
     * @mbggenerated
     */
    private String zipCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.pinyin
     *
     * @mbggenerated
     */
    private String pinyin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.jian_pin
     *
     * @mbggenerated
     */
    private String jianPin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.first_char
     *
     * @mbggenerated
     */
    private String firstChar;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.lng
     *
     * @mbggenerated
     */
    private String lng;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.lat
     *
     * @mbggenerated
     */
    private String lat;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.remark1
     *
     * @mbggenerated
     */
    private String remark1;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_areas_info.remark2
     *
     * @mbggenerated
     */
    private String remark2;

}