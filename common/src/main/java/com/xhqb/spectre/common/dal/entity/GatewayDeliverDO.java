package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GatewayDeliverDO implements Serializable {

    private static final long serialVersionUID = 9000589722424608239L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.msg_id
     *
     * @mbggenerated
     */
    private String msgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.user_name
     *
     * @mbggenerated
     */
    private String userName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.submit_time
     *
     * @mbggenerated
     */
    private String submitTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.done_time
     *
     * @mbggenerated
     */
    private String doneTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.sms_status
     *
     * @mbggenerated
     */
    private String smsStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.dest_terminal_id
     *
     * @mbggenerated
     */
    private String destTerminalId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.message_status
     *
     * @mbggenerated
     */
    private Integer messageStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.delivered_time
     *
     * @mbggenerated
     */
    private Integer deliveredTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_gateway_deliver.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;
}