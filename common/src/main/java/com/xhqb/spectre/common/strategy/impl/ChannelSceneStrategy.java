package com.xhqb.spectre.common.strategy.impl;

import com.xhqb.spectre.common.constant.GenericConstants;
import com.xhqb.spectre.common.dal.entity.FailResendRuleDO;
import com.xhqb.spectre.common.dto.FailResendSceneContextDTO;
import com.xhqb.spectre.common.strategy.FailResendSceneStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 渠道场景策略实现
 * scene_type = 1
 */
@Slf4j
public class ChannelSceneStrategy implements FailResendSceneStrategy {

    public static final String SCENE_TYPE_CHANNEL = "1";
    
    @Override
    public String getSceneType() {
        return SCENE_TYPE_CHANNEL;
    }
    
    @Override
    public boolean isRuleMatched(FailResendRuleDO rule, FailResendSceneContextDTO context) {
        if (!SCENE_TYPE_CHANNEL.equals(rule.getSceneType())) {
            return false;
        }
        
        String sceneValue = rule.getSceneValue();
        String channelCode = context.getSmsOrderDO().getChannelCode();
        
        if (StringUtils.isBlank(sceneValue) || StringUtils.isBlank(channelCode)) {
            log.debug("渠道场景匹配失败: sceneValue={}, channelCode={}", sceneValue, channelCode);
            return false;
        }
        Set<String> channelCodes = Arrays.stream(sceneValue.split(GenericConstants.COMMA))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        
        boolean matched = channelCodes.contains(channelCode);
        
        log.debug("渠道场景匹配结果: ruleId={}, sceneValue={}, channelCode={}, matched={}", 
                rule.getRuleId(), sceneValue, channelCode, matched);
        
        return matched;
    }
}
