package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FailResendQuery {
    /**
     * 模版编码
     */
    private String tplCode;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 策略 ID
     */
    private String strategyId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间开始时间
     */

    private String startTime;
    /**
     * 创建时间结束时间
     */
    private String endTime;

    /**
     * 分页参数
     *
     * @ignore
     */
    private PageParameter pageParameter;
}
