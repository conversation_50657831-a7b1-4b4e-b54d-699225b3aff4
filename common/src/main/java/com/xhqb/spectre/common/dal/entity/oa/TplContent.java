package com.xhqb.spectre.common.dal.entity.oa;

import lombok.Data;

import java.util.Date;

@Data
public class TplContent {
    /**
     * 主键
     */
    private Long id;

    /**
     * 短信内容 ID
     */
    private String contentId;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 内容类型
     */
    private String type;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 原始内容
     */
    private String originalContent;

    /**
     * 报备通过内容
     */
    private String approvedContent;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 签名 id
     */
    private String signId;

    /**
     * 模板编码
     */
    private String tplCode;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 短信类型(market)
     */
    private String smsTypeCode;

    /**
     * 渠道名称 code
     */
    private String channelCode;

    /**
     * tag
     */
    private Integer tag;

    /**
     * 变量参数类型
     */
    private String paramType;

    /**
     * 变量参数长度
     */
    private String paramLen;
}
