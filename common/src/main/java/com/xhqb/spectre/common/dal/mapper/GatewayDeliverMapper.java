package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.GatewayDeliverDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface GatewayDeliverMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_deliver
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(String msgId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_deliver
     *
     * @mbggenerated
     */
    int insert(GatewayDeliverDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_deliver
     *
     * @mbggenerated
     */
    int insertSelective(GatewayDeliverDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_deliver
     *
     * @mbggenerated
     */
    GatewayDeliverDO selectByPrimaryKey(String msgId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_deliver
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(GatewayDeliverDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_deliver
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(GatewayDeliverDO record);

    /**
     * 批量插入或修改
     *
     * @param gatewayDeliverDOList 网关推送记录
     * @return 成功条数
     */
    int insertOrUpdateBatch(List<GatewayDeliverDO> gatewayDeliverDOList);
}