package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/*
 * @Author: huangyanxiong
 * @Date: 2021/12/10 13:56
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum PhoneStatusEnum {

    NOT_QUERY(-1L, "未查询"),
    EMPTY_NUMBER(0L, "空号"),
    NORMAL(1L, "正常"),
    SUSPENDED(2L, "停机"),
    NOT_FOUND(3L, "库无"),
    SILENCE_NUMBER(4L, "沉默号"),
    RISK_NUMBER(5L, "沉默号"),
    UNKNOWN(99L, "未知");

    private Long status;

    private String description;

    /**
     * 获取描述
     *
     * @param status
     * @return
     */
    public static String getDescription(Long status) {
        return Arrays.stream(values())
                .filter(item -> item.getStatus().equals(status))
                .findFirst().orElse(PhoneStatusEnum.UNKNOWN).getDescription();
    }
}
