package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.test.tool.TestContentTaskDO;
import com.xhqb.spectre.common.dal.query.TestContentTaskQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface TestContentTaskMapper {
    // select by id
    TestContentTaskDO selectByPrimaryKey(Integer id);

    // insert
    int insertBySelective(TestContentTaskDO record);

    int updateByPrimaryKeySelective(TestContentTaskDO record);

    Integer countByQuery(@Param("query") TestContentTaskQuery testContentTaskQuery);

    List<TestContentTaskDO> selectByQuery(@Param("query") TestContentTaskQuery testContentTaskQuery);

    TestContentTaskDO selectByTaskId(String taskId);

    List<TestContentTaskDO> selectByTaskStatus(@Param("taskStatus") int taskStatus,@Param("startTime") Date startTime,@Param("endTime") Date endTime);

    Integer countUnionDataByQuery(@Param("query") TestContentTaskQuery testContentTaskQuery);

    List<TestContentTaskDO> selectUnionDataByQuery(@Param("query") TestContentTaskQuery query);
}
