package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/10/13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsUplinkRelationOrderQuery implements Serializable {

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 发送开始时间
     */

    private long startTimestamp;

    /**
     * 发送结束时间
     */
    private long endTimestamp;

    /**
     * 大小
     */
    private Integer pageSize;
}
