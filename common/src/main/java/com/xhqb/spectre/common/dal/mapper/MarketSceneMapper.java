package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.MarketSceneDO;
import com.xhqb.spectre.common.dal.query.MarketSceneQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MarketSceneMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_market_scene
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_market_scene
     *
     * @mbggenerated
     */
    int insert(MarketSceneDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_market_scene
     *
     * @mbggenerated
     */
    int insertSelective(MarketSceneDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_market_scene
     *
     * @mbggenerated
     */
    MarketSceneDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_market_scene
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(MarketSceneDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_market_scene
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(MarketSceneDO record);

    /**
     * 营销场景查询总数量
     *
     * @param marketSceneQuery
     * @return
     */
    Integer countByQuery(MarketSceneQuery marketSceneQuery);

    /**
     * 营销场景查询列表
     *
     * @param marketSceneQuery
     * @return
     */
    List<MarketSceneDO> selectByQuery(MarketSceneQuery marketSceneQuery);

    /**
     * 查询所有的营销场景信息
     *
     * @param status 状态，0：无效，1：有效 , 为空查所有
     * @return
     */
    List<MarketSceneDO> listAll(Integer status);

    /**
     * 删除营销场景与模板关联信息
     *
     * @param tplId
     * @param creator
     */
    void deleteMarketSceneTpl(@Param("tplId") Integer tplId, @Param("creator") String creator);

    /**
     * 保存营销场景与模板关联信息
     *
     * @param tplId
     * @param marketSceneId
     * @param creator
     */
    void insertMarketSceneTpl(@Param("tplId") Integer tplId, @Param("marketSceneId") Integer marketSceneId, @Param("creator") String creator);

    /**
     * 根据模板ID查询关联的营销场景ID列表
     *
     * @param tplId
     * @return
     */
    List<Integer> selectMarketSceneIdByTplId(@Param("tplId") Integer tplId);

    MarketSceneDO selectByName(@Param("name") String marketSceneName);
}