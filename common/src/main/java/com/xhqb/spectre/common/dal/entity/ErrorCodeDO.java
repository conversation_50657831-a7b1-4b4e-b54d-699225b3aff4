package com.xhqb.spectre.common.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * t_error_code
 * <AUTHOR>
@Data
public class ErrorCodeDO implements Serializable {
    /**
     * sumbit或devliver
     */
    private String type;

    /**
     * 错误码编码
     */
    private Integer xhErrCode;

    /**
     * 错误码描述
     */
    private String codeDesc;

    /**
     * 是否重发 0否 1是
     */
    private Integer retry;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}