package com.xhqb.spectre.common.dal.entity.test;

import lombok.Data;

import java.util.Date;

/**
 * 测试模版
 */
@Data
public class TestTplDO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 渠道账号
     */
    private Integer channelAccountId;

    /**
     * 测试模板名称
     */
    private String name;

    /**
     * 短信模版id
     */
    private Integer smsTplId;

    /**
     * 参数信息
     */
    private String params;

    /**
     * 检测频次 0:立即执行
     */
    private Integer checkTimes;

    /**
     * 测试来源 0:测试库，1:自定义
     */
    private Integer source;

    /**
     * 自定义的测试手机号 source =1
     */
    private String mobiles;

    /**
     * 号码个数
     */
    private Integer mobileCount;

    /**
     * 最大测试次数
     */
    private Integer maxTimes;

    /**
     * 测试时间范围
     */
    private String timePeriod;

    /**
     * 类型和权重 Json [{"type":0,"weight":40},{"type":1,"weight":10},{"type":2,"weight":50}]
     */
    private String typeWeight;

    /**
     * 异常通知账号
     */
    private String notifyAccount;

    /**
     * 启用状态(0:未启用 1:启用)
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 短信类型id
     */
    private String smsTypeCode;

    /**
     * 发送量级
     */
    private Integer senderLevel;
}
