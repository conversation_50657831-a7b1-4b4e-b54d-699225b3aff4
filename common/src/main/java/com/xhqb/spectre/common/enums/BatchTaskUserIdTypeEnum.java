package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 名单中的用户标识类型
 *
 * <AUTHOR>
 * @date 2021/9/24
 */
@Getter
@AllArgsConstructor
public enum BatchTaskUserIdTypeEnum {


    CID(1, "cid"),
    MOBILE(2, "手机号");

    private final Integer type;
    private final String description;


    /**
     * dataType 为mobile 或者 cid
     *
     * @param dataType
     * @return
     */
    public static Integer getUserIdType(String dataType) {
        if (StringUtils.equalsIgnoreCase("cid", dataType)) {
            return BatchTaskUserIdTypeEnum.CID.getType();
        } else if (StringUtils.equalsIgnoreCase("mobile", dataType)) {
            return BatchTaskUserIdTypeEnum.MOBILE.getType();
        }
        return null;
    }
}
