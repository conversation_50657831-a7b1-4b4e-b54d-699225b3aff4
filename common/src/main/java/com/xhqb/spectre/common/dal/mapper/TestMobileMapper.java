package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.test.TestMobileDO;
import com.xhqb.spectre.common.dal.query.ChannelTestMobileQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface TestMobileMapper {
    Integer countByQuery(@Param("query") ChannelTestMobileQuery channelTestMobileQuery);

    List<TestMobileDO> listByQuery(@Param("query") ChannelTestMobileQuery channelTestMobileQuery);

    List<TestMobileDO> selectByMobiles(@Param("mobiles") List<String> mobiles);

    int insertBySelective(TestMobileDO testMobileDO);

    int updateDeleteByIdList(@Param("idList") List<Integer> idList);

    int countAll();

    List<TestMobileDO> selectAll();

    int updateDeleteByMobileList(List<String> mobiles);

    int updateDetectTimeByMobileList(@Param("mobiles") List<String> mobiles, @Param("expiryDate") Date expiryDate);

    List<TestMobileDO> selectInvalid();

    int updateStatusByMobileList(@Param("mobiles")List<String> mobiles);

    TestMobileDO selectByUpdateTimeLimit();


    int updateByPrimaryKeySelective(TestMobileDO mobileDO);
}
