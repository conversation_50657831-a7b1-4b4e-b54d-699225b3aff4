package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 网关分配账号
 *
 * <AUTHOR>
 * @date 2021/10/26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClientChannelAccountQuery implements Serializable {

    /**
     * 服务代码
     */
    private String serviceId;
    /**
     * 账号
     */
    private String username;

    /**
     * 分页参数
     */
    private PageParameter pageParameter;
}
