package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.Data;

@Data
public class AutoTestTaskQuery {

    /**
     * 任务名称，模糊查询
     */
    private String likeName;

    /**
     * 任务类型，0：TOP模板，1：自定义模板
     */
    private Integer type;

    /**
     * 是否启用，0：禁用，1：启用
     */
    private Integer enable;

    /**
     * @ignore
     */
    private Integer isDelete = 0;

    /**
     * @ignore 分页参数
     */
    private PageParameter pageParameter;
}
