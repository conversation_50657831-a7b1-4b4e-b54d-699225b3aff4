package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskParamDO implements Serializable {

    private static final long serialVersionUID = -531743496562621856L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_param.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_param.task_id
     *
     * @mbggenerated
     */
    private Integer taskId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_param.sendStatus
     *
     * @mbggenerated
     */
    private Integer sendStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_param.file_md5
     *
     * @mbggenerated
     */
    private String fileMd5;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_param.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_param.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_param.param_json_array
     *
     * @mbggenerated
     */
    private String paramJsonArray;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_batch_task_param.is_delete
     *
     * @mbggenerated
     */
    private Integer isDelete;

    /**
     * 文件切分起始位置
     */
    private Integer startOffset;

    /**
     * 文件切分结束位置
     */
    private Integer endOffset;
}