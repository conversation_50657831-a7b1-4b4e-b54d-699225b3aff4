package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 渠道账号查询
 *
 * <AUTHOR>
 * @date 2021/9/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelAccountQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 渠道id
     */
    private Integer id;

    /**
     * 渠道名称
     */
    private String channelName;
    /**
     * 短信类型编码
     */
    private String smsTypeCode;
    /**
     * 状态，0：无效，1：有效
     */
    private Integer status;
    /**
     * 账号名称
     */
    private String key;
    /**
     * 分页参数
     */
    private PageParameter pageParameter;
    /**
     * 协议，1：http；2：cmpp
     */
    private Integer protocol;
    /**
     * 签名ID
     */
    private Integer signId;

    /**
     * 页数
     */
    private Integer pageNum;

    /**
     * 页大小
     */
    private Integer pageSize;
}
