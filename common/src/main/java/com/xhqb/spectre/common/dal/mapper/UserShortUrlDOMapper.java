package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.UserShortUrlDO;
import com.xhqb.spectre.common.dal.query.UserShortUrlQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface UserShortUrlDOMapper {

    List<UserShortUrlDO> loadUserShortUrlList(@Param("selectedDate") Date selectedDate, @Param("lastId") Long lastId, @Param("pageSize") Integer pageSize);

    List<UserShortUrlDO> selectByLimitTime(@Param("selectedDate") Date selectedDate);

    int insertSelective(UserShortUrlDO userShortUrlDO);

    UserShortUrlDO selectByPrimaryKey(Long id);

    Integer countByQuery(@Param("query") UserShortUrlQuery userShortUrlQuery);

    List<UserShortUrlDO> selectByQuery(@Param("query") UserShortUrlQuery userShortUrlQuery);

    void batchInsert(@Param("list") List<UserShortUrlDO> modelList);

    UserShortUrlDO selectByCode(@Param("code") String userCode);

    List<UserShortUrlDO> selectByShortCodes(@Param("list") List<String> shortCodes);

    List<UserShortUrlDO> selectByMobile(@Param("mobile") String mobile, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
