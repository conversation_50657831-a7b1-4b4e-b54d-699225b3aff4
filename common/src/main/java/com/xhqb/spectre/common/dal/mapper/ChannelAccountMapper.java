package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ChannelAccountDO;
import com.xhqb.spectre.common.dal.query.ChannelAccountQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ChannelAccountMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account
     *
     * @mbggenerated
     */
    int insert(ChannelAccountDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account
     *
     * @mbggenerated
     */
    int insertSelective(ChannelAccountDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account
     *
     * @mbggenerated
     */
    ChannelAccountDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(ChannelAccountDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_channel_account
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(ChannelAccountDO record);

    List<ChannelAccountDO> selectEnum(Integer status);

    ChannelAccountDO queryPartnerByName(@Param("partnerName") String partnerName, @Param("messageType") String messageType);

    List<ChannelAccountDO> selectByIdList(@Param("idList") List<Integer> idList);


    /**
     * 分页查询渠道账号列表总页数
     *
     * @param channelAccountQuery
     * @return
     */
    Integer countByQuery(ChannelAccountQuery channelAccountQuery);

    /**
     * 分页查询渠道账号列表数据
     *
     * @param channelAccountQuery
     * @return
     */
    List<ChannelAccountDO> selectByQuery(ChannelAccountQuery channelAccountQuery);

    /**
     * 渠道账号刷新缓存查询
     *
     * @param id
     * @param pageSize
     * @return
     */
    List<ChannelAccountDO> refreshCacheQuery(@Param("id") Integer id, @Param("pageSize") Integer pageSize);

    /**
     * 查询所有可用的账号
     * @return
     */
    List<ChannelAccountDO> selectAllEnabled();

    /**
     * 查询所有的账号信息
     *
     * @return
     */
    List<ChannelAccountDO> selectAll();
}