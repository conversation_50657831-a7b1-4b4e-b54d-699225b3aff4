package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.TopTplChannelDO;
import com.xhqb.spectre.common.dal.entity.TplChannelDO;
import com.xhqb.spectre.common.dal.entity.support.TplChannelDOSupport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Stream;

@Mapper
public interface TplChannelMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl_channel
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl_channel
     *
     * @mbggenerated
     */
    int insert(TplChannelDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl_channel
     *
     * @mbggenerated
     */
    int insertSelective(TplChannelDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl_channel
     *
     * @mbggenerated
     */
    TplChannelDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl_channel
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(TplChannelDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_tpl_channel
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(TplChannelDO record);

    void deleteByTplId(Integer tplId);

    List<TplChannelDO> selectByTplId(Integer tplId);

    /**
     * 报表特殊使用
     * @param tplIdList
     * @return
     */
    List<TopTplChannelDO> selectByTplIdList(@Param("tplIdList") List<Integer> tplIdList);

    List<TplChannelDO> selectByChannelAccountId(Integer channelAccountId);


    List<TplChannelDOSupport> selectSupportByTplId(Integer tplId);

    void deleteByTplIdList(@Param("tplIdList") List<Integer> tplIdList);

    void deleteByCover(@Param("tplIdList") List<Integer> tplIdList,
                       @Param("channelAccountIdList") List<Integer> channelAccountIdList);

    void saveByTplChannelDOList(@Param("tplChannelDOList") List<TplChannelDO> tplChannelDOList);

    int updateWeightByPrimaryKey(@Param("id") int id, @Param("weight") int weight);
}