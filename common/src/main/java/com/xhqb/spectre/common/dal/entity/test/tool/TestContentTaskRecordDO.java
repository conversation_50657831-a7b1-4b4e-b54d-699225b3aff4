package com.xhqb.spectre.common.dal.entity.test.tool;

import lombok.Data;

import java.util.Date;

/**
 * 文案测试子表的数据对象
 */
@Data
public class TestContentTaskRecordDO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 测试任务ID
     */
    private String taskId;

    /**
     * 测试短信文案
     */
    private String content;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 短信签名ID
     */
    private Integer smsSignId;

    /**
     * 测试模版
     */
    private String smsTplCode;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 发送状态，0：成功，-1：未知，其余失败
     */
    private Integer sendStatus;

    /**
     * 回执状态，0：成功，-1：未知，其余失败
     */
    private Integer reportStatus;

    /**
     * app上报状态（0：初始华 1：成功 2：被拦截）
     */
    private Integer appReportStatus;

    /**
     * app上报时间
     */
    private Integer appReportTime;

    /**
     * app上报扩展字段，json格式
     */
    private String appReportExtend;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 模板编码
     */
    private String tplCode;
}
