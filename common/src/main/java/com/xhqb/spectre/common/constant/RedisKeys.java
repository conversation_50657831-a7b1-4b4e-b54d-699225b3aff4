package com.xhqb.spectre.common.constant;

/**
 * redis key
 *
 * <AUTHOR>
 * @date 2021/9/21
 */
public interface RedisKeys {

    /**
     * 管理后台配置的redis缓存key前缀
     */
    String ADMIN_CACHE_KEY_PREFIX = "spectre-admin";

    /**
     * 渠道账号redis key信息
     *
     * <AUTHOR>
     * @date 2021/9/21
     */
    interface ChannelAccountKeys {
        /**
         * 渠道账户redis缓存(hash)
         */
        String CHANNEL_ACCOUNT_HASH_KEY = ADMIN_CACHE_KEY_PREFIX + "channel:account:hash";
    }

    /**
     * 模板redis key信息
     */
    interface TplKeys {
        String TPL_HASH_KEY = ADMIN_CACHE_KEY_PREFIX + ":tpl";
    }

    /**
     * 业务应用redis key信息
     */
    interface AppKeys {
        String APP_HASH_KEY = ADMIN_CACHE_KEY_PREFIX + ":app";
    }

    /**
     * 渠道账号屏蔽redis key信息
     */
    interface ChannelAccountDisableKeys {
        String DISABLE_HASH_KEY = ADMIN_CACHE_KEY_PREFIX + ":channel-account-disable";
    }

    /**
     * 手机号黑名单redis key信息
     */
    interface MobileBlackKeys {
        String MOBILE_BLACK_SET_KEY = ADMIN_CACHE_KEY_PREFIX + ":mobile-black-list";
    }

    /**
     * 手机号白名单redis key信息
     */
    interface MobileWhiteKeys {
        String MOBILE_WHITE_SET_KEY = ADMIN_CACHE_KEY_PREFIX + ":mobile-white-list";
    }

    /**
     * 应用发送频率配置redis key信息
     */
    interface AppSendLimitKeys {
        String APP_SEND_LIMIT_HASH_KEY = ADMIN_CACHE_KEY_PREFIX + ":app_send_limit";
    }

    /**
     * 群发短信redis key信息
     */
    interface BatchTaskKeys {
        /**
         * 批量任务数量缓存(string类型)
         */
        String BATCH_TASK_COUNT_STR_KEY = ADMIN_CACHE_KEY_PREFIX + ":batch:task:count";
        /**
         * 文件上传标记 (用于查询文件上传结果)(hash类型)
         */
        String BATCH_TASK_UPLOAD_FLAG_HASH_KEY = ADMIN_CACHE_KEY_PREFIX + ":batch:task:upload:flag";
        /**
         * 文件上传结果(string类型)
         */
        String BATCH_TASK_UPLOAD_RESULT_STR_KEY = ADMIN_CACHE_KEY_PREFIX + ":batch:task:upload:result";
        /**
         * 文件取消标记
         */
        String BATCH_TASK_CANCELED_FLAG_HASH_KEY = ADMIN_CACHE_KEY_PREFIX + ":batch:task:canceled:flag";

        /**
         * 群发任务兜底缓存(string类型)
         */
        String BATCH_TASK_COVER_STR_KEY = ADMIN_CACHE_KEY_PREFIX + ":batch:task:cover";
        /**
         * 群发cid策略缓存(string类型)
         */
        String CID_STRATEGY_STR_KEY = ADMIN_CACHE_KEY_PREFIX + ":batch:cid:strategy";
    }


    interface Messages {
        String MESSAGE_SEND_KEY = "spectre:message-send";
        String ORDER_LOCK_KEY = "spectre:order-lock:";
    }

    /**
     * Cmpp相关的redis key信息
     */
    interface CmppKeys {

        String CHANNEL_ACCOUNT_ALIVE_KEY_PREFIX = "spectre:cmpp:alive";

        /**
         * 渠道账号在线状态（spectre:cmpp:alive:${channelAccountId}）
         */
        String CHANNEL_ACCOUNT_ALIVE_KEY = CHANNEL_ACCOUNT_ALIVE_KEY_PREFIX + ":{0}";

        /**
         * 渠道账号重试次数
         */
        String CHANNEL_ACCOUNT_RETRY_KEY = "spectre:cmpp:channel:retry:{0}";


        String CHANNEL_ACCOUNT_STATUS_KEY = "spectre:cmpp:channel:status:{0}";
    }

    /**
     * Dispatcher相关的redis key信息
     */
    interface DispatcherKeys {
        /**
         * 选中的渠道（spectre-dispatcher:selected-channel:${tplCode}:${signNameMd5}:${mobile}）
         */
        String SELECTED_CHANNEL_KEY = "spectre-dispatcher:selected:{0}:{1}:{2}";

        /**
         * 订单选中的渠道，用于重试（spectre-dispatcher:selected:${orderId}）
         */
        String ORDER_SELECTED_CHANNEL_KEY = "spectre-dispatcher:selected:{0}";

        /**
         * t_sms_order信息（spectre-dispatcher:order:${orderId}:${resend}）
         */
        String ORDER_KEY = "spectre-dispatcher:order:{0}:{1}";
    }

    /**
     * Log Server相关的redis key信息
     */
    interface LogServerKeys {

        /**
         * 短信重发的分布式锁（spectre-resend-lock:order:${orderId}:${resend}）
         */
        String RESEND_LOCK_KEY = "spectre-resend-lock:order:{0}:{1}";

        String SEND_UDP_LOCK_KEY = "spectre-send-udp-lock:{0}";

        /**
         * submit结果的分布式锁，用于去重（spectre-logserver:submit:lock:${orderId}:${resend}:${result}）
         */
        String SUBMIT_RESULT_LOCK_KEY = "spectre-logserver:submit:lock:{0}:{1}:{2}";

        /**
         * deliver结果的分布式锁，用于去重（spectre-logserver:deliver:lock:${orderId}:${resend}:${result}）
         */
        String DELIVER_RESULT_LOCK_KEY = "spectre-logserver:deliver:lock:{0}:{1}:{2}";

        /**
         * 短信订单表（t_sms_order）批量写入的分布式锁
         */
        String SMS_ORDER_WRITE_LOCK_KEY = "spectre-logserver:batch-save:t_sms_order";

        /**
         * 模板使用表（t_tpl_usage）批量写入的分布式锁
         */
        String TPL_USAGE_WRITE_LOCK_KEY = "spectre-logserver:batch-save:t_tpl_usage";
    }


    String USER_SHORT_URL_PRE_KEY = "spectre:shorturl:shortcode:";

    String ENCODE_DATA_COUNT_KEY = "spectre:encode-data:count:";

    String ZBX_SHORT_URL_PRE_KEY = "spectre:zbx:shorturl:shortcode:";
}
