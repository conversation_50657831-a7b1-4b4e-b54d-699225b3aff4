package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.BatchDetectDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BatchDetectMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_detect
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_detect
     *
     * @mbggenerated
     */
    int insert(BatchDetectDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_detect
     *
     * @mbggenerated
     */
    int insertSelective(BatchDetectDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_detect
     *
     * @mbggenerated
     */
    BatchDetectDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_detect
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(BatchDetectDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_detect
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(BatchDetectDO record);

    /**
     * 批量保存
     *
     * @param list
     */
    void batchInsert(@Param("list") List<BatchDetectDO> list);

    /**
     * 根据id更新taskId
     *
     * @param list
     * @param taskId
     * @return
     */
    int updateTaskIdByIdList(@Param("list") List<Integer> list, @Param("taskId") Integer taskId);

    /**
     * 根据fileMd5查询所有detect 主键信息
     *
     * @param fileMd5
     * @return
     */
    List<Integer> selectIdByFileMd5(@Param("fileMd5") String fileMd5);


    /**
     * 导出查询文件检测结果
     *
     * @param taskId
     * @param lastId
     * @param pageSize
     * @param type     检测类型 0->无效CID 1->空号类型 2->停机类型 3->参数缺失 4->数据重复
     * @return
     */
    List<BatchDetectDO> exportQuery(@Param("taskId") Integer taskId, @Param("lastId") Integer lastId, @Param("pageSize") Integer pageSize, @Param("type") Integer type);

    /**
     * 导出文件检测的数据
     *
     * @param taskId
     * @param type   检测类型 0->无效CID 1->空号类型 2->停机类型 3->参数缺失 4->数据重复
     * @return
     */
    Integer exportCount(@Param("taskId") Integer taskId, @Param("type") Integer type);
}