package com.xhqb.spectre.common.enums;

public enum MsgSendResultEnum {
    /**
     * 参数缺失
     */
    MISS_PARAM("missParam", "参数缺失"),
    /**
     * 缺失request参数
     */
    MISS_REQUEST_PARAM("missRequestParam", "缺失request参数"),
    /**
     * 缺失outBizId参数
     */
    MISS_ID_PARAM("missOutBizIdParam", "缺失outBizId参数"),
    /**
     * 缺失渠道参数
     */
    MISS_CHANNEL_PARAM("missChannelParam", "缺失渠道参数"),

    //自定义批量短信发送
    /**
     * 缺失短信内容参数
     */
    MISS_MESSAGE_CONTENT("missContentParam", "缺失短信内容参数"),
    /**
     *缺失短信类型参数
     */
    MISS_MESSAGE_TYPE("missMsgTypeParam", "缺失短信类型参数"),
    /**
     * 缺失短信签名参数
     */
    MISS_MESSAGE_SIGN("missMsgSignParam", "缺失短信签名参数"),
    /**
     * 缺失短信参数号码集
     */
    MISS_MSG_PARAM_PHONES("missMsgParamPhonesParam", "缺失短信参数号码集"),

    /**
     * 缺失短信发送权重
     */
    MISS_MSG_SEND_WEIGHT("missMsgSendWeight", "缺失短信发送权重"),
    /**
     * 缺失短信模板
     */
    MISS_MSG_TEMPLATE("missMsgTemplate", "缺失短信模板"),
    /**
     * 短信模板不存在
     */
    MSG_TEMPLATE_NOT_EXIST("msgTemplateNotExist", "短信模板不存在"),

    /**
     * 请求参数非法
     */
    ILLEGAL_PARAM("illegalParam", "请求参数非法"),
    /**
     * 不支持该号码
     */
    ILLEGAL_NUM("illegalNum", "不支持该号码"),

    //发送频率、重发问题
    /**
     * 重复发送同一条短信
     */
    DUPLICATE_MSG("sendDuplicateMsg", "重复发送同一条短信"),
    /**
     * 同号码短时间内发送了相同短信内容
     */
    FREQUENT_IN_MIN("sendFrequentlyInMin", "同号码短时间内发送了相同短信内容"),
    /**
     * 同号码发送该短信模板次数达到上限
     */
    FREQUENT_IN_DAY("sendFrequentlyInDay", "同号码发送该短信模板次数达到上限"),

    //未知结果码
    /**
     * 无http结果返回
     */
    NULL_RETURN("returnNull", "无http结果返回"),

    /**
     * unknownRsCode
     */
    UNKNOWN_RS_CODE("unknownRsCode", "未定义的MsgSendResultEnum结果码"),

    //未知异常
    /**
     *系统异常
     */
    SYS_FAILURES("systemFailures", "系统异常"),

    /**
     * 成功
     */
    SUCCESS("success", "成功"),
    /**
     * 失败
     */
    FAILED("fail", "失败");

    /**
     * 结果码
     */
    private String code;

    /**
     * 结果内容
     */
    private String message;

    public String getCode() {
        return code;
    }



    public String getMessage() {
        return message;
    }



    /**
     * constructor
     * @param code
     * @param message
     */
    MsgSendResultEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据状态码获取枚举
     * @param code
     * @return
     */
    public static MsgSendResultEnum getByCode(String code) {
        MsgSendResultEnum[] arr = values();
        int length = arr.length;

        for (int i = 0; i < length; ++i) {
            MsgSendResultEnum statusEnum = arr[i];
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }

        return null;
    }
}
