package com.xhqb.spectre.common.mq;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CollectMessageMQ extends BaseBodyMessage {

    private static final long serialVersionUID = -8448129245456663351L;

/**
 // 批量手机号 13591992345-> 东风，9000，13581882934->南极，80000
 private Map<String, String> phoneNumMap;

 // 如果批量单文本, phoneList赋值
 private Set<String> phoneList;

 // 如果批量单文本，isUniqueText=true
 private Boolean isUniqueText = false;

 // 渠道商有序集
 private Set<String> partnerSet; **/

}
