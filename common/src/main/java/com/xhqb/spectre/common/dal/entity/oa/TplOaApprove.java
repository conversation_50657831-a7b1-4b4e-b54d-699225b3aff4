package com.xhqb.spectre.common.dal.entity.oa;

import lombok.Data;

import java.util.Date;

@Data
public class TplOaApprove {

    /**
     * 主键
     */
    private Long id;

    /**
     * 流程类型ID
     */
    private String flowTypeId;

    /**
     * 流程 id
     */
    private String flowId;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 原始内容
     */
    private String originalContent;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 审批状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 标题
     */
    private String title;

    /**
     * 申请人 id
     */
    private String userId;

}
