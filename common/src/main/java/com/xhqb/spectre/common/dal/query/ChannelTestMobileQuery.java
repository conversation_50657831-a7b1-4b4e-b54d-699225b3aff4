package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelTestMobileQuery implements Serializable {

    /**
     * 测试号码
     */
    private String mobile;

    /**
     * 手机号类型
     */
    private Integer type;

    /**
     * status （0 无效 1 有效）
     */
    private Integer status;

    private PageParameter pageParameter;
}
