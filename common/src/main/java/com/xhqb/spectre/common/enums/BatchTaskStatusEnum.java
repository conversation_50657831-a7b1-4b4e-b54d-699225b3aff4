package com.xhqb.spectre.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 群发短信任务状态枚举
 * 状态 0：待提交；1：已提交；2：发送中；3：已发送；4：灰度中；5：灰度结束；9：已取消
 *
 * <AUTHOR>
 * @date 2021/9/17
 */
@Getter
@AllArgsConstructor
public enum BatchTaskStatusEnum {

    UN_COMMIT(0, "待提交"),
    COMMITTED(1, "已提交"),
    SENDING(2, "发送中"),
    SENT(3, "已发送"),
    AB_SUBMIT(4, "灰度中"),
    AB_WARNING(5, "灰度结束"),
    CANCELLED(9, "已取消");

    private final Integer code;
    private final String description;


    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return
     */
    public static BatchTaskStatusEnum getByCode(Integer code) {
        for (BatchTaskStatusEnum status : values()) {
            if (Objects.equals(status.getCode(), code)) {
                return status;
            }
        }
        return null;
    }
}
