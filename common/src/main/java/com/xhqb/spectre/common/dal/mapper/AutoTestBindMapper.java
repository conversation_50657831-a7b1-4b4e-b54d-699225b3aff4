package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.AutoTestBindDO;
import com.xhqb.spectre.common.dal.query.AutoTestBindQuery;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AutoTestBindMapper {

    List<AutoTestBindDO> selectByAutoTestTaskId(@Param("autoTestTaskId") Long autoTestTaskId);

    Integer countByAutoTestTaskId(@Param("autoTestTaskId") Long autoTestTaskId);

    Date selectLastDateByAutoTestTaskId(@Param("autoTestTaskId") Long autoTestTaskId);

    Integer insert(@Param("entity") AutoTestBindDO entity);

    List<AutoTestBindDO> selectByQuery(@Param("query") AutoTestBindQuery query);

    Date selectLastDate();
}