package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SignChannelDO implements Serializable {

    private static final long serialVersionUID = 369183178633422681L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sign_channel.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sign_channel.sign_id
     *
     * @mbggenerated
     */
    private Integer signId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sign_channel.channel_account_id
     *
     * @mbggenerated
     */
    private Integer channelAccountId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sign_channel.channel_sign_id
     *
     * @mbggenerated
     */
    private String channelSignId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sign_channel.status
     *
     * @mbggenerated
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sign_channel.remark
     *
     * @mbggenerated
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sign_channel.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sign_channel.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;
}