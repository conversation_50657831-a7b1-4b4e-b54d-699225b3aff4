package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.TplOpRecordDO;
import com.xhqb.spectre.common.dal.query.TplChangeRecordsQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface TplOpRecordMapper {

    int insertSelective(TplOpRecordDO tplOpRecord);

    Integer countByQuery(@Param("query") TplChangeRecordsQuery query);


    List<TplOpRecordDO> selectByQuery(@Param("query") TplChangeRecordsQuery query);

    TplOpRecordDO selectByQueryOrderById(@Param("query") TplChangeRecordsQuery query);

    TplOpRecordDO selectByPrimaryKey(Integer id);
}
