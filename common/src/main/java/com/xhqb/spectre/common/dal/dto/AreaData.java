package com.xhqb.spectre.common.dal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/17 14:40
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AreaData implements Serializable {

    private static final long serialVersionUID = -6080149644549837507L;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 省份简名
     */
    private String provinceShortName;

    /**
     * 城市简名
     */
    private String cityShortName;
}
