package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.Data;

import java.io.Serializable;

/**
 * 群发日志查询
 *
 * <AUTHOR>
 * @date 2021/12/1
 */
@Data
public class BatchTaskLogQuery implements Serializable {

    /**
     * 群发任务ID
     */
    private Integer taskId;

    /**
     * 群发分片ID
     */
    private Integer taskParamId;

    /**
     * cid
     */
    private String cid;

    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 状态 0->失败 1->成功
     */
    private Integer status;

    /**
     * 分页参数
     */
    private PageParameter pageParameter;
}
