package com.xhqb.spectre.common.dal.page;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * common Pager.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonPager<T> implements Serializable {

    private static final long serialVersionUID = -1220101004792874251L;

    /**
     * page.
     */
//    private PageParameter page;

    /**
     * 总记录数
     */
    private Integer totalCount;

    /**
     * data.
     */
    private List<T> dataList;
}
