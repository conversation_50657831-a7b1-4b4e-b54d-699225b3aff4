package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.BatchAbJobDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface BatchAbJobMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_ab_job
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_ab_job
     *
     * @mbggenerated
     */
    int insert(BatchAbJobDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_ab_job
     *
     * @mbggenerated
     */
    int insertSelective(BatchAbJobDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_ab_job
     *
     * @mbggenerated
     */
    BatchAbJobDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_ab_job
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(BatchAbJobDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_batch_ab_job
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(BatchAbJobDO record);

    /**
     * 扫描群发灰度任务列表
     *
     * @param lastId
     * @param pageSize
     * @return
     */
    List<BatchAbJobDO> scanAbJobList(@Param("lastId") Integer lastId, @Param("pageSize") Integer pageSize);

    /**
     * 群发灰度触达率查询
     *
     * @param taskId
     * @param crateTime
     * @return
     */
    Map<String, Long> abJobRate(@Param("taskId") Integer taskId,@Param("startTime") long startTime,@Param("endTime") long endTime);
}