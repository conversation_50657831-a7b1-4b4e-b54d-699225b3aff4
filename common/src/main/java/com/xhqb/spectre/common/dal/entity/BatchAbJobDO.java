package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 群发灰度任务
 *
 * <AUTHOR>
 * @date 2021/12/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchAbJobDO implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 群发批次号
     */
    private Integer taskId;

    /**
     * 灰度测试内容  存放群发分片ID
     */
    private String content;

    /**
     * 灰度发送状态 0->灰度中 1->已完成
     */
    private Integer status;

    /**
     * 触达率
     */
    private BigDecimal rate;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 告警通知的手机号
     */
    private String mobile;

    /**
     * 创建者
     */
    private String creator;

}