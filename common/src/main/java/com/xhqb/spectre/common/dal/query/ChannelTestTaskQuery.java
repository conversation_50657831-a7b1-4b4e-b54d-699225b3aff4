package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelTestTaskQuery {
    /**
     * 渠道账号id
     */
    private Integer channelAccountId;

    /**
     * 测试模版id
     */
    private Long tplId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务名
     */
    private String name;

    private PageParameter pageParameter;
}
