package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.GatewayTplMappingDO;
import com.xhqb.spectre.common.dal.query.GatewayTplMappingQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GatewayTplMappingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_tpl_mapping
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_tpl_mapping
     *
     * @mbggenerated
     */
    int insert(GatewayTplMappingDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_tpl_mapping
     *
     * @mbggenerated
     */
    int insertSelective(GatewayTplMappingDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_tpl_mapping
     *
     * @mbggenerated
     */
    GatewayTplMappingDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_tpl_mapping
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(GatewayTplMappingDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_gateway_tpl_mapping
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(GatewayTplMappingDO record);

    Integer countByQuery(GatewayTplMappingQuery gatewayTplMappingQuery);

    List<GatewayTplMappingDO> selectByQuery(GatewayTplMappingQuery gatewayTplMappingQuery);

    void delete(@Param("id") Integer id, @Param("operator") String operator);

    GatewayTplMappingDO selectByTplIdAndContent(@Param("tplId") Integer tplId, @Param("tplContent") String tplContent);

    List<GatewayTplMappingDO> selectAll();

    GatewayTplMappingDO selectById(Integer id);
}