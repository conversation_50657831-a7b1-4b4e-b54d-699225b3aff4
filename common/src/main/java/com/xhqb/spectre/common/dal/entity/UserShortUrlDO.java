package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserShortUrlDO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 短链模版编码
     */
    private String tplCode;

    /**
     * 长链
     */
    private String longUrl;

    /**
     * 短链
     */
    private String shortUrl;

    /**
     * 过期时间
     */
    private String expiredDate;

    /**
     * 短链hash
     */
    private String shortCode;

    /**
     * 批次id
     */
    private String batchId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * cid
     */
    private String cid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 删除标志
     */
    private Integer isDelete;

    /**
     * 状态
     */
    private Integer status;
}
