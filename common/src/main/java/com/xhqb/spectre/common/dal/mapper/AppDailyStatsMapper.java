package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.AppDailyStatsDO;
import com.xhqb.spectre.common.dal.query.AppDailyStatsQuery;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_app_daily_stats(账户调用每日统计)】的数据库操作Mapper
* @createDate 2025-04-02 13:48:41
* @Entity generator.entity.AppDailyStats
*/
public interface AppDailyStatsMapper {

    int deleteByPrimaryKey(Long id);

    int insert(AppDailyStatsDO record);

    int insertSelective(AppDailyStatsDO record);

    AppDailyStatsDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AppDailyStatsDO record);

    int updateByPrimaryKey(AppDailyStatsDO record);

    Integer countByQuery(AppDailyStatsQuery appQuery);

    List<AppDailyStatsDO> selectByQuery(AppDailyStatsQuery appQuery);
    int saveOrUpdate(AppDailyStatsDO record);
}
