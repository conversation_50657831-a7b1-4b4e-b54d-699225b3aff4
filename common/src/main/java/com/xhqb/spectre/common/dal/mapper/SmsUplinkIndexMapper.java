package com.xhqb.spectre.common.dal.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/8/19 16:12
 **/
@Mapper
public interface SmsUplinkIndexMapper {
    /**
     * 查询短信上行id
     * @return
     */
    Long selectSmsUplinkId();

    /**
     * 更新短信上行id
     * @param id
     */
    void updateUplinkLastId(@Param("id") Long id);


}
