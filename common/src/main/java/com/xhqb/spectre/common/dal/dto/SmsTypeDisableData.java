package com.xhqb.spectre.common.dal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/*
 * @Author: huangyanxiong
 * @Date: 2022/2/17 15:58
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsTypeDisableData implements Serializable {

    private static final long serialVersionUID = 2952447528141997452L;

    /**
     * 短信类型编码
     */
    private String smsTypeCode;

    /**
     * 运营商列表
     */
    private List<String> ispList;

    /**
     * 地域列表
     */
    private List<AreaData> areaList;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;
}
