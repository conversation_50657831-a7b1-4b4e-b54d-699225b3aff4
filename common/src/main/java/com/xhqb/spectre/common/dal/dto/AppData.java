package com.xhqb.spectre.common.dal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/*
 * @Author: huangyanxiong
 * @Date: 2021/9/17 14:33
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppData implements Serializable {

    private static final long serialVersionUID = 6787375745694092404L;

    /**
     * 应用编码
     */
    private String code;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 秘钥
     */
    private String skey;

    /**
     * 回调地址
     */
    private String cbUrl;

    /**
     * 短信内容API类型，0：不支持；1：支持；2：支持并且免模板检测
     */
    private Integer contentApiType;
}
