package com.xhqb.spectre.common.dal.entity;

import java.util.Date;

public class SmsDayStatisKey {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_day_statis.date
     *
     * @mbggenerated
     */
    private Date date;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_sms_day_statis.code
     *
     * @mbggenerated
     */
    private String code;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_day_statis.date
     *
     * @return the value of t_sms_day_statis.date
     *
     * @mbggenerated
     */
    public Date getDate() {
        return date;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_day_statis.date
     *
     * @param date the value for t_sms_day_statis.date
     *
     * @mbggenerated
     */
    public void setDate(Date date) {
        this.date = date;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_sms_day_statis.code
     *
     * @return the value of t_sms_day_statis.code
     *
     * @mbggenerated
     */
    public String getCode() {
        return code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_sms_day_statis.code
     *
     * @param code the value for t_sms_day_statis.code
     *
     * @mbggenerated
     */
    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }
}