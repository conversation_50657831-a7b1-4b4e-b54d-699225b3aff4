package com.xhqb.spectre.common.utils;

import java.security.SecureRandom;

public class UniqueKeyUtil {

    /**
     * base58
     */
    private static final char[] ALLOWED_CHARS = new char[]{
            '1', '2', '3', '4', '5', '6', '7', '8', '9',
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'
    };
    private static final int SIZE = ALLOWED_CHARS.length;


    private static final char[] NUM_CHARS = new char[]{
            '1', '2', '3', '4', '5', '6', '7', '8', '9', '0'
    };

    private static final int NUM_SIZE = NUM_CHARS.length;


    private static final SecureRandom random = new SecureRandom();


    private static String generateByRandom(Integer length) {
        StringBuilder keyBuilder = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int randomIndex = random.nextInt(SIZE);
            keyBuilder.append(ALLOWED_CHARS[randomIndex]);
        }
        return keyBuilder.toString();
    }


    public static String generate(Integer length) {
        return generateByRandom(length);
    }


    private static String generateNumByRandom(Integer length) {
        StringBuilder keyBuilder = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int randomIndex = random.nextInt(NUM_SIZE);
            keyBuilder.append(NUM_CHARS[randomIndex]);
        }
        return keyBuilder.toString();
    }


    public static String generateNum(Integer length) {
        return generateNumByRandom(length);
    }
}
