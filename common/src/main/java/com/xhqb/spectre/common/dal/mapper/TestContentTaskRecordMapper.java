package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.TestContentStatDO;
import com.xhqb.spectre.common.dal.entity.test.tool.TestContentTaskRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Mapper
public interface TestContentTaskRecordMapper {

    // select by id
    TestContentTaskRecordDO selectByPrimaryKey(Integer id);

    // insert
    int insertBySelective(TestContentTaskRecordDO record);

    int updateByPrimaryKeySelective(TestContentTaskRecordDO record);

    List<TestContentTaskRecordDO> selectByTaskId(String taskId);

    TestContentTaskRecordDO selectByContent(@Param("mobile") String mobile, @Param("content") String content);

    int updateAppReportStatusByTaskId(@Param("taskId") String taskId);

    List<TestContentTaskRecordDO> selectByTaskIds(@Param("taskIds") Set<String> taskIds);

    List<TestContentStatDO> statByTaskIds(@Param("taskIdList") List<String> taskIdList, @Param("tplCode") String tplCode, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
