package com.xhqb.spectre.common.validation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class QueryValidator implements ConstraintValidator<ValidQuery, String> {

    /**
     * query格式遵循URL标准，即k1=v1&k2=v2
     */
    private static final String QUERY_REGEX = "^(\\w+=\\w+(&\\w+=\\w+)*)?$";

    @Override
    public void initialize(ValidQuery constraintAnnotation) {
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.isEmpty()) {
            return true;
        }
        return value.matches(QUERY_REGEX);
    }
}
