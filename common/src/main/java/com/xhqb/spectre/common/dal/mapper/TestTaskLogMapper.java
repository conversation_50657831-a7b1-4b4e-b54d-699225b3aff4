package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.test.TestTaskLogDO;
import com.xhqb.spectre.common.dal.query.ChannelTestRecordQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface TestTaskLogMapper {
    Integer countByQuery(@Param("query") ChannelTestRecordQuery channelTestRecordQuery);

    List<TestTaskLogDO> selectByQuery(@Param("query") ChannelTestRecordQuery channelTestRecordQuery);

    List<TestTaskLogDO> selectByTaskId(String taskId);

    int updateSendStatus(TestTaskLogDO testTaskLogDO);

    List<TestTaskLogDO> selectByTaskIdList(@Param("taskIdList")List<String> taskIdList);

    int insertBySelective(TestTaskLogDO taskLogDO);

    List<TestTaskLogDO> selectByTaskIdListAndCallStatus(@Param("taskIdList")List<String> taskIdList, @Param("callStatus")int callStatus);

    void updateByTaskIdAndMobile(TestTaskLogDO testTaskLogDO);
}
