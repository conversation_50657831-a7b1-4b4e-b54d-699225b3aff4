package com.xhqb.spectre.common.dal.entity;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
public class DebtSmsReportDO {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.id
     *
     * @mbggenerated
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.report_date
     *
     * @mbggenerated
     */
    private Date reportDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.tpl_code
     *
     * @mbggenerated
     */
    private String tplCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.biz_batch_id
     *
     * @mbggenerated
     */
    private String bizBatchId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_retry.start_time
     *
     * @mbggenerated
     */
    private Integer startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.status
     *
     * @mbggenerated
     */
    private Byte status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.alert_times
     *
     * @mbggenerated
     */
    private Integer alertTimes;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.submit_amount
     *
     * @mbggenerated
     */
    private Integer submitAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.valid_amount
     *
     * @mbggenerated
     */
    private Integer validAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.order_amount
     *
     * @mbggenerated
     */
    private Integer orderAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.success_amount
     *
     * @mbggenerated
     */
    private Integer successAmount;


    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.remark
     *
     * @mbggenerated
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_debt_sms_report.is_delete
     *
     * @mbggenerated
     */
    private Byte isDelete;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.id
     *
     * @return the value of t_debt_sms_report.id
     * @mbggenerated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.id
     *
     * @param id the value for t_debt_sms_report.id
     * @mbggenerated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.report_date
     *
     * @return the value of t_debt_sms_report.report_date
     * @mbggenerated
     */
    public Date getReportDate() {
        return reportDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.report_date
     *
     * @param reportDate the value for t_debt_sms_report.report_date
     * @mbggenerated
     */
    public void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.tpl_code
     *
     * @return the value of t_debt_sms_report.tpl_code
     * @mbggenerated
     */
    public String getTplCode() {
        return tplCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.tpl_code
     *
     * @param tplCode the value for t_debt_sms_report.tpl_code
     * @mbggenerated
     */
    public void setTplCode(String tplCode) {
        this.tplCode = tplCode == null ? null : tplCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.biz_batch_id
     *
     * @return the value of t_debt_sms_report.biz_batch_id
     * @mbggenerated
     */
    public String getBizBatchId() {
        return bizBatchId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.biz_batch_id
     *
     * @param bizBatchId the value for t_debt_sms_report.biz_batch_id
     * @mbggenerated
     */
    public void setBizBatchId(String bizBatchId) {
        this.bizBatchId = bizBatchId == null ? null : bizBatchId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.start_time
     *
     * @return the value of t_debt_sms_report.start_time
     * @mbggenerated
     */
    public Integer getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.start_time
     *
     * @param startTime the value for t_debt_sms_report.start_time
     * @mbggenerated
     */
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.status
     *
     * @return the value of t_debt_sms_report.status
     * @mbggenerated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.status
     *
     * @param status the value for t_debt_sms_report.status
     * @mbggenerated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.alert_times
     *
     * @return the value of t_debt_sms_report.alert_times
     * @mbggenerated
     */
    public Integer getAlertTimes() {
        return alertTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.alert_times
     *
     * @param alertTimes the value for t_debt_sms_report.alert_times
     * @mbggenerated
     */
    public void setAlertTimes(Integer alertTimes) {
        this.alertTimes = alertTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.submit_amount
     *
     * @return the value of t_debt_sms_report.submit_amount
     * @mbggenerated
     */
    public Integer getSubmitAmount() {
        return submitAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.submit_amount
     *
     * @param submitAmount the value for t_debt_sms_report.submit_amount
     * @mbggenerated
     */
    public void setSubmitAmount(Integer submitAmount) {
        this.submitAmount = submitAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.valid_amount
     *
     * @return the value of t_debt_sms_report.valid_amount
     * @mbggenerated
     */
    public Integer getValidAmount() {
        return validAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.valid_amount
     *
     * @param validAmount the value for t_debt_sms_report.valid_amount
     * @mbggenerated
     */
    public void setValidAmount(Integer validAmount) {
        this.validAmount = validAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.order_amount
     *
     * @return the value of t_debt_sms_report.order_amount
     * @mbggenerated
     */
    public Integer getOrderAmount() {
        return orderAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.order_amount
     *
     * @param orderAmount the value for t_debt_sms_report.order_amount
     * @mbggenerated
     */
    public void setOrderAmount(Integer orderAmount) {
        this.orderAmount = orderAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.success_amount
     *
     * @return the value of t_debt_sms_report.success_amount
     * @mbggenerated
     */
    public Integer getSuccessAmount() {
        return successAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.success_amount
     *
     * @param successAmount the value for t_debt_sms_report.success_amount
     * @mbggenerated
     */
    public void setSuccessAmount(Integer successAmount) {
        this.successAmount = successAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.remark
     *
     * @return the value of t_debt_sms_report.remark
     * @mbggenerated
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.remark
     *
     * @param remark the value for t_debt_sms_report.remark
     * @mbggenerated
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.create_time
     *
     * @return the value of t_debt_sms_report.create_time
     * @mbggenerated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.create_time
     *
     * @param createTime the value for t_debt_sms_report.create_time
     * @mbggenerated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.update_time
     *
     * @return the value of t_debt_sms_report.update_time
     * @mbggenerated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.update_time
     *
     * @param updateTime the value for t_debt_sms_report.update_time
     * @mbggenerated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_debt_sms_report.is_delete
     *
     * @return the value of t_debt_sms_report.is_delete
     * @mbggenerated
     */
    public Byte getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_debt_sms_report.is_delete
     *
     * @param isDelete the value for t_debt_sms_report.is_delete
     * @mbggenerated
     */
    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }
}