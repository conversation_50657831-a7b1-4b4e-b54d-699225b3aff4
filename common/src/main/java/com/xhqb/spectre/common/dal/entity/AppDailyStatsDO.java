package com.xhqb.spectre.common.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 账户调用每日统计
 * @TableName t_app_daily_stats
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppDailyStatsDO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 统计时间
     */
    private Date statDate;

    /**
     * 状态（1: 网关, 2: app）
     */
    private Integer accountCategory;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * 模版编码
     */
    private String tplCode;

    /**
     * 模板映射表id
     */
    private Integer tplMappingId;

    /**
     * 请求数
     */
    private Integer totalRequests;

    /**
     * 错误码
     */
    private String errCode;

    /**
     * 状态(0:失败;1成功)
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}