package com.xhqb.spectre.common.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChannelGroupItemDO implements Serializable {

    private static final long serialVersionUID = 5707123945825953618L;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group_item.id
     *
     * @mbggenerated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group_item.group_id
     *
     * @mbggenerated
     */
    private Integer groupId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group_item.channel_account_id
     *
     * @mbggenerated
     */
    private Integer channelAccountId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group_item.isps
     *
     * @mbggenerated
     */
    private String isps;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group_item.area_filter_type
     *
     * @mbggenerated
     */
    private Integer areaFilterType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group_item.areas
     *
     * @mbggenerated
     */
    private String areas;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group_item.weight
     *
     * @mbggenerated
     */
    private Integer weight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group_item.remark
     *
     * @mbggenerated
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group_item.create_time
     *
     * @mbggenerated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_channel_group_item.update_time
     *
     * @mbggenerated
     */
    private Date updateTime;
}