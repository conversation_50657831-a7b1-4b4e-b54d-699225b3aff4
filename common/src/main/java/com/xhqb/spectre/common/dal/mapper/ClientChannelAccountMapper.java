package com.xhqb.spectre.common.dal.mapper;

import com.xhqb.spectre.common.dal.entity.ClientChannelAccountDO;
import com.xhqb.spectre.common.dal.query.ClientChannelAccountQuery;

import java.util.List;

public interface ClientChannelAccountMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_client_channel_account
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_client_channel_account
     *
     * @mbggenerated
     */
    int insert(ClientChannelAccountDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_client_channel_account
     *
     * @mbggenerated
     */
    int insertSelective(ClientChannelAccountDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_client_channel_account
     *
     * @mbggenerated
     */
    ClientChannelAccountDO selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_client_channel_account
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(ClientChannelAccountDO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_client_channel_account
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(ClientChannelAccountDO record);

    /**
     * 分页查询网关账号总页数
     *
     * @param clientChannelAccountQuery
     * @return
     */
    Integer countByQuery(ClientChannelAccountQuery clientChannelAccountQuery);

    /**
     * 分页查询网关账号列表数据
     *
     * @param clientChannelAccountQuery
     * @return
     */
    List<ClientChannelAccountDO> selectByQuery(ClientChannelAccountQuery clientChannelAccountQuery);
}