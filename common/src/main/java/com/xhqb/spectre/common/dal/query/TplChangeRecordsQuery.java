package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplChangeRecordsQuery implements Serializable {

    private static final long serialVersionUID = 6927472440831713630L;

    /**
     * 主键 id（操作类型为 update 编辑 点击查看）
     */
    private Integer id;

    /**
     * 签名 id
     */
    @NotNull(message = "签名id不能为空")
    private Integer signId;

    /**
     * 短信类型编码
     */
    @NotNull(message = "短信类型编码不能为空")
    private String smsTypeCode;

    /**
     * 模板编码
     */
    @NotNull(message = "模板编码不能为空")
    private String code;

    /**
     * 分页参数
     *
     * @ignore
     */
    private PageParameter pageParameter;

}
