package com.xhqb.spectre.common.dal.query;

import com.xhqb.spectre.common.dal.page.PageParameter;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import javax.validation.constraints.NotBlank;

@Data
public class ParamsQuery {

    /**
     * 变量名
     */
    private String name;

    /**
     * 变量编码
     */
    private String code;

    /**
     * 变量编码
     */
    private String[] codes;

    private String value;

    private PageParameter pageParameter;
}
