package com.xhqb.spectre.sdk.sms.send.single;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.sdk.sms.send.SendRequest;
import com.xhqb.spectre.sdk.sms.utils.SdkSmsUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 发送 通知、催收等短信 请求
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
@SuperBuilder
@AllArgsConstructor
public class SingleRequest extends SendRequest {

    /**
     * 模版Id
     * 必填
     */
    private String tplCode;
    /**
     * 手机号码 多个用,分隔 必填 ,不能够大于100个
     * 必填
     */
    private String phoneNumbers;

    /**
     * 模板参数
     * key为手机号 value为参数，多个参数使用逗号分割
     */
    private Map<String, String> paramMap;
    /**
     * 发送时间
     * 实时/定时，为空则实时发送
     */
    private String sendTime;

    /**
     *
     * 用来兼容模版内容中带${}的，如果paramMap有值就认为模版中带[]，paramList有值就认为是${}模版
     *
     * 模板参数
     * map中必须包含phone的key参数，最多支持100个手机号
     */
    private List<? extends Map<String, String>> paramList;


    public SingleRequest() {
        this.paramMap = new HashMap<>(134);
    }

    /**
     * 没有参数
     *
     * @param mobile
     */
    public void put(String mobile) {
        this.paramMap.put(mobileTrim(mobile), "");
    }

    /**
     * 放入参数
     *
     * @param mobile
     * @param paramList
     */
    public void put(String mobile, List<String> paramList) {
        if (SdkSmsUtils.isEmpty(paramList)) {
            this.paramMap.put(mobileTrim(mobile), "");
            return;
        }
        this.paramMap.put(mobileTrim(mobile), String.join(",", paramList));
    }

    /**
     * 放入参数
     *
     * @param mobileGroup
     * @param paramList
     */
    public void put(List<String> mobileGroup, List<String> paramList) {
        if (CollectionUtils.isEmpty(mobileGroup)) {
            return;
        }
        String param = String.join(",", paramList);
        mobileGroup.forEach(mobile -> paramMap.put(mobileTrim(mobile), param));
    }

    /**
     * 分组放入参数
     *
     * @param mobileGroup
     * @param paramGroup
     */
    public void putGroup(List<String> mobileGroup, List<List<String>> paramGroup) {
        if (CollectionUtils.isEmpty(mobileGroup)) {
            return;
        }

        if (CollectionUtils.isEmpty(paramGroup)) {
            put(mobileGroup, new ArrayList<>());
            return;
        }

        for (int i = 0; i < mobileGroup.size(); i++) {
            paramMap.put(mobileTrim(mobileGroup.get(i)), String.join(",", paramGroup.get(i)));
        }

    }

    /**
     * 如果字符串为空 则返回""
     *
     * @param mobile
     * @return
     */
    private static String mobileTrim(String mobile) {
        return StringUtils.isNotBlank(mobile) ? mobile : "";
    }


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
