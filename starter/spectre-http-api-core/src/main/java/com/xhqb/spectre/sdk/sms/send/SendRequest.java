package com.xhqb.spectre.sdk.sms.send;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * 发送消息的请求
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public abstract class SendRequest implements Serializable {

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 应用秘钥
     */
    private String appSecret;
    /**
     * 批次号
     * 不是必填
     */
    private Integer batchId;
    /**
     * 渠道id，如果设置了该参数就不读取模版关联的渠道信息
     * 不是必填
     */
    private Integer channelAccountId;

    /**
     * 外部流水线号 requestId
     * 不是必填
     */
    private String requestId;

    /**
     * 签名code，为空则使用小花钱包签名
     */
    private String signCode;

    /**
     * 指定 有效号码组数据类型 值为: phone 或者 cid， 默认为phone
     * <p>
     * 用于支持短信cid发送
     */
    private String dataType = "phone";

}
