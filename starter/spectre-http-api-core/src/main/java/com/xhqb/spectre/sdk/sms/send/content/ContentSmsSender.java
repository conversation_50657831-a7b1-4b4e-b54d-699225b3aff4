package com.xhqb.spectre.sdk.sms.send.content;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhqb.spectre.sdk.sms.SdkSmsProperties;
import com.xhqb.spectre.sdk.sms.contants.Apis;
import com.xhqb.spectre.sdk.sms.send.AbstractSmsSender;
import com.xhqb.spectre.sdk.sms.send.CompositeMessageResult;
import com.xhqb.spectre.sdk.sms.send.SendResult;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 基于内容发送短信 请求
 *
 * <AUTHOR>
 * @date 2022/1/10
 */
@Component
public class ContentSmsSender extends AbstractSmsSender<ContentRequest, CompositeMessageResult<ContentResponse>> {

    @Resource
    private SdkSmsProperties sdkSmsProperties;

    /**
     * 获取到接口地址
     *
     * @return
     */
    @Override
    protected String getApi() {
        return sdkSmsProperties.getSpectreApiHost() + Apis.SpectreApi.SEND_CONTENT;
    }

    /**
     * 响应解析响应消息
     *
     * @param response
     * @return
     */
    @Override
    protected SendResult<CompositeMessageResult<ContentResponse>> doParse(ResponseEntity<SendResult> response) {
        String s = JSON.toJSONString(response.getBody());
        return JSON.parseObject(s, new TypeReference<SendResult<CompositeMessageResult<ContentResponse>>>() {
        });
    }
}
