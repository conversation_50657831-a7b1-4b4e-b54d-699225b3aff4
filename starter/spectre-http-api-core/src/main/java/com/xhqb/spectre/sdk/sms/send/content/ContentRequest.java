package com.xhqb.spectre.sdk.sms.send.content;

import com.xhqb.spectre.sdk.sms.send.SendRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * 基于内容发送短信请求
 *
 * <AUTHOR>
 * @date 2022/1/10
 */
@Data
@SuperBuilder
@AllArgsConstructor
public class ContentRequest extends SendRequest {

    /**
     * 短信类型
     */
    private String smsType;

    /**
     * 短信模板内容
     */
    private String content;

    /**
     * 发送时间
     * 实时/定时，为空则实时发送
     */
    private String sendTime;

    /**
     * 模板参数
     * map中必须包含phone的key参数，最多支持100个手机号
     */
    private List<? extends Map<String, String>> paramList;
}
