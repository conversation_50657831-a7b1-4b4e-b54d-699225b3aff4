package com.xhqb.spectre.sdk.sms.send.market;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.sdk.sms.send.single.SingleRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/11/2
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class MarketRequest extends SingleRequest {

    /**
     * 短信类型[通过查看api实现逻辑，短信类型为市场营销时必须填写，其他类型不需要设置值]
     * <p>
     * 参考 SMSMessageManagerServiceImpl.checkTplData
     */
    private String smsCodeType = "market";

    /**
     * 复制SingleRequest的数据
     *
     * @param singleRequest
     * @return
     */
    public static MarketRequest copy(SingleRequest singleRequest) {
        if (Objects.isNull(singleRequest)) {
            throw new IllegalArgumentException("MarketRequest copy 操作singleRequest不能够为空");
        }
        if (singleRequest instanceof MarketRequest) {
            return (MarketRequest) singleRequest;
        }
        return JSON.parseObject(singleRequest.toString(), MarketRequest.class);
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
