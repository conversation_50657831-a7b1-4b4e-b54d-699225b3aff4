package com.xhqb.spectre.sdk.sms.send.query;

import com.xhqb.spectre.sdk.sms.send.SendRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 手机内容发送状态请求
 *
 * <AUTHOR>
 * @date 2022/1/12
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class PhoneStatusRequest extends SendRequest {

    /**
     * 待查询的手机号码，多个手机号码已逗号分割
     */
    private String phoneNumbers;
}
