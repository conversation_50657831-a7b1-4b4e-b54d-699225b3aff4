package com.xhqb.spectre.sdk.sms.send.zbxsurl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhqb.spectre.sdk.sms.SdkSmsProperties;
import com.xhqb.spectre.sdk.sms.contants.Apis;
import com.xhqb.spectre.sdk.sms.send.AbstractSmsSender;
import com.xhqb.spectre.sdk.sms.send.SendResult;
import com.xhqb.spectre.sdk.sms.send.surl.ShortUrlRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
public class ZbxShortUrlCreateSender extends AbstractSmsSender<ShortUrlRequest, String> {

    @Resource
    private SdkSmsProperties sdkSmsProperties;

    @Override
    protected String getApi() {
        return sdkSmsProperties.getSpectreApiHost() + Apis.SpectreApi.ZBX_SHORT_URL_CREATE;
    }

    @Override
    protected SendResult<String> doParse(ResponseEntity<SendResult> response) {
        String s = JSON.toJSONString(response.getBody());
        return JSON.parseObject(s, new TypeReference<SendResult<String>>() {
        });
    }
}
