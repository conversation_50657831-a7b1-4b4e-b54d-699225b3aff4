package com.xhqb.spectre.sdk.sms.send.verify.check;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;

/**
 * 验证码校验结果
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
@Data
public class CheckCodeResponse implements Serializable {

    /**
     * 接口请求序列号
     */
    private String requestId;

    /**
     * 验证码验证结果
     */
    private Boolean resultFlag;

    /**
     * 验证码错误code
     */
    private String resultCode;

    /**
     * 验证码错误信息， 假如result为FALSE、会返回错误信息
     */
    private String resultMsg;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
