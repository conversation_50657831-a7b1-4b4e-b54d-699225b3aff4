package com.xhqb.spectre.sdk.sms.send.query;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 查询手机状态结果
 *
 * <AUTHOR>
 * @date 2022/1/12
 */
@Data
public class PhoneResult implements Serializable {

    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 最后更新时间
     */
    private Date lastTime;
    /**
     * 手机所属区域
     */
    private String area;
    /**
     * 短信所属运营商
     */
    private String numberType;
    /**
     * 状态
     * -1 -> 未查询
     * 0 -> 空号
     * 1 -> 正常
     * 2 -> 停机
     * 3 -> 库无
     * 4 -> 沉默号
     * 5 -> 风险号
     * 99 -> 未知
     */
    private Integer status;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
