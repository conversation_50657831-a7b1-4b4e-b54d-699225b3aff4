package com.xhqb.spectre.sdk.sms.send.verify.check;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhqb.spectre.sdk.sms.SdkSmsProperties;
import com.xhqb.spectre.sdk.sms.contants.Apis;
import com.xhqb.spectre.sdk.sms.send.AbstractSmsSender;
import com.xhqb.spectre.sdk.sms.send.SendResult;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 校验验证码发送请求
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
@Component
public class CheckCodeSmsSender extends AbstractSmsSender<CheckCodeRequest, CheckCodeResponse> {

    @Resource
    private SdkSmsProperties sdkSmsProperties;

    /**
     * 获取到接口地址
     *
     * @return
     */
    @Override
    protected String getApi() {
        return sdkSmsProperties.getSpectreApiHost() + Apis.SpectreApi.CHECK_VERIFY;
    }

    /**
     * 响应解析响应消息
     *
     * @param response
     * @return
     */
    @Override
    protected SendResult<CheckCodeResponse> doParse(ResponseEntity<SendResult> response) {
        String s = JSON.toJSONString(response.getBody());
        return JSON.parseObject(s, new TypeReference<SendResult<CheckCodeResponse>>() {
        });
    }
}
