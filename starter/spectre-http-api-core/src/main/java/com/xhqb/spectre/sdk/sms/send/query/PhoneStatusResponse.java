package com.xhqb.spectre.sdk.sms.send.query;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 手机内容发送状态响应
 *
 * <AUTHOR>
 * @date 2022/1/12
 */
@Data
public class PhoneStatusResponse implements Serializable {

    /**
     * 请求ID
     */
    private String requestId;
    /**
     * 短信查询结果
     */
    private List<PhoneResult> phoneResult;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
