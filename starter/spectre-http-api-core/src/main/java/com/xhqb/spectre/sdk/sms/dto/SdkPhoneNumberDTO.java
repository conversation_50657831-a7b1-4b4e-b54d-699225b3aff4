package com.xhqb.spectre.sdk.sms.dto;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.sdk.sms.dto.support.Marked;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 短信DTO
 * 包含手机号 和 参数信息
 *
 * <AUTHOR>
 * @date 2021/11/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SdkPhoneNumberDTO implements Serializable, Marked {

    /**
     * 手机号
     */
    private String phoneNumber;
    /**
     * 参数
     */
    private List<String> paramList;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
