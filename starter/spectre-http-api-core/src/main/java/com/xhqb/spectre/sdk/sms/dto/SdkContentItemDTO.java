package com.xhqb.spectre.sdk.sms.dto;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.sdk.sms.dto.support.Marked;

import java.util.Arrays;
import java.util.HashMap;

/**
 * 短信内容
 *
 * <AUTHOR>
 * @date 2022/1/10
 */
public class SdkContentItemDTO extends HashMap<String, String> implements Marked {

    public static final String PHONE = "phone";

    /**
     * 设置手机号码
     *
     * @param mobile
     */
    public SdkContentItemDTO(String mobile) {
        this.put(PHONE, mobile);
    }

    /**
     * 设置手机号码和参数映射关系
     *
     * @param mobile
     * @param entries
     */
    public SdkContentItemDTO(String mobile, SdkContentEntry... entries) {
        this(mobile);
        this.add(entries);
    }

    /**
     * 添加短信模板参数映射值
     *
     * @param key
     * @param value
     * @return
     */
    public SdkContentItemDTO add(String key, String value) {
        this.put(key, value);
        return this;
    }

    /**
     * 批量添加短信参数映射实体内容
     *
     * @param entries
     * @return
     */
    public SdkContentItemDTO add(SdkContentEntry... entries) {
        Arrays.stream(entries).forEach(entry -> put(entry.getKey(), entry.getValue()));
        return this;
    }


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
