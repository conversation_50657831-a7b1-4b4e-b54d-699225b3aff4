package com.xhqb.spectre.sdk.sms.send;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * 消息发送结果
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SendResult<T> implements Serializable {
    /**
     * api响应成功编码
     */
    private static final int SUCCESS_CODE = 200;

    /**
     * 响应编码
     */
    private Integer code;
    /**
     * 响应消息描述
     */
    private String msg;
    /**
     * 响应数据
     */
    private T data;
    /**
     * 服务端返回的日期字符串
     */
    private String date;

    /**
     * 当前响应结果是否成功
     *
     * @return
     */
    public boolean isSuccess() {
        return Objects.equals(code, SUCCESS_CODE);
    }
}
