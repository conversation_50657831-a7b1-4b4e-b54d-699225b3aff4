package com.xhqb.spectre.sdk.sms.send.record;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息发送成功记录
 * 参考： com.xhqb.spectre.api.model.smsresp.SMSSendSuccessRecord
 *
 * <AUTHOR>
 * @date 2021/9/23
 */
@Data
public class SendSuccessRecord implements Serializable {

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 投递时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;
}
