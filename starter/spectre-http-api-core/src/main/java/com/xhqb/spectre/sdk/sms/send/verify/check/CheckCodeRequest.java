package com.xhqb.spectre.sdk.sms.send.verify.check;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.sdk.sms.send.SendRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 校验短信验证码
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CheckCodeRequest extends SendRequest {

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 验证码
     */
    private String smsVerifyCode;


    /**
     * 短信验证码识别码
     */
    private String identificationCode;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
