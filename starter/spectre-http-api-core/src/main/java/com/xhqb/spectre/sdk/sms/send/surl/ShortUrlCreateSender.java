package com.xhqb.spectre.sdk.sms.send.surl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhqb.spectre.sdk.sms.SdkSmsProperties;
import com.xhqb.spectre.sdk.sms.contants.Apis;
import com.xhqb.spectre.sdk.sms.send.AbstractSmsSender;
import com.xhqb.spectre.sdk.sms.send.SendResult;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 短链创建
 *
 * <AUTHOR>
 * @date 2023/11/1
 */
@Component
public class ShortUrlCreateSender extends AbstractSmsSender<ShortUrlRequest, String> {

    @Resource
    private SdkSmsProperties sdkSmsProperties;

    @Override
    protected String getApi() {
        return sdkSmsProperties.getSpectreApiHost() + Apis.SpectreApi.SHORT_URL_CREATE;
    }

    @Override
    protected SendResult<String> doParse(ResponseEntity<SendResult> response) {
        String s = JSON.toJSONString(response.getBody());
        return JSON.parseObject(s, new TypeReference<SendResult<String>>() {
        });
    }
}
