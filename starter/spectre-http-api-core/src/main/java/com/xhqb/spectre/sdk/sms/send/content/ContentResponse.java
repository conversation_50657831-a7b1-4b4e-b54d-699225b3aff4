package com.xhqb.spectre.sdk.sms.send.content;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.sdk.sms.send.record.SendFailedRecord;
import com.xhqb.spectre.sdk.sms.send.record.SendSuccessRecord;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 基于内容发送短信响应
 *
 * <AUTHOR>
 * @date 2022/1/10
 */
@Data
public class ContentResponse implements Serializable {

    /**
     * 总记录数
     */
    private Integer total;
    /**
     * 成功数
     */
    private Integer success;
    /**
     * 失败数
     */
    private Integer failure;

    /**
     * 短信发送状态成功记录
     */
    private List<SendSuccessRecord> successSMSRecord;

    /**
     * 短信发送状态失败记录
     */
    private List<SendFailedRecord> failureSMSRecord;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
