package com.xhqb.spectre.sdk.sms.send.verify.gen;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.sdk.sms.send.SendRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 生成验证码请求
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class GenCodeRequest extends SendRequest {

    /**
     * 模板编号
     * 必填
     */
    private String tplCode;
    /**
     * 手机号码 只能发一个
     * 必填
     */
    private String phoneNumbers;

    /**
     * 验证码长度
     * 不是必填 在 4-6位之间 默认为4位
     */
    private Integer codeLen;
    /**
     * 自定义验证码，如果设置了该参数，则codeLen参数无效
     * 不是必填
     */
    private String smsVerifyCode;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
