package com.xhqb.spectre.sdk.sms.dto;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.sdk.sms.dto.support.Marked;
import com.xhqb.spectre.sdk.sms.send.SendRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

/**
 * 发送短信请求
 *
 * <AUTHOR>
 * @date 2021/11/3
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SdkSingleRequestDTO extends SendRequest {

    /**
     * 模版Id
     * 必填
     */
    private String tplCode;

    /**
     * 手机号码和参数
     */
    private List<SdkPhoneNumberDTO> sdkPhoneNumberList;

    /**
     * 发送时间
     * 实时/定时，为空则实时发送
     */
    private String sendTime;

    /**
     * 短信内容列表
     * <p>
     * 短信模板使用名称做占位符列表
     */
    private List<SdkContentItemDTO> contentItemList;

    /**
     * 添加手机号码信息
     *
     * @param marked
     * @return
     */
    public SdkSingleRequestDTO add(Marked marked) {
        return add(marked, 0);
    }

    /**
     * 添加手机号码信息
     * <p>
     * 指定初始化List大小
     *
     * @param marked
     * @param phoneNumSize 本次请求总共要添加的手机号码数量
     * @return
     */
    public SdkSingleRequestDTO add(Marked marked, int phoneNumSize) {
        if (marked instanceof SdkPhoneNumberDTO) {
            // * 占位符模板处理
            this.processAsteriskPlaceholder(marked, phoneNumSize);
        } else if (marked instanceof SdkContentItemDTO) {
            // 名称 占位符模板处理
            this.processNamePlaceholder(marked, phoneNumSize);
        } else {
            throw new UnsupportedOperationException("暂未支持该操作类型");
        }
        return this;
    }

    /**
     * 处理星号占位符
     *
     * @param marked
     * @param phoneNumSize
     */
    private void processAsteriskPlaceholder(Marked marked, int phoneNumSize) {
        if (this.sdkPhoneNumberList == null) {
            this.sdkPhoneNumberList = new ArrayList<>(phoneNumSize >= 0 ? phoneNumSize : 0);
        }
        SdkPhoneNumberDTO sdkPhoneNumber = (SdkPhoneNumberDTO) marked;
        this.sdkPhoneNumberList.add(sdkPhoneNumber);
    }

    /**
     * 处理名称占位符
     *
     * @param marked
     * @param phoneNumSize
     */
    private void processNamePlaceholder(Marked marked, int phoneNumSize) {
        if (this.contentItemList == null) {
            this.contentItemList = new ArrayList<>(phoneNumSize >= 0 ? phoneNumSize : 0);
        }
        SdkContentItemDTO sdkContentItem = (SdkContentItemDTO) marked;
        this.contentItemList.add(sdkContentItem);
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
