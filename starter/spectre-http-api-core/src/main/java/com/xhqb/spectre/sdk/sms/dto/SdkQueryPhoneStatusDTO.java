package com.xhqb.spectre.sdk.sms.dto;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.sdk.sms.send.SendRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询手机号码状态
 *
 * <AUTHOR>
 * @date 2022/1/12
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SdkQueryPhoneStatusDTO extends SendRequest {

    /**
     * 手机号码
     */
    private List<String> mobileList;

    /**
     * 添加手机号码
     *
     * @param mobiles
     * @return
     */
    public SdkQueryPhoneStatusDTO add(String... mobiles) {
        List<String> collect = Arrays.stream(mobiles).collect(Collectors.toList());
        this.getMobileList().addAll(collect);
        return this;
    }

    public List<String> getMobileList() {
        if (this.mobileList == null) {
            this.mobileList = new ArrayList<>(100);
        }
        return mobileList;
    }

    public void setMobileList(List<String> mobileList) {
        this.mobileList = mobileList;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
