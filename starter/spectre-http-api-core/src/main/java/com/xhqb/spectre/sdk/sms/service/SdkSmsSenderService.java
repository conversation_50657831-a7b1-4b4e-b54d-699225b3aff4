package com.xhqb.spectre.sdk.sms.service;

import com.xhqb.spectre.sdk.sms.dto.*;
import com.xhqb.spectre.sdk.sms.send.CompositeMessageResult;
import com.xhqb.spectre.sdk.sms.send.SendResult;
import com.xhqb.spectre.sdk.sms.send.content.ContentResponse;
import com.xhqb.spectre.sdk.sms.send.market.MarketResponse;
import com.xhqb.spectre.sdk.sms.send.query.PhoneStatusResponse;
import com.xhqb.spectre.sdk.sms.send.single.SingleResponse;
import com.xhqb.spectre.sdk.sms.send.verify.check.CheckCodeRequest;
import com.xhqb.spectre.sdk.sms.send.verify.check.CheckCodeResponse;
import com.xhqb.spectre.sdk.sms.send.verify.gen.GenCodeRequest;
import com.xhqb.spectre.sdk.sms.send.verify.gen.GenCodeResponse;

/**
 * spectre 短信发送服务
 *
 * <AUTHOR>
 * @date 2021/11/3
 */
public interface SdkSmsSenderService {

    /**
     * 发送验证码
     *
     * @param genCodeRequest
     * @return
     */
    SendResult<GenCodeResponse> sendVerify(GenCodeRequest genCodeRequest);

    /**
     * 校验验证码
     *
     * @param checkCodeRequest
     * @return
     */
    SendResult<CheckCodeResponse> checkVerify(CheckCodeRequest checkCodeRequest);

    /**
     * 发送 通知短信
     *
     * @param sdkSingleRequest
     * @return
     */
    SendResult<CompositeMessageResult<SingleResponse>> sendNotify(SdkSingleRequestDTO sdkSingleRequest);

    /**
     * 发送 催收短信
     *
     * @param sdkSingleRequest
     * @return
     */
    SendResult<CompositeMessageResult<SingleResponse>> sendCollector(SdkSingleRequestDTO sdkSingleRequest);

    /**
     * 发送 营销短信
     *
     * @param sdkSingleRequest
     * @return
     */
    SendResult<CompositeMessageResult<MarketResponse>> sendMarket(SdkSingleRequestDTO sdkSingleRequest);

    /**
     * 根据内容发送短信
     *
     * @param sdkContentRequest
     * @return
     */
    SendResult<CompositeMessageResult<ContentResponse>> sendContent(SdkContentRequestDTO sdkContentRequest);

    /**
     * 查询手机号码状态
     *
     * @param sdkQueryPhoneStatus
     * @return
     */
    SendResult<PhoneStatusResponse> queryPhoneStatus(SdkQueryPhoneStatusDTO sdkQueryPhoneStatus);

    /**
     * 短链创建
     *
     * @param shortUrlDTO
     * @return
     */
    SendResult<String> shortUrlCreate(ShortUrlDTO shortUrlDTO);

    /**
     * 短链创建
     *
     * @param zbxShortUrlDTO
     * @return
     */
    SendResult<String> zbxShortUrlCreate(ZbxShortUrlDTO zbxShortUrlDTO);

}
