package com.xhqb.spectre.sdk.sms.send.verify.gen;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 生成验证码响应结果
 *
 * <pre>
 * {
 * "code": 200,
 * "msg": "success",
 * "date": "2021-11-02 16:27:01",
 * "data": {
 * "identificationCode": "20211102000000018007",
 * "requestId": "1102162701010673800154",
 * "tplCode": "KF_YZM_200076",
 * "sendCode": "200",
 * "sendMsg": "success"
 * }
 * </pre>
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GenCodeResponse implements Serializable {

    /**
     * 验证码识别码
     */
    private String identificationCode;

    /**
     * 返回短信发送请求的请求Id
     */
    private String requestId;
    /**
     * 模板编号
     */
    private String tplCode;
    /**
     * 发送状态码
     */
    private String sendCode;
    /**
     * 发送描述信息
     */
    private String sendMsg;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
