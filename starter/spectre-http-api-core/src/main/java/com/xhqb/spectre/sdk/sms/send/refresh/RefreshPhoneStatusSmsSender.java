package com.xhqb.spectre.sdk.sms.send.refresh;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhqb.spectre.sdk.sms.SdkSmsProperties;
import com.xhqb.spectre.sdk.sms.contants.Apis;
import com.xhqb.spectre.sdk.sms.send.AbstractSmsSender;
import com.xhqb.spectre.sdk.sms.send.SendResult;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 刷新手机号码状态
 */
@Component
public class RefreshPhoneStatusSmsSender extends AbstractSmsSender<RefreshPhoneStatusRequest, RefreshPhoneStatusResponse> {

    @Resource
    private SdkSmsProperties sdkSmsProperties;

    /**
     * 获取到接口地址
     *
     * @return
     */
    @Override
    protected String getApi() {
        return sdkSmsProperties.getSpectreApiHost() + Apis.SpectreApi.REFRESH_PHONE_STATUS;
    }

    /**
     * 响应解析响应消息
     *
     * @param response
     * @return
     */
    @Override
    protected SendResult<RefreshPhoneStatusResponse> doParse(ResponseEntity<SendResult> response) {
        String s = JSON.toJSONString(response.getBody());
        return JSON.parseObject(s, new TypeReference<SendResult<RefreshPhoneStatusResponse>>() {
        });
    }
}
