package com.xhqb.spectre.sdk.sms.dto;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.sdk.sms.contants.SmsType;
import com.xhqb.spectre.sdk.sms.send.SendRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

/**
 * 基于内容发送短信请求
 *
 * <AUTHOR>
 * @date 2022/1/10
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SdkContentRequestDTO extends SendRequest {

    /**
     * 短信模板内容
     */
    private String content;

    /**
     * 短信类型
     */
    private SmsType smsType;

    /**
     * 发送时间
     * 实时/定时，为空则实时发送
     */
    private String sendTime;

    /**
     * 短信内容列表
     */
    private List<SdkContentItemDTO> contentItemList;

    /**
     * 添加手机号码信息
     *
     * @param sdkContentItem
     * @return
     */
    public SdkContentRequestDTO add(SdkContentItemDTO sdkContentItem) {
        return add(sdkContentItem, 0);
    }

    /**
     * 添加手机短信内容
     * <p>
     * 指定初始化List大小
     *
     * @param sdkContentItem
     * @param phoneNumSize   本次请求总共要添加的手机号码数量
     * @return
     */
    public SdkContentRequestDTO add(SdkContentItemDTO sdkContentItem, int phoneNumSize) {
        if (this.contentItemList == null) {
            this.contentItemList = new ArrayList<>(phoneNumSize >= 0 ? phoneNumSize : 0);
        }
        this.contentItemList.add(sdkContentItem);
        return this;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
