package com.xhqb.spectre.sdk.sms.dto;

import com.alibaba.fastjson.JSON;

import java.io.Serializable;

/**
 * sdk内容参数实体信息
 *
 * <AUTHOR>
 * @date 2022/1/12
 */
public class SdkContentEntry implements Serializable {

    /**
     * 短信占位符定义的名称
     */
    private String key;
    /**
     * 短信占位符预设的值
     */
    private String value;

    public SdkContentEntry(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
