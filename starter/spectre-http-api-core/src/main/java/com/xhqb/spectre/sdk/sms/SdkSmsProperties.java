package com.xhqb.spectre.sdk.sms;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * sdk sms 配置信息
 * prefix = "spectre.sdk.sms"
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
@Component
@Data
public class SdkSmsProperties {

    /**
     * spectre api 主机地址
     */
    @Value("${spectre.sdk.sms.spectreApiHost:http://spectre-api/spectre-api}")
    private String spectreApiHost;

    /**
     * 是否放开sdk msg 日志打印
     */
    @Value("${spectre.sdk.sms.debug:}")
    private Boolean debug;

    /**
     * appCode 若配置 请求参数则可以不传入appCode值
     */
    @Value("${spectre.sdk.sms.appCode:}")
    private String appCode;

    /**
     * appSecret 若配置 请求参数则可以不传入appSecret值
     */
    @Value("${spectre.sdk.sms.appSecret:}")
    private String appSecret;


    /**
     * 是否处于debug模式
     *
     * @return
     */
    public boolean isDebug() {
        return Objects.equals(true, debug);
    }
}
