package com.xhqb.spectre.sdk.sms.send;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.sdk.sms.SdkSmsProperties;
import com.xhqb.spectre.sdk.sms.send.single.SingleRequest;
import com.xhqb.spectre.sdk.sms.send.verify.gen.GenCodeRequest;
import com.xhqb.spectre.sdk.sms.utils.SdkSmsBooster;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/11/2
 */
public abstract class AbstractSmsSender<P extends SendRequest, R> implements SmsSender {

    private Logger logger = LoggerFactory.getLogger(getClass());

    private static final String HEADER_REQUEST_ID = "requestId";
    /**
     * 小信条后缀
     */
    private static final String XXT_SUFFIX = "_xxt";
    /**
     * 小信条签名
     */
    private static final String XXT_SIGN = "XXT";
    /**
     * 默认的签名
     */
    private static final String DEFAULT_SIGN = "BGALL";

    /**
     * 请求头
     */
    protected String CONTENT_TYPE = "Content-Type";
    /**
     * JSON请求头
     */
    protected String JSON_TYPE = "application/json;charset=UTF-8";

    @Resource
    private RestTemplate sdkSmsRestTemplate;
    @Resource
    private SdkSmsProperties sdkSmsProperties;

    /**
     * 短信发送
     *
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    public SendResult<R> send(SendRequest request) throws Exception {
        // 尝试使用venus配置 填充appCode和appSecret
        this.populateIfNecessary(request);
        P context = (P) request;

        HttpHeaders headers = new HttpHeaders();
        // 填充签名请求头
        SdkSmsBooster.boost(request.getAppCode(), request.getAppSecret(), headers);
        String requestId = request.getRequestId();
        if (StringUtils.isNotBlank(requestId)) {
            // 若设置了请求ID
            // 那么则填充至请求头中
            headers.add(HEADER_REQUEST_ID, requestId);
        }
        // 设置请求头
        headers.add(CONTENT_TYPE, JSON_TYPE);
        HttpEntity<P> body = new HttpEntity<>(context, headers);

        long start = System.currentTimeMillis();
        String api = this.getApi();
        if (sdkSmsProperties.isDebug()) {
            logger.info("发送短信请求 = {}, api = {}", JSON.toJSONString(request), api);
        }

        SendResult<R> result;
        ResponseEntity<SendResult> response = sdkSmsRestTemplate.exchange(this.getApi(), HttpMethod.POST, body, SendResult.class);
        if (!Objects.equals(response.getStatusCode(), HttpStatus.OK)) {
            // 失败
            result = response.getBody();
        } else {
            // 请求成功则进行内容解析
            result = doParse(response);
        }

        if (sdkSmsProperties.isDebug()) {
            logger.info("发送短信响应 = {}, 请求参数 ={}, api = {}, cost = {}", JSON.toJSONString(result), JSON.toJSONString(request), api, (System.currentTimeMillis() - start));
        }
        return result;
    }

    /**
     * 填充 appCode和 appSecret
     *
     * @param request
     */
    private void populateIfNecessary(SendRequest request) {
        String appCode = request.getAppCode();
        if (StringUtils.isBlank(appCode)) {
            request.setAppCode(sdkSmsProperties.getAppCode());
        }

        String appSecret = request.getAppSecret();
        if (StringUtils.isBlank(appSecret)) {
            request.setAppSecret(sdkSmsProperties.getAppSecret());
        }

        this.xxhAdapter(request);
    }

    /**
     * 小信条适配逻辑
     * <p>
     * 主要是根据模板编码调整小信条的签名
     *
     * @param request
     */
    private void xxhAdapter(SendRequest request) {
        boolean isSingleRequest = (request instanceof SingleRequest);
        String signCode = request.getSignCode();
        if (isSingleRequest) {
            SingleRequest singleRequest = (SingleRequest) request;
            String toUseCode = this.getAdapterSignCode(singleRequest.getTplCode(), signCode);
            singleRequest.setSignCode(toUseCode);
            return;
        }

        // 验证码
        boolean isGenCodeRequest = (request instanceof GenCodeRequest);
        if (isGenCodeRequest) {
            GenCodeRequest genCodeRequest = (GenCodeRequest) request;
            String toUseCode = this.getAdapterSignCode(genCodeRequest.getTplCode(), signCode);
            genCodeRequest.setSignCode(toUseCode);
        }
    }

    private String getAdapterSignCode(String tplCode, String signCode) {
        if (!StringUtils.endsWith(tplCode, XXT_SUFFIX)) {
            // 不是以_xxt结尾的模板编码则不进行适配操作
            return signCode;
        }

        // 以_xxt结尾的模板编码 那么则需要判断签名是否为默认的BGALL
        // 若为默认的BGALL则替换为XXT签名，否则不做任何操作
        if (StringUtils.equals(signCode, DEFAULT_SIGN) || StringUtils.isBlank(signCode)) {
            return XXT_SIGN;
        }

        // 返回原先设置的签名名称
        return signCode;
    }

    /**
     * 获取到接口地址
     *
     * @return
     */
    protected abstract String getApi();

    /**
     * 响应解析响应消息
     *
     * @param response
     * @return
     */
    protected abstract SendResult<R> doParse(ResponseEntity<SendResult> response);

}
