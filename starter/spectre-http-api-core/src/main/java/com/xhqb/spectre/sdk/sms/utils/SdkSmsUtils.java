package com.xhqb.spectre.sdk.sms.utils;

import com.xhqb.spectre.sdk.sms.contants.SmsType;
import com.xhqb.spectre.sdk.sms.dto.*;
import com.xhqb.spectre.sdk.sms.send.content.ContentRequest;
import com.xhqb.spectre.sdk.sms.send.market.MarketRequest;
import com.xhqb.spectre.sdk.sms.send.query.PhoneStatusRequest;
import com.xhqb.spectre.sdk.sms.send.single.SingleRequest;
import com.xhqb.spectre.sdk.sms.send.surl.ShortUrlRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * sdk sms 工具类
 *
 * <AUTHOR>
 * @date 2021/11/3
 */
public class SdkSmsUtils {

    /**
     * 判断容器类是否为空
     *
     * @param collection
     * @param <T>
     * @return
     */
    public static <T> boolean isEmpty(Collection<T> collection) {
        return Objects.isNull(collection) || collection.isEmpty();
    }

    /**
     * 转换成spectre-api接口定义的格式
     *
     * @param request
     * @return
     */
    public static SingleRequest toSingleRequest(SdkSingleRequestDTO request) {
        SingleRequest singleRequest = SingleRequest.builder()
                // 应用编码
                .appCode(request.getAppCode())
                // 应用秘钥
                .appSecret(request.getAppSecret())
                // 批次号
                .batchId(request.getBatchId())
                // 渠道id，如果设置了该参数就不读取模版关联的渠道信息
                .channelAccountId(request.getChannelAccountId())
                // 外部流水线号 requestId
                .requestId(request.getRequestId())
                // 模板编号
                .tplCode(request.getTplCode())
                // 签名编码
                .signCode(request.getSignCode())
                // 发送时间
                .sendTime(request.getSendTime())
                // 指定 有效号码组数据类型 值为: phone 或者 cid， 默认为phone
                // 用于支持短信cid发送
                .dataType(request.getDataType())
                .build();
        doSpectreApiTransform(request, singleRequest);
        return singleRequest;
    }

    /**
     * 转换成营销请求对象
     *
     * @param request
     * @return
     */
    public static MarketRequest toMarketRequest(SdkSingleRequestDTO request) {
        MarketRequest marketRequest = MarketRequest.builder()
                // 应用编码
                .appCode(request.getAppCode())
                // 应用秘钥
                .appSecret(request.getAppSecret())
                // 批次号
                .batchId(request.getBatchId())
                // 渠道id，如果设置了该参数就不读取模版关联的渠道信息
                .channelAccountId(request.getChannelAccountId())
                // 外部流水线号 requestId
                .requestId(request.getRequestId())
                // 模板编号
                .tplCode(request.getTplCode())
                // 签名编码
                .signCode(request.getSignCode())
                // 发送时间
                .sendTime(request.getSendTime())
                // 指定 有效号码组数据类型 值为: phone 或者 cid， 默认为phone
                // 用于支持短信cid发送
                .dataType(request.getDataType())
                .build();
        doSpectreApiTransform(request, marketRequest);
        return marketRequest;
    }

    /**
     * 将请求参数转换成spectre-api定义的格式
     *
     * @param request
     * @param singleRequest
     */
    private static void doSpectreApiTransform(SdkSingleRequestDTO request, SingleRequest singleRequest) {

        List<SdkContentItemDTO> contentItemList = request.getContentItemList();
        if (!isEmpty(contentItemList)) {
            // 如果名称占位符内容存在 那么则设置名称占位符内容
            singleRequest.setParamList(contentItemList);
            StringBuilder phones = new StringBuilder();
            contentItemList.stream()
                    .filter(s -> StringUtils.isNotBlank(s.get(SdkContentItemDTO.PHONE)))
                    .forEach(s -> phones.append(s.get(SdkContentItemDTO.PHONE)).append(","));
            if (phones.length() > 0) {
                phones.deleteCharAt(phones.length() - 1);
            }
            // 提前设置手机号码列表
            singleRequest.setPhoneNumbers(phones.toString());
        }

        List<SdkPhoneNumberDTO> sdkPhoneNumberList = request.getSdkPhoneNumberList();
        if (isEmpty(sdkPhoneNumberList)) {
            return;
        }

        // 转换成spectre-api 定义的手机数据格式
        StringBuilder phoneNumberList = new StringBuilder();
        String phoneNumber;
        int size = sdkPhoneNumberList.size();
        // 设置请求参数映射容器
        singleRequest.setParamMap(new HashMap<>((int) (size / 0.75)));
        for (int i = 0; i < size; i++) {
            SdkPhoneNumberDTO entry = sdkPhoneNumberList.get(i);
            if (Objects.isNull(entry)) {
                continue;
            }

            phoneNumber = entry.getPhoneNumber();
            if (StringUtils.isBlank(phoneNumber)) {
                continue;
            }
            phoneNumberList.append(phoneNumber).append(",");
            singleRequest.put(phoneNumber, entry.getParamList());
        }

        if (phoneNumberList.length() > 0) {
            phoneNumberList.deleteCharAt(phoneNumberList.length() - 1);
        }
        singleRequest.setPhoneNumbers(phoneNumberList.toString());
    }


    /**
     * 转换成spectre-api接口定义的内容短信发送请求
     *
     * @param request
     * @return
     */
    public static ContentRequest toContentRequest(SdkContentRequestDTO request) {
        ContentRequest contentRequest = ContentRequest.builder()
                // 应用编码
                .appCode(request.getAppCode())
                // 应用秘钥
                .appSecret(request.getAppSecret())
                // 批次号
                .batchId(request.getBatchId())
                // 渠道id，如果设置了该参数就不读取模版关联的渠道信息
                .channelAccountId(request.getChannelAccountId())
                // 外部流水线号 requestId
                .requestId(request.getRequestId())
                // 签名编码
                .signCode(request.getSignCode())
                // 发送时间
                .sendTime(request.getSendTime())
                // 短信模板内容
                .content(request.getContent())
                // 指定 有效号码组数据类型 值为: phone 或者 cid， 默认为phone
                // 用于支持短信cid发送
                .dataType(request.getDataType())
                .build();

        SmsType smsType = request.getSmsType();
        if (Objects.isNull(smsType)) {
            throw new IllegalArgumentException("请设置短信类型");
        }

        // 设置短信类型
        contentRequest.setSmsType(smsType.name().toLowerCase());

        List<SdkContentItemDTO> contentItemList = request.getContentItemList();
        if (Objects.isNull(contentItemList) || contentItemList.isEmpty()) {
            throw new IllegalArgumentException("待发送短信内容不能够为空");
        }
        contentRequest.setParamList(contentItemList);
        return contentRequest;
    }


    /**
     * 转换成spectre-api接口定义查询手机状态请求内容
     *
     * @param request
     * @return
     */
    public static PhoneStatusRequest toPhoneStatusRequest(SdkQueryPhoneStatusDTO request) {
        PhoneStatusRequest phoneStatusRequest = PhoneStatusRequest.builder()
                // 应用编码
                .appCode(request.getAppCode())
                // 应用秘钥
                .appSecret(request.getAppSecret())
                // 批次号
                .batchId(request.getBatchId())
                // 渠道id，如果设置了该参数就不读取模版关联的渠道信息
                .channelAccountId(request.getChannelAccountId())
                // 外部流水线号 requestId
                .requestId(request.getRequestId())
                // 签名编码
                .signCode(request.getSignCode())
                .build();

        String requestId = phoneStatusRequest.getRequestId();
        if (StringUtils.isBlank(requestId)) {
            // 如果没有传入requestId 那么sdk则生成一个requestId
            String nonce = SdkSmsBooster.getNonce();
            phoneStatusRequest.setRequestId(nonce);
        }

        List<String> mobileList = request.getMobileList();
        if (CollectionUtils.isEmpty(mobileList)) {
            throw new IllegalArgumentException("手机号码不能够为空");
        }

        phoneStatusRequest.setPhoneNumbers(String.join(",", mobileList));
        return phoneStatusRequest;
    }


    /**
     * 转换成spectre-api接口定义创建短链对象
     *
     * @param request
     * @return
     */
    public static ShortUrlRequest toShortUrlRequest(ShortUrlDTO request) {
        ShortUrlRequest shortUrlRequest = ShortUrlRequest.builder()
                // 应用编码
                .appCode(request.getAppCode())
                // 应用秘钥
                .appSecret(request.getAppSecret())
                // 签名编码
                .signCode(request.getSignCode())
                .srcUrl(request.getSrcUrl())
                .description(request.getDescription())
                .validPeriod(request.getValidPeriod())
                .build();
        return shortUrlRequest;
    }
}
