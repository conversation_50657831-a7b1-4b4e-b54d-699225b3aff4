package com.xhqb.spectre.sdk.sms.contants;

/**
 * api定义
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
public interface Apis {

    /**
     * spectre api
     */
    interface SpectreApi {
        /**
         * 发送 通知、催收等短信 请求
         */
        String SEND_SMS = "/api/spectre/v3/sendSMS";
        /**
         * 发送验证码 请求
         */
        String SEND_VERIFY = "/api/spectre/v3/sendVerify";
        /**
         * 校验验证码 请求
         */
        String CHECK_VERIFY = "/api/spectre/v3/checkVerify";
        /**
         * 发送营销短信 请求
         */
        String SEND_MARKET = "/api/spectre/v3/sendMarket";
        /**
         * 基于内容发送短信 请求
         */
        String SEND_CONTENT = "/api/spectre/v3/sendContentAuto";
        /**
         * 查询手机号码状态的接口
         */
        String CHECK_PHONE_STATUS = "/api/spectre/v3/checkPhone";
        /**
         * 刷新手机号码状态的接口
         */
        String REFRESH_PHONE_STATUS = "/api/spectre/v3/refreshPhoneNumberStatuses";
        /**
         * 短链创建接口
         */
        String SHORT_URL_CREATE = "/shortUrl/createShortUrl";

        /**
         *  中博信短链创建接口
         */
        String ZBX_SHORT_URL_CREATE = "/shortUrl/zbx/shorten";
    }
}
