package com.xhqb.spectre.sdk.sms.send.market;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhqb.spectre.sdk.sms.SdkSmsProperties;
import com.xhqb.spectre.sdk.sms.contants.Apis;
import com.xhqb.spectre.sdk.sms.send.AbstractSmsSender;
import com.xhqb.spectre.sdk.sms.send.CompositeMessageResult;
import com.xhqb.spectre.sdk.sms.send.SendResult;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 市场营销短信发送请求
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
@Component
public class MarketSmsSender extends AbstractSmsSender<MarketRequest, CompositeMessageResult<MarketResponse>> {

    @Resource
    private SdkSmsProperties sdkSmsProperties;

    /**
     * 获取到接口地址
     *
     * @return
     */
    @Override
    protected String getApi() {
        return sdkSmsProperties.getSpectreApiHost() + Apis.SpectreApi.SEND_MARKET;
    }

    /**
     * 响应解析响应消息
     *
     * @param response
     * @return
     */
    @Override
    protected SendResult<CompositeMessageResult<MarketResponse>> doParse(ResponseEntity<SendResult> response) {
        String s = JSON.toJSONString(response.getBody());
        return JSON.parseObject(s, new TypeReference<SendResult<CompositeMessageResult<MarketResponse>>>() {
        });
    }
}
