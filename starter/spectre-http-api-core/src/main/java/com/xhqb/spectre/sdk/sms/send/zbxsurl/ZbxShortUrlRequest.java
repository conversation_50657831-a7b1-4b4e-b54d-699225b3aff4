package com.xhqb.spectre.sdk.sms.send.zbxsurl;

import com.xhqb.spectre.sdk.sms.send.SendRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class ZbxShortUrlRequest extends SendRequest {

    /**
     * 源URL
     */
    private String srcUrl;

    /**
     * 有效期。1：90天；2：180天；3：365天；4：永久有效
     */
    private Integer validPeriod;
}
