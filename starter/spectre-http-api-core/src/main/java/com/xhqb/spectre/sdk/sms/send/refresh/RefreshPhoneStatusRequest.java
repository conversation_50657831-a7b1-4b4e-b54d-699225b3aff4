package com.xhqb.spectre.sdk.sms.send.refresh;

import com.xhqb.spectre.sdk.sms.send.SendRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 刷新手机号码状态请求
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class RefreshPhoneStatusRequest extends SendRequest {

    /**
     * 待查询的手机号码，多个手机号码已逗号分割
     */
    private String phoneNumbers;
}
