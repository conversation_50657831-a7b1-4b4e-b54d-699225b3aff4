package com.xhqb.spectre.sdk.sms.service.impl;

import com.xhqb.spectre.sdk.sms.dto.*;
import com.xhqb.spectre.sdk.sms.ex.SdkSmsException;
import com.xhqb.spectre.sdk.sms.send.CompositeMessageResult;
import com.xhqb.spectre.sdk.sms.send.SendResult;
import com.xhqb.spectre.sdk.sms.send.content.ContentRequest;
import com.xhqb.spectre.sdk.sms.send.content.ContentResponse;
import com.xhqb.spectre.sdk.sms.send.content.ContentSmsSender;
import com.xhqb.spectre.sdk.sms.send.market.MarketRequest;
import com.xhqb.spectre.sdk.sms.send.market.MarketResponse;
import com.xhqb.spectre.sdk.sms.send.market.MarketSmsSender;
import com.xhqb.spectre.sdk.sms.send.query.PhoneStatusRequest;
import com.xhqb.spectre.sdk.sms.send.query.PhoneStatusResponse;
import com.xhqb.spectre.sdk.sms.send.query.PhoneStatusSmsSender;
import com.xhqb.spectre.sdk.sms.send.single.SingleRequest;
import com.xhqb.spectre.sdk.sms.send.single.SingleResponse;
import com.xhqb.spectre.sdk.sms.send.single.SingleSmsSender;
import com.xhqb.spectre.sdk.sms.send.surl.ShortUrlCreateSender;
import com.xhqb.spectre.sdk.sms.send.surl.ShortUrlRequest;
import com.xhqb.spectre.sdk.sms.send.verify.check.CheckCodeRequest;
import com.xhqb.spectre.sdk.sms.send.verify.check.CheckCodeResponse;
import com.xhqb.spectre.sdk.sms.send.verify.check.CheckCodeSmsSender;
import com.xhqb.spectre.sdk.sms.send.verify.gen.GenCodeRequest;
import com.xhqb.spectre.sdk.sms.send.verify.gen.GenCodeResponse;
import com.xhqb.spectre.sdk.sms.send.verify.gen.GenCodeSmsSender;
import com.xhqb.spectre.sdk.sms.send.zbxsurl.ZbxShortUrlCreateSender;
import com.xhqb.spectre.sdk.sms.send.zbxsurl.ZbxShortUrlRequest;
import com.xhqb.spectre.sdk.sms.service.SdkSmsSenderService;
import com.xhqb.spectre.sdk.sms.utils.SdkSmsUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * spectre 短信发送服务
 *
 * <AUTHOR>
 * @date 2021/11/3
 */
@Component
public class SdkSmsSenderServiceImpl implements SdkSmsSenderService {

    @Resource
    private GenCodeSmsSender genCodeSmsSender;
    @Resource
    private CheckCodeSmsSender checkCodeSmsSender;
    @Resource
    private SingleSmsSender singleSmsSender;
    @Resource
    private MarketSmsSender marketSmsSender;
    @Resource
    private ContentSmsSender contentSmsSender;
    @Resource
    private PhoneStatusSmsSender phoneStatusSmsSender;
    @Resource
    private ShortUrlCreateSender shortUrlCreateSender;

    @Resource
    private ZbxShortUrlCreateSender zbxShortUrlCreateSender;


    /**
     * 发送验证码
     *
     * @param genCodeRequest
     * @return
     */
    @Override
    public SendResult<GenCodeResponse> sendVerify(GenCodeRequest genCodeRequest) {
        try {
            return genCodeSmsSender.send(genCodeRequest);
        } catch (Exception e) {
            throw new SdkSmsException(e);
        }
    }

    /**
     * 校验验证码
     *
     * @param checkCodeRequest
     * @return
     */
    @Override
    public SendResult<CheckCodeResponse> checkVerify(CheckCodeRequest checkCodeRequest) {
        try {
            return checkCodeSmsSender.send(checkCodeRequest);
        } catch (Exception e) {
            throw new SdkSmsException(e);
        }
    }

    /**
     * 发送 通知短信
     *
     * @param sdkSingleRequest
     * @return
     */
    @Override
    public SendResult<CompositeMessageResult<SingleResponse>> sendNotify(SdkSingleRequestDTO sdkSingleRequest) {
        try {
            SingleRequest singleRequest = SdkSmsUtils.toSingleRequest(sdkSingleRequest);
            return singleSmsSender.send(singleRequest);
        } catch (Exception e) {
            throw new SdkSmsException(e);
        }
    }

    /**
     * 发送 催收短信
     *
     * @param sdkSingleRequest
     * @return
     */
    @Override
    public SendResult<CompositeMessageResult<SingleResponse>> sendCollector(SdkSingleRequestDTO sdkSingleRequest) {
        return sendNotify(sdkSingleRequest);
    }

    /**
     * 发送 营销短信
     *
     * @param sdkSingleRequest
     * @return
     */
    @Override
    public SendResult<CompositeMessageResult<MarketResponse>> sendMarket(SdkSingleRequestDTO sdkSingleRequest) {
        try {
            MarketRequest marketRequest = SdkSmsUtils.toMarketRequest(sdkSingleRequest);
            return marketSmsSender.send(marketRequest);
        } catch (Exception e) {
            throw new SdkSmsException(e);
        }
    }

    /**
     * 根据内容发送短信
     *
     * @param sdkContentRequest
     * @return
     */
    @Override
    public SendResult<CompositeMessageResult<ContentResponse>> sendContent(SdkContentRequestDTO sdkContentRequest) {
        try {
            ContentRequest contentRequest = SdkSmsUtils.toContentRequest(sdkContentRequest);
            return contentSmsSender.send(contentRequest);
        } catch (Exception e) {
            throw new SdkSmsException(e);
        }
    }

    /**
     * 查询手机号码状态
     *
     * @param sdkQueryPhoneStatus
     * @return
     */
    @Override
    public SendResult<PhoneStatusResponse> queryPhoneStatus(SdkQueryPhoneStatusDTO sdkQueryPhoneStatus) {
        try {
            PhoneStatusRequest phoneStatusRequest = SdkSmsUtils.toPhoneStatusRequest(sdkQueryPhoneStatus);
            return phoneStatusSmsSender.send(phoneStatusRequest);
        } catch (Exception e) {
            throw new SdkSmsException(e);
        }
    }


    /**
     * 短链创建
     *
     * @param shortUrlDTO
     * @return
     */
    @Override
    public SendResult<String> shortUrlCreate(ShortUrlDTO shortUrlDTO) {
        try {
            ShortUrlRequest shortUrlRequest = SdkSmsUtils.toShortUrlRequest(shortUrlDTO);
            return shortUrlCreateSender.send(shortUrlRequest);
        } catch (Exception e) {
            throw new SdkSmsException(e);
        }
    }

    /**
     * 中博信短链创建
     *
     * @param zbxShortUrlDTO
     * @return
     */
    @Override
    public SendResult<String> zbxShortUrlCreate(ZbxShortUrlDTO zbxShortUrlDTO) {
        try {

            ZbxShortUrlRequest zbxShortUrlRequest = ZbxShortUrlRequest.builder()
                    // 应用编码
                    .appCode(zbxShortUrlDTO.getAppCode())
                    // 应用秘钥
                    .appSecret(zbxShortUrlDTO.getAppSecret())
                    // 签名编码
                    .signCode(zbxShortUrlDTO.getSignCode())
                    .srcUrl(zbxShortUrlDTO.getSrcUrl())
                    .validPeriod(zbxShortUrlDTO.getValidPeriod())
                    .build();
            return zbxShortUrlCreateSender.send(zbxShortUrlRequest);
        } catch (Exception e) {
            throw new SdkSmsException(e);
        }
    }
}
