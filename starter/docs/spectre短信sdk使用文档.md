### spectre短信sdk使用文档

#### 关于文档

| 版本                   | 类型 | 日期       | 修改人     | 说明                                                         |
| ---------------------- | ---- | ---------- | ---------- | ------------------------------------------------------------ |
| 1.1.0-SNAPSHOT         | 新建 | 2021-11-03 | 系统架构部 | 新建<br />添加spectre.sdk.sms.spectreApiHost配置说明         |
| 1.1.0-SNAPSHOT         | 新增 | 2021-11-04 | 系统架构部 | 1. 移除appCode和appSecret默认配置说明<br />2. 添加appCode和appSecret获取方式<br />3. 调整文档定义 appCode和appSecret为必填<br />4. 新增查看sdk api调用日志设置方法 |
| 1.1.0-SNAPSHOT         | 新增 | 2021-11-09 | 系统架构部 | 发送短信接口新增可选参数signCode [签名编码，为空则使用小<br />花钱包签名] |
| 1.1.0-SNAPSHOT         | 新增 | 2021-11-19 | 系统架构部 | 新增普通spring项目使用方式                                   |
| 1.1.0-SNAPSHOT         | 新增 | 2021-12-10 | 系统架构部 | 新增短信网关各个环境地址<br />新增自定义发送验证码说明       |
| 1.1.0-SNAPSHOT         | 新增 | 2021-12-20 | 系统架构部 | 新增短信定时发送参数                                         |
| 1.1.0-SNAPSHOT         | 新增 | 2022-01-05 | 系统架构部 | 新增批量发送手机号限制为100描述                              |
| 1.1.0-SNAPSHOT         | 新增 | 2022-01-12 | 系统架构部 | 新增基于内容发送短信和查询手机号码状态接口                   |
| 1.1.0-SNAPSHOT         | 新增 | 2022-02-18 | 系统架构部 | 新增基于${x}名称占位符使用例子说明 <br />4.3 sendNamePlaceholderNotify<br />4.4 sendNamePlaceholderCollector<br />4.5 sendNamePlaceholderMarket |
| 1.1.0-SNAPSHOT         | 新增 | 2022-03-07 | 系统架构部 | 新增 后台接口相关用法以及接口定义说明                        |
| 1.1.3.RELEASE-SNAPSHOT | 新增 | 2022-07-15 | 系统架构部 | 6.2.1新增查询记录返回content和billCount字段                  |
| 1.1.6.RELEASE-SNAPSHOT | 新增 | 2023-02-24 | 系统架构部 | 6.2.2新增订单状态查询用法以及定义说明                        |
| 1.1.8.RELEASE-SNAPSHOT | 新增 | 2023-07-12 | 系统架构部 | 7.2.2新增订单回执描述信息 (reportDesc,callCode)              |
| 1.1.9.RELEASE-SNAPSHOT | 新增 | 2023-08-17 | 系统架构部 | 7.2.1新增手机号查询条件                                      |
| 1.2.0.RELEASE-SNAPSHOT | 新增 | 2023-10-30 | 系统架构部 | 新增dataType支持cid发送短信功能                              |
| 1.2.1.RELEASE-SNAPSHOT | 新增 | 2023-11-01 | 系统架构部 | 新增8短链创建接口                                            |
| 1.2.2.RELEASE-SNAPSHOT | 新增 | 2024-07-03 | 系统架构部 | 7.2.1新增reportCode和reportDesc响应                          |

### 1 简介

spectre 短信sdk提供验证码、通知、催收、营销短信发送和检测验证码功能，同时提供了普通spring项目和springboot项目两种接入方式。



### 2 接入方式

#### 2.1 普通spring项目

- 添加spectre-http-api-core依赖

  ```
  <dependency>
      <groupId>com.xhqb.spectre</groupId>
      <artifactId>spectre-http-api-core</artifactId>
      <version>1.2.1.RELEASE-SNAPSHOT</version>
  </dependency>
  ```

- 添加短信sdk包名 com.xhqb.spectre.sdk.sms (**注意:xxx为原有项目的包名称**)

  ```xml
  <context:component-scan base-package="xxx,com.xhqb.spectre.sdk.sms"/>
  ```

#### 2.2 springboot项目

- 添加spectre-http-api-starter依赖

  ```xml
  <dependency>
      <groupId>com.xhqb.spectre</groupId>
      <artifactId>spectre-http-api-starter</artifactId>
      <version>1.2.1.RELEASE-SNAPSHOT</version>
  </dependency>
  ```

  

### 3 准备工作

#### 3.1 资源申请

联系系统架构部申请appCode和appSecret

#### 3.2 短信网关地址

| 短信网关环境 | 短信网关地址                         |
| ------------ | ------------------------------------ |
| test         | http://spectre-api.test8/spectre-api |
| uat          | http://spectre-api.prod2/spectre-api |
| prod         | http://spectre-api/spectre-api       |

#### 3.3 环境配置

短信网关默认地址为http://spectre-api/spectre-api，可以通过venus配置spectre.sdk.sms.spectreApiHost覆盖默认值。

```java
spectre.sdk.sms.spectreApiHost=http://spectre-api/spectre-api
```

### 4 短信相关用法

短信sdk主要使用SdkSmsSenderService服务类进行短信相关操作。发送短信默认使用小花钱包签名，需要使用其他短信签名时，您可以在**XxxRequest**(短信请求对象)对象中设置具体的signCode即可。

#### 4.1 发送验证码

> 如果要使用自己生成的验证码，则通过codeRequest.setSmsVerifyCode("自定义验证码")

```java
@Resource
private SdkSmsSenderService sdkSmsSenderService;

public void sendVerify() {
    GenCodeRequest codeRequest = new GenCodeRequest();
    codeRequest.setAppCode("系统架构部分配的appCode");
    codeRequest.setAppSecret("系统架构部分配的appSecret");
    codeRequest.setTplCode("模板编码");
    codeRequest.setPhoneNumbers("手机号码");
    SendResult<GenCodeResponse> result = sdkSmsSenderService.sendVerify(codeRequest);
    System.out.println(JSON.toJSONString(result));
}
```

#### 4.2 检测验证码

```java
@Resource
private SdkSmsSenderService sdkSmsSenderService;

public void checkVerify() {
    CheckCodeRequest checkCodeRequest = new CheckCodeRequest();
  	checkCodeRequest.setAppCode("系统架构部分配的appCode");
    checkCodeRequest.setAppSecret("系统架构部分配的appSecret");
    checkCodeRequest.setPhoneNumber("手机号码");
    checkCodeRequest.setSmsVerifyCode("手机获取到的验证码");
    checkCodeRequest.setRequestId("4.1中获取到的requestId");
    SendResult<CheckCodeResponse> result = sdkSmsSenderService.checkVerify(checkCodeRequest);
    System.out.println(JSON.toJSONString(result));
}
```

#### 4.3 发送通知类短信

> 通知类短信支持多手机号批量发送，批量发送时在sdkSingleRequest请求对象中添加多个SdkPhoneNumberDTO即可。若要定时发送短信需要设置sendTime参数。
>
> 短信批量发送手机号码不能够**超过**[*100*]
>
> 短信模板格式 支持 [*]占位符 和 ${x}名称占位符两种格式
>
> 1. [*]占位符使用SdkPhoneNumberDTO进行数据填充
> 2. ${x}名称占位符使用SdkContentItemDTO进行数据填充

```java
@Resource
private SdkSmsSenderService sdkSmsSenderService;

/**
 * [*]占位符使用方法
 */
public void sendAsteriskPlaceholderNotify() {
    SdkSingleRequestDTO sdkSingleRequest = new SdkSingleRequestDTO();
    sdkSingleRequest.setAppCode("系统架构部分配的appCode");
    sdkSingleRequest.setAppSecret("系统架构部分配的appSecret");
    sdkSingleRequest.setTplCode("模板编码");
  	// [*]占位符使用方法
    // 若有多个手机号码 添加多个SdkPhoneNumberDTO
    // 若没有参数，设置参数List为空即可 new SdkPhoneNumberDTO("手机号码1", null)
    sdkSingleRequest.add(new SdkPhoneNumberDTO("手机号码1", Lists.newArrayList("张三", "var1")));
    sdkSingleRequest.add(new SdkPhoneNumberDTO("手机号码2", Lists.newArrayList("李四", "var2")));
  	// 如果要定时发送短信则设置sendTime
  	// sendTime时间为unixtime毫秒数
    // 设置短信发送时间与当前时间间隔不能够超过24小时
  	// sdkSingleRequest.setSendTime("定时发送短信的unixtime");
    SendResult<CompositeMessageResult<SingleResponse>> result = sdkSmsSenderService.sendNotify(sdkSingleRequest);
    System.out.println(JSON.toJSONString(result));
}

/**
 * ${x}名称占位符使用方法
 */
public void sendNamePlaceholderNotify() {
    SdkSingleRequestDTO sdkSingleRequest = new SdkSingleRequestDTO();
    sdkSingleRequest.setAppCode("系统架构部分配的appCode");
    sdkSingleRequest.setAppSecret("系统架构部分配的appSecret");
    sdkSingleRequest.setTplCode("模板编码");
  	// [${x}名称占位符使用方法
    // 若有多个手机号码 添加多个SdkContentItemDTO
    sdkSingleRequest.add(new SdkContentItemDTO("手机号码1", new SdkContentEntry("username", "旺财1"), new SdkContentEntry("date", "2022-02-18 14:50")));
    sdkSingleRequest.add(new SdkContentItemDTO("手机号码2", new SdkContentEntry("username", "旺财2"), new SdkContentEntry("date", "2022-02-18 15:08")));
  	// 如果要定时发送短信则设置sendTime
  	// sendTime时间为unixtime毫秒数
    // 设置短信发送时间与当前时间间隔不能够超过24小时
  	// sdkSingleRequest.setSendTime("定时发送短信的unixtime");
    SendResult<CompositeMessageResult<SingleResponse>> result = sdkSmsSenderService.sendNotify(sdkSingleRequest);
    System.out.println(JSON.toJSONString(result));
}
```

#### 4.4 发送催收类短信

> 催收类短信支持多手机号批量发送，批量发送时在sdkSingleRequest请求对象中添加多个SdkPhoneNumberDTO即可。若要定时发送短信需要设置sendTime参数。
>
> 短信批量发送手机号码不能够**超过**[*100*]
>
> 短信模板格式 支持 [*]占位符 和 ${x}名称占位符两种格式
>
> 1. [*]占位符使用SdkPhoneNumberDTO进行数据填充
> 2. ${x}名称占位符使用SdkContentItemDTO进行数据填充

```java
@Resource
private SdkSmsSenderService sdkSmsSenderService;
/**
 * [*]占位符使用方法
 */
public void sendAsteriskPlaceholderCollector() {
    SdkSingleRequestDTO sdkSingleRequest = new SdkSingleRequestDTO();
    sdkSingleRequest.setAppCode("系统架构部分配的appCode");
    sdkSingleRequest.setAppSecret("系统架构部分配的appSecret");
    sdkSingleRequest.setTplCode("模板编码");
    // 若有多个手机号码 添加多个SdkPhoneNumberDTO
    // 若没有参数，设置参数List为空即可 new SdkPhoneNumberDTO("手机号码1", null)
    sdkSingleRequest.add(new SdkPhoneNumberDTO("手机号码1", Lists.newArrayList("张三", "var1")));
    sdkSingleRequest.add(new SdkPhoneNumberDTO("手机号码2", Lists.newArrayList("李四", "var2")));
  
    // 如果要定时发送短信则设置sendTime
  	// sendTime时间为unixtime毫秒数
    // 设置短信发送时间与当前时间间隔不能够超过24小时
  	// sdkSingleRequest.setSendTime("定时发送短信的unixtime");

    SendResult<CompositeMessageResult<SingleResponse>> result = sdkSmsSenderService.sendCollector(sdkSingleRequest);
    System.out.println(JSON.toJSONString(result));
}

/**
 * ${x}名称占位符使用方法
 */
public void sendNamePlaceholderCollector() {
    SdkSingleRequestDTO sdkSingleRequest = new SdkSingleRequestDTO();
    sdkSingleRequest.setAppCode("系统架构部分配的appCode");
    sdkSingleRequest.setAppSecret("系统架构部分配的appSecret");
    sdkSingleRequest.setTplCode("模板编码");
  	// [${x}名称占位符使用方法
    // 若有多个手机号码 添加多个SdkContentItemDTO
    sdkSingleRequest.add(new SdkContentItemDTO("手机号码1", new SdkContentEntry("username", "旺财1"), new SdkContentEntry("date", "2022-02-18 14:50")));
    sdkSingleRequest.add(new SdkContentItemDTO("手机号码2", new SdkContentEntry("username", "旺财2"), new SdkContentEntry("date", "2022-02-18 15:08")));
  	// 如果要定时发送短信则设置sendTime
  	// sendTime时间为unixtime毫秒数
    // 设置短信发送时间与当前时间间隔不能够超过24小时
  	// sdkSingleRequest.setSendTime("定时发送短信的unixtime");
    SendResult<CompositeMessageResult<SingleResponse>> result = sdkSmsSenderService.sendCollector(sdkSingleRequest);
    System.out.println(JSON.toJSONString(result));
}
```

#### 4.5 发送营销类短信

> 营销类短信支持多手机号批量发送，批量发送时在sdkSingleRequest请求对象中添加多个SdkPhoneNumberDTO即可。若要定时发送短信需要设置sendTime参数。
>
> 短信批量发送手机号码不能够**超过**[*100*]
>
> 短信模板格式 支持 [*]占位符 和 ${x}名称占位符两种格式
>
> 1. [*]占位符使用SdkPhoneNumberDTO进行数据填充
> 2. ${x}名称占位符使用SdkContentItemDTO进行数据填充

```java
@Resource
private SdkSmsSenderService sdkSmsSenderService;
/**
 * [*]占位符使用方法
 */
public void sendAsteriskPlaceholderMarket() {
    SdkSingleRequestDTO sdkSingleRequest = new SdkSingleRequestDTO();
    sdkSingleRequest.setAppCode("系统架构部分配的appCode");
    sdkSingleRequest.setAppSecret("系统架构部分配的appSecret");
    sdkSingleRequest.setTplCode("模板编码");
    // 若有多个手机号码 添加多个SdkPhoneNumberDTO
    // 若没有参数，设置参数List为空即可 new SdkPhoneNumberDTO("手机号码1", null)
    sdkSingleRequest.add(new SdkPhoneNumberDTO("手机号码1", Lists.newArrayList("张三", "var1")));
    sdkSingleRequest.add(new SdkPhoneNumberDTO("手机号码2", Lists.newArrayList("李四", "var2")));
  
    // 如果要定时发送短信则设置sendTime
  	// sendTime时间为unixtime毫秒数
    // 设置短信发送时间与当前时间间隔不能够超过24小时
  	// sdkSingleRequest.setSendTime("定时发送短信的unixtime");

    SendResult<CompositeMessageResult<MarketResponse>> result = sdkSmsSenderService.sendMarket(sdkSingleRequest);
    System.out.println(JSON.toJSONString(result));
}
/**
 * ${x}名称占位符使用方法
 */
public void sendNamePlaceholderMarket() {
    SdkSingleRequestDTO sdkSingleRequest = new SdkSingleRequestDTO();
    sdkSingleRequest.setAppCode("系统架构部分配的appCode");
    sdkSingleRequest.setAppSecret("系统架构部分配的appSecret");
    sdkSingleRequest.setTplCode("模板编码");
  	// [${x}名称占位符使用方法
    // 若有多个手机号码 添加多个SdkContentItemDTO
    sdkSingleRequest.add(new SdkContentItemDTO("手机号码1", new SdkContentEntry("username", "旺财1"), new SdkContentEntry("date", "2022-02-18 14:50")));
    sdkSingleRequest.add(new SdkContentItemDTO("手机号码2", new SdkContentEntry("username", "旺财2"), new SdkContentEntry("date", "2022-02-18 15:08")));
  	// 如果要定时发送短信则设置sendTime
  	// sendTime时间为unixtime毫秒数
    // 设置短信发送时间与当前时间间隔不能够超过24小时
  	// sdkSingleRequest.setSendTime("定时发送短信的unixtime");
    SendResult<CompositeMessageResult<SingleResponse>> result = sdkSmsSenderService.sendMarket(sdkSingleRequest);
    System.out.println(JSON.toJSONString(result));
}
```

#### 4.6 基于内容发送短信

> 基于内容发送短信支持手机批量发送，批量发送时在sdkContentRequest请求对象中添加多个SdkContentItemDTO即可，如果模板有多个参数，那么添加多个SdkContentEntry则可以实现多参数需求；如果要实现定时发送短信则需要设置sendTime参数。
>
> 短信批量发送手机号码不能够**超过**[*100*]
>
> 短信模板内容占位符使用**$花括号**包裹 如: 你好世界 from ${name}
>
> 
>
> **特别提醒**
>
> 基于内容发送短信模板申请时，需要管理员按照http_ 应用编码(appCode)_ 短信类型(smsType)_tpl (模板编码不能够包含空格)进行配置。
>
> 假如您的appCode为oidc，需要发送营销类短信，那么申请的短信模板编码就为 **http_oidc_market_tpl**





```java
@Resource
private SdkSmsSenderService sdkSmsSenderService;

public void sendContent() {
    SdkContentRequestDTO sdkContentRequest = new SdkContentRequestDTO();
    sdkContentRequest.setAppCode("系统架构部分配的appCode");
    sdkContentRequest.setAppSecret("系统架构部分配的appSecret");
    // 短信模板参数占位符使用双花括号包裹 如: 你好世界 from {{name}}
    sdkContentRequest.setContent("待发送的短信模板内容");
  	// 短信类型为SmsTyp枚举值
    sdkContentRequest.setSmsType(SmsType.MARKET);
    sdkContentRequest.setSignCode("签名编码");
  
  	// 若有多个手机号码 添加多个SdkContentItemDTO即可
    // 如果模板有多个参数，那么添加多个SdkContentEntry则可以实现多参数需求 
    // sdkContentRequest.add(new SdkContentItemDTO("手机号码", new SdkContentEntry("name", "旺财"),new SdkContentEntry("money","100")));
    sdkContentRequest.add(new SdkContentItemDTO("手机号码", new SdkContentEntry("name", "旺财")));
    sdkContentRequest.add(new SdkContentItemDTO("手机号码2", new SdkContentEntry("name", "张三")));
  
    // 如果要定时发送短信则设置sendTime
    // sendTime时间为unixtime毫秒数
    // 设置短信发送时间与当前时间间隔不能够超过24小时
    // sdkSingleRequest.setSendTime("定时发送短信的unixtime");
    SendResult<CompositeMessageResult<ContentResponse>> result = sdkSmsSenderService.sendContent(sdkContentRequest);
    System.out.println(JSON.toJSONString(result));
}
```



#### 4.7 查询手机号码状态

> 查询手机号码发送状态，可以设置多个手机号码一并查询。
>
> 手机号码状态批量查询号码不能够**超过**[*100*]

```java
@Resource
private SdkSmsSenderService sdkSmsSenderService;

public void queryPhoneStatus() {
    SdkQueryPhoneStatusDTO sdkQueryPhoneStatus = new SdkQueryPhoneStatusDTO();
    sdkQueryPhoneStatus.setAppCode("系统架构部分配的appCode");
    sdkQueryPhoneStatus.setAppSecret("系统架构部分配的appSecret");
  	// 该请求ID主要用于日志排查，可以不传，sdk会自动生成
    // sdkQueryPhoneStatus.setRequestId("16419795531917612284631882928");
    sdkQueryPhoneStatus.add("手机号码","手机号码2");
    SendResult<PhoneStatusResponse> result = sdkSmsSenderService.queryPhoneStatus(sdkQueryPhoneStatus);
    System.out.println(JSON.toJSONString(result));
}
```



### 5 短信相关接口定义

#### 5.1 发送验证码接口

##### 5.1.1 请求参数

| 名称          | 类型   | 是否必须 | 备注                                              |
| ------------- | ------ | -------- | ------------------------------------------------- |
| appCode       | string | M        | 系统架构部分配的appCode                           |
| appSecret     | string | M        | 系统架构部分配的appSecret                         |
| tplCode       | string | M        | 模板编号                                          |
| phoneNumbers  | string | M        | 手机号码(只支持一个手机号码)                      |
| codeLen       | int    | N        | 验证码长度(4-6位)，默认4位                        |
| smsVerifyCode | String | N        | 自定义验证码，如果设置了该参数，则codeLen参数无效 |
| signCode      | String | N        | 签名编码，为空则使用小花钱包签名                  |
| dataType      | String | N        | 发送短信的号码类型, phone 或者 cid， 默认为phone  |

##### 5.1.2 响应参数

| 名称    | 类型    | 是否必填 | 备注                                 |
| ------- | ------- | -------- | ------------------------------------ |
| code    | int     | M        | 状态码                               |
| msg     | string  | N        | 状态码描述                           |
| date    | string  | N        | 服务器处理时间                       |
| success | boolean | M        | 处理结果标记 true->成功  false->失败 |
| data    | object  | N        | 处理结果 处理失败时该值为空       |
| ├─identificationCode | string | M | 验证码识别码 |
| ├─requestId | string | M | 短信发送请求的请求Id |
| ├─sendCode | int | M | 发送状态码 |
| ├─sendMsg | string | M | 发送描述信息 |
| ├─tplCode | string | M | 模板编号 |

#### 5.2 验证验证码接口

##### 5.2.1 请求参数

| 名称          | 类型   | 是否必须 | 备注                                             |
| ------------- | ------ | -------- | ------------------------------------------------ |
| appCode       | string | M        | 系统架构部分配的appCode                          |
| appSecret     | string | M        | 系统架构部分配的appSecret                        |
| phoneNumber   | string | M        | 手机号码                                         |
| smsVerifyCode | string | M        | 验证码                                           |
| requestId     | string | M        | 请求ID (5.1返回返回的requestId)                  |
| dataType      | String | N        | 发送短信的号码类型, phone 或者 cid， 默认为phone |

##### 5.2.2 响应参数

| 名称         | 类型    | 是否必填 | 备注                                              |
| ------------ | ------- | -------- | ------------------------------------------------- |
| code         | int     | M        | 状态码                                            |
| msg          | string  | N        | 状态码描述                                        |
| date         | string  | N        | 服务器处理时间                                    |
| success      | boolean | M        | 处理结果标记 true->成功  false->失败              |
| data         | object  | N        | 处理结果 处理失败时该值为空                       |
| ├─requestId  | string  | M        | 请求ID                                            |
| ├─resultFlag | boolean | M        | 验证码验证结果 true->表示验证成功 false->验证失败 |
| ├─resultMsg  | string  | M        | 验证结果描述信息                                  |

#### 5.3 通知类短信接口

##### 5.3.1 请求参数

| 名称               | 类型   | 是否必须 | 备注                                                         |
| ------------------ | ------ | -------- | ------------------------------------------------------------ |
| appCode            | string | M        | 系统架构部分配的appCode                                      |
| appSecret          | string | M        | 系统架构部分配的appSecret                                    |
| tplCode            | string | M        | 模板编号                                                     |
| signCode           | string | N        | 签名编码，为空则使用小花钱包签名                             |
| sendTime           | string | N        | 短信定时发送时间(unixtime 单位为毫秒),为空则实时发送<br />设置短信发送时间与当前时间间隔不能够超过24小时 |
| sdkPhoneNumberList | list   | N        | 手机号码以及短信动态参数列表(批量发送时手机号码不能够超过100)模板为[*]占位符时必填 |
| ├─phoneNumber      | string | M        | 手机号码                                                     |
| ├─paramList        | list   | M        | 短信动态参数string列表                                       |
| contentItemList    | list   | N        | 手机号码以及短信动态参数列表(批量发送时手机号码不能够超过100)[contentItem为key:value键值对]模板为${x}占位符时必填 |
| ├─phone            | string | M        | 手机号码                                                     |
| ├─xx               | string | M        | 短信动态参数名称(xx为动态占位符名称,例如模板占位符为 ${name}, 那么需将xx替换为name) |
| dataType           | String | N        | 发送短信的号码类型, phone 或者 cid， 默认为phone             |

##### 5.3.2 响应参数

| 名称                    | 类型     | 是否必填 | 备注                                             |
| ----------------------- | -------- | -------- | ------------------------------------------------ |
| code                    | int      | M        | 状态码                                           |
| msg                     | string   | N        | 状态码描述                                       |
| date                    | string   | N        | 服务器处理时间                                   |
| success                 | boolean  | M        | 处理结果标记 true->成功  false->失败             |
| data                    | object   | N        | 处理结果 处理失败时该值为空                      |
| ├─requestId             | string   | M        | 请求ID                                           |
| ├─sendStatus            | string   | M        | 发送状态                                         |
| ├─smsSendResult         | object   | M        | 短信发送结果                                     |
| │   ├─ total            | int      | M        | 短信发送的总数量                                 |
| │   ├─ success          | int      | M        | 短信发送成功的数量                               |
| │   ├─ failure          | int      | M        | 短信发送失败的数量                               |
| │   ├─ successSMSRecord | list     | M        | 短信发送成功的记录信息(若没有成功记录则为空列表) |
| │   │   ├─ phoneNumber  | string   | M        | 手机号码                                         |
| │   │   ├─ sendTime     | datetime | M        | 发送时间                                         |
| │   ├─ failureSMSRecord | list     | M        | 短信发送失败的记录信息(若没有失败记录则为空列表) |
| │   │   ├─ phoneNumber  | string   | M        | 手机号码                                         |
| │   │   ├─ fileCode     | string   | M        | 失败编码                                         |
| │   │   ├─ fileMsg      | string   | M        | 失败描述信息                                     |

#### 5.4 催收类短信接口

##### 5.4.1 请求参数

同5.3.1

##### 5.4.2 响应参数

同5.3.2

#### 5.5 营销类短信接口

##### 5.5.1 请求参数

同5.3.1

##### 5.5.2 响应参数

同5.3.2



#### 5.6 基于内容发送短信

##### 5.6.1 请求参数

| 名称            | 类型   | 是否必须 | 备注                                                         |
| --------------- | ------ | -------- | ------------------------------------------------------------ |
| appCode         | string | M        | 系统架构部分配的appCode                                      |
| appSecret       | string | M        | 系统架构部分配的appSecret                                    |
| smsType         | string | M        | 短信类型(SmsType枚举, NOTIFY->通知,MARKET->营销,COLLECTOR->催收,VERIFY->验证码) |
| signCode        | string | M        | 签名编码                                                     |
| sendTime        | string | N        | 短信定时发送时间(unixtime 单位为毫秒),为空则实时发送<br />设置短信发送时间与当前时间间隔不能够超过24小时 |
| contentItemList | list   | M        | 手机号码以及短信动态参数列表(批量发送时手机号码不能够超过100)[contentItem为key:value键值对] |
| ├─phone         | string | M        | 手机号码                                                     |
| ├─xx            | string | M        | 短信动态参数名称(xx为动态占位符名称,例如模板占位符为 ${name}, 那么需将xx替换为name) |

##### 5.6.2 响应参数

| 名称                    | 类型     | 是否必填 | 备注                                             |
| ----------------------- | -------- | -------- | ------------------------------------------------ |
| code                    | int      | M        | 状态码                                           |
| msg                     | string   | N        | 状态码描述                                       |
| date                    | string   | N        | 服务器处理时间                                   |
| success                 | boolean  | M        | 处理结果标记 true->成功  false->失败             |
| data                    | object   | N        | 处理结果 处理失败时该值为空                      |
| ├─requestId             | string   | M        | 请求ID                                           |
| ├─sendStatus            | string   | M        | 发送状态                                         |
| ├─smsSendResult         | object   | M        | 短信发送结果                                     |
| │   ├─ total            | int      | M        | 短信发送的总数量                                 |
| │   ├─ success          | int      | M        | 短信发送成功的数量                               |
| │   ├─ failure          | int      | M        | 短信发送失败的数量                               |
| │   ├─ successSMSRecord | list     | M        | 短信发送成功的记录信息(若没有成功记录则为空列表) |
| │   │   ├─ phoneNumber  | string   | M        | 手机号码                                         |
| │   │   ├─ sendTime     | datetime | M        | 发送时间                                         |
| │   ├─ failureSMSRecord | list     | M        | 短信发送失败的记录信息(若没有失败记录则为空列表) |
| │   │   ├─ phoneNumber  | string   | M        | 手机号码                                         |
| │   │   ├─ fileCode     | string   | M        | 失败编码                                         |
| │   │   ├─ fileMsg      | string   | M        | 失败描述信息                                     |

#### 5.7 查询手机号码状态

##### 5.7.1 请求参数

| 名称       | 类型   | 是否必须 | 备注                                                  |
| ---------- | ------ | -------- | ----------------------------------------------------- |
| appCode    | string | M        | 系统架构部分配的appCode                               |
| appSecret  | string | M        | 系统架构部分配的appSecret                             |
| requestId  | string | O        | 该请求ID主要用于日志排查，可以不传，sdk会自动生成     |
| mobileList | list   | M        | 查询状态手机号码列表(批量查询时手机号码不能够超过100) |
| ├─         | string | M        | 手机号码                                              |

##### 5.7.2 响应参数

| 名称              | 类型    | 是否必填 | 备注                                                         |
| ----------------- | ------- | -------- | ------------------------------------------------------------ |
| code              | int     | M        | 状态码                                                       |
| msg               | string  | N        | 状态码描述                                                   |
| date              | string  | N        | 服务器处理时间                                               |
| success           | boolean | M        | 处理结果标记 true->成功  false->失败                         |
| data              | object  | N        | 处理结果 处理失败时该值为空                                  |
| ├─requestId       | string  | M        | 请求ID                                                       |
| ├─phoneResult     | list    | M        | 短信状态查询结果                                             |
| │   ├─ mobile     | string  | M        | 手机号码                                                     |
| │   ├─ lastTime   | date    | M        | 最后更新时间                                                 |
| │   ├─ area       | string  | M        | 号码所属区域                                                 |
| │   ├─ numberType | string  | M        | 号码所属运营商                                               |
| │   ├─ status     | int     | M        | 号码状态 <br />-1 -> 未查询, 0 -> 空号, 1 -> 正常, 2 -> 停机, 3 -> 库无, 4 -> 沉默号, 5 -> 风险号, 99 -> 未知 |



### 6 后台接口相关用法

#### 6.1 模板类接口

##### 6.1.1 查询模板列表

> TplListQuery对象中只有appKey与appSecret为必填参数,若其他值未传，后台则使用默认值进行查询

```java
@Resource
private OpenApiTplService openApiTplService;

public void queryTplList() {
    TplListQuery tplListQuery = new TplListQuery();
    tplListQuery.setAppKey("系统架构部分配的appKey");
    tplListQuery.setAppSecret("系统架构部分配的appSecret");
    // 模板ID
    tplListQuery.setId(0);
    tplListQuery.setCode("模板编码");
    tplListQuery.setTitle("模板名称");
    // 模板状态 0：无效，1：有效
    tplListQuery.setStatus(0);
    // 签名ID
    tplListQuery.setSignId(0);
    // 短信类型 notify、market、verify、collector
    tplListQuery.setSmsTypeCode("短信类型");
    // 模板内容为模糊匹配
    tplListQuery.setContent("模板内容");
  	// 创建人为模糊匹配
    tplListQuery.setCreator("创建人");
    // 当前页码
    tplListQuery.setPageNum(0);
    // 一页显示的数量
    tplListQuery.setPageSize(0);
    ActionResult<CommonPager<TplVO>> actionResult = openApiTplService.queryTplList(tplListQuery);
    if (actionResult.isSuccess()) {
        // 查询成功
        CommonPager<TplVO> data = actionResult.getData();
        System.out.println("服务器响应 = " + JSON.toJSONString(data));
    } else {
        // 请求失败
        System.out.println("查询失败 = " + JSON.toJSONString(actionResult));
    }
}
```

##### 6.1.2 查询模板详情

> TplDetailQuery对象中appKey与appSecret以及id都为必填参数

```java
@Resource
private OpenApiTplService openApiTplService;

public void queryTplInfo() {
    TplDetailQuery tplDetailQuery = new TplDetailQuery();
    tplDetailQuery.setAppKey("系统架构部分配的appKey");
    tplDetailQuery.setAppSecret("系统架构部分配的appSecret");
    // 模板ID
    tplDetailQuery.setId(0);
    ActionResult<TplVO> actionResult = openApiTplService.queryTplInfo(tplDetailQuery);
    if (actionResult.isSuccess()) {
        // 查询成功
        TplVO data = actionResult.getData();
        System.out.println("服务器响应 = " + JSON.toJSONString(data));
    } else {
        // 请求失败
        System.out.println("查询失败 = " + JSON.toJSONString(actionResult));
    }
}
```

##### 6.1.3 根据code查询

> ```
> TplCodeQuery对象中appKey、appSecret、tplCode以及signName都为必填参数
> ```

```java
@Resource
private OpenApiTplService openApiTplService;

public void queryTplCode() {
    TplCodeQuery tplCodeQuery = new TplCodeQuery();
    tplCodeQuery.setAppKey("系统架构部分配的appKey");
    tplCodeQuery.setAppSecret("系统架构部分配的appSecret");
    tplCodeQuery.setTplCode("模板编码");
    tplCodeQuery.setSignName("签名名称");
    ActionResult<TplVO> actionResult = openApiTplService.queryTplCode(tplCodeQuery);
    if (actionResult.isSuccess()) {
        // 查询成功
        TplVO data = actionResult.getData();
        System.out.println("服务器响应 = " + JSON.toJSONString(data));
    } else {
        // 请求失败
        System.out.println("查询失败 = " + JSON.toJSONString(actionResult));
    }
}
```

##### 6.1.4 查询所有模板

> TplCodeQuery对象中appKey、appSecret、appCode都为必填参数

```java
@Resource
private OpenApiTplService openApiTplService;

public void queryByAppCode() {
    AllTplQuery allTplQuery = new AllTplQuery();
    allTplQuery.setAppKey("系统架构部分配的appKey");
    allTplQuery.setAppSecret("系统架构部分配的appSecret");
    allTplQuery.setAppCode("应用编码");
    ActionResult<List<TplInfoVO>> actionResult = openApiTplService.queryAllTplByAppCode(allTplQuery);
    if (actionResult.isSuccess()) {
        // 查询成功
        List<TplInfoVO> data = actionResult.getData();
        System.out.println("服务器响应 = " + JSON.toJSONString(data));
    } else {
        // 请求失败
        System.out.println("查询失败 = " + JSON.toJSONString(actionResult));
    }
}
```



#### 6.2 发送记录类接口

##### 6.2.1 查询发送记录列表

> ```
> OrderListQuery对象中只有appKey与appSecret以及requestIds为必填参数
> ```

```java
@Resource
private OpenApiOrderService openApiOrderService;

public void queryOrderList() {
    OrderListQuery orderListQuery = new OrderListQuery();
    orderListQuery.setAppKey("系统架构部分配的appKey");
    orderListQuery.setAppSecret("系统架构部分配的appSecret");
  	// 使用orderListQuery提供的add方法设置请求ID列表
    orderListQuery.add("请求ID1","请求ID2");
  	// 另一种方法自己new List并设置 setRequestIds
    // List<String> list = new ArrayList<>();
    // list.add("请求ID1");
    // list.add("请求ID2");
    // orderListQuery.setRequestIds(list);
    ActionResult<List<OrderVO>> actionResult = openApiOrderService.queryOrderList(orderListQuery);
    if (actionResult.isSuccess()) {
        // 查询成功
        List<OrderVO> data = actionResult.getData();
        System.out.println("服务器响应 = " + JSON.toJSONString(data));
    } else {
        // 请求失败
        System.out.println("查询失败 = " + JSON.toJSONString(actionResult));
    }
}
```

##### 6.2.2 查询订单状态列表

```java
package com.xhqb.spectre.examples;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.order.SmsOrderStatusQuery;
import com.xhqb.spectre.openapi.send.order.SmsOrderStatusVO;
import com.xhqb.spectre.openapi.service.OpenApiOrderService;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/24
 */
@Component
public class QueryOrderStatusExamples {

    @Resource
    private OpenApiOrderService openApiOrderService;

    public void queryOrderStatus() {
        SmsOrderStatusQuery orderStatusQuery = new SmsOrderStatusQuery();
        orderStatusQuery.setAppKey("系统架构部分配的appKey");
        orderStatusQuery.setAppSecret("系统架构部分配的appSecret");
        orderStatusQuery.setMobiles(Lists.newArrayList("19111110029"));
        ActionResult<List<SmsOrderStatusVO>> actionResult = openApiOrderService.queryOrderStatus(orderStatusQuery);
        if (actionResult.isSuccess()) {
            // 查询成功
            List<SmsOrderStatusVO> data = actionResult.getData();
            System.out.println("服务器响应 = " + JSON.toJSONString(data));
        } else {
            // 请求失败
            System.out.println("查询失败 = " + JSON.toJSONString(actionResult));
        }

    }
}

```

##### 6.2.3 根据mobile查询短信发送记录接口请求参数

```java
@Resource
private OpenApiTplService openApiTplService;

public void queryReissueOrder() {
    SmsReissueOrderQuery smsReissueOrderQuery = new SmsReissueOrderQuery();
		smsReissueOrderQuery.setMoblie("110")
    // 当前页码
    smsReissueOrderQuery.setPageNum(0);
    // 一页显示的数量
    smsReissueOrderQuery.setPageSize(20);
    ActionResult<CommonPager<SmsReissueOrderVO>> actionResult = openApiTplService.queryReissueOrder(tplListQuery);
    if (actionResult.isSuccess()) {
        // 查询成功
        CommonPager<TplVO> data = actionResult.getData();
        System.out.println("服务器响应 = " + JSON.toJSONString(data));
    } else {
        // 请求失败
        System.out.println("查询失败 = " + JSON.toJSONString(actionResult));
    }
}
```



### 7 后台接口定义

#### 7.1 模板类接口

##### 7.1.1 查询模板列表

> 查询模板列表请求参数

| 名称        | 类型   | 是否必须 | 备注                                                         |
| ----------- | ------ | -------- | ------------------------------------------------------------ |
| appKey      | string | M        | 系统架构部分配的appKey                                       |
| appSecret   | string | M        | 系统架构部分配的appSecret                                    |
| id          | int    | N        | 模板ID                                                       |
| code        | string | N        | 模板编码                                                     |
| title       | string | N        | 模板名称                                                     |
| status      | int    | N        | 模板状态 0->无效 1->有效                                     |
| signId      | int    | N        | 签名ID                                                       |
| smsTypeCode | string | N        | 短信类型 notify->通知、market->营销、verify->验证码、collector->催收 |
| content     | string | N        | 模板内容,查询使用模糊匹配                                    |
| creator     | string | N        | 创建人,查询使用模糊匹配                                      |
| pageNum     | int    | N        | 当前页码,默认为第1页                                         |
| pageSize    | int    | N        | 一页显示的数量,默认为10条数据                                |

> 查询模板列表响应参数

| 名称              | 类型    | 是否必填 | 备注                                 |
| ----------------- | ------- | -------- | ------------------------------------ |
| code              | string  | M        | 状态码                               |
| message           | string  | N        | 状态码描述                           |
| success           | boolean | M        | 处理结果标记 true->成功  false->失败 |
| data              | object  | N        | 处理结果 处理失败时该值为空          |
| ├─totalCount      | int     | M        | 总记录数                             |
| ├─dataList        | array   | M        | 数据列表                             |
| │  ├──id          | int     | M        | 模板ID                               |
| │  ├──code        | string  | M        | 模板编码                             |
| │  ├──smsTypeCode | string  | M        | 短信类型编码                         |
| │  ├──title       | string  | M        | 模板名称                             |
| │  ├──signId      | int     | M        | 签名ID                               |
| │  ├──content     | string  | M        | 模板内容                             |
| │  ├──appCode     | string  | M        | 应用编码                             |
| │  ├──status      | int     | M        | 模板状态 0->无效 1->有效             |
| │  ├──createTime  | string  | M        | 创建时间                             |
| │  ├──creator     | string  | M        | 创建者                               |
| │  ├──updateTime  | string  | M        | 更新时间                             |
| │  ├──updater     | string  | M        | 更新者                               |

##### 7.1.2 查询模板详情

> 查询模板详情请求参数

| 名称      | 类型   | 是否必须 | 备注                      |
| --------- | ------ | -------- | ------------------------- |
| appKey    | string | M        | 系统架构部分配的appKey    |
| appSecret | string | M        | 系统架构部分配的appSecret |
| id        | int    | M        | 模板ID                    |

> 查询模板详情响应参数

| 名称          | 类型    | 是否必填 | 备注                                                         |
| ------------- | ------- | -------- | ------------------------------------------------------------ |
| code          | string  | M        | 状态码                                                       |
| message       | string  | N        | 状态码描述                                                   |
| success       | boolean | M        | 处理结果标记 true->成功  false->失败                         |
| data          | object  | N        | 处理结果 处理失败时该值为空                                  |
| ├─id          | int     | M        | 模板ID                                                       |
| ├─code        | string  | M        | 模板编码                                                     |
| ├─smsTypeCode | string  | M        | 短信类型 notify->通知、market->营销、verify->验证码、collector->催收 |
| ├─title       | string  | M        | 模板名称                                                     |
| ├─signId      | int     | M        | 签名ID                                                       |
| ├─content     | string  | M        | 模板内容                                                     |
| ├─appCode     | string  | M        | 应用编码                                                     |
| ├─remark      | string  | M        | 备注                                                         |
| ├─status      | string  | M        | 模板状态 0->无效 1->有效                                     |
| ├─createTime  | string  | M        | 创建时间                                                     |
| ├─creator     | string  | M        | 创建者                                                       |
| ├─updateTime  | string  | M        | 更新时间                                                     |
| ├─updater     | string  | M        | 更新者                                                       |

##### 7.1.3 根据code查询

> code查询请求参数

| 名称      | 类型   | 是否必须 | 备注                      |
| --------- | ------ | -------- | ------------------------- |
| appKey    | string | M        | 系统架构部分配的appKey    |
| appSecret | string | M        | 系统架构部分配的appSecret |
| tplCode   | string | M        | 模板编码                  |
| signName  | string | M        | 签名名称                  |

> code查询响应参数

| 名称          | 类型    | 是否必填 | 备注                                                         |
| ------------- | ------- | -------- | ------------------------------------------------------------ |
| code          | string  | M        | 状态码                                                       |
| message       | string  | N        | 状态码描述                                                   |
| success       | boolean | M        | 处理结果标记 true->成功  false->失败                         |
| data          | object  | N        | 处理结果 处理失败时该值为空                                  |
| ├─id          | int     | M        | 模板ID                                                       |
| ├─code        | string  | M        | 模板编码                                                     |
| ├─smsTypeCode | string  | M        | 短信类型 notify->通知、market->营销、verify->验证码、collector->催收 |
| ├─title       | string  | M        | 模板名称                                                     |
| ├─signId      | int     | M        | 签名ID                                                       |
| ├─content     | string  | M        | 模板内容                                                     |
| ├─appCode     | string  | M        | 应用编码                                                     |
| ├─remark      | string  | M        | 备注                                                         |
| ├─status      | string  | M        | 模板状态 0->无效 1->有效                                     |
| ├─createTime  | string  | M        | 创建时间                                                     |
| ├─creator     | string  | M        | 创建者                                                       |
| ├─updateTime  | string  | M        | 更新时间                                                     |
| ├─updater     | string  | M        | 更新者                                                       |

##### 7.1.4 查询所有模板

> 查询所有模板请求

| 名称      | 类型   | 是否必须 | 备注                      |
| --------- | ------ | -------- | ------------------------- |
| appKey    | string | M        | 系统架构部分配的appKey    |
| appSecret | string | M        | 系统架构部分配的appSecret |
| appCode   | string | M        | 应用编码                  |

> 查询所有模板响应

| 名称          | 类型    | 是否必填 | 备注                                                         |
| ------------- | ------- | -------- | ------------------------------------------------------------ |
| code          | string  | M        | 状态码                                                       |
| message       | string  | N        | 状态码描述                                                   |
| success       | boolean | M        | 处理结果标记 true->成功  false->失败                         |
| data          | object  | N        | 处理结果 处理失败时该值为空                                  |
| ├─id          | int     | M        | 模板ID                                                       |
| ├─code        | string  | M        | 模板编码                                                     |
| ├─smsTypeCode | string  | M        | 短信类型 notify->通知、market->营销、verify->验证码、collector->催收 |
| ├─title       | string  | M        | 模板名称                                                     |
| ├─signId      | int     | M        | 签名ID                                                       |
| ├─content     | string  | M        | 模板内容                                                     |
| ├─appCode     | string  | M        | 应用编码                                                     |
| ├─remark      | string  | M        | 备注                                                         |
| ├─status      | string  | M        | 模板状态 0->无效 1->有效                                     |
| ├─createTime  | string  | M        | 创建时间                                                     |
| ├─creator     | string  | M        | 创建者                                                       |
| ├─updateTime  | string  | M        | 更新时间                                                     |
| ├─updater     | string  | M        | 更新者                                                       |
| ├─signName    | string  | M        | 签名名称                                                     |

#### 7.2 发送记录类接口

##### 7.2.1 查询发送记录列表

> 查询发送记录列表请求参数

| 名称       | 类型   | 是否必须 | 备注                         |
| ---------- | ------ | -------- | ---------------------------- |
| appKey     | string | M        | 系统架构部分配的appKey       |
| appSecret  | string | M        | 系统架构部分配的appSecret    |
| requestIds | array  | M        | 请求ID列表(内容类型为string) |
| ├─         | string | M        | 具体的请求ID数据             |
| mobile     | string | M        | 手机号码                     |

> 查询发送记录列表查询响应参数

| 名称           | 类型    | 是否必填 | 备注                                     |
| -------------- | ------- | -------- | ---------------------------------------- |
| code           | string  | M        | 状态码                                   |
| message        | string  | N        | 状态码描述                               |
| success        | boolean | M        | 处理结果标记 true->成功  false->失败     |
| data           | array   | N        | 处理结果 处理失败时该值为空              |
| ├─requestId    | string  | M        | 请求ID                                   |
| ├─mobile       | string  | M        | 手机号码                                 |
| ├─orderId      | string  | M        | 发送记录ID                               |
| ├─sendStatus   | int     | M        | 发送状态 0 -> 成功，-1 -> 未知，其余失败 |
| ├─reportStatus | int     | M        | 回执状态 0 -> 成功，-1 -> 未知，其余失败 |
| ├─content      | string  | M        | 短信内容                                 |
| ├─billCount    | int     | M        | 计费条数                                 |
| ├─reportDesc   | string  | N        | 回执描述                                 |
| ├─reportCode   | string  | M        | 回执编码                                 |

##### 7.2.2 查询订单状态列表

> 查询订单状态请求参数

| 名称      | 类型   | 是否必须 | 备注                      |
| --------- | ------ | -------- | ------------------------- |
| appKey    | string | M        | 系统架构部分配的appKey    |
| appSecret | string | M        | 系统架构部分配的appSecret |
| appCode   | string | N        | 查询发送短信应用的编码    |
| tplCode   | string | N        | 模板编码                  |
| requestId | string | N        | 请求ID                    |
| mobiles   | array  | M        | 手机号码                  |
| ├─        | string | M        | 具体的手机号码            |

> 查询订单状态查询响应参数

| 名称           | 类型    | 是否必填 | 备注                                     |
| -------------- | ------- | -------- | ---------------------------------------- |
| code           | string  | M        | 状态码                                   |
| message        | string  | N        | 状态码描述                               |
| success        | boolean | M        | 处理结果标记 true->成功  false->失败     |
| data           | array   | N        | 处理结果 处理失败时该值为空              |
| ├─requestId    | string  | M        | 请求ID                                   |
| ├─mobile       | string  | M        | 手机号码                                 |
| ├─orderId      | string  | M        | 发送记录ID                               |
| ├─sendStatus   | int     | M        | 发送状态 0 -> 成功，-1 -> 未知，其余失败 |
| ├─reportStatus | int     | M        | 回执状态 0 -> 成功，-1 -> 未知，其余失败 |
| ├─content      | string  | M        | 短信内容                                 |
| ├─billCount    | int     | M        | 计费条数                                 |
| ├─callCode     | int     | N        | 回执错误码                               |

##### 7.2.3 根据mobile查询短信发送记录接口

根据mobile查询短信发送记录接口请求参数

| 名称     | 类型   | 是否必须 | 备注           |
| -------- | ------ | -------- | -------------- |
| mobile   | string | M        | 手机号         |
| pageNum  | int    | M        | 当前页码       |
| pageSize | int    | M        | 一页显示的数量 |

根据mobile查询短信发送记录接口响应参数

| 名称              | 类型    | 是否必填 | 备注                                 |
| ----------------- | ------- | -------- | ------------------------------------ |
| code              | string  | M        | 状态码                               |
| message           | string  | N        | 状态码描述                           |
| success           | boolean | M        | 处理结果标记 true->成功  false->失败 |
| data              | object  | N        | 处理结果 处理失败时该值为空          |
| ├─totalCount      | int     | M        | 总记录数                             |
| ├─dataList        | array   | M        | 数据列表                             |
| │  ├──tplCode     | String  | M        | 模板编码                             |
| │  ├──sendTime    | string  | M        | 发送日期                             |
| │  ├──smsTypeCode | string  | M        | 短信类型编码                         |
| │  ├──sendStatus  | int     | M        | 发送状态 0：成功，-1：未知，其余失败 |
| │  ├──sendDesc    | string  | M        | 发送失败原因                         |
| │  ├──content     | string  | M        | 模板内容                             |



### 8 短链

#### 8.1 短链创建

1 短链创建示例

```java
    @Resource
    private SdkSmsSenderService sdkSmsSenderService;

    /**
     * 短链创建
     */
    public void shortUrlCreate() {
        ShortUrlDTO shortUrlDTO = new ShortUrlDTO();
        shortUrlDTO.setAppCode("系统架构部分配的appCode");
        shortUrlDTO.setAppSecret("系统架构部分配的appSecret");
        // 待生成短链的源地址
        shortUrlDTO.setSrcUrl("https://www.xhqb.com");
        // 有效期。1：90天；2：180天；3：365天；4：永久有效
        shortUrlDTO.setValidPeriod(1);
        // 描述信息不能够为空，最大长度不能够超过256
        shortUrlDTO.setDescription("sdk测试短链");
        SendResult<String> result = sdkSmsSenderService.shortUrlCreate(shortUrlDTO);
        // {"code":200,"data":"stest.xh1.cn/NY61H","date":"2023-11-01 14:04:35","msg":"success","success":true}
        System.out.println(JSON.toJSONString(result));
    }

```



2 短链接口定义

| 名称        | 类型   | 是否必须 | 备注                                                   |
| ----------- | ------ | -------- | ------------------------------------------------------ |
| appKey      | string | M        | 系统架构部分配的appKey                                 |
| appSecret   | string | M        | 系统架构部分配的appSecret                              |
| srcUrl      | string | M        | 源URL，最大长度不能够超过512, 如: https://www.xhqb.com |
| validPeriod | int    | M        | 有效期。1：90天；2：180天；3：365天；4：永久有效       |
| description | string | M        | 描述信息不能够为空，最大长度不能够超过256              |

| 名称    | 类型    | 是否必填 | 备注                                 |
| ------- | ------- | -------- | ------------------------------------ |
| code    | string  | M        | 状态码                               |
| message | string  | N        | 状态码描述                           |
| success | boolean | M        | 处理结果标记 true->成功  false->失败 |
| data    | string  | N        | 短链地址                             |

### 9 状态码

#### 9.1 状态码列表

| 状态码 | 描述信息                         |
| ------ | -------------------------------- |
| 200    | 操作成功                         |
| 400    | 操作失败                         |
| 403    | 没有相关权限                     |
| 1000   | 参数缺失                         |
| 1001   | 无效的模版ID                     |
| 1002   | appId不能为空                    |
| 1003   | 缺失request参数                  |
| 1004   | 缺失outBizId参数                 |
| 1005   | 缺失渠道参数                     |
| 1006   | 缺失短信内容参数                 |
| 1007   | 缺失短信类型参数                 |
| 1008   | 缺失短信签名参数                 |
| 1009   | 缺失短信参数号码集               |
| 1010   | 缺失短信号码集                   |
| 1011   | 手机号码无效                     |
| 1012   | 短信发送参数有误                 |
| 1013   | 请求参数非法                     |
| 1014   | 所选模版动态参数不能为空         |
| 1015   | 手机号码名中黑名单               |
| 1016   | appCode和outOrderId已经存在      |
| 1017   | 验证码长度必须在4-6位之间        |
| 1018   | 验证码不能为空                   |
| 1019   | 验证码识别码不能为空             |
| 1020   | curTime时间错误                  |
| 1021   | 缺少随机码参数或随机码已失效     |
| 1022   | 随机码长度必须在32位内           |
| 1023   | 没有获取到渠道信息               |
| 2000   | 参数检验失败                     |
| 2001   | 时间戳无效                       |
| 2002   | appKey无效                       |
| 2003   | 签名超时                         |
| 2004   | 签名认证失败                     |
| 2005   | 短信发送过于频繁，请稍候再试     |
| 2006   | 发送号码超出最大允许提交值       |
| 2007   | 缺失短信发送权重                 |
| 2008   | 缺失短信模版                     |
| 2009   | 短信模版不存在                   |
| 2010   | 不支持该号码                     |
| 2011   | 未知的短信发送类型               |
| 2012   | 重复发送同一条短信               |
| 2013   | 同号码短时间内发送了相同短信内容 |
| 2014   | 同号码发送该短信模版次数达到上限 |
| 2015   | 无http结果返回                   |
| 2016   | 未定义的msgsendresultenum结果码  |
| 2017   | 系统繁忙                         |
| 2018   | 重复提交短信发送请求             |
| 2019   | 短信发送频率越限                 |
| 2020   | 短信类型错误                     |
| 3001   | 系统异常                         |

### 10 本地调试

#### 10.1 指定短信网关地址

设置jvm参数

```java
-Dspectre.sdk.sms.spectreApiHost=https://spectre-test.xhdev.xyz/spectre-api
```

#### 10.2 查看网关短信交互日志

设置jvm参数

```java
-Dspectre.sdk.sms.debug=true
```
