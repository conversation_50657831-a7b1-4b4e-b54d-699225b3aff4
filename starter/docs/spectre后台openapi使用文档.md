### 4spectre后台openapi使用文档

#### 关于文档

| 版本           | 类型 | 日期       | 修改人     | 说明                                                         |
| -------------- | ---- | ---------- | ---------- | ------------------------------------------------------------ |
| 1.1.0-SNAPSHOT | 新建 | 2021-12-16 | 系统架构部 | 新建                                                         |
| 1.1.0-SNAPSHOT | 新增 | 2022-02-09 | 系统架构部 | 新增4.1.3根据code查询模板接口<br />新增4.2.1查询发送记录列表接口 |
| 1.1.0-SNAPSHOT | 新增 | 2022-02-11 | 系统架构部 | 新增4.1.4查询所有模板接口                                    |
|                |      |            |            |                                                              |
|                |      |            |            |                                                              |
|                |      |            |            |                                                              |
|                |      |            |            |                                                              |
|                |      |            |            |                                                              |

### 1 简介

该sdk提供查询spectre后台openapi功能，同时提供了普通spring项目和springboot项目两种接入方式。



### 2 接入方式

#### 2.1 普通spring项目

- 添加spectre-admin-api-core依赖

  ```
  <dependency>
      <groupId>com.xhqb.spectre</groupId>
      <artifactId>spectre-admin-api-core</artifactId>
      <version>1.1.0.RELEASE-SNAPSHOT</version>
  </dependency>
  ```

- 添加openapi包名 com.xhqb.spectre.openapi (**注意:xxx为原有项目的包名称**)

  ```xml
  <context:component-scan base-package="xxx,com.xhqb.spectre.openapi"/>
  ```

#### 2.2 springboot项目

- 添加spectre-admin-api-starter依赖

  ```xml
  <dependency>
      <groupId>com.xhqb.spectre</groupId>
      <artifactId>spectre-admin-api-starter</artifactId>
      <version>1.1.0.RELEASE-SNAPSHOT</version>
  </dependency>
  ```

  

### 3 准备工作

- 资源申请

  联系系统架构部申请appKey和appSecret

- openapi地址

  | 环境 | openapi地址                              |
  | ---- | ---------------------------------------- |
  | test | http://spectre-admin.test8/spectre-admin |
  | uat  | http://spectre-admin.prod2/spectre-admin |
  | prod | http://spectre-admin/spectre-admin       |

- 环境配置

  后台openapi默认地址为http://spectre-admin/spectre-admin，可以通过venus配置spectre.admin.openapi.spectreAdminHost覆盖默认值。

  ```java
  spectre.admin.openapi.spectreAdminHost=http://spectre-admin/spectre-admin
  ```

### 4 用法

#### 4.1 模板类接口

##### 4.1.1 查询模板列表

> TplListQuery对象中只有appKey与appSecret为必填参数,若其他值未传，后台则使用默认值进行查询

```java
@Resource
private OpenApiTplService openApiTplService;

public void queryTplList() {
    TplListQuery tplListQuery = new TplListQuery();
    tplListQuery.setAppKey("系统架构部分配的appKey");
    tplListQuery.setAppSecret("系统架构部分配的appSecret");
    // 模板ID
    tplListQuery.setId(0);
    tplListQuery.setCode("模板编码");
    tplListQuery.setTitle("模板名称");
    // 模板状态 0：无效，1：有效
    tplListQuery.setStatus(0);
    // 签名ID
    tplListQuery.setSignId(0);
    // 短信类型 notify、market、verify、collector
    tplListQuery.setSmsTypeCode("短信类型");
    // 模板内容为模糊匹配
    tplListQuery.setContent("模板内容");
  	// 创建人为模糊匹配
    tplListQuery.setCreator("创建人");
    // 当前页码
    tplListQuery.setPageNum(0);
    // 一页显示的数量
    tplListQuery.setPageSize(0);
    ActionResult<CommonPager<TplVO>> actionResult = openApiTplService.queryTplList(tplListQuery);
    if (actionResult.isSuccess()) {
        // 查询成功
        CommonPager<TplVO> data = actionResult.getData();
        System.out.println("服务器响应 = " + JSON.toJSONString(data));
    } else {
        // 请求失败
        System.out.println("查询失败 = " + JSON.toJSONString(actionResult));
    }
}
```

##### 4.1.2 查询模板详情

> TplDetailQuery对象中appKey与appSecret以及id都为必填参数

```java
@Resource
private OpenApiTplService openApiTplService;

public void queryTplInfo() {
    TplDetailQuery tplDetailQuery = new TplDetailQuery();
    tplDetailQuery.setAppKey("系统架构部分配的appKey");
    tplDetailQuery.setAppSecret("系统架构部分配的appSecret");
    // 模板ID
    tplDetailQuery.setId(0);
    ActionResult<TplVO> actionResult = openApiTplService.queryTplInfo(tplDetailQuery);
    if (actionResult.isSuccess()) {
        // 查询成功
        TplVO data = actionResult.getData();
        System.out.println("服务器响应 = " + JSON.toJSONString(data));
    } else {
        // 请求失败
        System.out.println("查询失败 = " + JSON.toJSONString(actionResult));
    }
}
```

##### 4.1.3 根据code查询

> ```
> TplCodeQuery对象中appKey、appSecret、tplCode以及signName都为必填参数
> ```

```java
@Resource
private OpenApiTplService openApiTplService;

public void queryTplCode() {
    TplCodeQuery tplCodeQuery = new TplCodeQuery();
    tplCodeQuery.setAppKey("系统架构部分配的appKey");
    tplCodeQuery.setAppSecret("系统架构部分配的appSecret");
    tplCodeQuery.setTplCode("模板编码");
    tplCodeQuery.setSignName("签名名称");
    ActionResult<TplVO> actionResult = openApiTplService.queryTplCode(tplCodeQuery);
    if (actionResult.isSuccess()) {
        // 查询成功
        TplVO data = actionResult.getData();
        System.out.println("服务器响应 = " + JSON.toJSONString(data));
    } else {
        // 请求失败
        System.out.println("查询失败 = " + JSON.toJSONString(actionResult));
    }
}
```

##### 4.1.4 查询所有模板

> TplCodeQuery对象中appKey、appSecret、appCode都为必填参数

```java
@Resource
private OpenApiTplService openApiTplService;

public void queryByAppCode() {
    AllTplQuery allTplQuery = new AllTplQuery();
    allTplQuery.setAppKey("系统架构部分配的appKey");
    allTplQuery.setAppSecret("系统架构部分配的appSecret");
    allTplQuery.setAppCode("应用编码");
    ActionResult<List<TplInfoVO>> actionResult = openApiTplService.queryAllTplByAppCode(allTplQuery);
    if (actionResult.isSuccess()) {
        // 查询成功
        List<TplInfoVO> data = actionResult.getData();
        System.out.println("服务器响应 = " + JSON.toJSONString(data));
    } else {
        // 请求失败
        System.out.println("查询失败 = " + JSON.toJSONString(actionResult));
    }
}
```



#### 4.2 发送记录类接口

##### 4.2.1 查询发送记录列表

> ```
> OrderListQuery对象中只有appKey与appSecret以及requestIds为必填参数
> ```

```java
@Resource
private OpenApiOrderService openApiOrderService;

public void queryOrderList() {
    OrderListQuery orderListQuery = new OrderListQuery();
    orderListQuery.setAppKey("系统架构部分配的appKey");
    orderListQuery.setAppSecret("系统架构部分配的appSecret");
  	// 使用orderListQuery提供的add方法设置请求ID列表
    orderListQuery.add("请求ID1","请求ID2");
  	// 另一种方法自己new List并设置 setRequestIds
    // List<String> list = new ArrayList<>();
    // list.add("请求ID1");
    // list.add("请求ID2");
    // orderListQuery.setRequestIds(list);
    ActionResult<List<OrderVO>> actionResult = openApiOrderService.queryOrderList(orderListQuery);
    if (actionResult.isSuccess()) {
        // 查询成功
        List<OrderVO> data = actionResult.getData();
        System.out.println("服务器响应 = " + JSON.toJSONString(data));
    } else {
        // 请求失败
        System.out.println("查询失败 = " + JSON.toJSONString(actionResult));
    }
}
```



### 5 接口定义

#### 5.1 模板类接口

##### 5.1.1 查询模板列表

> 查询模板列表请求参数

| 名称        | 类型   | 是否必须 | 备注                                                         |
| ----------- | ------ | -------- | ------------------------------------------------------------ |
| appKey      | string | M        | 系统架构部分配的appKey                                       |
| appSecret   | string | M        | 系统架构部分配的appSecret                                    |
| id          | int    | N        | 模板ID                                                       |
| code        | string | N        | 模板编码                                                     |
| title       | string | N        | 模板名称                                                     |
| status      | int    | N        | 模板状态 0->无效 1->有效                                     |
| signId      | int    | N        | 签名ID                                                       |
| smsTypeCode | string | N        | 短信类型 notify->通知、market->营销、verify->验证码、collector->催收 |
| content     | string | N        | 模板内容,查询使用模糊匹配                                    |
| creator     | string | N        | 创建人,查询使用模糊匹配                                      |
| pageNum     | int    | N        | 当前页码,默认为第1页                                         |
| pageSize    | int    | N        | 一页显示的数量,默认为10条数据                                |

> 查询模板列表响应参数

| 名称    | 类型    | 是否必填 | 备注                                 |
| ------- | ------- | -------- | ------------------------------------ |
| code    | string  | M        | 状态码                               |
| message | string  | N        | 状态码描述                           |
| success | boolean | M        | 处理结果标记 true->成功  false->失败 |
| data    | object  | N        | 处理结果 处理失败时该值为空 |
| ├─totalCount | int | M | 总记录数 |
| ├─dataList | array | M | 数据列表 |
| │  ├──id | int | M | 模板ID |
| │  ├──code | string | M | 模板编码 |
| │  ├──smsTypeCode | string | M | 短信类型编码 |
| │  ├──title | string | M | 模板名称 |
| │  ├──signId | int | M | 签名ID |
| │  ├──content | string | M | 模板内容 |
| │  ├──appCode | string | M | 应用编码 |
| │  ├──status | int | M | 模板状态 0->无效 1->有效 |
| │  ├──createTime | string | M | 创建时间 |
| │  ├──creator | string | M | 创建者 |
| │  ├──updateTime | string | M | 更新时间 |
| │  ├──updater | string | M | 更新者 |

##### 5.1.2 查询模板详情

> 查询模板详情请求参数

| 名称      | 类型   | 是否必须 | 备注                      |
| --------- | ------ | -------- | ------------------------- |
| appKey    | string | M        | 系统架构部分配的appKey    |
| appSecret | string | M        | 系统架构部分配的appSecret |
| id        | int    | M        | 模板ID                    |

> 查询模板详情响应参数

| 名称          | 类型    | 是否必填 | 备注                                                         |
| ------------- | ------- | -------- | ------------------------------------------------------------ |
| code          | string  | M        | 状态码                                                       |
| message       | string  | N        | 状态码描述                                                   |
| success       | boolean | M        | 处理结果标记 true->成功  false->失败                         |
| data          | object  | N        | 处理结果 处理失败时该值为空                                  |
| ├─id          | int     | M        | 模板ID                                                       |
| ├─code        | string  | M        | 模板编码                                                     |
| ├─smsTypeCode | string  | M        | 短信类型 notify->通知、market->营销、verify->验证码、collector->催收 |
| ├─title       | string  | M        | 模板名称                                                     |
| ├─signId      | int     | M        | 签名ID                                                       |
| ├─content     | string  | M        | 模板内容                                                     |
| ├─appCode     | string  | M        | 应用编码                                                     |
| ├─remark      | string  | M        | 备注                                                         |
| ├─status      | string  | M        | 模板状态 0->无效 1->有效                                     |
| ├─createTime  | string  | M        | 创建时间                                                     |
| ├─creator     | string  | M        | 创建者                                                       |
| ├─updateTime  | string  | M        | 更新时间                                                     |
| ├─updater     | string  | M        | 更新者                                                       |

##### 5.1.3 根据code查询

> code查询请求参数

| 名称      | 类型   | 是否必须 | 备注                      |
| --------- | ------ | -------- | ------------------------- |
| appKey    | string | M        | 系统架构部分配的appKey    |
| appSecret | string | M        | 系统架构部分配的appSecret |
| tplCode   | string | M        | 模板编码                  |
| signName  | string | M        | 签名名称                  |

> code查询响应参数

| 名称          | 类型    | 是否必填 | 备注                                                         |
| ------------- | ------- | -------- | ------------------------------------------------------------ |
| code          | string  | M        | 状态码                                                       |
| message       | string  | N        | 状态码描述                                                   |
| success       | boolean | M        | 处理结果标记 true->成功  false->失败                         |
| data          | object  | N        | 处理结果 处理失败时该值为空                                  |
| ├─id          | int     | M        | 模板ID                                                       |
| ├─code        | string  | M        | 模板编码                                                     |
| ├─smsTypeCode | string  | M        | 短信类型 notify->通知、market->营销、verify->验证码、collector->催收 |
| ├─title       | string  | M        | 模板名称                                                     |
| ├─signId      | int     | M        | 签名ID                                                       |
| ├─content     | string  | M        | 模板内容                                                     |
| ├─appCode     | string  | M        | 应用编码                                                     |
| ├─remark      | string  | M        | 备注                                                         |
| ├─status      | string  | M        | 模板状态 0->无效 1->有效                                     |
| ├─createTime  | string  | M        | 创建时间                                                     |
| ├─creator     | string  | M        | 创建者                                                       |
| ├─updateTime  | string  | M        | 更新时间                                                     |
| ├─updater     | string  | M        | 更新者                                                       |

##### 5.1.4 查询所有模板

> 查询所有模板请求

| 名称      | 类型   | 是否必须 | 备注                      |
| --------- | ------ | -------- | ------------------------- |
| appKey    | string | M        | 系统架构部分配的appKey    |
| appSecret | string | M        | 系统架构部分配的appSecret |
| appCode   | string | M        | 应用编码                  |

> 查询所有模板响应

| 名称          | 类型    | 是否必填 | 备注                                                         |
| ------------- | ------- | -------- | ------------------------------------------------------------ |
| code          | string  | M        | 状态码                                                       |
| message       | string  | N        | 状态码描述                                                   |
| success       | boolean | M        | 处理结果标记 true->成功  false->失败                         |
| data          | object  | N        | 处理结果 处理失败时该值为空                                  |
| ├─id          | int     | M        | 模板ID                                                       |
| ├─code        | string  | M        | 模板编码                                                     |
| ├─smsTypeCode | string  | M        | 短信类型 notify->通知、market->营销、verify->验证码、collector->催收 |
| ├─title       | string  | M        | 模板名称                                                     |
| ├─signId      | int     | M        | 签名ID                                                       |
| ├─content     | string  | M        | 模板内容                                                     |
| ├─appCode     | string  | M        | 应用编码                                                     |
| ├─remark      | string  | M        | 备注                                                         |
| ├─status      | string  | M        | 模板状态 0->无效 1->有效                                     |
| ├─createTime  | string  | M        | 创建时间                                                     |
| ├─creator     | string  | M        | 创建者                                                       |
| ├─updateTime  | string  | M        | 更新时间                                                     |
| ├─updater     | string  | M        | 更新者                                                       |
| ├─signName    | string  | M        | 签名名称                                                     |

#### 5.2 发送记录类接口

##### 5.2.1 查询发送记录列表

> 查询发送记录列表请求参数

| 名称       | 类型   | 是否必须 | 备注                         |
| ---------- | ------ | -------- | ---------------------------- |
| appKey     | string | M        | 系统架构部分配的appKey       |
| appSecret  | string | M        | 系统架构部分配的appSecret    |
| requestIds | array  | M        | 请求ID列表(内容类型为string) |
| ├─         | string | M        | 具体的请求ID数据             |

> 查询发送记录列表查询响应参数

| 名称           | 类型    | 是否必填 | 备注                                     |
| -------------- | ------- | -------- | ---------------------------------------- |
| code           | string  | M        | 状态码                                   |
| message        | string  | N        | 状态码描述                               |
| success        | boolean | M        | 处理结果标记 true->成功  false->失败     |
| data           | array   | N        | 处理结果 处理失败时该值为空              |
| ├─requestId    | string  | M        | 请求ID                                   |
| ├─mobile       | string  | M        | 手机号码                                 |
| ├─orderId      | string  | M        | 发送记录ID                               |
| ├─sendStatus   | int     | M        | 发送状态 0 -> 成功，-1 -> 未知，其余失败 |
| ├─reportStatus | int     | M        | 回执状态 0 -> 成功，-1 -> 未知，其余失败 |

### 6 状态码

> 目前只定义了这些状态码，随着openapi功能越来越丰富，状态码也会随之增加

| 状态码 | 描述信息                |
| ------ | ----------------------- |
| 200    | Success                 |
| 400999 | 未知异常                |
| 401001 | 应用未被授权            |
| 400001 | appKey不能够为空        |
| 400002 | timestamp不能够为空     |
| 400003 | nonce不能够为空         |
| 400004 | sign不能够为空          |
| 400005 | timestamp不合法         |
| 400006 | timestamp已过期         |
| 400007 | nonce长度不能够超过32位 |
| 400008 | nonce重复               |
| 400009 | 签名不合法              |
|        |                         |
|        |                         |
|        |                         |


### 7 本地调试

- 指定openapi地址

  设置jvm参数

  ```java
  -Dspectre.admin.openapi.spectreAdminHost=https://spectre-test.xhdev.xyz/spectre-admin
  ```

- 查看接口交互日志

  设置jvm参数

  ```java
  -Dspectre.admin.openapi.debug=true
  ```
