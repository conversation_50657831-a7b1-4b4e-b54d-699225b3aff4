<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xhqb.spectre</groupId>
        <artifactId>spectre-parent</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>starter</artifactId>
    <packaging>pom</packaging>
    <version>1.2.28.RELEASE-SNAPSHOT</version>
    <modules>
        <module>spectre-http-api-starter</module>
        <module>spectre-http-api-core</module>
        <module>spectre-admin-api-core</module>
        <module>spectre-admin-api-starter</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <spring-boot.version>2.2.8.RELEASE</spring-boot.version>
        <sdk.spring.version>5.3.12</sdk.spring.version>
        <httpclient.version>4.5.12</httpclient.version>
        <commons.codec.version>1.15</commons.codec.version>
    </properties>

    <distributionManagement>
        <repository>
            <id>dev-releases</id>
            <name>nexus-releases</name>
            <url>http://repo.xhdev.xyz/releases</url>
        </repository>
        <snapshotRepository>
            <id>dev-snapshots</id>
            <name>nexus-snapshots</name>
            <url>http://repo.xhdev.xyz/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons.codec.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>
    <dependencies>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${sdk.spring.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${sdk.spring.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.10.4</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.32</version>
            <scope>provided</scope>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>fast</id>
            <activation>
                <property>
                    <name>fast</name>
                </property>
            </activation>
            <properties>
                <disable.checks>true</disable.checks>
            </properties>
        </profile>
        <profile>
            <id>jib-skip</id>
            <properties>
                <jib-docker.skip>true</jib-docker.skip>
            </properties>
        </profile>
    </profiles>

</project>