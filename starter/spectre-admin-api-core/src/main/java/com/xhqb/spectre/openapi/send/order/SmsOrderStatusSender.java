package com.xhqb.spectre.openapi.send.order;

import com.xhqb.spectre.openapi.contants.Apis;
import com.xhqb.spectre.openapi.send.AbstractOpenApiSender;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.SpectreOpenApiHttpFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 查询订单状态
 *
 * <AUTHOR>
 * @date 2023/2/24
 */
@Component
public class SmsOrderStatusSender extends AbstractOpenApiSender<SmsOrderStatusQuery, List<SmsOrderStatusVO>> {

    private static final int UNKNOWN_CODE = -2;
    private static final Map<String, Integer> CALL_CODE_MAPPING = new HashMap<>();

    static {
        CALL_CODE_MAPPING.put("反馈成功", 0);
        CALL_CODE_MAPPING.put("黑名单", 1);
        CALL_CODE_MAPPING.put("超限", 2);
        CALL_CODE_MAPPING.put("渠道屏蔽、失败", 3);
        CALL_CODE_MAPPING.put("运营商屏蔽、失败", 4);
        CALL_CODE_MAPPING.put("手机异常或号码失败", 5);
        CALL_CODE_MAPPING.put("文案或签名未报备", 6);
        CALL_CODE_MAPPING.put("关键词拦截", 7);
        CALL_CODE_MAPPING.put("其他", 8);
        CALL_CODE_MAPPING.put("UNDELIV", 9);
    }

    @Resource
    private SpectreOpenApiHttpFactory spectreOpenApiHttpFactory;

    @Override
    protected String getApi() {
        return Apis.Order.QUERY_ORDER_STATUS;
    }

    @Override
    protected ActionResult<List<SmsOrderStatusVO>> doSend(String apiUrl, SmsOrderStatusQuery request) {
        ActionResult<List<SmsOrderStatusVO>> actionResult = spectreOpenApiHttpFactory.postJSON(apiUrl, request, new ParameterizedTypeReference<ActionResult<List<SmsOrderStatusVO>>>() {
        });
        if (Objects.isNull(actionResult)) {
            return null;
        }

        List<SmsOrderStatusVO> dataList = actionResult.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            return actionResult;
        }

        dataList.forEach(this::mutate);
        return actionResult;
    }

    private void mutate(SmsOrderStatusVO vo) {
        if (Objects.isNull(vo)) {
            return;
        }
        String reportDesc = vo.getReportDesc();
        if (StringUtils.isBlank(reportDesc)) {
            reportDesc = "";
        }

        Integer callCode = CALL_CODE_MAPPING.get(reportDesc);
        if (Objects.nonNull(callCode)) {
            vo.setCallCode(callCode);
            return;
        }
        int reportStatus = vo.getReportStatus();
        if (Objects.equals(-1, reportStatus)) {
            return;
        }

        // 未定义的编码
        vo.setCallCode(UNKNOWN_CODE);
    }
}
