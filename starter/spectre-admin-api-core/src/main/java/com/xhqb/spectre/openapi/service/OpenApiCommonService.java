package com.xhqb.spectre.openapi.service;

import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.common.MarketSceneQuery;
import com.xhqb.spectre.openapi.send.common.MarketSceneVO;
import com.xhqb.spectre.openapi.send.common.ProjectDescQuery;
import com.xhqb.spectre.openapi.send.common.ProjectDescVO;

import java.util.List;

public interface OpenApiCommonService {

    /**
     * 项目用途查询
     *
     * @return
     */
    ActionResult<List<ProjectDescVO>> queryProjectDesc(ProjectDescQuery query);

    /**
     * 查询项目用途接口
     *
     * @return
     */
    ActionResult<List<MarketSceneVO>> queryMarketScene(MarketSceneQuery query);
}
