package com.xhqb.spectre.openapi.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.PropertyPlaceholderHelper;

import java.util.HashMap;
import java.util.Map;

/**
 * restful路径替换工具
 *
 * <AUTHOR>
 * @date 2021/12/16
 */
@Slf4j
public class PathUtils {

    private static final PropertyPlaceholderHelper PLACE_HOLDER_HELPER = new PropertyPlaceholderHelper("{", "}");

    private static final int TWO = 2;

    /**
     * restful 路径格式化
     *
     * @param url
     * @param mapping
     * @return
     */
    public static String format(String url, Map<String, String> mapping) {
        return PLACE_HOLDER_HELPER.replacePlaceholders(url, placeholderName -> mapping.get(placeholderName));
    }

    /**
     * 格式化
     * <p>
     * form("getUser/{id}","id","1")
     *
     * @param url
     * @param vars 参数一定要是2的倍数 key, value
     * @return
     */
    public static String format(String url, String... vars) {
        if (vars.length <= 0) {
            return url;
        }

        if (vars.length % TWO != 0) {
            log.warn("传入的参数不是2的倍数");
            return url;
        }

        Map<String, String> mapping = new HashMap<>(16);
        for (int i = 0; i < vars.length; i += TWO) {
            mapping.put(vars[i], vars[i + 1]);
        }
        return format(url, mapping);
    }
}
