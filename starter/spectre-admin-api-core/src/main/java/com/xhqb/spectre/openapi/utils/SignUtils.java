package com.xhqb.spectre.openapi.utils;

import com.xhqb.spectre.openapi.send.OpenApiRequest;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.http.HttpHeaders;

import java.util.concurrent.ThreadLocalRandom;

/**
 * 签名工具
 * <p>
 * 1. 拼接字待加签的字符串 appKey+appCode+nonce+NonceValue+timestamp+TimeValue+appSecret
 * 2. 根据第1步生成的字符串进行sha1生成签名
 *
 * <AUTHOR>
 * @date 2021/12/15
 */
public class SignUtils {

    /**
     * 最大的随机数长度
     */
    private static final int MAX_NONCE_LENGTH = 32;
    /**
     * 随机数seed
     */
    private static final long DEFAULT_BOUND = 100000L;

    /**
     * 请求头 存储appCode
     */
    private static final String H_APP_KEY = "appKey";
    /**
     * 随机数
     */
    private static final String H_NONCE = "nonce";
    /**
     * 当前时间戳 (unix time 单位秒)
     */
    private static final String H_TIMESTAMP = "timestamp";
    /**
     * 请求头签名信息
     */
    private static final String H_SIGN = "sign";

    /**
     * 设置http请求头
     * <p>
     * 增强 spectre api 加签功能
     *
     * @param request
     * @param headers
     */
    public static void boost(OpenApiRequest request, HttpHeaders headers) {
        String appKey = request.getAppKey();
        String appSecret = request.getAppSecret();
        String nonce = getNonce();
        String timestamp = getTimestamp();
        headers.add(H_APP_KEY, appKey);
        headers.add(H_NONCE, nonce);
        headers.add(H_TIMESTAMP, timestamp);
        headers.add(H_SIGN, getSign(appKey, nonce, timestamp, appSecret));
    }

    /**
     * 获取到签名值
     *
     * @param appKey    应用编码
     * @param nonce     随机数
     * @param timestamp unix time 时间搓 (秒)
     * @param appSecret 应用秘钥
     * @return
     */
    public static String getSign(String appKey, String nonce, String timestamp, String appSecret) {
        StringBuilder sb = new StringBuilder();
        sb.append(H_APP_KEY).append(appKey);
        sb.append(H_NONCE).append(nonce);
        sb.append(H_TIMESTAMP).append(timestamp);
        sb.append(appSecret);
        return DigestUtils.sha1Hex(sb.toString());
    }

    /**
     * 获取到随机数
     *
     * @return
     */
    private static String getNonce() {
        try {
            // 防止随机数重复
            double ranValue = ThreadLocalRandom.current().nextDouble();
            String nonce = System.currentTimeMillis() + "" + String.valueOf(ranValue).substring(2);
            if (nonce.length() > MAX_NONCE_LENGTH) {
                nonce = nonce.substring(0, MAX_NONCE_LENGTH);
            }
            return nonce;
        } catch (Exception e) {
            // ignore e
        }
        // 兜底返回
        return (ThreadLocalRandom.current().nextLong(DEFAULT_BOUND)) + "" + System.nanoTime();
    }

    /**
     * 获取到当前的时间搓
     *
     * @return
     */
    private static String getTimestamp() {
        return (System.currentTimeMillis() / 1000) + "";
    }
}
