package com.xhqb.spectre.openapi.send.order;

import com.xhqb.spectre.openapi.send.OpenApiRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@Data
public class TestStatQuery extends OpenApiRequest {

    /**
     * 统计时间(yyyy-MM-dd)
     */
    private String statDate;

    /**
     * 语音类型 market、notify等
     */
    private String smsTypeCode;

    /**
     * 渠道账号Id
     */
    private Integer channelAccountId;
}
