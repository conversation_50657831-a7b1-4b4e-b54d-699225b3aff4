package com.xhqb.spectre.openapi.service;

import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.CommonPager;
import com.xhqb.spectre.openapi.send.tpl.*;

import java.util.List;

/**
 * 模板服务
 *
 * <AUTHOR>
 * @date 2021/12/16
 */
public interface OpenApiTplService {

    /**
     * 模板列表查询
     *
     * @param tplListQuery
     * @return
     */
    ActionResult<CommonPager<TplVO>> queryTplList(TplListQuery tplListQuery);

    /**
     * 查询模板详情
     *
     * @param tplDetailQuery
     * @return
     */
    ActionResult<TplVO> queryTplInfo(TplDetailQuery tplDetailQuery);

    /**
     * 根据code查询模板详情
     *
     * @param tplCodeQuery
     * @return
     */
    ActionResult<TplVO> queryTplCode(TplCodeQuery tplCodeQuery);

    /**
     * 根据应用编码查询所有模板列表
     *
     * @param allTplQuery
     * @return
     */
    ActionResult<List<TplInfoVO>> queryAllTplByAppCode(AllTplQuery allTplQuery);
}
