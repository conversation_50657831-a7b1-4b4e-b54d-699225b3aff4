package com.xhqb.spectre.openapi.send;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhqb.spectre.openapi.utils.SignUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Set;

/**
 * open api http工厂
 *
 * <AUTHOR>
 * @date 2021/12/16
 */
@Component
public class SpectreOpenApiHttpFactory {

    @Resource
    private RestTemplate adminOpenApiRestTemplate;

    /**
     * get请求
     *
     * @param url
     * @param request
     * @param responseType
     * @param <T>
     * @return
     */
    public <T> ActionResult<T> get(String url, OpenApiRequest request, ParameterizedTypeReference<ActionResult<T>> responseType) {
        HttpHeaders headers = newHttpHeaders(request);
        HttpEntity<String> entity = new HttpEntity<>(null, headers);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);

        JSONObject jsonObject = request.toForm();

        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            builder.queryParam(entry.getKey(), entry.getValue());
        }
        return adminOpenApiRestTemplate.exchange(builder.build().encode().toString(),
                HttpMethod.GET,
                entity,
                responseType).getBody();

    }

    /**
     * post form请求
     *
     * @param url
     * @param request
     * @param responseType
     * @param <T>
     * @return
     */
    public <T> ActionResult<T> postForm(String url, OpenApiRequest request, ParameterizedTypeReference<ActionResult<T>> responseType) {
        HttpHeaders headers = newHttpHeaders(request);
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<JSONObject> entity = new HttpEntity<>(request.toForm(), headers);
        return adminOpenApiRestTemplate.exchange(url,
                HttpMethod.POST,
                entity,
                responseType).getBody();
    }

    /**
     * post json请求
     *
     * @param url
     * @param request
     * @param responseType
     * @param <T>
     * @return
     */
    public <T> ActionResult<T> postJSON(String url, OpenApiRequest request, ParameterizedTypeReference<ActionResult<T>> responseType) {
        HttpHeaders headers = newHttpHeaders(request);
        headers.setContentType(new MediaType("application", "json", StandardCharsets.UTF_8));
        HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(request), headers);
        return adminOpenApiRestTemplate.exchange(url,
                HttpMethod.POST,
                entity,
                responseType).getBody();
    }

    /**
     * 新建http请求头
     *
     * @param request
     * @return
     */
    private HttpHeaders newHttpHeaders(OpenApiRequest request) {
        HttpHeaders headers = new HttpHeaders();
        SignUtils.boost(request, headers);
        return headers;
    }
}
