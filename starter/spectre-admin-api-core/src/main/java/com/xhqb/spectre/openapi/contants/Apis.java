package com.xhqb.spectre.openapi.contants;

import com.xhqb.spectre.openapi.utils.EnvUtils;

/**
 * api定义
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
public interface Apis {

    boolean IS_LEGACY = EnvUtils.isLegacy();

    /**
     * api前缀
     */
    String PREFIX = IS_LEGACY ? "/open/api" : "/api/spectre/v3";


    /**
     * 模板接口
     */
    interface Tpl {
        /**
         * 查询模板列表
         */
        String QUERY_TPL_LIST = PREFIX + (IS_LEGACY ? "/tpl" : "/queryTpl");
        /**
         * 查询模板详情
         */
        String QUERY_TPL_DETAIL = PREFIX + (IS_LEGACY ? "/tpl/{id}" : "/queryTpl/{id}");
        /**
         * 查询模板详情code接口
         */
        String QUERY_TPL_CODE = PREFIX + (IS_LEGACY ? "/tpl/code" : "/queryTpl/code");

        /**
         * 根据应用编码查询所有模板信息
         */
        String QUERY_ALL_TPL_BY_APP_CODE = PREFIX + (IS_LEGACY ? "/tpl/all/{appCode}" : "/queryTpl/all/{appCode}");
    }

    /**
     * 发送记录接口
     */
    interface Order {
        /**
         * 查询发送记录列表
         */
        String QUERY_ORDER_LIST = PREFIX + (IS_LEGACY ? "/order" : "/queryOrder");
        /**
         * 查询订单状态
         */
        String QUERY_ORDER_STATUS = PREFIX + "/queryOrderStatus";
        /**
         * 根据mobile 查询短信记录
         */
        String QUERY_ORDER_BY_MOBILE = PREFIX + "/queryReissueOrder";
        /**
         * 查询短信测试统计
         */
        String QUERY_TEST_STATS = PREFIX + "/queryTestStats";
        /**
         * 查询渠道账号信息
         */
        String QUERY_CHANNEL_ACCOUNT = PREFIX + "/queryChannelAccount";

        /**
         * 根据ck合同号查询债转短信
         */
        String QUERY_DEBT_SMS = PREFIX + "/queryDebtSms";
    }

    /**
     * 发送公共配置接口
     */
    interface Common {
        /**
         * 项目用途接口
         */
        String QUERY_PROJECT_DESC = PREFIX + "/queryProjectDesc";
        /**
         * 项目营销场景接口
         */
        String QUERY_MARKET_SCENE = PREFIX + "/queryMarketScene";
    }
}
