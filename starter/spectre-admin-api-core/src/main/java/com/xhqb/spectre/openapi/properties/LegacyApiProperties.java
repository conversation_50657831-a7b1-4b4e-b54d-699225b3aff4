package com.xhqb.spectre.openapi.properties;

import com.xhqb.spectre.openapi.AdminOpenApiProperties;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import java.util.Objects;

/**
 * spectre admin的配置
 * <p>
 * 为了兼容已上线的项目
 * <p>
 * 新功能都将会迁移值spectre-api
 *
 * <AUTHOR>
 * @date 2022/3/4
 */
@Data
public class LegacyApiProperties implements AdminOpenApiProperties {

    /**
     * spectre admin 主机地址
     */
    @Value("${spectre.admin.openapi.spectreAdminHost:http://spectre-admin/spectre-admin}")
    private String spectreAdminHost;

    /**
     * 是否放开sdk msg 日志打印
     */
    @Value("${spectre.admin.openapi.debug:}")
    private Boolean debug;

    /**
     * appKey 若配置 请求参数则可以不传入appKey值
     */
    @Value("${spectre.admin.openapi.appKey:}")
    private String appKey;

    /**
     * appSecret 若配置 请求参数则可以不传入appSecret值
     */
    @Value("${spectre.admin.openapi.appSecret:}")
    private String appSecret;


    /**
     * 是否处于debug模式
     *
     * @return
     */
    @Override
    public boolean isDebug() {
        return Objects.equals(true, debug);
    }
}
