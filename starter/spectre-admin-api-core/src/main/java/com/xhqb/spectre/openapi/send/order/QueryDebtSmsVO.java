package com.xhqb.spectre.openapi.send.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class QueryDebtSmsVO implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * ck合同号
     */
    private String contractNo;

    /**
     * 1:一次债转，2:二次债转
     */
    private Integer debtLabel;

    /**
     * 手机号md5
     */
    private String mobileMd5;

    /**
     * 短信订单ID
     */
    private String smsOrderId;

    /**
     * 短信请求id
     */
    private String smsRequestId;

    /**
     * 渠道msgid
     */
    private String channelMsgId;

    /**
     * 渠道账号ID
     */
    private Integer channelAccountId;

    /**
     * 短信模板编码
     */
    private String tplCode;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 请求时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date requestTime;

    /**
     * 发送状态，0：成功，-1：未知，其余失败
     */
    private Integer sendStatus;

    /**
     * 调用渠道接口时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    /**
     * 回执状态，0：成功，-1：未知，其余失败
     */
    private Integer reportStatus;

    /**
     * 接收回执时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
