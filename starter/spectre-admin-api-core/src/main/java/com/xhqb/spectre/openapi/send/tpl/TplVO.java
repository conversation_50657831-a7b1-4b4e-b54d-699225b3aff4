package com.xhqb.spectre.openapi.send.tpl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TplVO implements Serializable {

    private static final long serialVersionUID = -644167102793176644L;

    private Integer id;

    private String code;

    private String smsTypeCode;

    private String title;

    private Integer signId;

    private String content;

    private String appCode;

    private String remark;

    private Integer status;

    private String createTime;

    private String creator;

    private String updateTime;

    private String updater;

}
