package com.xhqb.spectre.openapi.service.impl;

import com.xhqb.spectre.openapi.ex.OpenApiException;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.CommonPager;
import com.xhqb.spectre.openapi.send.order.*;
import com.xhqb.spectre.openapi.service.OpenApiOrderService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发送记录服务
 *
 * <AUTHOR>
 * @date 2022/2/9
 */
@Component
public class OpenApiOrderServiceImpl implements OpenApiOrderService {

    @Resource
    private QueryOrderListSender queryOrderListSender;
    @Resource
    private SmsOrderStatusSender smsOrderStatusSender;
    @Resource
    private SmsReissueOrderSender smsReissueOrderSender;
    @Resource
    private SmsTestStatsSender smsTestStatsSender;
    @Resource
    private SmsQueryChannelAccountSender smsQueryChannelAccountSender;

    @Resource
    private QueryDebtSmsSender queryDebtSmsSender;

    /**
     * 发送记录列表查询
     *
     * @param orderListQuery
     * @return
     */
    @Override
    public ActionResult<List<OrderVO>> queryOrderList(OrderListQuery orderListQuery) {
        try {
            return queryOrderListSender.send(orderListQuery);
        } catch (Exception e) {
            throw new OpenApiException(e);
        }
    }

    /**
     * 订单查询
     *
     * @param orderStatusQuery
     * @return
     */
    @Override
    public ActionResult<List<SmsOrderStatusVO>> queryOrderStatus(SmsOrderStatusQuery orderStatusQuery) {
        try {
            return smsOrderStatusSender.send(orderStatusQuery);
        } catch (Exception e) {
            throw new OpenApiException(e);
        }
    }

    @Override
    public ActionResult<CommonPager<SmsReissueOrderVO>> queryReissueOrderList(SmsReissueOrderQuery smsReissueOrderQuery) {
        try {
            return smsReissueOrderSender.send(smsReissueOrderQuery);
        } catch (Exception e) {
            throw new OpenApiException(e);
        }
    }

    @Override
    public ActionResult<TestStatVO> queryTestStats(TestStatQuery query) {
        try {
            return smsTestStatsSender.send(query);
        } catch (Exception e) {
            throw new OpenApiException(e);
        }
    }

    @Override
    public ActionResult<List<QueryChannelAccountVO>> queryChannelAccount(ChannelAccountQuery query) {
        try {
            return smsQueryChannelAccountSender.send(query);
        } catch (Exception e) {
            throw new OpenApiException(e);
        }
    }

    @Override
    public ActionResult<List<QueryDebtSmsVO>> queryDebtSms(DebtSmsQuery query) {
        try {
            return queryDebtSmsSender.send(query);
        } catch (Exception e) {
            throw new OpenApiException(e);
        }
    }
}
