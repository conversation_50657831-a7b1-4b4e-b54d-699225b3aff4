package com.xhqb.spectre.openapi.send.common;

import com.xhqb.spectre.openapi.contants.Apis;
import com.xhqb.spectre.openapi.send.AbstractOpenApiSender;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.SpectreOpenApiHttpFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 查询项目用途接口
 */
@Component
public class QueryProjectDescSender extends AbstractOpenApiSender<ProjectDescQuery, List<ProjectDescVO>> {

    @Resource
    private SpectreOpenApiHttpFactory spectreOpenApiHttpFactory;

    /**
     * 获取到接口地址
     *
     * @return
     */
    @Override
    protected String getApi() {
        return Apis.Common.QUERY_PROJECT_DESC;
    }

    /**
     * 响应解析响应消息
     *
     * @param apiUrl
     * @param request
     * @return
     */
    @Override
    protected ActionResult<List<ProjectDescVO>> doSend(String apiUrl, ProjectDescQuery request) {
        return spectreOpenApiHttpFactory.get(apiUrl, request, new ParameterizedTypeReference<ActionResult<List<ProjectDescVO>>>() {
        });
    }
}
