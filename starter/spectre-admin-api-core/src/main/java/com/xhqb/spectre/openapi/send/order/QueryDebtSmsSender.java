package com.xhqb.spectre.openapi.send.order;

import com.xhqb.spectre.openapi.contants.Apis;
import com.xhqb.spectre.openapi.send.AbstractOpenApiSender;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.SpectreOpenApiHttpFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class QueryDebtSmsSender  extends AbstractOpenApiSender<DebtSmsQuery, List<QueryDebtSmsVO>> {

    @Resource
    private SpectreOpenApiHttpFactory spectreOpenApiHttpFactory;

    @Override
    protected String getApi() {
        return Apis.Order.QUERY_DEBT_SMS;
    }

    @Override
    protected ActionResult<List<QueryDebtSmsVO>> doSend(String apiUrl, DebtSmsQuery request) {
        return spectreOpenApiHttpFactory.postJSON(apiUrl, request, new ParameterizedTypeReference<ActionResult<List<QueryDebtSmsVO>>>() {
        });
    }
}
