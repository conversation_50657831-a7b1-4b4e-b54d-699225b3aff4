package com.xhqb.spectre.openapi.send.tpl;

import com.xhqb.spectre.openapi.contants.Apis;
import com.xhqb.spectre.openapi.send.AbstractOpenApiSender;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.SpectreOpenApiHttpFactory;
import com.xhqb.spectre.openapi.utils.PathUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 根据应用编码查询所有模板信息
 *
 * <AUTHOR>
 * @date 2022/2/11
 */
@Component
public class QueryTplByAppCodeSender extends AbstractOpenApiSender<AllTplQuery, List<TplInfoVO>> {

    @Resource
    private SpectreOpenApiHttpFactory spectreOpenApiHttpFactory;

    /**
     * 获取到接口地址
     *
     * @return
     */
    @Override
    protected String getApi() {
        return Apis.Tpl.QUERY_ALL_TPL_BY_APP_CODE;
    }

    /**
     * 响应解析响应消息
     *
     * @param apiUrl
     * @param request
     * @return
     */
    @Override
    protected ActionResult<List<TplInfoVO>> doSend(String apiUrl, AllTplQuery request) {
        apiUrl = PathUtils.format(apiUrl, "appCode", request.getAppCode());
        return spectreOpenApiHttpFactory.get(apiUrl, request, new ParameterizedTypeReference<ActionResult<List<TplInfoVO>>>() {
        });
    }
}
