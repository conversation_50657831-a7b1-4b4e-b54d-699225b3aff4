package com.xhqb.spectre.openapi.send.order;

import com.xhqb.spectre.openapi.contants.Apis;
import com.xhqb.spectre.openapi.send.AbstractOpenApiSender;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.SpectreOpenApiHttpFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 查询短信测试统计
 *
 * <AUTHOR>
 * @date 2024/10/17
 */
@Component
public class SmsTestStatsSender extends AbstractOpenApiSender<TestStatQuery, TestStatVO> {

    @Resource
    private SpectreOpenApiHttpFactory spectreOpenApiHttpFactory;

    @Override
    protected String getApi() {
        return Apis.Order.QUERY_TEST_STATS;
    }

    @Override
    protected ActionResult<TestStatVO> doSend(String apiUrl, TestStatQuery request) {
        return spectreOpenApiHttpFactory.postJSON(apiUrl, request, new ParameterizedTypeReference<ActionResult<TestStatVO>>() {
        });
    }


}
