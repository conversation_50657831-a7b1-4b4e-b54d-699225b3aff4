package com.xhqb.spectre.openapi.send.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsReissueOrderVO {
    /**
     * 短信发送日期
     */
    private String sendTime;

    /**
     * 短信模版
     */
    private String tplCode;

    /**
     * 短信发送状态 0：成功，-1：未知，其余失败
     */
    private Integer sendStatus;

    /**
     * 短信发送失败原因
     */
    private String sendDesc;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 应用编码
     */
    private String appCode;

    /**
     * 应用名称
     */
    private String name;
}
