package com.xhqb.spectre.openapi.send;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 响应结果
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActionResult<T> implements Serializable {

    /**
     * 响应编码
     */
    private static final String SUCCESS_CODE = "200";

    /**
     * 响应码
     */
    private String code;
    /**
     * 响应消息
     */
    private String message;

    /**
     * 兼容(spectre-api接口响应)
     */
    private String msg;

    /**
     * 响应数据
     */
    private T data;

    public ActionResult(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    /**
     * 当前响应编码是否正常
     *
     * @return
     */
    public boolean isSuccess() {
        return StringUtils.equals(SUCCESS_CODE, code);
    }


}
