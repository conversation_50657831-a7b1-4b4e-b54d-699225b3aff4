package com.xhqb.spectre.openapi.service.impl;

import com.xhqb.spectre.openapi.ex.OpenApiException;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.CommonPager;
import com.xhqb.spectre.openapi.send.tpl.*;
import com.xhqb.spectre.openapi.service.OpenApiTplService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 模板服务
 *
 * <AUTHOR>
 * @date 2021/12/16
 */
@Component
public class OpenApiTplServiceImpl implements OpenApiTplService {

    @Resource
    private QueryTplListSender queryTplListSender;
    @Resource
    private QueryTplDetailSender queryTplDetailSender;
    @Resource
    private QueryTplCodeSender queryTplCodeSender;
    @Resource
    private QueryTplByAppCodeSender queryTplByAppCodeSender;

    /**
     * 模板列表查询
     *
     * @param tplListQuery
     * @return
     */
    @Override
    public ActionResult<CommonPager<TplVO>> queryTplList(TplListQuery tplListQuery) {
        try {
            return queryTplListSender.send(tplListQuery);
        } catch (Exception e) {
            throw new OpenApiException(e);
        }
    }

    /**
     * 查询模板详情
     *
     * @param tplDetailQuery
     * @return
     */
    @Override
    public ActionResult<TplVO> queryTplInfo(TplDetailQuery tplDetailQuery) {
        try {
            return queryTplDetailSender.send(tplDetailQuery);
        } catch (Exception e) {
            throw new OpenApiException(e);
        }
    }

    /**
     * 根据code查询模板详情
     *
     * @param tplCodeQuery
     * @return
     */
    @Override
    public ActionResult<TplVO> queryTplCode(TplCodeQuery tplCodeQuery) {
        try {
            return queryTplCodeSender.send(tplCodeQuery);
        } catch (Exception e) {
            throw new OpenApiException(e);
        }
    }

    /**
     * 根据应用编码查询所有模板列表
     *
     * @param allTplQuery
     * @return
     */
    @Override
    public ActionResult<List<TplInfoVO>> queryAllTplByAppCode(AllTplQuery allTplQuery) {
        try {
            return queryTplByAppCodeSender.send(allTplQuery);
        } catch (Exception e) {
            throw new OpenApiException(e);
        }
    }
}
