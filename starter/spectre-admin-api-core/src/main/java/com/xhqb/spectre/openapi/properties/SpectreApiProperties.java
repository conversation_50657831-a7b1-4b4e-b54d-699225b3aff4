package com.xhqb.spectre.openapi.properties;

import com.xhqb.spectre.openapi.AdminOpenApiProperties;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import java.util.Objects;

/**
 * spectreApi 兼容配置
 *
 * <AUTHOR>
 * @date 2022/3/4
 */
@Data
public class SpectreApiProperties implements AdminOpenApiProperties {

    /**
     * spectre api 主机地址
     */
    @Value("${spectre.sdk.sms.spectreApiHost:http://spectre-api/spectre-api}")
    private String spectreAdminHost;

    /**
     * 是否放开sdk msg 日志打印
     */
    @Value("${spectre.sdk.sms.debug:}")
    private Boolean debug;

    /**
     * appCode 若配置 请求参数则可以不传入appCode值
     */
    @Value("${spectre.sdk.sms.appCode:}")
    private String appKey;

    /**
     * appSecret 若配置 请求参数则可以不传入appSecret值
     */
    @Value("${spectre.sdk.sms.appSecret:}")
    private String appSecret;


    /**
     * 是否处于debug模式
     *
     * @return
     */
    @Override
    public boolean isDebug() {
        return Objects.equals(true, debug);
    }
}
