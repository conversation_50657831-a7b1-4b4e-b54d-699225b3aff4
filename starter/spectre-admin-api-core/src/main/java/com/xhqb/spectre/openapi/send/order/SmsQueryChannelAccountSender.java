package com.xhqb.spectre.openapi.send.order;

import com.xhqb.spectre.openapi.contants.Apis;
import com.xhqb.spectre.openapi.send.AbstractOpenApiSender;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.SpectreOpenApiHttpFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 查询渠道账号
 *
 * <AUTHOR>
 * @date 2024/10/17
 */
@Component
public class SmsQueryChannelAccountSender extends AbstractOpenApiSender<ChannelAccountQuery, List<QueryChannelAccountVO>> {

    @Resource
    private SpectreOpenApiHttpFactory spectreOpenApiHttpFactory;

    @Override
    protected String getApi() {
        return Apis.Order.QUERY_CHANNEL_ACCOUNT;
    }

    @Override
    protected ActionResult<List<QueryChannelAccountVO>> doSend(String apiUrl, ChannelAccountQuery request) {
        return spectreOpenApiHttpFactory.postJSON(apiUrl, request, new ParameterizedTypeReference<ActionResult<List<QueryChannelAccountVO>>>() {
        });
    }
}
