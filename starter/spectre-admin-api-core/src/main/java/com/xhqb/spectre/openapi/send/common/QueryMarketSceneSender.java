package com.xhqb.spectre.openapi.send.common;

import com.xhqb.spectre.openapi.contants.Apis;
import com.xhqb.spectre.openapi.send.AbstractOpenApiSender;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.SpectreOpenApiHttpFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 查询营销场景接口
 *
 */
@Component
public class QueryMarketSceneSender extends AbstractOpenApiSender<MarketSceneQuery, List<MarketSceneVO>> {

    @Resource
    private SpectreOpenApiHttpFactory spectreOpenApiHttpFactory;

    /**
     * 获取到接口地址
     *
     * @return
     */
    @Override
    protected String getApi() {
        return Apis.Common.QUERY_MARKET_SCENE;
    }

    /**
     * 响应解析响应消息
     *
     * @param apiUrl
     * @param request
     * @return
     */
    @Override
    protected ActionResult<List<MarketSceneVO>> doSend(String apiUrl, MarketSceneQuery request) {
        return spectreOpenApiHttpFactory.get(apiUrl, request, new ParameterizedTypeReference<ActionResult<List<MarketSceneVO>>>() {
        });
    }
}
