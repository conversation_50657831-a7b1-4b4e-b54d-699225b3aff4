package com.xhqb.spectre.openapi.send.tpl;

import com.xhqb.spectre.openapi.contants.Apis;
import com.xhqb.spectre.openapi.send.AbstractOpenApiSender;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.SpectreOpenApiHttpFactory;
import com.xhqb.spectre.openapi.utils.PathUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 模板详情查询
 *
 * <AUTHOR>
 * @date 2021/12/16
 */
@Component
public class QueryTplDetailSender extends AbstractOpenApiSender<TplDetailQuery, TplVO> {

    @Resource
    private SpectreOpenApiHttpFactory spectreOpenApiHttpFactory;

    /**
     * 获取到接口地址
     *
     * @return
     */
    @Override
    protected String getApi() {
        return Apis.Tpl.QUERY_TPL_DETAIL;
    }

    /**
     * 响应解析响应消息
     *
     * @param apiUrl
     * @param request
     * @return
     */
    @Override
    protected ActionResult<TplVO> doSend(String apiUrl, TplDetailQuery request) {
        apiUrl = PathUtils.format(apiUrl, "id", request.getId() + "");
        return spectreOpenApiHttpFactory.get(apiUrl, request,new ParameterizedTypeReference<ActionResult<TplVO>>() {
        });
    }


}
