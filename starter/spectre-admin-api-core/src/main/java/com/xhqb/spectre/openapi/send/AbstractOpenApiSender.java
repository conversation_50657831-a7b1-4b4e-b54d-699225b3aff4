package com.xhqb.spectre.openapi.send;

import com.alibaba.fastjson.JSON;
import com.xhqb.spectre.openapi.AdminOpenApiProperties;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;

import javax.annotation.Resource;

/**
 * 基础openapi 发送器
 *
 * <AUTHOR>
 * @date 2021/12/16
 */
public abstract class AbstractOpenApiSender<P extends OpenApiRequest, R> implements OpenApiSender {

    private Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 请求头
     */
    protected String CONTENT_TYPE = "Content-Type";
    /**
     * JSON请求头
     */
    protected String JSON_TYPE = "application/json;charset=UTF-8";

    @Resource
    private AdminOpenApiProperties adminOpenApiProperties;

    /**
     * 短信发送
     *
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    public ActionResult<R> send(OpenApiRequest request) throws Exception {
        // 尝试使用venus配置 填充appCode和appSecret
        this.populateIfNecessary(request);
        P context = (P) request;

        long start = System.currentTimeMillis();
        String api = adminOpenApiProperties.getSpectreAdminHost() + this.getApi();
        if (adminOpenApiProperties.isDebug()) {
            logger.info("spectre admin openapi 请求 = {}, api = {}", JSON.toJSONString(request), api);
        }
        // 发送请求
        ActionResult<R> result = doSend(api, context);
        if (adminOpenApiProperties.isDebug()) {
            logger.info("spectre admin openapi 响应 = {}, 请求参数 ={}, api = {}, cost = {}", JSON.toJSONString(result), JSON.toJSONString(request), api, (System.currentTimeMillis() - start));
        }
        return result;
    }


    /**
     * 填充 appKey和 appSecret
     *
     * @param request
     */
    private void populateIfNecessary(OpenApiRequest request) {
        String appKey = request.getAppKey();
        if (StringUtils.isBlank(appKey)) {
            request.setAppKey(adminOpenApiProperties.getAppKey());
        }

        String appSecret = request.getAppSecret();
        if (StringUtils.isBlank(appSecret)) {
            request.setAppSecret(adminOpenApiProperties.getAppSecret());
        }
    }

    /**
     * 绑定请求头
     *
     * @param headers
     */
    protected void bindHeaders(HttpHeaders headers) {
        // 设置请求头
        headers.add(CONTENT_TYPE, JSON_TYPE);
    }

    /**
     * 获取到接口地址
     *
     * @return
     */
    protected abstract String getApi();

    /**
     * 响应解析响应消息
     *
     * @param apiUrl
     * @param request
     * @return
     */
    protected abstract ActionResult<R> doSend(String apiUrl, P request);
}
