package com.xhqb.spectre.openapi.send.order;

import com.xhqb.spectre.openapi.contants.Apis;
import com.xhqb.spectre.openapi.send.AbstractOpenApiSender;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.CommonPager;
import com.xhqb.spectre.openapi.send.SpectreOpenApiHttpFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 根据mobile 查询短信记录
 *
 * <AUTHOR>
 * @date 2023/8/18
 */

@Component
public class SmsReissueOrderSender extends AbstractOpenApiSender<SmsReissueOrderQuery, CommonPager<SmsReissueOrderVO>> {

    @Resource
    private SpectreOpenApiHttpFactory spectreOpenApiHttpFactory;

    @Override
    protected String getApi() {
        return  Apis.Order.QUERY_ORDER_BY_MOBILE;
    }

    @Override
    protected ActionResult<CommonPager<SmsReissueOrderVO>> doSend(String apiUrl, SmsReissueOrderQuery request) {
        return spectreOpenApiHttpFactory.postJSON(apiUrl, request, new ParameterizedTypeReference<ActionResult<CommonPager<SmsReissueOrderVO>>>() {
        });
    }
}
