package com.xhqb.spectre.openapi.send.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/2/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmsOrderStatusVO implements Serializable {

    /**
     * requestId
     */
    private String requestId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 发送状态
     */
    private int sendStatus;

    /**
     * 回执状态
     */
    private int reportStatus;

    /**
     * 计费条数
     */
    private Integer billCount;

    /**
     * 回执错误描述
     */
    private String reportDesc;

    /**
     * 回执错误码
     */
    private Integer callCode;

}
