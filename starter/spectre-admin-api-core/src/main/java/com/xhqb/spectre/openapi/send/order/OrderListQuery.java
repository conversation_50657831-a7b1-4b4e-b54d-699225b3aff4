package com.xhqb.spectre.openapi.send.order;

import com.xhqb.spectre.openapi.send.OpenApiRequest;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 订单列表查询
 *
 * <AUTHOR>
 * @date 2022/2/9
 */
@Data
public class OrderListQuery extends OpenApiRequest {

    /**
     * 外部流水号
     */
    private List<String> requestIds;

    /**
     * 手机号码
     */
    private String mobile;


    /**
     * 添加requestId
     *
     * @param idList
     */
    public void add(String... idList) {
        if (Objects.isNull(requestIds)) {
            this.requestIds = new ArrayList<>();
        }
        for (String id : idList) {
            this.requestIds.add(id);
        }
    }
}
