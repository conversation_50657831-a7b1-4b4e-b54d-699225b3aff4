package com.xhqb.spectre.openapi.send.tpl;

import com.xhqb.spectre.openapi.send.OpenApiRequest;
import lombok.Data;

import java.io.Serializable;


@Data
public class TplListQuery extends OpenApiRequest implements Serializable {

    private static final long serialVersionUID = 6927472440831713630L;
    /**
     * 模板ID
     */
    private Integer id;
    /**
     * 模板编码
     */
    private String code;
    /**
     * 模板名称
     */
    private String title;
    /**
     * 模板状态 0：无效，1：有效
     */
    private Integer status;
    /**
     * 签名ID
     */
    private Integer signId;
    /**
     * 短信类型
     */
    private String smsTypeCode;
    /**
     * 模板内容
     */
    private String content;
    /**
     * 创建人
     */
    private String creator;

    /**
     * 当前页码
     */
    private Integer pageNum;
    /**
     * 一页显示的数量
     */
    private Integer pageSize;

}
