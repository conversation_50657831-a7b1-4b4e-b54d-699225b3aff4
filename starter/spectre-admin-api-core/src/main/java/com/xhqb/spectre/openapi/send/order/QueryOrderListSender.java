package com.xhqb.spectre.openapi.send.order;


import com.xhqb.spectre.openapi.contants.Apis;
import com.xhqb.spectre.openapi.send.AbstractOpenApiSender;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.SpectreOpenApiHttpFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 查询发送记录列表
 *
 * <AUTHOR>
 * @date 2022/2/9
 */
@Component
public class QueryOrderListSender extends AbstractOpenApiSender<OrderListQuery, List<OrderVO>> {

    @Resource
    private SpectreOpenApiHttpFactory spectreOpenApiHttpFactory;

    /**
     * 获取到接口地址
     *
     * @return
     */
    @Override
    protected String getApi() {
        return Apis.Order.QUERY_ORDER_LIST;
    }

    /**
     * 响应解析响应消息
     *
     * @param apiUrl
     * @param request
     * @return
     */
    @Override
    protected ActionResult<List<OrderVO>> doSend(String apiUrl, OrderListQuery request) {
        return spectreOpenApiHttpFactory.postJSON(apiUrl, request,new ParameterizedTypeReference<ActionResult<List<OrderVO>>>() {
        });
    }
}
