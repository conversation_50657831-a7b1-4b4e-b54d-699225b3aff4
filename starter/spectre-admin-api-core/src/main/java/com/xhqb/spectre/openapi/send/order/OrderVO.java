package com.xhqb.spectre.openapi.send.order;

import lombok.Data;

/**
 * 订单响应
 *
 * <AUTHOR>
 * @date 2022/2/9
 */
@Data
public class OrderVO {

    /**
     * 请求ID
     */
    String requestId;

    /**
     * 手机号
     */
    String mobile;

    /**
     * 订单号
     */
    Long orderId;
    /**
     * 发送状态，0：成功，-1：未知，其余失败
     */
    Integer sendStatus;

    /**
     * 回执状态，0：成功，-1：未知，其余失败
     */
    Integer reportStatus;

    /**
     * 短信内容
     */
    String content;

    /**
     * 计费条数
     */
    Integer billCount;
    /**
     * 短信回执状态码
     */
    String reportCode;
    /**
     * 回执状态描述
     */
    String reportDesc;

    /**
     * 发送时间
     */
    Integer sendTime;
    /**
     * 回执时间
     */
    Integer reportTime;

    /**
     * 发送编码
     */
    private String sendCode;

    /**
     * 发送描述
     */
    private String sendDesc;

    /**
     * 业务批次号
     */
    private String bizBatchId;
}
