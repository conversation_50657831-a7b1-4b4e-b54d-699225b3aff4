package com.xhqb.spectre.openapi.service.impl;

import com.xhqb.spectre.openapi.ex.OpenApiException;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.common.*;
import com.xhqb.spectre.openapi.service.OpenApiCommonService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class OpenApiCommonServiceImpl implements OpenApiCommonService {

    @Resource
    private QueryMarketSceneSender queryMarketSceneSender;

    @Resource
    private QueryProjectDescSender queryProjectDescSender;

    @Override
    public ActionResult<List<ProjectDescVO>> queryProjectDesc(ProjectDescQuery query) {
        try {
            return queryProjectDescSender.send(query);
        } catch (Exception e) {
            throw new OpenApiException(e);
        }
    }

    @Override
    public ActionResult<List<MarketSceneVO>> queryMarketScene(MarketSceneQuery query) {
        try {
            return queryMarketSceneSender.send(query);
        } catch (Exception e) {
            throw new OpenApiException(e);
        }
    }
}
