package com.xhqb.spectre.openapi.send.order;

import com.xhqb.spectre.openapi.send.OpenApiRequest;
import lombok.Data;

/**
 * 渠道账号查询
 *
 * <AUTHOR>
 * @date 2021/9/21
 */
@Data
public class ChannelAccountQuery extends OpenApiRequest {

    /**
     * 渠道id
     */
    private Integer id;

    /**
     * 渠道名称
     */
    private String channelName;
    /**
     * 短信类型编码
     */
    private String smsTypeCode;
    /**
     * 状态，0：无效，1：有效
     */
    private Integer status;
    /**
     * 账号名称
     */
    private String key;

    /**
     * 协议，1：http；2：cmpp
     */
    private Integer protocol;
    /**
     * 签名ID
     */
    private Integer signId;

    /**
     * 页数
     */
    private Integer pageNum;

    /**
     * 页大小
     */
    private Integer pageSize;
}
