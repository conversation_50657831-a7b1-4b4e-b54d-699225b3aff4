package com.xhqb.spectre.openapi;

/**
 * admin open api 配置信息
 * prefix = "spectre.admin.openapi"
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
public interface AdminOpenApiProperties {

    /**
     * 接口地址
     *
     * @return
     */
    String getSpectreAdminHost();

    /**
     * appKey
     *
     * @return
     */
    String getAppKey();

    /**
     * appSecret
     *
     * @return
     */
    String getAppSecret();

    /**
     * 是否处于debug模式
     *
     * @return
     */
    boolean isDebug();
}
