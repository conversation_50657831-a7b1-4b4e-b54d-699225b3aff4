package com.xhqb.spectre.openapi.service;

import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.CommonPager;
import com.xhqb.spectre.openapi.send.order.*;

import java.util.List;

/**
 * 发送记录服务
 *
 * <AUTHOR>
 * @date 2022/2/9
 */
public interface OpenApiOrderService {

    /**
     * 发送记录列表查询
     *
     * @param orderListQuery
     * @return
     */
    ActionResult<List<OrderVO>> queryOrderList(OrderListQuery orderListQuery);

    /**
     * 订单查询
     *
     * @param orderStatusQuery
     * @return
     */
    ActionResult<List<SmsOrderStatusVO>> queryOrderStatus(SmsOrderStatusQuery orderStatusQuery);


    ActionResult<CommonPager<SmsReissueOrderVO>> queryReissueOrderList(SmsReissueOrderQuery smsReissueOrderQuery);

    /**
     * 查询测试统计
     *
     * @param query
     * @return
     */
    ActionResult<TestStatVO> queryTestStats(TestStatQuery query);

    /**
     * 查询渠道账号信息
     *
     * @param query
     * @return
     */
    ActionResult<List<QueryChannelAccountVO>> queryChannelAccount(ChannelAccountQuery query);

    /**
     * 根据ck合同号查询债转短信
     */
    ActionResult<List<QueryDebtSmsVO>> queryDebtSms(DebtSmsQuery query);
}
