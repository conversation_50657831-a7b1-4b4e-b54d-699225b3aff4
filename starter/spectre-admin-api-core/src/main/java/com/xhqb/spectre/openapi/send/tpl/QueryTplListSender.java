package com.xhqb.spectre.openapi.send.tpl;

import com.xhqb.spectre.openapi.contants.Apis;
import com.xhqb.spectre.openapi.send.AbstractOpenApiSender;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.CommonPager;
import com.xhqb.spectre.openapi.send.SpectreOpenApiHttpFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 查询模板列表
 *
 * <AUTHOR>
 * @date 2021/12/16
 */
@Component
public class QueryTplListSender extends AbstractOpenApiSender<TplListQuery, CommonPager<TplVO>> {

    @Resource
    private SpectreOpenApiHttpFactory spectreOpenApiHttpFactory;

    /**
     * 获取到接口地址
     *
     * @return
     */
    @Override
    protected String getApi() {
        return Apis.Tpl.QUERY_TPL_LIST;
    }

    /**
     * 响应解析响应消息
     *
     * @param apiUrl
     * @param request
     * @return
     */
    @Override
    protected ActionResult<CommonPager<TplVO>> doSend(String apiUrl, TplListQuery request) {
        return spectreOpenApiHttpFactory.get(apiUrl, request, new ParameterizedTypeReference<ActionResult<CommonPager<TplVO>>>() {
        });
    }
}
