package com.xhqb.spectre.openapi.send.tpl;

import com.xhqb.spectre.openapi.contants.Apis;
import com.xhqb.spectre.openapi.send.AbstractOpenApiSender;
import com.xhqb.spectre.openapi.send.ActionResult;
import com.xhqb.spectre.openapi.send.SpectreOpenApiHttpFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 查询模版详情 code接口
 *
 * <AUTHOR>
 * @date 2022/2/9
 */
@Component
public class QueryTplCodeSender extends AbstractOpenApiSender<TplCodeQuery, TplVO> {

    @Resource
    private SpectreOpenApiHttpFactory spectreOpenApiHttpFactory;

    /**
     * 获取到接口地址
     *
     * @return
     */
    @Override
    protected String getApi() {
        return Apis.Tpl.QUERY_TPL_CODE;
    }

    /**
     * 响应解析响应消息
     *
     * @param apiUrl
     * @param request
     * @return
     */
    @Override
    protected ActionResult<TplVO> doSend(String apiUrl, TplCodeQuery request) {
        return spectreOpenApiHttpFactory.postJSON(apiUrl, request,new ParameterizedTypeReference<ActionResult<TplVO>>() {
        });
    }
}
