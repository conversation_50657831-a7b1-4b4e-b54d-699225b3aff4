package com.xhqb.spectre.openapi.send;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2021/12/16
 */
public abstract class OpenApiRequest {

    /**
     * 应用编码
     */
    private transient String appKey;

    /**
     * 应用秘钥
     */
    private transient String appSecret;

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    /**
     * 转成form对象
     *
     * @return
     */
    public JSONObject toForm() {
        JSONObject result = JSON.parseObject(JSON.toJSONString(this));
        result.remove("appKey");
        result.remove("appSecret");
        return result;
    }
}
