{"properties": [{"name": "spectre.admin.openapi.spectreAdminHost", "type": "java.lang.String", "description": "spectre admin 主机地址"}, {"name": "spectre.admin.openapi.debug", "type": "java.lang.Bo<PERSON>an", "description": "sdk msg 日志打印开关,true->开 false->关"}, {"name": "spectre.admin.openapi.appKey", "type": "java.lang.String", "description": "openapi应用key"}, {"name": "spectre.admin.openapi.appSecret", "type": "java.lang.String", "description": "openapi应用密钥"}]}