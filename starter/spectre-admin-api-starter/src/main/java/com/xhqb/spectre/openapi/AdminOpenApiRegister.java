package com.xhqb.spectre.openapi;

import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ClassPathBeanDefinitionScanner;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.type.AnnotationMetadata;

/**
 * sdk注入器
 *
 * <AUTHOR>
 * @date 2021/12/16
 */
public class AdminOpenApiRegister implements ImportBeanDefinitionRegistrar {
    @Override
    public void registerBeanDefinitions(AnnotationMetadata annotationMetadata, BeanDefinitionRegistry beanDefinitionRegistry) {
        new ClassPathBeanDefinitionScanner(beanDefinitionRegistry).scan(AdminOpenApiRegister.class.getPackage().getName());
    }
}
