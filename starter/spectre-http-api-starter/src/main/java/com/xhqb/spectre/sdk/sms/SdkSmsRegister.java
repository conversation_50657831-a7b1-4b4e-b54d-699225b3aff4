package com.xhqb.spectre.sdk.sms;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ClassPathBeanDefinitionScanner;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.util.ClassUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * sdk注入器
 *
 * <AUTHOR>
 * @date 2021/11/2
 */
@Slf4j
public class SdkSmsRegister implements ImportBeanDefinitionRegistrar {
    @Override
    public void registerBeanDefinitions(AnnotationMetadata annotationMetadata, BeanDefinitionRegistry beanDefinitionRegistry) {
        List<String> pkgList = new ArrayList<>();
        pkgList.add(SdkSmsRegister.class.getPackage().getName());
        // 是否存在admin自动注入配置
        boolean hasAdminAutoConfig = ClassUtils.isPresent("com.xhqb.spectre.openapi.AdminOpenApiAutoConfiguration", SdkSmsRegister.class.getClassLoader());
        if (!hasAdminAutoConfig) {
            pkgList.add("com.xhqb.spectre.openapi");
            log.info("add [com.xhqb.spectre.openapi] package for scanning.");
        }
        new ClassPathBeanDefinitionScanner(beanDefinitionRegistry).scan(pkgList.toArray(new String[0]));

    }
}
