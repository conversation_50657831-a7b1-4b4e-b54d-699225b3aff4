package com.xhqb.spectre.sdk.sms.service.impl;

import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2021/11/3
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = SdkSmsAutoConfiguration.class)
public class SdkSmsSenderServiceImplTest {

//    @Resource
//    private SdkSmsProperties sdkSmsProperties;
//    @Resource
//    private SdkSmsSenderService sdkSmsSenderService;

    @Before
    public void setup() {
//        sdkSmsProperties.setSpectreApiHost("https://spectre-test.xhdev.xyz/spectre-api");
//        sdkSmsProperties.setDebug(true);
//        sdkSmsProperties.setAppCode("oidc");
//        sdkSmsProperties.setAppSecret("oidc-secret");
    }

    @Test
    public void testSendVerify() {
//        GenCodeRequest codeRequest = new GenCodeRequest();
//        codeRequest.setTplCode("KF_YZM_200076");
//        codeRequest.setPhoneNumbers("13262296990");
//        SendResult<GenCodeResponse> result = sdkSmsSenderService.sendVerify(codeRequest);
//        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testCheckVerify() {
//        CheckCodeRequest checkCodeRequest = new CheckCodeRequest();
//        checkCodeRequest.setPhoneNumber("13262296990");
//        checkCodeRequest.setSmsVerifyCode("1987");
//        checkCodeRequest.setIdentificationCode("20211103000000018009");
//        checkCodeRequest.setRequestId("1103104316010673800156");
//
//        SendResult<CheckCodeResponse> result = sdkSmsSenderService.checkVerify(checkCodeRequest);
//        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testSendNotify() {
//        SdkSingleRequestDTO sdkSingleRequest = new SdkSingleRequestDTO();
//        sdkSingleRequest.setTplCode("kaifeng_001");
//        sdkSingleRequest.add(new SdkPhoneNumberDTO("15026552734", null));
//        SendResult<CompositeMessageResult<SingleResponse>> result = sdkSmsSenderService.sendNotify(sdkSingleRequest);
//        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testSendCollector() {
//        SdkSingleRequestDTO sdkSingleRequest = new SdkSingleRequestDTO();
//        sdkSingleRequest.setTplCode("test_collect");
//        sdkSingleRequest.add(new SdkPhoneNumberDTO("15026552734", null));
//        SendResult<CompositeMessageResult<SingleResponse>> result = sdkSmsSenderService.sendCollector(sdkSingleRequest);
//        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testSendMarket() {
//        SdkSingleRequestDTO sdkSingleRequest = new SdkSingleRequestDTO();
//        sdkSingleRequest.setTplCode("NOOP_TEST_001");
//        sdkSingleRequest.add(new SdkPhoneNumberDTO("15026552734", null));
//        SendResult<CompositeMessageResult<MarketResponse>> result = sdkSmsSenderService.sendMarket(sdkSingleRequest);
//        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testSendContent() {
//        SdkContentRequestDTO sdkContentRequest = new SdkContentRequestDTO();
//        sdkContentRequest.setContent("你好世界 from {{name}}");
//        sdkContentRequest.setSmsType(SmsType.MARKET);
//        sdkContentRequest.setSignCode("BGALL");
//        sdkContentRequest.add(new SdkContentItemDTO("13262296990", new SdkContentEntry("name", "旺财")));
//        sdkContentRequest.add(new SdkContentItemDTO("13262296991", new SdkContentEntry("name", "点我")));
//        sdkSmsSenderService.sendContent(sdkContentRequest);
    }

    @Test
    public void testQueryPhoneStatus() {
//        SdkQueryPhoneStatusDTO sdkQueryPhoneStatus = new SdkQueryPhoneStatusDTO();
//        sdkQueryPhoneStatus.add("13262296992");
//        sdkSmsSenderService.queryPhoneStatus(sdkQueryPhoneStatus);
    }

}
