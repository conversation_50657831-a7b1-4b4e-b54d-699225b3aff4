<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>kael-starter-app-parent</artifactId>
        <groupId>com.xhqb.kael</groupId>
        <version>5.5.0-SNAPSHOT</version>
    </parent>

    <groupId>com.xhqb.spectre</groupId>
    <artifactId>spectre-parent</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>

    <name>spectre</name>
    <description>new sms</description>

    <properties>
        <revision>5.10.0.RELEASE-SNAPSHOT</revision>
        <java.version>1.8</java.version>
        <fastjson.version>1.2.83</fastjson.version>
        <swagger2.version>2.9.2</swagger2.version>
        <swagger2-ui.version>2.9.2</swagger2-ui.version>
        <xhqb-swagger.version>1.0.3-RELEASE</xhqb-swagger.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <privilege.version>4.2.0-SNAPSHOT</privilege.version>
        <kael-mq.version>*******-RELEASE</kael-mq.version>
        <mockito.version>3.5.15</mockito.version>
        <kael-mybatis-plugin.version>1.0.11</kael-mybatis-plugin.version>
        <hutool.version>5.7.5</hutool.version>
        <lombok.version>1.18.6</lombok.version>
    </properties>

    <modules>
        <module>app</module>
        <module>common</module>
        <module>service</module>
        <module>dalgen</module>
        <module>app/http2gateway</module>
        <module>starter</module>
    </modules>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <deploy.env>local</deploy.env>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
        </profile>
        <profile>
            <id>test</id>
        </profile>
        <profile>
            <id>prod</id>
        </profile>

    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.xhqb.spectre</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xhqb</groupId>
                <artifactId>kael-mq-starter</artifactId>
                <version>${kael-mq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xhqb.kael.mybatis</groupId>
                <artifactId>kael-mybatis-plugin-starter</artifactId>
                <version>${kael-mybatis-plugin.version}</version>
            </dependency>
            <!--工具类相关-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!-- commons-lang3 相关依赖 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
    </dependencies>

    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <testSourceDirectory>src/test/java</testSourceDirectory>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
        </testResources>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.1.1</version>
                    <configuration>
                        <additionalOptions>-Xdoclint:none</additionalOptions>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>javadoc</goal>
                            </goals>
                            <phase>process-classes</phase>
                            <configuration>
                                <doclet>com.xhqb.swagger.doclet.SwaggerMoreDoclet</doclet>
                                <docletArtifacts>
                                    <docletArtifact>
                                        <groupId>com.xhqb.swagger</groupId>
                                        <artifactId>swagger-dubbo-javadoc</artifactId>
                                        <version>1.0.3-RELEASE</version>
                                    </docletArtifact>
                                    <docletArtifact>
                                        <groupId>com.xhqb.spectre</groupId>
                                        <artifactId>${project.artifactId}</artifactId>
                                        <version>${project.version}</version>
                                    </docletArtifact>
                                    <!-- 这里填API模块配置-->
                                </docletArtifacts>
                                <additionalOptions>-classDir ${project.build.outputDirectory}</additionalOptions>
                                <sourcepath>${project.build.sourceDirectory}</sourcepath>
                                <subpackages>com.xhqb.spectre</subpackages> <!--这里填需要扫描的包名-->
                                <useStandardDocletOptions>false</useStandardDocletOptions>
                            </configuration>
                        </execution>
                        <execution>
                            <id>attach-javadocs</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                            <configuration>
                                <additionalOptions>-Xdoclint:none</additionalOptions>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

    </build>

</project>