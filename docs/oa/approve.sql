create table if not exists t_tpl_oa_approve(
    `id`  bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `flow_type_id` varchar(32) NOT NULL DEFAULT '' COMMENT '流程类型ID',
    `sms_type_code` varchar(32) NOT NULL DEFAULT '' COMMENT '短信类型id',
    `flow_id` varchar(32) NOT NULL DEFAULT '' COMMENT 'oa流程 ID',
    `scene_code` varchar(32) NOT NULL DEFAULT '' COMMENT '场景编码',
    `title` varchar(32) NOT NULL DEFAULT '' COMMENT '标题',
    `original_content` varchar(2048) NOT NULL DEFAULT '' COMMENT '原始内容',
    `status` tinyint NOT NULL DEFAULT '-1' COMMENT '状态，0：初始化,1：审批中,2:拒绝, 3:通过,-1:其他',
    `user_id` varchar(32) NOT NULL DEFAULT '' COMMENT '申请人 userId',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `creator` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
    )engine=InnoDB comment='短信模板 OA 审批表';


create table if not exists t_tpl_content(
    `id`  bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `content_id` varchar(32) NOT NULL DEFAULT '' COMMENT '短信内容ID',
    `content` varchar(1024) NOT NULL DEFAULT '' COMMENT '内容',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
    )engine=InnoDB comment='短信内容';



create table if not exists t_tpl_approve_content(
    `id`  bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `flow_id` varchar(32) NOT NULL DEFAULT '' COMMENT ' 流程 ID',
    `content_id` varchar(32) NOT NULL DEFAULT '' COMMENT '短信内容ID',
    `tpl_code` varchar(128) NOT NULL DEFAULT '' COMMENT '模版编码',
    `sign_id` varchar(128) NOT NULL DEFAULT '' COMMENT '签名ID',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY`flow_id`(`flow_id`)
    )engine=InnoDB comment='关联表';


alter table t_tpl add column `approve_status` tinyint NOT NULL DEFAULT '-1' COMMENT '状态，0：初始化,1：审批中,2:拒绝, 3:通过,-1:其他';

alter table t_tpl_oa_approve add column `user_id` varchar(32) NOT NULL DEFAULT '' COMMENT '申请人 userId';
update t_tpl set approve_status = -1
