## 短信渠道路由优化方案

* 文档修订记录
<table>
    <tr>
        <th>版本号</th>
        <th>修订日期</th>
        <th>修订类型</th>
        <th>修订人</th>
        <th>摘要</th>
    </tr>
    <tr>
        <td>1.0.0</td>
        <td>2022-08-25</td>
        <td>新增</td>
        <td>huangyanxiong</td>
        <td>新建文档</td>
    </tr>
    <tr>
        <td>1.0.1</td>
        <td>2022-09-01</td>
        <td>更新</td>
        <td>huangyanxiong</td>
        <td>更新方案</td>
    </tr>
</table>

### 1. 背景
当前的短信渠道路由方案，采用的是加权随机算法，主要依赖于管理后台配置的渠道权重。该方案有一个缺陷，当某个渠道出现异常，成功率在短时间内快速下降时，系统是无法感知到的，不能及时调整路由选择，会影响最终的成功率。因此需要在该方案的基础上进行优化，将渠道实时成功率因素也纳入进来。

### 2. 方案概述
1. 新增路由计算服务，定时统计最近一段时间内（例如最近5分钟）渠道的成功率，计算出异常渠道数据，提供http接口给Dispatcher服务调用；
2. 修改Dispatcher服务，新增定时任务查询异常渠道数据，与渠道固定权重相结合，得到最终的渠道权重，最后采用加权随机算法选择渠道。

### 3. 详细设计
#### 3.1. 路由计算服务
##### 3.1.1. 异常渠道统计
1. 定时任务扫描短信订单表（t_sms_order），获取最近5分钟（从前6分钟至前1分钟，时间支持可配置）的订单统计数据
2. 统计维度
   * 渠道的成功率
   * 渠道 + 运营商的成功率
3. 维度最低样本数（统计数据大于等于该样本数时，才会去计算成功率，否则认为该渠道正常）
   * 渠道：20
   * 渠道 + 运营商：7
4. 根据短信类型和渠道成功率计算渠道比例系数，主要根据以往订单的经验值得出，取值逻辑详见以下表格
   
   * 营销

      |  成功率   | &gt;= 60%  | &gt;= 50% | &gt;= 40% | &gt;= 30% | &gt;= 20% | &gt; 0% | 0% |
      | ------- | ------- | -------- | ------| ------| -----| ---------|---------- |
      | 比例系数  | 1   | 0.8  | 0.6 | 0.4 | 0.2 | 0.1 | 0 |

   * 验证码
   
      |  成功率   | &gt;= 95% | &gt;= 90% | &gt;= 80% | &gt;= 70% | &gt;= 50% | &gt; 0% | 0% |
      | ------- | ------- | -------- | ------| ------| -----| ---------|---------- |
      | 比例系数  | 1   | 0.8  | 0.6 | 0.4 | 0.2 | 0.1 | 0 |
   
   * 通知
   
      |  成功率   | &gt;= 95% | &gt;= 90% | &gt;= 80% | &gt;= 70% | &gt;= 50% | &gt; 0% | 0% |
      | ------- | ------- | -------- | ------| ------| -----| ---------|---------- |
      | 比例系数  | 1   | 0.8  | 0.6 | 0.4 | 0.2 | 0.1 | 0 |
   
   * 还款通知
   
      |  成功率   | &gt;= 90% | &gt;= 80% | &gt;= 70% | &gt;= 60% | &gt;= 40% | &gt; 0% | 0% |
      | ------- | ------- | -------- | ------| -----|------| ---------|---------- |
      | 比例系数  | 1   | 0.8  | 0.6 | 0.4 | 0.2 | 0.1 | 0 |
   
   * 轻催
   
      |  成功率   | &gt;= 80% | &gt;= 70% | &gt;= 60% | &gt;= 50% | &gt;= 30% | &gt; 0% | 0% |
      | ------- | ------- | -------- | ------| -----|------| ---------|---------- |
      | 比例系数  | 1   | 0.8  | 0.6 | 0.4 | 0.2 | 0.1 | 0 |
   
   * 重催
   
      |  成功率   | &gt;= 80% | &gt;= 70% | &gt;= 60% | &gt;= 50% | &gt;= 30% | &gt; 0% | 0% |
      | ------- | ------- | -------- | ------| -----|------| ---------|---------- |
      | 比例系数  | 1   | 0.8  | 0.6 | 0.4 | 0.2 | 0.1 | 0 |
   
5. 将比例系数 < 1的渠道认定为异常渠道，将数据存储到内存中，使用Guava cache组件缓存（例如2分钟），提供给查询接口使用
6. 严重异常渠道数据
   * 渠道比例系数 <= 0.1时，认为该渠道严重异常，将该渠道数据额外存储一份，缓存较长时间（例如10分钟）
   * 当某个渠道在异常渠道列表、严重异常渠道列表中均存在时，优先读取严重异常渠道数据
7. 严重异常渠道恢复。当满足以下条件时，将严重异常渠道恢复正常，即将该渠道从严重渠道列表中剔除
   * 最低样本数：渠道维度：7，渠道 + 运营商维度：3
   * 成功率条件：

     | 短信类型  | 验证码 | 通知 | 营销 | 还款通知 | 轻催 | 重催 |
     | ------- | ------- | -------- | ------| -----|------| ---------|
     |  成功率  | &gt;= 80% | &gt;= 80% | &gt;= 50% | &gt;= 70% | &gt;= 60% | &gt;= 60% |

##### 3.1.2. 异常渠道数据查询接口
> 此接口仅在k8s集群内部使用，不支持nginx域名访问方式
1. 请求路径：http://spectre-route-calculate/spectre-route-calculate/queryRecentBadChannel
2. 请求参数：无
3. 响应参数：

   | 名称    | 类型    | 必填 | 备注                                 |
   | ------- | ------- | -------- | ------------------------------------ |
   | code    | int     | Y        | 状态码，0表示成功，其余失败                |
   | msg     | string  | Y        | 状态码描述                           |
   | data    | object  | N        | 处理结果，处理失败时该值为空       |
   | ├─channelList | list | Y | 渠道维度异常数据   |
   | ├─channelIspList | list | Y | 渠道 + 运营商维度异常数据   |

   channel结构：
   
   | 名称    | 类型    | 必填 | 备注                                 |
   | ------- | ------- | -------- | ------------------------------------ |
   | channelAccountId    | int     | Y        | 渠道账号ID                |
   | scaleFactor  | int | Y | 比例系数，单位：百分位 |

   channelIsp结构：   

   | 名称    | 类型    | 必填 | 备注                                 |
   | ------- | ------- | -------- | ------------------------------------ |
   | channelAccountId    | int     | Y        | 渠道账号ID                |
   | isp    | string     | Y        | 运营商                |
   | scaleFactor  | int | Y | 比例系数，单位：百分位 |

#### 3.2. Dispatcher服务改造
##### 3.2.1 查询渠道异常数据
新增定时任务，默认每30秒执行一次，调用渠道路由计算服务接口，获取到异常渠道数据，缓存到内存中，过期时间1分钟

##### 3.2.2. 权重计算
获取到渠道列表后，先计算出最终权重，再做加权随机排序。最终权重计算方式如下：
> 最终权重 = 固定权重 * 比例系数

##### 3.2.3. 渠道选择的流程图
![image](channel_select.jpg)

