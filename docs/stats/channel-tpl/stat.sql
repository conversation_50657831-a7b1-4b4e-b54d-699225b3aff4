create table if not exists `t_sms_send_stat`(
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `stat_date` varchar(10) NOT NULL COMMENT '统计日期',
    `app_code` varchar(32) NOT NULL DEFAULT '' COMMENT '应用Id',
    `tpl_code` varchar(128) NOT NULL DEFAULT '' COMMENT '短信模板id',
    `isp_code` varchar(32) NOT NULL DEFAULT '' COMMENT 'isp',
    `sms_type_code` varchar(32) NOT NULL DEFAULT '' COMMENT '短信类型id',
    `province_short_name` varchar(64) NOT NULL DEFAULT '' COMMENT '省',
    `channel_code` varchar(32) NOT NULL DEFAULT '' COMMENT '渠道code',
    `send_count` int NOT NULL DEFAULT '0' COMMENT '发送量',
    `reach_count` int NOT NULL DEFAULT '0' COMMENT '触达量',
    `first_reach_count` int NOT NULL DEFAULT '0' COMMENT '首发触达量',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE (`stat_date`, `app_code`, `tpl_code`, `isp_code`, `sms_type_code`,`province_short_name`,`channel_code`)
    )engine=InnoDB comment='短信发送统计表';