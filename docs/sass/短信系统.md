# 短信系统

<style>
  h1 {
    counter-reset: h2
  }
  h2 {
    counter-reset: h3
  }
  h3 {
    counter-reset: h4
  }
  h2:before {
    counter-increment: h2;
    content: counter(h2) ". "
  }
  h3:before {
    counter-increment: h3;
    content: counter(h2) "." counter(h3) ". "
  }
  h4:before {
    counter-increment: h4;
    content: counter(h2) "." counter(h3) "." counter(h4) ". "
  }
</style>

[TOC]

</br>

## 模块分解

+ 短信平台官网
+ 短信企业后台
+ 短信企业管理后台
+ 短信运营管理后台
+ 短信应用系统

</br>

## 功能需求分析

### 短信平台官网

#### 功能模块

+ 官网
+ 产品文档

</br>

### 短信企业后台

#### 功能模块

+ 账户管理
  + 账户安全
    + 修改密码
    + etc
  + 账户资料
  + 账户充值
  + etc
+ 权限管理
  + 子账户授权管理
  + IP白名单
+ 产品购买
+ 消费记录

</br>

### 短信管理后台（企业）

#### 功能模块

+ 短信签名
  + 增删改查
  + 签名审核
+ 短信模板
  + 标签管理
  + 增删模板
  + 模板审核
+ 业务统计
  + 发送量统计
  + 查询发送记录
  + 费用统计
+ 日志管理
  + 开启日志分析
  + 导出日志
+ 国内短信设置
  + 设置回执消息接收模式
  + 设置短信发送总量预警
  + 设置短信发送频率
  + 黑名单
  + 设置联系人
+ 短链服务

</br>

### 短信管理后台（运营）

#### 功能模块

+ 企业信息
+ 审核管理后台

</br>

### 短信应用系统

#### 接入形式

+ 控制台
+ API
  + HTTP
  + CMPP
+ SDK

#### 功能模块

+ 发送测试短信
+ 短信单独发送
+ 短信批量发送
