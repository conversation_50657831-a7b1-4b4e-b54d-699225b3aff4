#  <p style="text-align: center;">短信云服务设计文档</p>
  
  
<div STYLE="page-break-after: always;"></div>
  
```text
  主题：  短信云服务
  作者：  王建政
  版本：  1.0.0
  创建日期：  2022年5月25日
  修改日期：  2022年6月13日
```
  
<br>
  
<style>
  h1 {
    counter-reset: h2
  }
  h2 {
    counter-reset: h3
  }
  h3 {
    counter-reset: h4
  }
  h4 {
    counter-reset: h5
  }
  h2:before {
    counter-increment: h2;
    content: counter(h2) ". "
  }
  h3:before {
    counter-increment: h3;
    content: counter(h2) "." counter(h3) ". "
  }
  h4:before {
    counter-increment: h4;
    content: counter(h2) "." counter(h3) "." counter(h4) ". "
  }
  h5:before {
    counter-increment: h5;
    content: counter(h2) "." counter(h3) "." counter(h4) "." counter(h5) ". "
  }
</style>
  
  
- [短信云服务设计文档](#p-styletext-align-center短信云服务设计文档p )
  - [架构设计](#架构设计 )
    - [总体业务架构设计](#总体业务架构设计 )
    - [总体应用架构设计](#总体应用架构设计 )
  - [模块分解](#模块分解 )
  - [功能需求分析](#功能需求分析 )
    - [短信平台官网](#短信平台官网 )
    - [商户中心](#商户中心 )
    - [短信管理后台（商户）](#短信管理后台商户 )
    - [短信应用（发送）系统](#短信应用发送系统 )
      - [接入方式](#接入方式 )
    - [短信管理后台（运营）](#短信管理后台运营 )
  - [短信平台官网系统](#短信平台官网系统 )
  - [商户中心系统](#商户中心系统 )
    - [账户登录](#账户登录 )
      - [账户登录页面设计](#账户登录页面设计 )
    - [账户管理](#账户管理 )
      - [账户安全](#账户安全 )
        - [账户修改密码页面设计](#账户修改密码页面设计 )
      - [账户资料](#账户资料 )
        - [账户资料基本信息页面设计](#账户资料基本信息页面设计 )
        - [账户身份认证页面设计](#账户身份认证页面设计 )
      - [账户充值](#账户充值 )
        - [账户充值页面设计](#账户充值页面设计 )
    - [子账户授权](#子账户授权 )
      - [子账户授权管理页面设计](#子账户授权管理页面设计 )
    - [IP白名单](#ip白名单 )
      - [IP白名单管理主页面设计](#ip白名单管理主页面设计 )
      - [IP白名单添加分组页面设计](#ip白名单添加分组页面设计 )
    - [产品购买](#产品购买 )
      - [短信服务购买页面设计](#短信服务购买页面设计 )
  - [短信管理后台系统（商户）](#短信管理后台系统商户 )
    - [短信签名](#短信签名 )
      - [签名管理页面设计](#签名管理页面设计 )
      - [添加签名页面设计](#添加签名页面设计 )
    - [短信模板](#短信模板 )
      - [模板管理页面设计](#模板管理页面设计 )
      - [添加模板页面设计](#添加模板页面设计 )
    - [业务统计](#业务统计 )
      - [发送量统计页面设计](#发送量统计页面设计 )
    - [日志管理](#日志管理 )
    - [国内短信设置](#国内短信设置 )
      - [发送量设置页面设计](#发送量设置页面设计 )
      - [发送频率设置页面设计](#发送频率设置页面设计 )
      - [添加联系人页面设计](#添加联系人页面设计 )
      - [黑名单设置页面设计](#黑名单设置页面设计 )
    - [短链管理](#短链管理 )
  - [短信管理后台系统（运营）](#短信管理后台系统运营 )
  - [短信应用系统](#短信应用系统 )
    - [控制台发送流程设计](#控制台发送流程设计 )
    - [控制台添加发送任务页面设计](#控制台添加发送任务页面设计 )
    - [短信发送服务系统](#短信发送服务系统 )
      - [API短信发送流程图](#api短信发送流程图 )
      - [CMPP短信发送流程图](#cmpp短信发送流程图 )
    - [短信发送错误码列表](#短信发送错误码列表 )
  
</br>
  
##  架构设计
  
  
###  总体业务架构设计
  
  

```
Error: mermaid CLI is required to be installed.
Check https://github.com/mermaid-js/mermaid-cli for more information.



```  

  
<br>
  
###  总体应用架构设计
  
  

```
Error: mermaid CLI is required to be installed.
Check https://github.com/mermaid-js/mermaid-cli for more information.



```  

  
<br>
  
##  模块分解
  
  
+ 短信平台官网
  + 平台服务展示页。包括产品简介、帮助文档、公告、商户注册与登录引导页面等。
+ 商户中心
  + 商户对账号操作相关的一系列web页面功能。包括账户注册与登录、账户管理、产品购买、消费记录查询等。
+ 短信管理后台（商户）
  + 商户管理短信的后台web系统。包括短信签名管理、短信模版管理、短信业务统计、短信日志管理、国内短信设置、短链管理等。
+ 短信应用（发送）系统
  + 短信发送系统。包括测试短信发送、短信单独发送、短信批量发送等。支持控制台、API/SDK等接入方式。
+ 短信管理后台（运营）
  + 运营方短信管理后台系统。包括管理员账户权限管理、商户信息、产品交易信息、短信发送记录、审核管理后台等。
+ 大数据平台
  + 包括商户行为数据采集与分析、通道商反馈数据采集与分析等。
  
</br>
  
##  功能需求分析
  
  
###  短信平台官网
  
  
+ 官网
+ 产品文档
  
</br>
  
###  商户中心
  
  
+ 账户注册
+ 账户登录
+ 账户管理
  + 账户安全
    + 修改密码
    + 绑定手机
    + ……
  + 账户资料
    + 实名认证
    + ……
  + 账户充值
  + 子账户授权
  + IP白名单
+ 产品购买
  + 短信服务
+ 消费记录
  
</br>
  
###  短信管理后台（商户）
  
  
+ 短信签名
  + 签名管理
    + 增删改查
  + 签名提交审核
+ 短信模板
  + 模板标签管理
  + 增删模板
  + 模板提交审核
+ 业务统计
  + 发送量统计
  + 查询发送记录
  + 费用统计
+ 日志管理
  + 开启日志分析
  + 导出日志
+ 短信发送设置
  + 设置（国内/国际）短信
  + 设置回执消息接收模式
  + 设置短信发送总量预警
  + 设置短信发送频率
  + 黑名单
  + 设置联系人
+ 短链管理
  + 购买短链增值服务
  + 创建短链
  + 短链提交审核
  
</br>
  
###  短信应用（发送）系统
  
  
+ 发送测试短信
+ 短信单独发送
+ 短信批量发送
  
####  接入方式
  
  
+ 控制台
+ API
  + HTTP
+ SDK
  + HTTP
  + CMPP
  
</br>
  
###  短信管理后台（运营）
  
  
+ 管理员账户权限管理
+ 商户（企业）信息
+ 产品交易信息
+ 短信发送记录
+ 审核管理后台
+ 短信发送设置
  + （查看/修改）商户短信发送设置
  + （查看/修改）商户黑名单
  + （查看/修改）商户联系人
+ 通道配置
  + 通道分组
    + 白名单独立分组设置
    + 按运营商分组设置
    + 按地区分组设置
  + 通道权重策略配置
    + 人工、智能
  + 对特定商户的定制化通道配置
  
</br>
  
##  短信平台官网系统
  
  
（略）
  
<br>
  
##  商户中心系统
  
  
###  账户登录
  
  
####  账户登录页面设计
  
  
![avatar](resources/账户登录页面.png )
  
+ 输入账号和密码登录系统
  
<br>
  
###  账户管理
  
  
####  账户安全
  
  
#####  账户修改密码页面设计
  
  
![avatar](resources/账户修改密码页面.png )
  
+ 输入新修改密码和手机验证码
  
<br>
  
####  账户资料
  
  
#####  账户资料基本信息页面设计
  
  
![avatar](resources/账户资料基本信息页面.png )
  
+ 显示账户基本信息，如账号名称、账号ID等。
  
<br>
  
#####  账户身份认证页面设计
  
  
![avatar](resources/账户身份认证页面.png )
  
+ 输入企业名称、企业社会统一信用代码、企业法人信息、营业执照等，完成企业账户身份资料认证。
  
<br>
  
####  账户充值
  
  
#####  账户充值页面设计
  
  
![avatar](resources/账户充值页面.png )
  
+ 可以通过二维码支付、企业网银、线下汇款等方式进行充值。
  
<br>
  
###  子账户授权
  
  
####  子账户授权管理页面设计
  
  
![avatar](resources/子账户授权管理页面.png )
  
+ 主账号登录，可以进行创建子账号、授予或修改子账号等权限管理操作。
  
<br>
  
###  IP白名单
  
  
####  IP白名单管理主页面设计
  
  
![avatar](resources/ip白名单管理主页面.png )
  
+ 查看当前有权限管理的IP白名单分组。
  
<br>
  
####  IP白名单添加分组页面设计
  
  
![avatar](resources/ip白名单添加分组页面.png )
  
+ 添加IP白名单分组与分组下的IP。
  
<br>
  
###  产品购买
  
  
####  短信服务购买页面设计
  
  
![avatar](resources/短信服务购买页面.png )
  
+ 短信服务产品购买，可以定制化短信套餐包配置。
  
<br>
  
##  短信管理后台系统（商户）
  
  
###  短信签名
  
  
####  签名管理页面设计
  
  
![avatar](resources/签名管理页面.png )
  
+ 在签名管理页面创建或编辑商户相关的短信签名。
  
<br>
  
####  添加签名页面设计
  
  
 ![avatar](resources/添加签名页面.png )
  
+ 添加签名以及相关材料，提交审核。
  
<br>
  
###  短信模板
  
  
####  模板管理页面设计
  
  
![avatar](resources/模板管理页面.png )
  
+ 在模板管理页面创建或编辑商户相关的短信模板。
  
<br>
  
####  添加模板页面设计
  
  
![avatar](resources/添加模板页面.png )
  
+ 添加模板并提交审核。
  
<br>
  
###  业务统计
  
  
####  发送量统计页面设计
  
  
![avatar](resources/发送量统计页面.png )
  
+ 显示发送量统计等数据信息。
  
<br>
  
###  日志管理
  
  
（暂略）
  
<br>
  
###  国内短信设置
  
  
####  发送量设置页面设计
  
  
![avatar](resources/发送量设置页面.png )
  
+ 设置发送总量阈值、套餐包预警值阈值等。
  
<br>
  
####  发送频率设置页面设计
  
  
![avatar](resources/发送频率设置页面.png )
  
+ 设置验证码发送频率等。
  
<br>
  
####  添加联系人页面设计
  
  
![avatar](resources/添加联系人页面.png )
  
+ 设置接受预警与审核结果短信通知的联系人信息。
  
<br>
  
####  黑名单设置页面设计
  
  
![avatar](resources/黑名单设置页面.png )
  
+ 显示与设置从运营商反馈以及商户设置的黑名单。
  
<br>
  
###  短链管理
  
  
+ 参考现有的短链系统设计。
  
<br>
  
##  短信管理后台系统（运营）
  
  
+ 参考现有的OPS系统内的短信模块进行设计
  
<br>
  
##  短信应用系统
  
  
###  控制台发送流程设计
  
  

```
Error: mermaid CLI is required to be installed.
Check https://github.com/mermaid-js/mermaid-cli for more information.



```  

  
</br>
  
###  控制台添加发送任务页面设计
  
  
![avatar](resources/控制台添加发送任务页面.png )
  
+ 控制台配置发送任务
  
</br>
  
###  短信发送服务系统
  
  
####  API短信发送流程图
  
  

```
Error: mermaid CLI is required to be installed.
Check https://github.com/mermaid-js/mermaid-cli for more information.



```  

  
<br>
  
####  CMPP短信发送流程图
  
  

```
Error: mermaid CLI is required to be installed.
Check https://github.com/mermaid-js/mermaid-cli for more information.



```  

  
<br>
  
> API接口参考现有的API Server进行设计
  
<br>
  
###  短信发送错误码列表
  
  
| 错误码 | 错误码描述 |
| ----- | -------- |
| 0 | 发送成功 |
| 1 | 消息结构错 |
| 2 | 非法源地址 |
| 3 | 认证错 |
| 4 | 版本太高 |
| 5 | 资费代码错 |
| 6 | 超过最大信息长 |
| 7 | 业务代码错 |
| 8 | 流量控制错 |
| 9 | 本网关不负责此计费号码 |
| 10 | Src_ID错 |
| 11 | Msg_src错 |
| 12 | 计费地址错 |
| 13 | 目的地址错 |
| 15 | 超过最大连接数 |
| 20 | 信息格式错 |
| 21 | 签名错误 |
| 22 | 短信超长 |
| 23 | 没有Submit响应 |
| 24 | 手机号不正确 |
| 25 | 不支持群发 |
| 26 | 通道和扩展必须为数字 |
| 27 | 用户不存在 |
| 28 | 账号已禁用 |
| 29 | 余额不足 |
| 30 | 该用户此时间段禁止提交 |
| 9999 | cmpp所有渠道不可用 |
  