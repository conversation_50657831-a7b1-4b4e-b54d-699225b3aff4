【短信平台】短信文案测试工具

## 一、sql

### 1.1 白名单

```sql
CREATE TABLE `t_test_white` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `mobile_enc` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '手机号加密字段',
    `mobile_hash` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '手机号加密hash',
    `owner` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '手机号拥有者',
    `brand` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '手机品牌',
    `os_version` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '系统版本',
    `remark` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '备注	',
    `status` TINYINT(4) DEFAULT 0 COMMENT '状态（0：停用 1：启用）',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',
    `updater` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人',
    `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0:未删除 1:删除',
     PRIMARY KEY (`id`),
     KEY `idx_mobile_enc` (`mobile_enc`)
) ENGINE=InnoDB COMMENT='测试白名单表';
```

### 1.2 文案测试主表

```sql
CREATE TABLE `t_test_content_task` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_id` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '测试任务ID',
    `name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '测试名称',
    `content` VARCHAR(2047)  NOT NULL DEFAULT '' COMMENT '测试短信文案',
    `brand_config` VARCHAR(512)  NOT NULL DEFAULT '' COMMENT '手机号和品牌配置',
    `remark` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '备注	',
    `task_status` int unsigned NOT NULL DEFAULT 0 COMMENT '状态（0：初始华 1：执行中 2：成功 3：失败 4：取消）',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',
    `updater` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人',
     PRIMARY KEY (`id`),
     KEY `idx_task_id` (`task_id`),
     KEY `idx_update_time` (`update_timec`)
) ENGINE=InnoDB COMMENT='文案测试主表';
```

### 1.3 文案测试子表

```sql
CREATE TABLE `t_test_content_task_record` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_id` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '测试任务ID',
    `content` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '测试短信文案',
    `brand` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '手机品牌',
    `mobile_enc` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '手机号加密字段',
    `mobile_hash` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '手机号加密hash',
    `sms_sign_id` int unsigned NOT NULL DEFAULT 0 COMMENT '短信签名ID',
    `sms_tpl_code` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '测试模版',
    `request_id` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '请求ID',
    `send_status` int NOT NULL DEFAULT '-1' COMMENT '发送状态，0：成功，-1：未知，其余失败',
    `report_status` int NOT NULL DEFAULT '-1' COMMENT '回执状态，0：成功，-1：未知，其余失败',
    `app_report_status` int unsigned NOT NULL DEFAULT 0 COMMENT 'app上报状态（0：初始华 1：成功 2：被拦截）',
    `app_report_time` int unsigned NOT NULL DEFAULT '0' COMMENT 'app上报时间',
  	`app_report_extend` text COMMENT 'app上报扩展字段	，json格式',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`),
     KEY `idx_request_id_mobile_enc` (`request_id`,`mobile_enc`),
     KEY `idx_create_time` (`create_time`),
     KEY `idx_task_id` (`task_id`),
     KEY `idx_update_time` (`update_timec`)
) ENGINE=InnoDB COMMENT='文案测试子表';
```





```
http://127.0.0.1:8080/metis-api/strategy/tpl/detail

curl -H "Content-Type: application/json" -X POST -d '{"type":0,"code":"xh_notice_250509_2","appCode":"member"}' http://127.0.0.1:8080/metis-api/strategy/tpl/detail



#白名单
kael.mybatis.plugin.encrypt.tables[9].name=t_test_white
#白名单
kael.mybatis.plugin.encrypt.tables[9].columns[0].encryptColumn=mobile_enc
#白名单
kael.mybatis.plugin.encrypt.tables[9].columns[0].hashColumn=mobile_hash

#文案测试子表
kael.mybatis.plugin.encrypt.tables[10].name=t_test_content_task_record
#文案测试子表
kael.mybatis.plugin.encrypt.tables[10].columns[0].encryptColumn=mobile_enc
#文案测试子表
kael.mybatis.plugin.encrypt.tables[10].columns[0].hashColumn=mobile_hash



#测试文案记录回执状态更新
elastic-job.jobs.testContentTaskReportStatusUpdateJob.cron=4 19 1 * * ?
#测试文案记录回执状态更新
elastic-job.jobs.testContentTaskReportStatusUpdateJob.sharding-total-count=1
#测试文案记录回执状态更新
elastic-job.jobs.testContentTaskReportStatusUpdateJob.sharding-item-parameters=0=A
#测试文案记录回执状态更新
elastic-job.jobs.testContentTaskReportStatusUpdateJob.disabled=false
#测试文案记录回执状态更新
elastic-job.jobs.testContentTaskReportStatusUpdateJob.description=测试文案记录回执状态更新
```